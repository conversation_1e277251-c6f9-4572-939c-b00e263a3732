DROP TABLE IF EXISTS `sys_dict`;

CREATE TABLE `sys_dict` (
    `id` bigint NOT NULL COMMENT 'id',
    `pid` bigint DEFAULT NULL COMMENT '上级ID，一级为0',
    `dict_type` varchar(50) NOT NULL COMMENT '字典类型',
    `dict_name` varchar(255) NOT NULL COMMENT '字典名称',
    `dict_value` varchar(255) DEFAULT NULL COMMENT '字典值',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `sort` int unsigned DEFAULT NULL COMMENT '排序',
    `creator` bigint DEFAULT NULL COMMENT '创建者',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `updater` bigint DEFAULT NULL COMMENT '更新者',
    `update_date` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `dict_type` (`dict_type`,`dict_value`),
    KEY `idx_sort` (`sort`),
    KEY `idx_pid` (`pid`)
);

CREATE TABLE `sys_user` (
    `id` bigint(20) NOT NULL COMMENT 'id',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) DEFAULT NULL COMMENT '密码',
    `real_name` varchar(50) DEFAULT NULL COMMENT '姓名',
    `head_url` varchar(200) DEFAULT NULL COMMENT '头像',
    `gender` tinyint(3) unsigned DEFAULT NULL COMMENT '性别   0：男   1：女    2：保密',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `mobile` varchar(100) DEFAULT NULL COMMENT '手机号',
    `dept_id` varchar(120) DEFAULT NULL COMMENT '部门ID',
    `super_admin` tinyint(3) unsigned DEFAULT NULL COMMENT '超级管理员   0：否   1：是',
    `status` tinyint(4) DEFAULT NULL COMMENT '状态  0：停用   1：正常   2：过期',
    `postid` bigint(20) DEFAULT NULL COMMENT '岗位id-计划管理',
    `creator` bigint(20) DEFAULT NULL COMMENT '创建者',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `updater` bigint(20) DEFAULT NULL COMMENT '更新者',
    `update_date` datetime DEFAULT NULL COMMENT '更新时间',
    `end_date` datetime DEFAULT NULL COMMENT '到期时间',
    `org_type_sign` varchar(20) DEFAULT NULL COMMENT '部门类型',
    `ipad_user` int(2) DEFAULT NULL COMMENT '是否为ipad用户 0是1否',
    `mallid` varchar(64) DEFAULT NULL COMMENT '商场id',
    `is_temporary` int(11) NOT NULL DEFAULT '0' COMMENT '零时账号标志（1:是  0:否）',
    `effective_start_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效开始时间',
    `effective_end_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效结束时间',
    PRIMARY KEY (`id`)
);
INSERT INTO sys_user (id,username,password,real_name,head_url,gender,email,mobile,dept_id,super_admin,status,postid,creator,create_date,updater,update_date,end_date,org_type_sign,ipad_user,mallid,is_temporary,effective_start_date,effective_end_date) VALUES
(1067246875800000001,'admin','$2a$10$rwHKr45q5IdCuHoiWzXa6.kw3Vfe62hlWRAVHrEKbLJuQMS8iWS5m','管理员',NULL,0,'<EMAIL>','13612345678',NULL,1,1,NULL,1067246875800000001,'2019-02-20 11:33:15',1067246875800000001,'2019-02-20 11:33:15',NULL,NULL,NULL,NULL,0,'2023-10-17 14:27:53','2023-10-17 14:27:53'),
(1430443452705243137,'WillZhong','$2a$10$jgkYg4eR0EJRJahgjU2DJemsBrfUdXL9LeMJmGFg6jx6w2VYK76Gu','WillZhong',NULL,0,'','','1427874025023475714',0,1,NULL,1067246875800000001,'2021-08-25 16:14:30',1067246875800000001,'2021-08-25 16:14:30',NULL,NULL,NULL,NULL,0,'2023-10-17 14:27:53','2023-10-17 14:27:53');

CREATE TABLE `tb_cfg_authorizer_wx` (
    `id` bigint(20) NOT NULL DEFAULT '0' COMMENT '唯一值，主键',
    `mallid` varchar(255) DEFAULT NULL COMMENT '商场编号',
    `nickName` varchar(255) DEFAULT NULL COMMENT '授权方昵称',
    `headImg` varchar(255) DEFAULT NULL COMMENT '授权方头像',
    `serviceTypeInfo` varchar(255) DEFAULT NULL COMMENT '授权方公众号类型，0代表订阅号，1代表由历史老帐号升级后的订阅号，2代表服务号',
    `verifyTypeInfo` varchar(255) DEFAULT NULL COMMENT '授权方认证类型，-1代表未认证，0代表微信认证，1代表新浪微博认证，2代表腾讯微博认证，3代表已资质认证通过但还未通过名称认证，4代表已资质认证通过、还未通过名称认证，但通过了新浪微博认证，5代表已资质认证通过、还未通过名称认证，但通过了腾讯微博认证',
    `userName` varchar(255) DEFAULT NULL COMMENT '授权方公众号的原始ID',
    `principalName` varchar(255) DEFAULT NULL COMMENT '公众号的主体名称',
    `businessInfo` varchar(255) DEFAULT NULL,
    `alias` varchar(255) DEFAULT NULL COMMENT '授权方公众号所设置的微信号，可能为空',
    `qrcodeUrl` varchar(255) DEFAULT NULL COMMENT '二维码图片的URL，开发者最好自行也进行保存',
    `authorizerAppid` varchar(255) DEFAULT NULL COMMENT '用户授权的公众号appId',
    `funcInfo` varchar(255) DEFAULT NULL COMMENT '授权给开发者的权限集列表',
    `signature` text COMMENT '账号介绍',
    `miniProgramInfo` varchar(255) DEFAULT NULL COMMENT '可根据这个字段判断是否为小程序类型授权',
    `type` varchar(255) DEFAULT NULL COMMENT '1代表顾客小程序，2代表商户小程序，3代表商管小程序，4代表公众号，5代表商户公众号',
    `creator` bigint(20) DEFAULT NULL COMMENT '创建人',
    `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
    `create_Date` datetime DEFAULT NULL COMMENT '创建时间',
    `update_Date` datetime DEFAULT NULL COMMENT '更新时间',
    `group_id` varchar(255) DEFAULT NULL COMMENT '集团id',
    `dept_id` varchar(120) DEFAULT NULL COMMENT '部门ID',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_cfg_authorizer_wx (id,mallid,nickName,headImg,serviceTypeInfo,verifyTypeInfo,userName,principalName,businessInfo,alias,qrcodeUrl,authorizerAppid,funcInfo,signature,miniProgramInfo,`type`,creator,updater,create_Date,update_Date,group_id,dept_id) VALUES
(1481885172794257412,'8a88a9fd7f73ffcd017f968739870006','嘉里LINKS','http://wx.qlogo.cn/mmopen/0nn3FBrD9a25mxIrsbJhmWCCV1MjO04r1vicMRG3fx26atsvunxb9Dl1X490GicqFBrLPIkia2ym9FbDmPD2KtNC2wcaTIBH4W7/0','0','0','gh_0bd6a97b6d38','嘉里(中国)项目管理有限公司上海分公司','open_pay=1:open_shake=0:open_scan=0:open_card=0:open_store=0','','http://mmbiz.qpic.cn/mmbiz_jpg/1KP64IW1RjgqLuyZ88jeUkQA2TxpBOVbmauTytoy2TxTqibKXLj12cdibMPnxkVM51zCJL8UPKE5F5JXsibVWpH5A/0','wxd830fe4d1e04988e','17,18,19,25,30,31,36,37,40,41,45,48,49,51,57,65,67,70,71,73,76,81,84,85,86,88,93,99,102,105','KIP测试','xcx','1',NULL,NULL,NULL,'2024-01-30 15:52:08','8a888a087cc59dc0017cc622f9ad0000',NULL),
(1498850333786443778,'8a888aed7d0295e5017d029ff1f40000','沈阳嘉里城','http://wx.qlogo.cn/mmopen/Q3auHgzwzM6tb6pGwIJhoe9vet7B98Tn4VwwMH6Lupp56Zy0F58duqtGkh0GacOteEiahOJbZKZBWWbeOYuDf5x3jgwP8OnWPSFEInYFYdO8/0','2','0','gh_86e42dd18313','嘉里(沈阳)房地产开发有限公司','open_pay=1:open_shake=0:open_scan=0:open_card=1:open_store=0','SYkerryparkside','http://mmbiz.qpic.cn/mmbiz_jpg/6qL3ZgRxdUMvTiaDPwmacQygWyWJwaeF5Xrib6gqnE3Bu5b1iaoYRtKIJFzrrAQhuEXicttztSDia2Z8Q1W6Rx2ZRqg/0','wx2bd99ca94d6acd7e','1,2,3,4,6,7,9,11,13,15,22,26,27,33,35,46,66,89','沈阳嘉里城','gzh','4',NULL,NULL,NULL,'2024-01-30 15:54:06','8a8884e77cc9e70a017cca1c77e80004',NULL);
