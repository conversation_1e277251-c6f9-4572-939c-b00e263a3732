spring:
  application:
    name: points-service
  datasource:
    dynamic:
      primary: points
      datasource:
        points:
          url: jdbc:h2:mem:member_points_test;MODE=MYSQL;INIT=RUNSCRIPT FROM './src/test/resources/member_points.sql'
          username: root
          password: root
          driver-class-name: org.h2.Driver
          type: com.zaxxer.hikari.HikariDataSource
      # Hikari 连接池配置
      hikari:
        # 最小空闲连接数量
        minimum-idle: 10
        # 空闲连接存活最大时间，默认600000（10分钟）
        idle-timeout: 600000
        # 连接池最大连接数，默认是10
        maximum-pool-size: 20
        # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
        auto-commit: true
        # 连接池名称
        pool-name: Integral-HikariCP
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
        max-lifetime: 600000
        # 数据库连接超时时间,默认30秒，即30000
        connection-timeout: 10000
        connection-test-query: SELECT 1
        # 连接将被测试活动的最大时间量
        validation-timeout: 3000
  data:
    redis:
      host: 'cnsh-kerry-crm-dev.redis.rds.aliyuncs.com'
      port: 6379
      database: 1
      password: 'HBWj&OgZl%loBU&v79w$oWXwDIJ^akoq'

kerry-crm:
  rabbitmq:
    password: 'NTI5NkYyODlBMjJEMEYyQkMyMDdCOEFEQzU3RjEzNENGOTBGNzZFRDoxNzMwMzU1OTI4OTE2'
    username: 'MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLXZjMjN6Yzc0azAyOkxUQUk1dEdMajVKRFBHOGFFN05Dc2ViSA=='
    addresses: rabbitmq-serverless-cn-vc23zc74k02.cn-shanghai.amqp-0.vpc.mq.amqp.aliyuncs.com:5672
    virtual-host: crm-kpl
    requested-heartbeat: 10
    listener:
      direct:
        retry:
          enabled: true
          max-attempts: 5
          max-interval: 10000
        acknowledge-mode: auto

payment:
  rabbitmq:
    password: 'MjQ0NTc0RDU2NDY2Q0M5NzU0OTM4QTA0OTI2RDlGNzFEMEU4QjdCRToxNzMwNDM5ODQyMzIz'
    username: 'MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLXZjMjN6Yzc0azAyOkxUQUk1dEtBRTFQTWFmczkzeXNUQThnOQ=='
    addresses: rabbitmq-serverless-cn-vc23zc74k02.cn-shanghai.amqp-0.vpc.mq.amqp.aliyuncs.com:5672
    virtual-host: kip-payment-middleware-service
    requested-heartbeat: 10

kerry:
  services:
    kerry-staff: https://dev-kip-service-internal.kerryonvip.com/kerry-staff-service
    payment: https://dev-payment.kerryonvip.com/services
    hive-service: https://dev-kip-service-internal.kerryonvip.com/hive-service
    hive-vas: https://dev-kip-service-internal.kerryonvip.com/hive-view-assembler-service
    event-trigger: https://dev-kip-service-internal.kerryonvip.com/event-trigger-service
    unified-messaging: https://dev-kip-service-internal.kerryonvip.com/unified-messaging-service
    cip-service: https://dev-kip-service-internal.kerryonvip.com/cip-middleware-service
    profile: https://dev-kip-service-internal.kerryonvip.com/profile-service
    message-distribution-service: https://dev-kip-service-internal.kerryonvip.com/message-distribution-service

mall:
  koBigGroup: '4028e3817c2b3f79017c2b48c54c0000,8aaa82ea804d07cd01805174dd3b000c'
  list:
    # 杭州嘉里
    - mallId: 8aaa80b47c784020017c78b00d060022
      abbreviation: HKC
      groupId: 4028e3817c2b3f79017c2b48c54c0000
      code: qr,alipay,wxpay
      appId: wxd830fe4d1e04988e
      wxMchId: 1614666071
      wxBrandId: 4853
      wxCardTemplateId: 'p3qJ3s82q7MoYS705PTnOo2VY7Y0'
      aliMallId: 10012629
      aliCardTemplateId: '20220321000000003944406000300728'
      projectId: HKC
    # 天津嘉里
    - mallId: 8aaa809f8056fc2001805a5af5760000
      abbreviation: TKC
      groupId: 8aaa82ea804d07cd018051736b5c000b
      code: qr,photo
      appId: wxd830fe4d1e04988e
      wxMchId: 1615796420
      wxBrandId: 4872
      wxCardTemplateId: 'p-DAvuLPMcec1iXuTA8jKUNmR-OI'
      aliMallId: 10082256
      aliCardTemplateId: '20220506000000004000626000300720'
      projectId: TKC
    # 沈阳嘉里
    - mallId: 8aaa82ea804d07cd01805171768b0009
      abbreviation: SYKC
      groupId: 8aaa82ea804d07cd01805175c3a8000d
      code: qr,photo
      appId: wxd830fe4d1e04988e
      wxMchId: 1615610395
      wxBrandId: 4875
      wxCardTemplateId: 'pdJnI053lUTZNR4D6RfvsTcZwiUE'
      aliMallId: 10081831
      aliCardTemplateId: '20220120000000003842589000300720'
      projectId: SKC
    # 前海嘉里
    - mallId: 8aaa82ea804d07cd0180516ff03b0008
      abbreviation: QHKC
      groupId: 8aaa81947c6e1ca0017c73c13cc30006
      code: photo
      appId: wxd830fe4d1e04988e
      wxMchId: 1623514491
      wxBrandId: 4851
      wxCardTemplateId: 'pxq15xDmp28OXAvDakTVzS44P2KE'
      aliMallId: 10081842
      aliCardTemplateId: '20220330000000003948490000300729'
      projectId: 192
    # 静安嘉里
    - mallId: 8aaa81cb7c836c6b017c83e2c76f0000
      abbreviation: JAKC
      groupId: 8aaa82ea804d07cd01805174dd3b000c
      code: qr,alipay,wxpay
      appId: wx2a1741f02f7bb650
      wxMchId: 1616264750
      wxBrandId: 4934
      wxCardTemplateId: 'p1hKav1NxSo3tTN61sqUQMa7fY9c'
      aliMallId: 10002803
      aliCardTemplateId: '20200813000000002412173000300303'
      projectId: JAKC
    # 浦东嘉里
    - mallId: 8aaa81cb7c836c6b017c83e46b110001
      abbreviation: KP
      groupId: 8aaa82ea804d07cd01805174dd3b000c
      code: qr,alipay
      appId: wx2a1741f02f7bb650
      wxMchId: 1615791369
      wxBrandId: 4941
      wxCardTemplateId: 'pjQkexFnPVu_G5NX77_tdsR3H7KQ'
      aliMallId: 10093868
      aliCardTemplateId: '20220106000000003801208000300722'
      projectId: PKC
    # 北京嘉里
    - mallId: 8aaa80b47c784020017c78b205ba0023
      abbreviation: BKC
      groupId: 8aaa82ea804d07cd01805174dd3b000c
      code: qr,alipay
      appId: wx2a1741f02f7bb650
      wxMchId: 1616264644
      wxBrandId: 4882
      wxCardTemplateId: 'pmi1EwX-ge6tyRF-JNiYqnz4KFiI'
      aliMallId: '10016532,10016532K'
      aliCardTemplateId: '20210312000000002669102000300258'
      projectId: BKC
    # 福州江上图
    - mallId: 8a8481f68059f17501805eb0ed520001
      abbreviation: FZKC
      groupId: 8a8480f9805e8c7201805eaa0d520000
      code: qr,alipay,wxpay
      appId: wxd830fe4d1e04988e
      wxMchId: 1632570829
      wxBrandId: 5939
      wxCardTemplateId: ''
      aliMallId: ''
      aliCardTemplateId: ''
      projectId: FZJLZX
    # 合集&企业坊
    - mallId: 8a8881dc8754766f0187796cee6b0000
      abbreviation: HJQYF
      groupId: 8a88835c7cd96d31017cda3662720001
      code: photo
      appId: wxd830fe4d1e04988e
      aliMallId: '10138496'
      aliCardTemplateId: '20230704000000004763452000300721'
      projectId: 181
    #测试商场
    - mallId: prod-test-f4b2f01902f655bfb0001
      abbreviation: QATS
      groupId: prod-test-ae252018fdc2dfb780000
      code: photo
      appId: wxd830fe4d1e04988e
      projectId: prod-test-100

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

#GAG销采对接
smart-pos:
  url: http://api.test.goago.cn/oapi/rest
  appId: a5cd175df9ec41f4bead4ea4afd4f732
  appKey: 2c968585823429d9018497f69099000e
  appSecret: 08246B4091BB017B4EE42E0FF8CD8C5A
  messageFormat: 'json'
  method: 'gogo.bill.amount.query'
  version: '1.0'
  signMethod: 'MD5'

crm:
  image:
    domain: https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/

kip:
  signature:
    pos:
      system-id: kip4f3842c637732da5
      system-secret: kip8dfe013a0ad769bafef6bc0e66d001aa

abnormal:
  points:
    review:
      crmAdminUrl: 'https://dev-crm-admin-internal.kerryplus.com/admin/#/crm-integral-abnormalIntegralIntercept'
      crmPhotoPointsUrl: 'https://dev-crm-admin-internal.kerryplus.com/admin/#/equity/bonusself/bonusself'

#合合科技appId、secret
textin:
  appId: '5966ece64246e2f51468af5dd05d8e88'
  secret: '3da77e913b35b6979c0826bed20846a2'
  service: 'mall_receipt'
  env: 'uat'
  timeout: 50000
  accessLimit: 40
  ocrFileExternalDomain: 'kip-public-dev.oss-cn-shanghai.aliyuncs.com'
  ocrFileInternalDomain: 'kip-public-dev.oss-cn-shanghai-internal.aliyuncs.com'
  mock: false