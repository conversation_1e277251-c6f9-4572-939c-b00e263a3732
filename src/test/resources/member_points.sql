CREATE ALIAS IF NOT EXISTS "DATE_FORMAT" FOR "com.points.utils.H2Functions.dateFormat";

DROP TABLE IF EXISTS `tb_cash_out_config`;
CREATE TABLE `tb_cash_out_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `name` varchar(128) NOT NULL COMMENT '活动名称',
    `start_time` varchar(128) NOT NULL COMMENT '活动开始时间',
    `end_time` varchar(128) NOT NULL COMMENT '活动结束时间',
    `shop_no` varchar(1000) NOT NULL COMMENT '活动店铺，用,分隔',
    `is_limit` int NOT NULL COMMENT '是否开启最大金额现在 0 关闭 1 开启',
    `money` decimal(10, 1) NOT NULL DEFAULT '0.0' COMMENT '抵现最大金额',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    `business_type` varchar(128) NOT NULL DEFAULT '' COMMENT '活动名称',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_cash_out_config (group_id,mall_id,name,start_time,end_time,shop_no,is_limit,money,create_date,create_user,update_date,update_user,business_type) VALUES
('4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','积分抵现','2023-08-01 00:00:00','2999-10-01 00:00:00','HKC00041001,HKC00041002',1,100.0,'2023-08-30 00:00:00','nancy','2023-08-30 00:00:00','nancy','XS0110'),
('4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '测试', '2024-11-13 00:00:00.0', '2999-12-13 23:59:59.0', 'HKC00041001,HKC00041002', 1, 500.0, '2024-11-13 10:11:39', 'bert zhang', '2024-11-13 10:11:45', 'bert zhang', 'YM0120');


DROP TABLE IF EXISTS `tb_online_shop_rate`;
CREATE TABLE `tb_online_shop_rate` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT 'lbs id',
    `grade` varchar(128) NOT NULL COMMENT '会员等级code',
    `business_type` varchar(128) NOT NULL DEFAULT '' COMMENT '业务类型',
    `money` decimal(10,1) NOT NULL COMMENT '金额',
    `point_num` decimal(10,1) NOT NULL COMMENT '积分数',
    `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用，1: 启用，0: 禁用',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_online_shop_rate (group_id,mall_id,grade,business_type,money,point_num,status,create_date,creator,update_date,updater) VALUES
('4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','3','XS0110',1.0,1.0,0,'2023-08-30 17:03:29','admin','2023-08-30 17:03:29','admin'),
('4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','2','XS0110',1.0,1.0,0,'2023-08-30 17:03:29','admin','2023-08-30 17:03:29','admin'),
('4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','1','XS0110',1.0,1.0,0,'2023-08-30 17:03:29','admin','2023-08-30 17:03:29','admin'),
('4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '1', 'YM0120', 1.0, 1.0, 0, '2024-11-13 10:11:07', 'bert zhang', '2024-11-13 10:11:07', 'bert zhang'),
('4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '2', 'YM0120', 1.0, 1.0, 0, '2024-11-13 10:11:07', 'bert zhang', '2024-11-13 10:11:07', 'bert zhang'),
('4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '3', 'YM0120', 1.0, 1.0, 0, '2024-11-13 10:11:07', 'bert zhang', '2024-11-13 10:11:07', 'bert zhang');

DROP TABLE IF EXISTS `tb_points_review`;
CREATE TABLE `tb_points_review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `group_id` varchar(128) NOT NULL COMMENT '集团id',
  `mall_id` varchar(128) NOT NULL COMMENT '商场id',
  `vipcode` varchar(120) NOT NULL COMMENT '会员号',
  `mobile` varchar(32) NOT NULL COMMENT '手机号',
  `shop_no` varchar(120) NOT NULL COMMENT '店铺合同号',
  `shop_name` varchar(32) NOT NULL COMMENT '店铺名称',
  `serial_num` varchar(64) NOT NULL COMMENT '流水号',
  `bonus` int(11) NOT NULL DEFAULT '0' COMMENT '获得积分数量',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '积分方式（1.小票；2.二维码）',
  `image_url` varchar(255) NOT NULL COMMENT '积分审核图片地址',
  `qrcode` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `reason` varchar(2550) NOT NULL COMMENT '未通过原因',
  `upload_date` datetime NOT NULL COMMENT '上传时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '审核状态 1待审核 2审核通过 3审核未通过',
  `timestamp` varchar(50) NOT NULL COMMENT '时间戳',
  `trading_date` varchar(32) NOT NULL COMMENT '交易日期',
  `check_time` datetime NOT NULL COMMENT '审核时间',
  `check_name` varchar(64) NOT NULL COMMENT '审核人名字',
  `format_id` varchar(255) NOT NULL COMMENT '业态组合',
  `format_name` varchar(255) NOT NULL COMMENT '业态名称',
  `integral_adjust_id` varchar(32) NOT NULL COMMENT '积分审核记录id',
  `org_points` int(11) NOT NULL DEFAULT '0' COMMENT '变更前积分数',
  `org_grade` varchar(32) NOT NULL DEFAULT '' COMMENT '变更前会员等级',
  `money` decimal(10,1) NOT NULL COMMENT '金额',
  `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` varchar(128) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
   PRIMARY KEY (`id`)
);
INSERT INTO tb_points_review (id,group_id,mall_id,vipcode,mobile,shop_no,shop_name,serial_num,money,bonus,`type`,image_url,qrcode,description,reason,upload_date,status,`timestamp`,trading_date,check_time,check_name,format_id,format_name,integral_adjust_id,org_points,org_grade,create_user,create_date,update_user,update_date) VALUES
('1','8aaa82ea804d07cd01805174dd3b000c','8aaa81cb7c836c6b017c83e2c76f0000','KERRY100213904','17096851003','JAKC00011019','外婆Jia','1655808376956',163.0,163,3,'/member/img/1b5c7033e9454f1db457d9c03ebf7e4c.jpeg','','','','2022-06-21 18:46:17',1,'1655808376528','2022-06-21 18:46:16','2022-06-21 18:46:17','admin','','','8aaa82ea804d07cd01805174dd3b000c',14,'1','nancy','2022-06-21 18:46:17','kiki','2022-06-21 18:46:17'),
('2','8a84853b7c91ac5b017c962dab55030e','8a84853b7c91ac5b017c961a9b2a030d','KERRY100300966','17096851003','HKC00020002','外婆Jia','1655808376956',163.0,163,3,'/member/img/1b5c7033e9454f1db457d9c03ebf7e4c.jpeg','','','','2022-06-21 18:46:17',1,'1655808376528','2022-06-21 18:46:16','2022-06-21 18:46:17','admin','','','bf2ed27357aa484791fd30d0b8707881',14,'1','nancy','2022-06-21 18:46:17','kiki','2022-06-21 18:46:17'),
('3','4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','KERRY100213686','17096851003','HKC00020002','外婆Jia','1655808376956',163.0,163,3,'/member/img/1b5c7033e9454f1db457d9c03ebf7e4c.jpeg','','','','2022-06-21 18:46:17',1,'1655808376528','2022-06-21 18:46:16','2022-06-21 18:46:17','admin','','','bf2ed27357aa484791fd30d0b8707881',14,'1','nancy','2022-06-21 18:46:17','kiki','2022-06-21 18:46:17');

INSERT INTO `tb_points_review` (`id`, `group_id`, `mall_id`, `vipcode`, `mobile`, `shop_no`, `shop_name`, `serial_num`, `money`, `bonus`, `type`, `image_url`, `qrcode`, `description`, `reason`, `upload_date`, `status`, `timestamp`, `trading_date`, `check_time`, `check_name`, `format_id`, `format_name`, `integral_adjust_id`, `org_points`, `org_grade`, `create_user`, `create_date`, `update_user`, `update_date`)
VALUES (29, '8aaa82ea804d07cd01805174dd3b000c', '8aaa81cb7c836c6b017c83e2c76f0000', 'KERRY100213120', '17887991578', 'JAKC00011150', '', '4200000', 120.00, 0, 3, 'https://cnsh-kerry-crm-prod.oss-cn-shanghai.aliyuncs.com//points/photo/record/0b17c18d6d17416ab79bd23c333ee1bd.jpg', '', '', '小票信息不完整-五要素不全', '2024-07-31 10:02:50', 1, '16:34:10', '2024-07-30', '2024-07-31 10:03:01', 'OCR-人工审核', '', '', '', 0, '', '', '2024-07-31 10:02:50', 'OCR回调', '2024-07-31 10:03:01');

INSERT INTO `tb_points_review` (`id`, `group_id`, `mall_id`, `vipcode`, `mobile`, `shop_no`, `shop_name`, `serial_num`, `money`, `bonus`, `type`, `image_url`, `qrcode`, `description`, `reason`, `upload_date`, `status`, `timestamp`, `trading_date`, `check_time`, `check_name`, `format_id`, `format_name`, `integral_adjust_id`, `org_points`, `org_grade`, `create_user`, `create_date`, `update_user`, `update_date`)
VALUES
(66, '8aaa82ea804d07cd01805174dd3b000c', '8aaa81cb7c836c6b017c83e2c76f0000', 'KERRY100213886', '17602115550', 'JAKC00011019', '嘉里桦枫居房地产开发有限公司', '2024080242000001659', 1500.00, 1500, 3, 'https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/a771bda23bac445eab8f5e7c418331c7_hIlaNi68bOL414e063b3f5bd6b320d3494bf8a2ca0c1.jpg', '', '', '', '2024-08-02 17:01:36', 2, '16:59:10', '2024-08-02', '2024-08-02 17:01:43', 'OCR-人工审核', '1', '房地产', '6fd6087e1ad04609af5c8e431be5c002', 1350, '03', '', '2024-08-02 17:01:36', 'OCR回调', '2024-08-02 17:01:43');

DROP TABLE IF EXISTS `tb_integral_shop_rate`;
CREATE TABLE `tb_integral_shop_rate` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `grade_id` varchar(128) NOT NULL COMMENT '会员等级code',
    `grade_name` varchar(128) NOT NULL COMMENT '会员等级名称',
    `shop_id` varchar(128) NOT NULL COMMENT '商铺id',
    `shop_name` varchar(128) NOT NULL COMMENT '商铺名称',
    `money` decimal(10,0) DEFAULT NULL COMMENT '金额',
    `point_num` decimal(10,1) DEFAULT NULL COMMENT '积分数',
    `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用，1: 启用，0: 禁用',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    `is_consistent` int(11) NOT NULL DEFAULT '0' COMMENT '是否统一 0不统一 1统一',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_integral_shop_rate (id,group_id,mall_id,grade_id,grade_name,shop_id,shop_name,money,point_num,status,create_date,creator,update_date,updater,is_consistent) VALUES
(1,'4028e3817c2b3f79017c2b48c54c00001','8aaa80b47c784020017c78b00d0600221','721a1e91730b4f0f9a95465181c4c10d','铂金卡','HKC00021031','绝味鸭脖1',1,1.0,1,'2024-03-15 15:03:45','admin','2024-03-15 15:03:45','admin',0),
(2,'4028e3817c2b3f79017c2b48c54c00001','8aaa80b47c784020017c78b00d0600221','7b73f794f67d4857b5b0ae3e51d52d3a','测试卡','HKC00021031','绝味鸭脖1',1,1.0,1,'2024-03-15 15:03:45','admin','2024-03-15 15:03:45','admin',0),
(3,'4028e3817c2b3f79017c2b48c54c00001','8aaa80b47c784020017c78b00d0600221','3eaa66f7253048338892b12a9f47dae9','金卡','HKC00021031','绝味鸭脖1',1,1.0,1,'2024-03-15 15:03:45','admin','2024-03-15 15:03:45','admin',0),
(4,'4028e3817c2b3f79017c2b48c54c00001','8aaa80b47c784020017c78b00d0600221','2aa4efcabd864f6b8f6f20871f3e64f2','银卡','HKC00021031','绝味鸭脖1',1,1.0,1,'2024-03-15 15:03:45','admin','2024-03-15 15:03:45','admin',0);

DROP TABLE IF EXISTS `tb_integral_category_rate`;
CREATE TABLE `tb_integral_category_rate` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `category_id` varchar(128) NOT NULL COMMENT '业态id',
    `category_name` varchar(128) NOT NULL COMMENT '业态名称',
    `grade_id` varchar(128) NOT NULL COMMENT '会员等级code',
    `grade_name` varchar(128) DEFAULT NULL COMMENT '会员等级名称',
    `money` decimal(10,0) DEFAULT NULL COMMENT '金额',
    `point_num` decimal(10,1) DEFAULT NULL COMMENT '积分数',
    `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用，1: 启用，0: 禁用',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场id',
    `is_consistent` int(11) NOT NULL DEFAULT '0' COMMENT '是否统一 0不统一 1统一',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_integral_category_rate (group_id,category_id,category_name,grade_id,grade_name,money,point_num,status,create_date,creator,update_date,updater,mall_id,is_consistent) VALUES
('8aaa82ea804d07cd01805174dd3b000c','1593452272465088535','零售','5338721c42a34b4ea35f0065e410161f','嘉享卡',1,3.0,1,'2023-05-30 13:36:28','admin','2024-09-09 14:37:41','admin','8aaa81cb7c836c6b017c83e2c76f0000',0),
('8aaa82ea804d07cd01805174dd3b000c','1593452272465088535','零售','877244b3e284474bb11d1ac2a89dbf10','嘉礼卡',1,2.0,1,'2023-05-30 13:36:28','admin','2024-09-09 14:37:41','admin','8aaa81cb7c836c6b017c83e2c76f0000',0);


DROP TABLE IF EXISTS `tb_points_detail`;
CREATE TABLE `tb_points_detail` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `crm_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'crm ID',
    `group_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `mobile` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '手机号',
    `vipcode` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员编号',
    `type` VARCHAR(1) NOT NULL DEFAULT '1' COMMENT '调整类型 1->普通调整， 2->销售调整',
    `current_points` INT NOT NULL DEFAULT 0 COMMENT '积分变更后会员积分数',
    `amount` INT NOT NULL DEFAULT 0 COMMENT '调整积分数量',
    `left_points` INT NOT NULL DEFAULT 0 COMMENT '剩余可用积分数',
    `reason_type` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '积分调整原因',
    `reason_desc` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '冗余调整原因',
    `order_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '销售单号(销售调整)',
    `pos_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '收银机号(销售调整)',
    `shop_no` VARCHAR(120) NOT NULL DEFAULT '' COMMENT '店铺编号',
    `remark` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT '备注',
    `expire_date` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '过期时间',
    `extend1` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '扩展字段1',
    `extend2` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '扩展字段2',
    `extend3` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '扩展字段3',
    `image_url` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '拍照积分上传图片地址',
    `channel` VARCHAR(1) NOT NULL DEFAULT '2' COMMENT '调整渠道（0:crm admin端积分 1:服务台积分, 2:用户自动积分 默认2）',
    `org_points` INT NOT NULL DEFAULT 0 COMMENT '变更前积分数', -- 考虑是否去除该字段
    `org_grade` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '变更前会员等级',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`)
);

INSERT INTO `tb_points_detail` (`id`, `crm_id`, `group_id`, `mall_id`, `mobile`, `vipcode`, `type`, `current_points`, `amount`, `left_points`, `reason_type`, `reason_desc`, `order_no`, `pos_no`, `shop_no`, `remark`, `expire_date`, `extend1`, `extend2`, `extend3`, `image_url`, `channel`, `org_points`, `org_grade`, `create_date`, `create_user`, `update_date`, `update_user`)
VALUES
(1, '9cb349c30d4e4d19bcfafcde0ac87835', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '13000000000', 'KERRY00000000', 'S', 110, 10, 10, 'XS0030', '普通调整', '20240307160112000000', '001', 'HKC0000001', '测试备注11', '2099-12-31 23:59:59', '', '', '', '', '0', 100, 1, '2024-03-07 12:21:33', 'admin', '2024-03-07 12:21:33', 'admin'),
(2, '9cb349c30d4e4d19bcfafcde0ac87836', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '13000000000', 'KERRY00000000', 'S', 110, 10, 10, 'XS0030', '普通调整', '20240308090112000000', '001', 'HKC0000001', '测试备注22', '2099-12-31 23:59:59', '', '', '', '', '0', 100, 1, '2024-03-08 09:21:33', 'admin', '2024-03-08 09:21:33', 'admin'),
(3, 'c9f0313f566a472a89abddb4cbfec84d', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '13122000000', 'KERRY100213877', 'A', 101, -1, 0, 'YM0120', '优惠买单积分抵现', 'KEP2412241534354840', '', 'HKC00041004', '优惠买单积分抵现 - 积分抵现金额: 10.00, 扣除积分: -1', '2099-12-31 23:59:59', '', '', '', '', '0', 102, '33', '2024-12-24 15:37:07', 'kerry_pay', '2024-12-24 15:37:07', 'kerry_pay');

DROP TABLE IF EXISTS `tb_sales_detail`;
CREATE TABLE `tb_sales_detail` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增唯一主键id',
    `crm_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'CRM迁移数据主键ID,对应原crm销售明细表的主键id',
    `group_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '商场ID',
    `vipcode` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员编号',
    `kip_user_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'kip用户id',
    `points_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '积分记录id',
    `points_num` int NOT NULL DEFAULT 0 COMMENT '销售单获得积分数',
    `parent_order_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '父级订单号',
    `order_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '销售单号(销售调整)流水号',
    `total_amount` decimal(30,2) NOT NULL DEFAULT '0.00' COMMENT '销售单号总金额',
    `discount_amount` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
    `points_offset_amount` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '积分抵现金额',
    `pay_amount` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '实际支付金额',
    `sale_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '销售日期',
    `shop_no` VARCHAR(120) NOT NULL DEFAULT '' COMMENT '店铺编号',
    `shop_name` VARCHAR(120) NOT NULL DEFAULT '' COMMENT '店铺名称',
    `status` int NOT NULL DEFAULT 0 COMMENT '退货状态(0->未退货, 1->退货, 2->部分退款)',
    `sale_type` VARCHAR(32) NOT NULL DEFAULT 1 COMMENT '销售类型1.CRM销售,2.POS销售,3.微信照片,4.微信扫码,5.口碑销售',
    `refund_points_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '有单退货扣积分 积分记录id',
    `refund_amount` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '退货金额',
    `refund_points_num` int NOT NULL DEFAULT 0 COMMENT '退货积分数',
    `extend1` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员身份信息-是否办公楼会员',
    `extend2` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员身份信息-是否公寓会员',
    `extend3` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员身份信息-是否住户会员',
    `remark` VARCHAR(1024) DEFAULT NULL COMMENT '备注',
    `image_url` VARCHAR(512) NOT NULL DEFAULT '' COMMENT '销售小票地址',
    `org_points` int NOT NULL DEFAULT 0 COMMENT '变更前积分',
    `org_grade` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '变更前等级',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(128) NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`)
);

INSERT INTO `tb_sales_detail` (`id`, `crm_id`, `group_id`, `mall_id`, `vipcode`, `kip_user_id`, `points_id`, `points_num`, `parent_order_no`, `order_no`, `total_amount`, `discount_amount`, `points_offset_amount`, `pay_amount`, `sale_date`, `shop_no`, `shop_name`, `status`, `sale_type`, `refund_points_id`, `refund_amount`, `refund_points_num`, `extend1`, `extend2`, `extend3`, `remark`, `image_url`, `org_points`, `org_grade`, `create_date`, `create_user`, `update_date`, `update_user`)
VALUES
('111111', '4028e38171', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', 'KERRY00000000', '1111', '11111', 0, '', '11111', 10.00, 0.00, 0.00, 10.00, '2024-03-14 09:09:12', 'HKC0000001', '测试店铺', 0, '1', '', 0.00, 0, '0', '0', '0', '测试数据', '', 10, '1', '2024-03-14 09:09:12', 'admin', '2024-03-14 09:10:28', 'admin');

DROP TABLE IF EXISTS `tb_member_benefit_config`;
CREATE TABLE `tb_member_benefit_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `benefits_id` varchar(50) NOT NULL COMMENT '权益ID',
    `mall_id` varchar(120) NOT NULL COMMENT '商场编号',
    `member_grade` varchar(32) NOT NULL COMMENT '会员等级',
    `name` varchar(60) NOT NULL COMMENT '权益名称',
    `subtitle` varchar(60) NOT NULL DEFAULT '' COMMENT '副标题',
    `logo` varchar(255) NOT NULL COMMENT '图标',
    `link_type` varchar(10) NOT NULL DEFAULT '' COMMENT '链接类型：0站内 1站外 2无类型 3富文本',
    `inside_type` varchar(255) NOT NULL DEFAULT '' COMMENT '内部链接类型：0自定义1商品2券3页面4自定义页面',
    `inside_url` varchar(500) NOT NULL DEFAULT '' COMMENT '内部链接url',
    `inside_id` varchar(100) NOT NULL DEFAULT '' COMMENT '内部链接选中id',
    `two_level_linkage` varchar(255) NOT NULL DEFAULT '' COMMENT '二级联动回显',
    `outside_type` varchar(10) NOT NULL DEFAULT '' COMMENT '外部链接类型：0h5、1小程序',
    `outside_app_id` varchar(100) NOT NULL DEFAULT '' COMMENT '外部appid',
    `outside_url_name` varchar(100) NOT NULL DEFAULT '' COMMENT '外部链接名称',
    `outside_url` varchar(500) NOT NULL DEFAULT '' COMMENT '外部链接url',
    `show_type` varchar(500) NOT NULL DEFAULT '' COMMENT '展现形式：0弹框 1新页面',
    `content` varchar(500) NOT NULL DEFAULT '' COMMENT '权益内容',
    `status` int NOT NULL DEFAULT '0' COMMENT '状态',
    `group_id` varchar(255) NOT NULL COMMENT '集团ID',
    `creator` bigint NOT NULL COMMENT '创建人',
    `create_user` varchar(60) NOT NULL COMMENT '创建者',
    `create_date` datetime NOT NULL COMMENT '创建时间',
    `updater` bigint NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_user` varchar(60) NOT NULL DEFAULT 'SYS' COMMENT '更新者',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_del` int NOT NULL DEFAULT '0' COMMENT '是否删除;1删除/0正常',
    PRIMARY KEY (`id`),
    KEY `idx_benefits_id_mall_id` (`benefits_id`,`mall_id`)
);

INSERT INTO `tb_member_benefit_config` (`id`, `benefits_id`, `mall_id`, `member_grade`, `name`, `subtitle`, `logo`, `link_type`, `inside_type`, `inside_url`, `inside_id`, `two_level_linkage`, `outside_type`, `outside_app_id`, `outside_url_name`, `outside_url`, `show_type`, `content`, `status`, `group_id`, `creator`, `create_user`, `create_date`, `updater`, `update_user`, `update_date`, `is_del`)
VALUES
(23, 'c80051fb21224c63b30fc61255c3d75f', '8aaa80b47c784020017c78b00d060022', '3', '会员礼遇', '', '/crm/member/memberbenefits/d6162f36d92d4d0aaa23edfe01b5b0cd.png', '', '', '', '', '', '', '', '', '0', '', '哈哈聊聊', 0, '4028e3817c2b3f79017c2b48c54c0000', 1, 'nancy', '2023-08-09 09:20:34', 0, 'nancy', '2023-08-09 09:20:34', 0),
(24, 'c80051fb21224c63b30fc61255c3d75f', '8aaa80b47c784020017c78b00d060022', '2', '会员礼遇', '', '/crm/member/memberbenefits/d6162f36d92d4d0aaa23edfe01b5b0cd.png', '', '', '', '', '', '', '', '', '0', '', '消费礼赞', 0, '4028e3817c2b3f79017c2b48c54c0000', 1, 'nancy', '2023-08-09 09:20:34', 0, 'nancy', '2023-08-09 09:20:34', 0),
(31, '6628c5aa30f64c35acfc0c043fa22821', '8aaa80b47c784020017c78b00d060022', '2', 'e呵呵呵呵呵男的看', '', '/crm/member/memberbenefits/d6162f36d92d4d0aaa23edfe01b5b0cd.png', '', '', '', '', '', '', '', '', '0', '', '哈哈聊聊', 0, '8a84853b7c91ac5b017c962dab55030e', 1, 'nancy', '2023-08-09 16:57:42', 0, 'nancy', '2023-08-09 17:01:06', 0),
(32, '6628c5aa30f64c35acfc0c043fa22821', '8aaa80b47c784020017c78b00d060022', '3', 'c踩踩踩', '', '/crm/member/memberbenefits/d6162f36d92d4d0aaa23edfe01b5b0cd.png', '', '', '', '', '', '', '', '', '0', '', '消费礼111赞', 0, '8a84853b7c91ac5b017c962dab55030e', 1, 'nancy', '2023-08-09 16:57:42', 0, 'nancy', '2023-08-09 17:01:06', 0),
(35, '4038931153134d2ca8d2cd55bae4557d', '8aaa80b47c784020017c78b00d060022', '1', 'e呵呵呵呵呵男的看', '', '/crm/member/memberbenefits/d6162f36d92d4d0aaa23edfe01b5b0cd.png', '', '', '', '', '', '', '', '', '0', '', '停车礼遇', 0, '8a84853b7c91ac5b017c962dab55030e', 1, 'nancy', '2023-08-09 16:57:42', 0, 'nancy', '2023-08-09 17:04:03', 0);

DROP TABLE IF EXISTS `tb_member_grade`;
CREATE TABLE `tb_member_grade` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `group_id` varchar(255) NOT NULL DEFAULT '' COMMENT '集团ID',
    `code` varchar(32) NOT NULL DEFAULT '' COMMENT '等级[编码]',
    `name` varchar(128) NOT NULL DEFAULT '' COMMENT '等级名称（金卡、普卡）',
    `name_en` varchar(128) NOT NULL DEFAULT '' COMMENT '描述（金卡、普卡）英文',
    `sort` int NOT NULL DEFAULT '0' COMMENT '等级顺序',
    `desc_remark` varchar(256) NOT NULL DEFAULT '' COMMENT '描述备注',
    `desc_remark_en` varchar(255) NOT NULL DEFAULT '' COMMENT '描述备注英文',
    `card_cover_url` varchar(255) NOT NULL DEFAULT '' COMMENT '会员卡封面图片地址',
    `card_cover_url_en` varchar(255) NOT NULL DEFAULT '' COMMENT '会员卡封面图片地址（我的）英文',
    `card_cover_home_url` varchar(255) NOT NULL DEFAULT '' COMMENT '会员卡封面图片地址（首页）',
    `card_cover_home_url_en` varchar(255) NOT NULL DEFAULT '' COMMENT '会员卡封面图片地址（首页）英文',
    `grade_desc` varchar(5000) NOT NULL DEFAULT '' COMMENT '会员等级说明',
    `grade_desc_en` varchar(5000) NOT NULL DEFAULT '' COMMENT '会员等级说明英文',
    `up_gradation_status` int NOT NULL DEFAULT '1' COMMENT '是否参与升级，保级降级  1 参与   0 不参与',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_group_id_grade` (`group_id`,`code`),
    KEY `idx_code` (`code`)
);

INSERT INTO `tb_member_grade` (`id`, `group_id`, `code`, `name`, `name_en`, `sort`, `desc_remark`, `desc_remark_en`, `card_cover_url`, `card_cover_url_en`, `card_cover_home_url`, `card_cover_home_url_en`, `grade_desc`, `grade_desc_en`, `up_gradation_status`, `create_date`, `create_user`, `update_date`, `update_user`)
VALUES
('a84e84e42b8c4e43952e4010879741ff', '8a8884e77cc9e70a017cca188fae0003', '1', '银卡', '', 1, '', '', '/member/img/b6e48496a4614c94a4cc9ec027748afa.png', '/member/img/19b4509d0939497899d95e3aea6ae789.png', '', '', '<p>会员等级权益升级中，敬请期待！</p>', '', 1, '2024-02-04 17:33:56', '', '2024-02-29 16:05:38', 'admin'),
('2f9aae92299743949e4e9041cd50c770', '8a8884e77cc9e70a017cca188fae0003', '2', '金卡', '', 2, '保级日5000', '累计周期年 30000', '/member/img/9f70a3b07eb744318ec294fec10897ae.png', '/member/img/9bdbbd2a85cd4eabab80cae01d7a5790.png', '', '', '<p>金卡</p>', '', 1, '2024-02-04 17:33:56', '', '2024-02-29 16:05:38', 'admin');


DROP TABLE IF EXISTS `tb_member_asset`;
CREATE TABLE `tb_member_asset` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增唯一主键id',
    `crm_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'crm迁移数据id',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商场编号',
    `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号码',
    `current_points` int(11) NOT NULL DEFAULT '0' COMMENT '当前积分数',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员编号（规则生成）',
    `grade` varchar(64) NOT NULL DEFAULT '' COMMENT '会员等级[编码]',
    `kip_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'kip对应的user_id',
    `status` int(11) NOT NULL DEFAULT '1' COMMENT '会员状态（0:冻结；1:非冻结)',
    `wx_open_market` varchar(64) NOT NULL DEFAULT '' COMMENT ',微信激活商场',
    `is_completed` varchar(20) NOT NULL DEFAULT '' COMMENT '是否完善信息（0:未完善；1:完善)',
    `remark` varchar(4000) NOT NULL DEFAULT '' COMMENT '备注',
    `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间即加入日期',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间即加入日期',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人',
    `whether_blacklist` int(11) NOT NULL DEFAULT '1' COMMENT '是否黑名单(0:是 1:不是),供活动使用',
    `register_source` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源',
    `register_source_label` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源-附注信息(例如：活动，电子券)',
    `register_source_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源-备注信息',
    `authorized_mall_id` varchar(64) NOT NULL DEFAULT '' COMMENT '授权商场id',
    `authorized_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权事件',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_member_asset (id,crm_id,group_id,mall_id,mobile,current_points,vipcode,grade,kip_user_id,status,wx_open_market,is_completed,remark,join_time,create_date,create_user,update_date,update_user,whether_blacklist,register_source,register_source_label,register_source_remark) VALUES
(1699,'11','4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','16691027090',5000,'KERRY100213686','1','2c9d85bc8489e0ba01849e4629760003',1,'','','','2022-11-22 15:38:29','2022-11-22 15:38:29','2024-03-18 14:14:00','2022-11-22 15:38:29','',1,'alipay','alipay','',''),
(1525,'22', '4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','18149768377',10,'KERRY100213689','1','2c9d85a18498fd2e01849e8059260001',1,'','','','2022-11-22 17:57:49','2022-11-22 17:57:49','sys','2022-11-22 17:57:49','sys',1,'miniProgram','小程序注册','',''),
(1768,'33', '4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','13162037171',0,'KERRY100213001','1','Evan kip user id',1,'','','','2022-11-23 17:34:14','2022-11-23 17:34:14','sys','2022-11-23 17:34:14','sys',1,'alipay','支付宝注册','','');

INSERT INTO `tb_member_asset` (`id`, `crm_id`, `group_id`, `mall_id`, `mobile`, `current_points`, `vipcode`, `grade`, `kip_user_id`, `status`, `wx_open_market`, `is_completed`, `remark`, `join_time`, `create_date`, `create_user`, `update_date`, `update_user`, `whether_blacklist`, `register_source`, `register_source_label`, `register_source_remark`)
VALUES
(194, '1568', '8aaa82ea804d07cd01805174dd3b000c', '8aaa81cb7c836c6b017c83e46b110001', '17887991578', 0, 'KERRY100213120', '01', '8aaa87bb8051012901805941a9ff0000', 1, '', '', '', '2022-07-06 15:42:19', '2022-07-06 15:42:19', '2024-03-18 14:14:00', '2024-07-30 16:19:38', '', 1, 'miniProgram', '小程序注册', '', '8aaa80b47c784020017c78b00d060022'),
(195, '1567', '8aaa82ea804d07cd01805174dd3b000c', '8aaa81cb7c836c6b017c83e2c76f0000', '17887991582', 0, 'KERRY100213904', '01', '8aaa87bb8051012901805941a9ff0000', 1, '', '', '', '2022-07-06 15:42:19', '2022-07-06 15:42:19', '2024-03-18 14:14:00', '2024-07-30 16:19:38', '', 1, 'miniProgram', '小程序注册', '', '8aaa80b47c784020017c78b00d060022'),
(158, '1532', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '***********', 3890, 'KERRY100213877', '2', '2c9d850c870bea4101870cfbc08f0000', 1, '4028e3817bf860f3017bf86279b50001', '1', '11111', '2023-03-23 14:13:49', '2023-03-23 14:13:49', 'sys', '2024-07-30 16:19:38', 'admin', 1, 'miniProgram', '小程序注册', '', '8aaa80b47c784020017c78b00d060022');

DROP TABLE IF EXISTS `tb_points_expired_reminder`;
CREATE TABLE `tb_points_expired_reminder` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(64) NOT NULL COMMENT '商场号',
    `is_show_c` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'C端是否展示即将过期积分（0、不展示；1、展示年底过期；2、展示非年底过期）',
    `show_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '展示类型（1、按月；2、按天）',
    `ahead_month_num` int(11) NOT NULL DEFAULT '0' COMMENT '提前多少月',
    `ahead_day_num` int(11) NOT NULL DEFAULT '0' COMMENT '提前多少天',
    `year_end` varchar(2048) NOT NULL DEFAULT '' COMMENT '年底展示文案',
    `not_year_end` varchar(2048) NOT NULL DEFAULT '' COMMENT '非年底展示文案',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_points_expired_reminder (id,group_id,mall_id,is_show_c,show_type,ahead_month_num,ahead_day_num,year_end,not_year_end,create_date,create_user,update_date,update_user) VALUES
(1,'8a8480f9805e8c7201805eaa0d520000','8a8481f68059f17501805eb0ed520001',1,2,0,3,'您有{INTEGRAL}积分将于{DATE}零点过期，请进入积分商场尽快兑换礼品哦','','2023-10-09 14:02:33','daniel','2023-10-09 14:04:51','daniel'),
(6,'8aaa82ea804d07cd01805174dd3b000c','8aaa81cb7c836c6b017c83e2c76f0000',1,2,0,4,'您有{INTEGRAL}积分将于{DATE}零点过期，请进入积分商场尽快兑换礼品哦','','2023-10-09 14:07:03','daniel','2023-10-09 14:09:26','daniel'),
(7,'8aaa82ea804d07cd01805174dd3b000c','8aaa81cb7c836c6b017c83e46b110001',1,1,3,0,'您有{INTEGRAL}积分将于{DATE}零点过期，请进入积分商场尽快兑换礼品哦','','2023-10-09 14:07:17','daniel','2023-10-09 14:07:17','daniel'),
(8,'8aaa82ea804d07cd01805174dd3b000c','8aaa80b47c784020017c78b205ba0023',2,2,0,4,'','您有{INTEGRAL}赠送积分将于{DATE}零点过期，请进入积分商场尽快兑换礼品哦','2023-10-09 14:07:38','daniel','2023-10-09 14:07:38','daniel'),
(10,'4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022',1,1,1,0,'您有{INTEGRAL}积分将于{DATE}零点过期，请进入积分商场尽快兑换礼品哦','','2023-11-23 12:12:35','daniel','2023-11-23 12:12:35','daniel');


DROP TABLE IF EXISTS `tb_point_cleared`;
CREATE TABLE `tb_point_cleared` (
    `id` varchar(64) NOT NULL COMMENT '主键id',
    `name` varchar(128) NOT NULL COMMENT '积分清零规则名称',
    `group_id` varchar(64) NOT NULL COMMENT '集团id',
    `mall_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商场id',
    `integral_start_time` datetime NOT NULL COMMENT '所需清除积分开始时间',
    `integral_end_time` datetime NOT NULL COMMENT '所需清除积分结束时间',
    `integral_clear_time` datetime NOT NULL COMMENT '清除积分执行时间',
    `is_sms` int(11) NOT NULL DEFAULT '1' COMMENT '是否短信通知(0:是1:否)',
    `is_show` int(11) NOT NULL DEFAULT '1' COMMENT '是否小程序端展示(0:是1:否)',
    `show_start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '展示开始时间',
    `show_end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '展示结束时间',
    `show_content` varchar(1024) NOT NULL DEFAULT '' COMMENT '展示文案',
    `show_more_content` varchar(1024) NOT NULL DEFAULT '' COMMENT '更多内容',
    `status` int(11) NOT NULL DEFAULT '0' COMMENT '启用状态(0:未开启 1:启用)',
    `clear_status` int(11) NOT NULL DEFAULT '0' COMMENT '执行状态(0:未执行 1:已执行)',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_point_cleared_detail`;
CREATE TABLE `tb_point_cleared_detail` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `group_id` varchar(64) NOT NULL COMMENT '集团id',
    `mall_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商场id',
    `grade` varchar(32) NOT NULL DEFAULT '' COMMENT '会员等级',
    `integral_clear_id` varchar(64) NOT NULL COMMENT '积分清除规则id',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员号',
    `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号',
    `nick_name` varchar(64) NOT NULL DEFAULT '' COMMENT '会员昵称',
    `current_integral` int(11) NOT NULL DEFAULT '0' COMMENT '当前积分',
    `clear_integral` int(11) NOT NULL DEFAULT '0' COMMENT '所需清除积分',
    `clear_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '清除时间',
    `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，记录会员修改的版本信息',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`)
);


DROP TABLE IF EXISTS `tb_insensate_points_auth_record`;
CREATE TABLE `tb_insensate_points_auth_record` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `open_id` varchar(128) NOT NULL COMMENT '微信open id',
    `kip_user_id` varchar(128) NOT NULL COMMENT 'kip系统中用户唯一id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '会员卡号（支付宝）',
    `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '授权状态，0已授权，1未授权，默认0',
    `origin` tinyint(3) unsigned NOT NULL COMMENT '来源，0微信，1支付宝',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `mchid` varchar(128) NOT NULL DEFAULT '' COMMENT '商圈id',
    PRIMARY KEY (`id`)
);

INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`)
VALUES (1, 'oudWQ5RH8BAi3ETRYltGBTJEid8Q', '8a8486cb8339b32b01833f2f99de0020', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-10-20 10:49:29', '2022-10-20 10:49:29', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (2, 'oudWQ5cmU4OUq7lgRq0x_9KEX5Hs', '8a8480ab7ce499c3017ceefd5ac50016', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-10-20 10:50:18', '2022-10-20 10:50:18', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES
(3, 'oudWQ5SXbHmn3wrqatceCSIdqMh8', '8a84830b84a36f2c0184eb7845be0031', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '344920997847', 0, 0, '2022-10-20 12:14:18', '2022-10-20 12:14:18', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (4, 'oudWQ5frsbltwSUiK1b5_SZupupQ', '8a84830b84a36f2c0184ef6a84030034', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '908154163874', 0, 0, '2022-10-20 14:05:41', '2022-10-20 14:05:41', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (5, 'oudWQ5WxMpOkjuLjEKB8884Q4DFs', '8a84830b84a36f2c0185044bbcaf0042', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '915683418076', 0, 0, '2022-10-20 14:07:48', '2022-10-20 14:07:48', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (6, 'oudWQ5XqkJbskiAQ1VJ-6mjp0TN4', '8a84818a7d125c80017d12f10a6d0003', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-10-20 15:25:36', '2022-10-20 15:25:36', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (7, 'oudWQ5XszvWdRXXj5Hm6pTOzxVo0', '8a8485067d327431017d365656d50001', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-10-20 17:16:40', '2022-10-20 17:16:40', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (8, 'oudWQ5aJb77wEacLNnep0rwbltf0', '8a84817381dbec4e0181ffbe4c340020', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-10-21 14:31:30', '2022-10-21 14:31:30', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (9, 'oudWQ5eOfE0Pn0Xs4rDAAFSEjERg', '8a8486917fe4280c01803bf6330a0056', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-10-24 14:49:00', '2022-10-24 14:49:00', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (11, 'oudWQ5Q1BM3k4tOVAwG90eJAIm24', '8a8480928321aeba018334a2b7610001', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-10-26 14:45:53', '2022-10-26 14:45:53', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (14, 'ozdsB4zPwINcnjYfaN6M-eE-Z6QE', '8a848076843c342f0184416dd1750004', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-11-04 17:16:04', '2022-11-04 17:16:04', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`)
VALUES (19, 'oudWQ5R-6dTGtk-ysbDbo0ftYkaI1', '8a84810483bc013e0184120b0cd3006a', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 1, '2022-11-08 12:00:17', '2022-11-08 12:00:17', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (26, 'oudWQ5R-6dTGtk-ysbDbo0ftYkaI', '8a84830b84a36f2c0184f63ce0d0003f', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '148012790547', 0, 0, '2022-11-08 14:10:46', '2022-11-08 14:10:46', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (27, 'oudWQ5Z_8g-8QduGV6wQE63OPreM', '8a84830b84a36f2c01850e7ae9f1004b', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '861805612795', 0, 0, '2022-11-09 17:14:29', '2022-11-09 17:14:29', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (28, 'oudWQ5diGH-msg5a6hAR1V2reLa8', '8a84830b84a36f2c0184eab582b9002e', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '952009337355', 0, 0, '2022-11-09 17:55:21', '2022-11-09 17:55:21', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (29, 'oudWQ5YDHKoxpZSO1e4fwD4hvpQo', '8a848076843c342f01845a40c2fb001f', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-11-09 17:56:55', '2022-11-09 17:56:55', '1614666071');
INSERT INTO `tb_insensate_points_auth_record` (`id`, `open_id`, `kip_user_id`, `group_id`, `mall_id`, `card_no`, `status`, `origin`, `create_date`, `update_date`, `mchid`) VALUES (30, 'oudWQ5esYVLwlOToitJpBPDz2UdY', '8a8486c884660952018473f6fc65000c', '8a84853b7c91ac5b017c962dab55030e', '8a84853b7c91ac5b017c961a9b2a030d', '', 0, 0, '2022-11-14 11:57:57', '2022-11-14 11:57:57', '1614666071');


DROP TABLE IF EXISTS `tb_auto_points_config`;
CREATE TABLE `tb_auto_points_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `format` varchar(128) NOT NULL COMMENT '业态',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `lbs_id` varchar(128) NOT NULL COMMENT 'lbs id',
    `project_id` varchar(128) NOT NULL COMMENT 'project id',
    `code` varchar(128) NOT NULL DEFAULT '' COMMENT '自助积分功能位，多个之间英文逗号隔开',
    `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用，1: 启用，0: 禁用',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_auto_points_config (id, format,group_id,lbs_id,project_id,code,status,create_date,creator,update_date,updater) VALUES
(1, 'RETAIL','4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','HKC','qr,wxpay,alipay,photo',1,'2023-04-10 14:11:08','admin','2023-04-21 10:16:28','admin');


DROP TABLE IF EXISTS `tb_member_refine`;
CREATE TABLE `tb_member_refine` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `mall_id` varchar(64) NOT NULL COMMENT '商场号',
    `group_id` varchar(64) NOT NULL COMMENT '集团id',
    `top_img` varchar(255) NOT NULL COMMENT '页面头图图片地址',
    `synopsis` varchar(255) NOT NULL COMMENT '简介',
    `create_user` varchar(64) NOT NULL COMMENT '创建人姓名',
    `create_date` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(64) NOT NULL COMMENT '更新人姓名',
    `update_date` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_member_refine (id, mall_id,group_id,top_img,synopsis,create_user,create_date,update_user,update_date) VALUES
(4, '8a84834088f11119018949444636000c','8a8484b287c1b7cf01880e982460007a','/active/activepic/57495a6d38d649698ee83fb63803ecd1.png','完善信息有好，完善以下信息立即获得奖励积分{INTEGRAL}','xiaoqiang.zhang','2023-08-29 16:21:22','','2023-08-30 10:47:28');

DROP TABLE IF EXISTS `tb_member_refine_detail`;
CREATE TABLE `tb_member_refine_detail` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `config_id` bigint(20) NOT NULL COMMENT '配置id',
    `field_name` varchar(64) NOT NULL COMMENT '字段名称',
    `field_ename` varchar(64) NOT NULL COMMENT '字段英文名称',
    `is_refine` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否属于完善内容(0:否;1:是)',
    `is_required` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否必填项(0:否;1:是)',
    `sort` int(11) NOT NULL COMMENT 'C端展示顺序',
    `field_type` tinyint(4) NOT NULL COMMENT '字段类型(0:个人信息字段;1:标签)',
    `components_type` varchar(64) NOT NULL COMMENT '组件类型',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_member_refine_detail (id, config_id,field_name,field_ename,is_refine,is_required,sort,field_type,components_type) VALUES
(1,4,'姓名哈哈','realName',1,1,1,0,'input'),
(2,4,'姓名111','realName',1,1,1,1,'input');




DROP TABLE IF EXISTS `tb_points_repeat_rule`;
CREATE TABLE `tb_points_repeat_rule` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(255) NOT NULL COMMENT '商场id',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(1:有效，0或其他：无效)',
    `sales_time_difference` int(11) NOT NULL DEFAULT '0' COMMENT '销售时间差，单位分钟',
    `repeat_rule` varchar(120) NOT NULL DEFAULT '0' COMMENT '去重条件，多个值以英文逗号分隔，1:店铺，2:金额，3:销售时间，4:会员号',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人',
    `white_list` int(11) NOT NULL DEFAULT '0' COMMENT '是否配置白名单，0:否，1:是',
    `shop_no` varchar(5000) NOT NULL DEFAULT '' COMMENT '白名单店铺列表，用英文逗号分隔',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_points_repeat_rule (group_id,mall_id,status,sales_time_difference,repeat_rule,create_date,create_user,update_date,update_user,white_list,shop_no) VALUES
('4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022',1,34,'1,2,3,4','2023-11-06 18:50:57','daniel','2023-12-08 13:55:35','admin',0,'');

DROP TABLE IF EXISTS `tb_activity_promotion`;
CREATE TABLE `tb_activity_promotion` (
    `id` varchar(64) NOT NULL COMMENT '主键id',
    `group_id` varchar(128) NOT NULL COMMENT '集团id',
    `mallid` varchar(128) NOT NULL COMMENT '商场id',
    `name` varchar(64) NOT NULL COMMENT '规则名字',
    `module` varchar(32) DEFAULT NULL COMMENT ' 积分模式(1:多倍积分   2：固定积分) ',
    `beginTime` datetime DEFAULT NULL COMMENT '开始时间',
    `endTime` datetime DEFAULT NULL COMMENT '结束时间',
    `promotionBonusExpirationDate` datetime DEFAULT NULL COMMENT '营销积分过期时间',
    `shopId` text COMMENT '店铺id',
    `type` varchar(32) DEFAULT NULL COMMENT '关系类型（叠加或者取最高 必填项二选一）叠加  0    最高   1',
    `times` decimal(11,2) DEFAULT NULL COMMENT '倍数',
    `bonus` int(11) DEFAULT NULL COMMENT '积分',
    `crowd_id` varchar(64) NOT NULL DEFAULT '' COMMENT '规则名字',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `update_date` datetime DEFAULT NULL COMMENT '更新时间',
    `update_user` varchar(128) DEFAULT NULL COMMENT '更新人',
    `create_user` varchar(128) DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_activity_promotion (id,group_id,mallid,name,module,beginTime,endTime,promotionBonusExpirationDate,shopId,`type`,times,bonus,crowd_id,create_date,update_date,update_user,create_user) VALUES
('084ec9a3aa774ab9a643eb678938ea76','4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','会员双倍积分','1','2022-04-01 00:00:00','2025-04-30 00:00:00','2999-12-12 00:00:00','allstore','1',2.00,NULL,'123,456','2024-04-15 10:00:07','2024-04-15 10:00:07','admin','admin');

DROP TABLE IF EXISTS `tb_activity_promotion_condition`;
CREATE TABLE `tb_activity_promotion_condition` (
    `id` varchar(64) NOT NULL COMMENT '主键id',
    `promotion_id` varchar(128) NOT NULL COMMENT '营销积分活动id',
    `promotion_condition_group_id` varchar(128) NOT NULL COMMENT '活动组id',
    `promotion_condition_type` varchar(32) NOT NULL COMMENT '条件类型(0:会员等级、1:性别、2:生日当天、3:会员当月、4:每周、5:每月、6:此单购物是否当月首笔购物、7:会员地址、8:首次入会)',
    `promotion_condition_content` varchar(255) DEFAULT NULL COMMENT '设置规则的内容',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_activity_promotion_condition (id,promotion_id,promotion_condition_group_id,promotion_condition_type,promotion_condition_content) VALUES
('00afd734d5af4d1c8fd27e30332f8b0b','174bb535a47d4ddf88b08dde2829ea34','9870f7b339bf419499a2d73e6b2d4d74','5','01,02,03,04,05'),
('02ec842386834ff0976f19e411b2ee54','cda469595d064d8095ddd967ac80a647','98ad68bdc0a348c99b7a32ee9365c54c','0','2');

DROP TABLE IF EXISTS `tb_activity_promotion_condition_group`;
CREATE TABLE `tb_activity_promotion_condition_group` (
    `id` varchar(64) NOT NULL COMMENT '主键id',
    `promotion_id` varchar(128) NOT NULL COMMENT '营销积分活动id',
    PRIMARY KEY (`id`)
);

-- hdi_xcrm.tb_zindex_list definition

-- db_dev_kip_member_points.tb_self_defining_page definition

DROP TABLE IF EXISTS `tb_self_defining_page`;
CREATE TABLE `tb_self_defining_page` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `page_name` varchar(100) NOT NULL DEFAULT '' COMMENT '页面名称',
    `group_id` varchar(120) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` varchar(100) NOT NULL DEFAULT '' COMMENT '商场编号',
    `open_flag` int(10) NOT NULL DEFAULT '0' COMMENT '是否启用：0关闭 1启用',
    `type` int(10) NOT NULL DEFAULT '0' COMMENT '页面类型：0首页 1自定义页面 2我的',
    `versions` int(10) NOT NULL COMMENT '版本号',
    `head_navigation_show` int(2) NOT NULL DEFAULT '0' COMMENT '头部导航,切换lbs的，针对首页和自定义(1开0关)',
    `head_navigation_color` varchar(20) NOT NULL DEFAULT '' COMMENT '头部导航条字体颜色',
    `capsule_color` tinyint(1) NOT NULL DEFAULT '0' COMMENT '悬浮胶囊颜色(头部导航条旁边三个点 0黑1白)',
    `background_image_url` varchar(255) NOT NULL DEFAULT '' COMMENT '背景图链接(左侧背景图拖过来)',
    `background_image_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '背景图开关 0关闭1开启',
    `complete_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '背景图全图开关(新增页面的配置模式，针对首页 0关1开)',
    `underline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '下划线开关，没用到（0关1开）',
    `my_waterfall_show` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0关1开',
    `my_ad_show` tinyint(1) NOT NULL DEFAULT '0' COMMENT '广告图开关(我的页面完善信息 0关1开)',
    `my_ad_url` varchar(255) NOT NULL DEFAULT '' COMMENT '广告图片地址',
    `show_line_num` int(10) NOT NULL DEFAULT '0' COMMENT '我的页面功能球每行显示个数',
    `floating_btn_show` tinyint(1) NOT NULL DEFAULT '0' COMMENT '显示悬浮按钮开关(针对KO我的页面客服悬浮框)  0关1开',
    `floating_btn_url` varchar(255) NOT NULL DEFAULT '' COMMENT '悬浮按钮图片地址',
    `my_show_grade` varchar(800) NOT NULL DEFAULT '' COMMENT '显示卡等（针对页面类型为我的），可多个，逗号拼接',
    `module_context` mediumtext COMMENT '模型',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(20) NOT NULL DEFAULT '' COMMENT '创建者',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(20) NOT NULL DEFAULT '' COMMENT '更新者',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_self_defining_page (id, page_name,group_id,mall_id,open_flag,`type`,versions,head_navigation_show,head_navigation_color,capsule_color,background_image_url,background_image_flag,complete_flag,underline,my_waterfall_show,my_ad_show,my_ad_url,show_line_num,floating_btn_show,floating_btn_url,my_show_grade,module_context,create_date,create_user,update_date,update_user) VALUES
(1, '我的','8a8884e77cc9e70a017cca14d07e0002','8a88835c7cd96d31017cda2fd9910000',1,2,1,0,'#000',0,'undefined/images/default-img.png',0,0,0,0,1,'../../../statics/img/crm/zindex/my/ad.png',5,1,'/zindex/zindexpic/8cbc5529487846a990b966ad15bcd540.psd','01,02','[{"moduleSign":"my","sort":1,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":null,"multipleLineList":[{"id":null,"groupId":null,"mallId":null,"ballName":"多行内容1","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"../../../statics/img/crm/zindex/my/multiline-img-1.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":1,"convertible":null,"fontUnSelectColor":null,"showAliMini":0}],"imgList":[{"id":null,"groupId":null,"mallId":null,"ballName":"功能球1","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showAliMini":null},{"id":null,"groupId":null,"mallId":null,"ballName":"功能球2","sort":2,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showAliMini":null}],"suspensionList":[{"id":null,"groupId":null,"mallId":null,"ballName":null,"sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":null,"linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":2,"convertible":null,"fontUnSelectColor":null,"showAliMini":null}]}]','2024-05-22 15:31:31','','2024-05-22 15:31:31','');
INSERT INTO tb_self_defining_page (page_name,group_id,mall_id,open_flag,`type`,versions,head_navigation_show,head_navigation_color,capsule_color,background_image_url,background_image_flag,complete_flag,underline,my_waterfall_show,my_ad_show,my_ad_url,show_line_num,floating_btn_show,floating_btn_url,my_show_grade,module_context,create_date,create_user,update_date,update_user) VALUES
('首页','4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022',1,0,1,1,'#D12828',0,'undefined/images/default-img.png',0,0,0,0,0,'',0,0,'','','[{"moduleSign":"ball","sort":1,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":0,"multipleLineList":null,"imgList":[{"id":null,"groupId":null,"mallId":null,"ballName":"功能球","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showAliMini":0,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"ballName":"功能球1","sort":2,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showAliMini":0,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"ballName":"功能球1","sort":3,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showAliMini":0,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"ballName":"功能球1","sort":4,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showAliMini":0,"memberGrade":null}],"suspensionList":null,"waterfallsFlowList":null},{"moduleSign":"navigation","sort":2,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":1,"multipleLineList":null,"imgList":[{"id":null,"groupId":null,"mallId":null,"ballName":"底部导航无连接","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","imageCheckId":"","fontSelectColor":"rgb(52, 125, 246)","tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":"rgb(58, 130, 248)","showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"ballName":"底部导航","sort":2,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","imageCheckId":"","fontSelectColor":"rgb(52, 125, 246)","tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":"rgb(58, 130, 248)","showAliMini":null,"memberGrade":null}],"suspensionList":null,"waterfallsFlowList":null}]','2024-05-27 11:03:51','nancy','2024-05-27 11:03:51','nancy');
INSERT INTO tb_self_defining_page (id, page_name,group_id,mall_id,open_flag,`type`,versions,head_navigation_show,head_navigation_color,capsule_color,background_image_url,background_image_flag,complete_flag,underline,my_waterfall_show,my_ad_show,my_ad_url,show_line_num,floating_btn_show,floating_btn_url,my_show_grade,module_context,create_date,create_user,update_date,update_user) VALUES
    (12345, '首页','12345','12345',1,0,1,1,'#000',0,'undefined/images/default-img.png',0,0,0,0,0,'../../../statics/img/crm/zindex/my/ad.png',0,0,'/images/default-img.png','','[{"id":"ad93e943593d431faf3c5daa837d22d0","listId":null,"moduleSign":"title","sort":1,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":0,"multipleLineList":null,"imgList":[{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":null,"sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1805135504801583000,"imageUrl":"/zindex/zindexpic/7c467a6d4c5f49009d66a0cc146d0b86.jpeg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null}],"suspensionList":null,"waterfallsFlowList":null},{"id":"d05bc873db62420b9b48b568c2902349","listId":null,"moduleSign":"loop","sort":2,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":1,"multipleLineList":null,"imgList":[{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"33333","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611186003065929700,"imageUrl":"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":"","showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":null,"sort":2,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611186003065929700,"imageUrl":"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":null,"activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":"2024-12-12 00:00:00,2025-01-24 23:59:59#2024-12-19 00:00:00,2025-01-29 23:59:59#2024-12-31 00:00:00,2025-01-07 23:59:59","showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":null,"sort":3,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611186003065929700,"imageUrl":"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":null,"activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":"2024-12-21 00:00:00,2024-12-25 23:59:59#2024-12-19 00:00:00,2024-12-28 23:59:59","showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":null,"sort":4,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611186003065929700,"imageUrl":"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":null,"activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":"2025-01-06 00:00:00,2025-01-15 23:59:59#2024-12-16 00:00:00,2024-12-26 23:59:59#2025-01-30 00:00:00,2025-01-31 23:59:59#2025-01-29 00:00:00,2025-01-30 23:59:59","showAliMini":null,"memberGrade":null}],"suspensionList":null,"waterfallsFlowList":null},{"id":"1762433691098042370","listId":"1603209107648757761","moduleSign":"advertisement","sort":3,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":0,"multipleLineList":[],"imgList":[{"id":1762433691131596800,"groupId":null,"mallId":null,"moduleId":"1762433691098042370","ballName":"广告图","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611183336117731300,"imageUrl":"/zindex/zindexpic/2222db509a0d45bb9c5b4f7d6df5435e.jpg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null}],"suspensionList":[],"waterfallsFlowList":[{"id":1762433691131596800,"groupId":null,"mallId":null,"moduleId":"1762433691098042370","tabChName":"广告图","tabEnName":null,"sort":1,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/2222db509a0d45bb9c5b4f7d6df5435e.jpg","contentLinkName":"广告图","contentLinkType":null,"showNum":null,"moreUrl":null}]},{"id":"1762433691194511362","listId":"1603209107648757761","moduleSign":"advertisement","sort":4,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":1,"multipleLineList":[],"imgList":[{"id":1762433691211288600,"groupId":null,"mallId":null,"moduleId":"1762433691194511362","ballName":"直播入口","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611183508994359300,"imageUrl":"/zindex/zindexpic/09d36951e71e471494712cc0291ccdf1.png","linkType":"0","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/activity/liveList","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"直播大厅","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null}],"suspensionList":[],"waterfallsFlowList":[{"id":1762433691211288600,"groupId":null,"mallId":null,"moduleId":"1762433691194511362","tabChName":"直播入口","tabEnName":null,"sort":1,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/09d36951e71e471494712cc0291ccdf1.png","contentLinkName":"直播入口","contentLinkType":null,"showNum":null,"moreUrl":null}]},{"id":"1762433691244843009","listId":"1603209107648757761","moduleSign":"user","sort":5,"brandLogoTitle":"","brandLogoBg":"/zindex/zindex-default/brandLogoBg.jpg","showAliMini":1,"multipleLineList":[],"imgList":[{"id":1762433691261620200,"groupId":null,"mallId":null,"moduleId":"1762433691244843009","ballName":"zindex.jsRemark1","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":"","versions":43,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433691274203100,"groupId":null,"mallId":null,"moduleId":"1762433691244843009","ballName":"zindex.jsRemark1","sort":2,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":"","versions":43,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null}],"suspensionList":[],"waterfallsFlowList":[{"id":1762433691261620200,"groupId":null,"mallId":null,"moduleId":"1762433691244843009","tabChName":"zindex.jsRemark1","tabEnName":null,"sort":1,"selectedUrl":null,"unselectedUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691274203100,"groupId":null,"mallId":null,"moduleId":"1762433691244843009","tabChName":"zindex.jsRemark1","tabEnName":null,"sort":2,"selectedUrl":null,"unselectedUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null}]},{"id":"1762433691349700609","listId":"1603209107648757761","moduleSign":"navigation","sort":6,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":1,"multipleLineList":[],"imgList":[{"id":1762433691362283500,"groupId":null,"mallId":null,"moduleId":"1762433691349700609","ballName":"首页","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611184499319865300,"imageUrl":"/zindex/zindexpic/5208988f79004e0d946e6a6591325050.223","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/automatic/automaticIndex","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":"/zindex/zindexpic/f73a411a273641bb97f1a367656e82f2.113","imageCheckId":"1611184741767413762","fontSelectColor":"rgb(58, 130, 248)","tpId":"","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"首页","activityName":"","activityParams":null,"pageUrl":null,"activityType":0,"titleInput":false,"twolevelLinkage":"首页","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":"rgb(58, 130, 248)","showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433691383255000,"groupId":null,"mallId":null,"moduleId":"1762433691349700609","ballName":"我的会员码","sort":2,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611184577220673500,"imageUrl":"/zindex/zindexpic/7e41709eddb04e62bb0c56ec452fc288.222","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"myQrcode","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":"/zindex/zindexpic/fbfadd5c89004835b42859d1350e4fb6.112","imageCheckId":"1611184634372259841","fontSelectColor":null,"tpId":"","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"我的会员码","activityName":"","activityParams":null,"pageUrl":null,"activityType":0,"titleInput":false,"twolevelLinkage":"会员工具#我的会员码","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433691400032300,"groupId":null,"mallId":null,"moduleId":"1762433691349700609","ballName":"我的","sort":3,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611184791096623000,"imageUrl":"/zindex/zindexpic/b0b881b2498a4551a6824e4d6e2748c4.221","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/automatic/automaticMy","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":"/zindex/zindexpic/3103eee1888a43f7a737393aadbad469.111","imageCheckId":"1611184691305742338","fontSelectColor":"rgb(58, 130, 248)","tpId":"","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"我的","activityName":"","activityParams":null,"pageUrl":null,"activityType":0,"titleInput":false,"twolevelLinkage":"我的","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":"rgb(58, 130, 248)","showTime":null,"showAliMini":0,"memberGrade":null}],"suspensionList":[],"waterfallsFlowList":[{"id":1762433691362283500,"groupId":null,"mallId":null,"moduleId":"1762433691349700609","tabChName":"首页","tabEnName":null,"sort":1,"selectedUrl":"/zindex/zindexpic/f73a411a273641bb97f1a367656e82f2.113","unselectedUrl":"/zindex/zindexpic/5208988f79004e0d946e6a6591325050.223","contentLinkName":"首页","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691383255000,"groupId":null,"mallId":null,"moduleId":"1762433691349700609","tabChName":"我的会员码","tabEnName":null,"sort":2,"selectedUrl":"/zindex/zindexpic/fbfadd5c89004835b42859d1350e4fb6.112","unselectedUrl":"/zindex/zindexpic/7e41709eddb04e62bb0c56ec452fc288.222","contentLinkName":"我的会员码","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691400032300,"groupId":null,"mallId":null,"moduleId":"1762433691349700609","tabChName":"我的","tabEnName":null,"sort":3,"selectedUrl":"/zindex/zindexpic/3103eee1888a43f7a737393aadbad469.111","unselectedUrl":"/zindex/zindexpic/b0b881b2498a4551a6824e4d6e2748c4.221","contentLinkName":"我的","contentLinkType":null,"showNum":null,"moreUrl":null}]},{"id":"1762433691462946817","listId":"1603209107648757761","moduleSign":"ball","sort":7,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":0,"multipleLineList":[],"imgList":[{"id":1762433691483918300,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","ballName":"停车缴费首页","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611183787722637300,"imageUrl":"/zindex/zindexpic/51d4e97403904ba4a58661ade3eff30d.png","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/parkingFee/parkingFee","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":"","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"停车缴费首页","activityName":"","activityParams":null,"pageUrl":null,"activityType":0,"titleInput":false,"twolevelLinkage":"会员工具#停车缴费首页","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":1,"memberGrade":null},{"id":1762433691496501200,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","ballName":"自助积分","sort":2,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611183864067358700,"imageUrl":"/zindex/zindexpic/e33556c448774e4980f678dd7044ce31.png","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/accumulatePoints/selfServicePoints","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":"","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"自助积分","activityName":"","activityParams":null,"pageUrl":null,"activityType":0,"titleInput":false,"twolevelLinkage":"会员工具#自助积分","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433691513278500,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","ballName":"积分商城","sort":3,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611183945671737300,"imageUrl":"/zindex/zindexpic/0fa3ab45a8624efeb6a1956c9e337a28.png","linkType":"0","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/pointsMall/pointsMall","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"积分商城","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433691525861400,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","ballName":"报名活动","sort":4,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611184054602006500,"imageUrl":"/zindex/zindexpic/f5fc62993352414090ce8583f7604e53.png","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/activity/activity","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":"","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"报名活动","activityName":"","activityParams":null,"pageUrl":null,"activityType":0,"titleInput":false,"twolevelLinkage":"会员工具#报名活动","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433691538444300,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","ballName":"会员权益","sort":5,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611187355657666600,"imageUrl":"/zindex/zindexpic/b96d9e004ea1444db0326988d07c7d17.png","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/member/membershipInterests","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":"","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"会员权益","activityName":"","activityParams":null,"pageUrl":null,"activityType":0,"titleInput":false,"twolevelLinkage":"会员工具#会员权益","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433691551027200,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","ballName":"优惠买单","sort":6,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1805135504801583000,"imageUrl":"/zindex/zindexpic/92979546a55d473daffd142067ec982d.png","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/specialOffer/specialOffer","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":"","salePrice":"","marketPrice":"","shopName":null,"versions":43,"insideId":"优惠买单","activityName":"","activityParams":null,"pageUrl":null,"activityType":0,"titleInput":false,"twolevelLinkage":"会员工具#优惠买单","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433691563610000,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","ballName":"功能球","sort":7,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433691580387300,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","ballName":"会员预约","sort":8,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611184741767413800,"imageUrl":"/zindex/zindexpic/f73a411a273641bb97f1a367656e82f2.113","linkType":"0","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/membershipBenefits/index","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"会员预约","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null}],"suspensionList":[],"waterfallsFlowList":[{"id":1762433691483918300,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","tabChName":"停车缴费首页","tabEnName":null,"sort":1,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/51d4e97403904ba4a58661ade3eff30d.png","contentLinkName":"停车缴费首页","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691496501200,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","tabChName":"自助积分","tabEnName":null,"sort":2,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/e33556c448774e4980f678dd7044ce31.png","contentLinkName":"自助积分","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691513278500,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","tabChName":"积分商城","tabEnName":null,"sort":3,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/0fa3ab45a8624efeb6a1956c9e337a28.png","contentLinkName":"积分商城","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691525861400,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","tabChName":"报名活动","tabEnName":null,"sort":4,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/f5fc62993352414090ce8583f7604e53.png","contentLinkName":"报名活动","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691538444300,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","tabChName":"会员权益","tabEnName":null,"sort":5,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/b96d9e004ea1444db0326988d07c7d17.png","contentLinkName":"会员权益","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691551027200,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","tabChName":"优惠买单","tabEnName":null,"sort":6,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/92979546a55d473daffd142067ec982d.png","contentLinkName":"优惠买单","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691563610000,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","tabChName":"功能球","tabEnName":null,"sort":7,"selectedUrl":null,"unselectedUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","contentLinkName":"功能球","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433691580387300,"groupId":null,"mallId":null,"moduleId":"1762433691462946817","tabChName":"会员预约","tabEnName":null,"sort":8,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/f73a411a273641bb97f1a367656e82f2.113","contentLinkName":"会员预约","contentLinkType":null,"showNum":null,"moreUrl":null}]},{"id":"1762433691697827842","listId":"1603209107648757761","moduleSign":"brandLogo","sort":8,"brandLogoTitle":"","brandLogoBg":"/images/default-img.png","showAliMini":0,"multipleLineList":[],"imgList":[{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406542,"imageUrl":"/brandGuide/28de674ea90344d8aaebebdc422705e3.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/brand/brandDetails?shopNo=HKC00041004&lbsId=8aaa80b47c784020017c78b00d060022","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00041004","activityName":"admin11","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00041004","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":2,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406577,"imageUrl":"/brandGuide/4b8b88287c914c3eafedd2271ad539db.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021024","activityName":"杭州回收宝科技有限公司12321","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021024","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":3,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406586,"imageUrl":"/brandGuide/171478e9749e4a0698c47403ea2144f5.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021017","activityName":"danyuan","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021017","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":4,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406588,"imageUrl":"/brandGuide/8006936f70bd486ba7b331bc178032b8.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021026","activityName":"12555","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021026","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":5,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1448919585516011521,"imageUrl":"/brandGuide/eb75b6cbf56346a782d9809114c93ea5.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/brand/brandDetails?shopNo=131371&lbsId=8aaa80b47c784020017c78b00d060022","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"131371","activityName":"丝芙兰","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"131371","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":6,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406582,"imageUrl":"/brandGuide/3bd3396a421b48baa31fc614a7ab72e0.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021028","activityName":"ts","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021028","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":7,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406583,"imageUrl":"/brandGuide/3bd3396a421b48baa31fc614a7ab72e0.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021028","activityName":"ts","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021028","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":8,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406584,"imageUrl":"/brandGuide/3bd3396a421b48baa31fc614a7ab72e0.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021028","activityName":"ts","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021028","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":9,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406585,"imageUrl":"/brandGuide/3bd3396a421b48baa31fc614a7ab72e0.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021028","activityName":"ts","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021028","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":10,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406587,"imageUrl":"/brandGuide/8006936f70bd486ba7b331bc178032b8.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021026","activityName":"1234别名07","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021026","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":11,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406610,"imageUrl":"/brandGuide/22c80f44f3664e5b87fe1ca4d12967e3.svg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00041001","activityName":"餐饮","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00041001","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":12,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371536468410370,"imageUrl":"/brandGuide/c66a56bcdca543c488a95e34580629e7.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"8aaa80b47c784020017c7869d7a2000c","activityName":"丝芙兰","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"8aaa80b47c784020017c7869d7a2000c","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":13,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406552,"imageUrl":"/brandGuide/6ae6299c2e2942af868f804c8b9586c2.png","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021024","activityName":"杭州回收宝科技有限公司12321","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021024","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":14,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406573,"imageUrl":"/brandGuide/af14e32bfb97456b9827a4e3640632d3.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00041004","activityName":"admin12","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00041004","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":15,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406574,"imageUrl":"/brandGuide/af14e32bfb97456b9827a4e3640632d3.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00041004","activityName":"admin11","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00041004","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":16,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406569,"imageUrl":"/brandGuide/dc29b50b9cba42beafce66ff1bb7a6ad.png","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00041006","activityName":"新元素餐饮","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00041006","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":17,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406581,"imageUrl":"/brandGuide/4e5361c38dfd4ed696e5ebe9d0f26bd8.png","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00041004","activityName":"admin12","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00041004","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":18,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406576,"imageUrl":"/brandGuide/529591bf0ef041dda05e335dfd2e9931.jpg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00021024","activityName":"杭州回收宝科技有限公司","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00021024","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":19,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406611,"imageUrl":"/brandGuide/511cf0672f6d4bf29fffc7194c910c40.svg","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00041001","activityName":"餐饮1","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00041001","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":20,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406612,"imageUrl":"https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/61400f1771a54e52a0dd11a940ddcf5f_6e8338dcf67349acbe78592b70c5c363.webp","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00041002","activityName":"杭州鑫晨服务","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00041002","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"zindex.jsRemark1","sort":21,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1450371719088406613,"imageUrl":"https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/61400f1771a54e52a0dd11a940ddcf5f_6e8338dcf67349acbe78592b70c5c363.webp","linkType":"3","insideType":"0","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"HKC00041002","activityName":"汽车","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":"HKC00041002","resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":null}],"suspensionList":[],"waterfallsFlowList":[{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/28de674ea90344d8aaebebdc422705e3.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/4b8b88287c914c3eafedd2271ad539db.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/171478e9749e4a0698c47403ea2144f5.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/8006936f70bd486ba7b331bc178032b8.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/eb75b6cbf56346a782d9809114c93ea5.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/3bd3396a421b48baa31fc614a7ab72e0.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/3bd3396a421b48baa31fc614a7ab72e0.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/3bd3396a421b48baa31fc614a7ab72e0.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/3bd3396a421b48baa31fc614a7ab72e0.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/8006936f70bd486ba7b331bc178032b8.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/22c80f44f3664e5b87fe1ca4d12967e3.svg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/c66a56bcdca543c488a95e34580629e7.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/6ae6299c2e2942af868f804c8b9586c2.png","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/af14e32bfb97456b9827a4e3640632d3.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/af14e32bfb97456b9827a4e3640632d3.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/dc29b50b9cba42beafce66ff1bb7a6ad.png","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/4e5361c38dfd4ed696e5ebe9d0f26bd8.png","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/529591bf0ef041dda05e335dfd2e9931.jpg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"/brandGuide/511cf0672f6d4bf29fffc7194c910c40.svg","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/61400f1771a54e52a0dd11a940ddcf5f_6e8338dcf67349acbe78592b70c5c363.webp","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":null,"groupId":null,"mallId":null,"moduleId":null,"tabChName":"zindex.jsRemark1","tabEnName":null,"sort":null,"selectedUrl":null,"unselectedUrl":"https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/61400f1771a54e52a0dd11a940ddcf5f_6e8338dcf67349acbe78592b70c5c363.webp","contentLinkName":"zindex.jsRemark1","contentLinkType":null,"showNum":null,"moreUrl":null}]},{"id":"1762433691949486082","listId":"1603209107648757761","moduleSign":"advertisement","sort":9,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":1,"multipleLineList":[],"imgList":[{"id":1762433691962069000,"groupId":null,"mallId":null,"moduleId":"1762433691949486082","ballName":"广告图HKC02","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1611186003065929700,"imageUrl":"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null}],"suspensionList":[],"waterfallsFlowList":[{"id":1762433691962069000,"groupId":null,"mallId":null,"moduleId":"1762433691949486082","tabChName":"广告图HKC02","tabEnName":null,"sort":1,"selectedUrl":null,"unselectedUrl":"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg","contentLinkName":"广告图HKC02","contentLinkType":null,"showNum":null,"moreUrl":null}]},{"id":"1762433691999817730","listId":"1603209107648757761","moduleSign":"waterfall","sort":10,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":0,"multipleLineList":[],"imgList":[{"id":1762433692012400600,"groupId":null,"mallId":null,"moduleId":"1762433691999817730","ballName":"去去去去去去去","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"/ticket/ticketpic/62743e6cc6ac4cd9b7ae0028978679c0.jpg","linkType":"0","insideType":"2","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"../ticket/ticketDetailsNew?id=1697138788908564481&isSubscribe=0","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":"1697138788908564481","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"去去去去去去去","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":1,"memberGrade":null},{"id":1762433692024983600,"groupId":null,"mallId":null,"moduleId":"1762433691999817730","ballName":"商品名称","sort":2,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433692041760800,"groupId":null,"mallId":null,"moduleId":"1762433691999817730","ballName":"领券中心","sort":3,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/webView/couponH5?url=/pages/coupon/index&format=RETAIL","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":"","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"领券中心","activityName":"","activityParams":null,"pageUrl":null,"activityType":0,"titleInput":false,"twolevelLinkage":"会员工具#领券中心","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null},{"id":1762433692054343700,"groupId":null,"mallId":null,"moduleId":"1762433691999817730","ballName":"积分商城","sort":4,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":null,"imageUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","linkType":"0","insideType":"3","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"/pages/pointsMall/pointsMall","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":"","salePrice":null,"marketPrice":null,"shopName":null,"versions":43,"insideId":"积分商城","activityName":"","activityParams":null,"pageUrl":"/pages/ticket/ticket","activityType":0,"titleInput":false,"twolevelLinkage":"会员工具#积分商城","circleAuthorize":0,"memberAuthorize":0,"showImgFlag":0,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":0,"memberGrade":null}],"suspensionList":[],"waterfallsFlowList":[{"id":1762433692012400600,"groupId":null,"mallId":null,"moduleId":"1762433691999817730","tabChName":"去去去去去去去","tabEnName":null,"sort":1,"selectedUrl":null,"unselectedUrl":"/ticket/ticketpic/62743e6cc6ac4cd9b7ae0028978679c0.jpg","contentLinkName":"去去去去去去去","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433692024983600,"groupId":null,"mallId":null,"moduleId":"1762433691999817730","tabChName":"商品名称","tabEnName":null,"sort":2,"selectedUrl":null,"unselectedUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","contentLinkName":"商品名称","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433692041760800,"groupId":null,"mallId":null,"moduleId":"1762433691999817730","tabChName":"领券中心","tabEnName":null,"sort":3,"selectedUrl":null,"unselectedUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","contentLinkName":"领券中心","contentLinkType":null,"showNum":null,"moreUrl":null},{"id":1762433692054343700,"groupId":null,"mallId":null,"moduleId":"1762433691999817730","tabChName":"积分商城","tabEnName":null,"sort":4,"selectedUrl":null,"unselectedUrl":"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png","contentLinkName":"积分商城","contentLinkType":null,"showNum":null,"moreUrl":null}]},{"id":"2f1310a9562c4d469e094f006e342bf6","listId":null,"moduleSign":"screen","sort":11,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":1,"multipleLineList":null,"imgList":[{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"888","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1805135504801583000,"imageUrl":"/zindex/zindexpic/7c467a6d4c5f49009d66a0cc146d0b86.jpeg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":"","showAliMini":null,"memberGrade":null}],"suspensionList":null,"waterfallsFlowList":null},{"id":"d21e725a4a4a4bcd80f0ea4c0c86ead8","listId":null,"moduleSign":"suspen","sort":12,"brandLogoTitle":null,"brandLogoBg":null,"showAliMini":0,"multipleLineList":null,"imgList":[{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"悬浮图标","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1699965970679099400,"imageUrl":"/zindex/zindexpic/40cd5cb024fc4c6c8ba49ef6abca60a2.jpg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":0,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":[]}],"suspensionList":[{"id":null,"groupId":null,"mallId":null,"moduleId":null,"ballName":"悬浮图标","sort":1,"appFlag":null,"appUrl":null,"htmlUrl":null,"title":null,"folderId":null,"imageId":1699965970679099400,"imageUrl":"/zindex/zindexpic/40cd5cb024fc4c6c8ba49ef6abca60a2.jpg","linkType":"2","insideType":"","outsideType":"","outsideAppid":"","outsideUrlName":"","outsideUrl":"","insideUrl":"","openFlag":null,"beginTime":null,"endTime":null,"createDate":null,"creator":null,"updateDate":null,"updater":null,"deptId":null,"imageCheckUrl":null,"imageCheckId":null,"fontSelectColor":null,"tpId":null,"salePrice":null,"marketPrice":null,"shopName":null,"versions":null,"insideId":"","activityName":"","activityParams":null,"pageUrl":null,"activityType":null,"titleInput":false,"twolevelLinkage":null,"circleAuthorize":0,"memberAuthorize":0,"showImgFlag":null,"shopno":null,"resourcesType":2,"convertible":null,"fontUnSelectColor":null,"showTime":null,"showAliMini":null,"memberGrade":[]}],"waterfallsFlowList":null}]','2022-12-15 10:03:32','admin','2025-01-15 15:26:51','nancy.xie');



DROP TABLE IF EXISTS `tb_subscription_record`;
CREATE TABLE `tb_subscription_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `user_id` varchar(128) NOT NULL DEFAULT '' COMMENT '业务人员id',
    `status` int(10) NOT NULL DEFAULT '0' COMMENT '订阅状态(0: 取消订阅 1:订阅成功)',
    `subscription_type` int(10) NOT NULL DEFAULT '0' COMMENT '订阅类型(0:积分拦截 1:其它)',
    `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_subscription_record (id, group_id,mall_id,user_id,status,subscription_type,create_user,create_date) VALUES
(1,'1','1','',1,1,'','2024-07-16 14:58:24'),
(2,'1569640119912222722','1805089581344604176','1067246875800000001',1,0,'nancy','2024-07-16 16:30:44'),
(3,'8aaa82ea804d07cd01805174dd3b000c','8aaa81cb7c836c6b017c83e46b110001','1067246875800000001',0,0,'admin','2024-07-17 11:51:17');



DROP TABLE IF EXISTS `tb_brand_guide_collection`;
CREATE TABLE `tb_brand_guide_collection` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL COMMENT '集团id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `vipcode` varchar(128) NOT NULL COMMENT '会员号',
    `brand_guide_id` varchar(128) NOT NULL COMMENT '品牌导览id',
    `status` tinyint unsigned NOT NULL DEFAULT 1 COMMENT '是否有效，1: 有效，0: 无效',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
);
INSERT INTO `tb_brand_guide_collection` (`id`, `group_id`, `mall_id`, `vipcode`, `brand_guide_id`, `status`, `create_date`)
VALUES
(1, '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', 'KERRY100213877', '1450371719088406587', 1, '2024-07-19 14:33:18'),
(2, '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', 'KERRY100213877', '1450371719088406585', 1, '2024-07-19 16:27:13'),
(3, '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', 'KERRY100216246', '1448919585516011521', 1, '2024-07-22 18:00:17'),
(4, '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', 'KERRY100213686', '111', 1, now());

DROP TABLE IF EXISTS `tb_ocr_callback_record`;
CREATE TABLE `tb_ocr_callback_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `task_id` varchar(128) NOT NULL COMMENT '微信openId',
    `photo_id` bigint NOT NULL COMMENT '拍照积分审核记录id',
    `content` text COMMENT '合合回调内容',
    `create_date` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
);

INSERT INTO `tb_ocr_callback_record` (`id`, `task_id`, `photo_id`, `content`, `create_date`)
VALUES
(24, '240731_621add5716c547f6883e020a29366900_uat', 29, '{\"isPass\":false,\"isRobot\":false,\"mall\":\"8a88835c7cd96d31017cda2fd9910000\",\"shop\":\"JAKC00011150\",\"amount\":\"120.00\",\"ticketNo\":\"4200000\",\"transTime\":\"2024-07-30 16:34:10\",\"rejectReason\":{\"id\":12,\"name\":\"小票信息不完整-五要素不全\",\"missingFields\":[\"shop\",\"amount\",\"ticketNo\",\"transTime\"],\"type\":\"\"},\"task_id\":\"240731_621add5716c547f6883e020a29366900_uat\"}', '2024-07-31 10:02:50');

DROP TABLE IF EXISTS `tb_member_asset_invalid`;
CREATE TABLE `tb_member_asset_invalid` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `group_id` varchar(255) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` varchar(120) NOT NULL DEFAULT '' COMMENT '商场编号',
    `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号码',
    `current_points` double NOT NULL DEFAULT '0' COMMENT '当前积分',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员编号（规则生成）',
    `status` varchar(8) NOT NULL DEFAULT '' COMMENT '会员状态（0:冻结；1:非冻结)',
    `wx_open_market` varchar(64) NOT NULL DEFAULT '' COMMENT ',微信激活商场',
    `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间即加入日期',
    `kip_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'kip对应的user_id',
    `is_completed` varchar(20) NOT NULL DEFAULT '' COMMENT '是否完善信息（0:未完善；1:完善)',
    `remark` varchar(4000) NOT NULL DEFAULT '' COMMENT '备注',
    `grade` varchar(32) NOT NULL DEFAULT '' COMMENT '等级[编码]',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间即加入日期',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人',
    `whether_blacklist` int(11) NOT NULL DEFAULT '1' COMMENT '是否黑名单(0:是 1:不是),供活动使用',
    `register_source` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源',
    `register_source_label` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源-附注信息(例如：活动，电子券)',
    `register_source_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源-备注信息',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_sys_dict`;
CREATE TABLE `tb_sys_dict` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增唯一主键id',
    `pid` bigint(20) NOT NULL DEFAULT '0' COMMENT '上级ID，一级为0',
    `dict_type` varchar(50) NOT NULL DEFAULT '' COMMENT '字典类型',
    `dict_name` varchar(255) NOT NULL DEFAULT '' COMMENT '字典名称',
    `dict_value` varchar(255) NOT NULL DEFAULT '' COMMENT '字典值',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(255) NOT NULL DEFAULT '' COMMENT '创建者',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(255) NOT NULL DEFAULT '' COMMENT '更新者',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_sys_dict (id, pid,dict_type,dict_name,dict_value,remark,sort,create_date,create_user,update_date,update_user) VALUES
(70, 69,'XS0075','加速积分补发','XS0075','add',0,'2022-04-13 11:12:52','1067246875800000001','2022-07-20 16:25:25','1067246875800000001');

DROP TABLE IF EXISTS `tb_activity_promotion_join_vip`;
CREATE TABLE `tb_activity_promotion_join_vip` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `promotion_id` varchar(128) NOT NULL COMMENT '营销积分活动id',
    `promotion_condition_group_id` varchar(128) NOT NULL COMMENT '活动组id',
    `promotion_condition_id` varchar(128) NOT NULL COMMENT '活动组实体id',
    `member_grade` varchar(128) DEFAULT NULL COMMENT '会员等级',
    `join_time_type` int(1) DEFAULT NULL COMMENT '入会时间类型（0、当日；1、次日起）',
    `join_time` int(11) DEFAULT NULL COMMENT '入会多少天内',
    `sale_rule_type` int(1) DEFAULT NULL COMMENT '消费规则条件（0、所有消费；1、完成）',
    `sale_num` int(3) DEFAULT NULL COMMENT '消费多少次后',
    `former_num` int(11) DEFAULT NULL COMMENT '前多少次消费',
    `creator` bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
    `update_date` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_activity_promotion_join_vip (id, promotion_id,promotion_condition_group_id,promotion_condition_id,member_grade,join_time_type,join_time,sale_rule_type,sale_num,former_num,creator,create_date,updater,update_date) VALUES
(1, '2280d233295e4b8b90d409c600c777fc','974b181e1fcb4e0d874eddadc5ddba1e','5bbe65a48ee549c784c8bd0cb4be2a77','4',0,NULL,0,NULL,NULL,1067246875800000001,'2022-11-16 11:24:50',NULL,NULL),
(2, '1f1e85a57cb84382b3d1d10c6c1432de','2c99b8b63e804b13b1b091a634760e1d','e220ba8ffe374d9e81681540d1e7b36e','1',0,NULL,0,NULL,NULL,1067246875800000001,'2022-11-16 11:25:19',NULL,NULL);

DROP TABLE IF EXISTS `tb_batch_point_present_detail`;
CREATE TABLE `tb_batch_point_present_detail` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `group_id` varchar(255) NOT NULL COMMENT '集团ID',
    `record_id` varchar(50) NOT NULL COMMENT '导入记录id',
    `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态(1:未生效，2:已生效，3:已过期)',
    `mobile` varchar(64) NOT NULL DEFAULT '' COMMENT '会员手机号',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员号',
    `current_integral` int(11) NOT NULL DEFAULT '0' COMMENT '当前积分',
    `exec_result` tinyint(2) NOT NULL DEFAULT '1' COMMENT '执行结果(1:成功，0:失败)',
    `exec_remark` varchar(128) NOT NULL COMMENT '执行备注',
    `import_integral` int(11) NOT NULL DEFAULT '0' COMMENT '导入积分',
    `valid_time` datetime NOT NULL COMMENT '生效时间',
    `integral_type` varchar(64) NOT NULL DEFAULT '0' COMMENT '赠送积分类型',
    `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
    `overdue_time` datetime NOT NULL COMMENT '过期时间',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `creator` bigint(20) NOT NULL COMMENT '创建人id',
    `create_user` varchar(64) NOT NULL COMMENT '创建人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人id',
    `update_user` varchar(64) NOT NULL DEFAULT 'SYS' COMMENT '更新人',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_batch_point_present_detail (id, group_id,record_id,status,mobile,vipcode,current_integral,exec_result,exec_remark,import_integral,valid_time,integral_type,remark,overdue_time,create_time,creator,create_user,update_time,updater,update_user) VALUES
(5, '4028e3817c2b3f79017c2b48c54c0000','1090c1d50b7e42d48f20d82019d7af42',3,'15868844766','',0,0,'未查询到该会员',10,'2023-08-04 15:47:00','XS0076','','2023-08-04 16:59:06','2023-08-07 15:09:09',1,'nancy','2023-08-07 15:09:09',1,'nancy'),
(6, '4028e3817c2b3f79017c2b48c54c0000','1090c1d50b7e42d48f20d82019d7af42',3,'2839','',0,0,'未查询到该会员',1,'2023-08-04 15:47:00','XS0076','','2023-08-04 16:59:06','2023-08-07 15:09:09',1,'nancy','2023-08-07 15:09:09',1,'nancy'),
(7, '4028e3817c2b3f79017c2b48c54c0000','9d48c5f80970458a87cc678b34d126f6',3,'17521276852','KERRY100211498',270,1,'成功',50,'2023-07-06 10:00:00','XS0076','赠送50积分','2023-07-04 11:59:06','2023-08-16 17:22:28',1,'nancy','2023-08-16 17:22:28',1,'nancy');

DROP TABLE IF EXISTS `tb_integral_activity_rate`;
CREATE TABLE `tb_integral_activity_rate` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `reward_node` varchar(128) NOT NULL COMMENT '活动id',
    `grade_id` varchar(128) NOT NULL COMMENT '会员等级code',
    `grade_name` varchar(128) DEFAULT NULL COMMENT '会员等级名称',
    `money` decimal(10,0) DEFAULT NULL COMMENT '金额',
    `point_num` decimal(10,1) DEFAULT NULL COMMENT '积分数',
    `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用，1: 启用，0: 禁用',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_integral_activity_rate (id, group_id,mall_id,reward_node,grade_id,grade_name,money,point_num,status,create_date,creator,update_date,updater) VALUES
(1, '4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','001','4e3a89018a7d4e1a9a6ce33328229fea','铂金卡',1,4.0,1,'2023-05-29 15:59:08','nancy','2023-05-29 15:59:08','nancy'),
(2, '4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','001','e2b242dd5431467eb07a7b14b8157c5e','金卡',1,3.0,1,'2023-05-29 15:59:08','nancy','2023-05-29 15:59:08','nancy');

DROP TABLE IF EXISTS `tb_ko_integral_clear_record`;
CREATE TABLE `tb_ko_integral_clear_record` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团id',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员号',
    `get_points` int(11) NOT NULL DEFAULT '0' COMMENT '获得积分数',
    `cost_points` int(11) NOT NULL DEFAULT '0' COMMENT '消耗积分数',
    `clear_points` int(11) NOT NULL DEFAULT '0' COMMENT '清零积分数',
    `points_num` int(11) NOT NULL DEFAULT '0' COMMENT '实际操作积分数',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_ko_integral_clear_record (group_id,vipcode,get_points,cost_points,clear_points,points_num,create_date,update_date) VALUES
('8a88835c7cd96d31017cda3f77e80003','01000002280',44187,0,-44000,187,'2023-04-18 10:14:46','2023-04-18 10:14:46'),
('8a88835c7cd96d31017cda3f77e80003','01000002523',-88,0,-461,-461,'2023-04-18 10:14:46','2023-04-18 10:14:46');

DROP TABLE IF EXISTS `tb_member_benefit`;
CREATE TABLE `tb_member_benefit` (
    `id` varchar(50) NOT NULL COMMENT '主键',
    `mall_id` varchar(120) NOT NULL COMMENT '商场编号',
    `member_grade` varchar(32) NOT NULL COMMENT '会员等级',
    `status` int(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `group_id` varchar(255) NOT NULL COMMENT '集团ID',
    `creator` bigint(20) NOT NULL COMMENT '创建人',
    `create_user` varchar(60) NOT NULL COMMENT '创建者',
    `create_date` datetime NOT NULL COMMENT '创建时间',
    `updater` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
    `update_user` varchar(60) NOT NULL DEFAULT 'SYS' COMMENT '更新者',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_del` int(1) NOT NULL DEFAULT '0' COMMENT '是否删除;1删除/0正常',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_member_benefit (id,mall_id,member_grade,status,group_id,creator,create_user,create_date,updater,update_user,update_date,is_del) VALUES
('97c29c3d7d634a088d6353dbc948b168','8aaa80b47c784020017c78b00d060022','22',0,'4028e3817c2b3f79017c2b48c54c0000',1,'nancy','2023-08-09 09:21:38',0,'xiaoqiang.zhang','2024-04-26 14:44:02',0),
('c80051fb21224c63b30fc61255c3d75f','8aaa80b47c784020017c78b00d060022','3',0,'4028e3817c2b3f79017c2b48c54c0000',1,'nancy','2023-08-09 09:20:34',0,'','2023-08-09 09:20:34',0);

DROP TABLE IF EXISTS `tb_member_equity`;
CREATE TABLE `tb_member_equity` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `mall_id` varchar(120) NOT NULL DEFAULT '' COMMENT '商场编号',
    `content` mediumtext COMMENT '会员权益内容（代码）',
    `dept_id` varchar(120) NOT NULL DEFAULT '' COMMENT '部门ID',
    `group_id` varchar(255) NOT NULL DEFAULT '' COMMENT '集团ID',
    `type` tinyint(1) NOT NULL COMMENT '1会员权益2优惠信息3服务协议4自助积分指南',
    `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
    `protocol_name` varchar(255) NOT NULL DEFAULT '' COMMENT '协议名称',
    `code` int(11) NOT NULL DEFAULT '0' COMMENT '自助积分指南(1:扫码积分2:微信无感积分3:支付宝无感积分4:客服台积分)',
    `create_user` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` varchar(32) NOT NULL DEFAULT '' COMMENT '更新人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_member_equity (mall_id,content,dept_id,group_id,`type`,title,protocol_name,code,create_user,create_date,update_user,update_date) VALUES
('8aaa80b47c784020017c78b00d060022','<h3><strong>积分方式</strong></h3><p>1.扫码积分：微信扫码当日消费小票底部积分二维码。</p><p>2.服务台积分：持当日消费小票至商城L1服务台。</p><p>3.支付宝无感积分：付款前绑定支付宝无感积分。</p><h3><strong>积分比率</strong></h3><p>1元积1分：国际精品、服装、鞋包、化妆品</p><p>5元积1分：餐饮、家具、家居、美容、美发、健身、生活服务、电影院</p><p>10元积1分：超市、电器、书店</p><p>不积分：汽车、展览、活动等</p><p>注：各参与商户所属的类型、上述未涉及的商户/商品类型的积分规则及其他具体细则以商场制定客服服务台的说明为准。</p><h3><strong>积分方式</strong></h3><p>需凭当日有效消费小票原件积分（不接受复印件、手写收据、重复打印的小票），过期无效，且不可重复积分。</p><p>每年积分将于次年12月31日清零。</p>','','',4,'','',0,'admin','2023-05-09 10:43:04','','2023-05-09 10:43:04'),
('8aaa80b47c784020017c78b00d060022','<h3><strong>积分方式</strong></h3><p>1.扫码积分扫码积分指南：微信扫码当日消费小票底部积分二维码。</p><p>2.服务台积分：持当日消费小票至商城L1服务台。</p><p>3.支付宝无感积分：付款前绑定支付宝无感积分。</p><h3><strong>积分比率</strong></h3><p>1元积1分：国际精品、服装、鞋包、化妆品</p><p>5元积1分：餐饮、家具、家居、美容、美发、健身、生活服务、电影院</p><p>10元积1分：超市、电器、书店</p><p>不积分：汽车、展览、活动等</p><p>注：各参与商户所属的类型、上述未涉及的商户/商品类型的积分规则及其他具体细则以商场制定客服服务台的说明为准。</p><h3><strong>积分方式</strong></h3><p>需凭当日有效消费小票原件积分（不接受复印件、手写收据、重复打印的小票），过期无效，且不可重复积分。</p><p>每年积分将于次年12月31日清零。</p>','','',1,'','',0,'admin','2023-05-09 10:59:09','','2023-05-09 10:59:09');

DROP TABLE IF EXISTS `tb_member_grade_change_detail`;
CREATE TABLE `tb_member_grade_change_detail` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商场号',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员号',
    `nick_name` varchar(255) NOT NULL DEFAULT '' COMMENT '昵称',
    `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号',
    `old_grade` varchar(32) NOT NULL DEFAULT '' COMMENT '会员旧等级',
    `new_grade` varchar(32) NOT NULL DEFAULT '' COMMENT '会员新等级',
    `remark` varchar(128) NOT NULL DEFAULT '' COMMENT '备注',
    `change_type` int(11) NOT NULL DEFAULT '0' COMMENT '卡变动类型（0、人工调整-降级；1、人工调整-升级；2、每日消费金额-升级；3、累计消费金额-升级；4、累计消费金额降级；5、入会当日消费-升级；6、会员身份认证-升级；7、累计消费金额-保级；8、每日消费金额-保级；9、会员身份认证-保级）',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_member_grade_change_detail (id,group_id,mall_id,vipcode,nick_name,mobile,old_grade,new_grade,remark,change_type,create_date,create_user) VALUES
('07594f4819994ce7b4fac419a97ea6e1','4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','KERRY100213966','微信用户','13014614228','1','3','每日消费金额满足升级标准：在非服务类和汽车类中消费20000.0元',2,'2023-12-05 12:33:21','system'),
('08735e0b3db3424f8ae0b82f826dc691','4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022','KERRY100213231','泰森','13156669811','1','22','变更会员等级信息',1,'2024-06-21 11:57:21','admin');

DROP TABLE IF EXISTS `tb_member_grade_effective_config`;
CREATE TABLE `tb_member_grade_effective_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) DEFAULT NULL COMMENT '集团ID',
    `enable_downgrade_job` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否开启保级降级定时JOB(0: 否；1:是)',
    `enable_refund_downgrade` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否开启退货降级判断(0: 否；1:是)',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(128) DEFAULT NULL COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(128) DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_member_grade_rule`;
CREATE TABLE `tb_member_grade_rule` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `group_id` varchar(255) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(120) NOT NULL DEFAULT '' COMMENT '商场id',
    `grade_code` varchar(64) NOT NULL DEFAULT '' COMMENT '会员等级编码',
    `rule_type` int(11) NOT NULL COMMENT '规则类型 1、每日消费金额升级；2、累计消费金额升级；3、入会当日消费金额升级；4、会员身份认证升级；5、每日消费保级；6、累计消费保级；7、会员身份认证保级；8、会员月累计消费升级；9、会员月累计消费保级',
    `money` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '消费金额（适用1、2、3）',
    `formats` varchar(5000) NOT NULL DEFAULT '' COMMENT '业态编码（多条业态编码逗号分隔，为空则为全业态)',
    `formats_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '业态描述',
    `sum_type` int(11) NOT NULL DEFAULT '1' COMMENT '累计类型（1、自然年；2、周期年；适用于规则类型条件2和6）',
    `type` int(11) NOT NULL DEFAULT '1' COMMENT '类型（0、保级；1、升级）',
    `is_count` int(11) NOT NULL DEFAULT '0' COMMENT '是否用于计算成长进度条（0、否；1、是）',
    `pid` bigint(20) NOT NULL DEFAULT '0' COMMENT '上级id，如果是主规则则上级id默认为0',
    `certification` varchar(255) NOT NULL DEFAULT '' COMMENT '会员认证 office#apartment 多个用#分隔',
    `effective_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(20) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(20) NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_member_grade_rule (group_id,mall_id,grade_code,rule_type,money,formats,formats_desc,sum_type,`type`,is_count,pid,certification,create_date,create_user,update_date,update_user) VALUES
('8aaa81947c6e1ca0017c73c13cc30006','','3',5,11111.00,'all,1,2,110,120','',0,0,0,0,'','2024-01-22 14:44:23','','2024-01-22 14:44:23',''),
('8aaa81947c6e1ca0017c73c13cc30006','','3',1,11.00,'all,1,2,110,120','',0,1,0,0,'','2024-01-22 14:44:23','','2024-01-22 14:44:23','');

DROP TABLE IF EXISTS `tb_member_grade_rule_process`;
CREATE TABLE `tb_member_grade_rule_process` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团id',
    `grade` varchar(16) NOT NULL DEFAULT '' COMMENT '会员等级编码',
    `year_grade_rule_id` varchar(128) NOT NULL DEFAULT '' COMMENT '年等级规则id',
    `month_grade_rule_id` varchar(128) NOT NULL DEFAULT '' COMMENT '月等级规则id',
    `day_grade_rule_id` varchar(128) NOT NULL DEFAULT '' COMMENT '日等级规则id',
    `up_progress_text` varchar(3000) NOT NULL DEFAULT '' COMMENT '升级成长进度条文案',
    `up_progress_text_not_show` varchar(1000) NOT NULL DEFAULT '' COMMENT '升级成长进度条文案-不显示',
    `sustain_progress_text` varchar(3000) NOT NULL DEFAULT '' COMMENT '保级成长进度条文案',
    `sustain_progress_text_max` varchar(3000) NOT NULL DEFAULT '' COMMENT '保级成长进度条最高卡等文案',
    `sustain_progress_text_not_show` varchar(1000) NOT NULL DEFAULT '' COMMENT '保级成长进度条文案-不显示',
    `create_user` varchar(20) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_member_grade_rule_process (group_id,grade,year_grade_rule_id,month_grade_rule_id,day_grade_rule_id,up_progress_text,up_progress_text_not_show,sustain_progress_text,sustain_progress_text_max,sustain_progress_text_not_show,create_user,create_date,update_user,update_date) VALUES
('8aaa81947c6e1ca0017c73c13cc30006','3','','','','升级进度条文案展示 :{CurrentCardLevel}','升级进度条文案展示 :{CurrentCardLevel}','保级进行中时-进度条文案展示 ','保级成功时-进度条文案展示(最高卡等维护)','','admin','2024-01-23 15:53:21','admin','2024-01-23 15:53:21'),
('8aaa82ea804d07cd01805175c3a8000d','1','','','61','','','','','','admin','2024-03-25 11:49:38','admin','2024-03-25 11:49:52');

INSERT INTO `tb_ocr_callback_record` (`id`, `task_id`, `photo_id`, `content`, `create_date`) VALUES (61, '240802_857af15d552148a3aabec151e78233ec_uat', 66, '{\"isPass\":true,\"isRobot\":false,\"mall\":\"8aaa81cb7c836c6b017c83e2c76f0000\",\"shop\":\"JAKC00011019\",\"amount\":\"1500.00\",\"ticketNo\":\"2024080242000001659\",\"transTime\":\"2024-08-02 16:59:10\",\"rejectReason\":{\"id\":12,\"name\":\"小票信息不完整-五要素不全\",\"missingFields\":[\"shop\",\"amount\",\"transTime\"],\"type\":\"\"},\"task_id\":\"240802_857af15d552148a3aabec151e78233ec_uat\"}', '2024-08-02 17:01:36');
INSERT INTO `tb_ocr_callback_record` (`id`, `task_id`, `photo_id`, `content`, `create_date`) VALUES (62, '240802_f1604a31a946413785409d9155e436a2_uat', 67, '{\"isPass\":true,\"isRobot\":false,\"mall\":\"8aaa81cb7c836c6b017c83e2c76f0000\",\"shop\":\"JAKC00011019\",\"amount\":\"1800.00\",\"ticketNo\":\"2024080242000001710\",\"transTime\":\"2024-08-02 17:10:10\",\"rejectReason\":{\"id\":12,\"name\":\"小票信息不完整-五要素不全\",\"missingFields\":[\"shop\",\"amount\",\"transTime\"],\"type\":\"\"},\"task_id\":\"240802_f1604a31a946413785409d9155e436a2_uat\"}', '2024-08-02 17:10:55');
INSERT INTO `tb_ocr_callback_record` (`id`, `task_id`, `photo_id`, `content`, `create_date`) VALUES (63, '240802_bbe76eab94814f4ea886e8b73f60b1d8_uat', 68, '{\"isPass\":true,\"isRobot\":false,\"mall\":\"8aaa81cb7c836c6b017c83e2c76f0000\",\"shop\":\"JAKC00011019\",\"amount\":\"2000.00\",\"ticketNo\":\"2024080241000001715\",\"transTime\":\"2024-08-02 17:15:10\",\"rejectReason\":{\"id\":12,\"name\":\"小票信息不完整-五要素不全\",\"missingFields\":[\"shop\",\"amount\",\"transTime\"],\"type\":\"\"},\"task_id\":\"240802_bbe76eab94814f4ea886e8b73f60b1d8_uat\"}', '2024-08-02 17:14:16');
INSERT INTO `tb_ocr_callback_record` (`id`, `task_id`, `photo_id`, `content`, `create_date`) VALUES (64, '240809_43b77668d12949ccbf8906c2a8f71bca_uat', 69, '{\"isPass\":true,\"isRobot\":false,\"mall\":\"8aaa81cb7c836c6b017c83e2c76f0000\",\"shop\":\"JAKC00011019\",\"amount\":\"2000.00\",\"ticketNo\":\"2024080241000001715\",\"transTime\":\"2024-08-02 17:15:10\",\"rejectReason\":{\"id\":12,\"name\":\"小票信息不完整-五要素不全\",\"missingFields\":[\"shop\",\"amount\",\"transTime\"],\"type\":\"\"},\"task_id\":\"240809_43b77668d12949ccbf8906c2a8f71bca_uat\"}', '2024-08-09 15:05:43');

DROP TABLE IF EXISTS `tb_points_intercept_config`;
CREATE TABLE `tb_points_intercept_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `rule_type` int(10) NOT NULL COMMENT '规则类型 1 单人单店铺',
    `time_type` varchar(128) NOT NULL COMMENT '时间类型 1:日 2:自然周 3：周期周(连续7天) 4:自然月',
    `count_type` int(10) NOT NULL COMMENT '计数类型 1 累计 ',
    `count` int(11) NOT NULL DEFAULT '0' COMMENT '次数',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    `white_list` int(11) NOT NULL DEFAULT '0' COMMENT '是否配置白名单，0:否，1:是',
    `shop_no` varchar(5000) NOT NULL DEFAULT '' COMMENT '白名单店铺列表，用英文逗号分隔',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_points_intercept_config (group_id,mall_id,rule_type,time_type,count_type,count,create_date,create_user,update_date,update_user,white_list,shop_no) VALUES
('4028e3817c2b3f79017c2b48c54c0000','8aaa80b47c784020017c78b00d060022',1,'1',1,100,'2024-04-26 12:24:09','admin','2024-08-26 14:22:40','admin',0,'');

DROP TABLE IF EXISTS `tb_points_intercept_approve`;
CREATE TABLE `tb_points_intercept_approve` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号',
    `kip_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'kip对应的user_id',
    `sale_id` varchar(64) NOT NULL DEFAULT '' COMMENT '销售单号唯一主键',
    `order_no` varchar(64) NOT NULL DEFAULT '' COMMENT '销售单号(流水号)',
    `shop_no` varchar(120) NOT NULL DEFAULT '' COMMENT '店铺编号',
    `total_amount` decimal(30,2) NOT NULL DEFAULT '0.00' COMMENT '销售单号总金额',
    `discount_amount` decimal(30,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
    `pay_amount` decimal(30,2) NOT NULL DEFAULT '0.00' COMMENT '消费金额',
    `sale_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '消费时间',
    `status` int(11) NOT NULL DEFAULT '0' COMMENT '退货状态(0->未退货, 1->退货,2->部分退款,3->待审核,4->审核驳回)',
    `sale_type` varchar(32) NOT NULL DEFAULT '1' COMMENT '销售类型1.CRM销售,2.POS销售,3.微信照片,4.微信扫码,5.口碑销售',
    `remark_name` varchar(120) NOT NULL DEFAULT '1' COMMENT '积分说明',
    `refund_amount` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '退货金额',
    `refund_points_num` int(11) NOT NULL DEFAULT '0' COMMENT '退货积分数',
    `app_id` varchar(120) NOT NULL DEFAULT '1' COMMENT '支付宝推送记录对应的appId',
    `wx_or_ali_id` varchar(120) NOT NULL DEFAULT '1' COMMENT '微信或支付宝暂存的用户唯一id',
    `intercept_reason` varchar(1000) NOT NULL DEFAULT '' COMMENT '异常原因',
    `reject_reason` varchar(1000) NOT NULL DEFAULT '' COMMENT '驳回原因',
    `img_url` varchar(200) NOT NULL DEFAULT '' COMMENT '小票地址',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(128) NOT NULL DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`)
);

INSERT INTO tb_points_intercept_approve (group_id,mall_id,mobile,kip_user_id,sale_id,order_no,shop_no,total_amount,discount_amount,pay_amount,sale_date,status,sale_type,remark_name,refund_amount,refund_points_num,app_id,wx_or_ali_id,intercept_reason,reject_reason,img_url,create_date,create_user,update_date,update_user) VALUES
    ('8aaa82ea804d07cd01805174dd3b000c','8aaa81cb7c836c6b017c83e2c76f0000','17602115550','2c9d850c86beab150186bf2fff7d0000','49b39c04df9d4b57afac8483dfe5588c','2024080142000001505','JAKC00011019',0.00,0.00,80.00,'2024-08-01 10:34:10',4,'3','拍照积分',0.00,0,'','','自然周超过或等于1次','2er','','2024-08-01 15:06:32','999','2024-08-20 13:50:58','admin');


DROP TABLE IF EXISTS `tb_insensate_points_push_record`;
CREATE TABLE `tb_insensate_points_push_record` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `open_id` varchar(128) NOT NULL COMMENT '微信openid或支付宝user_id。根据origin判断',
    `transaction_id` varchar(32) NOT NULL COMMENT '支付订单号',
    `event_type` tinyint(1) unsigned NOT NULL COMMENT '事件类型。1支付，2退款。对应IntegralConstant.WX_PAY_SUCCESS等枚举',
    `transaction_info` varchar(8126) NOT NULL DEFAULT '' COMMENT '支付具体信息',
    `origin` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '来源，0微信，1支付宝',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_insensate_points_push_record (open_id,transaction_id,event_type,transaction_info,origin,create_date,update_date) VALUES
                                                                                                                                    ('oudWQ5WxMpOkjuLjEKB8884Q4DFs','4200001619202210203322112360',1,'{"amount":1700,"appId":"wxd830fe4d1e04988e","commitTag":"manual","mchId":"1614666071","merchantName":"杭州嘉里中心","openId":"oudWQ5WxMpOkjuLjEKB8884Q4DFs","shopName":"茉沏","shopNumber":"HKC00021012","timeEnd":"2022-10-20T14:18:26+08:00","transactionId":"4200001619202210203322112360"}',0,'2022-10-20 14:51:08','2022-10-20 14:51:08'),
                                                                                                                                    ('oudWQ5XqkJbskiAQ1VJ-6mjp0TN4','Evan-101',1,'{"amount":200,"appId":"wxd678efh567hg6787","commitTag":null,"mchId":"1614666071","merchantName":"腾讯广场","openId":"oudWQ5XqkJbskiAQ1VJ-6mjp0TN4","shopName":"微信支付","shopNumber":"HKC00021012","timeEnd":"2020-05-20T13:29:35+08:00","transactionId":"Evan-101"}',0,'2022-10-20 15:25:36','2022-10-20 15:25:36');

DROP TABLE IF EXISTS `tb_insensate_points_repeat_log`;
CREATE TABLE `tb_insensate_points_repeat_log` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `transaction_id` varchar(64) NOT NULL COMMENT '无感积分单号',
    `origin` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '来源，0微信，1支付宝',
    `repeat_sale_id` varchar(32) NOT NULL COMMENT '重复销售记录id',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_insensate_points_repeat_log (transaction_id,origin,repeat_sale_id,create_date) VALUES
                                                                                                  ('42000198865432123533110321',0,'1bdff70214be4198b01f09a6818eb817','2022-10-26 12:03:20'),
                                                                                                  ('4200001617202211024685168147',0,'10512c8702184c5cb2f363a617f1ecc8','2022-11-02 16:15:37');


INSERT INTO `tb_member_equity` (`id`, `mall_id`, `content`, `dept_id`, `group_id`, `type`, `title`, `protocol_name`, `code`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES (274, '8aaa81cb7c836c6b017c83e46b110001', '<p>测试</p>', '', '8aaa82ea804d07cd01805174dd3b000c', 1, '', '', 0, 'bert zhang', '2024-09-14 10:41:10', '', '2024-09-14 10:41:10');
INSERT INTO `tb_member_equity` (`id`, `mall_id`, `content`, `dept_id`, `group_id`, `type`, `title`, `protocol_name`, `code`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES (277, '8aaa81cb7c836c6b017c83e46b110001', '<p>自助积分页备注<img src=\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/quillPic/64cb59d8a9ea4aa9bab21b1593cd8715.jpg\"></p>', '', '8aaa82ea804d07cd01805174dd3b000c', 11, '', '', 0, 'Ryan Yang', '2024-10-22 10:22:27', '', '2024-10-22 10:22:27');
INSERT INTO `tb_member_equity` (`id`, `mall_id`, `content`, `dept_id`, `group_id`, `type`, `title`, `protocol_name`, `code`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES (278, '8aaa81cb7c836c6b017c83e2c76f0000', '<p>自助积分页面备注<img src=\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/quillPic/41c3182af28c4e24a66ba49a89caf777.jpg\"></p>', '', '8aaa82ea804d07cd01805174dd3b000c', 11, '', '', 0, 'Ryan Yang', '2024-10-22 11:17:21', '', '2024-10-22 11:17:21');
INSERT INTO `tb_member_equity` (`id`, `mall_id`, `content`, `dept_id`, `group_id`, `type`, `title`, `protocol_name`, `code`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES (279, '8aaa81cb7c836c6b017c83e46b110001', '<p><img src=\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/quillPic/8e1d7fbc0c564d39b90530db5bf21506.jpg\"></p>', '', '8aaa82ea804d07cd01805174dd3b000c', 10, '', '', 0, 'Ryan Yang', '2024-10-23 09:25:01', '', '2024-10-23 09:25:01');
INSERT INTO `tb_member_equity` (`id`, `mall_id`, `content`, `dept_id`, `group_id`, `type`, `title`, `protocol_name`, `code`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES (280, '8aaa81cb7c836c6b017c83e46b110001', '<p><img src=\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/pointGuide/4d86f3e23ccf45b1863d04781a677560.jpg\"></p>', '', '8aaa82ea804d07cd01805174dd3b000c', 12, '', '', 0, 'Ryan Yang', '2024-10-23 15:54:08', 'Ryan Yang', '2024-10-24 10:24:26');


DROP TABLE IF EXISTS `tb_member_register_source`;
CREATE TABLE `tb_member_register_source` (
    `id` bigint NOT NULL COMMENT '主键id',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员id',
    `kip_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '会员id',
    `utm_lbs` varchar(64) NOT NULL DEFAULT '' COMMENT '商场id',
    `channel` varchar(64) NOT NULL DEFAULT '' COMMENT '当前渠道',
    `utm_channel` varchar(64) NOT NULL DEFAULT '' COMMENT '来源渠道',
    `utm_method` varchar(64) NOT NULL DEFAULT '' COMMENT '方式',
    `utm_source` varchar(64) NOT NULL DEFAULT '' COMMENT '来源',
    `utm_function` varchar(64) NOT NULL DEFAULT '' COMMENT '功能',
    `utm_user` varchar(64) NOT NULL DEFAULT '' COMMENT '分享人',
    `page_path` varchar(255) NOT NULL DEFAULT '' COMMENT '页面连接',
    `original_params` text NOT NULL DEFAULT '' COMMENT '原始参数',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);
--线上商城积分比例配置
INSERT INTO `tb_online_shop_rate` (`group_id`, `mall_id`, `grade`, `business_type`, `money`, `point_num`, `status`, `create_date`, `creator`, `update_date`, `updater`) VALUES ('4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '22', 'YM0120', 1.0, 1.0, 0, '2024-11-13 10:11:07', 'bert zhang', '2024-11-13 10:11:07', 'bert zhang');
-- 会员信息
INSERT INTO `tb_member_asset` (`crm_id`, `group_id`, `mall_id`, `mobile`, `current_points`, `vipcode`, `grade`, `kip_user_id`, `status`, `wx_open_market`, `is_completed`, `remark`, `join_time`, `create_date`, `create_user`, `update_date`, `update_user`, `whether_blacklist`, `register_source`, `register_source_label`, `register_source_remark`) VALUES ('1532', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '***********', 3890, 'KERRY100213877', '22', '2c9d850c870bea4101870cfbc08f0000', 1, '4028e3817bf860f3017bf86279b50001', '1', '11111', '2023-03-23 14:13:49', '2023-03-23 14:13:49', 'sys', '2024-07-30 16:19:38', 'admin', 1, 'miniProgram', '小程序注册', '');

DROP TABLE IF EXISTS `tb_authorizer_user`;
CREATE TABLE `tb_authorizer_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user` varchar(255) NOT NULL DEFAULT '' COMMENT '请求参数用户名(系统用户sys_开头，第三方thirdparty_开头)',
    `password` varchar(255) NOT NULL DEFAULT '' COMMENT '请求参数用户密码',
    `ips` varchar(512) NOT NULL DEFAULT '' COMMENT '用户关联请求ip',
    `is_use` int NOT NULL DEFAULT '1' COMMENT '(0 : 不可用; 1 : 可用)',
    `is_check` int NOT NULL DEFAULT '1' COMMENT '(0 : 不验证; 1 : 验证)',
    `create_user` varchar(255) NOT NULL DEFAULT '' COMMENT '创建用户',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` varchar(255) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `authorizer_wx_type` varchar(255) NOT NULL DEFAULT '' COMMENT '授权类型',
    PRIMARY KEY (`id`)
);

INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES
                                                                                                                                                                         ('pos', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2020-05-26 14:18:25', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('sys_gzh', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2020-05-26 14:18:25', '', '2020-05-26 14:18:25', '4');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('sys_miniprogram', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2020-05-26 14:18:25', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('lanka', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2021-04-19 09:27:05', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('crm', 'qatiiOFk1CwfiSgm8gp67g==', '127.0.0.1', 0, 0, 'admin', '2020-06-13 13:49:52', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('admin', 'qatiiOFk1CwfiSgm8gp67g==', '127.0.0.1', 0, 0, 'admin', '2020-06-13 13:50:45', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('hongxing', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2021-04-19 10:26:18', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('hx', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 0, 'admin', '2021-04-19 10:26:18', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('hf', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 0, 'admin', '2021-04-19 10:26:18', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('ipad', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'ipad', '2020-05-26 14:18:25', 'ipad', '2021-10-20 15:11:24', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('shmini', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2021-04-19 10:26:18', 'admin', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('lifang', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2020-05-26 14:18:25', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('haiding', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2020-05-26 14:18:25', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('kinetic', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2021-11-22 14:18:25', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('guest', 'R18Pb9spyGcreVZ/afz2Ew==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2020-05-26 14:18:25', '密码：RvHIzeRzuLg==', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('kerry_DA', 'qPLIwJ/rf1kwnjxMNjQ0hQ==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2022-03-02 13:52:01', '', '2020-05-26 14:18:25', '1');
INSERT INTO `tb_authorizer_user` (`user`, `password`, `ips`, `is_use`, `is_check`, `create_user`, `create_time`, `update_user`, `update_time`, `authorizer_wx_type`) VALUES ('menjin', 'qatiiOFk1CwfiSgm8gp67g==', '0:0:0:0:0:0:0:1', 1, 1, 'admin', '2022-03-11 13:52:01', '', '2020-05-26 14:18:25', '1');


DROP TABLE IF EXISTS `tb_field_sort`;
CREATE TABLE `tb_field_sort` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团ID',
    `field_name` varchar(64) NOT NULL COMMENT '字段列名称',
    `field_sort` int NOT NULL COMMENT '字段列顺序 0：代表不展示 ；其他大于0的数字依次代表顺序',
    `page_type` varchar(4) NOT NULL COMMENT '页面类型： 1 : 表示拍照积分审核页面',
    `is_initial` int NOT NULL COMMENT '是否默认数据 0：否  1：是',
    `field_cn_name` varchar(128) NOT NULL COMMENT '字段列(中文)名称',
    `user_id` bigint NOT NULL COMMENT '用户id',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `update_user` varchar(128) NOT NULL DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`)
);
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (21, 'mallName', 1, '1', 1, '商场名称', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (61, 'mallName', 1, '1', 0, '商场名称', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (22, 'mobile', 2, '1', 1, '会员手机号', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (62, 'mobile', 2, '1', 0, '会员手机号', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (23, 'vipcode', 3, '1', 1, '会员卡号', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (63, 'vipcode', 3, '1', 0, '会员卡号', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (24, 'nickName', 4, '1', 1, '会员昵称', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (64, 'nickName', 4, '1', 0, '会员昵称', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (25, 'uploadDate', 5, '1', 1, '上传时间', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (65, 'uploadDate', 5, '1', 0, '上传时间', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (26, 'statusStr', 6, '1', 1, '审核状态', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (66, 'statusStr', 6, '1', 0, '审核状态', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (27, 'shopAliasName', 7, '1', 1, '店铺别名', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (67, 'shopAliasName', 7, '1', 0, '店铺别名', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (28, 'shopName', 8, '1', 1, '店铺名称', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (68, 'shopName', 8, '1', 0, '店铺名称', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (29, 'shopNo', 9, '1', 1, '店铺编号', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (69, 'shopNo', 9, '1', 0, '店铺编号', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (30, 'tradingDate', 10, '1', 1, '消费时间', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (70, 'tradingDate', 10, '1', 0, '消费时间', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (31, 'money', 11, '1', 1, '消费金额', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (71, 'money', 11, '1', 0, '消费金额', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (32, 'serialNum', 12, '1', 1, '小票号', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (72, 'serialNum', 12, '1', 0, '小票号', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (33, 'imageUrl', 13, '1', 1, '小票照片', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (73, 'imageUrl', 13, '1', 0, '小票照片', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (34, 'bonus', 14, '1', 1, '获得积分', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (74, 'bonus', 14, '1', 0, '获得积分', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (35, 'orgGrade', 15, '1', 1, '调整前等级', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (75, 'orgGrade', 15, '1', 0, '调整前等级', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (36, 'orgPoints', 16, '1', 1, '调整前积分', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (76, 'orgPoints', 16, '1', 0, '调整前积分', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (37, 'reason', 17, '1', 1, '驳回原因', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (77, 'reason', 17, '1', 0, '驳回原因', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (38, 'checkTime', 18, '1', 1, '审核时间', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (78, 'checkTime', 18, '1', 0, '审核时间', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (39, 'checkName', 19, '1', 1, '审核人', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (79, 'checkName', 19, '1', 0, '审核人', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (40, 'ocrTaskId', 20, '1', 1, 'OCR任务ID', 0, '2024-12-02 16:18:02', '', '2024-12-02 16:18:02', '');
INSERT INTO `tb_field_sort` (`id`, `field_name`, `field_sort`, `page_type`, `is_initial`, `field_cn_name`, `user_id`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (80, 'ocrTaskId', 20, '1', 0, 'OCR任务ID', 95, '2024-12-03 13:47:26', '', '2024-12-03 13:47:26', '');

DROP TABLE IF EXISTS `tb_sale_matched_promotion`;
CREATE TABLE `tb_sale_matched_promotion` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `sell_no` varchar(64) DEFAULT NULL COMMENT '销售单号',
    `integral_adjust_id` varchar(64) DEFAULT NULL COMMENT '积分调整记录id',
    `promotion_id` varchar(64) DEFAULT NULL COMMENT '营销活动id',
    `promotion_name` varchar(255) DEFAULT NULL COMMENT '营销活动名称',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);
INSERT INTO `tb_sale_matched_promotion` (`id`, `sell_no`, `integral_adjust_id`, `promotion_id`, `promotion_name`, `create_date`, `update_date`) VALUES (2, '000331', '48ead58ac95740d2999978e6960f6f56', '2c9e5dc8e01e4a709c2b1f32126c46c4', '入会当日3倍积分', '2023-09-27 16:57:18', '2023-09-27 16:57:18');
INSERT INTO `tb_sale_matched_promotion` (`id`, `sell_no`, `integral_adjust_id`, `promotion_id`, `promotion_name`, `create_date`, `update_date`) VALUES (3, '103093993', 'e4d8dccd3bd9492694f5dcebd2f774f0', '2c9e5dc8e01e4a709c2b1f32126c46c4', '入会当日3倍积分', '2023-10-09 16:46:50', '2023-10-09 16:46:50');
INSERT INTO `tb_sale_matched_promotion` (`id`, `sell_no`, `integral_adjust_id`, `promotion_id`, `promotion_name`, `create_date`, `update_date`) VALUES (4, '1000333', '2d3ea7fe7cf7443ebd3528c155a3cae3', '2c9e5dc8e01e4a709c2b1f32126c46c4', '入会当日3倍积分', '2023-10-09 16:48:17', '2023-10-09 16:48:17');
INSERT INTO `tb_sale_matched_promotion` (`id`, `sell_no`, `integral_adjust_id`, `promotion_id`, `promotion_name`, `create_date`, `update_date`) VALUES (5, '100203', '2c012e4bfe534c8696ac1adef7b38bd8', '1027ec12836f4ad7b026d56f5fad5188', '入会2倍积分', '2023-10-09 16:49:31', '2023-10-09 16:49:31');
INSERT INTO `tb_sale_matched_promotion` (`id`, `sell_no`, `integral_adjust_id`, `promotion_id`, `promotion_name`, `create_date`, `update_date`) VALUES (6, '103003', 'cb38c7d27a9f40a0affbd0cc995d8a74', '1027ec12836f4ad7b026d56f5fad5188', '入会2倍积分', '2023-10-09 16:55:11', '2023-10-09 16:55:11');
INSERT INTO `tb_sale_matched_promotion` (`id`, `sell_no`, `integral_adjust_id`, `promotion_id`, `promotion_name`, `create_date`, `update_date`) VALUES (7, '1030303', 'c803bbaca0c94805b4012bfa72b01a28', '1027ec12836f4ad7b026d56f5fad5188', '入会2倍积分', '2023-10-09 17:49:03', '2023-10-09 17:49:03');
INSERT INTO `tb_sale_matched_promotion` (`id`, `sell_no`, `integral_adjust_id`, `promotion_id`, `promotion_name`, `create_date`, `update_date`) VALUES (8, '000222', '90e1464d5d6349b2abd79c9d45d8bb5e', '1027ec12836f4ad7b026d56f5fad5188', '入会2倍积分', '2023-10-09 17:49:26', '2023-10-09 17:49:26');

DROP TABLE IF EXISTS `tb_member_mall_relation`;
CREATE TABLE `tb_member_mall_relation` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `mobile` varchar(64) NOT NULL DEFAULT '' COMMENT '手机号',
    `name` varchar(64) NOT NULL DEFAULT '' COMMENT '姓名',
    `gender` varchar(64) NOT NULL DEFAULT '' COMMENT '性别',
    `mall_id` varchar(120) NOT NULL DEFAULT '' COMMENT '商场编号',
    `group_id` varchar(120) DEFAULT NULL COMMENT '集团id',
    `biz_card_no` varchar(64) NOT NULL DEFAULT '' COMMENT '支付宝业务卡号',
    `external_card_no` varchar(64) NOT NULL DEFAULT '' COMMENT '外部商户会员卡卡号',
    `open_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开卡时间',
    `valid_date` varchar(128) NOT NULL DEFAULT '' COMMENT '有效期',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '支付宝用户id',
    `mall_type` int NOT NULL DEFAULT '0' COMMENT '会员所属商圈，0集团，1上海静安，2北京',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_qr_code_record`;
CREATE TABLE `tb_qr_code_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `qr_code` varchar(128) NOT NULL DEFAULT '' COMMENT '扫码积分二维码编号',
    `content` varchar(2048) NOT NULL DEFAULT '' COMMENT '销售记录json字符串',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
);
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (1, 'F878HHHHGqJkKjsHklkkslkisskohhklt', '{"rescode":"OPEN_SUCCESS","resmsg":"查询成功","uuid":"B59A4A2EE798950939003B61AEAFDF27","amount":33.0,"billserialnumber":"2022102711233813718569310019 2022-11-11 11:23:46","merchantCode":"HKC00021012","merchantName":"HKC00021012","sign":"F7C907EE2455F372182089F066F63C2A","saleTime":"20221111112344"}', '2024-10-28 16:13:08');

DROP TABLE IF EXISTS `tb_subscription_record`;
CREATE TABLE `tb_subscription_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `user_id` varchar(128) NOT NULL DEFAULT '' COMMENT '业务人员id',
    `status` int NOT NULL DEFAULT '0' COMMENT '订阅状态(0: 取消订阅 1:订阅成功)',
    `subscription_type` int NOT NULL DEFAULT '0' COMMENT '订阅类型(0:积分拦截 1:其它)',
    `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_member_register_source`;
CREATE TABLE `tb_member_register_source` (
    `id` bigint(20) NOT NULL COMMENT '主键id',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员id',
    `kip_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '会员id',
    `utm_lbs` varchar(64) NOT NULL DEFAULT '' COMMENT '商场id',
    `channel` varchar(64) NOT NULL DEFAULT '' COMMENT '当前渠道',
    `utm_channel` varchar(64) NOT NULL DEFAULT '' COMMENT '来源渠道',
    `utm_method` varchar(64) NOT NULL DEFAULT '' COMMENT '方式',
    `utm_source` varchar(64) NOT NULL DEFAULT '' COMMENT '来源',
    `utm_function` varchar(64) NOT NULL DEFAULT '' COMMENT '功能',
    `utm_user` varchar(64) NOT NULL DEFAULT '' COMMENT '分享人',
    `page_path` varchar(255) NOT NULL DEFAULT '' COMMENT '页面连接',
    `original_params` text COMMENT '原始参数',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    PRIMARY KEY (`id`)
);

INSERT INTO `tb_member_register_source` (`id`, `vipcode`, `kip_user_id`, `utm_lbs`, `channel`, `utm_channel`, `utm_method`, `utm_source`, `utm_function`, `utm_user`, `page_path`, `original_params`, `create_user`, `create_date`, `last_update_date`) VALUES (1860943673423990786, 'KERRY100216375', '8aaa8a2d92318dc601926aff6aa70000', '8aaa80b47c784020017c78b205ba0023', 'wxd830fe4d1e04988e', 'wxd830fe4d1e04988e', 'dr', '', '', '', '', '{\"channel\":\"wxd830fe4d1e04988e\",\"utm_channel\":\"wxd830fe4d1e04988e\",\"utm_method\":\"dr\"}', 'KERRY100216375', '2024-11-25 15:08:41', '2024-11-25 15:08:41');
INSERT INTO `tb_member_register_source` (`id`, `vipcode`, `kip_user_id`, `utm_lbs`, `channel`, `utm_channel`, `utm_method`, `utm_source`, `utm_function`, `utm_user`, `page_path`, `original_params`, `create_user`, `create_date`, `last_update_date`) VALUES (1869634636396523522, 'KERRY100283970', '2c9d85268a91239e018a91617f190000', '8aaa80b47c784020017c78b00d060022', 'wxd830fe4d1e04988e', 'wxd830fe4d1e04988e', 'dr', '', '', '', '', '{\"channel\":\"wxd830fe4d1e04988e\",\"utm_channel\":\"wxd830fe4d1e04988e\",\"utm_method\":\"dr\"}', 'KERRY100283970', '2024-12-19 14:43:28', '2024-12-19 14:43:28');
INSERT INTO `tb_member_register_source` (`id`, `vipcode`, `kip_user_id`, `utm_lbs`, `channel`, `utm_channel`, `utm_method`, `utm_source`, `utm_function`, `utm_user`, `page_path`, `original_params`, `create_user`, `create_date`, `last_update_date`) VALUES (1869668469472915458, 'KERRY100283972', '2c9d850286b45db00186b5ef076f0000', '8aaa83458c6b14cf018c6bb1aacf0000', 'wxd830fe4d1e04988e', 'wxd830fe4d1e04988e', 'dr', '', '', '', '', '{\"channel\":\"wxd830fe4d1e04988e\",\"utm_channel\":\"wxd830fe4d1e04988e\",\"utm_method\":\"dr\"}', 'KERRY100283972', '2024-12-19 16:57:54', '2024-12-19 16:57:54');
INSERT INTO `tb_member_register_source` (`id`, `vipcode`, `kip_user_id`, `utm_lbs`, `channel`, `utm_channel`, `utm_method`, `utm_source`, `utm_function`, `utm_user`, `page_path`, `original_params`, `create_user`, `create_date`, `last_update_date`) VALUES (1869940827027931137, 'KERRY100283974', '2c9d85268a91239e018a91617f190000', '8aaa80b47c784020017c78b00d060022', 'wxd830fe4d1e04988e', 'wxd830fe4d1e04988e', 'dr', '', '', '', '', '{\"channel\":\"wxd830fe4d1e04988e\",\"utm_channel\":\"wxd830fe4d1e04988e\",\"utm_method\":\"dr\"}', 'KERRY100283974', '2024-12-20 11:00:09', '2024-12-20 11:00:09');
INSERT INTO `tb_member_register_source` (`id`, `vipcode`, `kip_user_id`, `utm_lbs`, `channel`, `utm_channel`, `utm_method`, `utm_source`, `utm_function`, `utm_user`, `page_path`, `original_params`, `create_user`, `create_date`, `last_update_date`) VALUES (1869979204569960450, 'KERRY100283975', '2c9d85268a91239e018a91617f190000', '8aaa80b47c784020017c78b00d060022', 'wxd830fe4d1e04988e', 'wxd830fe4d1e04988e', 'dr', '', '', '', '', '{\"channel\":\"wxd830fe4d1e04988e\",\"utm_channel\":\"wxd830fe4d1e04988e\",\"utm_method\":\"dr\"}', 'KERRY100283975', '2024-12-20 13:32:39', '2024-12-20 13:32:39');
INSERT INTO `tb_member_register_source` (`id`, `vipcode`, `kip_user_id`, `utm_lbs`, `channel`, `utm_channel`, `utm_method`, `utm_source`, `utm_function`, `utm_user`, `page_path`, `original_params`, `create_user`, `create_date`, `last_update_date`) VALUES (1871856092350824450, 'KERRY100283977', '8aaa8a2d92318dc601926aff6aa70000', '8aaa82ea804d07cd01805171768b0009', 'wxd830fe4d1e04988e', 'wxd830fe4d1e04988e', 'dr', '', '', '', '', '{\"channel\":\"wxd830fe4d1e04988e\",\"utm_channel\":\"wxd830fe4d1e04988e\",\"utm_method\":\"dr\"}', 'KERRY100283977', '2024-12-25 17:50:44', '2024-12-25 17:50:44');

DROP TABLE IF EXISTS `tb_tag_member_no_logic`;
CREATE TABLE `tb_tag_member_no_logic` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团编号',
    `mall_id` varchar(32) NOT NULL DEFAULT '' COMMENT '商场号',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员号',
    `tag_ids` varchar(1024) NOT NULL DEFAULT '' COMMENT '会员无逻辑标签组',
    `lables` varchar(1024) NOT NULL DEFAULT '' COMMENT '会员逻辑标签组',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_member_open_mall_id`;
CREATE TABLE `tb_member_open_mall_id` (
    `id` bigint(20) NOT NULL COMMENT '主键',
    `group_id` varchar(128) NOT NULL COMMENT '集团号',
    `mobile` varchar(128) NOT NULL COMMENT '手机号',
    `open_mall_id` varchar(128) NOT NULL COMMENT '会员开卡商场',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_member_open_card_record`;
CREATE TABLE `tb_member_open_card_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `open_id` varchar(128) NOT NULL COMMENT 'open id',
    `kip_user_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'kip系统中用户唯一id',
    `card_id` varchar(128) NOT NULL DEFAULT '' COMMENT '会员卡号ID',
    `card_no` varchar(64) NOT NULL DEFAULT '' COMMENT '会员卡号',
    `event_type` varchar(64) NOT NULL DEFAULT '' COMMENT '事件类型',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_auto_sale_member_sort`;
CREATE TABLE `tb_auto_sale_member_sort` (
    `id` bigint(20) NOT NULL COMMENT '主键',
    `group_id` varchar(255) NOT NULL DEFAULT '' COMMENT '集团id',
    `sort_name` varchar(255) NOT NULL DEFAULT '' COMMENT '分类名称',
    `pid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'pid',
    `create_user` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_auto_sale_member_tag`;
CREATE TABLE `tb_auto_sale_member_tag` (
    `id` bigint(20) NOT NULL COMMENT '主键id',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团Id',
    `tag_name` varchar(255) NOT NULL DEFAULT '' COMMENT '标签名称',
    `describes` varchar(2048) NOT NULL DEFAULT '' COMMENT '标签描述',
    `status` int NOT NULL DEFAULT '0' COMMENT '1:统计中  2：统计成功 3：统计失败',
    `firsort_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '一级分类id',
    `secsort_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '二级分类id',
    `target_number` bigint(20) NOT NULL DEFAULT '0' COMMENT '目标人数',
    `condition_param` varchar(2048) NOT NULL DEFAULT '' COMMENT '规则条件',
    `query_conditions` varchar(2048) NOT NULL DEFAULT '' COMMENT '查询sql字符串',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `type` int NOT NULL DEFAULT '0' COMMENT '标签类型（1：逻辑标签；2：无逻辑标签）',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(30) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(30) NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_activity_promotion_points`;
CREATE TABLE `tb_activity_promotion_points` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `promotion_id` varchar(64) NOT NULL DEFAULT '' COMMENT '营销规则id',
    `points_id` varchar(64) NOT NULL DEFAULT '' COMMENT '积分调整记录id',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员编号',
    `integral` int NOT NULL DEFAULT '0' COMMENT '营销积分',
    `enable_integral` int(11) NOT NULL DEFAULT '0' COMMENT '可用营销积分',
    `mobile` varchar(100) NOT NULL DEFAULT '' COMMENT '手机号',
    `prime_integral` int NOT NULL DEFAULT '0' COMMENT '初始积分',
    `promotion_name` varchar(255) NOT NULL DEFAULT '' COMMENT '营销活动名称',
    `sale_money` decimal(10,2) DEFAULT '0.00' COMMENT '消费金额',
    `mall_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商场号',
    `sell_no` varchar(64) NOT NULL DEFAULT '' COMMENT '销售单号',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `expire_time` datetime NOT NULL DEFAULT '9999-12-31 23:59:59' COMMENT '营销积分过期时间',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

INSERT INTO `tb_activity_promotion` (`id`, `group_id`, `mallid`, `name`, `module`, `beginTime`, `endTime`, `promotionBonusExpirationDate`, `shopId`, `type`, `times`, `bonus`, `crowd_id`, `create_date`, `update_date`, `update_user`, `create_user`) VALUES ('bae0c88add9546a39d7fb479ff3df0d9', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '测试', '1', '2023-01-01 00:00:00', '2035-12-31 00:00:00', '2999-12-12 00:00:00', 'allstore', '0', 3.00, NULL, '', '2024-12-30 12:37:31', '2024-12-30 12:37:31', 'bert zhang', 'bert zhang');

INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (108, '4028e3817c2b3f79017c2b48c54c0000', '', '11', 1, 0.00, 'all,02,3,4,1,2,0100,0011,12,6666', '', 0, 1, 0, 0, '', '2024-11-27 11:38:04', 'admin', '2025-01-10 16:37:24', 'admin', '2024-11-27 11:38:04');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (109, '4028e3817c2b3f79017c2b48c54c0000', '', '22', 1, 1000.00, 'all,02,3,4,1,2,0100,0011,12,6666', '', 0, 1, 0, 0, '', '2025-01-09 15:16:16', 'xiaoqiang.zhang', '2025-01-10 16:37:24', 'nancy.xie', '2025-01-09 15:16:16');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (295, '4028e3817c2b3f79017c2b48c54c0000', '', '33', 1, 10.00, '4', '汽车消费', 0, 1, 0, 0, '{"money":10.00,"formats":"02","effectiveDate":"2025-01-01 00:00:00"}', '2025-01-21 11:52:48', 'xiaoqiang.zhang', '2025-01-21 11:52:48', 'bert zhang', '2025-01-16 00:00:00');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (133, '4028e3817c2b3f79017c2b48c54c0000', '', '33', 2, 100000.00, '3,4,2', '', 2, 1, 0, 0, '', '2025-01-21 11:52:48', 'xiaoqiang.zhang', '2025-01-21 11:52:48', 'bert zhang', '2025-01-21 11:52:48');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (136, '4028e3817c2b3f79017c2b48c54c0000', '', '22', 2, 5000.00, 'all,02,3,4,1,2,0100,0011,12,6666', '', 2, 1, 0, 0, '', '2025-01-09 15:16:16', 'xiaoqiang.zhang', '2025-01-10 16:37:24', 'nancy.xie', '2025-01-09 15:16:16');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (107, '4028e3817c2b3f79017c2b48c54c0000', '', '11', 5, 0.00, 'all,02,3,4,1,2,0100,0011,12,6666', '', 0, 0, 0, 0, '', '2024-11-27 11:38:04', 'admin', '2025-01-10 16:37:24', 'admin', '2024-11-27 11:38:04');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (241, '4028e3817c2b3f79017c2b48c54c0000', '', '22', 5, 3000.00, 'all,02,3,4,1,2,0100,0011,12,6666', '', 0, 0, 0, 0, '', '2025-01-09 15:16:16', 'xiaoqiang.zhang', '2025-01-10 16:37:24', 'nancy.xie', '2025-01-09 15:16:16');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (245, '4028e3817c2b3f79017c2b48c54c0000', '', '33', 5, 30002.00, '02,1,2,0100,0011,12,6666,4', '', 0, 0, 0, 0, '', '2025-01-21 11:52:48', 'xiaoqiang.zhang', '2025-01-21 11:52:48', 'bert zhang', '2025-01-21 11:52:48');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (277, '4028e3817c2b3f79017c2b48c54c0000', '', '33', 5, 2.00, '02,3', '', 0, 0, 0, 0, '', '2025-01-21 11:52:48', 'xiaoqiang.zhang', '2025-01-21 11:52:48', 'bert zhang', '2025-01-21 11:52:48');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (135, '4028e3817c2b3f79017c2b48c54c0000', '', '22', 6, 10000.00, 'all,02,3,4,1,2,0100,0011,12,6666', '', 2, 0, 0, 0, '', '2025-01-09 15:16:16', 'xiaoqiang.zhang', '2025-01-10 16:37:24', 'nancy.xie', '2025-01-09 15:16:16');
INSERT INTO `tb_member_grade_rule` (`id`, `group_id`, `mall_id`, `grade_code`, `rule_type`, `money`, `formats`, `formats_desc`, `sum_type`, `type`, `is_count`, `pid`, `certification`, `create_date`, `create_user`, `update_date`, `update_user`, `effective_date`) VALUES (232, '4028e3817c2b3f79017c2b48c54c0000', '', '33', 8, 1000000.00, '3,4,1', '', 0, 1, 0, 0, '', '2025-01-21 11:52:48', 'xiaoqiang.zhang', '2025-01-21 11:52:48', 'bert zhang', '2025-01-21 11:52:48');

INSERT INTO `tb_member_grade` (`id`, `group_id`, `code`, `name`, `name_en`, `sort`, `desc_remark`, `desc_remark_en`, `card_cover_url`, `card_cover_url_en`, `card_cover_home_url`, `card_cover_home_url_en`, `grade_desc`, `grade_desc_en`, `up_gradation_status`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES ('b2063cc4447e4ba9b7ff14459378666a', '4028e3817c2b3f79017c2b48c54c0000', '11', '银卡', '', 1, '', '', '', '', '', '', '<p><br></p>', '', 1, '2024-04-18 15:36:41', 'admin', '2024-11-27 11:38:05', 'nancy.xie');
INSERT INTO `tb_member_grade` (`id`, `group_id`, `code`, `name`, `name_en`, `sort`, `desc_remark`, `desc_remark_en`, `card_cover_url`, `card_cover_url_en`, `card_cover_home_url`, `card_cover_home_url_en`, `grade_desc`, `grade_desc_en`, `up_gradation_status`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES ('04efde8fb670420dab58b2d0ee3c798f', '4028e3817c2b3f79017c2b48c54c0000', '22', '金卡', '', 2, '', '', '', '', '', '', '', '', 1, '2024-04-22 09:12:20', 'xiaoqiang.zhang', '2025-01-09 15:16:16', 'nancy.xie');
INSERT INTO `tb_member_grade` (`id`, `group_id`, `code`, `name`, `name_en`, `sort`, `desc_remark`, `desc_remark_en`, `card_cover_url`, `card_cover_url_en`, `card_cover_home_url`, `card_cover_home_url_en`, `grade_desc`, `grade_desc_en`, `up_gradation_status`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES ('22381b9fb87a4094b2b9a2cb9e0f1859', '4028e3817c2b3f79017c2b48c54c0000', '33', '铂金卡', '', 33, '', '', '', '', '', '', '', '', 1, '2024-05-08 15:47:47', 'xiaoqiang.zhang', '2025-01-21 11:52:48', 'bert zhang');

INSERT INTO `tb_member_asset` (`id`, `crm_id`, `group_id`, `mall_id`, `mobile`, `current_points`, `vipcode`, `grade`, `kip_user_id`, `status`, `wx_open_market`, `is_completed`, `remark`, `join_time`, `create_date`, `create_user`, `update_date`, `update_user`, `whether_blacklist`, `register_source`, `register_source_label`, `register_source_remark`) VALUES (77365, '18a7043fb424496ca459bb098cc49949', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', '13122957626', 117, 'KERRY100284140', '22', '2c9d850c870bea4101870cfbc08f0000', 1, '8aaa80b47c784020017c78b00d060022', '0', '', '2025-01-16 16:59:01', '2025-01-16 16:59:01', 'sys', '2025-01-16 18:00:54', 'sys', 1, 'miniProgram', '小程序注册', '');
INSERT INTO `tb_sales_detail` (`id`, `crm_id`, `group_id`, `mall_id`, `vipcode`, `kip_user_id`, `points_id`, `points_num`, `parent_order_no`, `order_no`, `total_amount`, `discount_amount`, `points_offset_amount`, `pay_amount`, `sale_date`, `shop_no`, `shop_name`, `status`, `sale_type`, `refund_points_id`, `refund_amount`, `refund_points_num`, `extend1`, `extend2`, `extend3`, `remark`, `image_url`, `org_points`, `org_grade`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (16602, '29880f64d10045e9995e019549ae87a1', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', 'KERRY100284140', '2c9d850c870bea4101870cfbc08f0000', 'd2833ea298824d74b90fcc72b66bf049', 45, '', '09876678987', 5.00, 0.00, 0.00, 5.00, '2025-01-02 00:00:00', 'HKC00041004', '十二饭店1', 0, '1', '', 0.00, 0, '1', '0', '0', '销售积分, 满足店铺积分比例得:15分，营销积分活动获得积分:30，总积分:45分', 'https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/9b60f468916f4bd7b1673752730c33a7_46b51053b71c4c409ffdaa2cf65052a3.jpg', 0, '11', '2025-01-16 17:00:57', 'bert zhang', '2025-01-16 17:00:57', 'bert zhang');
INSERT INTO `tb_sales_detail` (`id`, `crm_id`, `group_id`, `mall_id`, `vipcode`, `kip_user_id`, `points_id`, `points_num`, `parent_order_no`, `order_no`, `total_amount`, `discount_amount`, `points_offset_amount`, `pay_amount`, `sale_date`, `shop_no`, `shop_name`, `status`, `sale_type`, `refund_points_id`, `refund_amount`, `refund_points_num`, `extend1`, `extend2`, `extend3`, `remark`, `image_url`, `org_points`, `org_grade`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (16603, '9df1f8948540456eb66ecac913374c6e', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', 'KERRY100284140', '2c9d850c870bea4101870cfbc08f0000', 'f2a00d12ab124a8db5135164f51808fe', 54, '', '0987897', 6.00, 0.00, 0.00, 6.00, '2025-01-02 00:00:00', 'HKC00041004', '十二饭店1', 0, '1', '', 0.00, 0, '1', '0', '0', '销售积分, 满足店铺积分比例得:18分，营销积分活动获得积分:36，总积分:54分', 'https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/5208de97a6d242c89b3cae77ad0e91ba_46b51053b71c4c409ffdaa2cf65052a3.jpg', 45, '11', '2025-01-16 17:01:51', 'bert zhang', '2025-01-16 17:01:51', 'bert zhang');
INSERT INTO `tb_sales_detail` (`id`, `crm_id`, `group_id`, `mall_id`, `vipcode`, `kip_user_id`, `points_id`, `points_num`, `parent_order_no`, `order_no`, `total_amount`, `discount_amount`, `points_offset_amount`, `pay_amount`, `sale_date`, `shop_no`, `shop_name`, `status`, `sale_type`, `refund_points_id`, `refund_amount`, `refund_points_num`, `extend1`, `extend2`, `extend3`, `remark`, `image_url`, `org_points`, `org_grade`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (16604, '65565235df3b44c68b350d7378342d7a', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', 'KERRY100284140', '2c9d850c870bea4101870cfbc08f0000', 'fd3fe60d922245b58380829d9f332f6d', 9, '', '6789789', 1.00, 0.00, 0.00, 1.00, '2025-01-02 11:27:45', 'HKC00041004', '十二饭店1', 0, '1', '', 0.00, 0, '1', '0', '0', '销售积分, 满足店铺积分比例得:3分，营销积分活动获得积分:6，总积分:9分', 'https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/14ac511a465b4b1e91c7d1e235b3c55c_46b51053b71c4c409ffdaa2cf65052a3.jpg', 99, '11', '2025-01-16 17:35:11', 'bert zhang', '2025-01-16 17:35:11', 'bert zhang');
INSERT INTO `tb_sales_detail` (`id`, `crm_id`, `group_id`, `mall_id`, `vipcode`, `kip_user_id`, `points_id`, `points_num`, `parent_order_no`, `order_no`, `total_amount`, `discount_amount`, `points_offset_amount`, `pay_amount`, `sale_date`, `shop_no`, `shop_name`, `status`, `sale_type`, `refund_points_id`, `refund_amount`, `refund_points_num`, `extend1`, `extend2`, `extend3`, `remark`, `image_url`, `org_points`, `org_grade`, `create_date`, `create_user`, `update_date`, `update_user`) VALUES (16605, '8972815dbfeb4491a15c230feb4f1282', '4028e3817c2b3f79017c2b48c54c0000', '8aaa80b47c784020017c78b00d060022', 'KERRY100284140', '2c9d850c870bea4101870cfbc08f0000', 'b9ff87e57b324fe5beb169955a731c02', 9, '', '45678965', 1.00, 0.00, 0.00, 1.00, '2025-01-02 13:16:23', 'HKC00041004', '十二饭店1', 0, '1', '', 0.00, 0, '1', '0', '0', '销售积分, 满足店铺积分比例得:3分，营销积分活动获得积分:6，总积分:9分', 'https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/1406e000871c409e9392288f27482f9b_46b51053b71c4c409ffdaa2cf65052a3.jpg', 108, '11', '2025-01-16 18:00:54', 'bert zhang', '2025-01-16 18:00:54', 'bert zhang');
