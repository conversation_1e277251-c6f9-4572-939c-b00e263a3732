DROP TABLE IF EXISTS `tb_integral_repeat_rule`;
CREATE TABLE `tb_integral_repeat_rule` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `mall_id` varchar(255) NOT NULL COMMENT '商场编号',
    `group_id` varchar(255) DEFAULT NULL COMMENT '集团ID',
    `dept_id` varchar(120) DEFAULT NULL COMMENT '部门ID',
    `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态(1:有效，0或其他：无效)',
    `sales_time_difference` int(11) NOT NULL COMMENT '销售时间差，单位分钟',
    `repeat_rule` varchar(120) NOT NULL DEFAULT '0' COMMENT '去重条件，多个值以英文逗号分隔，1:店铺，2:金额，3:销售时间，4:会员号',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` bigint(20) NOT NULL COMMENT '创建人id',
    `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` bigint(20) DEFAULT NULL COMMENT '更新人id',
    `update_user` varchar(64) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `tb_card_member_relation`;
CREATE TABLE `tb_card_member_relation` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
     `mobile` varchar(64) DEFAULT NULL COMMENT '手机号',
     `name` varchar(64) DEFAULT NULL COMMENT '姓名',
     `gender` varchar(64) DEFAULT NULL COMMENT '性别',
     `mall_id` varchar(120) DEFAULT NULL COMMENT '商场编号',
     `group_id` varchar(120) DEFAULT NULL COMMENT '集团id',
     `biz_card_no` varchar(64) DEFAULT NULL COMMENT '支付宝业务卡号',
     `external_card_no` varchar(64) DEFAULT NULL COMMENT '外部商户会员卡卡号',
     `open_date` date DEFAULT NULL COMMENT '开卡时间',
     `valid_date` varchar(64) DEFAULT NULL COMMENT '有效期',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `update_date` datetime DEFAULT NULL COMMENT '更新时间',
     `user_id` varchar(64) DEFAULT NULL COMMENT '支付宝用户id',
     `mall_type` int(2) DEFAULT '0' COMMENT '会员所属商圈，0集团，1上海静安，2北京',
     PRIMARY KEY (`id`)
);
INSERT INTO tb_card_member_relation (id,mobile,name,gender,mall_id,group_id,biz_card_no,external_card_no,open_date,valid_date,create_time,update_date,user_id,mall_type) VALUES
(2,'15210195507','李江','F','8a84853b7c91ac5b017c961a9b2a030d','8a84853b7c91ac5b017c962dab55030e','0000020668','KERRY100200104','2021-10-22','3220-02-20 21:20:46','2021-10-22 08:24:33','2021-10-22 08:25:01','2088902504263681',0),
(5,'18621994812','吴建兵','F','8a84853b7c91ac5b017c961a9b2a030d','8a84853b7c91ac5b017c962dab55030e','0000011216','KERRY100200106','2021-10-22','3220-02-20 21:20:46','2021-10-22 08:44:29','2021-10-22 14:00:01','2088002009074163',0),
(6,'17858698100','蔡褚','F','8a84853b7c91ac5b017c961a9b2a030d','8a84853b7c91ac5b017c962dab55030e','0000020856','KERRY100200107','2021-10-22','3220-02-20 21:20:46','2021-10-22 08:44:45',NULL,'2088122041803564',0);

CREATE TABLE `tb_qr_code_record` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `qr_code` varchar(128) NOT NULL COMMENT '扫码积分二维码编号',
    `content` varchar(2048) DEFAULT NULL COMMENT '销售记录json字符串',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_qr_code` (`qr_code`)
);

INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (1, 'F878HHHHGqJkKjsHklkkslkisskohhklt', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"B59A4A2EE798950939003B61AEAFDF27\",\"amount\":33.0,\"billserialnumber\":\"2022102711233813718569310019 2024-04-08 11:23:46\",\"merchantCode\":\"HKC00021012\",\"merchantName\":\"HKC00021012\",\"sign\":\"F7C907EE2455F372182089F066F63C2A\",\"saleTime\":\"20240408112344\"}', '2022-11-11 15:16:40');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (2, 'Vv8dHHHHKKogssKKklkksssssqhiljhkj', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"B34B934814619ED3C66BA36E67B874AB\",\"amount\":62.0,\"billserialnumber\":\"0601\",\"merchantCode\":\"HKC00021014\",\"merchantName\":\"HKC00021014\",\"sign\":\"4CD4B9BDD891D4E633505A461E94BCE2\",\"saleTime\":\"20221111154703\"}', '2022-11-11 16:32:41');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (3, 'Tc8mHHHHKKogssKKklkkssshskqlogskl', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"03306AF2C7A6F51C8B5FB7A8B772F0DD\",\"amount\":15.0,\"billserialnumber\":\"0295\",\"merchantCode\":\"HKC00021014\",\"merchantName\":\"HKC00021014\",\"sign\":\"C2692B4283CA2A73783AB2547B30871B\",\"saleTime\":\"20221114125035\"}', '2022-11-14 13:34:35');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (4, 'ZvdiHHHHtooHogihklkkssshskhtqoqii', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"D8F3D9C537E0AE89ECF1DC8F4EADE178\",\"amount\":89.2,\"billserialnumber\":\"0294\",\"merchantCode\":\"HKC00021012\",\"merchantName\":\"HKC00021012\",\"sign\":\"FEA445953B59BB08267112120CD9876E\",\"saleTime\":\"20221114124853\"}', '2022-11-14 13:38:38');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (5, 'VeujHHHHtooHogihklkkssshskhiqhsti', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"D5862EDBA6403210B1693DD762E7F545\",\"amount\":49.5,\"billserialnumber\":\"0293\",\"merchantCode\":\"HKC00021014\",\"merchantName\":\"HKC00021014\",\"sign\":\"DE5CBFBA1905A31C474C404E3D1376D2\",\"saleTime\":\"20221114124747\"}', '2022-11-14 13:38:46');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (6, 'Wq9etllGtljlhKjkklkkssshsqktkqjsj', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"D26B911FD7F0551600B121C44251677B\",\"amount\":35.0,\"billserialnumber\":\"美团外卖 #19\",\"merchantCode\":\"N4-0600002\",\"merchantName\":\"JAKC00011016\",\"sign\":\"1F6FC2CA1383D4F5A04BB1E458B1565C\",\"saleTime\":\"20221115102812\"}', '2022-11-15 11:37:17');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (7, 'V000tllGtljlksjJklkkssksshhjliksg', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"57B034BC808CCE26B185978D5F773D2D\",\"amount\":146.0,\"billserialnumber\":\"8542317\",\"merchantCode\":\"JAKC00011226\",\"merchantName\":\"多财食堂\",\"sign\":\"7A9A814196942AD76C4589F55B4E52EF\",\"saleTime\":\"20221121144607\"}', '2022-11-22 14:13:46');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (8, 'Q386HHHHstkIHGkIklkkslsgllkgoljjh', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"1C11B754941BAEEB2815D544E9CA11E3\",\"amount\":2279.0,\"billserialnumber\":\"10022022101200021\",\"merchantCode\":\"L220000001\",\"merchantName\":\"PKC00011051\",\"sign\":\"B7E901FC4CF1DFC3CE943D85A2710B96\",\"saleTime\":\"20221019100628\"}', '2022-11-22 14:14:19');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (9, 'Rv67HHHHHGGkjHhkklkkskltshotlkqjk', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"74690BF77503DFBF413F84EAA0E34C30\",\"amount\":8029.0,\"billserialnumber\":\"08HNO268719\",\"merchantCode\":\"HKC00021012\",\"merchantName\":\"25460301,stone island\",\"sign\":\"87D63DE1D70D833E9C66095739059D17\",\"saleTime\":\"20221208143802\"}', '2022-12-08 14:49:08');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (10, 'T9g8tllGtljljGohklkkskltshoiqqjlj', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"8105D1666A6859C759BDE82DB5832EC8\",\"amount\":128.0,\"billserialnumber\":\"V220CN0311M067199\",\"merchantCode\":\"HKC00021015\",\"merchantName\":\"Venchi\",\"sign\":\"266611FE0FB980D9BE2D1B3D4C0EF210\",\"saleTime\":\"20221208143754\"}', '2022-12-08 14:51:39');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (11, 'VbdutllGtljlhKjkklkkskltshotlsiok', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"4040C1F292261530409BD40D024AE21C\",\"amount\":16.0,\"billserialnumber\":\"27274722212080024\",\"merchantCode\":\"N4-0600002\",\"merchantName\":\"JAKC00011016\",\"sign\":\"F931213B4FFC8EAB2BE7D4A6620B17BC\",\"saleTime\":\"20221208143804\"}', '2022-12-08 14:55:44');
INSERT INTO `tb_qr_code_record` (`id`, `qr_code`, `content`, `create_date`) VALUES (12, 'NohatllGtljlhHogklkkskltshoiqsqjk', '{\"rescode\":\"OPEN_SUCCESS\",\"resmsg\":\"查询成功\",\"uuid\":\"D16CFC41BFCD3DB5481A93598F554B78\",\"amount\":186.0,\"billserialnumber\":\"26469312212080019\",\"merchantCode\":\"N1-10B0001\",\"merchantName\":\"JAKC00011017\",\"sign\":\"2694DD0C5E0A0D9EC24BFF393EE224B1\",\"saleTime\":\"20221208143750\"}', '2022-12-08 14:57:36');

CREATE TABLE `tb_tag_member_nologic` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `vipcode` varchar(64) DEFAULT NULL COMMENT '会员号',
    `tagids` text COMMENT '会员无逻辑标签组',
    `lables` text COMMENT '会员逻辑标签组',
    `group_id` varchar(64) DEFAULT NULL COMMENT '集团编号',
    `mallid` varchar(32) DEFAULT NULL COMMENT '商场号',
    `createDate` datetime DEFAULT NULL COMMENT '创建时间',
    `updateDate` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_tag_member_nologic (id, vipcode,tagids,lables,group_id,mallid,createDate,updateDate) VALUES
('1111','KERRY100245640',NULL,'1564794770261401602#1566702522143416322','8a84853b7c91ac5b017c962dab55030e',NULL,'2022-08-31 09:58:42','2022-08-31 09:58:42'),
('1112','KERRY100245641',NULL,'1564794770261401602#1566702522143416322','8a84853b7c91ac5b017c962dab55030e',NULL,'2022-08-31 09:58:42','2022-08-31 09:58:42');

CREATE TABLE `tb_sequence_vipcode` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增唯一主键id',
    `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
    PRIMARY KEY (`id`)
);


CREATE TABLE `tb_activity_promotionintegral` (
    `id` varchar(64) NOT NULL,
    `promotionid` varchar(64) DEFAULT NULL COMMENT '营销规则id',
    `integraladjustid` varchar(64) DEFAULT NULL COMMENT '积分调整记录id',
    `vipno` varchar(64) DEFAULT NULL COMMENT '会员编号',
    `integral` double DEFAULT NULL COMMENT '营销积分',
    `enableIntegral` double DEFAULT NULL COMMENT '可用营销积分',
    `integralExpirationDate` datetime DEFAULT NULL COMMENT '营销积分过期时间',
    `createdate` datetime DEFAULT NULL,
    `updatedate` datetime DEFAULT NULL,
    `mobile` varchar(100) DEFAULT NULL COMMENT '手机号',
    `prime_integral` double DEFAULT NULL COMMENT '初始积分',
    `promotion_name` varchar(255) DEFAULT NULL COMMENT '营销活动名称',
    `sale_money` double DEFAULT NULL COMMENT '消费金额',
    `mallid` varchar(64) DEFAULT NULL COMMENT '商场号',
    `sell_no` varchar(64) DEFAULT NULL COMMENT '销售单号',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_activity_promotionintegral (id,promotionid,integraladjustid,vipno,integral,enableIntegral,integralExpirationDate,createdate,updatedate,mobile,prime_integral,promotion_name,sale_money,mallid,sell_no,remark) VALUES
('0000a4dcaa144f2bad61b7213071b5a8','7391fae447ea4742ae665b28355f1781','3146fbd09d894db38ecc48fe35f94e1c','KERRY100303050',522.0,522.0,'2999-12-12 00:00:00','2023-06-15 16:52:49','2023-06-15 16:52:49',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('002adbb3c1ef4b749543282ccbfe62b2','bc43e0fc7bed41e292a331cc7c45b2b9','1dbface08a2b405db82a627b72d4761b','KERRY100325303',60.0,0.0,'2999-12-12 00:00:00','2024-04-23 11:12:16','2024-04-23 11:15:05','17621414410',NULL,'固定积分-叠加2倍',12.0,'8a888abd8ceb6c70018d117f469c0010','SZ04231111','拍照积分');

CREATE TABLE `tb_autosale_membertag` (
    `id` bigint(20) NOT NULL COMMENT '主键id',
    `tagname` varchar(255) DEFAULT NULL COMMENT '标签名称',
    `describes` text COMMENT '标签描述',
    `status` int(1) DEFAULT NULL COMMENT '1:统计中  2：统计成功 3：统计失败',
    `firsort_id` bigint(20) DEFAULT NULL COMMENT '一级分类id',
    `secsort_id` bigint(20) DEFAULT NULL COMMENT '二级分类id',
    `targetnumber` bigint(20) DEFAULT NULL COMMENT '目标人数',
    `conditionparam` longtext COMMENT '规则条件',
    `queryconditions` mediumtext COMMENT '查询sql字符串',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `group_id` varchar(64) DEFAULT NULL COMMENT '集团Id',
    `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `update_date` datetime DEFAULT NULL COMMENT '更新时间',
    `create_user` varchar(30) DEFAULT NULL COMMENT '创建人',
    `update_user` varchar(30) DEFAULT NULL COMMENT '更新人',
    `creator` bigint(20) DEFAULT NULL COMMENT '创建者id',
    `updater` bigint(20) DEFAULT NULL COMMENT '更新人id',
    `type` int(2) DEFAULT NULL COMMENT '标签类型（1：逻辑标签；2：无逻辑标签）',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_autosale_membertag (id,tagname,describes,status,firsort_id,secsort_id,targetnumber,conditionparam,queryconditions,remark,group_id,dept_id,create_date,update_date,create_user,update_user,creator,updater,`type`) VALUES
(1556491138717724673,'来源1',NULL,2,1556490647139180545,1556490904788807682,0,'{"basic":"","member":" and ( ( source in (''miniProgram'',''serviceCounter'') ) ) and ( ( registerSourceRemark in (''TEST活动列表:'') ) )","integral":"","mall":"","ticket":"","onlineBrowse":"","onlineConsume":""}','{"sourceArray":[{"conditionRelation1":"and","includeRelation1":"in","citys":[],"showFlag":false,"provinces":["miniProgram","serviceCounter"]}],"registerSourceRemarkArray":[{"conditionRelation1":"and","includeRelation1":"in","citys":[],"showFlag":false,"provinces":"TEST活动列表:"}]}',NULL,'8a84853b7c91ac5b017c962dab55030e',NULL,'2022-08-08 12:03:00','2022-08-31 21:30:10',NULL,NULL,1067246875800000001,NULL,1),
(1556517723097362434,'测试来源名称','1111',2,1556490647139180545,1556490904788807682,0,'{"basic":"","member":" and ( ( source in (''serviceCounter'',''miniProgram'') ) ) and ( ( registerSourceRemark in ('':123456'') ) )","integral":" and ( ( currentIntegral > ''0'' and currentIntegral > ''0'') )","mall":"","ticket":"","onlineBrowse":"","onlineConsume":""}','{"currentIntegralArray":[{"conditionRelation1":"and","conditionCompare1":">","value1":"0","conditionRelation2":"and","conditionCompare2":">","value2":"0","showFlag":false}],"sourceArray":[{"conditionRelation1":"and","includeRelation1":"in","citys":[],"showFlag":false,"provinces":["serviceCounter","miniProgram"]}],"registerSourceRemarkArray":[{"conditionRelation1":"and","includeRelation1":"in","citys":[],"showFlag":false,"provinces":":123456"}]}',NULL,'8a84853b7c91ac5b017c962dab55030e',NULL,'2022-08-08 13:48:39','2022-09-16 15:48:38',NULL,NULL,1067246875800000001,NULL,1);

CREATE TABLE `tb_card_market` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `group_id` varchar(64) DEFAULT NULL COMMENT '集团id',
    `mall_id` varchar(120) DEFAULT NULL COMMENT '商场编号',
    `name` varchar(255) DEFAULT NULL COMMENT '商场名称',
    `zfb_name` varchar(255) DEFAULT NULL COMMENT '支付宝商圈名称',
    `latitude` varchar(32) DEFAULT NULL COMMENT '商场位置坐标纬度',
    `longitude` varchar(32) DEFAULT NULL COMMENT '商场位置坐标经度',
    `url` text NOT NULL COMMENT '支付宝会员卡领卡链接',
    `template_id` varchar(64) DEFAULT NULL COMMENT '会员卡模板id',
    `status` int(11) DEFAULT '0' COMMENT '状态,0默认，1已开通',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_date` datetime DEFAULT NULL COMMENT '更新时间',
    `mall_ali` varchar(255) DEFAULT NULL COMMENT '支付宝商场id',
    `is_to_mini_program_ali` int(2) NOT NULL DEFAULT '0' COMMENT '是否需要跳转到第三方支付宝小程序(0:不需要;1:需要)',
    `pages_ali` varchar(255) DEFAULT NULL COMMENT '支付宝小程序跳转的页面路径与参数',
    `appid_ali` varchar(64) DEFAULT NULL COMMENT '支付宝小程序的appid',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_card_market (id, group_id,mall_id,name,zfb_name,latitude,longitude,url,template_id,status,create_time,update_date,mall_ali,is_to_mini_program_ali,pages_ali,appid_ali) VALUES
(1, '8a84853b7c91ac5b017c962dab55030e','8a84853b7c91ac5b017c961a9b2a030d','杭州嘉里中心','杭州嘉里中心',NULL,NULL,'https://memberprod.alipay.com/account/openform/activecard.htm?app_id=****************&template_id=20211011000000003269782000300720&__webview_options__=canPullDown%3dNO%26transparentTitle%3dauto&out_string=8a84853b7c91ac5b017c961a9b2a030d&callback=https://qa-crm-kpl.kerryprops.com.cn/xcrm-api/zhsq/alipay/alipayMarketCardTemplateCallBack','20211011000000003269782000300720',1,'2021-10-18 14:50:54',NULL,'********',0,NULL,NULL);

CREATE TABLE `tb_email_template_config` (
    `id` bigint(20) NOT NULL COMMENT '邮件模板id',
    `name` varchar(32) DEFAULT NULL COMMENT '邮件模板名称',
    `mallid` varchar(120) DEFAULT NULL COMMENT '商场编号',
    `group_id` varchar(120) DEFAULT NULL COMMENT '集团id',
    `email_send_node` int(11) DEFAULT NULL COMMENT '邮件发送节点(0:注册,1:登陆,2:活动取消(活动取消审核通过),3:活动报名成功,4:活动取消,5:活动即将开始,6:活动取消(会员S端被取消报名),7:会员等级变更,8:积分变更,9:积分即将过期,10:电子券兑换成功,11:电子券核销成功,12:电子券即将过期)',
    `memberGrade` varchar(100) DEFAULT NULL COMMENT '会员卡等级(1:银卡,2金卡,3铂金卡)',
    `start_date` datetime DEFAULT NULL COMMENT '生效时间',
    `end_date` datetime DEFAULT NULL COMMENT '失效时间',
    `email_subject` varchar(64) DEFAULT NULL COMMENT '邮件主题',
    `email_content` varchar(255) DEFAULT NULL COMMENT '邮件内容',
    `status` int(11) DEFAULT '0' COMMENT '生效状态(0:生效,1:不生效）',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `update_date` datetime DEFAULT NULL COMMENT '更新时间',
    `create_user` varchar(30) DEFAULT NULL COMMENT '创建人',
    `update_user` varchar(30) DEFAULT NULL COMMENT '更新人',
    `creator` bigint(20) DEFAULT NULL COMMENT '创建者id',
    `updater` bigint(20) DEFAULT NULL COMMENT '更新人id',
    `start_date_status` int(11) DEFAULT '0' COMMENT '生效时间是否限制,0:(默认)不限制,1限制',
    PRIMARY KEY (`id`)
);

CREATE TABLE `tb_insensate_points_auth_record` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `open_id` varchar(128) NOT NULL COMMENT '微信open id',
    `kip_user_id` varchar(128) NOT NULL COMMENT 'kip系统中用户唯一id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '会员卡号（支付宝）',
    `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '授权状态，0已授权，1未授权，默认0',
    `origin` tinyint(3) unsigned NOT NULL COMMENT '来源，0微信，1支付宝',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `mchid` varchar(128) NOT NULL DEFAULT '' COMMENT '商圈id',
    PRIMARY KEY (`id`)
);
INSERT INTO tb_insensate_points_auth_record (open_id,kip_user_id,group_id,mall_id,card_no,status,origin,create_date,update_date,mchid) VALUES
('oudWQ5ccsJLSlUGt0s_RQysoHqgg','8a8481d78e63c8c8018e7426f18e0000','8a8884e77cc9e70a017cca1011970001','8a8883557cca9463017ccb002b360001','',1,0,'2024-02-04 10:03:02','2024-02-04 10:03:02','1616241765'),
('2088902504263681','8a8486ad8cf7d555018d1b122a940024','8a8884e77cc9e70a017cca1011970001','8a8883557cca9463017ccb002b360001','',0,1,'2024-02-05 10:17:47','2024-02-05 10:17:47','********'),
('oudWQ5XOKnEMGatSUua6sSPyQc-Q','8a8486ad8cf7d555018d1b122a940024','8a8884e77cc9e70a017cca1011970001','8a8883557cca9463017ccb002b360001','',0,0,'2024-02-05 10:24:16','2024-02-05 10:24:16','1616241765');

CREATE TABLE `tb_member_card_open_record` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `open_id` varchar(128) NOT NULL COMMENT 'open id',
    `kip_user_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'kip系统中用户唯一id',
    `card_id` varchar(128) NOT NULL DEFAULT '' COMMENT '会员卡号ID',
    `card_no` varchar(64) NOT NULL DEFAULT '' COMMENT '会员卡号',
    `event_type` varchar(64) NOT NULL DEFAULT '' COMMENT '事件类型',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
);

CREATE TABLE `tb_member_source` (
    `id` bigint(20) NOT NULL,
    `vipcode` varchar(64) DEFAULT NULL COMMENT '会员id',
    `kip_user_id` varchar(64) DEFAULT NULL COMMENT '会员id',
    `utm_lbs` varchar(64) DEFAULT NULL COMMENT '商场id',
    `channel` varchar(64) DEFAULT NULL COMMENT '当前渠道',
    `utm_channel` varchar(64) DEFAULT NULL COMMENT '来源渠道',
    `utm_method` varchar(64) DEFAULT NULL COMMENT '方式',
    `utm_source` varchar(64) DEFAULT NULL COMMENT '来源',
    `utm_function` varchar(64) DEFAULT NULL COMMENT '功能',
    `utm_user` varchar(64) DEFAULT NULL COMMENT '分享人',
    `page_path` varchar(255) DEFAULT NULL COMMENT '页面连接',
    `original_params` text COMMENT '原始参数',
    `create_user` varchar(64) NOT NULL COMMENT '创建人',
    `create_date` datetime NOT NULL COMMENT '创建时间',
    `last_update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);
INSERT INTO tb_member_source (id,vipcode,kip_user_id,utm_lbs,channel,utm_channel,utm_method,utm_source,utm_function,utm_user,page_path,original_params,create_user,create_date,last_update_date) VALUES
(1503288615091978242,'KERRY100298988','8a8481ec7f86f8f2017f8759b6730000','8a84853b7c91ac5b017c961a9b2a030d','wxd830fe4d1e04988e','wxd830fe4d1e04988e','dr',NULL,NULL,NULL,NULL,NULL,'KERRY100298988','2022-03-14 16:35:10','2022-03-14 16:35:10'),
(1503312662639034369,'KERRY100298994','8a8481ec7f86f8f2017f87e89b600004','8a84853b7c91ac5b017c961a9b2a030d','wxd830fe4d1e04988e','wxd830fe4d1e04988e','sh','sh_wechat','co_86b702683ecd4be893a0676b6a94f378','8a8481917f718976017f7311db6f0002',NULL,NULL,'KERRY100298994','2022-03-14 18:10:44','2022-03-14 18:10:43');











