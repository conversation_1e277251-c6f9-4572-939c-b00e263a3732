package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbBrandGuideCollectionMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 08/02/2024 10:13
 **********************************************************************************************************************/

@Slf4j
public class TbBrandGuideCollectionMapperTest extends BaseTest {

    @Autowired
    private TbBrandGuideCollectionMapper tbBrandGuideCollectionMapper;

    @Test
    public void findByGroupIdAndMallIdAndVipcode() {
        DynamicDataSourceContextHolder.push("points");
        List<String> ids = tbBrandGuideCollectionMapper.findByGroupIdAndMallIdAndVipcode("4028e3817c2b3f79017c2b48c54c0000", "8aaa80b47c784020017c78b00d060022", "KERRY100213877");
        DynamicDataSourceContextHolder.clear();
        Assert.assertNotNull(ids);
    }

}
