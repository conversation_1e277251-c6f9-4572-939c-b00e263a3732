package com.points.mapper;

import com.kerryprops.kip.service.integral.entity.SubscriptionRecord;
import com.kerryprops.kip.service.integral.mapper.SelfDefiningPageMapper;
import com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class SelfDefiningPageMapperTest extends BaseTest {

    @Autowired
    private SelfDefiningPageMapper selfDefiningPageMapper;

    @Test
    public void findHomePage() {
        selfDefiningPageMapper.findHomePage("8a8884e77cc9e70a017cca14d07e0002", "8a88835c7cd96d31017cda2fd9910000", "1");
    }

    @Test
    public void findPageId() {
        selfDefiningPageMapper.findPageId("8a8884e77cc9e70a017cca14d07e0002", "8a88835c7cd96d31017cda2fd9910000", "1");
    }

    @Test
    public void getPageById() {
        selfDefiningPageMapper.getPageById("8a8884e77cc9e70a017cca14d07e0002", "8a88835c7cd96d31017cda2fd9910000", "1");
    }

}
