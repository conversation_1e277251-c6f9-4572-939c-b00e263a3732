package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.SysUserMapper;
import com.kerryprops.kip.service.integral.mapper.TbBatchIntegralPresentDetailMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbBatchIntegralPresentDetailMapperTest extends BaseTest {

    @Autowired
    private TbBatchIntegralPresentDetailMapper tbBatchIntegralPresentDetailMapper;

    @Test
    public void queryDetailByVipcodeAndOverdueTime() {
        tbBatchIntegralPresentDetailMapper.queryDetailByVipcodeAndOverdueTime("KERRY100211498", "4028e3817c2b3f79017c2b48c54c0000", "2025-01-01 00:00:00");
    }
}
