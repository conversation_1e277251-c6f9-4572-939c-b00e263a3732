package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.TbMemberGradeMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbMemberGradeMapperTest extends BaseTest {

    @Autowired
    private TbMemberGradeMapper tbMemberGradeMapper;

    @Test
    public void getByGradeAndGroupId() {
        tbMemberGradeMapper.getByGradeAndGroupId("1", "4028e3817c2b3f79017c2b48c54c0000");
    }

    @Test
    public void getGradeSortAscByGroupId() {
        tbMemberGradeMapper.getGradeSortAscByGroupId("4028e3817c2b3f79017c2b48c54c0000");
    }

    @Test
    public void getGradeSortDescByGroupId() {
        tbMemberGradeMapper.getGradeSortDescByGroupId("4028e3817c2b3f79017c2b48c54c0000");
    }

    @Test
    public void getMinGradeByGroupId() {
        tbMemberGradeMapper.getMinGradeByGroupId("4028e3817c2b3f79017c2b48c54c0000");
    }

}
