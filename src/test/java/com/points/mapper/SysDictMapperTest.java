package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.SysDictMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class SysDictMapperTest extends BaseTest {

    @Autowired
    private SysDictMapper sysDictMapper;

    @Test
    public void findByDictTypes() {
        sysDictMapper.findByDictTypes(Collections.singletonList("XS0075"));
    }

    @Test
    public void findByDictType() {
        sysDictMapper.findByDictType("XS0075");
    }

    @Test
    public void getListByTypes() {
        sysDictMapper.getListByTypes(Collections.singletonList("XS0075"));
    }

}
