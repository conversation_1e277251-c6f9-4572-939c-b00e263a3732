package com.points.mapper;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate;
import com.kerryprops.kip.service.integral.mapper.TbIntegralCategoryRateMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbIntegralCategoryRateMapperTest extends BaseTest {

    @Autowired
    private TbIntegralCategoryRateMapper tbIntegralCategoryRateMapper;

    @Test
    public void categoryRatePage() {
        tbIntegralCategoryRateMapper.categoryRatePage("8a84853b7c91ac5b017c962dab55030e", "6", Collections.emptyList(), 1, 1);
    }

    @Test
    public void getTotal() {
        tbIntegralCategoryRateMapper.getTotal("8a84853b7c91ac5b017c962dab55030e", "6", Collections.emptyList());
    }

    @Test
    public void deleteByParams() {
        tbIntegralCategoryRateMapper.deleteByParams("8a84853b7c91ac5b017c962dab55030e", "", "6");
    }

    @Test
    public void saveCategoryRateList() {
        tbIntegralCategoryRateMapper.saveCategoryRateList(Collections.singletonList(TbIntegralCategoryRate.builder()
                .groupId("8a84853b7c91ac5b017c962dab55030e")
                        .mallId("8aaa80b47c784020017c78b00d060022")
                .categoryId("001")
                .categoryName("001")
                .gradeId("001")
                .gradeName("铂金卡")
                .money(BigDecimal.ONE)
                .pointNum(BigDecimal.ONE)
                .status(1)
                .creator("")
                .updater("")
                .createDate(DateUtil.date())
                .isConsistent(0)
                .build()));
    }

    @Test
    public void getCategoryRateList() {
        tbIntegralCategoryRateMapper.getCategoryRateList(TbIntegralCategoryRate.builder()
                .groupId("8a84853b7c91ac5b017c962dab55030e")
                .categoryId("001")
                .categoryName("001")
                .gradeId("001")
                .gradeName("铂金卡")
                .money(BigDecimal.ONE)
                .pointNum(BigDecimal.ONE)
                .status(1)
                .creator("")
                .updater("")
                .createDate(DateUtil.date())
                .build());
    }

}
