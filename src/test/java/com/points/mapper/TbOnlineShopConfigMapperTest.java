package com.points.mapper;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig;
import com.kerryprops.kip.service.integral.mapper.TbOnlineShopConfigMapper;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopResource;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbOnlineShopConfigMapperTest extends BaseTest {

    @Autowired
    private TbOnlineShopConfigMapper tbOnlineShopConfigMapper;

    @Test
    public void onlineShopTotal() {
        tbOnlineShopConfigMapper.onlineShopTotal(TbOnlineShopResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").build());
    }

    @Test
    public void onlineShopPage() {
        tbOnlineShopConfigMapper.onlineShopPage(TbOnlineShopResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").build());
    }

    @Test
    public void deleteByParam() {
        tbOnlineShopConfigMapper.deleteByParam(TbOnlineShopConfig.builder().mallId("8aaa80b47c784020017c78b00d060022").businessType("XS0110").build());
    }

    @Test
    public void insertOnlineShopBatch() {
        tbOnlineShopConfigMapper.insertOnlineShopBatch(Collections.singletonList(TbOnlineShopConfig
                .builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .grade("3")
                .businessType("XS0110")
                .money(BigDecimal.ONE)
                .pointNum(BigDecimal.TEN)
                .status(0)
                .createDate(DateUtil.date())
                .creator("")
                .updateDate(DateUtil.date())
                .updater("")
                .build()));
    }

    @Test
    public void findByMallIdAndGradeAndBusinessType() {
        tbOnlineShopConfigMapper.findByMallIdAndGradeAndBusinessType("8aaa80b47c784020017c78b00d060022", "3", "");
    }

}
