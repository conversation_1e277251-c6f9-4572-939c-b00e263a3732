package com.points.mapper;

import com.kerryprops.kip.service.integral.entity.SubscriptionRecord;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.mapper.TbPhotoReviewMapper;
import com.kerryprops.kip.service.integral.mapper.TbSubscriptionRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbSubscriptionRecordMapperTest extends BaseTest {

    @Autowired
    private TbSubscriptionRecordMapper tbSubscriptionRecordMapper;

    @Test
    public void querySubscriptionDto() {
        SubscriptionRecord list = tbSubscriptionRecordMapper.querySubscriptionDto(SubscriptionRecordQueryDto.builder().mallId("8aaa81cb7c836c6b017c83e46b110001").build());
    }

    @Test
    public void querySubscriptionList() {
        List<SubscriptionRecord> list = tbSubscriptionRecordMapper.querySubscriptionList(SubscriptionRecordQueryDto.builder().mallId("8aaa81cb7c836c6b017c83e46b110001").build());
    }

    @Test
    public void querySubscriptionUserIdList() {
        List<String> list = tbSubscriptionRecordMapper.querySubscriptionUserIdList(SubscriptionRecordQueryDto.builder().mallId("8aaa81cb7c836c6b017c83e46b110001").build());
    }

}
