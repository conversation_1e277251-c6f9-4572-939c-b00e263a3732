package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbMemberCardOpenRecordMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbMemberCardOpenRecordMapperTest extends BaseTest {

    @Autowired
    private TbMemberCardOpenRecordMapper tbMemberCardOpenRecordMapper;

    @Test
    public void selectByOpenIdAndMallId() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbMemberCardOpenRecordMapper.selectByOpenIdAndMallId("", "");
        DynamicDataSourceContextHolder.clear();
    }

}
