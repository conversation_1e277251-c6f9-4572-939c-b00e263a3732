package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.TbMemberGradeRuleMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbMemberGradeRuleMapperTest extends BaseTest {

    @Autowired
    private TbMemberGradeRuleMapper tbMemberGradeRuleMapper;

    @Test
    public void queryGradeRulesByGroupIdAndGradeAndRuleTypes() {
        tbMemberGradeRuleMapper.queryGradeRulesByGroupIdAndGradeAndRuleTypes("8aaa81947c6e1ca0017c73c13cc30006", "3", Collections.EMPTY_LIST);
    }

    @Test
    public void queryGradeRulesByGroupIdAndGradeAndType() {
        tbMemberGradeRuleMapper.queryGradeRulesByGroupIdAndGradeAndType("8aaa81947c6e1ca0017c73c13cc30006", "3", 1);
    }

    @Test
    public void findGradeRulesByGroupIdAndType() {
        tbMemberGradeRuleMapper.findGradeRulesByGroupIdAndType("8aaa81947c6e1ca0017c73c13cc30006", 1);
    }

    @Test
    public void findGradeRulesByGroupIdAndRuleType() {
        tbMemberGradeRuleMapper.findGradeRulesByGroupIdAndRuleType("8aaa81947c6e1ca0017c73c13cc30006", 1);
    }

    @Test
    public void isCycleYear() {
        tbMemberGradeRuleMapper.isCycleYear("8aaa81947c6e1ca0017c73c13cc30006", 1);
    }

}
