package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.TbMemberEquityMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbMemberEquityMapperTest extends BaseTest {

    @Autowired
    private TbMemberEquityMapper tbMemberEquityMapper;

    @Test
    public void findByMallIdAndType() {
        tbMemberEquityMapper.findByMallIdAndType("8aaa82ea804d07cd01805174dd3b000c","8aaa80b47c784020017c78b00d060022", Arrays.asList(1, 2));
    }

}
