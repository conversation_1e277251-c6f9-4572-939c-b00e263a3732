package com.points.mapper;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import com.kerryprops.kip.service.integral.mapper.TbIntegralShopRateMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbIntegralShopRateMapperTest extends BaseTest {

    @Autowired
    private TbIntegralShopRateMapper tbIntegralShopRateMapper;

    @Test
    public void shopRatePage() {
        tbIntegralShopRateMapper.shopRatePage("4028e3817c2b3f79017c2b48c54c00001", Collections.emptyList(), "HKC00021031", 1, 1);
    }

    @Test
    public void getTotal() {
        tbIntegralShopRateMapper.getTotal("4028e3817c2b3f79017c2b48c54c00001", Collections.emptyList(), "HKC00021031");
    }

    @Test
    public void getShopRateList() {
        tbIntegralShopRateMapper.getShopRateList(TbIntegralShopRate.builder().groupId("4028e3817c2b3f79017c2b48c54c00001").build());
    }

    @Test
    public void saveShopRateList() {
        tbIntegralShopRateMapper.saveShopRateList(Collections.singletonList(TbIntegralShopRate
                .builder()
                .groupId("8a84853b7c91ac5b017c962dab55030e")
                .mallId("")
                .shopId("001")
                .shopName("001")
                .gradeId("001")
                .gradeName("铂金卡")
                .money(BigDecimal.ONE)
                .pointNum(BigDecimal.ONE)
                .status(1)
                .creator("")
                .updater("")
                .isConsistent(0)
                .createDate(DateUtil.date()).build())
        );
    }

    @Test
    public void deleteByParams() {
        tbIntegralShopRateMapper.deleteByParams("8aaa80b47c784020017c78b00d0600221", Collections.emptyList());
    }

    @Test
    public void getShopRateDto() {
        tbIntegralShopRateMapper.getShopRateDto(TbIntegralShopRate.builder().groupId("4028e3817c2b3f79017c2b48c54c00001").build());
    }

}
