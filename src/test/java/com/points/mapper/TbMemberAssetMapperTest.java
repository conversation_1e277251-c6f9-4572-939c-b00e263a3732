package com.points.mapper;

import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 05/28/2024 09:06
 **********************************************************************************************************************/

@Slf4j
public class TbMemberAssetMapperTest extends BaseTest {

    @Autowired
    private TbMemberAssetMapper tbMemberAssetMapper;

    @Test
    public void queryMemberByGroupIdAndKipUserId() {
        tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId("4028e3817c2b3f79017c2b48c54c0000", "2c9d85bc8489e0ba01849e4629760003");
    }

    @Test
    public void queryMemberByGroupIdAndMobile() {
        tbMemberAssetMapper.queryMemberByGroupIdAndMobile("4028e3817c2b3f79017c2b48c54c0000", "16691027090");
    }

    @Test
    public void queryMemberByGroupIdAndVipcode() {
        tbMemberAssetMapper.queryMemberByGroupIdAndVipcode("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213686");
    }

    @Test
    public void findKipUserIdByGroupIdAndVipcode() {
        tbMemberAssetMapper.findKipUserIdByGroupIdAndVipcode("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213686");
    }

    @Test
    public void queryMemberByGroupIdAndVipcodes() {
        tbMemberAssetMapper.queryMemberByGroupIdAndVipcodes("4028e3817c2b3f79017c2b48c54c0000", Collections.emptyList());
    }

    @Test
    public void updateMemberStatus() {
        tbMemberAssetMapper.updateMemberStatus(32L, "1");
    }

    @Test
    public void updateMemberIntegral() {
        //tbMemberAssetMapper.updateMemberIntegral(TbMemberAsset.builder().build());
    }

    @Test
    public void fillMemberKipUserId() {
        tbMemberAssetMapper.fillMemberKipUserId(TbMemberAsset.builder().build());
    }

    @Test
    public void getMemberCountBetweenDate() {
        tbMemberAssetMapper.getMemberCountBetweenDate("4028e3817c2b3f79017c2b48c54c0000", "", "", "", "");
    }

    @Test
    public void updateMemberCompletedStatus() {
        tbMemberAssetMapper.updateMemberCompletedStatus(TbMemberAsset.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY100213686").build());
    }

    @Test
    public void updateRegisterSource() {
        tbMemberAssetMapper.updateRegisterSource(TbMemberAsset.builder()
                        .grade("01")
                        .registerSource("1111")
                .groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY100213686").build());
    }

    @Test
    public void updateMemberGrade() {
        TbMemberAsset memberAsset = TbMemberAsset.builder().build();
        tbMemberAssetMapper.updateMemberGrade(memberAsset);
    }

    @Test
    public void updateMobileByKipUserId() {
        tbMemberAssetMapper.updateMobileByKipUserId("19899879695", "2c9d85bc8489e0ba01849e4629760003");
    }

    @Test
    public void findByKipUserId() {
        tbMemberAssetMapper.findByKipUserId("2c9d85bc8489e0ba01849e4629760003");
    }

    @Test
    public void findByMobile() {
        tbMemberAssetMapper.findByMobile("19083838611");
    }

    @Test
    public void findByDto() {
        tbMemberAssetMapper.findByDto(SingleMemberQueryDto.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY100213686").build());
    }

    @Test
    public void copyToMemberInvalidByKipUserId() {
        tbMemberAssetMapper.copyToMemberInvalidByKipUserId("2c9d85bc8489e0ba01849e4629760003", Collections.singletonList("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    public void copyToMemberInvalidByMobile() {
        tbMemberAssetMapper.copyToMemberInvalidByMobile("16691027090", Collections.singletonList("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    public void removeMemberByKipUserId() {
        tbMemberAssetMapper.removeMemberByKipUserId("2c9d85bc8489e0ba01849e4629760003", Collections.singletonList("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    public void removeMemberByMobile() {
        tbMemberAssetMapper.removeMemberByMobile("16691027090", Collections.singletonList("4028e3817c2b3f79017c2b48c54c0000"));
    }

}
