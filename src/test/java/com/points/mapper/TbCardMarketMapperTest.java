package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.TbCardMarketMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbCardMarketMapperTest extends BaseTest {

    @Autowired
    private TbCardMarketMapper tbCardMarketMapper;

//    @Test
//    public void selectByMallAli() {
//        DynamicDataSourceContextHolder.push("xcrm");
//        tbCardMarketMapper.selectByMallAli("8a84853b7c91ac5b017c961a9b2a030d");
//        DynamicDataSourceContextHolder.clear();
//    }

}
