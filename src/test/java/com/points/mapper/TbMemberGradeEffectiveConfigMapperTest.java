package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.TbMemberGradeEffectiveConfigMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbMemberGradeEffectiveConfigMapperTest extends BaseTest {

    @Autowired
    private TbMemberGradeEffectiveConfigMapper tbMemberGradeEffectiveConfigMapper;

    @Test
    public void findByGroupId() {
        tbMemberGradeEffectiveConfigMapper.findByGroupId("8aaa80b47c784020017c78b00d060022");
    }

}
