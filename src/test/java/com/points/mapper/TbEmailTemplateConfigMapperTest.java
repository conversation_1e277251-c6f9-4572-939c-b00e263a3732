package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.TbEmailTemplateConfigMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbEmailTemplateConfigMapperTest extends BaseTest {

    @Autowired
    private TbEmailTemplateConfigMapper tbEmailTemplateConfigMapper;

//    @Test
//    public void findByEntity() {
//        DynamicDataSourceContextHolder.push("xcrm");
//        tbEmailTemplateConfigMapper.findByEntity(TbEmailTemplateConfig.builder().mallId("8a88a9fd7f73ffcd017f968739870006").build());
//        DynamicDataSourceContextHolder.clear();
//    }

}
