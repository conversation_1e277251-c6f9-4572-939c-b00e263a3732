package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionconditionMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbActivityPromotionConditionMapperTest extends BaseTest {

    @Autowired
    private TbActivityPromotionconditionMapper tbActivityPromotionconditionMapper;

    @Test
    public void findByPromotionId() {
        tbActivityPromotionconditionMapper.findByPromotionId("174bb535a47d4ddf88b08dde2829ea34");
    }

    @Test
    public void findByPromotionIds() {
        tbActivityPromotionconditionMapper.findByPromotionIds(Collections.singletonList("174bb535a47d4ddf88b08dde2829ea34"));
    }

}
