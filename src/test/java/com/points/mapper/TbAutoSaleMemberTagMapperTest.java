package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbAutoSaleMemberTagMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbAutoSaleMemberTagMapperTest extends BaseTest {

    @Autowired
    private TbAutoSaleMemberTagMapper tbAutoSaleMemberTagMapper;

    @Test
    public void getBySecSortIdAndType() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbAutoSaleMemberTagMapper.getBySecSortIdAndType(1556491138717724673L, 2);
        DynamicDataSourceContextHolder.clear();
    }

}
