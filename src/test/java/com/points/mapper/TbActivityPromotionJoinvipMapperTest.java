package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionJoinvipMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbActivityPromotionJoinvipMapperTest extends BaseTest {

    @Autowired
    private TbActivityPromotionJoinvipMapper tbActivityPromotionJoinvipMapper;

    @Test
    public void findByPromotionId() {
        tbActivityPromotionJoinvipMapper.findByPromotionId("2280d233295e4b8b90d409c600c777fc");
    }

    @Test
    public void findByPromotionIds() {
        tbActivityPromotionJoinvipMapper.findByPromotionIds(Collections.singletonList("2280d233295e4b8b90d409c600c777fc"));
    }

}
