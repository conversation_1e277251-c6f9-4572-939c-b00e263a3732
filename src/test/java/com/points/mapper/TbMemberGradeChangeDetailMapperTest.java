package com.points.mapper;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeChangeDetailMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbMemberGradeChangeDetailMapperTest extends BaseTest {

    @Autowired
    private TbMemberGradeChangeDetailMapper tbMemberGradeChangeDetailMapper;

    @Test
    public void queryChangeMaxByGroupIdAndVipcode() {
        tbMemberGradeChangeDetailMapper.queryChangeMaxByGroupIdAndVipcode("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213966");
    }

    @Test
    public void getMaxChangeDetailByGroupIdAndVipcodeAndOverDate() {
        tbMemberGradeChangeDetailMapper.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213966", "");
    }

    @Test
    public void getChangeDetailList() {
        tbMemberGradeChangeDetailMapper.getChangeDetailList("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213966");
    }

    @Test
    public void queryMemberJoinTimeMaxGradeChangeItem() {
        tbMemberGradeChangeDetailMapper.queryMemberJoinTimeMaxGradeChangeItem("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213966", "");
    }

    @Test
    public void queryDetailByGroupIdAndDate() {
        tbMemberGradeChangeDetailMapper.queryDetailByGroupIdAndDate("4028e3817c2b3f79017c2b48c54c0000", "2026-09-08 00:00:00");
    }

    @Test
    public void updateChangeDate() {
        tbMemberGradeChangeDetailMapper.updateChangeDate(TbMemberGradeChangeDetail.builder().id("07594f4819994ce7b4fac419a97ea6e1").createDate(DateUtil.date()).build());
    }

    @Test
    public void queryChangeGradeListBySaleDate() {
        tbMemberGradeChangeDetailMapper.queryChangeGradeListBySaleDate("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213966", "");
    }

    @Test
    public void getMaxRelegationRecordByDate() {
        tbMemberGradeChangeDetailMapper.getMaxRelegationRecordByDate("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213966", "", Collections.EMPTY_LIST);
    }

    @Test
    public void queryNearChangeGradeBySaleDate() {
        tbMemberGradeChangeDetailMapper.queryNearChangeGradeBySaleDate("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213966", "");
    }

    @Test
    public void getJoinDayMaxGrade() {
        tbMemberGradeChangeDetailMapper.getJoinDayMaxGrade("KERRY100213966", "4028e3817c2b3f79017c2b48c54c0000", "");
    }

}
