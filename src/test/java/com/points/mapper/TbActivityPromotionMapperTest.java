package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbActivityPromotionMapperTest extends BaseTest {

    @Autowired
    private TbActivityPromotionMapper tbActivityPromotionMapper;

    @Test
    public void queryByMallIdAndDate() {
        tbActivityPromotionMapper.queryByMallIdAndDate("8aaa80b47c784020017c78b00d060022", "");
    }

}
