package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.mapper.TbPhotoReviewMapper;
import com.kerryprops.kip.service.integral.model.dto.BonusSelfQueryDto;
import com.kerryprops.kip.service.integral.model.dto.BonusSelfUploadDto;
import com.kerryprops.kip.service.integral.model.dto.PhotoReviewCheckDto;
import com.kerryprops.kip.service.integral.model.dto.TakePhotoAuditDto;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbPhotoReviewMapperTest extends BaseTest {

    @Autowired
    private TbPhotoReviewMapper tbPhotoReviewMapper;

    @Test
    public void findByOcrTaskId() {
        TbPhotoReview photoReview = tbPhotoReviewMapper.findByOcrTaskId("240731_621add5716c547f6883e020a29366900_uat");
        Assert.assertNotNull(photoReview);
    }

    @Test
    public void checkPhotoReviewRecord() {
        DynamicDataSourceContextHolder.push("points");
        PhotoReviewCheckDto checkDto = PhotoReviewCheckDto.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").vipcode("KERRY100213886").ticketNo("2024080242000001659").shopNo("JAKC00011019")
                .amount(Double.parseDouble("1500.00")).tradingDate("2024-08-02").timestamp("16:59:10").build();
        TbPhotoReview tbPhotoReview = tbPhotoReviewMapper.checkPhotoReviewRecord(checkDto);
        DynamicDataSourceContextHolder.clear();
        Assert.assertNotNull(tbPhotoReview);
    }
    @Test
    public void geTbBonusSelfList() {
        tbPhotoReviewMapper.getBonusList(BonusSelfQueryDto.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").build());
    }

    @Test
    public void getBonusTotal() {
        tbPhotoReviewMapper.getBonusTotal(BonusSelfQueryDto.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").build());
    }

    @Test
    public void getReviewPage() {
        tbPhotoReviewMapper.getReviewPage(BonusSelfQueryDto.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").build());

    }

    @Test
    public void getForPrevPage() {
        tbPhotoReviewMapper.getForPrevPage("1724653223199", "8aaa81cb7c836c6b017c83e2c76f0000", "1717344000000", "1725292799000");
    }

    @Test
    public void getForNextPage() {
        tbPhotoReviewMapper.getForNextPage("1724653223199", "8aaa81cb7c836c6b017c83e2c76f0000", "1717344000000", "1725292799000");
    }

    @Test
    public void getNextImage() {
        tbPhotoReviewMapper.getNextImage("1724653223199", "8aaa81cb7c836c6b017c83e2c76f0000", "1717344000000", "1725292799000");
    }

}
