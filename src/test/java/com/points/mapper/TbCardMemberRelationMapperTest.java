package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbCardMemberRelationMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbCardMemberRelationMapperTest extends BaseTest {

    @Autowired
    private TbCardMemberRelationMapper tbCardMemberRelationMapper;

    @Test
    public void getByGroupIdAndAliUserId() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbCardMemberRelationMapper.getByGroupIdAndAliUserId("8a84853b7c91ac5b017c962dab55030e", "2088902504263681");
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getByMallIdAndAliUserId() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbCardMemberRelationMapper.getByMallIdAndAliUserId("8a84853b7c91ac5b017c961a9b2a030d", "2088902504263681");
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getByMallIdAndMobile() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbCardMemberRelationMapper.getByMallIdAndMobile("8a84853b7c91ac5b017c961a9b2a030d", "15868844766");
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getByMallIdAndExternalCardNo() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbCardMemberRelationMapper.getByMallIdAndExternalCardNo("8a84853b7c91ac5b017c961a9b2a030d", "KERRY100200104");
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getByAliUserId() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbCardMemberRelationMapper.getByAliUserId("2088122041803564");
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getByGroupIdAndMobileAndMallId() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbCardMemberRelationMapper.getByGroupIdAndMobileAndMallId("8a84853b7c91ac5b017c962dab55030e", "15868844766", "8a84853b7c91ac5b017c961a9b2a030d");
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void findByExternalCardNo() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbCardMemberRelationMapper.findByExternalCardNo("KERRY100200104");
        DynamicDataSourceContextHolder.clear();
    }

}
