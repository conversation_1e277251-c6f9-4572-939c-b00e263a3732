package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbInsensatePointsPushRecordMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbInsensatePointsPushRecordMapperTest extends BaseTest {

    @Autowired
    private TbInsensatePointsPushRecordMapper tbInsensatePointsPushRecordMapper;

    @Test
    public void findByTransactionIdAndEventTypeAndOrigin() {
        tbInsensatePointsPushRecordMapper.findByTransactionIdAndEventTypeAndOrigin("4200001619202210203322112360", 1, 1);
    }

    @Test
    public void findByOpenIdAndTransactionIdAndEventType() {
        tbInsensatePointsPushRecordMapper.findByOpenIdAndTransactionIdAndEventType("oudWQ5WxMpOkjuLjEKB8884Q4DFs", "4200001619202210203322112360", 1);
    }

    @Test
    public void selectByOpenId() {
        tbInsensatePointsPushRecordMapper.selectByOpenId("oudWQ5WxMpOkjuLjEKB8884Q4DFs");
    }

    @Test
    public void findByTransactionId() {
        tbInsensatePointsPushRecordMapper.findByTransactionId("4200001619202210203322112360");
    }

}
