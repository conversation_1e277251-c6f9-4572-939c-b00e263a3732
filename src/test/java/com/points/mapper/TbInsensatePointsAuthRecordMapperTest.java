package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbInsensatePointsAuthRecordMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbInsensatePointsAuthRecordMapperTest extends BaseTest {

    @Autowired
    private TbInsensatePointsAuthRecordMapper tbInsensatePointsAuthRecordMapper;

    @Test
    public void selectByOpenId() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbInsensatePointsAuthRecordMapper.selectByOpenId("oudWQ5ccsJLSlUGt0s_RQysoHqgg", "8a8883557cca9463017ccb002b360001", "8a8884e77cc9e70a017cca1011970001", 1, 1);
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getByOpenIdAndMallId() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbInsensatePointsAuthRecordMapper.getByOpenIdAndMallId("oudWQ5ccsJLSlUGt0s_RQysoHqgg", "8a8883557cca9463017ccb002b360001");
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void checkExists() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbInsensatePointsAuthRecordMapper.checkExists("oudWQ5ccsJLSlUGt0s_RQysoHqgg", "8a8884e77cc9e70a017cca1011970001", "8a8883557cca9463017ccb002b360001", 0);
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void selectByKipUserIdAndMallId() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbInsensatePointsAuthRecordMapper.selectByKipUserIdAndMallId("8a8481d78e63c8c8018e7426f18e0000", "8a8883557cca9463017ccb002b360001");
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getByKipUserIdAndMallIdAndOrigin() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbInsensatePointsAuthRecordMapper.getByKipUserIdAndMallIdAndOrigin("8a8481d78e63c8c8018e7426f18e0000", "8a8883557cca9463017ccb002b360001", 1);
        DynamicDataSourceContextHolder.clear();
    }

//    @Test
//    public void insertBatchFromCardMemberRelation() {
//        DynamicDataSourceContextHolder.push("xcrm");
//        tbInsensatePointsAuthRecordMapper.insertBatchFromCardMemberRelation();
//        DynamicDataSourceContextHolder.clear();
//    }

    @Test
    public void getBatchData() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbInsensatePointsAuthRecordMapper.getBatchData(12L, 100);
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getMallBatchData() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbInsensatePointsAuthRecordMapper.getMallBatchData("8a8883557cca9463017ccb002b360001", 12L, 100);
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getByCardNoAndOrigin() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbInsensatePointsAuthRecordMapper.getByCardNoAndOrigin("", 1);
        DynamicDataSourceContextHolder.clear();
    }

}
