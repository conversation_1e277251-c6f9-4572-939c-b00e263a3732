package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbAutoPointsConfigMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbAutoPointsConfigMapperTest extends BaseTest {

    @Autowired
    private TbAutoPointsConfigMapper tbAutoPointsConfigMapper;

    @Test
    public void findByLbsId() {
        tbAutoPointsConfigMapper.findByLbsId("1");
    }

    @Test
    public void listData() {
        tbAutoPointsConfigMapper.listData("4028e3817c2b3f79017c2b48c54c0000", Collections.EMPTY_LIST, 10, 10);
    }

    @Test
    public void total() {
        tbAutoPointsConfigMapper.total("4028e3817c2b3f79017c2b48c54c0000", Collections.EMPTY_LIST);
    }

}
