package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.entity.TbOcrCallbackRecord;
import com.kerryprops.kip.service.integral.mapper.TbOcrCallbackRecordMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 08/13/2024 08:45
 **********************************************************************************************************************/

@Slf4j
public class TbOcrCallbackRecordMapperTest extends BaseTest {

    @Autowired
    private TbOcrCallbackRecordMapper tbOcrCallbackRecordMapper;

    @Test
    public void findByPhotoIds() {
        DynamicDataSourceContextHolder.push("points");
        List<TbOcrCallbackRecord> list = tbOcrCallbackRecordMapper.findByPhotoIds(Arrays.asList(66L, 67L));
        DynamicDataSourceContextHolder.clear();
        Assert.assertNotNull(list);
    }

}
