package com.points.mapper;

import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.entity.TbCfgAuthorizerWx;
import com.kerryprops.kip.service.integral.entity.TbKoIntegralClearRecord;
import com.kerryprops.kip.service.integral.mapper.TbKoIntegralClearRecordMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbKoIntegralClearRecordMapperTest extends BaseTest {

    @Autowired
    private TbKoIntegralClearRecordMapper tbKoIntegralClearRecordMapper;

    @Test
    public void saveBatch() {
        tbKoIntegralClearRecordMapper.saveBatch(Collections.singletonList(TbKoIntegralClearRecord
                .builder()
                .groupId("8a88835c7cd96d31017cda3f77e80003")
                .vipcode("01000002280")
                .getPoints(10)
                .costPoints(10)
                .clearPoints(10)
                .pointsNum(10)
                .createDate(DateUtil.date())
                .updateDate(DateUtil.date())
                .build()));
    }

    @Test
    public void total() {
        tbKoIntegralClearRecordMapper.total();
    }

    @Test
    public void findList() {
        tbKoIntegralClearRecordMapper.findList(2, 10);
    }

}
