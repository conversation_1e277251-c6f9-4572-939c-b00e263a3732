package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionIntegralMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbActivityPromotionIntegralMapperTest extends BaseTest {

    @Autowired
    private TbActivityPromotionIntegralMapper tbActivityPromotionIntegralMapper;

    /*public void saveBatch() {
        DynamicDataSourceContextHolder.push("xcrm");
        List<TbActivityPromotionIntegral> list = new ArrayList<>();
        list.add(TbActivityPromotionIntegral.builder()
                .promotionId("b15cf471bba94619b6ddb7919041e8b6")
                .pointsId("48d8ca42ef2e400f90861cc07649baeb")
                .vipcode("KERRY100325541")
                .integral(30)
                .enableIntegral(30)
                .expireTime(DateUtil.date())
                .createDate(DateUtil.date())
                .updateDate(DateUtil.date())
                .mobile("18976543567")
                .promotionName("固定最高8积分")
                .build());
        tbActivityPromotionIntegralMapper.saveBatch(list);
        DynamicDataSourceContextHolder.clear();
    }*/

    @Test
    public void getMatchedPromotions() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbActivityPromotionIntegralMapper.getMatchedPromotions(Collections.singletonList("3146fbd09d894db38ecc48fe35f94e1c"));
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getListByVipCode() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbActivityPromotionIntegralMapper.getListByVipCode("KERRY100303050");
        DynamicDataSourceContextHolder.clear();
    }

    @Test
    public void getSaleMatchedActivityPromotions() {
        DynamicDataSourceContextHolder.push("xcrm");
        tbActivityPromotionIntegralMapper.getSaleMatchedActivityPromotions("KERRY100303050", "SZ04231111");
        DynamicDataSourceContextHolder.clear();
    }

}
