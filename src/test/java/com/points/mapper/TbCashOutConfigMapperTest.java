package com.points.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.kerryprops.kip.service.integral.mapper.TbCashOutConfigMapper;
import com.kerryprops.kip.service.integral.webservice.resource.TbCashOutConfigResource;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbCashOutConfigMapperTest extends BaseTest {

    @Autowired
    private TbCashOutConfigMapper tbCashOutConfigMapper;

    @Test
    public void selectByMallAli() {
        tbCashOutConfigMapper.getConfig(TbCashOutConfigResource.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").build());
    }

}
