package com.points.mapper;

import com.kerryprops.kip.service.integral.entity.TbMemberSource;
import com.kerryprops.kip.service.integral.mapper.TbMemberSourceMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class TbMemberSourceMapperTest extends BaseTest {

    @Autowired
    private TbMemberSourceMapper tbMemberSourceMapper;

    @Test
    public void saveSource() {
        tbMemberSourceMapper.saveSource(TbMemberSource.builder()
                .id(1L).vipcode("KERRY100298988").kipUserId("11111").channel("11").utmChannel("111").utmFunction("111")
                        .utmLbs("1111").utmMethod("1111").utmSource("1111").createUser("").utmUser("111").pagePath("111")
                        .originalParams("111").build());
    }

    @Test
    public void findByVipcode() {
        tbMemberSourceMapper.findByVipcode("KERRY100298988");
    }


}
