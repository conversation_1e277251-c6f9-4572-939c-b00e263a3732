package com.points.mapper;

import com.kerryprops.kip.service.integral.mapper.SysUserMapper;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:32
 **********************************************************************************************************************/

@Slf4j
public class SysUserMapperTest extends BaseTest {

    @Autowired
    private SysUserMapper sysUserMapper;

//    @Test
//    public void getUserListByIds() {
//        DynamicDataSourceContextHolder.push("hbs");
//        sysUserMapper.getUserListByIds(Collections.singletonList("1067246875800000001"));
//        DynamicDataSourceContextHolder.clear();
//    }
//
//    @Test
//    public void getUserEmailList() {
//        DynamicDataSourceContextHolder.push("hbs");
//        sysUserMapper.getUserEmailList(Collections.singletonList("1067246875800000001"));
//        DynamicDataSourceContextHolder.clear();
//    }

}
