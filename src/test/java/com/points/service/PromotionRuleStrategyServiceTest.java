package com.points.service;


import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import com.kerryprops.kip.service.integral.service.promotion.impl.PromotionRuleStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - bert
 * Created Date - 01/13/2025 10:22
 **********************************************************************************************************************/

@Slf4j
class PromotionRuleStrategyServiceTest {

    @InjectMocks
    private PromotionRuleStrategyService promotionRuleStrategyService;

    @Mock
    private Map<String, PromotionRule> ruleMap = new HashMap<>(16);

    @Mock
    private ApplicationContext applicationContext;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleProcess() {
        Assertions.assertThrows(Exception.class, () ->promotionRuleStrategyService.getRuleProcess("123"));
    }

}