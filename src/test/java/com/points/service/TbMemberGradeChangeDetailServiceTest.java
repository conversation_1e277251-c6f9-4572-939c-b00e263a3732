package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeChangeDetailMapper;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TbMemberGradeChangeDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@Slf4j
class TbMemberGradeChangeDetailServiceTest {

    @InjectMocks
    private TbMemberGradeChangeDetailServiceImpl tbMemberGradeChangeDetailServiceImpl;

    @Mock
    private TbMemberGradeChangeDetailMapper tbMemberGradeChangeDetailMapper;
    @Mock
    private RabbitMqService rabbitMqService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void queryChangeMaxByGroupIdAndVipcode() {
        Mockito.when(tbMemberGradeChangeDetailMapper.queryChangeMaxByGroupIdAndVipcode(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(new TbMemberGradeChangeDetail());
        Assertions.assertNotNull(tbMemberGradeChangeDetailServiceImpl.queryChangeMaxByGroupIdAndVipcode(Mockito.anyString(), Mockito.anyString()));
    }

    @Test
    void getMaxChangeDetailByGroupIdAndVipcodeAndOverDate() {
        Mockito.when(tbMemberGradeChangeDetailMapper.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(new TbMemberGradeChangeDetail());
        Assertions.assertNotNull(tbMemberGradeChangeDetailServiceImpl
                .getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()));
    }

    @Test
    void getChangeDetailList() {
        Mockito.when(tbMemberGradeChangeDetailMapper.getChangeDetailList(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(new ArrayList<>());
        Assertions.assertNotNull(tbMemberGradeChangeDetailServiceImpl.getChangeDetailList(Mockito.anyString(), Mockito.anyString()));
    }

    @Test
    void fillMemberGradeChangeDetailMallId() {
        Mockito.when(tbMemberGradeChangeDetailMapper.queryChangeMaxByGroupIdAndVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeChangeDetail.builder().build());
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.fillMemberGradeChangeDetailMallId("", "", ""));

        Mockito.when(tbMemberGradeChangeDetailMapper.queryChangeMaxByGroupIdAndVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeChangeDetail.builder().mallId("mallId").build());
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.fillMemberGradeChangeDetailMallId("", "", ""));

        Mockito.when(tbMemberGradeChangeDetailMapper.queryChangeMaxByGroupIdAndVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.fillMemberGradeChangeDetailMallId("", "", ""));
    }

    @Test
    void reduceMemberGradeChangeDate() {
        Mockito.when(tbMemberGradeChangeDetailMapper.queryDetailByGroupIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.reduceMemberGradeChangeDate("", ""));

        Mockito.when(tbMemberGradeChangeDetailMapper.queryDetailByGroupIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGradeChangeDetail.builder().build(),
                        TbMemberGradeChangeDetail.builder().createDate(DateUtil.date()).build()));
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.reduceMemberGradeChangeDate("", ""));
    }

    @Test
    void saveDetail() {
        TbMemberGradeChangeDetail gradeDetail = new TbMemberGradeChangeDetail();
        gradeDetail.setNewGrade("03");
        gradeDetail.setOldGrade("02");
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.saveDetail(gradeDetail));

        TbMemberGradeChangeDetail gradeDetail1 = new TbMemberGradeChangeDetail();
        gradeDetail1.setNewGrade("03");
        gradeDetail1.setOldGrade("02");
        gradeDetail1.setGroupId("groupId");
        gradeDetail1.setVipcode("vipCode");
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.saveDetail(gradeDetail1));
    }

    @Test
    void saveMemberGradeChangeRecord() {
        TbMemberAsset asset = new TbMemberAsset();
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.saveMemberGradeChangeRecord(asset, 1, "", "", "", ""));
    }

    @Test
    void queryChangeGradeListBySaleDate() {
        Mockito.when(tbMemberGradeChangeDetailMapper.queryChangeGradeListBySaleDate(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(new ArrayList<>());
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.queryChangeGradeListBySaleDate( "", "", ""));
    }

    @Test
    void getMaxRelegationRecordByDate() {
        Mockito.when(tbMemberGradeChangeDetailMapper.getMaxRelegationRecordByDate(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeChangeDetail.builder().build());
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.getMaxRelegationRecordByDate( "", "", "", null));
    }

    @Test
    void getChangeDetailGtCreateDate() {
        assertDoesNotThrow(() -> tbMemberGradeChangeDetailServiceImpl.getChangeDetailGtCreateDate( "1111", "KERRY00001", "2024-01-01 00:00:00", null));
    }

    @Test
    void changeMemberGradeChangeDetailDate() {
        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.changeMemberGradeChangeDetailDate( null, DateUtil.date()));

        assertDoesNotThrow(() ->
                tbMemberGradeChangeDetailServiceImpl.changeMemberGradeChangeDetailDate( Collections.singletonList(TbMemberGradeChangeDetail.builder().build()), DateUtil.date()));
    }

    @Test
    void queryNearChangeGradeBySaleDate() {
        Mockito.when(tbMemberGradeChangeDetailMapper.queryNearChangeGradeBySaleDate(Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(TbMemberGradeChangeDetail.builder().build());
        Assertions.assertNotNull(tbMemberGradeChangeDetailServiceImpl.queryNearChangeGradeBySaleDate("", "", ""));
    }

    @Test
    void getJoinDayMaxGrade() {
        Mockito.when(tbMemberGradeChangeDetailMapper.getJoinDayMaxGrade(Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn("123");
        Assertions.assertNotNull(tbMemberGradeChangeDetailServiceImpl.getJoinDayMaxGrade("", "", ""));
    }


}