package com.points.service;

import com.kerryprops.kip.service.integral.mapper.TbMemberGradeMapper;
import com.kerryprops.kip.service.integral.service.impl.TbMemberGradeWithCacheServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 08/05/2024 13:50
 **********************************************************************************************************************/

@Slf4j
class TbMemberGradeWithCacheServiceTest {

    @InjectMocks
    private TbMemberGradeWithCacheServiceImpl tbMemberGradeWithCacheServiceImpl;

    @Mock
    private TbMemberGradeMapper tbMemberGradeMapper;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void queryGradeSortAscByGroupId() {
        Mockito.when(tbMemberGradeMapper.getGradeSortAscByGroupId(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbMemberGradeWithCacheServiceImpl.queryGradeSortAscByGroupId("12345"));
    }


}
