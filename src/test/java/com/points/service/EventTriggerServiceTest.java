package com.points.service;

import com.kerryprops.kip.service.integral.client.EventTriggerClient;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.PerfectMemberActivityDto;
import com.kerryprops.kip.service.integral.service.EventTriggerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class EventTriggerServiceTest {

    @InjectMocks
    private EventTriggerService eventTriggerService;

    @Mock
    private EventTriggerClient eventTriggerClient;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void obtainRewardForInfo() {
        Mockito.when(eventTriggerClient.getPerfectMemberInfo(Mockito.any(), Mockito.any()))
                .thenReturn(PerfectMemberActivityDto.builder().build());
        Assertions.assertNotNull(eventTriggerService.obtainRewardForInfo("123", "123"));

        Mockito.when(eventTriggerClient.getPerfectMemberInfo(Mockito.any(), Mockito.any()))
                .thenThrow(new BizException(400, ""));
        Assertions.assertNotNull(eventTriggerService.obtainRewardForInfo("123", "123"));
    }
}
