package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbAutoSaleMemberTag;
import com.kerryprops.kip.service.integral.entity.TbTagMemberNologic;
import com.kerryprops.kip.service.integral.mapper.TbAutoSaleMemberTagMapper;
import com.kerryprops.kip.service.integral.mapper.TbTagMemberNologicMapper;
import com.kerryprops.kip.service.integral.service.CrmVipcodeService;
import com.kerryprops.kip.service.integral.service.impl.TbAutoSaleMemberTagServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.MemberProfileCheckResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class TbAutoSaleMemberTagServiceTest {

    @InjectMocks
    private TbAutoSaleMemberTagServiceImpl tbAutoSaleMemberTagServiceImpl;

    @Mock
    private TbAutoSaleMemberTagMapper tbAutoSaleMemberTagMapper;
    @Mock
    private TbTagMemberNologicMapper tbTagMemberNologicMapper;
    @Mock
    private CrmVipcodeService crmVipcodeService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getBySecSortIdAndType() {
        Mockito.when(tbAutoSaleMemberTagMapper.getBySecSortIdAndType(Mockito.anyLong(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TbAutoSaleMemberTag.builder().build()));
        Assertions.assertNotNull(tbAutoSaleMemberTagServiceImpl.getBySecSortIdAndType("10", 10));
    }

    @Test
    void getByIds() {
        Assertions.assertNotNull(tbAutoSaleMemberTagServiceImpl.getByIds(null));

        Mockito.when(tbAutoSaleMemberTagMapper.selectBatchIds(Mockito.anyList())).thenReturn(Collections.emptyList());
        Assertions.assertNotNull(tbAutoSaleMemberTagServiceImpl.getByIds(Collections.singletonList(1L)));
    }

    @Test
    void getByGroupIdAndVipcode() {
        Mockito.when(tbTagMemberNologicMapper.getByGroupIdAndVipcode(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(TbTagMemberNologic.builder().build());
        Assertions.assertNotNull(tbAutoSaleMemberTagServiceImpl.getByGroupIdAndVipcode(Mockito.anyString(), Mockito.anyString()));
    }

    @Test
    void updateMemberLabels() {
        MemberProfileCheckResource resource = MemberProfileCheckResource.builder().build();
        Mockito.when(crmVipcodeService.getVipcode(Mockito.any(), Mockito.any())).thenReturn("");
        Mockito.when(tbTagMemberNologicMapper.getByGroupIdAndVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(TbTagMemberNologic.builder().build());
        Assertions.assertDoesNotThrow(() -> tbAutoSaleMemberTagServiceImpl.updateMemberLabels(resource));

        MemberProfileCheckResource resource1 = MemberProfileCheckResource.builder().labels("1000391").build();
        Assertions.assertDoesNotThrow(() -> tbAutoSaleMemberTagServiceImpl.updateMemberLabels(resource1));

        Mockito.when(tbTagMemberNologicMapper.getByGroupIdAndVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbAutoSaleMemberTagServiceImpl.updateMemberLabels(resource));

        Assertions.assertDoesNotThrow(() -> tbAutoSaleMemberTagServiceImpl.updateMemberLabels(resource1));
    }

}
