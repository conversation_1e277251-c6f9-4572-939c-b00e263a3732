package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.service.impl.MemberRegisterServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 04/26/2024 16:10
 **********************************************************************************************************************/

@Slf4j
class MemberRegisterServiceTest {

    @InjectMocks
    private MemberRegisterServiceImpl memberRegisterService;

    @Mock
    private TbMemberAssetMapper tbMemberAssetMapper;
    @Mock
    private RedisService redisService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void saveMember() {
        Assertions.assertThrows(Exception.class, () -> memberRegisterService.saveMember(TbMemberAsset.builder().build()));

        Assertions.assertThrows(Exception.class, () -> memberRegisterService.saveMember(TbMemberAsset.builder().groupId("12345").build()));

        Assertions.assertThrows(Exception.class, () -> memberRegisterService.saveMember(TbMemberAsset.builder().mobile("12345").build()));

        Assertions.assertDoesNotThrow(() -> memberRegisterService.saveMember(TbMemberAsset.builder().groupId("12345").mobile("12345").build()));
    }

    @Test
    void fillMemberKipUserId() {
        Assertions.assertDoesNotThrow(() -> memberRegisterService.fillMemberKipUserId(null, ""));

        Assertions.assertDoesNotThrow(() -> memberRegisterService.fillMemberKipUserId(TbMemberAsset.builder().build(), ""));

        Assertions.assertDoesNotThrow(() -> memberRegisterService.fillMemberKipUserId(TbMemberAsset.builder().id(1L).build(), "12345"));

    }

    @Test
    void findByGroupIdAndKipUserId() {
        Assertions.assertDoesNotThrow(() -> memberRegisterService.findByGroupIdAndKipUserId("123", "12345"));
    }

    @Test
    void findByKipUserId() {
        Assertions.assertDoesNotThrow(() -> memberRegisterService.findByKipUserId("123"));
    }

    @Test
    void queryByGroupIdAndKipUserId() {
        Assertions.assertDoesNotThrow(() -> memberRegisterService.queryByGroupIdAndKipUserId("123", "123"));
    }

    @Test
    void queryByGroupIdAndMobile() {
        Assertions.assertDoesNotThrow(() -> memberRegisterService.queryByGroupIdAndMobile("123", "123"));
    }

    @Test
    void queryByGroupIdAndVipcode() {
        Assertions.assertDoesNotThrow(() -> memberRegisterService.queryByGroupIdAndVipcode("123", "123"));
    }

    @Test
    void findKipUserIdByGroupIdAndVipcode() {
        Assertions.assertDoesNotThrow(() -> memberRegisterService.findKipUserIdByGroupIdAndVipcode("123", "123"));
    }

    @Test
    void removeCacheMember() {
        Assertions.assertDoesNotThrow(() -> memberRegisterService.removeCacheMember("", "123"));

        Assertions.assertDoesNotThrow(() -> memberRegisterService.removeCacheMember("123", ""));

        Assertions.assertDoesNotThrow(() -> memberRegisterService.removeCacheMember("123", "123"));
    }

}
