package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.impl.BirthdayPromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class BirthdayPromotionRuleTest {

    @InjectMocks
    private BirthdayPromotionRule birthdayPromotionRule;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertNotNull(birthdayPromotionRule.getRuleType());
    }

    @Test
    void checkRule() {
        TbActivityPromotionCondition condition = TbActivityPromotionCondition.builder().build();
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build();
        Assertions.assertFalse(birthdayPromotionRule.checkRule(condition, dto, DateUtil.date(), null));

        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder().member(TbMemberAsset.builder().birthDate(DateUtil.date()).build()).build();
        Assertions.assertTrue(birthdayPromotionRule.checkRule(condition, dto1, DateUtil.date(), null));
    }
}
