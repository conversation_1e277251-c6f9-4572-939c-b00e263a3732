package com.points.service;

import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.enums.PointsInterceptStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsIntercept;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.MemberSalePointsProcessServiceImpl;
import com.kerryprops.kip.service.integral.webservice.response.TbInterceptResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

@Slf4j
class MemberSalePointsProcessServiceTest {

    @InjectMocks
    private MemberSalePointsProcessServiceImpl memberSalePointsProcessServiceImpl;

    @Mock
    private TbSalesDetailService tbSalesDetailService;

    @Mock
    private TbPointsRepeatRuleService tbPointsRepeatRuleService;

    @Mock
    private TbSetshoprateService tbSetshoprateService;

    @Mock
    private TbActivityPromotionService tbActivityPromotionService;

    @Mock
    private TbMemberAssetService tbMemberAssetService;

    @Mock
    private RabbitMqService rabbitMqService;

    @Mock
    private TbBaseShopService tbBaseShopService;

    @Mock
    private KerryStaffService kerryStaffService;

    @Mock
    private TbMemberPointsChangeService tbMemberPointsChangeService;

    @Mock
    private TbPointsDetailService tbPointsDetailService;

    @Mock
    private TbInsensatePointsRepeatLogService tbInsensatePointsRepeatLogService;

    @Mock
    private TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;

    @Mock
    private TbPointsInterceptApproveService tbPointsInterceptApproveService;

    @Mock
    private MallConfig mallConfig;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("BKC-支付宝及基础覆盖")
    void salePointsProcess() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder()
                .remark(IntegralConstant.ALIPAY_POINTS)
                .saleType(SaleTypeEnum.ALIPAY.getValue())
                .build();
        // 补充会员
        Mockito.when(tbMemberAssetService.findAllByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any())).thenReturn(CustomerUserDto.builder().id("12345").build());
        // 无店铺进入异常
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto));

        // 补充会员
        Mockito.when(tbMemberAssetService.findAllByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().kipUserId("12345").build());
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto));

        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder()
                .remark(IntegralConstant.ALIPAY_POINTS)
                .saleType(SaleTypeEnum.ALIPAY.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .build();
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(TbBaseShop.builder().build());
        // 进入bkc 支付宝 进入异常
        Mockito.when(mallConfig.isBkc(Mockito.any())).thenReturn(true);
        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(Mockito.any())).thenReturn(TbSalesDetail.builder().build());
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));
    }

    @Test
    @DisplayName("bkc-微信支付进入异常")
    void salePointsProcessCase1() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder()
                .remark(IntegralConstant.WECHAT_POINTS)
                .saleType(SaleTypeEnum.WECHAT.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .baseShop(TbBaseShop.builder().build())
                .build();
        // 进入bkc 支付宝 配置规则为空
        Mockito.when(mallConfig.isBkc(Mockito.any())).thenReturn(true);
        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(Mockito.any())).thenReturn(null);
        Mockito.when(tbPointsRepeatRuleService.getSaleRepeatCondition(Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(null);
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));

        // 配置规则不为空
        Mockito.when(tbPointsRepeatRuleService.getSaleRepeatCondition(Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(MemberSaleRepeatQueryDto.builder().build());
        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto.builder().saleTypes(SaleTypeEnum.getNormalSaleTypeValueForBkc()).build()))
                .thenReturn(TbSalesDetail.builder().build());
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));
    }

    @Test
    @DisplayName("bkc-扫码积分")
    void salePointsProcessCase2() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder()
                .remark("HF0053")
                .saleType(SaleTypeEnum.SCAN.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .baseShop(TbBaseShop.builder().build())
                .build();
        Mockito.when(mallConfig.isBkc(Mockito.any())).thenReturn(true);
        // 配置规则不为空
        Mockito.when(tbPointsRepeatRuleService.getSaleRepeatCondition(Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(MemberSaleRepeatQueryDto.builder().build());
        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(Mockito.any()))
                .thenReturn(null);
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));
    }

    @Test
    @DisplayName("bkc-销售录入")
    void salePointsProcessCase3() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder()
                .remark("HF0053")
                .saleType(SaleTypeEnum.CRM.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .baseShop(TbBaseShop.builder().build())
                .build();
        Mockito.when(mallConfig.isBkc(Mockito.any())).thenReturn(true);
        // 配置规则不为空
        Mockito.when(tbPointsRepeatRuleService.getSaleRepeatCondition(Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(MemberSaleRepeatQueryDto.builder().build());
        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto
                        .builder()
                        .saleNo("")
                        .saleTypes(IntegralConstant.UNIQUE_SALE_NO_BKC)
                        .build()))
                .thenReturn(TbSalesDetail.builder().build());
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));

        // 为了覆盖 StringUtils.equals(dto.getRemark(), IntegralConstant.ALIPAY_REFUND_POINTS
        SalesAutoPointsDto dto2 = SalesAutoPointsDto.builder()
                .remark(IntegralConstant.ALIPAY_REFUND_POINTS)
                .saleType(SaleTypeEnum.CRM.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .baseShop(TbBaseShop.builder().build())
                .build();
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto2));
    }

    @Test
    @DisplayName("非bkc-ali支付-去重通过")
    void salePointsProcessCase4() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder()
                .remark(IntegralConstant.ALIPAY_POINTS)
                .saleType(SaleTypeEnum.ALIPAY.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .baseShop(TbBaseShop.builder().id("KM00000001").build())
                .build();
        Mockito.when(mallConfig.isBkc(Mockito.any())).thenReturn(false);
        // 配置规则不为空
        Mockito.when(tbPointsRepeatRuleService.getSaleRepeatCondition(Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(MemberSaleRepeatQueryDto.builder().build());
        Mockito.when(tbInsensatePointsAuthRecordService.getByAliUserId(Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));

        Mockito.when(tbInsensatePointsAuthRecordService.getByAliUserId(Mockito.any(), Mockito.any())).thenReturn(TbInsensatePointsAuthRecordDto.builder().build());
        Mockito.when(tbSetshoprateService.salesToIntegral(Mockito.any())).thenReturn(10);
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));
    }

    @Test
    @DisplayName("非bkc-微信支付-去重通过")
    void salePointsProcessCase5() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder()
                .remark(IntegralConstant.WECHAT_POINTS)
                .saleType(SaleTypeEnum.WECHAT.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .baseShop(TbBaseShop.builder().build())
                .interceptStatus(PointsInterceptStatusEnum.INTERCEPT_SUCCESS_STATUS.getType())
                .integralAdjustId("12345")
                .salesRemark("salesRemark")
                .build();
        Mockito.when(mallConfig.isBkc(Mockito.any())).thenReturn(false);
        // 配置规则为空
        Mockito.when(tbPointsRepeatRuleService.getSaleRepeatCondition(Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(null);
        Mockito.when(tbSetshoprateService.salesToIntegral(Mockito.any())).thenReturn(0);
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));

        Mockito.when(tbPointsDetailService.savePointsChangeRecord(Mockito.any())).thenThrow(new BizException(400, ""));
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));
    }

    @Test
    @DisplayName("非bkc-销售录入-非ko-去重通过")
    void salePointsProcessCase6() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder()
                .remark("XS0002")
                .saleType(SaleTypeEnum.CRM.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .baseShop(TbBaseShop.builder().build())
                .integralAdjustId("12345")
                .salesRemark("salesRemark")
                .build();
        Mockito.when(mallConfig.isBkc(Mockito.any())).thenReturn(false);
        // 配置规则为空
        Mockito.when(tbPointsRepeatRuleService.getSaleRepeatCondition(Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(MemberSaleRepeatQueryDto.builder().build());
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(false);
        Mockito.when(tbPointsInterceptApproveService.checkSaleIsIntercept(Mockito.any()))
                .thenReturn(TbInterceptResponse.builder().isIntercept(Boolean.FALSE).build());

        Mockito.when(tbSetshoprateService.salesToIntegral(Mockito.any())).thenReturn(0);
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));
    }

    @Test
    @DisplayName("非bkc-销售录入-ko-去重通过-进入拦截-拦截重复")
    void salePointsProcessCase7() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder()
                .remark("XS0002")
                .saleType(SaleTypeEnum.CRM.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .baseShop(TbBaseShop.builder().build())
                .integralAdjustId("12345")
                .salesRemark("salesRemark")
                .build();
        Mockito.when(mallConfig.isBkc(Mockito.any())).thenReturn(false);
        // 配置规则为空
        Mockito.when(tbPointsRepeatRuleService.getSaleRepeatCondition(Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(MemberSaleRepeatQueryDto.builder().build());
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(true);
        Mockito.when(tbPointsInterceptApproveService.checkSaleIsIntercept(Mockito.any()))
                .thenReturn(TbInterceptResponse.builder().isIntercept(Boolean.TRUE).build());
        Mockito.when(tbPointsInterceptApproveService.getInterceptDto(Mockito.any())).thenReturn(TbPointsIntercept.builder().build());
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));
    }

    @Test
    @DisplayName("非bkc-拍照积分-ko-去重通过-进入拦截-拦截落表")
    void salePointsProcessCase8() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder()
                .remark("XS0002")
                .saleType(SaleTypeEnum.TICKET.getValue())
                .member(TbMemberAsset.builder().kipUserId("12345").build())
                .baseShop(TbBaseShop.builder().build())
                .integralAdjustId("12345")
                .salesRemark("salesRemark")
                .build();
        Mockito.when(mallConfig.isBkc(Mockito.any())).thenReturn(false);
        // 配置规则为空
        Mockito.when(tbPointsRepeatRuleService.getSaleRepeatCondition(Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(MemberSaleRepeatQueryDto.builder().build());
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(true);
        Mockito.when(tbPointsInterceptApproveService.checkSaleIsIntercept(Mockito.any()))
                .thenReturn(TbInterceptResponse.builder().isIntercept(Boolean.TRUE).build());
        Mockito.when(tbPointsInterceptApproveService.getInterceptDto(Mockito.any())).thenReturn(null);
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.salePointsProcess(dto1));
    }

    @Test
    @DisplayName("无单退货")
    void refundSalePointsProcess() {
        SalesAutoPointsDto dto = SalesAutoPointsDto
                .builder()
                .remark(IntegralConstant.NO_ORDER_REFUND)
                .refundAmount("1")
                .member(TbMemberAsset.builder().currentPoints(100).build())
                .build();
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.refundSalePointsProcess(dto));

        SalesAutoPointsDto dto1 = SalesAutoPointsDto
                .builder()
                .remark(IntegralConstant.NO_ORDER_REFUND)
                .refundAmount("1")
                .member(TbMemberAsset.builder().currentPoints(100).build())
                .integralAdjustId("12345")
                .build();
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.refundSalePointsProcess(dto1));
    }

    @Test
    @DisplayName("有单退货-detail为空")
    void refundSalePointsProcessCase1() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto
                .builder()
                .remark(IntegralConstant.ORDER_REFUND)
                .refundAmount("1")
                .member(TbMemberAsset.builder().currentPoints(0).build())
                .refundAmount("10")
                .build();
        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(Mockito.any())).thenReturn(null);
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.refundSalePointsProcess(dto1));

        Mockito.when(tbPointsInterceptApproveService.getInterceptDto(Mockito.any()))
                .thenReturn(TbPointsIntercept.builder().payAmount(BigDecimal.ZERO).build());
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.refundSalePointsProcess(dto1));

        Mockito.when(tbPointsInterceptApproveService.getInterceptDto(Mockito.any()))
                .thenReturn(TbPointsIntercept.builder().payAmount(new BigDecimal(100)).refundAmount(BigDecimal.ONE).build());
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.refundSalePointsProcess(dto1));
    }

    @Test
    @DisplayName("有单退货-detail不合法-退款金额小于支付金额-退款积分大于会员积分")
    void refundSalePointsProcessCase2() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto
                .builder()
                .remark(IntegralConstant.ORDER_REFUND)
                .member(TbMemberAsset.builder().currentPoints(0).build())
                .refundAmount("1")
                .build();
        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(Mockito.any())).thenReturn(TbSalesDetail.builder()
                .refundAmount(BigDecimal.TEN)
                .payAmount(BigDecimal.ONE)
                .build());
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.refundSalePointsProcess(dto1));

        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(Mockito.any())).thenReturn(TbSalesDetail.builder()
                .refundAmount(BigDecimal.ZERO)
                .payAmount(BigDecimal.TEN)
                .pointsNum(10)
                .build());
        Assertions.assertThrows(Exception.class, () -> memberSalePointsProcessServiceImpl.refundSalePointsProcess(dto1));
    }

    @Test
    @DisplayName("有单退货-sale不为空-退款金额大于支付金额-退款积分大于会员积分")
    void refundSalePointsProcessCase3() {
        SalesAutoPointsDto dto1 = SalesAutoPointsDto
                .builder()
                .remark(IntegralConstant.ORDER_REFUND)
                .member(TbMemberAsset.builder().currentPoints(100).build())
                .memberSale(TbSalesDetail.builder().pointsNum(10).payAmount(BigDecimal.TEN).build())
                .amount("10")
                .refundAmount("10")
                .build();
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.refundSalePointsProcess(dto1));

        // 退回积分为0
        SalesAutoPointsDto dto2 = SalesAutoPointsDto
                .builder()
                .remark(IntegralConstant.REFUND_REWARDS_MALL)
                .member(TbMemberAsset.builder().currentPoints(100).build())
                .memberSale(TbSalesDetail.builder().pointsNum(0).payAmount(BigDecimal.TEN).build())
                .amount("10")
                .refundAmount("1")
                .build();
        Assertions.assertDoesNotThrow(() -> memberSalePointsProcessServiceImpl.refundSalePointsProcess(dto2));
    }

}