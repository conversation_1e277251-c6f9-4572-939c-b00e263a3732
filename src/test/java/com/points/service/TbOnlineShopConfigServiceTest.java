package com.points.service;

import com.kerryprops.kip.service.integral.common.enums.PointsRedemptionEnum;
import com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig;
import com.kerryprops.kip.service.integral.service.TbOnlineShopConfigService;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopResource;
import com.points.BaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 12/31/2024 08:58
 **********************************************************************************************************************/
class TbOnlineShopConfigServiceTest extends BaseTest {

    @Autowired
    private TbOnlineShopConfigService tbOnlineShopConfigService;

    @Test
    void onlineShopTotal() {
        TbOnlineShopResource resource = TbOnlineShopResource.builder().page(1).size(0).businessTypes(PointsRedemptionEnum.getCashEnumList()).groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallIdList(Collections.singletonList("8aaa80b47c784020017c78b00d060022")).build();
        Assertions.assertEquals(5, tbOnlineShopConfigService.onlineShopTotal(resource));
    }

    @Test
    void onlineShopPage() {
        TbOnlineShopResource resource = TbOnlineShopResource.builder().page(1).size(0).businessTypes(PointsRedemptionEnum.getCashEnumList()).groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallIdList(Collections.singletonList("8aaa80b47c784020017c78b00d060022")).build();
        Assertions.assertNotNull(tbOnlineShopConfigService.onlineShopPage(resource));
    }

    @Test
    void saveOrUpdateOnlineShopConfig() {
        List<TbOnlineShopConfig> list = new ArrayList<>();
        list.add(TbOnlineShopConfig
                .builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .grade("1").money(BigDecimal.valueOf(1L))
                .pointNum(BigDecimal.valueOf(1L))
                .businessType("REWARDS_MALL")
                .creator("test")
                .updater("test")
                .build());
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigService.saveOrUpdateOnlineShopConfig(list));
    }

    @Test
    void removeOnlineShopConfig() {
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigService.removeOnlineShopConfig(new Long[]{1L, 2L}));
    }

    @Test
    void getOnlineShop() {
        TbOnlineShopConfig config = TbOnlineShopConfig.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022")
                .businessType(PointsRedemptionEnum.POS_CASH_OUT.getCode()).build();
        Assertions.assertNotNull(tbOnlineShopConfigService.getOnlineShop(config));
    }

    @Test
    void findByMallIdAndGradeAndBusinessType() {
        String mallId = "8aaa80b47c784020017c78b00d060022";
        String grade = "1";
        String businessType = PointsRedemptionEnum.POS_CASH_OUT.getCode();
        Assertions.assertNotNull(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(mallId, grade, businessType));
    }

}
