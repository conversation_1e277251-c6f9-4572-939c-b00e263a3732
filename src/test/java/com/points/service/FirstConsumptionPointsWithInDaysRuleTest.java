package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.service.promotion.impl.FirstConsumptionPointsWithInDaysRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class FirstConsumptionPointsWithInDaysRuleTest {

    @InjectMocks
    private FirstConsumptionPointsWithInDaysRule firstConsumptionPointsWithInDaysRule;

    @Mock
    private TbSalesDetailService tbSalesDetailService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertNotNull(firstConsumptionPointsWithInDaysRule.getRuleType());
    }

    @Test
    void checkRule() {
        SalesAutoPointsDto dto = SalesAutoPointsDto
                .builder()
                .member(TbMemberAsset.builder().joinTime(DateUtil.offsetDay(DateUtil.date(), -10)).build())
                .build();
        TbActivityPromotionCondition condition = TbActivityPromotionCondition.builder().build();
        Assertions.assertFalse(
                firstConsumptionPointsWithInDaysRule.checkRule(condition, dto, DateUtil.date(), null)
        );

        TbActivityPromotionCondition condition1 = TbActivityPromotionCondition.builder().build();
        condition1.setPromotionConditionContent("2");
        Assertions.assertFalse(
                firstConsumptionPointsWithInDaysRule.checkRule(condition1, dto, DateUtil.date(), null)
        );

        SalesAutoPointsDto dto1 = SalesAutoPointsDto
                .builder()
                .member(TbMemberAsset.builder().joinTime(DateUtil.offsetDay(DateUtil.date(), +10)).build())
                .build();
        Mockito.when(tbSalesDetailService.queryMemberSaleCountBetweenTime(Mockito.any())).thenReturn(10);
        Assertions.assertFalse(
                firstConsumptionPointsWithInDaysRule.checkRule(condition1, dto1, DateUtil.date(), null)
        );
    }
}
