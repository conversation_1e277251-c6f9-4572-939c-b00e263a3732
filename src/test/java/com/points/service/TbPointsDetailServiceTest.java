package com.points.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.mapper.TbPointsDetailMapper;
import com.kerryprops.kip.service.integral.model.dto.MemberIntegralAdjustRecordPageQueryDto;
import com.kerryprops.kip.service.integral.model.dto.MemberPointSumQueryDto;
import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.service.SysDictService;
import com.kerryprops.kip.service.integral.service.impl.TbPointsDetailServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 03/07/2024 15:57
 **********************************************************************************************************************/
class TbPointsDetailServiceTest {

    @InjectMocks
    private TbPointsDetailServiceImpl tbPointsDetailService;

    @Mock
    private TbPointsDetailMapper tbPointsDetailMapper;
    @Mock
    private SysDictService sysDictService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private MallConfig mallConfig;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getTotalPointsOfDay() {
        when(tbPointsDetailMapper.getTotalPointsOfDay(any(), any(), anyList(), any())).thenReturn(0);
        Assertions.assertEquals(0, tbPointsDetailService.getTotalPointsOfDay("", "", null, ""));

    }

    @Test
    void savePointsChangeRecord() {
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId("4028e3817c2b3f79017c2b48c54c0000").exchangePoints(10)
                .mallId("8aaa80b47c784020017c78b00d060022").vipcode("KERRY00000000").shopId("HKC0000001").saleNo("20240307160112000000").saleDate("2024-03-07 16:01:12")
                .amount("160.00").refundAmount("").createUser("admin").saleType("1").status("0").remark("XS0030").remarkName("普通调整").refund(Boolean.FALSE).posNo("001")
                .totalAmount("160.00").salesRemark("测试备注").build();
        TbMemberAsset asset = TbMemberAsset.builder().id(10L).groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").vipcode("KERRY00000000")
                .mobile("***********").currentPoints(100).grade("1").nickName("会员用户").adjustIntegralNum(50).build();
        pointsDto.setMember(asset);
        // 保存积分变更记录
        Assertions.assertNotNull(tbPointsDetailService.savePointsChangeRecord(pointsDto));

        SalesAutoPointsDto pointsDto1 = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId("4028e3817c2b3f79017c2b48c54c0000").exchangePoints(10)
                .mallId("8aaa80b47c784020017c78b00d060022").vipcode("KERRY00000000").shopId("HKC0000001").saleNo("20240307160112000000").saleDate("2024-03-07 16:01:12")
                .amount("160.00").refundAmount("").createUser("admin").saleType("1").status("0").remark("XS0030").remarkName("普通调整").refund(Boolean.FALSE).posNo("001")
                .totalAmount("160.00").salesRemark("测试备注").build();
        TbMemberAsset asset1 = TbMemberAsset.builder().id(10L).groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").vipcode("KERRY00000000")
                .mobile("***********").currentPoints(100).grade("1").nickName("会员用户").adjustIntegralNum(-50).build();
        pointsDto1.setMember(asset1);
        // 保存积分变更记录
        Assertions.assertNotNull(tbPointsDetailService.savePointsChangeRecord(pointsDto1));

        SalesAutoPointsDto pointsDto2 = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId("4028e3817c2b3f79017c2b48c54c0000").exchangePoints(0)
                .mallId("8aaa80b47c784020017c78b00d060022").vipcode("KERRY00000000").shopId("HKC0000001").saleNo("20240307160112000000").saleDate("2024-03-07 16:01:12")
                .amount("160.00").refundAmount("").createUser("admin").saleType("1").status("0").remark("XS0030").remarkName("普通调整").refund(Boolean.FALSE).posNo("001")
                .totalAmount("160.00").salesRemark("测试备注").build();
        TbMemberAsset asset2 = TbMemberAsset.builder().id(10L).groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").vipcode("KERRY00000000")
                .mobile("***********").currentPoints(100).grade("1").nickName("会员用户").adjustIntegralNum(-50).build();
        pointsDto2.setMember(asset2);
        // 保存积分变更记录
        Assertions.assertNotNull(tbPointsDetailService.savePointsChangeRecord(pointsDto2));

        SalesAutoPointsDto pointsDto3 = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId("4028e3817c2b3f79017c2b48c54c0000").exchangePoints(10)
                .mallId("").vipcode("KERRY00000000").shopId("")
                .saleNo("").saleDate("2024-03-07 16:01:12")
                .amount("160.00").refundAmount("").createUser("admin")
                .saleType("1")
                .status("0").remark("XS0015").remarkName("").refund(Boolean.FALSE)
                .posNo("001")
                .totalAmount("160.00")
                .salesRemark("").build();
        TbMemberAsset asset3 = TbMemberAsset.builder().id(10L).groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").vipcode("KERRY00000000")
                .mobile("***********").currentPoints(100).grade("1").nickName("会员用户").adjustIntegralNum(50).build();
        pointsDto3.setMember(asset3);
        // 保存积分变更记录
        Assertions.assertNotNull(tbPointsDetailService.savePointsChangeRecord(pointsDto3));
    }

    @Test
    void savePointsAdjustRecord() {
        MemberPointsChangeDto dto = MemberPointsChangeDto
                .builder()
                .changePointsNum(100)
                .dictValue("XS0030")
                .associatedBusinessId("businessId")
                .build();
        Mockito.when(sysDictService.findByDictType(Mockito.any())).thenReturn(SysDict.builder().build());
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.savePointsAdjustRecord(dto,
                TbMemberAsset.builder().currentPoints(100).adjustIntegralNum(10).build()));

        MemberPointsChangeDto dto1 = MemberPointsChangeDto
                .builder()
                .changePointsNum(100)
                .dictValue("XS0030")
                .associatedBusinessId("businessId")
                .remark("XS0030")
                .build();
        Mockito.when(sysDictService.findByDictType(Mockito.any())).thenReturn(SysDict.builder().build());
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.savePointsAdjustRecord(dto1,
                TbMemberAsset.builder().currentPoints(100).adjustIntegralNum(-10).build()));

        MemberPointsChangeDto dto2 = MemberPointsChangeDto
                .builder()
                .changePointsNum(100)
                .dictValue("XS0030")
                .adjustPointsId("10300303")
                .mallId("10304040050505")
                .dictName("19303")
                .shopNo("hkc1003030")
                .saleNo("1003030")
                .creator("test")
                .content("content")
                .build();
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.savePointsAdjustRecord(dto2,
                TbMemberAsset.builder().currentPoints(100).adjustIntegralNum(10).build()));
    }

    @Test
    void savePointsAdjustRecordNoMessage() {
        MemberPointsChangeDto dto2 = MemberPointsChangeDto
                .builder()
                .changePointsNum(100)
                .dictValue("XS0030")
                .adjustPointsId("10300303")
                .mallId("10304040050505")
                .dictName("19303")
                .shopNo("hkc1003030")
                .saleNo("1003030")
                .creator("test")
                .content("content")
                .build();
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.savePointsAdjustRecordNoMessage(dto2,
                TbMemberAsset.builder().currentPoints(100).build()));
    }

    @Test
    void recordList() {
        Assertions.assertNotNull(tbPointsDetailService.recordList(MemberIntegralAdjustRecordPageQueryDto.builder().page(1).size(10).build()));

        Mockito.when(tbPointsDetailMapper.getList(Mockito.any())).thenReturn(Collections.singletonList(TbPointsDetail.builder().createDate(DateUtil.date()).build()));
        Assertions.assertNotNull(tbPointsDetailService.recordList(MemberIntegralAdjustRecordPageQueryDto.builder().page(1).size(10).build()));
    }

    @Test
    void getPointsList() {
        Assertions.assertNotNull(tbPointsDetailService.getPointsList("", ""));
    }

    @Test
    void getCount() {
        MemberIntegralAdjustRecordPageQueryDto queryDto = MemberIntegralAdjustRecordPageQueryDto.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY00000000").page(1).size(10).build();
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.getCount(queryDto));
    }

    @Test
    void getMonthlyIntegral() {
        Mockito.when(tbPointsDetailMapper.getMonthlyIntegral(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(Arrays.asList(TbPointsDetail.builder().moon("1").amount(10).build(),
                        TbPointsDetail.builder().moon("2").amount(-10).build()));
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.getMonthlyIntegral("", "", "", Arrays.asList("0", "1", "2")));
    }

    @Test
    void getMemberConsumePointsBetweenDate() {
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.getMemberConsumePointsBetweenDate(MemberPointSumQueryDto.builder().build()));
    }

    @Test
    void findByIds() {
        Assertions.assertNotNull(tbPointsDetailService.findByIds(null));

        Assertions.assertNotNull(tbPointsDetailService.findByIds(Arrays.asList("1", "2", "3")));

        Mockito.when(tbPointsDetailMapper.findByCrmIds(Mockito.anyList())).thenReturn(Collections.singletonList(TbPointsDetail.builder().crmId("123").createDate(DateUtil.date()).build()));
        Assertions.assertNotNull(tbPointsDetailService.findByIds(Arrays.asList("1", "2", "3")));
    }

    @Test
    void integralRecordTotal() {
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.integralRecordTotal("KERRY00000000", "8aaa80b47c784020017c78b00d060022"));
    }

    @Test
    void integralRecordList() {
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.integralRecordList("KERRY00000000", "8aaa80b47c784020017c78b00d060022", 10, 1));
    }

    @Test
    void queryAdjustList() {
        Assertions.assertDoesNotThrow(() -> tbPointsDetailService.queryAdjustList("KERRY00000000", "8aaa80b47c784020017c78b00d060022", "", "", ""));
    }

    @Test
    void queryKerryPayPointsRedemptionAmountRecord() {
        TbPointsDetail detail = tbPointsDetailService.queryKerryPayPointsRedemptionAmountRecord("4028e3817c2b3f79017c2b48c54c0000", "8aaa80b47c784020017c78b00d060022",
                "KERRY100213877", "KEP2412241534354840", "HKC00041004", "YM0120");
        assertNull(detail);
    }

    @Test
    void queryPointsRecordByConditions() {
        List<TbPointsDetail> details = tbPointsDetailService.queryPointsRecordByConditions("4028e3817c2b3f79017c2b48c54c0000", "8aaa80b47c784020017c78b00d060022",
                "KERRY100213877", "KEP2412241534354840", "HKC00041004", List.of("YM0120"));
        Assertions.assertNotNull(details);
    }

    @Test
    void checkPointsAdjustExistRecord() {
        TbPointsDetail detail = tbPointsDetailService.checkPointsAdjustExistRecord("4028e3817c2b3f79017c2b48c54c0000", "", "", "",
                10, "", "", "");
        assertNull(detail);
    }

}
