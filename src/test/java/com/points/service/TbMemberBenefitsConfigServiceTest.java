package com.points.service;

import com.kerryprops.kip.service.integral.service.TbMemberBenefitsConfigService;
import com.points.BaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 12/31/2024 09:23
 **********************************************************************************************************************/
class TbMemberBenefitsConfigServiceTest extends BaseTest {

    @Autowired
    private TbMemberBenefitsConfigService tbMemberBenefitsConfigService;

    @Test
    void findBenefitConfigByByMallIdAndGrade() {
        Assertions.assertNotNull(tbMemberBenefitsConfigService.findBenefitConfigByByMallIdAndGrade("8aaa80b47c784020017c78b00d060022", "1"));
    }

}
