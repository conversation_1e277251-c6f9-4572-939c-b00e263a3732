package com.points.service;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import com.kerryprops.kip.pmw.client.resource.QueryPointsAuthOutputResource;
import com.kerryprops.kip.pmw.client.service.BizCircleClientService;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.AuthorizeStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.common.enums.ProviderEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.mapper.TbInsensatePointsAuthRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.TbInsensatePointsAuthRecordDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TbInsensatePointsAuthRecordServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@Slf4j
class TbInsensatePointsAuthRecordServiceTest {

    @InjectMocks
    private TbInsensatePointsAuthRecordServiceImpl tbInsensatePointsAuthRecordServiceImpl;
    
    @Mock
    private TbInsensatePointsAuthRecordMapper tbInsensatePointsAuthRecordMapper;
    @Mock
    private TbAutoPointsConfigService tbAutoPointsConfigService;
    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private TbCardMemberRelationService tbCardMemberRelationService;
    @Mock
    private BizCircleClientService bizCircleClientService;
    @Mock
    private HeaderResource headerResource;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private KerryStaffService kerryStaffService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private Mapper mapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    @DisplayName("sendMq全覆盖，insertJudgment全覆盖")
    void insertJudgment() {
        Mockito.when(tbInsensatePointsAuthRecordMapper.checkExists(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyInt()))
                .thenReturn(null);
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.insertJudgment(TbInsensatePointsAuthRecord
                        .builder()
                        .origin(InsensateOriginEnum.WECHAT.getValue())
                        .build()));

        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.insertJudgment(TbInsensatePointsAuthRecord
                        .builder()
                        .origin(InsensateOriginEnum.ALIPAY.getValue())
                        .groupId("groupId")
                        .kipUserId("kipUserId")
                        .build()));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(),Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.insertJudgment(TbInsensatePointsAuthRecord
                        .builder()
                        .origin(InsensateOriginEnum.ALIPAY.getValue())
                        .groupId("groupId")
                        .kipUserId("kipUserId")
                        .build()));

        Mockito.when(tbInsensatePointsAuthRecordMapper.checkExists(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyInt()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().kipUserId("kip").cardNo("cardNo").status(AuthorizeStatusEnum.UNAUTHORIZED.getVal()).build());
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.insertJudgment(TbInsensatePointsAuthRecord
                        .builder()
                        .origin(InsensateOriginEnum.ALIPAY.getValue())
                        .groupId("groupId")
                        .kipUserId("kipUserId")
                        .build()));

    }

    @Test
    void insertAuthRecord() {
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.insertAuthRecord(TbInsensatePointsAuthRecord
                        .builder()
                        .origin(InsensateOriginEnum.ALIPAY.getValue())
                        .groupId("groupId")
                        .kipUserId("kipUserId")
                        .build()));
    }

    @Test
    void update() {
        Mockito.when(tbInsensatePointsAuthRecordMapper.updateById(Mockito.any())).thenReturn(1);
        Assertions.assertEquals(1, tbInsensatePointsAuthRecordServiceImpl.update(TbInsensatePointsAuthRecord.builder().build()));
    }

    @Test
    void getByWeChatOpenId() {
        Assertions.assertNull(tbInsensatePointsAuthRecordServiceImpl.getByWeChatOpenId("", "", ""));

        Mockito.when(tbInsensatePointsAuthRecordMapper.selectByOpenId(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().build());
        Mockito.when(mapper.map(TbInsensatePointsAuthRecord.builder().build(), TbInsensatePointsAuthRecordDto.class))
                .thenReturn(TbInsensatePointsAuthRecordDto.builder().build());
        Assertions.assertNotNull(tbInsensatePointsAuthRecordServiceImpl.getByWeChatOpenId("openId", "mallId", "groupId"));

        Mockito.when(tbInsensatePointsAuthRecordMapper.selectByOpenId(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNull(tbInsensatePointsAuthRecordServiceImpl.getByWeChatOpenId("openId", "mallId", "groupId"));
    }

    @Test
    void getByKipUserIdAndMallIdAndOrigin() {
        Mockito.when(tbInsensatePointsAuthRecordMapper.getByKipUserIdAndMallIdAndOrigin(Mockito.any(), Mockito.any(), Mockito.anyInt()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().build());
        Assertions.assertNotNull(tbInsensatePointsAuthRecordServiceImpl.getByKipUserIdAndMallIdAndOrigin( "mallId", "groupId", 1));
    }

    @Test
    void queryByOpenIdAndMallId() {
        Mockito.when(tbInsensatePointsAuthRecordMapper.getByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().build());
        Assertions.assertNotNull(tbInsensatePointsAuthRecordServiceImpl.queryByOpenIdAndMallId( "mallId", "groupId"));
    }

    @Test
    void checkHkcMemberAuthStatus() {
        assertDoesNotThrow(() -> tbInsensatePointsAuthRecordServiceImpl.checkHkcMemberAuthStatus("1111", "111", 0));
    }

    @Test
    void getByAliUserId() {
        Mockito.when(tbInsensatePointsAuthRecordMapper.selectByOpenId(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().build());
        Mockito.when(mapper.map(TbInsensatePointsAuthRecord.builder().build(), TbInsensatePointsAuthRecordDto.class))
                .thenReturn(TbInsensatePointsAuthRecordDto.builder().build());
        Assertions.assertNotNull(tbInsensatePointsAuthRecordServiceImpl.getByAliUserId( "mallId", "groupId"));

        Mockito.when(tbInsensatePointsAuthRecordMapper.selectByOpenId(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(null);
        Assertions.assertNull(tbInsensatePointsAuthRecordServiceImpl.getByAliUserId( "mallId", "groupId"));
    }

    @Test
    void getAutoPointInfo() {
        LoginUser loginUser = LoginUser.builder().build();
        Mockito.when(tbAutoPointsConfigService.findByLbsId(Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbInsensatePointsAuthRecordServiceImpl.getAutoPointInfo( loginUser, "groupId", ""));

        Mockito.when(tbAutoPointsConfigService.findByLbsId(Mockito.any()))
                .thenReturn(TbAutoPointsConfig.builder().code("wxpay").build());
        Assertions.assertNotNull(tbInsensatePointsAuthRecordServiceImpl.getAutoPointInfo( loginUser, "groupId", ""));

        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any()))
                .thenReturn(MallItem.builder().build());
        Assertions.assertNotNull(tbInsensatePointsAuthRecordServiceImpl.getAutoPointInfo( loginUser, ProviderEnum.ALI.getVal(), ""));

        Mockito.when(tbInsensatePointsAuthRecordMapper.selectByKipUserIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(TbInsensatePointsAuthRecord.builder()
                                .origin(InsensateOriginEnum.ALIPAY.getValue())
                                .status(AuthorizeStatusEnum.UNAUTHORIZED.getVal())
                                .build(),
                        TbInsensatePointsAuthRecord.builder()
                                .origin(InsensateOriginEnum.WECHAT.getValue())
                                .status(AuthorizeStatusEnum.UNAUTHORIZED.getVal())
                                .build()));
        Assertions.assertNotNull(tbInsensatePointsAuthRecordServiceImpl.getAutoPointInfo( loginUser, ProviderEnum.ALI.getVal(), ""));
    }

    @Test
    void getBatchData() {
        Mockito.when(tbInsensatePointsAuthRecordMapper.getBatchData(Mockito.anyLong(), Mockito.anyInt())).thenReturn(null);
        Assertions.assertNull(tbInsensatePointsAuthRecordServiceImpl.getBatchData(1L, 1));
    }

    @Test
    void getMallBatchData() {
        Mockito.when(tbInsensatePointsAuthRecordMapper.getMallBatchData(Mockito.anyString(), Mockito.anyLong(), Mockito.anyInt())).thenReturn(null);
        Assertions.assertNull(tbInsensatePointsAuthRecordServiceImpl.getMallBatchData("",1L, 1));
    }

    @Test
    void checkAndSaveAliMemberOpenCard() {
        Mockito.when(kerryStaffService.findByMobile(Mockito.any())).thenReturn(null);
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.checkAndSaveAliMemberOpenCard(TbInsensatePointsAuthRecord
                        .builder()
                        .origin(InsensateOriginEnum.ALIPAY.getValue())
                        .groupId("groupId")
                        .kipUserId("kipUserId")
                        .build()));

        Mockito.when(kerryStaffService.findByMobile(Mockito.any())).thenReturn(CustomerUserDto.builder().build());
        Mockito.when(tbInsensatePointsAuthRecordMapper.checkExists(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyInt()))
                .thenReturn(null);
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.checkAndSaveAliMemberOpenCard(TbInsensatePointsAuthRecord
                        .builder()
                        .origin(InsensateOriginEnum.ALIPAY.getValue())
                        .groupId("groupId")
                        .kipUserId("kipUserId")
                        .build()));

        Mockito.when(tbInsensatePointsAuthRecordMapper.checkExists(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyInt()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().kipUserId("kip").cardNo("").status(AuthorizeStatusEnum.UNAUTHORIZED.getVal()).build());
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.checkAndSaveAliMemberOpenCard(TbInsensatePointsAuthRecord
                        .builder()
                        .origin(InsensateOriginEnum.ALIPAY.getValue())
                        .groupId("groupId")
                        .kipUserId("kipUserId")
                        .build()));
    }

    @Test
    void saveAuthRecord() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(tbCardMemberRelationService.getByGroupIdAndMobileAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.saveAuthRecord("", ""));

        Mockito.when(tbCardMemberRelationService.getByGroupIdAndMobileAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbCardMemberRelation.builder().build());
        Mockito.when(tbInsensatePointsAuthRecordMapper.checkExists(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyInt()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().build());
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.saveAuthRecord("", ""));

        Mockito.when(tbInsensatePointsAuthRecordMapper.checkExists(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyInt()))
                .thenReturn(null);
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.saveAuthRecord("", ""));

        Mockito.when(memberRegisterService.queryByGroupIdAndMobile(Mockito.any(),Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        assertDoesNotThrow(() ->
                tbInsensatePointsAuthRecordServiceImpl.saveAuthRecord("", ""));
    }

    @Test
    void getByOpenIdAndMallId() {
        Mockito.when(tbInsensatePointsAuthRecordMapper.getByOpenIdAndMallId(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Assertions.assertNull(tbInsensatePointsAuthRecordServiceImpl.getByOpenIdAndMallId("",""));
    }

    @Test
    void insertBatchFromCardMemberRelation() {
        assertDoesNotThrow(() ->tbInsensatePointsAuthRecordServiceImpl.insertBatchFromCardMemberRelation());
    }

    @Test
    void getByCardNoAndOrigin() {
        Mockito.when(tbInsensatePointsAuthRecordMapper.getByCardNoAndOrigin(Mockito.anyString(), Mockito.anyInt())).thenReturn(null);
        Assertions.assertNull(tbInsensatePointsAuthRecordServiceImpl.getByCardNoAndOrigin("",1));
    }

    @Test
    void checkAuthStatus() {
        Mockito.when(bizCircleClientService.queryPointsAuth(Mockito.any()))
                .thenThrow(new BizException(400, ""));
        assertDoesNotThrow(() ->tbInsensatePointsAuthRecordServiceImpl
                .checkAuthStatus(TbInsensatePointsAuthRecord.builder().status(AuthorizeStatusEnum.AUTHORIZED.getVal()).build(), MallItem.builder().build()));
    }

    @Test
    void checkAuthStatusCase1() {
        Mockito.when(bizCircleClientService.queryPointsAuth(Mockito.any()))
                .thenReturn(null);
        assertDoesNotThrow(() ->tbInsensatePointsAuthRecordServiceImpl
                .checkAuthStatus(TbInsensatePointsAuthRecord.builder().status(AuthorizeStatusEnum.UNAUTHORIZED.getVal()).build(), MallItem.builder().build()));

        QueryPointsAuthOutputResource.QueryPointsAuthOutputBodyResource respBody = new QueryPointsAuthOutputResource.QueryPointsAuthOutputBodyResource();
        QueryPointsAuthOutputResource outputResource = new QueryPointsAuthOutputResource(null, respBody, "");
        Mockito.when(bizCircleClientService.queryPointsAuth(Mockito.any()))
                .thenReturn(outputResource);
        assertDoesNotThrow(() ->tbInsensatePointsAuthRecordServiceImpl
                .checkAuthStatus(TbInsensatePointsAuthRecord.builder().status(AuthorizeStatusEnum.AUTHORIZED.getVal()).build(), MallItem.builder().build()));

        QueryPointsAuthOutputResource.QueryPointsAuthOutputBodyResource respBody1 = new QueryPointsAuthOutputResource.QueryPointsAuthOutputBodyResource();
        respBody1.setAuthorizeState("UNAUTHORIZED");
        QueryPointsAuthOutputResource outputResource1 = new QueryPointsAuthOutputResource(null, respBody1, "");
        Mockito.when(bizCircleClientService.queryPointsAuth(Mockito.any()))
                .thenReturn(outputResource1);
        assertDoesNotThrow(() ->tbInsensatePointsAuthRecordServiceImpl
                .checkAuthStatus(TbInsensatePointsAuthRecord.builder().status(AuthorizeStatusEnum.AUTHORIZED.getVal()).build(), MallItem.builder().build()));

    }

    @Test
    void recheckWxAuthRecordByKipUserId() {
        assertDoesNotThrow(() ->tbInsensatePointsAuthRecordServiceImpl.recheckWxAuthRecordByKipUserId("111111"));
    }

    @Test
    void recheckWxAuthRecordByKipUserId1() {
        TbInsensatePointsAuthRecord record1 = TbInsensatePointsAuthRecord.builder().mallId("11111").build();
        TbInsensatePointsAuthRecord record2 = TbInsensatePointsAuthRecord.builder().mallId("22222").build();
        Mockito.when(mallConfig.getByMallId("22222")).thenReturn(MallItem.builder().mallId("22222").build());
        Mockito.when(tbInsensatePointsAuthRecordMapper.findWxAuthRecordByKipUserId(Mockito.anyString()))
               .thenReturn(Arrays.asList(record1, record2));
        assertDoesNotThrow(() ->tbInsensatePointsAuthRecordServiceImpl.recheckWxAuthRecordByKipUserId("111111"));
    }



}