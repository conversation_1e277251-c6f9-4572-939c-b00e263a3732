package com.points.service;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.entity.SelfDefiningPage;
import com.kerryprops.kip.service.integral.service.SelfDefiningPageService;
import com.kerryprops.kip.service.integral.service.impl.ZIndexServiceImpl;
import com.kerryprops.kip.service.integral.webservice.response.SelfDefiningModuleResponse;
import com.kerryprops.kip.service.integral.webservice.response.ZIndexListResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> nancy
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/
class ZIndexServiceServiceTest {

    @InjectMocks
    private ZIndexServiceImpl zIndexServiceImpl;

    @Mock
    private Mapper mapper;
    @Mock
    private SelfDefiningPageService selfDefiningPageService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void setModulesForCustomize() {
        ZIndexListResponse response = new ZIndexListResponse();
        Assertions.assertDoesNotThrow(() -> zIndexServiceImpl.setModulesForCustomize(response, ""));
        Assertions.assertDoesNotThrow(() -> zIndexServiceImpl.setModulesForCustomize(response, "[{\"id\":\"1433318383717593089\",\"listId\":\"1430412704095772674\",\"moduleSign\":\"my\",\"sort\":1,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"waterfallsFlowList\":null}]"));
        Assertions.assertDoesNotThrow(() -> zIndexServiceImpl.setModulesForCustomize(response, "[{\"id\":\"1433318383717593089\",\"listId\":\"1430412704095772674\",\"moduleSign\":\"waterfall\",\"sort\":1,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"waterfallsFlowList\":null}]"));
    }

    @Test
    void getHomePageByGroupIdAndMallId() {
        Mockito.when(selfDefiningPageService.getZIndex(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> zIndexServiceImpl.getHomePageByGroupIdAndMallId("", ""));
        SelfDefiningPage selfDefiningPage = new SelfDefiningPage();
        Mockito.when(selfDefiningPageService.getZIndex(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(selfDefiningPage);
        Mockito.when(mapper.map(selfDefiningPage, ZIndexListResponse.class)).thenReturn(new ZIndexListResponse());
        Assertions.assertDoesNotThrow(() -> zIndexServiceImpl.getHomePageByGroupIdAndMallId("", ""));
    }

    @Test
    void getMyPageByGroupIdAndMallId() {
        Mockito.when(selfDefiningPageService.getZIndex(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertNull(zIndexServiceImpl.getMyPageByGroupIdAndMallId("", ""));

        SelfDefiningPage selfDefiningPage = new SelfDefiningPage();
        Mockito.when(selfDefiningPageService.getZIndex(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(selfDefiningPage);
        Mockito.when(mapper.map(selfDefiningPage, ZIndexListResponse.class)).thenReturn(new ZIndexListResponse());
        Assertions.assertNull(zIndexServiceImpl.getMyPageByGroupIdAndMallId("", ""));

        SelfDefiningPage selfDefiningPage1 = new SelfDefiningPage();
        selfDefiningPage1.setModuleContext("[{\"id\":\"1433318383717593089\",\"listId\":\"1430412704095772674\",\"moduleSign\":\"my\",\"sort\":1,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"waterfallsFlowList\":null}]");
        Mockito.when(selfDefiningPageService.getZIndex("", "", "2"))
                .thenReturn(selfDefiningPage1);
        Mockito.when(mapper.map(selfDefiningPage1, ZIndexListResponse.class)).thenReturn(new ZIndexListResponse());

        SelfDefiningPage selfDefiningPage2 = new SelfDefiningPage();
        selfDefiningPage2.setModuleContext("[{\"id\":\"1433318383717593089\",\"listId\":\"1430412704095772674\",\"moduleSign\":\"navigation\",\"sort\":1,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"waterfallsFlowList\":null}]");
        selfDefiningPage2.setFloatingBtnShow(1);
        Mockito.when(selfDefiningPageService.getZIndex("", "", "0"))
                .thenReturn(selfDefiningPage2);
        Assertions.assertNotNull(zIndexServiceImpl.getMyPageByGroupIdAndMallId("", ""));
    }

    @Test
    void getHomePageDisplayInMyPageModules() {
        Mockito.when(selfDefiningPageService.getZIndex(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new SelfDefiningPage());
        Assertions.assertNull(zIndexServiceImpl.getHomePageDisplayInMyPageModules("", "", new SelfDefiningModuleResponse()));

        SelfDefiningPage selfDefiningPage = new SelfDefiningPage();
        Mockito.when(selfDefiningPageService.getZIndex(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(selfDefiningPage);
        Mockito.when(mapper.map(selfDefiningPage, ZIndexListResponse.class)).thenReturn(new ZIndexListResponse());
        Assertions.assertNull(zIndexServiceImpl.getHomePageDisplayInMyPageModules("", "", new SelfDefiningModuleResponse()));

        SelfDefiningPage selfDefiningPage1 = new SelfDefiningPage();
        selfDefiningPage1.setModuleContext("[{\"id\":\"1433318383717593089\",\"listId\":\"1430412704095772674\",\"moduleSign\":\"navigation\",\"sort\":1,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"waterfallsFlowList\":null}]");
        selfDefiningPage1.setFloatingBtnShow(1);
        Mockito.when(selfDefiningPageService.getZIndex(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(selfDefiningPage1);
        Mockito.when(mapper.map(selfDefiningPage1, ZIndexListResponse.class)).thenReturn(new ZIndexListResponse());
        Assertions.assertNotNull(zIndexServiceImpl.getHomePageDisplayInMyPageModules("", "", new SelfDefiningModuleResponse()));
    }

    @Test
    void getPageById() {
        Mockito.when(selfDefiningPageService.getPageById(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertNull(zIndexServiceImpl.getPageById("", "", ""));

        SelfDefiningPage selfDefiningPage1 = new SelfDefiningPage();
        selfDefiningPage1.setModuleContext("[{\"id\":\"1433318383717593089\",\"listId\":\"1430412704095772674\",\"moduleSign\":\"navigation\",\"sort\":1,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"waterfallsFlowList\":null}]");
        selfDefiningPage1.setFloatingBtnShow(1);
        Mockito.when(selfDefiningPageService.getPageById(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(selfDefiningPage1);
        Mockito.when(mapper.map(selfDefiningPage1, ZIndexListResponse.class)).thenReturn(new ZIndexListResponse());
        Assertions.assertNotNull(zIndexServiceImpl.getPageById("", "", ""));
    }

}
