package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbMemberEquity;
import com.kerryprops.kip.service.integral.service.TbMemberEquityService;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 11/08/2024 11:22
 **********************************************************************************************************************/

@Slf4j
class TbMemberEquityServiceTest extends BaseTest {

    @Autowired
    private TbMemberEquityService tbMemberEquityService;

    @Test
    void findByMallIdAndType() {
        String mallId = "8aaa81cb7c836c6b017c83e46b110001";
        String groupId = "8aaa82ea804d07cd01805174dd3b000c";
        String type = "1,10,11,12";
        List<TbMemberEquity> equities = tbMemberEquityService.findByMallIdAndType(groupId, mallId, type);
        Assertions.assertNotNull(equities);
    }

}
