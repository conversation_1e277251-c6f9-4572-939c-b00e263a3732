package com.points.service;

import com.kerryprops.kip.service.integral.mapper.TicketMemberGradeMapper;
import com.kerryprops.kip.service.integral.model.dto.TicketMemberGradeDto;
import com.kerryprops.kip.service.integral.service.impl.TicketMemberGradeServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> nancy
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/
class TicketMemberGradeServiceTest {

    @InjectMocks
    private TicketMemberGradeServiceImpl ticketMemberGradeServiceImpl;

    @Mock
    private TicketMemberGradeMapper ticketMemberGradeMapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getTicketInfo() {
        Assertions.assertDoesNotThrow(() -> ticketMemberGradeServiceImpl.getTicketInfo(Collections.emptyList(), ""));

        Mockito.when(ticketMemberGradeMapper.findList(Mockito.anyList(), Mockito.any())).thenReturn(Collections.singletonList(TicketMemberGradeDto.builder().ticketId("12").build()));
        Assertions.assertDoesNotThrow(() -> ticketMemberGradeServiceImpl.getTicketInfo(Collections.singletonList("123"), ""));
    }

}
