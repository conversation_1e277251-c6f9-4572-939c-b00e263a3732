package com.points.service;

import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbPointsInterceptApproveService;
import com.kerryprops.kip.service.integral.webservice.response.TbInterceptResponse;
import com.points.BaseTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/
class TbPointsInterceptConfigServiceTest extends BaseTest {

    @Resource
    private TbPointsInterceptApproveService tbPointsInterceptApproveService;

    @Test
    void checkSaleIsIntercept() {
        SalesAutoPointsDto dto = SalesAutoPointsDto
                .builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .vipcode("KERRY00000000")
                .shopId("HKC0000001")
                .build();
        Assertions.assertDoesNotThrow(() -> tbPointsInterceptApproveService.checkSaleIsIntercept(dto));
    }
}
