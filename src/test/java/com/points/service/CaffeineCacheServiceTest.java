package com.points.service;


import com.kerryprops.kip.service.integral.client.BadgeServiceClient;
import com.kerryprops.kip.service.integral.common.enums.CustomizePageTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.TbMemberGradeCacheDto;
import com.kerryprops.kip.service.integral.service.CaffeineCacheService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.service.ZIndexService;
import com.kerryprops.kip.service.integral.webservice.response.ZIndexListResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - bert
 * Created Date - 01/13/2025 10:22
 **********************************************************************************************************************/

@Slf4j
class CaffeineCacheServiceTest {

    @InjectMocks
    private CaffeineCacheService caffeineCacheService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private ZIndexService zindexService;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private BadgeServiceClient badgeServiceClient;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getGradeList() {
        String gradeJson = "[{\"id\":\"159cb3a50aa842319d181cfb5d3d38e6\",\"membergrade\":\"1\",\"desc\":\"银卡\",\"sort\":1,\"description\":\"\",\"remark\":\"\",\"groupId\":\"8a8884e77cc9e70a017cca1011970001\",\"cardCoverUrl\":\"\",\"cardCoverHomeUrl\":\"\"},{\"id\":\"0facdc2c12334ee4b25613087f78047e\",\"membergrade\":\"4\",\"desc\":\"臻选银卡\",\"sort\":2,\"description\":\"<p><sub><strong>申请条件</strong>:</sub></p><p><sub style=\\\"color: black; --darkreader-inline-color:#e8e6e3;\\\" data-darkreader-inline-color=\\\"\\\">1，除服务类，汽车消费外：当日于商场累积消费满人民币600元，或者连续12个月内于商场累积消费满人民币3,000元</sub></p><p><sub style=\\\"color: black; --darkreader-inline-color:#e8e6e3;\\\" data-darkreader-inline-color=\\\"\\\">2，服务类或汽车消费：单日消费满人民币3,000元</sub></p><p><sub><strong>续卡条件</strong>:  </sub></p><p><sub style=\\\"color: black; --darkreader-inline-color:#e8e6e3;\\\" data-darkreader-inline-color=\\\"\\\">除服务类，汽车消费外：自成为臻选银卡会员之日起的12个月内于商场累积消费满人民币3,000元</sub></p><p><br></p><p><br></p>\",\"remark\":\"甄选银卡\",\"groupId\":\"8a8884e77cc9e70a017cca1011970001\",\"cardCoverUrl\":\"/member/img/ffb77048ceaf405b92a502a374aa98ba.png\",\"cardCoverHomeUrl\":\"/member/img/a6001826b0b84f61a1cc43fc410de77a.jpg\"},{\"id\":\"b3450aa848ce48eaadac5baaff05060d\",\"membergrade\":\"2\",\"desc\":\"金卡1\",\"sort\":3,\"description\":\"<p><sub><strong>申请条件</strong>:</sub></p><p><sub style=\\\"color: rgb(0, 0, 0);\\\">1，除服务类，汽车消费外：当日于商场累积消费满人民币5,000元，或者连续12个月内于商场累积消费满人民币30,000元</sub></p><p><sub style=\\\"color: black;\\\">2，服务类或汽车消费：单日消费人民币满25,000元</sub></p><p><sub style=\\\"color: black;\\\">3，</sub><sub style=\\\"color: rgb(0, 0, 0);\\\">优享升级：首次入会当日非除服务类、汽车之外消费满3,000元。并且首次入会当日服务类和汽车消费累计消费3,000元 </sub></p><p><sub><strong>续卡条件</strong>:</sub>  </p><p><sub style=\\\"color: black;\\\">除服务类，汽车消费外：自成为金卡会员之日起的12个月内于商场累积消费满人民币30,000元</sub></p><p><br></p><p class=\\\"ql-align-center\\\"><span style=\\\"color: black;\\\">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></p><p class=\\\"ql-align-center\\\">  </p><p><br></p>\",\"remark\":\"金卡\",\"groupId\":\"8a8884e77cc9e70a017cca1011970001\",\"cardCoverUrl\":\"/member/img/b549e536b3134a3a843071e9e1039beb.png\",\"cardCoverHomeUrl\":\"/member/img/7500cc6443e44e689daa1d212a696eff.jpg\"},{\"id\":\"89893a1cf5c6492ab664ede6072b40b3\",\"membergrade\":\"3\",\"desc\":\"铂金卡\",\"sort\":4,\"description\":\"\",\"remark\":\"\",\"groupId\":\"8a8884e77cc9e70a017cca1011970001\",\"cardCoverUrl\":\"/member/img/034974eb389b47fba9d6f1117d3481ed.png\",\"cardCoverHomeUrl\":\"/member/img/9884cf08acba4d298b70382ab8fc4e0f.png\"}]";
        List<TbMemberGrade> grades = JsonUtils.stringToList(gradeJson, TbMemberGrade.class);
        caffeineCacheService.init();
        List<TbMemberGrade> list = caffeineCacheService.getGradeList("8a8884e77cc9e70a017cca1011970001");
        Assertions.assertEquals(list.size(), 0);

        when(tbMemberGradeService.queryGradeSortAscWithoutCacheByGroupId(any())).thenReturn(grades);
        list = caffeineCacheService.getGradeList("8a8884e77cc9e70a017cca1011970001");
        Assertions.assertEquals(list.size(), 4);
    }

    @Test
    void getCacheVal() {
        Mockito.when(zindexService.getHomePageByGroupIdAndMallId(Mockito.any(), Mockito.any())).thenReturn(new ZIndexListResponse());
        Assertions.assertDoesNotThrow(() ->caffeineCacheService.getCacheVal("123", "124", CustomizePageTypeEnum.HOME_PAGE.getValue()));

        Assertions.assertDoesNotThrow(() ->caffeineCacheService.getCacheVal("123", "124", CustomizePageTypeEnum.MY_PAGE.getValue()));
    }

    @Test
    void asyncGetHomePage() {
        caffeineCacheService.init();
        Assertions.assertNotNull(caffeineCacheService.asyncGetHomePage("123", "123", "123"));
    }

    @Test
    void asyncGetMyPage() {
        caffeineCacheService.init();
        Assertions.assertNotNull(caffeineCacheService.asyncGetMyPage("123", "123", "123"));
    }

    @Test
    void asyncGetMyBadge() {
        caffeineCacheService.init();
        Assertions.assertNotNull(caffeineCacheService.asyncGetMyBadge("123", "123", "123"));
    }

    @Test
    void getGradeListCase1() {
        Mockito.when(tbMemberGradeService.getGradeList((Mockito.any()))).thenReturn(TbMemberGradeCacheDto.builder().list(Collections.singletonList(TbMemberGrade.builder().groupId("123").build())).build());
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        caffeineCacheService.init();
        Assertions.assertDoesNotThrow(() ->caffeineCacheService.getGradeList("123"));
    }

}