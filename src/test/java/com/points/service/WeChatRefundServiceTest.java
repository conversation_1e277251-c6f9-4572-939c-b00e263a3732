package com.points.service;

import com.kerryprops.kip.pmw.client.resource.*;
import com.kerryprops.kip.pmw.client.service.BizCircleClientService;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.EventTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.WechatAccelerateIntegralServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationEventPublisher;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
@Slf4j
class WeChatRefundServiceTest {

    @InjectMocks
    private WechatAccelerateIntegralServiceImpl wechatAccelerateIntegralServiceImpl;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private ApplicationEventPublisher applicationEventPublisher;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    @Mock
    private TbPointsDetailService tbPointsDetailService;
    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;
    @Mock
    private BizCircleClientService bizCircleClientService;
    @Mock
    private HeaderResource headerResource;
    @Mock
    private TbInsensatePointsPushRecordService tbInsensatePointsPushRecordService;
    @Mock
    private RedisService redisService;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private TbMemberCardOpenRecordService tbMemberCardOpenRecordService;
    @Mock
    private TbAutoPointsConfigService tbAutoPointsConfigService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void refundCallback() {
        PointsRefundConfirmResource.PointsRefundConfirmBodyResource body = new PointsRefundConfirmResource.PointsRefundConfirmBodyResource();
        body.setEventType(EventTypeEnum.REFUND_SUCC.getName());
        PointsRefundConfirmResource.PointsRefundDetailResource detailResource = new PointsRefundConfirmResource.PointsRefundDetailResource();
        detailResource.setOpenId("openId");
        detailResource.setMchId("mchId");
        detailResource.setShopNumber("shopNumber");
        detailResource.setTransactionId("transactionId");
        detailResource.setPayAmount(0);
        detailResource.setRefundAmount(100);
        body.setResource(detailResource);
        PointsRefundConfirmResource resource = new PointsRefundConfirmResource(null, body, "");

        Mockito.when(mallConfig.getByMchId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(memberSalePointsProcessService.refundSalePointsProcess(Mockito.any())).thenReturn(0);
        int refundPoints = wechatAccelerateIntegralServiceImpl.refundCallback(resource);
        assertEquals(0, refundPoints);
    }

    @Test
    void payCallbackCase1() {
        PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource body = new PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource();
        body.setEventType(EventTypeEnum.REFUND_SUCC.getName());
        PointsPaymentConfirmResource.PointsPaymentDetailResource detailResource = new PointsPaymentConfirmResource.PointsPaymentDetailResource();
        detailResource.setOpenId("openId");
        detailResource.setMchId("mchId");
        detailResource.setShopNumber("shopNumber");
        detailResource.setTransactionId("transactionId");
        detailResource.setAmount(100);
        body.setResource(detailResource);
        PointsPaymentConfirmResource resource = new PointsPaymentConfirmResource(null, body, "");
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.payCallback(resource));
    }

    @Test
    void payCallbackCase2() {
        PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource body = new PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource();
        body.setEventType(EventTypeEnum.TRANS_SUCC.getName());
        PointsPaymentConfirmResource.PointsPaymentDetailResource detailResource = new PointsPaymentConfirmResource.PointsPaymentDetailResource();
        detailResource.setOpenId("openId");
        detailResource.setMchId("mchId");
        detailResource.setShopNumber("shopNumber");
        detailResource.setTransactionId("transactionId");
        detailResource.setAmount(100);
        body.setResource(detailResource);
        PointsPaymentConfirmResource resource = new PointsPaymentConfirmResource(null, body, "");

        Mockito.when(mallConfig.getByMchId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(TbBaseShop.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Assertions.assertThrows(IllegalArgumentException.class, () -> wechatAccelerateIntegralServiceImpl.payCallback(resource));
    }

    @Test
    void memberPointsAuth() {
        PointsActivationConfirmResource.PointsActivationConfirmBodyResource body = new PointsActivationConfirmResource.PointsActivationConfirmBodyResource();
        body.setEventType(EventTypeEnum.TRANS_SUCC.getName());
        PointsActivationConfirmResource.PointsActivationDetailResource detailResource = new PointsActivationConfirmResource.PointsActivationDetailResource();
        detailResource.setOpenId("openId");
        detailResource.setMchId("mchId");
        body.setResource(detailResource);
        PointsActivationConfirmResource resource = new PointsActivationConfirmResource(null, body, "");
        Mockito.when(mallConfig.getByMchId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        // 获取event报错
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.memberPointsAuth(resource));
    }

    @Test
    void memberCardOpenEvent() {
        PointsActivationConfirmResource.PointsActivationConfirmBodyResource body = new PointsActivationConfirmResource.PointsActivationConfirmBodyResource();
        PointsActivationConfirmResource.PointsActivationDetailResource detailResource = new PointsActivationConfirmResource.PointsActivationDetailResource();
        body.setResource(detailResource);
        PointsActivationConfirmResource resource = new PointsActivationConfirmResource(null, body, "");
        Mockito.when(tbInsensatePointsAuthRecordService.getByCardNoAndOrigin(Mockito.any(), Mockito.any())).thenReturn(TbInsensatePointsAuthRecord.builder().build());
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.memberCardOpenEvent(resource));
    }

    @Test
    void syncAliPay() {
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.syncAliPay());
    }

    // outputResource.getBody()为空
    @Test
    void syncWechatPointsCase1() {
        String transactionStr = "{\"mchId\":10247801,\"merchantName\": \"HKC\",\"shopName\": \"HKC0001\"," +
                "\"appId\":null,\"openId\":\"weChat001\",\"amount\":\"10\"," +
                "\"description\":\"微信支付订单号\"}";
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().saleNo("001").build();
        Mockito.when(tbInsensatePointsPushRecordService.findByTransactionIdAndEventTypeAndOrigin("001", EventTypeEnum.TRANS_SUCC.getValue(), InsensateOriginEnum.WECHAT.getValue()))
                .thenReturn(InsensatePointsPushRecordDto.builder().transactionInfo(transactionStr).build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(tbSalesDetailService.selectBySellNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(TbSalesDetail.builder().build());
        Mockito.when(tbPointsDetailService.getTotalPointsOfDay(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(10);
        Mockito.when(bizCircleClientService.earnRewardPoints(Mockito.any())).thenReturn(new EarnRewardPointsOutputResource(null, null, ""));
        Mockito.when(redisService.setRetryCount(Mockito.any())).thenReturn(1L);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.syncWechatPoints(dto));
    }

    // outputResource.getBody()不为空
    @Test
    void syncWechatPointsCase2() {
        String transactionStr = "{\"mchId\":10247801,\"merchantName\": \"HKC\",\"shopName\": \"HKC0001\"," +
                "\"appId\":null,\"openId\":\"weChat001\",\"amount\":\"10\"," +
                "\"description\":\"微信支付订单号\"}";
        EarnRewardPointsOutputResource.EarnRewardPointsResultBodyResource resource = new EarnRewardPointsOutputResource.EarnRewardPointsResultBodyResource();
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().saleNo("001").build();
        Mockito.when(tbInsensatePointsPushRecordService.findByTransactionIdAndEventTypeAndOrigin("001", EventTypeEnum.TRANS_SUCC.getValue(), InsensateOriginEnum.WECHAT.getValue()))
                .thenReturn(InsensatePointsPushRecordDto.builder().transactionInfo(transactionStr).build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(tbSalesDetailService.selectBySellNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(TbSalesDetail.builder().build());
        Mockito.when(tbPointsDetailService.getTotalPointsOfDay(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(10);
        Mockito.when(bizCircleClientService.earnRewardPoints(Mockito.any())).thenReturn(new EarnRewardPointsOutputResource(null, resource, ""));
        Mockito.when(redisService.setRetryCount(Mockito.any())).thenReturn(1L);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.syncWechatPoints(dto));
    }

    @Test
    void queryMemberPointsCommitStatus() {
        LoginUser loginUser = new LoginUser();
        loginUser.setLbsId("8a8883557cca9463017ccb002b360001");
        loginUser.setOpenId("2024131091");
        QueryPointsCommitStatusOutputResource.QueryPointsCommitStatusBodyResource resource = new QueryPointsCommitStatusOutputResource.QueryPointsCommitStatusBodyResource();
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().wxBrandId(103).build());
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(Boolean.TRUE);
        Mockito.when(tbAutoPointsConfigService.findByLbsId(Mockito.any())).thenReturn(TbAutoPointsConfig.builder().code("wxpay").build());
        Mockito.when(tbInsensatePointsAuthRecordService.getByKipUserIdAndMallIdAndOrigin(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().openId("2024131091").build());
        Mockito.when(bizCircleClientService.queryPointsCommitStatus(Mockito.any())).thenReturn(new QueryPointsCommitStatusOutputResource(null, resource, ""));
        Assertions.assertNotNull(wechatAccelerateIntegralServiceImpl.queryMemberPointsCommitStatus(loginUser));
    }


}
