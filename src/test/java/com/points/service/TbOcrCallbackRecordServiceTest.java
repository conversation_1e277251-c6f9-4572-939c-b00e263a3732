package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbOcrCallbackRecord;
import com.kerryprops.kip.service.integral.service.TbOcrCallbackRecordService;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 08/12/2024 14:04
 **********************************************************************************************************************/
@Slf4j
class TbOcrCallbackRecordServiceTest extends BaseTest {

    @Autowired
    private TbOcrCallbackRecordService tbOcrCallbackRecordService;

    @Test
    void saveRecord() {
        String taskId = "Test";
        Long photoId = 10L;
        Assertions.assertDoesNotThrow(() -> tbOcrCallbackRecordService.saveRecord(taskId, photoId));
    }

    @Test
    void findByTaskId() {
        TbOcrCallbackRecord record = tbOcrCallbackRecordService.findByTaskId("240802_857af15d552148a3aabec151e78233ec_uat");
        Assertions.assertNotNull(record);
    }

    @Test
    void findByPhotoIds() throws ExecutionException, InterruptedException {
        CompletableFuture<Map<Long, String>> future = tbOcrCallbackRecordService.findByPhotoIds(Arrays.asList(66L, 67L));
        CompletableFuture.allOf(future).join();
        Assertions.assertNotNull(future.get());
    }

}
