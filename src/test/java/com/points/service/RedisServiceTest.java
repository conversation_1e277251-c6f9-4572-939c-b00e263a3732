package com.points.service;

import com.kerryprops.kip.service.integral.service.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.redis.core.*;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 15:49
 **********************************************************************************************************************/

@Slf4j
class RedisServiceTest {
    @Mock
    RedisTemplate<String, String> redisTemplate;

    @Mock
    ValueOperations<String,String> valueOperations;
    @Mock
    HashOperations hashOperations;
    @Mock
    SetOperations<String,String> setOperations;

    @InjectMocks
    private RedisService redisService;

    @BeforeEach
    public void before() {
        MockitoAnnotations.openMocks(this);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(redisTemplate.opsForHash()).thenReturn(hashOperations);
        Mockito.when(redisTemplate.opsForSet()).thenReturn(setOperations);
    }

    @Test
    void setRetryCount() {
        Mockito.when(valueOperations.increment("test",1)).thenReturn(1L);
        Assertions.assertEquals(0L, redisService.setRetryCount(""));
    }

    @Test
    void incrementOne() {
        Mockito.when(valueOperations.increment("test",1)).thenReturn(1L);
        Assertions.assertEquals(0L, redisService.incrementOne(""));
    }

    @Test
    void incrementLimitedSteps() {
        Mockito.when(valueOperations.increment(Mockito.any(),Mockito.anyLong())).thenReturn(1L);
        Assertions.assertEquals(1L, redisService.incrementLimitedSteps("", 1L));
    }

    @Test
    void delRetryCount() {
        Assertions.assertDoesNotThrow(() ->redisService.delRetryCount(""));
    }

    @Test
    void hSet() {
        Assertions.assertDoesNotThrow(() ->redisService.hSet("", "", "" ,1L));
    }

    @Test
    void hGet() {
        Mockito.when(hashOperations.get(Mockito.any(), Mockito.any())).thenReturn(1L);
        Assertions.assertNotNull(redisService.hGet("", ""));
    }

    @Test
    void expire() {
        Assertions.assertDoesNotThrow(() ->redisService.expire("", 1L));
    }

    @Test
    void getValue() {
        Mockito.when(valueOperations.get(Mockito.any())).thenReturn(null);
        Assertions.assertNull(redisService.getValue(""));

        Mockito.when(valueOperations.get(Mockito.any())).thenReturn("122");
        Assertions.assertNotNull(redisService.getValue(""));
    }

    @Test
    void getKeys() {
        Assertions.assertNotNull(redisService.getKeys(""));
    }

    @Test
    void setValue() {
        Assertions.assertDoesNotThrow(() -> redisService.setValue("", "", 1L));
    }

    @Test
    void setVal() {
        Assertions.assertDoesNotThrow(() -> redisService.setVal("", "", 1L, TimeUnit.SECONDS));
    }

    @Test
    void getKeyExpireTime() {
        Assertions.assertDoesNotThrow(() -> redisService.getKeyExpireTime(""));
    }

    @Test
    void delKeys() {
        Assertions.assertDoesNotThrow(() -> redisService.delKeys(Collections.singletonList("")));
    }

    @Test
    void flush() {
        Mockito.when(redisTemplate.keys(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> redisService.flush(""));
    }

    @Test
    void setSaleNoIfAbsent() {
        Assertions.assertDoesNotThrow(() -> redisService.setSaleNoIfAbsent("", ""));
    }

    @Test
    void setIfAbsentWithExpire() {
        Assertions.assertDoesNotThrow(() -> redisService.setIfAbsentWithExpire("", "", 1L, TimeUnit.SECONDS));
    }

    @Test
    void hasKey() {
        Assertions.assertDoesNotThrow(() -> redisService.hasKey(""));
    }

    @Test
    void addFavoriteBrand() {
        Assertions.assertDoesNotThrow(() -> redisService.addFavoriteBrand("", ""));
    }

    @Test
    void removeFavoriteBrand() {
        Assertions.assertDoesNotThrow(() -> redisService.removeFavoriteBrand("", ""));
    }

    @Test
    void getFavoriteBrands() {
        Assertions.assertDoesNotThrow(() -> redisService.getFavoriteBrands(""));

        Set<String> arr = new HashSet<>();
        arr.add("123");
        Mockito.when(setOperations.members(Mockito.any())).thenReturn(arr);
        Assertions.assertDoesNotThrow(() -> redisService.getFavoriteBrands(""));
    }

    @Test
    void inSet() {
        Assertions.assertDoesNotThrow(() -> redisService.inSet("", ""));
    }



}
