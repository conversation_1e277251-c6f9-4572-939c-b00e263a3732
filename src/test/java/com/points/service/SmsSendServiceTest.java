package com.points.service;

import com.kerryprops.kip.service.integral.client.UnifiedMessageClient;
import com.kerryprops.kip.service.integral.config.NotifyEmailProperties;
import com.kerryprops.kip.service.integral.model.dto.CrmSysUserDto;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.vo.EmailSendVo;
import com.kerryprops.kip.service.integral.service.KerryStaffService;
import com.kerryprops.kip.service.integral.service.TbSubscriptionRecordService;
import com.kerryprops.kip.service.integral.service.impl.SmsSendServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/

@Slf4j
class SmsSendServiceTest {

    @InjectMocks
    private SmsSendServiceImpl smsSendServiceImpl;

    @Mock
    private KerryStaffService kerryStaffService;

    @Mock
    private TbSubscriptionRecordService tbSubscriptionRecordService;

    @Mock
    private UnifiedMessageClient unifiedMessageClient;

    @Mock
    private NotifyEmailProperties notifyEmailProperties;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void sendPointsEmailCase0() {
        // userIds为空
        Assertions.assertDoesNotThrow(() -> smsSendServiceImpl.sendPointsEmail("8aaa80b47c784020017c78b00d060022", 0));

        // 获取email为空
        Mockito.when(tbSubscriptionRecordService.querySubscriptionUserIdList(Mockito.any())).thenReturn(Collections.singletonList("123"));
        Assertions.assertDoesNotThrow(() -> smsSendServiceImpl.sendPointsEmail("8aaa80b47c784020017c78b00d060022", 0));

        Mockito.when(kerryStaffService.getUserList(Mockito.any())).thenReturn(Collections.singletonList(CrmSysUserDto.builder().id(1L).build()));
        Assertions.assertDoesNotThrow(() -> smsSendServiceImpl.sendPointsEmail("8aaa80b47c784020017c78b00d060022", null));
    }

    @Test
    void sendPointsEmailCase1() {
        Mockito.when(tbSubscriptionRecordService.querySubscriptionUserIdList(Mockito.any())).thenReturn(Collections.singletonList("10030"));
        Mockito.when(kerryStaffService.getUserList(Mockito.any())).thenReturn(Collections.singletonList(CrmSysUserDto.builder().email("10030").build()));
        Mockito.when(unifiedMessageClient.emailAliCloudSend(EmailSendVo.builder().build())).thenReturn(KerryResultDto.builder().build());
        Mockito.when(notifyEmailProperties.getCrmAdminUrl()).thenReturn("");
        Assertions.assertDoesNotThrow(() -> smsSendServiceImpl.sendPointsEmail("8aaa80b47c784020017c78b00d060022", 0));
    }

    @Test
    void sendPointsEmailCase2() {
        Mockito.when(tbSubscriptionRecordService.querySubscriptionUserIdList(Mockito.any())).thenReturn(Collections.singletonList("10030"));
        Mockito.when(kerryStaffService.getUserList(Mockito.any())).thenReturn(Collections.singletonList(CrmSysUserDto.builder().email("10030").build()));
        Mockito.when(unifiedMessageClient.emailAliCloudSend(EmailSendVo.builder().build())).thenReturn(KerryResultDto.builder().build());
        Mockito.when(notifyEmailProperties.getCrmPhotoPointsUrl()).thenReturn("");
        Assertions.assertDoesNotThrow(() -> smsSendServiceImpl.sendPointsEmail("8aaa80b47c784020017c78b00d060022", 1));
    }

    @Test
    void sendPointsEmailCase3() {
        Mockito.when(tbSubscriptionRecordService.querySubscriptionUserIdList(Mockito.any())).thenReturn(Collections.singletonList("10030"));
        Mockito.when(kerryStaffService.getUserList(Mockito.any())).thenReturn(Collections.singletonList(CrmSysUserDto.builder().email("10030").build()));
        Mockito.when(unifiedMessageClient.emailAliCloudSend(EmailSendVo.builder().build())).thenReturn(KerryResultDto.builder().build());
        Mockito.when(notifyEmailProperties.getCrmPhotoPointsUrl()).thenReturn("");
        Assertions.assertDoesNotThrow(() -> smsSendServiceImpl.sendPointsEmail("8aaa80b47c784020017c78b00d060022", 2));
    }

    @Test
    void sendEmail() {
        Mockito.when(unifiedMessageClient.emailSend(EmailSendVo.builder().build())).thenReturn(KerryResultDto.builder().build());
        Assertions.assertNotNull(smsSendServiceImpl.sendEmail(EmailSendVo.builder().build()));
    }

    @Test
    void emailAliCloudSend() {
        Mockito.when(unifiedMessageClient.emailAliCloudSend(EmailSendVo.builder().build())).thenReturn(KerryResultDto.builder().build());
        Assertions.assertNotNull(smsSendServiceImpl.emailAliCloudSend(EmailSendVo.builder().build()));
    }

}
