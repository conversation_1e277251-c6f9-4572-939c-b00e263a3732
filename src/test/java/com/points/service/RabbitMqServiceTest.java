package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.impl.RabbitMqServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class RabbitMqServiceTest {

    @InjectMocks
    private RabbitMqServiceImpl rabbitMqService;

    @Mock
    private RabbitTemplate crmRabbitTemplate;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void sendMessage() {
        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendMessage("123", "123"));
    }

    @Test
    void sendTopicMessage() {
        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendTopicMessage("123", "123"));
    }

    @Test
    void sendLazyMessage() {
        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendLazyMessage("123", "123", 1));
    }

    @Test
    void sendMemberNewMsg() {
        TbMemberAsset asset = TbMemberAsset.builder().build();
        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendMemberNewMsg(asset, false));
    }

    @Test
    void sendMqMessage() {
        TbSalesDetail detail = TbSalesDetail.builder().payAmount(BigDecimal.TEN).build();
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().refund(Boolean.TRUE).amount("100").build();
        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendMqMessage(detail, dto));

        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder().refund(Boolean.FALSE).amount("100").build();
        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendMqMessage(detail, dto1));
    }

    @Test
    @DisplayName("true")
    void sendFullActivityReward() {
        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendFullActivityReward(null, null, true));

        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendFullActivityReward(TbSalesDetail.builder().build(), null, true));

        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendFullActivityReward(TbSalesDetail.builder().build(), SalesAutoPointsDto.builder().build(), true));

        Assertions.assertDoesNotThrow(() ->
                rabbitMqService.sendFullActivityReward(TbSalesDetail.builder().saleDate(DateUtil.date()).status(1).build(),
                        SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).refundAmount("100").build(), true));
    }

    @Test
    @DisplayName("false")
    void sendFullActivityRewardCase1() {
        Assertions.assertDoesNotThrow(() ->
                rabbitMqService.sendFullActivityReward(
                        TbSalesDetail.builder().saleDate(DateUtil.date()).refundAmount(BigDecimal.ONE).status(1).build(),
                        SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).refundAmount("100").build(), false));
    }

    @Test
    void sendFanoutMessage() {
        Assertions.assertDoesNotThrow(() -> rabbitMqService.sendFanoutMessage(RabbitMqConstant.OCR_FANOUT_EXCHANGE,"", ""));
    }
}