package com.points.service;

import com.kerryprops.kip.pmw.client.resource.PointsActivationConfirmResource;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbOpenCardFailRecord;
import com.kerryprops.kip.service.integral.mapper.TbOpenCardFailRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsAuthRecordService;
import com.kerryprops.kip.service.integral.service.impl.TbOpenCardFailRecordServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 08/12/2024 14:04
 **********************************************************************************************************************/
@Slf4j
class TbOpenCardFailRecordServiceTest {

    @InjectMocks
    private TbOpenCardFailRecordServiceImpl tbOpenCardFailRecordServiceImpl;

    @Mock
    private TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    @Mock
    private TbOpenCardFailRecordMapper tbOpenCardFailRecordMapper;
    @Mock
    private MallConfig mallConfig;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void saveOrUpdateRecord() {
        PointsActivationConfirmResource.PointsActivationConfirmBodyResource body =
                new PointsActivationConfirmResource.PointsActivationConfirmBodyResource();
        PointsActivationConfirmResource resource =
                new PointsActivationConfirmResource(null, body, "");
        Assertions.assertDoesNotThrow(() -> tbOpenCardFailRecordServiceImpl.saveOrUpdateRecord(resource));

        PointsActivationConfirmResource.PointsActivationConfirmBodyResource body2 =
                new PointsActivationConfirmResource.PointsActivationConfirmBodyResource();
        PointsActivationConfirmResource.PointsActivationDetailResource detailResource2 =
                new PointsActivationConfirmResource.PointsActivationDetailResource();
        body2.setResource(detailResource2);
        PointsActivationConfirmResource resource2 =
                new PointsActivationConfirmResource(null, body2, "");
        Assertions.assertDoesNotThrow(() -> tbOpenCardFailRecordServiceImpl.saveOrUpdateRecord(resource2));

        Mockito.when(mallConfig.getByMchId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertDoesNotThrow(() -> tbOpenCardFailRecordServiceImpl.saveOrUpdateRecord(resource2));
    }

    @Test
    void queryByOpenIdAndMallId() {
        Mockito.when(tbOpenCardFailRecordMapper.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(new TbOpenCardFailRecord());
        Assertions.assertDoesNotThrow(() -> tbOpenCardFailRecordServiceImpl.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()));
    }

    @Test
    void checkWeChatAndAlipayAuthStatus() {
        Mockito.when(tbOpenCardFailRecordMapper.queryByOpenId(Mockito.anyString()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbOpenCardFailRecordServiceImpl.checkWeChatAndAlipayAuthStatus("123", "456"));

        Mockito.when(tbOpenCardFailRecordMapper.queryByOpenId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbOpenCardFailRecord.builder().openId("openId1").build()));
        Mockito.when(tbInsensatePointsAuthRecordService.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                        .thenReturn(TbInsensatePointsAuthRecord.builder().build());
        Assertions.assertDoesNotThrow(() -> tbOpenCardFailRecordServiceImpl.checkWeChatAndAlipayAuthStatus("123", "456"));

        Mockito.when(tbInsensatePointsAuthRecordService.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbOpenCardFailRecordServiceImpl.checkWeChatAndAlipayAuthStatus("123", "456"));


        PointsActivationConfirmResource.PointsActivationDetailResource authorizeResource = new PointsActivationConfirmResource.PointsActivationDetailResource();
        Mockito.when(tbOpenCardFailRecordMapper.queryByOpenId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbOpenCardFailRecord
                        .builder()
                        .openId("openId1")
                        .content(JsonUtils.objToString(authorizeResource))
                        .build()));
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().build());
        Assertions.assertDoesNotThrow(() -> tbOpenCardFailRecordServiceImpl.checkWeChatAndAlipayAuthStatus("123", "456"));
    }

    @Test
    void queryWeChatOpenCardInfo() {
        Mockito.when(mallConfig.getAppIdList()).thenReturn(Arrays.asList("123", "345"));
        Assertions.assertDoesNotThrow(() -> tbOpenCardFailRecordServiceImpl.queryWeChatOpenCardInfo("openId", "userId"));
    }

}
