package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbMemberGradeChangeDetailService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.service.promotion.impl.JoinDayPromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class JoinDayPromotionRuleTest {

    @InjectMocks
    private JoinDayPromotionRule joinDayPromotionRule;

    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void checkRule() {
        TbActivityPromotionCondition condition = TbActivityPromotionCondition.builder().id("01").build();
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build();
        Date saleDate = DateUtil.date();
        List<TbActivityPromotionJoinvip> vips = Collections.singletonList(new TbActivityPromotionJoinvip());
        Assertions.assertFalse(joinDayPromotionRule.checkRule(condition, pointsDto, saleDate, vips));

        SalesAutoPointsDto pointsDto1 = SalesAutoPointsDto.builder().member(TbMemberAsset.builder().joinTime(DateUtil.date()).build()).build();
        Assertions.assertFalse(joinDayPromotionRule.checkRule(condition, pointsDto1, saleDate, vips));


        List<TbActivityPromotionJoinvip> vips1 = Collections.singletonList(TbActivityPromotionJoinvip.builder().promotionConditionId("01").build());
        Assertions.assertFalse(joinDayPromotionRule.checkRule(condition, pointsDto1, saleDate, vips1));

        List<TbActivityPromotionJoinvip> vips2 = Collections.singletonList(TbActivityPromotionJoinvip.builder()
                .promotionConditionId("01")
                .memberGrade("01,02,03")
                .joinTimeType(0)
                .build());
        Mockito.when(tbMemberGradeChangeDetailService.getJoinDayMaxGrade(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("");
        Mockito.when(tbMemberGradeService.getGroupMiniGradeCode(Mockito.any()))
                .thenReturn("03");
        Assertions.assertTrue(joinDayPromotionRule.checkRule(condition, pointsDto1, saleDate, vips2));


        List<TbActivityPromotionJoinvip> vips3 = Collections.singletonList(TbActivityPromotionJoinvip.builder()
                .promotionConditionId("01")
                .memberGrade("01,02")
                .joinTimeType(1)
                .build());
        Assertions.assertFalse(joinDayPromotionRule.checkRule(condition, pointsDto1, saleDate, vips3));
    }

    @Test
    void checkRuleCase2() {
        TbActivityPromotionCondition condition = TbActivityPromotionCondition.builder().id("01").build();
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto
                .builder()
                .member(TbMemberAsset.builder().joinTime(DateUtil.date()).build())
                .build();
        Date saleDate = DateUtil.offsetDay(DateUtil.date(), 10);
        List<TbActivityPromotionJoinvip> vips3 = Collections.singletonList(TbActivityPromotionJoinvip.builder()
                .promotionConditionId("01")
                .memberGrade("01#02#03")
                .joinTimeType(1)
                .joinTime(1)
                .saleNum(2)
                .build());
        Mockito.when(tbMemberGradeChangeDetailService.getJoinDayMaxGrade(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("01");
        Assertions.assertFalse(joinDayPromotionRule.checkRule(condition, pointsDto, saleDate, vips3));

        Date saleDate1 = DateUtil.date();
        Assertions.assertFalse(joinDayPromotionRule.checkRule(condition, pointsDto, saleDate1, vips3));
    }
}
