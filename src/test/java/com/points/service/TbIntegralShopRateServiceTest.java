package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import com.kerryprops.kip.service.integral.service.TbIntegralShopRateService;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralShopRateResource;
import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import com.points.BaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
class TbIntegralShopRateServiceTest extends BaseTest {

    @Autowired
    private TbIntegralShopRateService tbIntegralShopRateService;

    @Test
    void shopRatePage() {
        Assertions.assertNotNull(tbIntegralShopRateService.shopRatePage("4028e3817c2b3f79017c2b48c54c00001", Collections.singletonList("8aaa80b47c784020017c78b00d0600221"), null, 0, 10));
    }

    @Test
    void  getTotal() {
        Assertions.assertDoesNotThrow(() -> tbIntegralShopRateService.getTotal("4028e3817c2b3f79017c2b48c54c00001", Collections.singletonList("8aaa80b47c784020017c78b00d0600221"), null));
    }

    @Test
    void getShopRateList() {
        Assertions.assertNotNull(tbIntegralShopRateService.getShopRateList(TbIntegralShopRate.builder().groupId("4028e3817c2b3f79017c2b48c54c00001").mallId("8aaa80b47c784020017c78b00d0600221").shopId("HKC00021031").build()));
    }

    @Test
    void deleteShopRateList() {
        Assertions.assertDoesNotThrow(() -> tbIntegralShopRateService.deleteShopRateList(new Long[]{2L, 3L}));
    }

    @Test
    void saveOrUpdateShopRate() {
        List<GradeIntegralResponse> list = new ArrayList<>();
        list.add(GradeIntegralResponse.builder().money(BigDecimal.valueOf(1)).pointNum(BigDecimal.valueOf(1)).gradeId("11111").gradeName("银卡").build());
        TbIntegralShopRateResource resource = TbIntegralShopRateResource.builder().groupId("4028e3817c2b3f79017c2b48c54c00001").mallId("8aaa80b47c784020017c78b00d0600221").shopIds(Arrays.asList("HKC00021031")).build();
        resource.setList(list);
        resource.setCreator("Test111");
        Assertions.assertDoesNotThrow(() -> tbIntegralShopRateService.saveOrUpdateShopRate(resource));
    }

}
