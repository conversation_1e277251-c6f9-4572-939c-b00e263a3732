package com.points.service;


import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotion;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionIntegral;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionIntegralMapper;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbSaleMatchedPromotionService;
import com.kerryprops.kip.service.integral.service.impl.TbActivityPromotionIntegralServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - bert
 * Created Date - 01/13/2025 10:22
 **********************************************************************************************************************/

@Slf4j
class TbActivityPromotionIntegralServiceTest {

    @InjectMocks
    private TbActivityPromotionIntegralServiceImpl tbActivityPromotionIntegralService;

    @Mock
    private TbActivityPromotionIntegralMapper tbActivityPromotionIntegralMapper;
    @Mock
    private TbSaleMatchedPromotionService tbSaleMatchedPromotionService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void savePromotionIntegral() {
        Assertions.assertDoesNotThrow(() ->tbActivityPromotionIntegralService.savePromotionIntegral(null, SalesAutoPointsDto.builder().build(), 1, 1));

        Assertions.assertDoesNotThrow(() ->tbActivityPromotionIntegralService.savePromotionIntegral(
                Collections.singletonList(TbActivityPromotion.builder().module("1")
                        .type("1")
                        .promotionBonusExpirationDate(DateUtil.date())
                        .times(BigDecimal.ONE).build()),
                SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build(),
                1,
                1));

        Assertions.assertDoesNotThrow(() ->tbActivityPromotionIntegralService.savePromotionIntegral(
                Collections.singletonList(TbActivityPromotion.builder().module("2")
                        .type("0")
                        .bonus(100)
                        .promotionBonusExpirationDate(DateUtil.date())
                        .times(BigDecimal.ONE).build()),
                SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build(),
                1,
                1));

        Assertions.assertDoesNotThrow(() ->tbActivityPromotionIntegralService.savePromotionIntegral(
                Collections.singletonList(TbActivityPromotion.builder().module("2").type("2").times(BigDecimal.ONE).build()),
                SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build(),
                1,
                1));
    }

    @Test
    void updatePromotionIntegral() {
        Assertions.assertDoesNotThrow(() ->tbActivityPromotionIntegralService.updatePromotionIntegral(TbSalesDetail.builder().build()));

        Mockito.when(tbActivityPromotionIntegralMapper.getSaleMatchedActivityPromotions(Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(TbActivityPromotionIntegral.builder().enableIntegral(-1).build(),
                        TbActivityPromotionIntegral.builder().enableIntegral(10).expireTime(DateUtil.date()).build()));
        Assertions.assertDoesNotThrow(() ->tbActivityPromotionIntegralService.updatePromotionIntegral(TbSalesDetail.builder().build()));
    }

}