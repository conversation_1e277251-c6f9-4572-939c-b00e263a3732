package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate;
import com.kerryprops.kip.service.integral.mapper.TbIntegralCategoryRateMapper;
import com.kerryprops.kip.service.integral.service.impl.TbIntegralCategoryRateServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralCategoryRateResource;
import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class TbIntegralCategoryRateServiceTest {

    @InjectMocks
    private TbIntegralCategoryRateServiceImpl tbIntegralCategoryRateServiceImpl;

    @Mock
    private TbIntegralCategoryRateMapper tbIntegralCategoryRateMapper;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void categoryRatePage() {
        Mockito.when(tbIntegralCategoryRateMapper.categoryRatePage(Mockito.anyString(), Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(new TbIntegralCategoryRate()));
        Assertions.assertNotNull(tbIntegralCategoryRateServiceImpl
                .categoryRatePage(Mockito.anyString(), Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt()));
    }

    @Test
    void getTotal() {
        Mockito.when(tbIntegralCategoryRateMapper.getTotal(Mockito.anyString(), Mockito.anyString(), Mockito.anyList()))
                .thenReturn(10);
        Assertions.assertEquals (10, tbIntegralCategoryRateServiceImpl
                .getTotal(Mockito.anyString(), Mockito.anyString(), Mockito.anyList()));
    }

    @Test
    void saveOrUpdateCategoryRate() {
        TbIntegralCategoryRateResource resource = new TbIntegralCategoryRateResource();
        resource.setCategoryId("categoryId");
        Assertions.assertDoesNotThrow(() -> tbIntegralCategoryRateServiceImpl.saveOrUpdateCategoryRate(resource));

        TbIntegralCategoryRateResource resource1 = new TbIntegralCategoryRateResource();
        resource1.setCategoryId("categoryId");
        List<GradeIntegralResponse> gradeList = new ArrayList<>();
        GradeIntegralResponse grade1 = GradeIntegralResponse.builder().build();
        GradeIntegralResponse grade2 = GradeIntegralResponse.builder().build();
        gradeList.add(grade1);
        gradeList.add(grade2);
        resource1.setList(gradeList);
        Assertions.assertDoesNotThrow(() -> tbIntegralCategoryRateServiceImpl.saveOrUpdateCategoryRate(resource1));
    }

    @Test
    void getCategoryRateList() {
        TbIntegralCategoryRate rate = new TbIntegralCategoryRate();
        Mockito.when(tbIntegralCategoryRateMapper.getCategoryRateList(rate)).thenReturn(Collections.singletonList(new TbIntegralCategoryRate()));
        Assertions.assertNotNull(tbIntegralCategoryRateServiceImpl.getCategoryRateList(rate));
    }

    @Test
    void deleteCategoryRateList() {
        Long[] ids = new Long[] {1L, 2L, 3L};
        Assertions.assertDoesNotThrow(() -> tbIntegralCategoryRateServiceImpl.deleteCategoryRateList(ids));
    }

}
