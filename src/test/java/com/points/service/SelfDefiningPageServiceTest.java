package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.SelfDefiningPage;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.SelfDefiningPageMapper;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.impl.SelfDefiningPageServiceImpl;
import com.kerryprops.kip.service.integral.service.promotion.impl.BirthdayPromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class SelfDefiningPageServiceTest {

    @InjectMocks
    private SelfDefiningPageServiceImpl selfDefiningPageService;

    @Mock
    private SelfDefiningPageMapper selfDefiningPageMapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getZIndex() {
        Mockito.when(selfDefiningPageMapper.findHomePage(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(SelfDefiningPage.builder().build());
        Assertions.assertNotNull(selfDefiningPageService.getZIndex("12345", "12345", "12"));
    }

    @Test
    void getPageById() {
        Mockito.when(selfDefiningPageMapper.getPageById(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(SelfDefiningPage.builder().build());
        Assertions.assertNotNull(selfDefiningPageService.getPageById("12345", "12345", "12"));
    }
}
