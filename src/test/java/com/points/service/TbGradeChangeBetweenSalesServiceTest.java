package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbGradeChangeBetweenSales;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.mapper.TbGradeChangeBetweenSalesMapper;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeChangeDetailMapper;
import com.kerryprops.kip.service.integral.model.dto.MemberYearAmountCalDto;
import com.kerryprops.kip.service.integral.service.impl.TbGradeChangeBetweenSalesServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
class TbGradeChangeBetweenSalesServiceTest {

    @InjectMocks
    private TbGradeChangeBetweenSalesServiceImpl tbGradeChangeBetweenSalesService;
    @Mock
    private TbGradeChangeBetweenSalesMapper tbGradeChangeBetweenSalesMapper;
    @Mock
    private TbMemberGradeChangeDetailMapper tbMemberGradeChangeDetailMapper;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findByChangeId() {
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.findByChangeId("123"));
    }

    @Test
    void saveDetail() {
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.saveDetail("123", new Date(), new Date()));
    }

    @Test
    void saveDetail1() {
        when(tbGradeChangeBetweenSalesMapper.findByChangeId(any())).thenReturn(TbGradeChangeBetweenSales.builder().build());
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.saveDetail("123", new Date(), new Date()));
    }

    @Test
    void checkChangeBetweenSales() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().changeType(4).newGrade("01").oldGrade("02").build();
        TbMemberGrade minGrade = TbMemberGrade.builder().code("01").build();
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.checkChangeBetweenSales(detail, MemberYearAmountCalDto.builder().cycleYear(true).orgGrade("01").build(), minGrade));
    }

    @Test
    void checkChangeBetweenSales1() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().changeType(4).newGrade("03").oldGrade("02").build();
        TbMemberGrade minGrade = TbMemberGrade.builder().code("01").build();
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.checkChangeBetweenSales(detail, MemberYearAmountCalDto.builder().cycleYear(true).orgGrade("03").build(), minGrade));
    }

    @Test
    void checkChangeBetweenSales2() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().changeType(4).newGrade("03").oldGrade("02").build();
        TbMemberGrade minGrade = TbMemberGrade.builder().code("01").build();
        when(tbMemberGradeChangeDetailMapper.getChangeDetailBetweenTime(any(), any(), any(), any(), any())).thenReturn(Collections.singletonList(TbMemberGradeChangeDetail.builder().build()));
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.checkChangeBetweenSales(detail, MemberYearAmountCalDto.builder().cycleYear(true).orgGrade("03").build(), minGrade));
    }

    @Test
    void checkChangeBetweenSales3() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().changeType(3).newGrade("03").oldGrade("02").build();
        TbMemberGrade minGrade = TbMemberGrade.builder().code("01").build();
        when(tbMemberGradeChangeDetailMapper.getChangeDetailBetweenTime(any(), any(), any(), any(), any())).thenReturn(Collections.singletonList(TbMemberGradeChangeDetail.builder().build()));
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.checkChangeBetweenSales(detail, MemberYearAmountCalDto.builder().cycleYear(true).orgGrade("03").build(), minGrade));
    }

    @Test
    void checkChangeBetweenSales31() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().changeType(3).newGrade("03").oldGrade("02").build();
        TbMemberGrade minGrade = TbMemberGrade.builder().code("01").build();
        when(tbMemberGradeChangeDetailMapper.getChangeDetailBetweenTime(any(), any(), any(), any(), any())).thenReturn(Collections.singletonList(TbMemberGradeChangeDetail.builder().build()));
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.checkChangeBetweenSales(detail, MemberYearAmountCalDto.builder().cycleYear(true).orgGrade("02").build(), minGrade));
    }

    @Test
    void checkChangeBetweenSales4() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().changeType(3).newGrade("03").oldGrade("02").build();
        TbMemberGrade minGrade = TbMemberGrade.builder().code("01").build();
        when(tbGradeChangeBetweenSalesMapper.findByChangeId(any())).thenReturn(TbGradeChangeBetweenSales.builder().build());
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.checkChangeBetweenSales(detail, MemberYearAmountCalDto.builder().cycleYear(true).orgGrade("03").build(), minGrade));
    }

    @Test
    void checkChangeBetweenSales5() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().id("10").changeType(3).newGrade("03").oldGrade("02").build();
        TbMemberGrade minGrade = TbMemberGrade.builder().code("01").build();
        when(tbMemberGradeChangeDetailMapper.getChangeDetailBetweenTime(any(), any(), any(), any(), any())).thenReturn(
                Collections.singletonList(TbMemberGradeChangeDetail.builder().id("11").build()));
        when(tbGradeChangeBetweenSalesMapper.findByChangeId("11")).thenReturn(TbGradeChangeBetweenSales.builder().build());
        assertDoesNotThrow(() -> tbGradeChangeBetweenSalesService.checkChangeBetweenSales(detail, MemberYearAmountCalDto.builder().cycleYear(true).orgGrade("03").build(), minGrade));
    }

}
