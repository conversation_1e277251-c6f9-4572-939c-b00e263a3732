package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbActivityPromotion;
import com.kerryprops.kip.service.integral.mapper.TbSaleMatchedPromotionMapper;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.impl.TbSaleMatchedPromotionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 12/30/2024 12:24
 **********************************************************************************************************************/
@Slf4j
class TbSaleMatchedPromotionServiceTest {

    @InjectMocks
    private TbSaleMatchedPromotionServiceImpl tbSaleMatchedPromotionService;
    @Mock
    private TbSaleMatchedPromotionMapper tbSaleMatchedPromotionMapper;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void saveRecord() {
        Assertions.assertDoesNotThrow(() ->
                tbSaleMatchedPromotionService.saveRecord(Collections.emptyList(), SalesAutoPointsDto.builder().build()));

        Assertions.assertDoesNotThrow(() ->
                tbSaleMatchedPromotionService.saveRecord(Collections.singletonList(TbActivityPromotion.builder().build()), SalesAutoPointsDto.builder().build()));
    }

    @Test
    void findBySellNos() {
        Assertions.assertNotNull(tbSaleMatchedPromotionService.findBySellNos(Collections.emptyList()));

        Mockito.when(tbSaleMatchedPromotionMapper.findBySellNos(Mockito.any())).thenReturn(Collections.emptyList());
        Assertions.assertNotNull(tbSaleMatchedPromotionService.findBySellNos(Collections.singletonList("123")));
    }

    @Test
    void findByAdjustIds() {
        Assertions.assertNotNull(tbSaleMatchedPromotionService.findByAdjustIds(Collections.emptyList()));

        Mockito.when(tbSaleMatchedPromotionMapper.findByAdjustIds(Mockito.any())).thenReturn(Collections.emptyList());
        Assertions.assertNotNull(tbSaleMatchedPromotionService.findByAdjustIds(Collections.singletonList("123")));
    }

}
