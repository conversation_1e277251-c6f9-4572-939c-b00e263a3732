package com.points.service;

import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbBkcSaleMember;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.TbBkcSaleMemberMapper;
import com.kerryprops.kip.service.integral.service.MemberRegisterService;
import com.kerryprops.kip.service.integral.service.impl.TbBkcSaleMemberServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 15:49
 **********************************************************************************************************************/

@Slf4j
class TbBkcSaleMemberServiceTest {

    @InjectMocks
    private TbBkcSaleMemberServiceImpl tbBkcSaleMemberServiceImpl;

    @Mock
    private TbBkcSaleMemberMapper tbBkcSaleMemberMapper;
    @Mock
    private MemberRegisterService memberRegisterService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findByVipcode() {
        Mockito.when(tbBkcSaleMemberMapper.findByVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(TbBkcSaleMember.builder().build());
        Assertions.assertNotNull(tbBkcSaleMemberServiceImpl.findByVipcode(Mockito.any(), Mockito.any()));
    }

    @Test
    void bkcMigrationConfirmStatus() {
        LoginUser loginUser = LoginUser.builder().build();
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbBkcSaleMemberServiceImpl.bkcMigrationConfirmStatus(loginUser, 0));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(tbBkcSaleMemberMapper.findByVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbBkcSaleMemberServiceImpl.bkcMigrationConfirmStatus(loginUser, 0));

        Mockito.when(tbBkcSaleMemberMapper.findByVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(TbBkcSaleMember.builder().confirmStatus(1).build());
        Assertions.assertDoesNotThrow(() -> tbBkcSaleMemberServiceImpl.bkcMigrationConfirmStatus(loginUser, 0));

        Mockito.when(tbBkcSaleMemberMapper.findByVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(TbBkcSaleMember.builder().confirmStatus(0).build());
        Assertions.assertDoesNotThrow(() -> tbBkcSaleMemberServiceImpl.bkcMigrationConfirmStatus(loginUser, 0));
    }

    @Test
    void insertBkcCaseMember() {
        Assertions.assertDoesNotThrow(() -> tbBkcSaleMemberServiceImpl.insertBkcCaseMember(TbBkcSaleMember.builder().build()));
    }

}
