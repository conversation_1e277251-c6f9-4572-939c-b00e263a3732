package com.points.service;

import com.kerryprops.kip.service.integral.client.KerryStaffClient;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.service.impl.KerryStaffServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class KerryStaffServiceTest {

    @InjectMocks
    private KerryStaffServiceImpl kerryStaffService;

    @Mock
    private KerryStaffClient kerryStaffClient;
    @Mock
    private ProfileServiceClient profileServiceClient;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findByKipUserId() {
        Assertions.assertNull(kerryStaffService.findByKipUserId(""));

        KerryResultDto<List<CustomerUserDto>> data = new KerryResultDto<>();
        Mockito.when(kerryStaffClient.getProfileUsers(Mockito.any()))
                .thenReturn(data);
        Assertions.assertNull(kerryStaffService.findByKipUserId("12345"));

        KerryResultDto<List<CustomerUserDto>> data1 = new KerryResultDto<>();
        data1.setData(Collections.singletonList(CustomerUserDto.builder().build()));
        Mockito.when(kerryStaffClient.getProfileUsers(Mockito.any()))
                .thenReturn(data1);
        Assertions.assertNotNull(kerryStaffService.findByKipUserId("12345"));
    }

    @Test
    void findByMobile() {
        Assertions.assertNull(kerryStaffService.findByMobile(""));

        KerryResultDto<List<CustomerUserDto>> data = new KerryResultDto<>();
        Mockito.when(kerryStaffClient.getProfileUsers(Mockito.any()))
                .thenReturn(data);
        Assertions.assertNull(kerryStaffService.findByMobile("12345"));

        KerryResultDto<List<CustomerUserDto>> data1 = new KerryResultDto<>();
        data1.setData(Collections.singletonList(CustomerUserDto.builder().build()));
        Mockito.when(kerryStaffClient.getProfileUsers(Mockito.any()))
                .thenReturn(data1);
        Assertions.assertNotNull(kerryStaffService.findByMobile("12345"));
    }

    @Test
    void findByOpenId() {
        Mockito.when(kerryStaffClient.findByOpenId(Mockito.any())).thenReturn(null);
        Assertions.assertNull(kerryStaffService.findByOpenId(""));
    }

    @Test
    void modifyProfileInfo() {
        KerryResultDto<String> result = new KerryResultDto<>();
        result.setCode(IntegralConstant.DEF_SUCC);
        Mockito.when(kerryStaffClient.modifyProfileInfo(Mockito.any())).thenReturn(result);
        Assertions.assertNull(kerryStaffService.modifyProfileInfo(null));

        KerryResultDto<String> result1 = new KerryResultDto<>();
        result1.setCode("400");
        Mockito.when(kerryStaffClient.modifyProfileInfo(Mockito.any())).thenReturn(result1);
        Assertions.assertNull(kerryStaffService.modifyProfileInfo(null));
    }

    @Test
    void findByKipUserIds() {
        Assertions.assertNotNull(kerryStaffService.findByKipUserIds(Collections.emptyList()));

        KerryResultDto<List<CustomerUserDto>> data = new KerryResultDto<>();
        Mockito.when(kerryStaffClient.getProfileUsers(Mockito.any())).thenReturn(data);
        Assertions.assertNull(kerryStaffService.findByKipUserIds(Collections.singletonList("12345")));
    }

    @Test
    void getMapByKipUserIds() {
        Assertions.assertNotNull(kerryStaffService.getMapByKipUserIds(Collections.emptyList()));

        KerryResultDto<List<CustomerUserDto>> data = new KerryResultDto<>();
        data.setData(Collections.singletonList(CustomerUserDto.builder().build()));
        Mockito.when(kerryStaffClient.getProfileUsers(Mockito.any())).thenReturn(data);
        Assertions.assertNotNull(kerryStaffService.getMapByKipUserIds(Collections.singletonList("12345")));
    }

    @Test
    void getMapByMobiles() {
        Assertions.assertNotNull(kerryStaffService.getMapByMobiles(Collections.emptyList()));

        KerryResultDto<List<CustomerUserDto>> data = new KerryResultDto<>();
        data.setData(Collections.singletonList(CustomerUserDto.builder().build()));
        Mockito.when(kerryStaffClient.getProfileUsers(Mockito.any())).thenReturn(data);
        Assertions.assertNotNull(kerryStaffService.getMapByMobiles(Collections.singletonList("12345")));
    }

    @Test
    void getMapFutureByMobile() {
        Assertions.assertNotNull(kerryStaffService.getMapFutureByMobile(Collections.emptyList()));

        KerryResultDto<List<CustomerUserDto>> data = new KerryResultDto<>();
        data.setData(Collections.singletonList(CustomerUserDto.builder().build()));
        Mockito.when(kerryStaffClient.getProfileUsers(Mockito.any())).thenReturn(data);
        Assertions.assertNotNull(kerryStaffService.getMapFutureByMobile(Collections.singletonList("12345")));
    }

    @Test
    void getWxInfoByMobileAndAppId() {
        Assertions.assertNull(kerryStaffService.getWxInfoByMobileAndAppId("", ""));
    }

    @Test
    void getByAreaCodeDetail() {
        Assertions.assertNull(kerryStaffService.getByAreaCodeDetail(""));

        Mockito.when(profileServiceClient.getByAreaCodeDetail(Mockito.any())).thenReturn(null);
        Assertions.assertNull(kerryStaffService.getByAreaCodeDetail("123"));
    }

    @Test
    void getUserList() {
        Mockito.when(kerryStaffClient.getUserList(Mockito.any())).thenReturn(null);
        Assertions.assertNull(kerryStaffService.getUserList(null));
    }
}
