package com.points.service;

import com.kerryprops.kip.service.integral.common.enums.PointsRedemptionEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.mapper.TbIntegralShopRateMapper;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TbSetshoprateServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 11/27/2024 19:31
 **********************************************************************************************************************/

@Slf4j
class TbSetshoprateServiceTest {

    @InjectMocks
    private TbSetshoprateServiceImpl tbSetshoprateServiceImpl;
    @Mock
    private TbIntegralShopRateMapper tbIntegralShopRateMapper;
    @Mock
    private TbIntegralCategoryRateService tbIntegralCategoryRateService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private TbOnlineShopConfigService tbOnlineShopConfigService;
    @Mock
    private TbCashOutConfigService tbCashOutConfigService;
    @Mock
    private MallConfig mallConfig;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("salesToIntegral中PointsRedemptionEnum.REWARDS_MALL及getPoints全覆盖")
    void salesToIntegral() {
        SalesAutoPointsDto autoPointsDto = SalesAutoPointsDto
                .builder()
                .amount("5")
                .refundAmount("10")
                .member(TbMemberAsset.builder().build())
                .build();
        Assertions.assertEquals(0, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));
        SalesAutoPointsDto autoPointsDto1 = SalesAutoPointsDto
                .builder()
                .amount("5")
                .refundAmount("2")
                .member(TbMemberAsset.builder().build())
                .remark(PointsRedemptionEnum.REWARDS_MALL.getCode())
                .build();
        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertEquals(0, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto1));
        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbOnlineShopConfig.builder().money(BigDecimal.ZERO).build());
        Assertions.assertEquals(0, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto1));
        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbOnlineShopConfig.builder().money(BigDecimal.TEN).pointNum(BigDecimal.TEN).build());
        Assertions.assertEquals(3, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto1));
        Mockito.when(mallConfig.isTestGroup(Mockito.any())).thenReturn(true);
        Assertions.assertEquals(3, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto1));
    }

    @Test
    @DisplayName("salesToIntegral中PointsRedemptionEnum.POS_CASH_OUT, getDeductPoints全覆盖")
    void salesToIntegralCase1() {
        SalesAutoPointsDto autoPointsDto = SalesAutoPointsDto
                .builder()
                .amount("5")
                .member(TbMemberAsset.builder().build())
                .remark(PointsRedemptionEnum.POS_CASH_OUT.getCode())
                .build();
        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertEquals(0, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));
        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbOnlineShopConfig.builder().money(BigDecimal.ZERO).build());
        Assertions.assertEquals(0, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));
        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbOnlineShopConfig.builder().money(BigDecimal.TEN).pointNum(BigDecimal.TEN).build());
        Assertions.assertEquals(5, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));
    }

    @Test
    @DisplayName("salesToIntegral中else场景")
    void salesToIntegralCase2() {
        SalesAutoPointsDto autoPointsDto = SalesAutoPointsDto
                .builder()
                .amount("5")
                .member(TbMemberAsset.builder().build())
                .build();
        Mockito.when(tbMemberGradeService.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertEquals(0, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));

        Mockito.when(tbMemberGradeService.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGrade.builder().build());

        Mockito.when(tbIntegralShopRateMapper.getShopRateDto(Mockito.any()))
                .thenReturn(TbIntegralShopRate.builder().pointNum(BigDecimal.TEN).money(BigDecimal.TEN).build());
        Assertions.assertEquals(5, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));

        Mockito.when(tbIntegralShopRateMapper.getShopRateDto(Mockito.any()))
                .thenReturn(null);
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertEquals(0, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));

        TbBaseShop baseShop = TbBaseShop.builder().build();
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(baseShop);
        Assertions.assertEquals(0, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));

        baseShop.setFirstFormatCode("01");
        baseShop.setSecondFormatCode("02");
        baseShop.setThirdFormatCode("03");
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(true);
        Mockito.when(tbIntegralCategoryRateService.getCategoryRateList(Mockito.any()))
                .thenReturn(null);
        Assertions.assertEquals(0, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));

        Mockito.when(tbIntegralCategoryRateService.getCategoryRateList(Mockito.any()))
                .thenReturn(Arrays.asList(TbIntegralCategoryRate.builder().categoryId("01").money(BigDecimal.TEN).pointNum(BigDecimal.TEN).build(),
                        TbIntegralCategoryRate.builder().categoryId("02").money(BigDecimal.TEN).pointNum(BigDecimal.TEN).build(),
                        TbIntegralCategoryRate.builder().categoryId("03").money(BigDecimal.TEN).pointNum(BigDecimal.TEN).build()));
        Assertions.assertEquals(5, tbSetshoprateServiceImpl.salesToIntegral(autoPointsDto));
    }

    @Test
    @DisplayName("getMaxAmountOfStoreCashOut")
    void getMaxAmountOfStoreCashOut() {
        SalesAutoPointsDto autoPointsDto = SalesAutoPointsDto
                .builder()
                .amount("5")
                .shopId("05")
                .member(TbMemberAsset.builder().build())
                .build();
        Mockito.when(tbCashOutConfigService.getConfig(Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbSetshoprateServiceImpl.getMaxAmountOfStoreCashOut(autoPointsDto));

        Mockito.when(tbCashOutConfigService.getConfig(Mockito.any()))
                .thenReturn(TbCashOutConfig.builder().shopNo("01").build());
        Assertions.assertNotNull(tbSetshoprateServiceImpl.getMaxAmountOfStoreCashOut(autoPointsDto));

        TbCashOutConfig cashOutConfig = TbCashOutConfig.builder().shopNo("05").isLimit(1).money(BigDecimal.TEN).build();

        Mockito.when(tbCashOutConfigService.getConfig(Mockito.any()))
                .thenReturn(cashOutConfig);
        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbSetshoprateServiceImpl.getMaxAmountOfStoreCashOut(autoPointsDto));

        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbOnlineShopConfig.builder().money(BigDecimal.TEN).pointNum(BigDecimal.TEN).build());
        Assertions.assertNotNull(tbSetshoprateServiceImpl.getMaxAmountOfStoreCashOut(autoPointsDto));

        cashOutConfig.setIsLimit(0);
        Assertions.assertNotNull(tbSetshoprateServiceImpl.getMaxAmountOfStoreCashOut(autoPointsDto));
    }

    @Test
    void getMaxAmountOfShopCashOut() {
        SalesAutoPointsDto autoPointsDto = SalesAutoPointsDto
                .builder()
                .amount("5")
                .shopId("05")
                .member(TbMemberAsset.builder().build())
                .build();
        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbSetshoprateServiceImpl.getMaxAmountOfShopCashOut(autoPointsDto, TbCashOutConfig.builder().isLimit(1).build()));

        Mockito.when(tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbOnlineShopConfig.builder().money(BigDecimal.TEN).pointNum(BigDecimal.TEN).build());
        Assertions.assertNotNull(tbSetshoprateServiceImpl.getMaxAmountOfShopCashOut(autoPointsDto,
                TbCashOutConfig.builder().isLimit(1).money(BigDecimal.TEN).build()));

        Assertions.assertNotNull(tbSetshoprateServiceImpl.getMaxAmountOfShopCashOut(autoPointsDto,
                TbCashOutConfig.builder().isLimit(0).money(BigDecimal.TEN).build()));
    }
}
