package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeEffectiveConfig;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeChangeDetailMapper;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeEffectiveConfigMapper;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.service.impl.TbMemberGradeChangeDetailServiceImpl;
import com.kerryprops.kip.service.integral.service.impl.TbMemberGradeEffectiveConfigServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

@Slf4j
class TbMemberGradeEffectiveConfigServiceTest {

    @InjectMocks
    private TbMemberGradeEffectiveConfigServiceImpl tbMemberGradeEffectiveConfigServiceImpl;

    @Mock
    private TbMemberGradeEffectiveConfigMapper tbMemberGradeEffectiveConfigMapper;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findByGroupId() {
        Mockito.when(tbMemberGradeEffectiveConfigMapper.findByGroupId(Mockito.anyString()))
                .thenReturn(new TbMemberGradeEffectiveConfig());
        Assertions.assertNotNull(tbMemberGradeEffectiveConfigServiceImpl.findByGroupId(""));
    }

    @Test
    void getFutureByGroupId() {
        Mockito.when(tbMemberGradeEffectiveConfigMapper.findByGroupId(Mockito.anyString()))
                .thenReturn(new TbMemberGradeEffectiveConfig());
        Assertions.assertNotNull(tbMemberGradeEffectiveConfigServiceImpl
                .getFutureByGroupId(""));
    }
}