package com.points.service;

import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.mapper.TbInsensatePointsRepeatLogMapper;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.impl.TbInsensatePointsRepeatLogServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

@Slf4j
class TbInsensatePointsRepeatLogServiceTest {

    @InjectMocks
    private TbInsensatePointsRepeatLogServiceImpl tbInsensatePointsRepeatLogService;
    
    @Mock
    private TbInsensatePointsRepeatLogMapper tbInsensatePointsRepeatLogMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void saveRepeatLog() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().saleType(SaleTypeEnum.WECHAT.getValue()).build();
        TbSalesDetail salesDetail = TbSalesDetail.builder().build();
        Mockito.when(tbInsensatePointsRepeatLogMapper.getByTransactionIdAndOrigin(Mockito.any(), Mockito.anyInt()))
                .thenReturn(TbInsensatePointsRepeatLog.builder().build());
        Assertions.assertDoesNotThrow(() -> tbInsensatePointsRepeatLogService.saveRepeatLog(dto, salesDetail));

        Mockito.when(tbInsensatePointsRepeatLogMapper.getByTransactionIdAndOrigin(Mockito.any(), Mockito.anyInt()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbInsensatePointsRepeatLogService.saveRepeatLog(SalesAutoPointsDto.builder().saleType(SaleTypeEnum.ALIPAY.getValue()).build(), salesDetail));

    }
}