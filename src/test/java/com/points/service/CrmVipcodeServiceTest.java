package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.MemberRegisterService;
import com.kerryprops.kip.service.integral.service.impl.CrmVipcodeServiceImpl;
import com.kerryprops.kip.service.integral.service.promotion.impl.EachWeekPromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class CrmVipcodeServiceTest {

    @InjectMocks
    private CrmVipcodeServiceImpl crmVipcodeService;

    @Mock
    private MemberRegisterService memberRegisterService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getVipcode() {
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().vipcode("12345").build());
        Assertions.assertNotNull(crmVipcodeService.getVipcode("123", "123"));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertThrows(BizNotFoundException.class, () -> crmVipcodeService.getVipcode("123", "123"));
    }
}
