package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeMapper;
import com.kerryprops.kip.service.integral.service.TbMemberGradeWithCacheService;
import com.kerryprops.kip.service.integral.service.impl.TbMemberGradeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 07/31/2024 10:37
 **********************************************************************************************************************/

@Slf4j
class TbMemberGradeServiceTest {

    @InjectMocks
    private TbMemberGradeServiceImpl tbMemberGradeService;
    @Mock
    private TbMemberGradeMapper tbMemberGradeMapper;
    @Mock
    private TbMemberGradeWithCacheService tbMemberGradeWithCacheService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void queryGradeSortAscByGroupId() {
        Mockito.when(tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(Mockito.any())).thenReturn(Collections.emptyList());
        Assertions.assertNotNull(tbMemberGradeService.queryGradeSortAscByGroupId("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void getMapFutureByGroupId() {
        Assertions.assertNotNull(tbMemberGradeService.getMapFutureByGroupId("4028e3817c2b3f79017c2b48c54c0000"));

        Mockito.when(tbMemberGradeMapper.getGradeSortDescByGroupId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").name("01").build()));
        Assertions.assertNotNull(tbMemberGradeService.getMapFutureByGroupId("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void queryGradeSortDescByGroupId() {
        Assertions.assertNotNull(tbMemberGradeService.queryGradeSortDescByGroupId("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void queryGradeSortDescWithoutCacheByGroupId() {
        Assertions.assertNotNull(tbMemberGradeService.queryGradeSortDescWithoutCacheByGroupId("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void queryGradeSortAscWithoutCacheByGroupId() {
        Assertions.assertNotNull(tbMemberGradeService.queryGradeSortAscWithoutCacheByGroupId("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void getGradeSortAscByGroupIdWithException() {
        Assertions.assertThrows(Exception.class, ()-> tbMemberGradeService.getGradeSortAscByGroupIdWithException("4028e3817c2b3f79017c2b48c54c0000"));

        Mockito.when(tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").name("01").build()));
        Assertions.assertNotNull(tbMemberGradeService.getGradeSortAscByGroupIdWithException("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void queryByGroupIdAndGrade() {
        Assertions.assertNull(tbMemberGradeService.queryByGroupIdAndGrade("4028e3817c2b3f79017c2b48c54c0000", "03"));
    }

    @Test
    void queryMaxGroupGrade() {
        Assertions.assertNull(tbMemberGradeService.queryMaxGroupGrade("12345"));

        Mockito.when(tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").name("01").sort(1).build()));
        Assertions.assertNotNull(tbMemberGradeService.queryMaxGroupGrade("12345"));
    }

    @Test
    void queryMinGroupGrade() {
        Assertions.assertNull(tbMemberGradeService.queryMinGroupGrade("4028e3817c2b3f79017c2b48c54c0000"));
        Mockito.when(tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").name("01").sort(1).build()));
        Assertions.assertNotNull(tbMemberGradeService.queryMinGroupGrade("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void getGroupMiniGradeCode() {
        Assertions.assertNull(tbMemberGradeService.getGroupMiniGradeCode("4028e3817c2b3f79017c2b48c54c0000"));
        Mockito.when(tbMemberGradeMapper.getMinGradeByGroupId(Mockito.any()))
                .thenReturn(TbMemberGrade.builder().code("01").sort(1).build());
        Assertions.assertNotNull(tbMemberGradeService.getGroupMiniGradeCode("4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void getGradeList() {
        Assertions.assertNull(tbMemberGradeService.getGradeList("12345"));
        Mockito.when(tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").name("01").sort(1).build()));
        Assertions.assertNotNull(tbMemberGradeService.getGradeList("12345"));
    }

}
