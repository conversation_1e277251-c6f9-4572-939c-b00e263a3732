package com.points.service;

import com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto;
import com.kerryprops.kip.service.integral.service.TbSubscriptionRecordService;
import com.points.BaseTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> nancy
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/
class TbSubscriptionRecordServiceTest extends BaseTest {

    @Resource
    private TbSubscriptionRecordService tbSubscriptionRecordService;

    @Test
    void querySubscriptionDto() {
        Assertions.assertNull(tbSubscriptionRecordService.querySubscriptionDto(SubscriptionRecordQueryDto.builder().mallId("8aaa81cb7c836c6b017c83e46b110001").build()));
    }

    @Test
    void querySubscriptionList() {
        Assertions.assertNotNull(tbSubscriptionRecordService.querySubscriptionList(SubscriptionRecordQueryDto.builder().mallId("8aaa81cb7c836c6b017c83e46b110001").build()));
    }

    @Test
    void querySubscriptionUserIdList() {
        Assertions.assertNotNull(tbSubscriptionRecordService.querySubscriptionUserIdList(SubscriptionRecordQueryDto.builder().mallId("8aaa81cb7c836c6b017c83e46b110001").build()));
    }

}
