package com.points.service;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeEffectiveConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberRefine;
import com.kerryprops.kip.service.integral.entity.TbMemberRefineField;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeEffectiveConfigMapper;
import com.kerryprops.kip.service.integral.mapper.TbMemberRefineDetailMapper;
import com.kerryprops.kip.service.integral.mapper.TbMemberRefineMapper;
import com.kerryprops.kip.service.integral.model.dto.PerfectMemberActivityDto;
import com.kerryprops.kip.service.integral.service.EventTriggerService;
import com.kerryprops.kip.service.integral.service.impl.TbMemberGradeEffectiveConfigServiceImpl;
import com.kerryprops.kip.service.integral.service.impl.TbMemberRefineServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberRefineDetailResource;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberRefineResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.internal.exceptions.ExceptionIncludingMockitoWarnings;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;

@Slf4j
class TbMemberRefineServiceTest {

    @InjectMocks
    private TbMemberRefineServiceImpl tbMemberRefineServiceImpl;

    @Mock
    private TbMemberRefineMapper tbMemberRefineMapper;
    @Mock
    private TbMemberRefineDetailMapper tbMemberRefineDetailMapper;
    @Mock
    private EventTriggerService eventTriggerService;
    @Mock
    private Mapper mapper;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getByMallId() {
        Assertions.assertThrows(BizException.class, () -> tbMemberRefineServiceImpl.getByMallId(""));

        Assertions.assertNull(tbMemberRefineServiceImpl.getByMallId("12345"));

        Mockito.when(tbMemberRefineMapper.findByMallId(Mockito.anyString()))
                .thenReturn(TbMemberRefine.builder().synopsis("synopsis").build());
        Mockito.when(eventTriggerService.obtainRewardForInfo(Mockito.any(), Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(PerfectMemberActivityDto.builder().issuePoints("100").build()));
        Mockito.when(mapper.map(TbMemberRefine.builder().synopsis("synopsis").build(), TbMemberRefineResource.class))
                .thenReturn(TbMemberRefineResource.builder().synopsis("synopsis").build());
        Mockito.when(tbMemberRefineDetailMapper.findByConfigId(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberRefineField.builder().build()));
        Mockito.when(mapper.map(TbMemberRefineField.builder().build(), TbMemberRefineDetailResource.class))
                .thenReturn(TbMemberRefineDetailResource.builder().fieldType(1).build());

        Assertions.assertNotNull(tbMemberRefineServiceImpl.getByMallId("12345"));
    }

    @Test
    void memberRefineRequiredFields() {
        Mockito.when(tbMemberRefineDetailMapper.findMallRefineFields(Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbMemberRefineServiceImpl.memberRefineRequiredFields("12345"));

        Mockito.when(tbMemberRefineDetailMapper.findMallRefineFields(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberRefineField.builder().isRequired(1).build()));
        Assertions.assertNotNull(tbMemberRefineServiceImpl.memberRefineRequiredFields("12345"));
    }

}