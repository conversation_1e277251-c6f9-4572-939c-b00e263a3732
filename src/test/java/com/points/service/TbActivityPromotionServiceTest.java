package com.points.service;

import com.kerryprops.kip.service.integral.client.CipMiddleWareClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionJoinvipMapper;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionMapper;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionconditionMapper;
import com.kerryprops.kip.service.integral.model.dto.CipResultDto;
import com.kerryprops.kip.service.integral.model.dto.CrowdResultDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbActivityPromotionIntegralService;
import com.kerryprops.kip.service.integral.service.impl.TbActivityPromotionServiceImpl;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import com.kerryprops.kip.service.integral.service.promotion.impl.BirthdayPromotionRule;
import com.kerryprops.kip.service.integral.service.promotion.impl.PromotionRuleStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 12/30/2024 12:24
 **********************************************************************************************************************/
@Slf4j
class TbActivityPromotionServiceTest {

    @InjectMocks
    private TbActivityPromotionServiceImpl tbActivityPromotionService;
    @Mock
    private TbActivityPromotionMapper tbActivityPromotionMapper;
    @Mock
    private TbActivityPromotionconditionMapper tbActivityPromotionconditionMapper;
    @Mock
    private TbActivityPromotionJoinvipMapper tbActivityPromotionJoinvipMapper;
    @Mock
    private TbActivityPromotionIntegralService tbActivityPromotionIntegralService;
    @Mock
    private CipMiddleWareClient cipMiddleWareClient;
    @Mock
    private PromotionRuleStrategyService promotionRuleStrategyService;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void queryByMallId() {
        Assertions.assertNotNull(tbActivityPromotionService.queryByMallId("12345",
                "2025-03-03 11:00:00",
                "HKC001",
                "12345"));

        Mockito.when(tbActivityPromotionMapper.queryByMallIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbActivityPromotion.builder().shopId("12345").build()));
        // shopNo不为空 promotions为空
        Assertions.assertNotNull(tbActivityPromotionService.queryByMallId("12345",
                "2025-03-03 11:00:00",
                "HKC001",
                "12345"));

        // shopNo不为空  crowdIdList为空
        Mockito.when(tbActivityPromotionMapper.queryByMallIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbActivityPromotion.builder()
                        .shopId("HKC001").build()));
        Assertions.assertNotNull(tbActivityPromotionService.queryByMallId("12345",
                "2025-03-03 11:00:00",
                "HKC001",
                "12345"));

        // shopNo不为空  allstore cipMiddleWareClient没返回数据
        Mockito.when(tbActivityPromotionMapper.queryByMallIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbActivityPromotion.builder().crowdId("1")
                        .shopId(IntegralConstant.DEFAULT_STORE).build()));
        CipResultDto<List<CrowdResultDto>> resultDto = new CipResultDto<>();
        Mockito.when(cipMiddleWareClient.hasCrowdByUser(Mockito.any(), Mockito.any()))
                .thenReturn(resultDto);
        Assertions.assertNotNull(tbActivityPromotionService.queryByMallId("12345",
                "2025-03-03 11:00:00",
                "HKC001",
                "12345"));

        // shopNo为空 没有 过滤出mallId符合且hasCrowd为true的数据
        CipResultDto<List<CrowdResultDto>> resultDto1 = new CipResultDto<>();
        resultDto1.setData(Collections.singletonList(CrowdResultDto.builder().crowd_id("0039").build()));
        Mockito.when(cipMiddleWareClient.hasCrowdByUser(Mockito.any(), Mockito.any()))
                .thenReturn(resultDto1);
        Assertions.assertNotNull(tbActivityPromotionService.queryByMallId("12345",
                "2025-03-03 11:00:00",
                "",
                "12345"));

        //  cipMiddleWareClient进入异常 最终还是为空
        Mockito.when(cipMiddleWareClient.hasCrowdByUser(Mockito.any(), Mockito.any()))
                .thenThrow(new BizException(400, ""));
        Assertions.assertNotNull(tbActivityPromotionService.queryByMallId("12345",
                "2025-03-03 11:00:00",
                "",
                "12345"));
    }

    @Test
    void queryByMallIdCase1() {
        // conditions 和 vips均为空
        Mockito.when(tbActivityPromotionMapper.queryByMallIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(TbActivityPromotion.builder()
                                .id("12345")
                                .crowdId("2")
                                .shopId(IntegralConstant.DEFAULT_STORE).build(),
                        TbActivityPromotion.builder()
                                .id("12345")
                                .crowdId("103")
                                .shopId(IntegralConstant.DEFAULT_STORE).build(),
                        TbActivityPromotion.builder()
                                .shopId(IntegralConstant.DEFAULT_STORE).build()));
        CipResultDto<List<CrowdResultDto>> resultDto2 = new CipResultDto<>();
        resultDto2.setData(Collections.singletonList(CrowdResultDto.builder().crowd_id("103").hasCrowd(true).mall_id("12345").build()));
        Mockito.when(cipMiddleWareClient.hasCrowdByUser(Mockito.any(), Mockito.any()))
                .thenReturn(resultDto2);
        Assertions.assertNotNull(tbActivityPromotionService.queryByMallId("12345",
                "2025-03-03 11:00:00",
                "",
                "12345"));

        // conditions 和 vips均不为空
        Mockito.when(tbActivityPromotionconditionMapper.findByPromotionIds(Mockito.any()))
                .thenReturn(Collections.singletonList(TbActivityPromotionCondition.builder().promotionId("12345").promotionConditionGroupId("123").build()));
        Mockito.when(tbActivityPromotionJoinvipMapper.findByPromotionIds(Mockito.any()))
                .thenReturn(Collections.singletonList(TbActivityPromotionJoinvip.builder().promotionId("12345").build()));
        Assertions.assertNotNull(tbActivityPromotionService.queryByMallId("12345",
                "2025-03-03 11:00:00",
                "",
                "12345"));
    }

    @Test
    void handlePromotionActivity() {

        Assertions.assertNotNull(tbActivityPromotionService.handlePromotionActivity(SalesAutoPointsDto.builder()
                        .exchangePoints(10).member(TbMemberAsset.builder().build()).build(),
                true));

        Mockito.when(tbActivityPromotionMapper.queryByMallIdAndDate(Mockito.any(), Mockito.any()))
                        .thenReturn(Collections.singletonList(TbActivityPromotion
                                .builder()
                                .shopId(IntegralConstant.DEFAULT_STORE)
                                .conditions(Collections.singletonList(Collections.singletonList(TbActivityPromotionCondition.builder().build())))
                                .build()));
        Mockito.when(promotionRuleStrategyService.getRuleProcess(Mockito.any()))
                .thenReturn(new BirthdayPromotionRule());
        Assertions.assertNotNull(tbActivityPromotionService.handlePromotionActivity(
                SalesAutoPointsDto.builder().exchangePoints(10)
                        .exchangePoints(10).member(TbMemberAsset.builder().build())
                        .build(),
                true));

        // 进入固定加最高  dbWithDj==0
        Mockito.when(tbActivityPromotionMapper.queryByMallIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbActivityPromotion
                        .builder()
                        .shopId(IntegralConstant.DEFAULT_STORE)
                        .module("2")
                        .type("0")
                        .bonus(10)
                        .build()));
        Assertions.assertNotNull(tbActivityPromotionService.handlePromotionActivity(
                SalesAutoPointsDto.builder().exchangePoints(10)
                        .exchangePoints(10).member(TbMemberAsset.builder().build())
                        .build(),
                true));

        // 进入固定加最高  dbWithDj!=0
        Mockito.when(tbActivityPromotionMapper.queryByMallIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(TbActivityPromotion
                        .builder()
                        .shopId(IntegralConstant.DEFAULT_STORE)
                        .module("2")
                        .type("0")
                        .bonus(10)
                        .build(),
                        TbActivityPromotion
                                .builder()
                                .shopId(IntegralConstant.DEFAULT_STORE)
                                .module("1")
                                .times(BigDecimal.ONE)
                                .type("0")
                                .bonus(10)
                                .build()));
        Assertions.assertNotNull(tbActivityPromotionService.handlePromotionActivity(
                SalesAutoPointsDto.builder().exchangePoints(10)
                        .exchangePoints(10).member(TbMemberAsset.builder().build())
                        .build(),
                false));

        // 进入多倍 + 最高
        Mockito.when(tbActivityPromotionMapper.queryByMallIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(TbActivityPromotion
                                .builder()
                                .shopId(IntegralConstant.DEFAULT_STORE)
                                .module("2")
                                .type("0")
                                .bonus(0)
                                .build(),
                        TbActivityPromotion
                                .builder()
                                .shopId(IntegralConstant.DEFAULT_STORE)
                                .module("1")
                                .type("1")
                                .times(BigDecimal.TEN)
                                .build()));
        Assertions.assertNotNull(tbActivityPromotionService.handlePromotionActivity(
                SalesAutoPointsDto.builder().exchangePoints(10)
                        .exchangePoints(10).member(TbMemberAsset.builder().build())
                        .build(),
                false));

        // 进入多倍 + 最高 dbWithDj不为0
        Mockito.when(tbActivityPromotionMapper.queryByMallIdAndDate(Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(TbActivityPromotion
                                .builder()
                                .shopId(IntegralConstant.DEFAULT_STORE)
                                .module("2")
                                .type("0")
                                .bonus(0)
                                .build(),
                        TbActivityPromotion
                                .builder()
                                .shopId(IntegralConstant.DEFAULT_STORE)
                                .module("1")
                                .type("0")
                                .times(BigDecimal.TEN)
                                .build(),
                        TbActivityPromotion
                                .builder()
                                .shopId(IntegralConstant.DEFAULT_STORE)
                                .module("1")
                                .type("1")
                                .times(BigDecimal.TEN)
                                .build()));
        Assertions.assertNotNull(tbActivityPromotionService.handlePromotionActivity(
                SalesAutoPointsDto.builder().exchangePoints(10)
                        .exchangePoints(10).member(TbMemberAsset.builder().build())
                        .build(),
                false));


    }



}
