package com.points.service;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import com.kerryprops.kip.service.integral.service.TbPointsDetailService;
import com.kerryprops.kip.service.integral.service.impl.TbMemberPointsChangeServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.MemberPointsChangeResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - member-points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> nancy
 * Created Date - 05/30/2024 15:45
 **********************************************************************************************************************/

@Slf4j
class TbMemberPointsChangeServiceTest {

    @InjectMocks
    private TbMemberPointsChangeServiceImpl tbMemberPointsChangeServiceImpl;

    @Mock
    private TbMemberAssetService tbMemberAssetService;

    @Mock
    private Mapper mapper;

    @Mock
    private MallConfig mallConfig;

    @Mock
    private TbPointsDetailService tbPointsDetailService;

    @Mock
    private RabbitMqService rabbitMqService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("会员不为空")
    void changeMemberPointsCase1() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().exchangePoints(-10)
                .saleType(SaleTypeEnum.WECHAT.getValue()).member(TbMemberAsset.builder().kipUserId("12345").build()).build();
        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndKipUserId(Mockito.any(), Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(-50).status("1").build());
        Mockito.when(mallConfig.isAllowNegativePointsGroup(Mockito.any())).thenReturn(true);
        Mockito.when(tbMemberAssetService.updatePoints(Mockito.any())).thenReturn(1);
        Assertions.assertDoesNotThrow(() -> tbMemberPointsChangeServiceImpl.changeMemberPoints(dto));

        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder().exchangePoints(-10).saleType(SaleTypeEnum.WECHAT.getValue())
                .member(TbMemberAsset.builder().kipUserId("false").build()).build();
        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndMobile(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().currentPoints(-50).status("1").build());
        Mockito.when(mallConfig.isAllowNegativePointsGroup(Mockito.any())).thenReturn(false);
        Mockito.when(tbMemberAssetService.updatePoints(Mockito.any())).thenReturn(0);
        Assertions.assertDoesNotThrow(() -> tbMemberPointsChangeServiceImpl.changeMemberPoints(dto1));

        SalesAutoPointsDto dto2 = SalesAutoPointsDto.builder().exchangePoints(10).saleType(SaleTypeEnum.WECHAT.getValue())
                .member(TbMemberAsset.builder().kipUserId("").build()).build();
        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndMobile(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().currentPoints(-50).status("1").build());
        Mockito.when(mallConfig.isAllowNegativePointsGroup(Mockito.any())).thenReturn(true);
        Mockito.when(tbMemberAssetService.updatePoints(Mockito.any())).thenReturn(1);
        Assertions.assertDoesNotThrow(() -> tbMemberPointsChangeServiceImpl.changeMemberPoints(dto2));
    }

    @Test
    @DisplayName("会员为空")
    void changeMemberPointsCase2() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().exchangePoints(-10)
                .saleType(SaleTypeEnum.WECHAT.getValue()).build();
        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndVipCode(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().currentPoints(-50).status("1").build());
        Mockito.when(mallConfig.isAllowNegativePointsGroup(Mockito.any())).thenReturn(true);
        Mockito.when(tbMemberAssetService.updatePoints(Mockito.any())).thenReturn(1);
        Assertions.assertDoesNotThrow(() -> tbMemberPointsChangeServiceImpl.changeMemberPoints(dto));
    }

    @Test
    void updateMemberPoints() {
        Assertions.assertThrows(Exception.class, () ->tbMemberPointsChangeServiceImpl.updateMemberPoints(MemberPointsChangeDto
                .builder()
                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                .vipcode("kerry130996")
                .changePointsNum(0)
                .build())
        );
        Assertions.assertThrows(Exception.class, () ->tbMemberPointsChangeServiceImpl.updateMemberPoints(MemberPointsChangeDto
                .builder()
                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                .vipcode("kerry130996")
                .changePointsNum(10)
                .checkMemberStatus(1)
                .adjustPointsId("12345")
                .build())
        );
        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndVipCode("8a888a087cc59dc0017cc622f9ad0000", "kerry130996"))
                .thenReturn(TbMemberAsset
                        .builder()
                        .currentPoints(-50)
                        .status("1")
                        .build()
                );
        Assertions.assertThrows(Exception.class, () ->tbMemberPointsChangeServiceImpl.updateMemberPoints(MemberPointsChangeDto
                .builder()
                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                .vipcode("kerry130996")
                .changePointsNum(-10)
                .build())
        );
        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndVipCode("8a888a087cc59dc0017cc622f9ad0000", "kerry130996"))
                .thenReturn(TbMemberAsset
                        .builder()
                        .currentPoints(5)
                        .status("1")
                        .build()
                );
        Assertions.assertThrows(Exception.class, () ->tbMemberPointsChangeServiceImpl.updateMemberPoints(MemberPointsChangeDto
                .builder()
                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                .vipcode("kerry130996")
                .changePointsNum(-10)
                .build())
        );
        Mockito.when(mallConfig.isAllowNegativePointsGroup("8a888a087cc59dc0017cc622f9ad0000")).thenReturn(Boolean.TRUE);
        Mockito.when(tbMemberAssetService.updatePoints(Mockito.any())).thenReturn(1);
        Assertions.assertDoesNotThrow(() ->tbMemberPointsChangeServiceImpl.updateMemberPoints(MemberPointsChangeDto
                .builder()
                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                .vipcode("kerry130996")
                .changePointsNum(10)
                .build())
        );
        Mockito.when(tbMemberAssetService.updatePoints(Mockito.any())).thenReturn(0);
        Assertions.assertThrows(Exception.class, () ->tbMemberPointsChangeServiceImpl.updateMemberPoints(MemberPointsChangeDto
                .builder()
                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                .vipcode("kerry130996")
                .changePointsNum(10)
                .build())
        );
    }

    @Test
    void updateMemberPoints1() {
        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndVipCode("8a888a087cc59dc0017cc622f9ad0000", "kerry130996"))
                .thenReturn(TbMemberAsset
                        .builder()
                        .currentPoints(-50)
                        .status("1")
                        .build()
                );
        Mockito.when(mallConfig.isAllowNegativePointsGroup("8a888a087cc59dc0017cc622f9ad0000")).thenReturn(Boolean.TRUE);
        Mockito.when(tbMemberAssetService.updatePoints(TbMemberAsset.builder().currentPoints(-50).status("1").build())).thenReturn(1);
        Assertions.assertNotNull(tbMemberPointsChangeServiceImpl.updateMemberPoints(MemberPointsChangeDto
                .builder()
                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                .vipcode("kerry130996")
                        .dictValue("RDB002")
                .changePointsNum(100)
                .build())
        );
    }

    @Test
    void batchAdjustPoints() {
        Assertions.assertThrows(Exception.class, () ->
                tbMemberPointsChangeServiceImpl.batchAdjustPoints(
                        Collections.singletonList(MemberPointsChangeResource.builder().build())));

        Assertions.assertThrows(Exception.class, () ->
                tbMemberPointsChangeServiceImpl.batchAdjustPoints(
                        Collections.singletonList(MemberPointsChangeResource
                                .builder()
                                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                                .mallId("8a888abd8ceb6c70018d117f469c0010")
                                .vipcode("kerry130996")
                                .saleType("1")
                                .dictValue("XS0025")
                                .changePointsNum(0)
                                .build())));

        List<MemberPointsChangeResource> list = Arrays.asList(
                MemberPointsChangeResource
                        .builder()
                        .groupId("8a888a087cc59dc0017cc622f9ad0000")
                        .mallId("8a888abd8ceb6c70018d117f469c0010")
                        .vipcode("kerry130996")
                        .saleType("1")
                        .dictValue("XS0025")
                        .changePointsNum(100)
                        .build(),
                MemberPointsChangeResource
                        .builder()
                        .groupId("8a888a087cc59dc0017cc622f9ad0000")
                        .mallId("8a888abd8ceb6c70018d117f469c0010")
                        .vipcode("kerry130996")
                        .saleType("1")
                        .dictValue("XS0025")
                        .changePointsNum(0)
                        .build()
        );
        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndVipCode(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset
                        .builder()
                        .currentPoints(-50)
                        .status("1")
                        .build()
                );
        Mockito.when(mapper.map(MemberPointsChangeResource
                .builder()
                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                .mallId("8a888abd8ceb6c70018d117f469c0010")
                .vipcode("kerry130996")
                .saleType("1")
                .dictValue("XS0025")
                .changePointsNum(100)
                .build(), MemberPointsChangeDto.class)).thenReturn(MemberPointsChangeDto
                .builder()
                .groupId("8a888a087cc59dc0017cc622f9ad0000")
                .mallId("8a888abd8ceb6c70018d117f469c0010")
                .vipcode("kerry130996")
                .saleType("1")
                .dictValue("XS0025")
                .changePointsNum(100)
                .build());
        Mockito.when(mallConfig.isAllowNegativePointsGroup("8a888a087cc59dc0017cc622f9ad0000")).thenReturn(Boolean.TRUE);
        Mockito.when(tbMemberAssetService.updatePoints(TbMemberAsset.builder().currentPoints(-50).status("1").build())).thenReturn(1);
        Assertions.assertDoesNotThrow(() -> tbMemberPointsChangeServiceImpl.batchAdjustPoints(list));
    }
}
