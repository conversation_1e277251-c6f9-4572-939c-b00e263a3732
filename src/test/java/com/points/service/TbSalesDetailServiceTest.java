package com.points.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper;
import com.kerryprops.kip.service.integral.mapper.TbSalesDetailMapper;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TbSalesDetailServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 03/13/2024 14:39
 **********************************************************************************************************************/
class TbSalesDetailServiceTest {

    @InjectMocks
    private TbSalesDetailServiceImpl tbSalesDetailService;

    @Mock
    private TbSalesDetailMapper tbSalesDetailMapper;
    @Mock
    private ProfileServiceClient profileServiceClient;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private RedisService redisService;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private TbMemberAssetMapper tbMemberAssetMapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void saveMemberSale() {
        Date now = new Date();
        TbSalesDetail detail = TbSalesDetail.builder()
                .crmId(IdUtil.simpleUUID())
                .imageUrl("/test.jpg")
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .vipcode("KERRY00000000")
                .kipUserId("1111111")
                .shopNo("HKC0000001")
                .shopName("测试店铺")
                .orderNo("1111111111111")
                // 代表已退款
                .status(1)
                .createUser("admin")
                .createDate(now)
                .saleDate(now)
                .updateDate(now)
                .updateUser("admin")
                .saleType("1")
                .refundPointsId("11111111")
                .remark("无单退货")
                // 退还积分
                .refundPointsNum(10)
                // 退款金额
                .refundAmount(BigDecimal.ONE)
                // 无单退货的金额和有单退货金额记一样的
                .totalAmount(BigDecimal.ONE)
                .payAmount(BigDecimal.ONE)
                .pointsId("11111111")
                .pointsNum(0)
                .orgGrade("1")
                .orgPoints(100)
                .build();
        detail.setDefault();
        // 不能用saveSaleData方法，因为无单退货只保存数据，没有其它业务
        assertDoesNotThrow(() -> tbSalesDetailService.saveMemberSale(detail));
    }

    @Test
    void saveNoOrderRefundRecord() {
        SalesAutoPointsDto queryDto = SalesAutoPointsDto
                .builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .vipcode("KERRY00000000")
                .refundAmount("100")
                .saleDate("2024-01-01 11:00:00")
                .exchangePoints(100)
                .member(TbMemberAsset.builder().currentPoints(100).build())
                .saleNo("11111").build();
        when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().shopName("12345").build());
        assertDoesNotThrow(() -> tbSalesDetailService.saveNoOrderRefundRecord(queryDto));

        SalesAutoPointsDto queryDto1 = SalesAutoPointsDto
                .builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .vipcode("KERRY00000000")
                .refundAmount("100")
                .saleDate("2024-01-01 11:00:00")
                .exchangePoints(100)
                .member(TbMemberAsset.builder().currentPoints(100).build())
                .saleNo("11111")
                .remark("XS0001")
                .build();
        assertDoesNotThrow(() -> tbSalesDetailService.saveNoOrderRefundRecord(queryDto1));
    }

    @Test
    void checkSaleRecordRepeatedOrNot() {
        Assertions.assertNull(tbSalesDetailService.checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto.builder().build()));
    }

    @Test
    void checkSaleRepeatedByConditions() {
        List<MemberSaleRepeatQueryDto> list = Collections.singletonList(MemberSaleRepeatQueryDto.builder().build());
        assertDoesNotThrow(() -> tbSalesDetailService.checkSaleRepeatedByConditions(list));
    }

    @Test
    void querySaleBySellNo() {
        Assertions.assertNull(tbSalesDetailService.querySaleBySellNo("11111"));
    }

    @Test
    void queryBySellNoAndSaleType() {
        Assertions.assertNull(tbSalesDetailService.queryBySellNoAndSaleType("11111", "1"));
    }

    @Test
    void queryMemberMonthSalesNumber() {
        MemberSaleMonthNumberQueryDto queryDto = MemberSaleMonthNumberQueryDto.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY00000000")
                .startDate("2024-03-01 00:00:00").endDate("2024-03-15 23:59:59").build();
        assertDoesNotThrow(() ->tbSalesDetailService.queryMemberMonthSalesNumber(queryDto));
    }

    @Test
    void queryMemberSaleCountBetweenTime() {
        Assertions.assertEquals(0, tbSalesDetailService.queryMemberSaleCountBetweenTime(MemberSaleMonthNumberQueryDto.builder().build()));
    }

    @Test
    void selectBySellNoAndMallId() {
        Assertions.assertNull(tbSalesDetailService.selectBySellNoAndMallId("11111", "8aaa80b47c784020017c78b00d060022"));
    }

    @Test
    @DisplayName("totalAmount为空，退款金额为0，imgUrl为空")
    void saveSaleData() {
        SalesAutoPointsDto queryDto = SalesAutoPointsDto
                .builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .vipcode("KERRY00000000")
                .refundAmount("0")
                .saleDate("2024-01-01 11:00:00")
                .exchangePoints(100)
                .member(TbMemberAsset.builder().currentPoints(100).build())
                .saleNo("11111")
                .baseShop(TbBaseShop.builder().retailBrandName("HKC0001").build())
                .discountAmount("10")
                .build();
        assertDoesNotThrow(() ->tbSalesDetailService.saveSaleData(queryDto));
    }

    @Test
    @DisplayName("totalAmount不为空，退款金额为0，imgUrl为空")
    void saveSaleDataCase1() {
        SalesAutoPointsDto queryDto = SalesAutoPointsDto
                .builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .vipcode("KERRY00000000")
                .refundAmount("10")
                .saleDate("2024-01-01 11:00:00")
                .exchangePoints(100)
                .member(TbMemberAsset.builder().currentPoints(100).build())
                .saleNo("11111")
                .totalAmount("100")
                .baseShop(TbBaseShop.builder().retailBrandName("HKC0001").build())
                .discountAmount("10")
                .imageUrl("1234")
                .build();
        assertDoesNotThrow(() ->tbSalesDetailService.saveSaleData(queryDto));

        SalesAutoPointsDto queryDto1 = SalesAutoPointsDto
                .builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .vipcode("KERRY00000000")
                .refundAmount("10")
                .saleDate("2024-01-01 11:00:00")
                .exchangePoints(100)
                .member(TbMemberAsset.builder().currentPoints(100).kipUserId("123456").build())
                .saleNo("11111")
                .totalAmount("100")
                .baseShop(TbBaseShop.builder().retailBrandName("HKC0001").build())
                .discountAmount("10")
                .imageUrl("1234")
                .remark(IntegralConstant.REWARDS_MALL)
                .build();
        when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        assertDoesNotThrow(() ->tbSalesDetailService.saveSaleData(queryDto1));

        when(profileServiceClient.getIdentityResponse(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(CustomerIdentityDto.builder().office(true).apartment(true).residence(true).build()));
        assertDoesNotThrow(() ->tbSalesDetailService.saveSaleData(queryDto1));
    }

    @Test
    void getMemberSaleAmountBetweenTime() {
        when(redisService.hGet(Mockito.anyString(), Mockito.anyString())).thenReturn(100);
        Assertions.assertNotNull(tbSalesDetailService.getMemberSaleAmountBetweenTime(
                TbMemberAsset.builder().build(),
                TbMemberGradeRule.builder().build(),
                DateUtil.date(),
                DateUtil.date(),
                Arrays.asList("123", "124")));

        when(redisService.hGet(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Assertions.assertNotNull(tbSalesDetailService.getMemberSaleAmountBetweenTime(
                TbMemberAsset.builder().build(),
                TbMemberGradeRule.builder().id(1L).build(),
                DateUtil.date(),
                DateUtil.date(),
                Arrays.asList("123", "124")));
    }

    @Test
    void getMemberSaleAmountBetweenDate() {
        when(redisService.hGet(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        when(tbSalesDetailMapper.getSaleAmountBetweenTime(Mockito.any()))
                .thenReturn(TbSalesDetail.builder().payAmount(BigDecimal.TEN).refundAmount(BigDecimal.ONE).build());
        Assertions.assertNotNull(tbSalesDetailService.getMemberSaleAmountBetweenDate(
                TbMemberAsset.builder().build(),
                Collections.singletonList("123"),
                DateUtil.date(),
                DateUtil.date(),
                123L));
        Assertions.assertNotNull(tbSalesDetailService.getMemberSaleAmountBetweenDate(
                TbMemberAsset.builder().build(),
                Collections.singletonList("123"),
                null,
                null,
                123L));
    }

    @Test
    void checkMemberSaleAmountWhetherRelegationSucceeded() {
        when(tbSalesDetailMapper.checkMemberSaleAmountWhetherRelegationSucceeded(Mockito.any())).thenReturn(1);
        Assertions.assertTrue(tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(
                TbMemberAsset.builder().build(),
                TbMemberGradeRule.builder().build(),
                DateUtil.date(),
                DateUtil.date(),
                Collections.singletonList("123")));

        when(tbSalesDetailMapper.checkMemberSaleAmountWhetherRelegationSucceeded(Mockito.any())).thenReturn(-1);
        Assertions.assertFalse(tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(
                TbMemberAsset.builder().build(),
                TbMemberGradeRule.builder().money(new BigDecimal(-1)).build(),
                null,
                null,
                Collections.singletonList("123")));
    }

    @Test
    void initMemberGradeData() {
        when(mallConfig.getList()).thenReturn(Collections.singletonList(MallItem.builder().build()));
        assertDoesNotThrow(() -> tbSalesDetailService.initMemberGradeData(""));

        when(tbSalesDetailMapper.initMemberGradeData(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbSalesDetail.builder().build()));
        assertDoesNotThrow(() -> tbSalesDetailService.initMemberGradeData(""));
    }

    @Test
    void modifyMemberSaleShopId() {
        assertDoesNotThrow(() -> tbSalesDetailService.modifyMemberSaleShopId("", "", ""));

        assertDoesNotThrow(() -> tbSalesDetailService.modifyMemberSaleShopId("1,2", "1", "1"));

        assertDoesNotThrow(() -> tbSalesDetailService.modifyMemberSaleShopId("1,2", "1", "1,2"));

        when(tbSalesDetailMapper.getShopMemberSalesIds(Mockito.any(), Mockito.any())).thenReturn(Collections.singletonList(1L));
        assertDoesNotThrow(() -> tbSalesDetailService.modifyMemberSaleShopId("1,2", "1", "1,2"));
    }

    @Test
    void findSalesMap() {
        Assertions.assertNotNull(tbSalesDetailService.findSalesMap("", "", "", Collections.emptyList()));
    }

    @Test
    void getSalesList() {
        Assertions.assertNotNull(tbSalesDetailService.getSalesList("", "", "", null));

        Assertions.assertNotNull(tbSalesDetailService.getSalesList("", "", "", Collections.singletonList("123")));

        when(tbSalesDetailMapper.findSalesList(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbSalesDetail.builder().orderNo("123").build()));
        Assertions.assertNotNull(tbSalesDetailService.getSalesList("", "", "", Collections.singletonList("123")));

        when(tbSalesDetailMapper.findSalesList(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbSalesDetail.builder().orderNo("123").shopNo("123").build()));
        Map<String, TenantInfoVo> nameMap = new HashMap<>();
        nameMap.put("123", TenantInfoVo.builder().name("123").build());
        when(hiveVasService.getTenantInfoByShopNos(Mockito.anyList()))
                .thenReturn(nameMap);
        Assertions.assertNotNull(tbSalesDetailService.getSalesList("", "", "", Collections.singletonList("123")));
    }

    @Test
    void memberSaleRefundProcess() {
        assertDoesNotThrow(() -> tbSalesDetailService.memberSaleRefundProcess(123L));

        when(tbSalesDetailMapper.getMemberSaleById(Mockito.any())).thenReturn(TbSalesDetail.builder().build());
        assertDoesNotThrow(() -> tbSalesDetailService.memberSaleRefundProcess(123L));

        when(tbMemberAssetService.queryMemberByGroupIdAndVipCode(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        assertDoesNotThrow(() -> tbSalesDetailService.memberSaleRefundProcess(123L));
    }

    @Test
    void getMemberSaleById() {
        assertDoesNotThrow(() -> tbSalesDetailService.getMemberSaleById(123L));
    }

    @Test
    void getSaleListCreateAsc() {
        assertDoesNotThrow(() -> tbSalesDetailService.getSaleListCreateAsc("", "", DateUtil.date(), DateUtil.date(), Collections.singletonList("123")));
    }

    @Test
    void checkSalesUpgradeGrade() {
        assertDoesNotThrow(() -> tbSalesDetailService.checkSalesUpgradeGrade("", "", ""));

        when(tbSalesDetailMapper.checkSalesUpgradeGrade(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList("123"));
        assertDoesNotThrow(() -> tbSalesDetailService.checkSalesUpgradeGrade("", "", ""));

        when(tbSalesDetailMapper.getOneSalesBetweenTime(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("123");
        assertDoesNotThrow(() -> tbSalesDetailService.checkSalesUpgradeGrade("", "", ""));
    }

    @Test
    void getGradeRuleCacheAmount() {
        Assertions.assertNull(tbSalesDetailService.getGradeRuleCacheAmount(TbMemberGradeRule.builder().build(), TbMemberAsset.builder().build()));

        when(redisService.hGet(Mockito.any(), Mockito.any())).thenReturn(100);
        Assertions.assertNotNull(tbSalesDetailService.getGradeRuleCacheAmount(TbMemberGradeRule.builder().build(), TbMemberAsset.builder().build()));

        when(redisService.hGet(Mockito.any(), Mockito.any())).thenThrow(new RuntimeException());
        Assertions.assertNull(tbSalesDetailService.getGradeRuleCacheAmount(TbMemberGradeRule.builder().build(), TbMemberAsset.builder().build()));
    }

    @Test
    void checkSaleIsIntercept() {
        assertDoesNotThrow(() -> tbSalesDetailService.checkSaleIsIntercept(SaleDetailQueryDto.builder().build()));
    }

    @Test
    void getMemberSaleList() {
        assertDoesNotThrow(() -> tbSalesDetailService.getMemberSaleList("", ""));
    }

    @Test
    void executeSql() {
        assertDoesNotThrow(() -> tbSalesDetailService.executeSql(""));
    }

    @Test
    void querySqlResult() {
        assertDoesNotThrow(() -> tbSalesDetailService.querySqlResult(""));
    }

    @Test
    void accumulatedSalesAmount() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailService.accumulatedSalesAmount(resource));
    }

    @Test
    void consumeShopCount() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailService.consumeShopCount(resource));
    }

    @Test
    void consumeFrequency() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailService.consumeFrequency(resource));
    }

    @Test
    void consumeDays() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailService.consumeDays(resource));
    }

    @Test
    void badgeShopGroupSalesDetails() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailService.badgeShopGroupSalesDetails(Collections.singletonList(resource)));
    }

    @Test
    void gradeChangeTriggerMemberUpgrade() {
        assertDoesNotThrow(() -> tbSalesDetailService.gradeChangeTriggerMemberUpgrade("4028e3817c2b3f79017c2b48c54c0000", "2024-01-01 00:00:00"));
    }

    @Test
    void gradeChangeTriggerMemberUpgrade1() {
        List<String> vipcodes = new ArrayList<>();
        for (int i=0; i<10; i++) {
            vipcodes.add("KERRY0000000" + i);
        }
        when(tbSalesDetailMapper.gradeChangeTriggerMemberUpgrade(any(), any())).thenReturn(vipcodes);
        when(tbMemberAssetMapper.queryMemberByGroupIdAndVipcode("4028e3817c2b3f79017c2b48c54c0000", "KERRY00000002")).thenReturn(TbMemberAsset.builder().id(10L)
                .currentPoints(0).groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY00000002").build());
        when(tbMemberAssetMapper.queryMemberByGroupIdAndVipcode("4028e3817c2b3f79017c2b48c54c0000", "KERRY00000006")).thenReturn(TbMemberAsset.builder().id(10L)
                .currentPoints(0).groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY00000006").build());
        when(tbSalesDetailMapper.getLatestSalesRecord("4028e3817c2b3f79017c2b48c54c0000", "KERRY00000006")).thenReturn(TbSalesDetail.builder().id(10L)
                .saleDate(new Date()).groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY00000006").payAmount(BigDecimal.valueOf(100)).build());
        assertDoesNotThrow(() -> tbSalesDetailService.gradeChangeTriggerMemberUpgrade("4028e3817c2b3f79017c2b48c54c0000", "2024-01-01 00:00:00"));
    }


}
