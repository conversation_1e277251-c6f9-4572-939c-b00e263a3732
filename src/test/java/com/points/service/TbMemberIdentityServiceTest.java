package com.points.service;

import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.MemberIdentityInfo;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TbMemberIdentityServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.MemberIdentityResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

@Slf4j
class TbMemberIdentityServiceTest {

    @InjectMocks
    private TbMemberIdentityServiceImpl tbMemberIdentityService;

    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private ProfileServiceClient profileServiceClient;
    @Mock
    private TbMemberGradeRuleService tbMemberGradeRuleService;
    @Mock
    private TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("lbs为空,mallItem为空, sendMemberAddIdentityReward全覆盖")
    void kipSyncMemberIdentityProcess() {
        MemberIdentityResource resource = MemberIdentityResource.builder().lbsId("").projectId("190").build();
        Mockito.when(mallConfig.getByProjectId(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));

        MemberIdentityResource resource1 = MemberIdentityResource.builder().lbsId("").projectId("").auditType("auditType").build();
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource1));
    }

    @Test
    @DisplayName("lbs不为空")
    void kipSyncMemberIdentityProcessCase1() {
        MemberIdentityResource resource = MemberIdentityResource.builder().lbsId("123").projectId("190").status(3).auditType("auditType").build();
        Mockito.when(mallConfig.getByProjectId(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));

        MemberIdentityResource resource1 = MemberIdentityResource.builder().lbsId("123").projectId("190").status(2).auditType("auditType").build();
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource1));
    }

    @Test
    @DisplayName("lbs不为空, mall不为空")
    void kipSyncMemberIdentityProcessCase2() {
        MemberIdentityResource resource = MemberIdentityResource.builder().lbsId("123").projectId("190").status(3).auditType("auditType").build();
        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any())).thenReturn(MallItem.builder().mallId("01").build());
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));

        Mockito.when(tbMemberAssetService.getMemberWithProfileInfo(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("02").build());
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));

        Mockito.when(profileServiceClient.getProjectIdentityResponse(Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(MemberIdentityInfo.builder().apartmentMember("1").build(),
                        MemberIdentityInfo.builder().officeMember("1").build()));
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));

        Mockito.when(tbMemberGradeRuleService.queryByGroupIdAndRuleType(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGradeRule.builder().mallId("02").build()));
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));

        Mockito.when(tbMemberGradeRuleService.queryByGroupIdAndRuleType(Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGradeRule.builder().mallId("01").gradeCode("02").build(),
                        TbMemberGradeRule.builder().mallId("01").gradeCode("02").certification("1123344").build(),
                        TbMemberGradeRule.builder().mallId("01").gradeCode("03").certification("{\"office\":\"1\",\"apartment\":\"1\"}").build()));
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));

        Mockito.when(tbMemberGradeService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").build(), TbMemberGrade.builder().code("03").upGradationStatus(0).build()));
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));

        Mockito.when(tbMemberGradeService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").build(), TbMemberGrade.builder().code("02").upGradationStatus(0).build()));
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));

        Mockito.when(tbMemberGradeService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").sort(1).build(), TbMemberGrade.builder().code("02").sort(2).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("03").sort(3).upGradationStatus(1).build()));
        Assertions.assertDoesNotThrow (() -> tbMemberIdentityService.kipSyncMemberIdentityProcess(resource));


    }

}