package com.points.service;

import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig;
import com.kerryprops.kip.service.integral.mapper.TbAutoPointsConfigMapper;
import com.kerryprops.kip.service.integral.model.dto.HiveLbsInfoDto;
import com.kerryprops.kip.service.integral.model.dto.ProjectDto;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.service.impl.TbAutoPointsConfigServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class TbAutoPointsConfigServiceTest {

    @InjectMocks
    private TbAutoPointsConfigServiceImpl tbAutoPointsConfigServiceImpl;

    @Mock
    private TbAutoPointsConfigMapper tbAutoPointsConfigMapper;
    @Mock
    private HiveVasClient hiveVasClient;
    @Mock
    private RedisService redisService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findByLbsId() {
        Mockito.when(tbAutoPointsConfigMapper.findByLbsId(Mockito.anyString())).thenReturn(TbAutoPointsConfig.builder().build());
        Assertions.assertNotNull(tbAutoPointsConfigServiceImpl.findByLbsId(Mockito.anyString()));
    }

    @Test
    void saveConfig() {
        Mockito.when(tbAutoPointsConfigMapper.findByLbsId(Mockito.any())).thenReturn(TbAutoPointsConfig.builder().build());
        Assertions.assertThrows(Exception.class, () -> tbAutoPointsConfigServiceImpl.saveConfig(TbAutoPointsConfig.builder().build()));

        Mockito.when(tbAutoPointsConfigMapper.findByLbsId(Mockito.any())).thenReturn(null);
        Mockito.when(hiveVasClient.getLbsInfo(Mockito.any())).thenReturn(Collections.singletonList(HiveLbsInfoDto.builder().project(ProjectDto.builder().build()).build()));
        Assertions.assertDoesNotThrow(() -> tbAutoPointsConfigServiceImpl.saveConfig(TbAutoPointsConfig.builder().build()));
    }

    @Test
    void updateConfig() {
        Assertions.assertThrows(Exception.class, () -> tbAutoPointsConfigServiceImpl.updateConfig(TbAutoPointsConfig.builder().build()));

        Mockito.when(tbAutoPointsConfigMapper.selectById(Mockito.any())).thenReturn(TbAutoPointsConfig.builder().build());
        Assertions.assertDoesNotThrow(() -> tbAutoPointsConfigServiceImpl.updateConfig(TbAutoPointsConfig.builder().build()));
    }

    @Test
    void removeConfig() {
        Assertions.assertThrows(Exception.class, () -> tbAutoPointsConfigServiceImpl.removeConfig(Mockito.anyLong()));

        Mockito.when(tbAutoPointsConfigMapper.selectById(Mockito.any())).thenReturn(TbAutoPointsConfig.builder().build());
        Assertions.assertDoesNotThrow(() -> tbAutoPointsConfigServiceImpl.removeConfig(Mockito.anyLong()));
    }

    @Test
    void findById() {
        Mockito.when(tbAutoPointsConfigMapper.selectById(Mockito.anyLong())).thenReturn(TbAutoPointsConfig.builder().build());
        Assertions.assertDoesNotThrow(() -> tbAutoPointsConfigServiceImpl.findById(Mockito.anyLong()));
    }

    @Test
    void list() {
        Mockito.when(tbAutoPointsConfigMapper.listData(Mockito.anyString(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TbAutoPointsConfig.builder().build()));
        Assertions.assertNotNull(tbAutoPointsConfigServiceImpl.list(Mockito.anyString(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt()));
    }

    @Test
    void total() {
        Mockito.when(tbAutoPointsConfigMapper.total(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(10);
        Assertions.assertEquals(10, tbAutoPointsConfigServiceImpl.total(Mockito.anyString(), Mockito.anyList()));
    }
}
