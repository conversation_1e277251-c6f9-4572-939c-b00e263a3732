package com.points.service;

import com.alipay.api.*;
import com.alipay.api.domain.AppTokenExchangeSubElement;
import com.alipay.api.request.AlipayOpenAuthTokenAppQueryRequest;
import com.alipay.api.request.AlipayOpenAuthTokenAppRequest;
import com.alipay.api.response.AlipayOpenAuthTokenAppQueryResponse;
import com.alipay.api.response.AlipayOpenAuthTokenAppResponse;
import com.kerryprops.kip.service.integral.config.AliPayConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.TbInsensatePointsAuthRecordDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.AliPayServiceImpl;
import com.kerryprops.kip.service.integral.strategy.AliPayStrategy;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Collections;

import static org.mockito.Mockito.when;

@Slf4j
class AliPayServiceTest {

    @InjectMocks
    private AliPayServiceImpl aliPayServiceImpl;

    @Mock
    private TbBaseShopService baseShopService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private RedisService redisService;
    @Mock
    private AliPayClientService aliPayClientService;
    @Mock
    private TbInsensatePointsAuthRecordService insensatePointsAuthRecordService;
    @Mock
    private TbInsensatePointsPushRecordService insensatePointsPushRecordService;
    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;
    @Mock
    private ApplicationEventPublisher applicationEventPublisher;
    @Mock
    private AliPayConfig aliPayConfig;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("支付场景会员为空")
    void callbackCasePay1() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        aliPayBizContentDto.setOutRequestNo("");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }

    @Test
    @DisplayName("支付场景店铺为空")
    void callbackCasePay2() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        MallItem mallItem = MallItem.builder().build();
        aliPayBizContentDto.setMallItem(mallItem);
        aliPayBizContentDto.setOutRequestNo("");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("8a84816c92bd56670192e5ad9e04000e").build());
        Mockito.when(baseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }

    @Test
    @DisplayName("支付场景redis为false")
    void callbackCasePay3() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        MallItem mallItem = MallItem.builder().build();
        aliPayBizContentDto.setMallItem(mallItem);
        aliPayBizContentDto.setOutRequestNo("");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("8a84816c92bd56670192e5ad9e04000e").build());
        Mockito.when(baseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(Boolean.FALSE);
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }

    @Test
    @DisplayName("支付场景走完")
    void callbackCasePay4() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        MallItem mallItem = MallItem.builder().build();
        aliPayBizContentDto.setMallItem(mallItem);
        aliPayBizContentDto.setOutRequestNo("");
        aliPayBizContentDto.setMethod("alipay.business.mall.tradeapply.notify");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("8a84816c92bd56670192e5ad9e04000e").build());
        Mockito.when(baseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(Boolean.TRUE);
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }

    @Test
    @DisplayName("支付场景进入异常")
    void callbackCasePay5() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        MallItem mallItem = MallItem.builder().build();
        aliPayBizContentDto.setMallItem(mallItem);
        aliPayBizContentDto.setOutRequestNo("");
        aliPayBizContentDto.setMethod("alipay.business.mall.tradeapply.notify");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("8a84816c92bd56670192e5ad9e04000e").build());
        Mockito.when(baseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(Boolean.TRUE);
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any()))
                .thenThrow(new BizException(400, ""));
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }

    @Test
    @DisplayName("退款场景会员为空")
    void callbackCasePay6() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        aliPayBizContentDto.setOutRequestNo("REFUND");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }

    @Test
    @DisplayName("退款场景店铺为空")
    void callbackCasePay7() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        MallItem mallItem = MallItem.builder().build();
        aliPayBizContentDto.setMallItem(mallItem);
        aliPayBizContentDto.setOutRequestNo("REFUND");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("8a84816c92bd56670192e5ad9e04000e").build());
        Mockito.when(baseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }

    @Test
    @DisplayName("退款场景redis为false")
    void callbackCasePay8() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        MallItem mallItem = MallItem.builder().build();
        aliPayBizContentDto.setMallItem(mallItem);
        aliPayBizContentDto.setOutRequestNo("REFUND");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("8a84816c92bd56670192e5ad9e04000e").build());
        Mockito.when(baseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(Boolean.FALSE);
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }

    @Test
    @DisplayName("退款场景走完")
    void callbackCasePay9() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        MallItem mallItem = MallItem.builder().build();
        aliPayBizContentDto.setMallItem(mallItem);
        aliPayBizContentDto.setOutRequestNo("REFUND");
        aliPayBizContentDto.setMethod("alipay.business.mall.tradeapply.notify");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("8a84816c92bd56670192e5ad9e04000e").build());
        Mockito.when(baseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(Boolean.TRUE);
        Mockito.when(memberSalePointsProcessService.refundSalePointsProcess(Mockito.any())).thenReturn(-19);
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }

    @Test
    @DisplayName("退款场景异常")
    void callbackCasePay10() {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        MallItem mallItem = MallItem.builder().build();
        aliPayBizContentDto.setMallItem(mallItem);
        aliPayBizContentDto.setOutRequestNo("REFUND");
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("8a84816c92bd56670192e5ad9e04000e").build());
        Mockito.when(baseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(Boolean.FALSE);
        Mockito.when(memberSalePointsProcessService.refundSalePointsProcess(Mockito.any()))
                .thenThrow(new BizException(400, "退款场景异常"));
        Assertions.assertNotNull(aliPayServiceImpl.callback(aliPayBizContentDto));
    }


    // mallAuth方法为true，直接返回
    @Test
    void authCallbackCase1() throws AlipayApiException {
        AlipayClient client = Mockito.mock(AlipayClient.class);
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        Mockito.when(AliPayStrategy.getClient(Mockito.anyString())).thenReturn(client);
        AppTokenExchangeSubElement element = new AppTokenExchangeSubElement();
        element.setAppAuthToken("appAuthToken");
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        aliPayAuthDto.setAuthCode("002412310001");
        aliPayAuthDto.setAppAuthCode("ali002412310001");
        aliPayAuthDto.setMallItem(mallItem);
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("10012629");
        when(aliPayConfig.getByName(Mockito.anyString())).thenReturn(aliPayItem);
        AlipayOpenAuthTokenAppResponse response = new AlipayOpenAuthTokenAppResponse();
        response.setCode("10000");
        response.setTokens(Collections.singletonList(element));
        when(client.execute(Mockito.any(AlipayOpenAuthTokenAppRequest.class))).thenReturn(response);
        when(client.execute(Mockito.any(AlipayOpenAuthTokenAppQueryRequest.class))).thenReturn(new AlipayOpenAuthTokenAppQueryResponse());
        Assertions.assertNull(aliPayServiceImpl.authCallback(aliPayAuthDto));
        mockedStatic.close();
    }

    // url不为空，直接返回
    @Test
    void authCallbackCase2() throws Exception {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        aliPayAuthDto.setAuthCode("002412310001");
        aliPayAuthDto.setMallItem(mallItem);
        aliPayAuthDto.setMallId("8a8883557cca9463017ccb002b360001");
        AliPayMemberTokenDto tokenDto = new AliPayMemberTokenDto();
        when(aliPayClientService.getAccessToken(Mockito.anyString(), Mockito.anyString())).thenReturn(tokenDto);
        ActiveFormDto activeFormDto = new ActiveFormDto();
        activeFormDto.setMobile("18976543436");
        when(aliPayClientService.queryActiveForm(Mockito.any(AliPayAuthDto.class))).thenReturn(activeFormDto);
        when(aliPayClientService.openAlipayMarketingCard(Mockito.any(AliPayAuthDto.class))).thenReturn("/ali/authCallback");
        Assertions.assertNotNull(aliPayServiceImpl.authCallback(aliPayAuthDto));
    }

    // url为空，直接返回
    @Test
    void authCallbackCase3() throws Exception {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        aliPayAuthDto.setAuthCode("002412310001");
        aliPayAuthDto.setMallItem(mallItem);
        aliPayAuthDto.setMallId("8a8883557cca9463017ccb002b360001");
        AliPayMemberTokenDto tokenDto = new AliPayMemberTokenDto();
        tokenDto.setUserId("2024123109002");
        when(aliPayClientService.getAccessToken(Mockito.anyString(), Mockito.anyString())).thenReturn(tokenDto);
        ActiveFormDto activeFormDto = new ActiveFormDto();
        activeFormDto.setMobile("18976543436");
        when(aliPayClientService.queryActiveForm(Mockito.any(AliPayAuthDto.class))).thenReturn(activeFormDto);
        when(aliPayClientService.openAlipayMarketingCard(Mockito.any(AliPayAuthDto.class))).thenReturn(null);
        when(insensatePointsAuthRecordService.getByAliUserId(Mockito.anyString(), Mockito.anyString())).thenReturn(TbInsensatePointsAuthRecordDto.builder().build());
        when(aliPayClientService.querySchemaUrl(Mockito.any(), Mockito.any())).thenReturn("/ali/authCallback");
        Assertions.assertNotNull(aliPayServiceImpl.authCallback(aliPayAuthDto));
    }

    @Test
    void memberAuth() {
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        aliPayAuthDto.setAuthCode("002412310001");
        when(aliPayClientService.getAccessToken(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Assertions.assertNotNull(aliPayServiceImpl.memberAuth(aliPayAuthDto));
    }

    @Test
    @DisplayName("tokenDto为null")
    void memberAuthCase1() {
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        aliPayAuthDto.setAuthCode("002412310001");
        aliPayAuthDto.setMallId("8a84816c92bd56670192e5ad9e04000e");
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        aliPayAuthDto.setMallItem(mallItem);
        when(aliPayClientService.getAccessToken(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Assertions.assertNotNull(aliPayServiceImpl.memberAuth(aliPayAuthDto));
    }

    @Test
    void smartPointsCallback() {
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        Assertions.assertNotNull(aliPayServiceImpl.smartPointsCallback(aliPayAuthDto));
    }

    @Test
    @DisplayName("进入tokenDto.getAppAuthToken()为空的判断")
    void mallAuth() throws AlipayApiException {
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        MallItem mallItem = new MallItem();
        aliPayAuthDto.setMallItem(mallItem);
        aliPayAuthDto.setAppAuthCode("appAuthCode");
        AlipayClient client = Mockito.mock(AlipayClient.class);
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        Mockito.when(AliPayStrategy.getClient(Mockito.any())).thenReturn(client);
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(new AliPayItem());
        AppTokenExchangeSubElement element = new AppTokenExchangeSubElement();
        element.setAppAuthToken("");
        AlipayOpenAuthTokenAppResponse response = new AlipayOpenAuthTokenAppResponse();
        response.setCode("10000");
        response.setTokens(Collections.singletonList(element));
        when(client.execute(Mockito.any(AlipayOpenAuthTokenAppRequest.class))).thenReturn(response);
        Assertions.assertNotNull(aliPayServiceImpl.mallAuth(aliPayAuthDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("进入异常")
    void mallAuthCase1() throws AlipayApiException {
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        MallItem mallItem = new MallItem();
        aliPayAuthDto.setMallItem(mallItem);
        aliPayAuthDto.setAppAuthCode("appAuthCode");
        AlipayClient client = Mockito.mock(AlipayClient.class);
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        Mockito.when(AliPayStrategy.getClient(Mockito.any())).thenReturn(client);
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(new AliPayItem());
        AppTokenExchangeSubElement element = new AppTokenExchangeSubElement();
        element.setAppAuthToken("");
        AlipayOpenAuthTokenAppResponse response = new AlipayOpenAuthTokenAppResponse();
        response.setCode("10000");
        response.setTokens(Collections.singletonList(element));
        when(client.execute(Mockito.any(AlipayOpenAuthTokenAppRequest.class))).thenThrow(new AlipayApiException());
        Assertions.assertNotNull(aliPayServiceImpl.mallAuth(aliPayAuthDto));
        mockedStatic.close();
    }

}