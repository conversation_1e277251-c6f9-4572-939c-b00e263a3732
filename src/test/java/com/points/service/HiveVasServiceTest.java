package com.points.service;

import com.kerryprops.kip.service.integral.client.HiveServiceClient;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.model.dto.HiveLbsInfoDto;
import com.kerryprops.kip.service.integral.model.dto.LbsItemDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.ProjectDto;
import com.kerryprops.kip.service.integral.model.vo.TenantAllVo;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import com.kerryprops.kip.service.integral.service.HiveVasService;
import com.kerryprops.kip.service.integral.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 08/05/2024 13:50
 **********************************************************************************************************************/

@Slf4j
class HiveVasServiceTest {

    @InjectMocks
    private HiveVasService hiveVasService;

    @Mock
    private HiveVasClient hiveVasClient;
    @Mock
    private HiveServiceClient hiveServiceClient;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private RedisService redisService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getTenantInfoByShopNos() {
        Assertions.assertNotNull(hiveVasService.getTenantInfoByShopNos(null));

        Assertions.assertNotNull(hiveVasService.getTenantInfoByShopNos(Collections.singletonList("12345")));

        Mockito.when(hiveVasClient.getTenantInfo(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TenantAllVo.builder().tenant(TenantInfoVo.builder().build()).build()));
        Assertions.assertNotNull(hiveVasService.getTenantInfoByShopNos(Collections.singletonList("12345")));
    }

    @Test
    void getTenantFuture() {
        Assertions.assertNotNull(hiveVasService.getTenantFuture(null));

        Assertions.assertNotNull(hiveVasService.getTenantFuture(Collections.singletonList("12345")));

        Mockito.when(hiveVasClient.getTenantInfo(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TenantAllVo.builder().tenant(TenantInfoVo.builder().build()).build()));
        Assertions.assertNotNull(hiveVasService.getTenantFuture(Collections.singletonList("12345")));
    }

    @Test
    void getTenantFutureFromHiveService() {
        Assertions.assertNotNull(hiveVasService.getTenantFutureFromHiveService(null));

        Assertions.assertNotNull(hiveVasService.getTenantFutureFromHiveService(Collections.singletonList("12345")));

        Mockito.when(hiveServiceClient.findByShopNos(Mockito.any()))
                .thenReturn(Collections.singletonList(TenantInfoVo.builder().contractNo("").build()));
        Assertions.assertNotNull(hiveVasService.getTenantFutureFromHiveService(Collections.singletonList("12345")));
    }

    @Test
    void getMallName() {
        Assertions.assertNotNull(hiveVasService.getMallName(null));

        Assertions.assertNotNull(hiveVasService.getMallName(Collections.singletonList("12345")));

        Mockito.when(hiveVasClient.getLbsInfo(Mockito.any()))
                .thenReturn(Collections.singletonList(HiveLbsInfoDto.builder().lbs(LbsItemDto.builder().build()).build()));
        Assertions.assertNotNull(hiveVasService.getMallName(Collections.singletonList("12345")));
    }

    @Test
    void getLbsName() {
        Assertions.assertNull(hiveVasService.getLbsName("12345"));

        Mockito.when(hiveVasClient.getLbsInfo(Mockito.any()))
                .thenReturn(Collections.singletonList(HiveLbsInfoDto.builder().lbs(LbsItemDto.builder().build()).build()));
        Assertions.assertNotNull(hiveVasService.getLbsName("12345"));
    }

    @Test
    void getLbsProjectInfo() {
        Mockito.when(redisService.getValue(Mockito.any())).thenReturn("12345");
        Assertions.assertNotNull(hiveVasService.getLbsProjectInfo("12345"));

        Mockito.when(redisService.getValue(Mockito.any())).thenReturn(null);
        Mockito.when(hiveVasClient.getLbsInfo(Mockito.anyList())).thenReturn(null);
        Assertions.assertNull(hiveVasService.getLbsProjectInfo("12345"));

        Mockito.when(hiveVasClient.getLbsInfo(Mockito.anyList())).thenReturn(Collections.singletonList(HiveLbsInfoDto.builder().lbs(LbsItemDto.builder().build()).build()));
        Assertions.assertNull(hiveVasService.getLbsProjectInfo("12345"));

        Mockito.when(hiveVasClient.getLbsInfo(Mockito.anyList())).thenReturn(Collections.singletonList(HiveLbsInfoDto.builder().project(ProjectDto.builder().build()).build()));
        Assertions.assertNotNull(hiveVasService.getLbsProjectInfo("12345"));
    }

    @Test
    void getMallByLbsId() {
        Assertions.assertNull(hiveVasService.getMallByLbsId(""));

        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertNotNull(hiveVasService.getMallByLbsId("12345"));

        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(null);
        Mockito.when(redisService.getValue(Mockito.any())).thenReturn("12345");
        Mockito.when(mallConfig.getByProjectId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertNotNull(hiveVasService.getMallByLbsId("12345"));

        Mockito.when(redisService.getValue(Mockito.any())).thenReturn(null);
        Assertions.assertNull(hiveVasService.getMallByLbsId("12345"));
    }
}
