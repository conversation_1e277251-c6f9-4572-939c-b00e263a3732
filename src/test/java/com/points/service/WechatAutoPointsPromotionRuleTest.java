package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.impl.WechatAutoPointsPromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class WechatAutoPointsPromotionRuleTest {

    @InjectMocks
    private WechatAutoPointsPromotionRule wechatAutoPointsPromotionRule;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertNotNull(wechatAutoPointsPromotionRule.getRuleType());
    }

    @Test
    void checkRule() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build();
        TbActivityPromotionCondition condition = TbActivityPromotionCondition.builder().build();
        Assertions.assertFalse(
                wechatAutoPointsPromotionRule.checkRule(condition, dto, DateUtil.date(), null)
        );

        TbActivityPromotionCondition condition1 = TbActivityPromotionCondition.builder().build();
        condition1.setPromotionConditionContent("1");
        Assertions.assertTrue(
                wechatAutoPointsPromotionRule.checkRule(condition1,
                        SalesAutoPointsDto.builder().remark(IntegralConstant.WECHAT_POINTS).build(),
                        DateUtil.date(), null)
        );

        TbActivityPromotionCondition condition2 = TbActivityPromotionCondition.builder().build();
        condition2.setPromotionConditionContent("2");
        Assertions.assertTrue(
                wechatAutoPointsPromotionRule.checkRule(condition2,
                        SalesAutoPointsDto.builder().remark(IntegralConstant.ALIPAY_POINTS).build(),
                        DateUtil.date(), null)
        );

        TbActivityPromotionCondition condition3 = TbActivityPromotionCondition.builder().build();
        condition3.setPromotionConditionContent("123");
        Assertions.assertFalse(
                wechatAutoPointsPromotionRule.checkRule(condition3,
                        SalesAutoPointsDto.builder().build(),
                        DateUtil.date(), null)
        );
    }
}
