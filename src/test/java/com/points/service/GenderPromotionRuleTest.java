package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.CustomerIdentityDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.impl.GenderPromotionRule;
import com.kerryprops.kip.service.integral.service.promotion.impl.OfficePromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class GenderPromotionRuleTest {

    @InjectMocks
    private GenderPromotionRule genderPromotionRule;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertNotNull(genderPromotionRule.getRuleType());
    }

    @Test
    void checkRule() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build();
        Assertions.assertFalse(
                genderPromotionRule.checkRule(TbActivityPromotionCondition.builder().build(), dto, DateUtil.date(), null)
        );

        Assertions.assertFalse(
                genderPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("U").build(), dto, DateUtil.date(), null)
        );

        Assertions.assertFalse(
                genderPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("F").build(), dto, DateUtil.date(), null)
        );

        Assertions.assertFalse(
                genderPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("M").build(), dto, DateUtil.date(), null)
        );
    }
}
