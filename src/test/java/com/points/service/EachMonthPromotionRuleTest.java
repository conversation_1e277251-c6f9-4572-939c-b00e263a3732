package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.impl.EachMonthPromotionRule;
import com.kerryprops.kip.service.integral.service.promotion.impl.EachWeekPromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class EachMonthPromotionRuleTest {

    @InjectMocks
    private EachMonthPromotionRule eachMonthPromotionRule;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertNotNull(eachMonthPromotionRule.getRuleType());
    }

    @Test
    void checkRule() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build();
        Assertions.assertFalse(
                eachMonthPromotionRule.checkRule(TbActivityPromotionCondition.builder().build(), dto, DateUtil.date(), null)
        );
        Assertions.assertFalse(
                eachMonthPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto, DateUtil.date(), null)
        );
    }
}
