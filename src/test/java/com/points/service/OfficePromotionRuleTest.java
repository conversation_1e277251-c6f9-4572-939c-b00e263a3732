package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.enums.RuleTimeEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.model.dto.CustomerIdentityDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.MemberGradeStrategyServiceImpl;
import com.kerryprops.kip.service.integral.service.promotion.impl.OfficePromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class OfficePromotionRuleTest {

    @InjectMocks
    private OfficePromotionRule officePromotionRule;

    @Mock
    private MallConfig mallConfig;
    @Mock
    private ProfileServiceClient profileServiceClient;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertNotNull(officePromotionRule.getRuleType());
    }

    @Test
    void checkRule() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().build();
        Assertions.assertFalse(
                officePromotionRule.checkRule(TbActivityPromotionCondition.builder().build(), dto, DateUtil.date(), null)
        );

        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder().build();
        dto1.setMember(TbMemberAsset.builder().build());
        Assertions.assertFalse(
                officePromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("1,2,3").build(), dto1, DateUtil.date(), null)
        );

        SalesAutoPointsDto dto2 = SalesAutoPointsDto.builder().build();
        dto2.setMember(TbMemberAsset.builder().kipUserId("kipUserId").build());
        Assertions.assertFalse(
                officePromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("1,2,3").build(), dto2, DateUtil.date(), null)
        );

        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertFalse(
                officePromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("1,2,3").build(), dto2, DateUtil.date(), null)
        );

        Mockito.when(profileServiceClient.getIdentityResponse(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(CustomerIdentityDto.builder().office(true).build()));
        Assertions.assertTrue(
                officePromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("1,2,3").build(), dto2, DateUtil.date(), null)
        );

        Mockito.when(profileServiceClient.getIdentityResponse(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(CustomerIdentityDto.builder().apartment(true).build()));
        Assertions.assertTrue(
                officePromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("1,2,3").build(), dto2, DateUtil.date(), null)
        );

        Mockito.when(profileServiceClient.getIdentityResponse(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(CustomerIdentityDto.builder().residence(true).build()));
        Assertions.assertTrue(
                officePromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("1,2,3").build(), dto2, DateUtil.date(), null)
        );
    }
}
