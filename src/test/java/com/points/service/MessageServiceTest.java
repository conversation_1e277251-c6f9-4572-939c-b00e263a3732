package com.points.service;

import com.kerryprops.kip.service.integral.client.MessageServiceClient;
import com.kerryprops.kip.service.integral.entity.TbTemplateMessage;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.dto.MessageQueryDto;
import com.kerryprops.kip.service.integral.model.dto.TbSmsTemplateConfig;
import com.kerryprops.kip.service.integral.model.vo.WxTemplateSendVo;
import com.kerryprops.kip.service.integral.service.impl.MessageServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class MessageServiceTest {

    @InjectMocks
    private MessageServiceImpl messageService;

    @Mock
    private MessageServiceClient messageServiceClient;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getSmsTemplate() {
        KerryResultDto<List<TbSmsTemplateConfig>> resultDto = new KerryResultDto<>();
        Mockito.when(messageServiceClient.getSmsTemplate(Mockito.any())).thenReturn(resultDto);
        Assertions.assertNull(messageService.getSmsTemplate(MessageQueryDto.builder().build()));
    }

    @Test
    void sendSmsMessage() {
        Assertions.assertNull(messageService.sendSmsMessage(MessageQueryDto.builder().build()));
    }

    @Test
    void getModelTemplate() {
        KerryResultDto<List<TbTemplateMessage>> resultDto = new KerryResultDto<>();
        Mockito.when(messageServiceClient.getModelTemplate(Mockito.any())).thenReturn(resultDto);
        Assertions.assertNull(messageService.getModelTemplate(MessageQueryDto.builder().build()));
    }

    @Test
    void sendSmsTemplate() {
        Assertions.assertNull(messageService.sendSmsTemplate(WxTemplateSendVo.builder().build()));
    }
}
