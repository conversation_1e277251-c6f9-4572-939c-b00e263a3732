package com.points.service;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.pmw.client.resource.PointsPaymentConfirmResource;
import com.kerryprops.kip.service.integral.common.enums.EventTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsPushRecord;
import com.kerryprops.kip.service.integral.mapper.TbInsensatePointsPushRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;
import com.kerryprops.kip.service.integral.service.impl.TbInsensatePointsPushRecordServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

@Slf4j
class TbInsensatePointsPushRecordServiceTest {

    @InjectMocks
    private TbInsensatePointsPushRecordServiceImpl tbInsensatePointsPushRecordServiceImpl;
    
    @Mock
    private TbInsensatePointsPushRecordMapper insensatePointsPushRecordMapper;
    @Mock
    private Mapper mapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void pushTransaction() {
        InsensatePointsPushRecordDto dto = InsensatePointsPushRecordDto.builder().build();
        Mockito.when(mapper.map(dto, TbInsensatePointsPushRecord.class)).thenReturn(TbInsensatePointsPushRecord.builder().build());
        Assertions.assertDoesNotThrow(() -> tbInsensatePointsPushRecordServiceImpl.pushTransaction(dto));
    }

    @Test
    void saveWeChatPushPaymentRecord() {
        PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource body = new PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource();
        PointsPaymentConfirmResource dto = new PointsPaymentConfirmResource(null, body, null);
        Assertions.assertDoesNotThrow(() -> tbInsensatePointsPushRecordServiceImpl.saveWeChatPushPaymentRecord(dto));

        PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource body1 = new PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource();
        body1.setEventType(EventTypeEnum.TRANS_SUCC.getName());
        PointsPaymentConfirmResource.PointsPaymentDetailResource resource = new PointsPaymentConfirmResource.PointsPaymentDetailResource();
        body1.setResource(resource);
        PointsPaymentConfirmResource dto1 = new PointsPaymentConfirmResource(null, body1, null);
        Assertions.assertDoesNotThrow(() -> tbInsensatePointsPushRecordServiceImpl.saveWeChatPushPaymentRecord(dto1));
    }

    @Test
    void findByTransactionIdAndEventTypeAndOrigin() {
        Mockito.when(insensatePointsPushRecordMapper.findByTransactionIdAndEventTypeAndOrigin(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(null);
        Assertions.assertNull(tbInsensatePointsPushRecordServiceImpl.findByTransactionIdAndEventTypeAndOrigin("", 1, 2));

        Mockito.when(insensatePointsPushRecordMapper.findByTransactionIdAndEventTypeAndOrigin(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(TbInsensatePointsPushRecord.builder().build());
        Mockito.when(mapper.map(TbInsensatePointsPushRecord.builder().build(), InsensatePointsPushRecordDto.class))
                .thenReturn(InsensatePointsPushRecordDto.builder().build());
        Assertions.assertNotNull(tbInsensatePointsPushRecordServiceImpl.findByTransactionIdAndEventTypeAndOrigin("", 1, 2));
    }

    @Test
    void selectByOpenId() {
        Mockito.when(insensatePointsPushRecordMapper.selectByOpenId(Mockito.any())).thenReturn(null);
        Assertions.assertNull(tbInsensatePointsPushRecordServiceImpl.selectByOpenId(""));
    }

    @Test
    void findByTransactionId() {
        Mockito.when(insensatePointsPushRecordMapper.findByTransactionId(Mockito.any())).thenReturn(null);
        Assertions.assertNull(tbInsensatePointsPushRecordServiceImpl.findByTransactionId(""));
    }
}