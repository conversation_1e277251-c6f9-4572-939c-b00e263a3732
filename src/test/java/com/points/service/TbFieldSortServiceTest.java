package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbFieldSort;
import com.kerryprops.kip.service.integral.mapper.TbFieldSortMapper;
import com.kerryprops.kip.service.integral.service.impl.TbFieldSortServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 12/04/2024 09:27
 **********************************************************************************************************************/
@Slf4j
class TbFieldSortServiceTest {

    @InjectMocks
    private TbFieldSortServiceImpl tbFieldSortService;
    @Mock
    private TbFieldSortMapper tbFieldSortMapper;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findByUserIdAndPageType() {
        TbFieldSort sort = TbFieldSort.builder().fieldSort(0).fieldCnName("cn").fieldName("测试").userId(95L).groupId("ALL").isInitial(0).build();
        TbFieldSort sort1 = TbFieldSort.builder().fieldSort(0).fieldCnName("cn").fieldName("测试").userId(0L).groupId("ALL").isInitial(1).build();
        when(tbFieldSortMapper.selectByUserIdsAndPageType(any(), any())).thenReturn(Arrays.asList(sort, sort1));
        List<TbFieldSort> list = tbFieldSortService.findByUserIdAndPageType(95L, "1");
        list = tbFieldSortService.findByUserIdAndPageType(100L, "1");
        Assertions.assertEquals(1, list.size());
    }

}
