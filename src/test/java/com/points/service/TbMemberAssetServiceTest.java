package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.dto.alipay.ActiveFormDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayAuthDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayMemberTokenDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TbMemberAssetServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.BrandMemberDeleteResource;
import com.kerryprops.kip.service.integral.webservice.resource.KipSyncMemberResource;
import com.kerryprops.kip.service.integral.webservice.resource.MemberSignUpResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 05/28/2024 09:06
 **********************************************************************************************************************/

@Slf4j
class TbMemberAssetServiceTest {

    @InjectMocks
    private TbMemberAssetServiceImpl tbMemberAssetService;
    @Mock
    private TbMemberAssetMapper tbMemberAssetMapper;
    @Mock
    private RedisService redisService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    @Mock
    private TbCardMemberRelationService tbCardMemberRelationService;
    @Mock
    private ProfileServiceClient profileServiceClient;
    @Mock
    private KerryStaffService kerryStaffService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private TbSequenceVipcodeService tbSequenceVipcodeService;
    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private TbMemberSourceService tbMemberSourceService;
    @Mock
    private ApplicationEventPublisher applicationEventPublisher;
    @Mock
    private MallConfig mallConfig;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getAllByGroupIdAndKipUserId() {
        assertNull(tbMemberAssetService.getAllByGroupIdAndKipUserId("4028e3817c2b3f79017c2b48c54c0000", "2c9d85bc8489e0ba01849e4629760003"));

        when(memberRegisterService.findByGroupIdAndKipUserId(any(), any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("12345").build());
        Assertions.assertNotNull(tbMemberAssetService.getAllByGroupIdAndKipUserId("4028e3817c2b3f79017c2b48c54c0000", "2c9d85bc8489e0ba01849e4629760003"));

        when(memberRegisterService.findByGroupIdAndKipUserId(any(), any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("").build());
        Assertions.assertNotNull(tbMemberAssetService.getAllByGroupIdAndKipUserId("4028e3817c2b3f79017c2b48c54c0000", "2c9d85bc8489e0ba01849e4629760003"));
    }

    @Test
    void findByOpenIdAndGroupId() {
        assertNull(tbMemberAssetService.findByOpenIdAndGroupId("", "aaa80b47c784020017c78b00d060022"));

        when(kerryStaffService.findByOpenId(any()))
                .thenReturn(CustomerUserDto.builder().build());
        assertNull(tbMemberAssetService.findByOpenIdAndGroupId("", "aaa80b47c784020017c78b00d060022"));

        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any()))
                .thenReturn(TbMemberAsset.builder().status(IntegralConstant.MEMBER_FREEZE_STATUS).build());
        assertNull(tbMemberAssetService.findByOpenIdAndGroupId("", "aaa80b47c784020017c78b00d060022"));

        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertNotNull(tbMemberAssetService.findByOpenIdAndGroupId("", "aaa80b47c784020017c78b00d060022"));
    }

    @Test
    void getMemberWithProfileInfo() {
        when(kerryStaffService.findByKipUserId(any())).thenReturn(null);
        assertNull(tbMemberAssetService.getMemberWithProfileInfo("2c9d85bc8489e0ba01849e4629760003", "aaa80b47c784020017c78b00d060022"));
    }

    @Test
    void getMemberForAutoPointsByOpenIdAndGroupIdAndMallId() {
        when(tbInsensatePointsAuthRecordService.getByOpenIdAndMallId(any(), any()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().kipUserId("12345").build());
        when(tbCardMemberRelationService.getByMallIdAndAliUserId(any(), any()))
                .thenReturn(TbCardMemberRelation.builder().mobile("12345").build());
        when(tbCardMemberRelationService.getByAliUserId(any()))
                .thenReturn(TbCardMemberRelation.builder().mobile("12345").build());
        assertNull(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId("", "aaa80b47c784020017c78b00d060022", "8aaa80b47c784020017c78b00d060022"));
    }

    @Test
    void findByVipcodeAndGroupId() {
        assertNull(tbMemberAssetService.findByVipcodeAndGroupId("KERRY100213686" ,"aaa80b47c784020017c78b00d060022"));
    }

    @Test
    void findMemberAllInfoByVipcodeAndGroupId() {
        assertNull(tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId("KERRY100213686" ,"aaa80b47c784020017c78b00d060022"));
    }

    @Test
    void getLoginMemberInfo() {
        LoginUser loginUser = LoginUser.builder()
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .build();
        assertNull(tbMemberAssetService.getLoginMemberInfo(loginUser));

        when(hiveVasService.getMallByLbsId(any())).thenReturn(MallItem.builder().build());
        assertNull(tbMemberAssetService.getLoginMemberInfo(loginUser));

        when(tbMemberGradeService.queryGradeSortAscByGroupId(any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("02").upGradationStatus(0).build()));
        Assertions.assertThrows(Exception.class, () -> tbMemberAssetService.getLoginMemberInfo(loginUser));

        when(kerryStaffService.findByKipUserId(any())).thenReturn(CustomerUserDto.builder().build());

        // affectedRows<=0
        when(memberRegisterService.saveMember(any())).thenReturn(0);
        assertNull(tbMemberAssetService.getLoginMemberInfo(loginUser));

        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any()))
                .thenReturn(TbMemberAsset.builder().grade("02").currentPoints(-1).build());
        Assertions.assertNotNull(tbMemberAssetService.getLoginMemberInfo(loginUser));

        // affectedRows > 0
        when(memberRegisterService.saveMember(any())).thenReturn(1);
        Assertions.assertNotNull(tbMemberAssetService.getLoginMemberInfo(loginUser));
    }

    @Test
    void getLoginMemberInfo1() {
        LoginUser loginUser = LoginUser.builder()
                .cId("7e5a7e6029724eaeb2b7917ee5f0f361")
                .phoneNumber("13000000000")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .build();

        assertNull(tbMemberAssetService.getLoginMemberInfo(loginUser));

        MallItem mallItem = MallItem.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").build();
        when(hiveVasService.getMallByLbsId(any())).thenReturn(mallItem);
        assertNull(tbMemberAssetService.getLoginMemberInfo(loginUser));

        when(tbMemberGradeService.queryGradeSortAscByGroupId(any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("02").upGradationStatus(0).build()));

        when(tbMemberAssetMapper.findByGroupIdsAndMobile(anyList(), any())).thenReturn(
                Collections.singletonList(TbMemberAsset.builder().grade("02").groupId("8aaa82ea804d07cd01805174dd3b000c").currentPoints(0)
                        .vipcode("KERRY00001").mobile("13000000000").mallId("8aaa81cb7c836c6b017c83e2c76f0000").build()));
        when(mallConfig.isKoBigGroup(any())).thenReturn(Boolean.TRUE);
        assertNull(tbMemberAssetService.getLoginMemberInfo(loginUser));

        when(memberRegisterService.saveMember(any())).thenReturn(1);
        Assertions.assertNotNull(tbMemberAssetService.getLoginMemberInfo(loginUser));
    }

    @Test
    void findByKipUserIdAndGroupId() {
        assertNull(tbMemberAssetService.findByKipUserIdAndGroupId("2c9d85bc8489e0ba01849e4629760003", "4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void findByKipUserIdAndGroupIdWithException() {
        Assertions.assertThrows(Exception.class, () ->tbMemberAssetService.findByKipUserIdAndGroupIdWithException("2c9d85bc8489e0ba01849e4629760003", "4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void findByKipUserIdAndGroupIdWithException1() {
        TbMemberAsset tbMemberAsset = TbMemberAsset.builder().id(10010L).currentPoints(100).grade("01").vipcode("KERRY00000001")
                .groupId("aaa80b47c784020017c78b00d060022").mobile("***********").status("1").build();
        when(memberRegisterService.findByGroupIdAndKipUserId(any(), any())).thenReturn(tbMemberAsset);
        Assertions.assertNotNull(tbMemberAssetService.findByKipUserIdAndGroupIdWithException("2c9d85bc8489e0ba01849e4629760003", "4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void findByMobileAndGroupId() {
        assertNull(tbMemberAssetService.findByMobileAndGroupId("***********", "aaa80b47c784020017c78b00d060022"));
    }

    @Test
    void findByVipcodesAndGroupId() {
        Assertions.assertNotNull(tbMemberAssetService.findByVipcodesAndGroupId(null, "aaa80b47c784020017c78b00d060022"));
        Assertions.assertNotNull(tbMemberAssetService.findByVipcodesAndGroupId(Collections.singletonList("KERRY100213686"), "aaa80b47c784020017c78b00d060022"));
        when(tbMemberAssetMapper.queryMemberByGroupIdAndVipcodes(any(), Mockito.anyList())).thenReturn(Collections.singletonList(TbMemberAsset.builder().status("1").build()));
        Assertions.assertNotNull(tbMemberAssetService.findByVipcodesAndGroupId(Collections.singletonList("KERRY100213686"), "aaa80b47c784020017c78b00d060022"));
    }

    @Test
    void findByDto() {
        assertNull(tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId("aaa80b47c784020017c78b00d060022").mobile("***********").build()));
    }

    @Test
    void findAllByDto() {
        assertNull(tbMemberAssetService.findAllByDto(SingleMemberQueryDto.builder().groupId("aaa80b47c784020017c78b00d060022").mobile("***********").build()));
    }

    @Test
    void findAllByDto1() {
        TbMemberAsset tbMemberAsset = TbMemberAsset.builder().id(10010L).currentPoints(100).grade("01").vipcode("KERRY00000001")
                .groupId("aaa80b47c784020017c78b00d060022").mobile("***********").build();
        when(tbMemberAssetMapper.findByDto(any())).thenReturn(tbMemberAsset);
        assertNotNull(tbMemberAssetService.findAllByDto(SingleMemberQueryDto.builder().groupId("aaa80b47c784020017c78b00d060022").mobile("***********").build()));
    }

    @Test
    void profileMobileModify() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.profileMobileModify("2c9d85bc8489e0ba01849e4629760003", "17789890976"));

        when(tbMemberAssetMapper.findByKipUserId(any()))
                .thenReturn(Collections.singletonList(TbMemberAsset.builder().mobile("17789890976").build()));
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.profileMobileModify("2c9d85bc8489e0ba01849e4629760003", "17789890976"));

        when(tbMemberAssetMapper.findByKipUserId(any()))
                .thenReturn(Collections.singletonList(TbMemberAsset.builder().mobile("").build()));
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.profileMobileModify("2c9d85bc8489e0ba01849e4629760003", "17789890976"));
    }

    @Test
    void getByMobileAndGroupIdWithException() {
        Assertions.assertThrows(Exception.class, () ->tbMemberAssetService.getByMobileAndGroupIdWithException("18149768377", "4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void getByMobileAndGroupIdWithException1() {
        TbMemberAsset tbMemberAsset = TbMemberAsset.builder().id(10010L).currentPoints(100).grade("01").vipcode("KERRY00000001")
                .groupId("aaa80b47c784020017c78b00d060022").mobile("***********").status("1").build();
        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any())).thenReturn(tbMemberAsset);

        Assertions.assertNotNull(tbMemberAssetService.getByMobileAndGroupIdWithException("18149768377", "4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void getOrCreateMember() {
        AliPayAuthDto dto = AliPayAuthDto.builder().mallId("8a8883557cca9463017ccb0ce0a00002").groupId("8a8884e77cc9e70a017cca188fae0003")
                .requestId("20240528015789492275168046809").templateId("20220506000000004000626000300720").build();

        AliPayMemberTokenDto tokenDto = AliPayMemberTokenDto.builder().userId("2088212547206809").accessToken("composeBdf296a01fe064c7d80197a3a05346C80")
                .build();
        dto.setMemberToken(tokenDto);

        ActiveFormDto formDto = ActiveFormDto.builder().gender("男").mobile("13122957625").birthday("04-26").mallId("8a8883557cca9463017ccb0ce0a00002")
                .groupId("8a8884e77cc9e70a017cca188fae0003").build();
        dto.setActiveForm(formDto);
        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertNotNull(tbMemberAssetService.getOrCreateMember(dto));

        ActiveFormDto formDto1 = ActiveFormDto.builder().gender("男").mobile("+13122957625").birthday("04-26").mallId("8a8883557cca9463017ccb0ce0a00002")
                .groupId("8a8884e77cc9e70a017cca188fae0003").build();
        dto.setActiveForm(formDto1);
        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any()))
                .thenReturn(null);
        Assertions.assertThrows(Exception.class, () -> tbMemberAssetService.getOrCreateMember(dto));

        when(tbMemberGradeService.getGroupMiniGradeCode(any()))
                .thenReturn("01");
        // idDto不为空 affectedRows<=0
        when(profileServiceClient.alipayCreateUser(any()))
                .thenReturn(IdDto.builder().build());
        when(memberRegisterService.saveMember(any()))
                .thenReturn(0);
        assertNull(tbMemberAssetService.getOrCreateMember(dto));

        // idDto为空 affectedRows>0
        when(profileServiceClient.alipayCreateUser(any()))
                .thenReturn(null);
        when(kerryStaffService.findByMobile(any())).thenReturn(CustomerUserDto.builder().build());
        when(memberRegisterService.saveMember(any()))
                .thenReturn(1);
        Assertions.assertNotNull(tbMemberAssetService.getOrCreateMember(dto));
    }

    @Test
    void memberSignUp() {
        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any())).thenReturn(TbMemberAsset.builder().build());
        Assertions.assertNotNull(tbMemberAssetService.memberSignUp(MemberSignUpResource.builder().build()));

        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any()))
                .thenReturn(null);
        Assertions.assertThrows(Exception.class, ()-> tbMemberAssetService.memberSignUp(MemberSignUpResource.builder().build()));

        when(tbMemberGradeService.getGroupMiniGradeCode(any())).thenReturn("01");
        // idDto不为空 affectedRows<=0
        when(profileServiceClient.alipayCreateUser(any()))
                .thenReturn(IdDto.builder().build());
        when(memberRegisterService.saveMember(any()))
                .thenReturn(0);
        assertNull(tbMemberAssetService.memberSignUp(MemberSignUpResource.builder().build()));

        // idDto为空 进入异常·
        when(profileServiceClient.alipayCreateUser(any()))
                .thenReturn(null);
        when(kerryStaffService.findByMobile(any()))
                .thenReturn(CustomerUserDto.builder().build());
        when(memberRegisterService.saveMember(any()))
                .thenThrow(new RuntimeException());
        assertNull(tbMemberAssetService.memberSignUp(MemberSignUpResource.builder().build()));

    }

    @Test
    void memberSignUp1() {
        MemberSignUpResource resource = MemberSignUpResource.builder()
                .mallId("8aaa80b47c784020017c78b00d060022")
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mobile("13000000000")
                .areaCode("+86")
                .build();

        MallItem mallItem = MallItem.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").build();
        when(hiveVasService.getMallByLbsId(any())).thenReturn(mallItem);
        when(tbMemberGradeService.getGroupMiniGradeCode(any())).thenReturn("01");
        when(mallConfig.isKoBigGroup(any())).thenReturn(Boolean.TRUE);
        when(tbMemberAssetMapper.findByGroupIdsAndMobile(anyList(), any())).thenReturn(
                Collections.singletonList(TbMemberAsset.builder().grade("02").groupId("8aaa82ea804d07cd01805174dd3b000c").currentPoints(0)
                        .vipcode("KERRY00001").mobile("13000000000").mallId("8aaa81cb7c836c6b017c83e2c76f0000").build()));

        when(memberRegisterService.saveMember(any())).thenReturn(1);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.memberSignUp(resource));
    }

    @Test
    void memberSignUp2() {
        MemberSignUpResource resource = MemberSignUpResource.builder()
                .mallId("8aaa80b47c784020017c78b00d060022")
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mobile("13000000000")
                .areaCode("+86")
                .build();

        MallItem mallItem = MallItem.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").build();
        when(hiveVasService.getMallByLbsId(any())).thenReturn(mallItem);
        when(tbMemberGradeService.getGroupMiniGradeCode(any())).thenReturn("01");
        when(mallConfig.isKoBigGroup(any())).thenReturn(Boolean.FALSE);
        when(tbMemberAssetMapper.findByGroupIdsAndMobile(anyList(), any())).thenReturn(
                Collections.singletonList(TbMemberAsset.builder().grade("02").groupId("8aaa82ea804d07cd01805174dd3b000c").currentPoints(0)
                        .vipcode("KERRY00001").mobile("13000000000").mallId("8aaa81cb7c836c6b017c83e2c76f0000").build()));

        when(memberRegisterService.saveMember(any())).thenReturn(1);
        when(profileServiceClient.alipayCreateUser(any())).thenReturn(IdDto.builder().id("00000011").build());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.memberSignUp(resource));
    }

    @Test
    void updateMemberStatus() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.updateMemberStatus("", Collections.emptyList()));
        when(tbMemberAssetMapper.queryMemberByGroupIdAndVipcodes(any(), any()))
                .thenReturn(Collections.singletonList(TbMemberAsset.builder().status("1").build()));
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.updateMemberStatus("", Collections.emptyList()));
    }

    @Test
    void updateMemberCompletedStatus() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.updateMemberCompletedStatus(TbMemberAsset.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY100213686").build()));
    }

    @Test
    void getMemberById() {
        assertNull(tbMemberAssetService.getMemberById("1699"));
    }

    @Test
    void getMemberAllInfoById() {
        assertNull(tbMemberAssetService.getMemberAllInfoById("1699"));
    }

    @Test
    void updateMemberRegisterSource() {
        MemberRegisterResourceDto resource = MemberRegisterResourceDto.builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .kipUserId("2c9d85bc8489e0ba01849e4629760003")
                .registerSource("12345")
                .registerSourceLabel("12345")
                .registerSourceRemark("12345")
                .build();
        when(tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(any(), any()))
                .thenReturn(TbMemberAsset.builder().joinTime(DateUtil.parseDateTime("2024-01-01 11:00:00")).build());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.updateMemberRegisterSource(resource));

        when(tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(any(), any()))
                .thenReturn(TbMemberAsset.builder().joinTime(DateUtil.date()).build());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.updateMemberRegisterSource(resource));
    }

    @Test
    void updateMemberGrade() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.updateMemberGrade(TbMemberAsset.builder().build()));

        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.updateMemberGrade(TbMemberAsset.builder().id(1L).build()));
    }

    @Test
    void kipSyncMemberCrmProcess() {
        KipSyncMemberResource resource = KipSyncMemberResource.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .userId("2c9d85bc8489e0ba01849e4629760003")
                .build();
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource));

        KipSyncMemberResource resource1 = KipSyncMemberResource.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .userId("2c9d85bc8489e0ba01849e4629760003")
                .phoneNumber("189")
                .isInvitation(true)
                .build();
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource1));

        when(tbMemberAssetMapper.findByKipUserId(any()))
                .thenReturn(Collections.singletonList(TbMemberAsset.builder().mobile("189").build()));
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource1));

        when(tbMemberAssetMapper.findByKipUserId(any()))
                .thenReturn(Collections.singletonList(TbMemberAsset.builder().mobile("173").build()));
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource1));

        when(hiveVasService.getMallByLbsId(any())).thenReturn(MallItem.builder().build());
        // tbMemberAsset不为空
        when(tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(any(), any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource1));

        // tbMemberAsset为空 中间查询不为空
        when(tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(any(), any()))
                .thenReturn(null);
        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource1));

        // tbMemberAsset为空 中间查询为空
        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource1));

        when(tbMemberGradeService.getGroupMiniGradeCode(any())).thenReturn("01");
        when(memberRegisterService.saveMember(any())).thenReturn(0);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource1));

        when(memberRegisterService.saveMember(any())).thenReturn(1);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource1));

        KipSyncMemberResource resource2 = KipSyncMemberResource.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .userId("2c9d85bc8489e0ba01849e4629760003")
                .phoneNumber("189")
                .source("SZKP-MIGRATION")
                .isInvitation(true)
                .build();
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource2));
    }

    @Test
    void kipSyncMemberCrmProcess1() {
        KipSyncMemberResource resource = KipSyncMemberResource.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .userId("2c9d85bc8489e0ba01849e4629760003")
                .phoneNumber("13000000000")
                .isInvitation(Boolean.FALSE)
                .build();

        MallItem mallItem = MallItem.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").build();
        when(hiveVasService.getMallByLbsId(any())).thenReturn(mallItem);
        when(tbMemberGradeService.getGroupMiniGradeCode(any())).thenReturn("01");
        when(mallConfig.isKoBigGroup(any())).thenReturn(Boolean.TRUE);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource));

        when(tbMemberAssetMapper.findByGroupIdsAndMobile(anyList(), any())).thenReturn(
                Collections.singletonList(TbMemberAsset.builder().grade("02").groupId("8aaa82ea804d07cd01805174dd3b000c").currentPoints(0)
                        .vipcode("KERRY00001").mobile("13000000000").mallId("8aaa81cb7c836c6b017c83e2c76f0000").build()));
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource));

        when(memberRegisterService.saveMember(any())).thenReturn(1);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource));
    }

    @Test
    void kipSyncMemberCrmProcess2() {
        KipSyncMemberResource resource = KipSyncMemberResource.builder()
                .lbsId("8aaa82ea804d07cd0180516ff03b0008")
                .brandId("8aaa81947c6e1ca0017c73c13cc30006")
                .userId("2c9d85bc8489e0ba01849e4629760003")
                .phoneNumber("13000000000")
                .build();

        MallItem mallItem = MallItem.builder().groupId("8aaa81947c6e1ca0017c73c13cc30006").mallId("8aaa82ea804d07cd0180516ff03b0008").build();
        when(hiveVasService.getMallByLbsId(any())).thenReturn(mallItem);
        when(tbMemberGradeService.getGroupMiniGradeCode(any())).thenReturn("01");
        TbMemberAsset tbMemberAsset = TbMemberAsset.builder().id(10010L).currentPoints(100).grade("01").vipcode("KERRY00000001")
                .groupId("aaa80b47c784020017c78b00d060022").mobile("***********").status("1").build();
        when(tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(any(), any())).thenReturn(tbMemberAsset);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource));
    }

    @Test
    void kipSyncMemberCrmProcess3() {
        KipSyncMemberResource resource = KipSyncMemberResource.builder()
                .lbsId("8aaa82ea804d07cd0180516ff03b0008")
                .brandId("8aaa81947c6e1ca0017c73c13cc30006")
                .userId("2c9d85bc8489e0ba01849e4629760003")
                .phoneNumber("13000000000")
                .build();

        MallItem mallItem = MallItem.builder().groupId("8aaa81947c6e1ca0017c73c13cc30006").mallId("8aaa82ea804d07cd0180516ff03b0008").build();
        when(hiveVasService.getMallByLbsId(any())).thenReturn(mallItem);
        when(tbMemberGradeService.getGroupMiniGradeCode(any())).thenReturn("01");
        TbMemberAsset tbMemberAsset = TbMemberAsset.builder().id(10010L).currentPoints(100).grade("01").vipcode("KERRY00000001")
                .groupId("aaa80b47c784020017c78b00d060022").mobile("***********").status("1").build();
        when(tbMemberAssetMapper.queryMemberByGroupIdAndMobile(any(), any())).thenReturn(tbMemberAsset);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.kipSyncMemberCrmProcess(resource));
    }

    @Test
    void removeMemberByKipUserId() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeMemberByKipUserId(""));
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeMemberByKipUserId("2c9d85bc8489e0ba01849e4629760003"));
        when(tbMemberAssetMapper.findByKipUserId(any())).thenReturn(Collections.singletonList(TbMemberAsset.builder().build()));
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeMemberByKipUserId("2c9d85bc8489e0ba01849e4629760003"));
    }

    @Test
    void removeMemberByMobile() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeMemberByMobile(""));
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeMemberByMobile("2c9d85bc8489e0ba01849e4629760003"));
    }

    @Test
    void removeGroupMemberAccount() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeGroupMemberAccount(BrandMemberDeleteResource.builder().build()));

        BrandMemberDeleteResource resource = BrandMemberDeleteResource.builder()
                .groupId("8aaa80b47c784020017c78b00d060022")
                .kipUserId("2c9d85bc8489e0ba01849e4629760003")
                .mobile("***********")
                .build();
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeGroupMemberAccount(resource));

        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeGroupMemberAccount(BrandMemberDeleteResource.builder()
                .groupId("8aaa80b47c784020017c78b00d060022")
                .build()));
    }

    @Test
    void removeGroupMemberAccount1() {
        TbMemberAsset tbMemberAsset = TbMemberAsset.builder().groupId("8aaa80b47c784020017c78b00d060022").currentPoints(0).vipcode("KERRY0001").grade("01").build();
        List<TbMemberAsset> memberList = Collections.singletonList(tbMemberAsset);
        when(tbMemberAssetMapper.findByGroupIdsAndKipUserId(anyList(), any()))
                .thenReturn(memberList);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeGroupMemberAccount(BrandMemberDeleteResource.builder()
                .groupId("8aaa80b47c784020017c78b00d060022")
                .kipUserId("2c9d85bc8489e0ba01849e4629760003")
                .kipUserId("12345").build()));
    }

    @Test
    void removeGroupMemberAccount2() {
        TbMemberAsset tbMemberAsset = TbMemberAsset.builder().groupId("8aaa80b47c784020017c78b00d060022").currentPoints(0)
                .mobile("***********").vipcode("KERRY0001").grade("01").build();
        List<TbMemberAsset> memberList = Collections.singletonList(tbMemberAsset);
        when(tbMemberAssetMapper.findByGroupIdsAndMobile(anyList(), any()))
                .thenReturn(memberList);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.removeGroupMemberAccount(BrandMemberDeleteResource.builder()
                .groupId("8aaa80b47c784020017c78b00d060022")
                .kipUserId("2c9d85bc8489e0ba01849e4629760003").mobile("***********").build()));
    }

    @Test
    void revertMemberById() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.revertMemberById("122"));
    }

    @Test
    void queryMemberByGroupIdAndMobile() {
        assertNull(tbMemberAssetService.queryMemberByGroupIdAndMobile("4028e3817c2b3f79017c2b48c54c0000", "***********"));
    }

    @Test
    void queryMemberByGroupIdAndKipUserId() {
        assertNull(tbMemberAssetService.queryMemberByGroupIdAndKipUserId("4028e3817c2b3f79017c2b48c54c0000", "2c9d85bc8489e0ba01849e4629760003"));
    }

    @Test
    void queryMemberByGroupIdAndKipUserIds() {
        Assertions.assertNotNull(tbMemberAssetService.queryMemberByGroupIdAndKipUserIds("4028e3817c2b3f79017c2b48c54c0000", null));
    }

    @Test
    void queryMemberByGroupIdAndVipCode() {
        assertNull(tbMemberAssetService.queryMemberByGroupIdAndVipCode("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213686"));
    }

    @Test
    void updatePoints() {
        TbMemberAsset asset = TbMemberAsset.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").kipUserId("2c9d85bc8489e0ba01849e4629760003").currentPoints(0).build();
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.updatePoints(asset));
    }

    @Test
    void getMemberCountBetweenDate() {
        when(tbMemberAssetMapper.getMemberCountBetweenDate(any(), any(), any(), any(), any()))
                .thenReturn(4);
        Assertions.assertEquals(4, tbMemberAssetService.getMemberCountBetweenDate("4028e3817c2b3f79017c2b48c54c0000", "", "", "", ""));
    }

    @Test
    void updateById() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetService.updateById(TbMemberAsset.builder().build()));
    }

}
