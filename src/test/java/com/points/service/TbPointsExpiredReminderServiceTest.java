package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbBatchIntegralPresentDetail;
import com.kerryprops.kip.service.integral.entity.TbPointClearedDetail;
import com.kerryprops.kip.service.integral.entity.TbPointsExpiredReminder;
import com.kerryprops.kip.service.integral.mapper.TbBatchIntegralPresentDetailMapper;
import com.kerryprops.kip.service.integral.mapper.TbPointsExpiredReminderMapper;
import com.kerryprops.kip.service.integral.service.TbPointClearedDetailService;
import com.kerryprops.kip.service.integral.service.TbPointsDetailService;
import com.kerryprops.kip.service.integral.service.impl.TbPointsExpiredReminderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class TbPointsExpiredReminderServiceTest {

    @InjectMocks
    private TbPointsExpiredReminderServiceImpl tbPointsExpiredReminderService;

    @Mock
    private TbPointsExpiredReminderMapper tbPointsExpiredReminderMapper;
    @Mock
    private TbPointClearedDetailService tbPointClearedDetailService;
    @Mock
    private TbBatchIntegralPresentDetailMapper tbBatchIntegralPresentDetailMapper;
    @Mock
    private TbPointsDetailService tbPointsDetailService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("isShowC == 0")
    void getPointsClearMessage() {
        Assertions.assertNotNull(tbPointsExpiredReminderService.getPointsClearMessage("", "", ""));

        Mockito.when(tbPointsExpiredReminderMapper.findByMallIdAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbPointsExpiredReminder.builder().isShowC(0).build());
        Assertions.assertNotNull(tbPointsExpiredReminderService.getPointsClearMessage("", "", ""));

    }

    @Test
    @DisplayName("isShowC == 1")
    void getPointsClearMessageCase1() {
        Mockito.when(tbPointsExpiredReminderMapper.findByMallIdAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbPointsExpiredReminder.builder()
                        .isShowC(1)
                        .showType(1)
                        .aheadMonthNum(1)
                        .aheadDayNum(10)
                        .yearEnd("123456")
                        .notYearEnd("789101112")
                        .build());
        Assertions.assertNotNull(tbPointsExpiredReminderService.getPointsClearMessage("", "", ""));

        Mockito.when(tbPointClearedDetailService.getLatestMemberClearIntegral(Mockito.any(), Mockito.any()))
                .thenReturn(TbPointClearedDetail.builder().clearDate(null).build());
        Assertions.assertNotNull(tbPointsExpiredReminderService.getPointsClearMessage("", "", ""));

        Mockito.when(tbPointClearedDetailService.getLatestMemberClearIntegral(Mockito.any(), Mockito.any()))
                .thenReturn(TbPointClearedDetail.builder().clearDate(DateUtil.date()).build());
        Assertions.assertNotNull(tbPointsExpiredReminderService.getPointsClearMessage("", "", ""));

        Mockito.when(tbPointsExpiredReminderMapper.findByMallIdAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbPointsExpiredReminder.builder()
                        .isShowC(1)
                        .showType(2)
                        .aheadMonthNum(1)
                        .aheadDayNum(10)
                        .yearEnd("123456")
                        .notYearEnd("789101112")
                        .build());
        Mockito.when(tbPointClearedDetailService.getLatestMemberClearIntegral(Mockito.any(), Mockito.any()))
                .thenReturn(TbPointClearedDetail.builder().clearDate(DateUtil.date()).clearDate(DateUtil.date()).build());
        Assertions.assertNotNull(tbPointsExpiredReminderService.getPointsClearMessage("", "", ""));

    }

    @Test
    @DisplayName("isShowC == 2")
    void getPointsClearMessageCase2() {
        Mockito.when(tbPointsExpiredReminderMapper.findByMallIdAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbPointsExpiredReminder.builder()
                        .isShowC(2)
                        .showType(1)
                        .aheadMonthNum(1)
                        .aheadDayNum(10)
                        .yearEnd("123456")
                        .notYearEnd("789101112")
                        .build());
        Assertions.assertNotNull(tbPointsExpiredReminderService.getPointsClearMessage("", "", ""));

        Mockito.when(tbBatchIntegralPresentDetailMapper.queryDetailByVipcodeAndOverdueTime(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbBatchIntegralPresentDetail.builder().importIntegral(1).overdueTime(DateUtil.date()).build());
        Mockito.when(tbPointsDetailService.getMemberConsumePointsBetweenDate(Mockito.any())).thenReturn(2);
        Assertions.assertNotNull(tbPointsExpiredReminderService.getPointsClearMessage("", "", ""));

        Mockito.when(tbPointsExpiredReminderMapper.findByMallIdAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbPointsExpiredReminder.builder()
                        .isShowC(2)
                        .showType(2)
                        .aheadMonthNum(1)
                        .aheadDayNum(10)
                        .yearEnd("123456")
                        .notYearEnd("789101112")
                        .build());
        Assertions.assertNotNull(tbPointsExpiredReminderService.getPointsClearMessage("", "", ""));
    }
}
