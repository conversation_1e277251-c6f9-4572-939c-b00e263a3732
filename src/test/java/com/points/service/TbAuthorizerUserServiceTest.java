package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbAuthorizerUser;
import com.kerryprops.kip.service.integral.mapper.TbAuthorizerUserMapper;
import com.kerryprops.kip.service.integral.service.impl.TbAuthorizerUserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 11/14/2024 14:36
 **********************************************************************************************************************/

@Slf4j
class TbAuthorizerUserServiceTest {

    @InjectMocks
    private TbAuthorizerUserServiceImpl tbAuthorizerUserServiceImpl;
    @Mock
    private TbAuthorizerUserMapper tbAuthorizerUserMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getAuthorizerUserByUser() {
        Assertions.assertNull(tbAuthorizerUserServiceImpl.getAuthorizerUserByUser(""));

        Mockito.when(tbAuthorizerUserMapper.getAuthorizerUserByUser(any())).thenReturn(TbAuthorizerUser.builder().build());
        Assertions.assertNotNull(tbAuthorizerUserServiceImpl.getAuthorizerUserByUser("123"));
    }

}
