package com.points.service;

import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import com.kerryprops.kip.pmw.client.resource.QueryPointsAuthOutputResource;
import com.kerryprops.kip.pmw.client.service.BizCircleClientService;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsAuthRecordService;
import com.kerryprops.kip.service.integral.service.WechatPaymentAutoPointsCheckService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> nancy
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/
class WechatPaymentAutoPointsCheckServiceTest {

    @InjectMocks
    private WechatPaymentAutoPointsCheckService wechatPaymentAutoPointsCheckService;

    @Mock
    private TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    @Mock
    private BizCircleClientService bizCircleClientService;
    @Mock
    private HeaderResource headerResource;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void checkMallWechatAutoPointsStatus() {
        MallItem mallItem = new MallItem();
        mallItem.setMallId("mallId");
        Mockito.when(tbInsensatePointsAuthRecordService.getMallBatchData("mallId",  0L, 200))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatPaymentAutoPointsCheckService.checkMallWechatAutoPointsStatus(mallItem));

        TbInsensatePointsAuthRecord authRecord1 = new TbInsensatePointsAuthRecord();
        authRecord1.setId(1L);
        Mockito.when(tbInsensatePointsAuthRecordService.getMallBatchData("mallId",  0L, 200))
                .thenReturn(Collections.singletonList(authRecord1));
        Mockito.when(bizCircleClientService.queryPointsAuth(Mockito.any()))
                .thenThrow(new BizException(400, ""));
        Assertions.assertDoesNotThrow(() -> wechatPaymentAutoPointsCheckService.checkMallWechatAutoPointsStatus(mallItem));
    }

    @Test
    void checkMallWechatAutoPointsStatusWithException() {
        MallItem mallItem = new MallItem();
        mallItem.setMallId("mallId");
        TbInsensatePointsAuthRecord authRecord1 = new TbInsensatePointsAuthRecord();
        authRecord1.setId(1L);
        authRecord1.setStatus(0);
        Mockito.when(tbInsensatePointsAuthRecordService.getMallBatchData("mallId",  0L, 200))
                .thenReturn(Collections.singletonList(authRecord1));
        Mockito.when(bizCircleClientService.queryPointsAuth(Mockito.any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatPaymentAutoPointsCheckService.checkMallWechatAutoPointsStatus(mallItem));


    }

    @Test
    void checkMallWechatAutoPointsCase1() {
        MallItem mallItem = new MallItem();
        mallItem.setMallId("mallId");
        TbInsensatePointsAuthRecord authRecord2 = new TbInsensatePointsAuthRecord();
        authRecord2.setId(1L);
        authRecord2.setStatus(1);
        Mockito.when(tbInsensatePointsAuthRecordService.getMallBatchData("mallId",  0L, 200))
                .thenReturn(Collections.singletonList(authRecord2));
        QueryPointsAuthOutputResource.QueryPointsAuthOutputBodyResource resource = new QueryPointsAuthOutputResource.QueryPointsAuthOutputBodyResource();
        resource.setAuthorizeState("AUTHORIZED");
        QueryPointsAuthOutputResource outputResource = new QueryPointsAuthOutputResource(null, resource, "");
        Mockito.when(bizCircleClientService.queryPointsAuth(Mockito.any()))
                .thenReturn(outputResource);
        Assertions.assertDoesNotThrow(() -> wechatPaymentAutoPointsCheckService.checkMallWechatAutoPointsStatus(mallItem));
    }
}
