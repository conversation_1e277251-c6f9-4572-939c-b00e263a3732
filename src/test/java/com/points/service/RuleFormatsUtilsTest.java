package com.points.service;

import com.kerryprops.kip.service.integral.common.utils.RuleFormatsUtils;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 08/05/2024 13:50
 **********************************************************************************************************************/

@Slf4j
class RuleFormatsUtilsTest extends BaseTest {



    @Test
    void getGroupRuleFormats() {
        String ruleFormats = "[{\"mallId\":\"8aaa81cb7c836c6b017c83e2c76f0000\",\"format\":\"all,01,1,2,3,4,0909090909090\",\"formatList\":[\"all\",\"01\",\"1\",\"2\",\"3\",\"4\",\"0909090909090\"]},{\"mallId\":\"8aaa81cb7c836c6b017c83e46b110001\",\"format\":\"all,01,1,2,3,4,0909090909090\",\"formatList\":[\"all\",\"01\",\"1\",\"2\",\"3\",\"4\",\"0909090909090\"]}]";
        Assertions.assertDoesNotThrow(() -> RuleFormatsUtils.getGroupRuleFormats(ruleFormats));
    }


}
