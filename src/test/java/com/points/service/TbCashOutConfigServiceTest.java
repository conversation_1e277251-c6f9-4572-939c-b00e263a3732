package com.points.service;

import com.kerryprops.kip.service.integral.service.TbCashOutConfigService;
import com.kerryprops.kip.service.integral.webservice.resource.TbCashOutConfigResource;
import com.points.BaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 12/30/2024 17:29
 **********************************************************************************************************************/
class TbCashOutConfigServiceTest extends BaseTest {

    @Autowired
    private TbCashOutConfigService tbCashOutConfigService;

    @Test
    void getConfig() {
        Assertions.assertDoesNotThrow(() -> tbCashOutConfigService.getConfig(TbCashOutConfigResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").businessType("XS0110").build()));
    }

}
