package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.enums.MessageTypeEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.entity.TbTemplateMessage;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TemplateMessageSendServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> nancy
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/
class TemplateMessageSendServiceTest {

    @InjectMocks
    private TemplateMessageSendServiceImpl templateMessageSendServiceImpl;

    @Mock
    private SysDictService sysDictService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private KerryStaffService kerryStaffService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;

    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private MessageService messageService;
    @Mock
    private MallConfig mallConfig;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void sendMessage() {
        SendMessageDto sendMessageDto = SendMessageDto.builder()
                .memberId("1699")
                .templateType(1)
                .messageType(MessageTypeEnum.NEXT_DAY.getType())
                .updateDate(DateUtil.formatDateTime(new Date()))
                .dictType("XS0001")
                .number("5")
                .build();
        Mockito.when(tbMemberAssetService.getMemberById(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> templateMessageSendServiceImpl.sendMessage(sendMessageDto));

        Mockito.when(tbMemberAssetService.getMemberById(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("kipUserId").mallId("mallId").build());
        Mockito.when(hiveVasService.getLbsName(Mockito.any()))
                .thenReturn(HiveLbsInfoDto.builder().build());
        Mockito.when(messageService.getModelTemplate(Mockito.any()))
                .thenReturn(Collections.singletonList(TbTemplateMessage.builder().build()));

        Mockito.when(kerryStaffService.findByKipUserId(Mockito.any()))
                .thenReturn(CustomerUserDto.builder().build());
        Mockito.when(sysDictService.findByDictType(Mockito.any()))
                .thenReturn(SysDict.builder().dictName("dictName").build());
        Mockito.when(tbMemberGradeService.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGrade.builder().build());
        Assertions.assertDoesNotThrow(() -> templateMessageSendServiceImpl.sendMessage(sendMessageDto));

        Mockito.when(messageService.getModelTemplate(Mockito.any()))
                .thenReturn(Collections.singletonList(TbTemplateMessage.builder().content("{\"keyword1\":{\"value\":\"场次时间内报名活动{UPDATE_DATE}\",\"color\":\"#000\"},\"keyword2\":{\"value\":\"活动时间：{BEGIN_TIME}，{MEMBER_NICKNAME} \",\"color\":\"#000\"},\"keyword3\":{\"value\":\"{activity_name}@1 & {activity_addr}@2 \",\"color\":\"#000\"}}").build()));
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("QHKC").build());
        Mockito.when(messageService.getSmsTemplate(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> templateMessageSendServiceImpl.sendMessage(sendMessageDto));

        Mockito.when(messageService.getSmsTemplate(Mockito.any()))
                .thenReturn(Collections.singletonList(TbSmsTemplateConfig.builder().build()));
        Assertions.assertDoesNotThrow(() -> templateMessageSendServiceImpl.sendMessage(sendMessageDto));

        Mockito.when(messageService.getSmsTemplate(Mockito.any()))
                .thenReturn(Collections.singletonList(TbSmsTemplateConfig.builder().mallid("mallId").build()));
        Assertions.assertDoesNotThrow(() -> templateMessageSendServiceImpl.sendMessage(sendMessageDto));

        Mockito.when(messageService.getSmsTemplate(Mockito.any()))
                .thenReturn(Collections.singletonList(TbSmsTemplateConfig.builder().mallid("mallId").smsPreview("【嘉里建设】您的会员卡号「{MEMBER_CARD_NO}」发生积分变更：「{POINTS_CHANGE_NUMBER}」").build()));
        Assertions.assertDoesNotThrow(() -> templateMessageSendServiceImpl.sendMessage(sendMessageDto));
    }

}
