package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeRuleMapper;
import com.kerryprops.kip.service.integral.service.impl.TbMemberGradeRuleServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 07/31/2024 10:37
 **********************************************************************************************************************/

@Slf4j
class TbMemberGradeRuleServiceTest {

    @InjectMocks
    private TbMemberGradeRuleServiceImpl tbMemberGradeRuleServiceimpl;
    @Mock
    private TbMemberGradeRuleMapper tbMemberGradeRuleMapper;
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void queryGradeRulesByGroupIdAndGradeAndRuleTypes() {
        Mockito.when(tbMemberGradeRuleMapper.queryGradeRulesByGroupIdAndGradeAndRuleTypes(Mockito.any(), Mockito.any(), Mockito.anyList())).thenReturn(new ArrayList<>());
        Assertions.assertNotNull(tbMemberGradeRuleServiceimpl.queryGradeRulesByGroupIdAndGradeAndRuleTypes("4028e3817c2b3f79017c2b48c54c0000", "", null));
    }

    @Test
    void queryUpgradeRule() {
        Assertions.assertNull(tbMemberGradeRuleServiceimpl.queryUpgradeRule("4028e3817c2b3f79017c2b48c54c0000", null));

        Mockito.when(tbMemberGradeRuleMapper.selectOne(Mockito.any())).thenReturn(new TbMemberGradeRule());
        Assertions.assertNotNull(tbMemberGradeRuleServiceimpl.queryUpgradeRule("4028e3817c2b3f79017c2b48c54c0000", "03"));
    }

    @Test
    void queryByGroupIdAndType() {
        Mockito.when(tbMemberGradeRuleMapper.findGradeRulesByGroupIdAndType(Mockito.any(),Mockito.any()))
                .thenReturn(new ArrayList<>());
        Assertions.assertNotNull(tbMemberGradeRuleServiceimpl.queryByGroupIdAndType("4028e3817c2b3f79017c2b48c54c0000", 1));
    }

    @Test
    void queryByGroupIdAndRuleType() {
        Mockito.when(tbMemberGradeRuleMapper.findGradeRulesByGroupIdAndRuleType(Mockito.any(),Mockito.any()))
                .thenReturn(new ArrayList<>());
        Assertions.assertNotNull(tbMemberGradeRuleServiceimpl.queryByGroupIdAndRuleType("4028e3817c2b3f79017c2b48c54c0000", 3));
    }

    @Test
    void queryByGroupIdAndGradeAndType() {
        Mockito.when(tbMemberGradeRuleMapper.queryGradeRulesByGroupIdAndGradeAndType(Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(new ArrayList<>());
        Assertions.assertNotNull(tbMemberGradeRuleServiceimpl.queryByGroupIdAndGradeAndType("4028e3817c2b3f79017c2b48c54c0000", "",3));
    }

    @Test
    void isCycleYear() {
        Mockito.when(tbMemberGradeRuleMapper.isCycleYear("4028e3817c2b3f79017c2b48c54c0000",3)).thenReturn(Boolean.FALSE);
        Assertions.assertEquals(Boolean.FALSE, tbMemberGradeRuleServiceimpl.isCycleYear("4028e3817c2b3f79017c2b48c54c0000", 3));
    }

}
