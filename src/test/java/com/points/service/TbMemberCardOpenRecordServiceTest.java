package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbMemberCardOpenRecord;
import com.kerryprops.kip.service.integral.mapper.TbMemberCardOpenRecordMapper;
import com.kerryprops.kip.service.integral.service.impl.TbMemberCardOpenRecordServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 07/31/2024 10:37
 **********************************************************************************************************************/

@Slf4j
class TbMemberCardOpenRecordServiceTest {

    @InjectMocks
    private TbMemberCardOpenRecordServiceImpl tbMemberCardOpenRecordServiceImpl;
    @Mock
    private TbMemberCardOpenRecordMapper tbMemberCardOpenRecordMapper;
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void saveOrUpdateRecord() {
        TbMemberCardOpenRecord record = TbMemberCardOpenRecord.builder().build();
        Mockito.when(tbMemberCardOpenRecordMapper.selectByOpenIdAndMallId(Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbMemberCardOpenRecordServiceImpl.saveOrUpdateRecord(record));

        TbMemberCardOpenRecord record1 = TbMemberCardOpenRecord
                .builder()
                .eventType("01")
                .cardId("01")
                .kipUserId("01")
                .build();
        Mockito.when(tbMemberCardOpenRecordMapper.selectByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberCardOpenRecord
                        .builder()
                        .eventType("01")
                        .cardId("01")
                        .kipUserId("01")
                        .build());
        Assertions.assertDoesNotThrow(() -> tbMemberCardOpenRecordServiceImpl.saveOrUpdateRecord(record1));

        Mockito.when(tbMemberCardOpenRecordMapper.selectByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberCardOpenRecord
                        .builder()
                        .eventType("02")
                        .cardId("01")
                        .kipUserId("01")
                        .build());
        Assertions.assertDoesNotThrow(() -> tbMemberCardOpenRecordServiceImpl.saveOrUpdateRecord(record1));

        Mockito.when(tbMemberCardOpenRecordMapper.selectByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberCardOpenRecord
                        .builder()
                        .eventType("01")
                        .cardId("02")
                        .kipUserId("01")
                        .build());
        Assertions.assertDoesNotThrow(() -> tbMemberCardOpenRecordServiceImpl.saveOrUpdateRecord(record1));

        Mockito.when(tbMemberCardOpenRecordMapper.selectByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberCardOpenRecord
                        .builder()
                        .eventType("01")
                        .cardId("01")
                        .kipUserId("02")
                        .build());
        Assertions.assertDoesNotThrow(() -> tbMemberCardOpenRecordServiceImpl.saveOrUpdateRecord(record1));
    }

}
