package com.points.service;

import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.model.dto.BrandGuideRespDto;
import com.kerryprops.kip.service.integral.service.impl.ZIndexResourcesServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> nancy
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/
class ZIndexResourcesServiceTest {

    @InjectMocks
    private ZIndexResourcesServiceImpl zIndexResourcesServiceImpl;

    @Mock
    private HiveVasClient hiveVasClient;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getBrandLogoByMallId() {
        Mockito.when(hiveVasClient.getBrandGuideHomePage("")).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> zIndexResourcesServiceImpl.getBrandLogoByMallId(""));
        BrandGuideRespDto brandGuideRespDto = new BrandGuideRespDto();
        brandGuideRespDto.setRedirectDetailSwitch(Boolean.TRUE);
        Mockito.when(hiveVasClient.getBrandGuideHomePage("")).thenReturn(Collections.singletonList(brandGuideRespDto));
        Assertions.assertDoesNotThrow(() -> zIndexResourcesServiceImpl.getBrandLogoByMallId(""));
    }



}
