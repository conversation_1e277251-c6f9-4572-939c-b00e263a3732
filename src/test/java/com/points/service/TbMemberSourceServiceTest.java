package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MemberRegisterResourceDto;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import com.kerryprops.kip.service.integral.service.TbMemberSourceService;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 11/08/2024 14:23
 **********************************************************************************************************************/

@Slf4j
class TbMemberSourceServiceTest extends BaseTest {

    @Autowired
    private TbMemberSourceService tbMemberSourceService;
    @MockBean
    private TbMemberAssetService tbMemberAssetService;

    @Test
    void checkAndSave() {
        MemberRegisterResourceDto dto = MemberRegisterResourceDto.builder().registerSource("mini").registerSourceLabel("小程序").registerSourceRemark("小程序")
                .groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").kipUserId("2c9d85bc8489e0ba01849e4629760003")
                .regMemberSourceOriginalParams("{\"utm_lbs\": \"1111\", \"utm_channel\": \"1111\", \"channel\": \"1111\", \"utm_method\": \"1111\"}").build();
        when(tbMemberAssetService.findByVipcodeAndGroupId(any(String.class), any(String.class)))
                .thenReturn(TbMemberAsset.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022")
                        .grade("1").currentPoints(100).status("1").mobile("13000000000").vipcode("KERRY100213686").build());
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByVipcodeAndGroupId("KERRY100213686", "4028e3817c2b3f79017c2b48c54c0000");
        // 新增
        Assertions.assertDoesNotThrow(() -> tbMemberSourceService.checkAndSave(dto, tbMemberAsset));
    }

}
