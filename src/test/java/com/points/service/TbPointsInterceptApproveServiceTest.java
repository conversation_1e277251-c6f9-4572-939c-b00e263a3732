package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.enums.PointsInterceptEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsIntercept;
import com.kerryprops.kip.service.integral.entity.TbPointsInterceptConfig;
import com.kerryprops.kip.service.integral.mapper.TbPointsInterceptApproveMapper;
import com.kerryprops.kip.service.integral.mapper.TbPointsInterceptConfigMapper;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.TbPointsInterceptQueryDto;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.service.impl.TbPointsInterceptApproveServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Arrays;
import java.util.Collections;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 07/31/2024 10:37
 **********************************************************************************************************************/

@Slf4j
class TbPointsInterceptApproveServiceTest {

    @InjectMocks
    private TbPointsInterceptApproveServiceImpl tbPointsInterceptApproveServiceImpl;
    @Mock
    private TbPointsInterceptConfigMapper tbPointsInterceptConfigMapper;
    @Mock
    private TbPointsInterceptApproveMapper tbPointsInterceptApproveMapper;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private ApplicationEventPublisher applicationEventPublisher;
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void checkSaleIsIntercept() {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().shopId("123").build();
        Assertions.assertNotNull(tbPointsInterceptApproveServiceImpl.checkSaleIsIntercept(salesAutoPointsDto));

        // list.size() == whiteList.size()
        Mockito.when(tbPointsInterceptConfigMapper.findByMallId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbPointsInterceptConfig.builder().shopNo("123").whiteList(1).build()));
        Assertions.assertNotNull(tbPointsInterceptApproveServiceImpl.checkSaleIsIntercept(salesAutoPointsDto));

        // CollectionUtils.isEmpty(list)
        Mockito.when(tbPointsInterceptConfigMapper.findByMallId(Mockito.any()))
                .thenReturn(Arrays.asList(TbPointsInterceptConfig.builder().shopNo("123").whiteList(1).build(),
                        TbPointsInterceptConfig.builder().shopNo("123").whiteList(0).build()));
        Assertions.assertNotNull(tbPointsInterceptApproveServiceImpl.checkSaleIsIntercept(salesAutoPointsDto));

    }

    @Test
    void checkSaleIsInterceptQuery() {
        // 会员为空
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().shopId("123").build();
        Mockito.when(tbPointsInterceptConfigMapper.findByMallId(Mockito.any()))
                .thenReturn(Arrays.asList(TbPointsInterceptConfig.builder().id(1L).shopNo("123").whiteList(1).build(),
                        TbPointsInterceptConfig.builder().id(2L).shopNo("456").whiteList(1).build()));
        Assertions.assertNotNull(tbPointsInterceptApproveServiceImpl.checkSaleIsIntercept(salesAutoPointsDto));

        Mockito.when(tbPointsInterceptConfigMapper.findByMallId(Mockito.any()))
                .thenReturn(Arrays.asList(TbPointsInterceptConfig.builder().id(1L).shopNo("123").whiteList(1).build(),
                        TbPointsInterceptConfig.builder().id(2L).shopNo("456")
                                .timeType(PointsInterceptEnum.DAY.getType())
                                .count(10)
                                .whiteList(1).build(),
                        TbPointsInterceptConfig.builder().id(3L).shopNo("456")
                                .timeType(PointsInterceptEnum.NATURAL_WEEK.getType())
                                .count(10)
                                .whiteList(1).build(),
                        TbPointsInterceptConfig.builder().id(4L).shopNo("456")
                                .timeType(PointsInterceptEnum.CYCLE_WEEK.getType())
                                .count(10)
                                .whiteList(1).build(),
                        TbPointsInterceptConfig.builder().id(5L).shopNo("456")
                                .timeType(PointsInterceptEnum.NATURAL_MONTH.getType())
                                .count(10)
                                .whiteList(1).build(),
                        TbPointsInterceptConfig.builder().id(6L).shopNo("456")
                                .timeType(PointsInterceptEnum.CYCLE_MONTH.getType())
                                .count(1)
                                .whiteList(1).build()));
        SalesAutoPointsDto salesAutoPointsDto1 = SalesAutoPointsDto.builder()
                .saleDate(DateUtil.formatDateTime(DateUtil.date()))
                .member(TbMemberAsset.builder().build())
                .shopId("123").build();
        Assertions.assertNotNull(tbPointsInterceptApproveServiceImpl.checkSaleIsIntercept(salesAutoPointsDto1));

    }

    @Test
    void savePointsIntercept() {
        TbPointsIntercept intercept = TbPointsIntercept.builder().groupId("groupId").mallId("mallId").shopNo("123").build();
        Assertions.assertDoesNotThrow(() -> tbPointsInterceptApproveServiceImpl.savePointsIntercept(intercept));
    }

    @Test
    void getTotal() {
        TbPointsInterceptQueryDto  queryDto = TbPointsInterceptQueryDto.builder().shopNo("123").build();
        Mockito.when(tbPointsInterceptApproveMapper.getTotal(Mockito.any())).thenReturn(10);
        Assertions.assertEquals(10, tbPointsInterceptApproveServiceImpl.getTotal(queryDto));
    }

    @Test
    void getPageData() {

        TbPointsInterceptQueryDto tbPointsInterceptQueryDto = TbPointsInterceptQueryDto.builder().size(1).num(1).shopNo("123").build();
        Mockito.when(tbPointsInterceptApproveMapper.getPageData(Mockito.any())).thenReturn(null);
        Assertions.assertNull(tbPointsInterceptApproveServiceImpl.getPageData(tbPointsInterceptQueryDto));
    }

    @Test
    void getInterceptDto() {
        Mockito.when(tbPointsInterceptApproveMapper.getInterceptDto(Mockito.any())).thenReturn(null);
        Assertions.assertNull(tbPointsInterceptApproveServiceImpl.getInterceptDto(null));
    }


}
