package com.points.service;

import com.kerryprops.kip.service.integral.mapper.TbCardMemberRelationMapper;
import com.kerryprops.kip.service.integral.service.impl.TbCardMemberRelationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class TbCardMemberRelationServiceTest {

    @InjectMocks
    private TbCardMemberRelationServiceImpl tbCardMemberRelationService;
    @Mock
    private TbCardMemberRelationMapper tbCardMemberRelationMapper;


    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getByMallIdAndAliUserId() {
        Assertions.assertNull(tbCardMemberRelationService.getByMallIdAndAliUserId("001", "001"));
    }

    @Test
    void getByAliUserId() {
        Assertions.assertNull(tbCardMemberRelationService.getByAliUserId("001"));
    }

    @Test
    void getByMallIdAndExternalCardNo() {
        Assertions.assertNull(tbCardMemberRelationService.getByMallIdAndExternalCardNo("001", "001"));
    }

    @Test
    void getByMallIdAndMobile() {
        Assertions.assertNull(tbCardMemberRelationService.getByMallIdAndMobile("001", "001"));
    }

    @Test
    void findByExternalCardNo() {
        Assertions.assertNull(tbCardMemberRelationService.findByExternalCardNo("001"));
    }

    @Test
    void getByGroupIdAndMobileAndMallId() {
        Assertions.assertNull(tbCardMemberRelationService.getByGroupIdAndMobileAndMallId("001", "001", "12345"));
    }
}
