package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.service.promotion.impl.CurrentMonthFirstSalePromotionRule;
import com.kerryprops.kip.service.integral.service.promotion.impl.MemberGradePromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class CurrentMonthFirstSalePromotionRuleTest {

    @InjectMocks
    private CurrentMonthFirstSalePromotionRule currentMonthFirstSalePromotionRule;
    @Mock
    private TbSalesDetailService tbSalesDetailService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertNotNull(currentMonthFirstSalePromotionRule.getRuleType());
    }

    @Test
    void checkRule() {
        TbActivityPromotionCondition condition = new TbActivityPromotionCondition();
        Mockito.when(tbSalesDetailService.queryMemberMonthSalesNumber(Mockito.any())).thenReturn(0);
        Assertions.assertTrue(currentMonthFirstSalePromotionRule.checkRule(condition, SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build(), DateUtil.date(), null));
    }
}
