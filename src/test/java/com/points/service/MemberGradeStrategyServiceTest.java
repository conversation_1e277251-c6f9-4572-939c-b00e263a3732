package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.enums.RuleTimeEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.MemberGradeStrategyServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class MemberGradeStrategyServiceTest {

    @InjectMocks
    private MemberGradeStrategyServiceImpl memberGradeStrategyServiceImpl;

    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private TbMemberGradeRuleService tbMemberGradeRuleService;
    @Mock
    private TbMemberGradeChangeDetailService memberGradeChangeDetailService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("getCalculateYearAmountBeginDate")
    void modifyUpgradeLeftMoney() {
        TbMemberGradeRule memberGradeRule = TbMemberGradeRule.builder().build();
        TbMemberAsset asset = TbMemberAsset.builder().build();
        Mockito.when(tbMemberGradeRuleService.isCycleYear(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(tbMemberGradeService.getGroupMiniGradeCode(Mockito.any())).thenReturn("");
        Mockito.when(memberGradeChangeDetailService.getChangeDetailGtCreateDate(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(TbMemberGradeChangeDetail.builder().build()));
        Mockito.when(tbBaseShopService.getContractNoList(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.emptyList());
        Mockito.when(tbSalesDetailService.getMemberSaleAmountBetweenTime(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(100D);
        Assertions.assertNotNull(memberGradeStrategyServiceImpl.modifyUpgradeLeftMoney(memberGradeRule, asset));
    }

    @Test
    void getNaturalYearStartDate() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().build();
        detail.setCreateDate(DateUtil.date());
        Assertions.assertNotNull(memberGradeStrategyServiceImpl.getNaturalYearStartDate(detail, DateUtil.date()));

        Assertions.assertNotNull(memberGradeStrategyServiceImpl.getNaturalYearStartDate(TbMemberGradeChangeDetail.builder().build(), DateUtil.date()));
    }

    @Test
    void getCycleYearStartDate() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().build();
        detail.setCreateDate(DateUtil.date());
        Assertions.assertNotNull(memberGradeStrategyServiceImpl.getCycleYearStartDate(detail, DateUtil.date()));

        Assertions.assertNotNull(memberGradeStrategyServiceImpl.getCycleYearStartDate(TbMemberGradeChangeDetail.builder().build(), DateUtil.date()));
    }

    @Test
    @DisplayName("DAY")
    void getRelegationRuleBeginDate() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().build();
        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getRelegationRuleBeginDate(detail,
                        true, RuleTimeEnum.DAY.getValue(), DateUtil.date()));

        TbMemberGradeChangeDetail detail1 = TbMemberGradeChangeDetail.builder().build();
        detail1.setCreateDate(DateUtil.date());
        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getRelegationRuleBeginDate(detail1,
                        true, RuleTimeEnum.DAY.getValue(), DateUtil.date()));

        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getRelegationRuleBeginDate(detail1,
                        true, RuleTimeEnum.DAY.getValue(), DateUtil.offsetDay(DateUtil.date(), -1)));
    }

    @Test
    @DisplayName("MONTH")
    void getRelegationRuleBeginDateCaseMonth() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().build();
        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getRelegationRuleBeginDate(detail,
                        true, RuleTimeEnum.MONTH.getValue(), DateUtil.date()));

        TbMemberGradeChangeDetail detail1 = TbMemberGradeChangeDetail.builder().build();
        detail1.setCreateDate(DateUtil.date());
        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getRelegationRuleBeginDate(detail1,
                        true, RuleTimeEnum.MONTH.getValue(), DateUtil.date()));

        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getRelegationRuleBeginDate(detail1,
                        true, RuleTimeEnum.MONTH.getValue(), DateUtil.offsetDay(DateUtil.date(), -1)));
    }

    @Test
    @DisplayName("ELSE")
    void getRelegationRuleBeginDateCaseElse() {
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().build();
        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getRelegationRuleBeginDate(detail,
                        true, RuleTimeEnum.YEAR.getValue(), DateUtil.date()));

        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getRelegationRuleBeginDate(detail,
                        false, RuleTimeEnum.YEAR.getValue(), DateUtil.date()));
    }

    @Test
    void getUpgradeRuleBeginDate() {
        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getUpgradeRuleBeginDate(RuleTimeEnum.DAY.getValue(), DateUtil.date()));

        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getUpgradeRuleBeginDate(RuleTimeEnum.MONTH.getValue(), DateUtil.date()));

        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getUpgradeRuleBeginDate(RuleTimeEnum.YEAR.getValue(), DateUtil.date()));
    }

    @Test
    void getCalculateYearAmountBeginDate() {
        Mockito.when(tbMemberGradeRuleService.isCycleYear(Mockito.any(), Mockito.anyInt())).thenReturn(false);

        Mockito.when(tbMemberGradeService.getGroupMiniGradeCode(Mockito.any())).thenReturn("03");
        Mockito.when(memberGradeChangeDetailService.getChangeDetailGtCreateDate(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(TbMemberGradeChangeDetail.builder().createDate(DateUtil.date()).newGrade("03").changeType(0).build()));
        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getCalculateYearAmountBeginDate("", ""));

        Mockito.when(memberGradeChangeDetailService.getChangeDetailGtCreateDate(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(TbMemberGradeChangeDetail.builder().createDate(DateUtil.date()).newGrade("01").changeType(0).build()));
        Assertions.assertNotNull(
                memberGradeStrategyServiceImpl.getCalculateYearAmountBeginDate("", ""));
    }
}
