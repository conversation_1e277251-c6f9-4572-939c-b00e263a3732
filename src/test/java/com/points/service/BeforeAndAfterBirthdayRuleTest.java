package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.impl.BeforeAndAfterBirthdayRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 10/08/2024 10:15
 **********************************************************************************************************************/

@Slf4j
class BeforeAndAfterBirthdayRuleTest{

    @InjectMocks
    private BeforeAndAfterBirthdayRule beforeAndAfterBirthdayRule;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertDoesNotThrow(() -> beforeAndAfterBirthdayRule.getRuleType());
    }

    @Test
    void checkRule() {
        Assertions.assertDoesNotThrow(() -> beforeAndAfterBirthdayRule.checkRule(TbActivityPromotionCondition.builder().build(),
                SalesAutoPointsDto.builder().build(),
                new Date(),
                Collections.emptyList()));

        Assertions.assertDoesNotThrow(() -> beforeAndAfterBirthdayRule.checkRule(
                TbActivityPromotionCondition.builder().promotionConditionContent("123").build(),
                SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build(),
                new Date(),
                Collections.emptyList()));

        Assertions.assertDoesNotThrow(() -> beforeAndAfterBirthdayRule.checkRule(
                TbActivityPromotionCondition.builder().promotionConditionContent("-1,1").build(),
                SalesAutoPointsDto.builder().saleDate("2025-03-04").member(TbMemberAsset.builder().birthDate(DateUtil.date()).build()).build(),
                new Date(),
                Collections.emptyList()));

        Assertions.assertDoesNotThrow(() -> beforeAndAfterBirthdayRule.checkRule(
                TbActivityPromotionCondition.builder().promotionConditionContent("1,-1").build(),
                SalesAutoPointsDto.builder().saleDate("2025-03-04").member(TbMemberAsset.builder().birthDate(DateUtil.date()).build()).build(),
                new Date(),
                Collections.emptyList()));
    }


}
