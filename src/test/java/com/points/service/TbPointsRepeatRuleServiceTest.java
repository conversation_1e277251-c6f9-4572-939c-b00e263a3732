package com.points.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.MerchantCard;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.kerryprops.kip.service.integral.common.enums.TradingAreaEnum;
import com.kerryprops.kip.service.integral.config.AliPayConfig;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsRepeatRule;
import com.kerryprops.kip.service.integral.mapper.TbPointsRepeatRuleMapper;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.TbInsensatePointsAuthRecordDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.*;
import com.kerryprops.kip.service.integral.service.TbCardMemberRelationService;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsAuthRecordService;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import com.kerryprops.kip.service.integral.service.impl.AliPayClientServiceImpl;
import com.kerryprops.kip.service.integral.service.impl.TbPointsRepeatRuleServiceImpl;
import com.kerryprops.kip.service.integral.strategy.AliPayStrategy;
import com.kerryprops.kip.service.integral.webservice.resource.AlipayCardTemplateUpdateResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import static org.mockito.Mockito.when;

@Slf4j
class TbPointsRepeatRuleServiceTest {

    @InjectMocks
    private TbPointsRepeatRuleServiceImpl tbPointsRepeatRuleService;

    @Mock
    private TbPointsRepeatRuleMapper tbPointsRepeatRuleMapper;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("参数为空")
    void getRuleByMallIdAndGroupId() {
        Assertions.assertNull(tbPointsRepeatRuleService.getRuleByMallIdAndGroupId("", ""));
    }

    @Test
    void getSaleRepeatCondition() {
        Mockito.when(tbPointsRepeatRuleMapper.queryRulesByMallIdAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbPointsRepeatRule
                        .builder()
                        .whiteList(1)
                        .repeatRule("1,2,3,4")
                        .shopNo("123")
                        .salesTimeDifference(1)
                        .build());
        SalesAutoPointsDto dto = SalesAutoPointsDto
                .builder()
                .saleDate("2024-01-01 11:00:00")
                .amount("10")
                .shopId("123")
                .member(TbMemberAsset.builder().build())
                .build();
        Assertions.assertNull(tbPointsRepeatRuleService.getSaleRepeatCondition(dto, true));

        SalesAutoPointsDto dto1 = SalesAutoPointsDto
                .builder()
                .saleDate("2024-01-01 11:00:00")
                .amount("10")
                .shopId("0912")
                .member(TbMemberAsset.builder().build())
                .build();
        Assertions.assertNotNull(tbPointsRepeatRuleService.getSaleRepeatCondition(dto1, true));

        Mockito.when(tbPointsRepeatRuleMapper.queryRulesByMallIdAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbPointsRepeatRuleService.getSaleRepeatCondition(dto1, true));
    }

}