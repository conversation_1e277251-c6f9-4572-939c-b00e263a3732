package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.client.SmallTicketRecognitionClient;
import com.kerryprops.kip.service.integral.common.enums.OcrErrorCodeMappingEnum;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.common.utils.DeferredResultCacheUtils;
import com.kerryprops.kip.service.integral.config.SmallTicketProperties;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.SmallTicketRecognitionServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketCallbackResource;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketRejectResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.data.redis.core.*;

import java.util.Arrays;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 08/02/2024 09:14
 **********************************************************************************************************************/

@Slf4j
class SmallTicketRecognitionCaseServiceTest {

    @Mock
    RedisTemplate<String, String> redisTemplate;

    @Mock
    ValueOperations<String,String> valueOperations;

    @Mock
    private RedisService redisService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private SmallTicketProperties properties;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private TbPhotoReviewService tbPhotoReviewService;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private DefaultRedisScript<Long> checkAndDelScript;
    @Mock
    private SmallTicketProperties smallTicketProperties;
    @Mock
    private TbOcrCallbackRecordService tbOcrCallbackRecordService;
    @Mock
    private SmallTicketRecognitionClient smallTicketRecognitionClient;
    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;


    @InjectMocks
    private SmallTicketRecognitionServiceImpl smallTicketRecognitionService;

    @BeforeEach
    public void before() {
        MockitoAnnotations.openMocks(this);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void ocrRecognitionTimeoutAutomaticPoints() {
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(
                        SmallTicketCallbackResource.builder().build(),
                        TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build()));

        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(
                        SmallTicketCallbackResource.builder().isPass(false).build(),
                        TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_0.getCode()).build()));

        // 出现重复不为空
        Mockito.when(tbPhotoReviewService.checkPhotoReviewRecord(Mockito.any()))
                .thenReturn(TbPhotoReview.builder().build());
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(
                        SmallTicketCallbackResource.builder().amount("100").isPass(true).build(),
                        TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_0.getCode()).build()));

        // 未出现重复 但是该拍照已积分
        Mockito.when(tbPhotoReviewService.checkPhotoReviewRecord(Mockito.any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(
                        SmallTicketCallbackResource.builder().amount("100").isPass(true).build(),
                        TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_0.getCode()).bonus(10).integralAdjustId("1001").build()));

        // 会员为空
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(
                        SmallTicketCallbackResource.builder().amount("100").isPass(true).build(),
                        TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_0.getCode()).build()));

        // baseShop为空
        Mockito.when(tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(
                        SmallTicketCallbackResource.builder().amount("100").isPass(true).build(),
                        TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_0.getCode()).build()));

        // baseShop 不为空但是重复回传
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().firstFormatCode("01").firstFormatCode("01Name").build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(false);
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(
                        SmallTicketCallbackResource.builder().amount("100").isPass(true).build(),
                        TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_0.getCode()).build()));

        // baseShop 不为空但是重复回传 然后抛出异常
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().shopAliasName("shopAliasName").firstFormatCode("01").firstFormatCode("01Name").build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any()))
                .thenThrow(new BizException(400, ""));
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(
                        SmallTicketCallbackResource.builder().amount("100").isPass(true).build(),
                        TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_0.getCode()).build()));
    }

    @Test
    void ocrCallbackHandler() {
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(true).build(),
                        ""));

        // 合合未完成
        Mockito.when(tbPhotoReviewService.findByOcrTaskId(Mockito.any())).thenReturn(TbPhotoReview.builder().build());
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().code(101).build(),
                        ""));

        // 合合未完成
        Mockito.when(tbPhotoReviewService.findByOcrTaskId(Mockito.any())).thenReturn(TbPhotoReview.builder().build());
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().code(404).build(),
                        ""));

        // fillPhotoInfo resource.success() deferredResult为null 会直接在307行return掉
        Mockito.when(tbPhotoReviewService.findByOcrTaskId(Mockito.any()))
                .thenReturn(TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_0.getCode())
                        .uploadDate(DateUtil.parseDate("2025-01-12 00:00:00")).build());
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder()
                                .mall("12345")
                                .shop("123")
                                .isRobot(true)
                                .amount("100")
                                .ticketNo("12345678")
                                .transTime("2023-12-01 11:00:00")
                                .isPass(true)
                                .code(200)
                                .build(),
                        ""));

        // fillPhotoInfo resource.success() 小票非审核状态 307行return掉
        Mockito.when(tbPhotoReviewService.findByOcrTaskId(Mockito.any()))
                .thenReturn(TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode())
                        .uploadDate(DateUtil.parseDate("2025-01-12 00:00:00")).build());
        Mockito.when(redisService.setIfAbsentWithExpire(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito.any()))
                .thenReturn(true);
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().mall("12345").shop("123").isRobot(true).amount("100").ticketNo("12345678").transTime("2023-12-01 11:00:00").isPass(true).code(200).build(),
                        ""));

        // fillPhotoInfo isPass为false rejection为空 307行return掉
        Mockito.when(tbPhotoReviewService.findByOcrTaskId(Mockito.any()))
                .thenReturn(TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode())
                        .uploadDate(DateUtil.date()).build());
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(false)
                                .code(200).build(),
                        ""));

        // fillPhotoInfo isPass为false rejection不为空 非消费小票 307行return掉
        Mockito.when(tbPhotoReviewService.findByOcrTaskId(Mockito.any()))
                .thenReturn(TbPhotoReview.builder()
                        .status(PhotoReviewStatusEnum.TYPE_2.getCode())
                        .uploadDate(DateUtil.date())
                        .build());
        Mockito.when(redisTemplate.execute(Mockito.any(DefaultRedisScript.class), Mockito.anyList())).thenReturn(1L);
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(false)
                                .rejectReason(null)
                                .code(200).build(),
                        ""));

        // 307行return掉
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(false)
                                .rejectReason(SmallTicketRejectResource.builder().id(OcrErrorCodeMappingEnum.ERR_12.getOcrErrorCode()).build())
                                .code(200).build(),
                        ""));

        // fillPhotoInfo isPass为false rejection不为空 识别超时 307行return掉
        Mockito.when(tbPhotoReviewService.findByOcrTaskId(Mockito.any()))
                .thenReturn(TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build());
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(false)
                                .rejectReason(SmallTicketRejectResource.builder().id(OcrErrorCodeMappingEnum.ERR_60.getOcrErrorCode()).build())
                                .code(200).build(),
                        ""));

        // fillPhotoInfo isPass为false rejection不为空 五要素为空 307行return掉
        Mockito.when(tbPhotoReviewService.findByOcrTaskId(Mockito.any()))
                .thenReturn(TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build());
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(false)
                                .rejectReason(SmallTicketRejectResource.builder()
                                        .id(OcrErrorCodeMappingEnum.ERR_12.getOcrErrorCode())
                                        .build())
                                .code(200).build(),
                        ""));

        // fillPhotoInfo isPass为false rejection不为空 五要素不为空 307行return掉
        Mockito.when(tbPhotoReviewService.findByOcrTaskId(Mockito.any()))
                .thenReturn(TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build());

        MockedStatic<DeferredResultCacheUtils> mockedStatic = Mockito.mockStatic(DeferredResultCacheUtils.class);
        Mockito.when(DeferredResultCacheUtils.getResultMap(Mockito.any())).thenReturn(new DeferredResult<>());

        // setUserRedisErrorCount
        Mockito.when(redisTemplate.hasKey(Mockito.any())).thenReturn(null);
        Mockito.when(valueOperations.increment(Mockito.any(), Mockito.anyInt())).thenReturn(1L);
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(false)
                                .taskId("12345")
                                .code(200)
                                .build(),
                        ""));
        mockedStatic.close();

        // setUserRedisErrorCount
        MockedStatic<DeferredResultCacheUtils> mockedStatic1 = Mockito.mockStatic(DeferredResultCacheUtils.class);
        Mockito.when(DeferredResultCacheUtils.getResultMap(Mockito.any())).thenReturn(new DeferredResult<>());

        Mockito.when(redisTemplate.hasKey(Mockito.any())).thenReturn(false);
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(false)
                                .taskId("12345")
                                .rejectReason(SmallTicketRejectResource.builder()
                                        .id(OcrErrorCodeMappingEnum.ERR_12.getOcrErrorCode())
                                        .missingFields(Arrays.asList("mall", "shop", "amount", "transTime", "ticketNo", "test"))
                                        .build())
                                .code(200).build(),
                        ""));
        mockedStatic1.close();

        // setUserRedisErrorCount
        MockedStatic<DeferredResultCacheUtils> mockedStatic2 = Mockito.mockStatic(DeferredResultCacheUtils.class);
        Mockito.when(DeferredResultCacheUtils.getResultMap(Mockito.any())).thenReturn(new DeferredResult<>());

        Mockito.when(redisTemplate.hasKey(Mockito.any())).thenReturn(true);
        Mockito.when(valueOperations.increment(Mockito.any(), Mockito.anyInt())).thenReturn(5L);
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(false)
                                .taskId("12345")
                                .rejectReason(SmallTicketRejectResource.builder()
                                        .id(OcrErrorCodeMappingEnum.ERR_12.getOcrErrorCode())
                                        .missingFields(Arrays.asList("mall", "shop", "amount", "transTime", "ticketNo", "test"))
                                        .build())
                                .code(200).build(),
                        ""));
        mockedStatic2.close();

        // 290 resource.success()
        MockedStatic<DeferredResultCacheUtils> mockedStatic3 = Mockito.mockStatic(DeferredResultCacheUtils.class);
        Mockito.when(DeferredResultCacheUtils.getResultMap(Mockito.any())).thenReturn(new DeferredResult<>());

        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(true)
                                .taskId("12345")
                                .code(200)
                                .build(),
                        ""));
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(null);
        mockedStatic3.close();


        MockedStatic<DeferredResultCacheUtils> mockedStatic4 = Mockito.mockStatic(DeferredResultCacheUtils.class);
        Mockito.when(DeferredResultCacheUtils.getResultMap(Mockito.any())).thenReturn(new DeferredResult<>());

        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().shopAliasName("AliasName").build());
        Mockito.when(tbPhotoReviewService.checkPhotoReviewRecord(Mockito.any())).thenReturn(TbPhotoReview.builder().build());
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(true)
                                .amount("10")
                                .transTime("2025-03-05 00:00:00")
                                .taskId("12345")
                                .code(200)
                                .build(),
                        ""));
        mockedStatic4.close();


        MockedStatic<DeferredResultCacheUtils> mockedStatic5 = Mockito.mockStatic(DeferredResultCacheUtils.class);
        Mockito.when(DeferredResultCacheUtils.getResultMap(Mockito.any())).thenReturn(new DeferredResult<>());

        Mockito.when(tbPhotoReviewService.checkPhotoReviewRecord(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(true)
                                .amount("10")
                                .transTime("2025-03-05 00:00:00")
                                .taskId("12345")
                                .code(200)
                                .build(),
                        ""));
        mockedStatic5.close();

        // 没有交易时间
        MockedStatic<DeferredResultCacheUtils> mockedStatic6 = Mockito.mockStatic(DeferredResultCacheUtils.class);
        Mockito.when(DeferredResultCacheUtils.getResultMap(Mockito.any())).thenReturn(new DeferredResult<>());

        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(true)
                                .amount("10")
                                .taskId("12345")
                                .code(200)
                                .build(),
                        ""));
        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(Mockito.any())).thenReturn(TbSalesDetail.builder().build());
        mockedStatic6.close();

        // 没有交易时间
        MockedStatic<DeferredResultCacheUtils> mockedStatic7 = Mockito.mockStatic(DeferredResultCacheUtils.class);
        Mockito.when(DeferredResultCacheUtils.getResultMap(Mockito.any())).thenReturn(new DeferredResult<>());

        Assertions.assertDoesNotThrow(() ->
                smallTicketRecognitionService.ocrCallbackHandler(
                        SmallTicketCallbackResource.builder().isPass(true)
                                .amount("10")
                                .taskId("12345")
                                .code(200)
                                .build(),
                        ""));
        Mockito.when(tbSalesDetailService.checkSaleRecordRepeatedOrNot(Mockito.any())).thenReturn(null);
        mockedStatic7.close();
    }

    @Test
    void fillPhotoInfo() {
        Assertions.assertDoesNotThrow(() ->smallTicketRecognitionService.fillPhotoInfo(
                TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_0.getCode()).build(),
                SmallTicketCallbackResource.builder()
                        .mall("12345")
                        .shop("123")
                        .isRobot(true)
                        .amount("100")
                        .ticketNo("no123")
                        .transTime("2023-12-12 00:00:00")
                        .isPass(true)
                        .build()
        ));

        Assertions.assertDoesNotThrow(() ->smallTicketRecognitionService.fillPhotoInfo(
                TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build(),
                SmallTicketCallbackResource.builder()
                        .mall("12345")
                        .shop("123")
                        .isRobot(true)
                        .amount("100")
                        .ticketNo("no123")
                        .transTime("2023-12-12 00:00:00")
                        .isPass(true)
                        .build()
        ));

        Assertions.assertDoesNotThrow(() ->smallTicketRecognitionService.fillPhotoInfo(
                TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build(),
                SmallTicketCallbackResource.builder()
                        .isPass(false)
                        .build()
        ));

        Assertions.assertDoesNotThrow(() ->smallTicketRecognitionService.fillPhotoInfo(
                TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build(),
                SmallTicketCallbackResource.builder()
                        .isPass(false)
                        .rejectReason(SmallTicketRejectResource.builder().id(OcrErrorCodeMappingEnum.ERR_13.getOcrErrorCode()).build())
                        .build()
        ));

        Assertions.assertDoesNotThrow(() ->smallTicketRecognitionService.fillPhotoInfo(
                TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build(),
                SmallTicketCallbackResource.builder()
                        .isPass(false)
                        .rejectReason(SmallTicketRejectResource.builder().id(OcrErrorCodeMappingEnum.ERR_60.getOcrErrorCode()).build())
                        .build()
        ));

        Assertions.assertDoesNotThrow(() ->smallTicketRecognitionService.fillPhotoInfo(
                TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build(),
                SmallTicketCallbackResource.builder()
                        .isPass(false)
                        .rejectReason(SmallTicketRejectResource.builder().id(OcrErrorCodeMappingEnum.ERR_12.getOcrErrorCode()).build())
                        .build()
        ));

        Assertions.assertDoesNotThrow(() ->smallTicketRecognitionService.fillPhotoInfo(
                TbPhotoReview.builder().status(PhotoReviewStatusEnum.TYPE_2.getCode()).build(),
                SmallTicketCallbackResource.builder()
                        .isPass(false)
                        .rejectReason(SmallTicketRejectResource.builder()
                                .id(OcrErrorCodeMappingEnum.ERR_12.getOcrErrorCode())
                                .missingFields(Arrays.asList("mall", "shop", "amount", "transTime", "ticketNo", "test"))
                                .build())
                        .build()
        ));
    }

}
