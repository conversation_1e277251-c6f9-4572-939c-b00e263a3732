package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.service.impl.TbSequenceVipcodeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class TbSequenceVipcodeServiceTest {

    @InjectMocks
    private TbSequenceVipcodeServiceImpl tbSequenceVipCodeService;
    @Mock
    private TbMemberAssetMapper tbMemberAssetMapper;
    @Mock
    private RedisService redisService;


    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void nextId() {
        Mockito.when(redisService.hasKey(Mockito.any())).thenReturn(true);
        Mockito.when(redisService.incrementOne(Mockito.any())).thenReturn(1L);
        Assertions.assertNotNull(tbSequenceVipCodeService.nextId());

        Mockito.when(redisService.hasKey(Mockito.any())).thenReturn(false);
        Mockito.when(tbMemberAssetMapper.getLatestMemberAsset())
                .thenReturn(TbMemberAsset.builder().vipcode("12345").build());
        Assertions.assertNotNull(tbSequenceVipCodeService.nextId());
    }
}
