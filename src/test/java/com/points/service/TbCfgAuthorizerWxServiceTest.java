package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbCfgAuthorizerWx;
import com.kerryprops.kip.service.integral.mapper.TbCfgAuthorizerWxMapper;
import com.kerryprops.kip.service.integral.service.impl.TbCfgAuthorizerWxServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 10/08/2024 10:15
 **********************************************************************************************************************/

@Slf4j
class TbCfgAuthorizerWxServiceTest {

    @InjectMocks
    private TbCfgAuthorizerWxServiceImpl tbCfgAuthorizerWxService;
    @Mock
    private TbCfgAuthorizerWxMapper tbCfgAuthorizerWxMapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findByEntity() {
        Assertions.assertDoesNotThrow(() -> tbCfgAuthorizerWxService.findByEntity(TbCfgAuthorizerWx.builder().build()));
    }


}
