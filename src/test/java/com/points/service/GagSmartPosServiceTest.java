package com.points.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.client.SmartPosRpcClient;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.config.SmartPosProperties;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.mapper.TbBkcSaleMemberMapper;
import com.kerryprops.kip.service.integral.model.dto.BillInfoRpcDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.GagSmartPosServiceImpl;
import com.kerryprops.kip.service.integral.service.impl.TbBkcSaleMemberServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.GagScanCodeAchievePointsResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Date;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 15:49
 **********************************************************************************************************************/

@Slf4j
class GagSmartPosServiceTest {

    @InjectMocks
    private GagSmartPosServiceImpl gagSmartPosServiceImpl;

    @Mock
    private SmartPosRpcClient smartPosRpcClient;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private SmartPosProperties smartPosProperties;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private RedisService redisService;
    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;
    @Mock
    private TbQrCodeRecordService tbQrCodeRecordService;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private SalesAutoPointsDto salesAutoPointsDto;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("getBillInfoRpcDto 部分场景, getParams 全部场景")
    void smartPosAutoPoints() {
        LoginUser loginUser = new LoginUser();
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(smartPosProperties.getAppSecret()).thenReturn("appSecret");
        Mockito.when(smartPosProperties.getSignMethod()).thenReturn("signMethod");
        Mockito.when(smartPosRpcClient.getBillInfoRpc(Mockito.any())).thenReturn(null);
        Assertions.assertThrows(BizNotFoundException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(smartPosProperties.getAppSecret()).thenReturn("appSecret");
        Mockito.when(smartPosProperties.getSignMethod()).thenReturn("MD5");
        Mockito.when(smartPosRpcClient.getBillInfoRpc(Mockito.any()))
                .thenReturn(BillInfoRpcDto.builder().rescode("OPEN_SUCCESS").build());
        Assertions.assertThrows(BizException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        Mockito.when(smartPosRpcClient.getBillInfoRpc(Mockito.any()))
                .thenReturn(BillInfoRpcDto.builder().rescode("OPEN_SUCCESS").merchantCode("merchantCode").amount(0D).build());
        Assertions.assertThrows(BizException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        Mockito.when(smartPosRpcClient.getBillInfoRpc(Mockito.any()))
                .thenReturn(BillInfoRpcDto.builder().rescode("NOT_OPEN_SUCCESS").merchantCode("merchantCode").amount(0D).build());
        Assertions.assertThrows(BizException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));
    }

    @Test
    @DisplayName("getBillInfoRpcDto 全部场景, getParams 全部场景")
    void smartPosAutoPointsCase1() {
        LoginUser loginUser = new LoginUser();
        BillInfoRpcDto billInfoRpcDto = BillInfoRpcDto.builder().amount(100D).saleTime("30000101110000").rescode("OPEN_SUCCESS").build();
        TbQrCodeRecord qrCodeRecord = new TbQrCodeRecord();
        qrCodeRecord.setContent(JsonUtils.objToString(billInfoRpcDto));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.anyString()))
                .thenReturn(qrCodeRecord);
        Assertions.assertThrows(BizException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        BillInfoRpcDto billInfoRpcDto1 = BillInfoRpcDto.builder().amount(100D).saleTime("20250101110000").rescode("OPEN_SUCCESS").build();
        TbQrCodeRecord qrCodeRecord1 = new TbQrCodeRecord();
        qrCodeRecord1.setContent(JsonUtils.objToString(billInfoRpcDto1));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.anyString()))
                .thenReturn(qrCodeRecord1);
        Assertions.assertThrows(BizNotFoundException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("HKC").build());
        Assertions.assertThrows(BizException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        BillInfoRpcDto billInfoRpcDto2 = BillInfoRpcDto.builder().amount(100D).saleTime("20250101110000").rescode("OPEN_SUCCESS").build();
        TbQrCodeRecord qrCodeRecord2 = new TbQrCodeRecord();
        qrCodeRecord2.setContent(JsonUtils.objToString(billInfoRpcDto2));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.anyString()))
                .thenReturn(qrCodeRecord2);
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("JAKC").build());
        Assertions.assertThrows(BizException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        BillInfoRpcDto billInfoRpcDto3 = BillInfoRpcDto.builder().amount(100D).saleTime(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN)).rescode("OPEN_SUCCESS").build();
        TbQrCodeRecord qrCodeRecord3 = new TbQrCodeRecord();
        qrCodeRecord2.setContent(JsonUtils.objToString(billInfoRpcDto3));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.anyString()))
                .thenReturn(qrCodeRecord3);
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("JAKC").build());
        Assertions.assertThrows(BizNotFoundException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));
    }

    @Test
    @DisplayName("smartPosAutoPoints发放获取tbBaseShopService.getByContractNoAndMallId")
    void smartPosAutoPointsCase2() {
        LoginUser loginUser = new LoginUser();
        BillInfoRpcDto billInfoRpcDto3 = BillInfoRpcDto.builder().amount(100D)
                .saleTime(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN))
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord3 = new TbQrCodeRecord();
        qrCodeRecord3.setContent(JsonUtils.objToString(billInfoRpcDto3));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.anyString()))
                .thenReturn(qrCodeRecord3);

        MallItem mallItem = MallItem.builder().abbreviation("JAKC").build();
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(mallItem);
        Assertions.assertThrows(BizNotFoundException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        TbBaseShop baseShop = TbBaseShop.builder().build();
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(baseShop);
        Assertions.assertThrows(BizNotFoundException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        Mockito.when(tbMemberAssetService.getMemberWithProfileInfo(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertThrows(BizException.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(Boolean.TRUE);
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any())).thenReturn(10);
        Assertions.assertNotNull(gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

        // 进入salePointsProcess方法的catch异常
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any()))
                .thenThrow(new BizException(400, ""));
        Assertions.assertThrows(Exception.class, () -> gagSmartPosServiceImpl.smartPosAutoPoints(loginUser, ""));

    }

    @Test
    void qrcodeDetail() {
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(null);
        Assertions.assertNotNull(gagSmartPosServiceImpl.qrcodeDetail("", ""));

        BillInfoRpcDto billInfoRpcDto = BillInfoRpcDto.builder().amount(100D)
                .saleTime(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN))
                .rescode("NOT_OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord = new TbQrCodeRecord();
        qrCodeRecord.setContent(JsonUtils.objToString(billInfoRpcDto));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord);
        Assertions.assertNotNull(gagSmartPosServiceImpl.qrcodeDetail("", ""));

        BillInfoRpcDto billInfoRpcDto1 = BillInfoRpcDto.builder().amount(0D)
                .saleTime(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN))
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord1 = new TbQrCodeRecord();
        qrCodeRecord1.setContent(JsonUtils.objToString(billInfoRpcDto1));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord1);
        Assertions.assertNotNull(gagSmartPosServiceImpl.qrcodeDetail("", ""));

        BillInfoRpcDto billInfoRpcDto2 = BillInfoRpcDto.builder().amount(10D)
                .saleTime("30000101110000")
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord2 = new TbQrCodeRecord();
        qrCodeRecord2.setContent(JsonUtils.objToString(billInfoRpcDto2));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord2);
        Assertions.assertNotNull(gagSmartPosServiceImpl.qrcodeDetail("", ""));

        BillInfoRpcDto billInfoRpcDto3 = BillInfoRpcDto.builder().amount(10D)
                .saleTime("20000101110000")
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord3 = new TbQrCodeRecord();
        qrCodeRecord3.setContent(JsonUtils.objToString(billInfoRpcDto3));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord3);
        Assertions.assertNotNull(gagSmartPosServiceImpl.qrcodeDetail("", ""));

        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("HKC").build());
        Assertions.assertNotNull(gagSmartPosServiceImpl.qrcodeDetail("", ""));

        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("JAKC").build());
        Assertions.assertNotNull(gagSmartPosServiceImpl.qrcodeDetail("", ""));


        BillInfoRpcDto billInfoRpcDto4 = BillInfoRpcDto.builder().amount(10D)
                .saleTime(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN))
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord4 = new TbQrCodeRecord();
        qrCodeRecord4.setContent(JsonUtils.objToString(billInfoRpcDto4));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord4);
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("JAKC").build());
        Assertions.assertNotNull(gagSmartPosServiceImpl.qrcodeDetail("", ""));

        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Mockito.when(tbSalesDetailService.queryBySellNoAndSaleType(Mockito.any(), Mockito.anyString()))
                .thenReturn(TbSalesDetail.builder().build());
        Mockito.when(tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertNotNull(gagSmartPosServiceImpl.qrcodeDetail("", ""));
    }

    @Test
    void scanCodeAchievePoints() {
        GagScanCodeAchievePointsResource resource = GagScanCodeAchievePointsResource.builder().build();
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(null);
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

        BillInfoRpcDto billInfoRpcDto = BillInfoRpcDto.builder().amount(100D)
                .saleTime(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN))
                .rescode("NOT_OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord = new TbQrCodeRecord();
        qrCodeRecord.setContent(JsonUtils.objToString(billInfoRpcDto));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord);
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

        BillInfoRpcDto billInfoRpcDto1 = BillInfoRpcDto.builder().amount(0D)
                .saleTime(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN))
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord1 = new TbQrCodeRecord();
        qrCodeRecord1.setContent(JsonUtils.objToString(billInfoRpcDto1));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord1);
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

        BillInfoRpcDto billInfoRpcDto2 = BillInfoRpcDto.builder().amount(10D)
                .saleTime("30000101110000")
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord2 = new TbQrCodeRecord();
        qrCodeRecord2.setContent(JsonUtils.objToString(billInfoRpcDto2));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord2);
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

        BillInfoRpcDto billInfoRpcDto3 = BillInfoRpcDto.builder().amount(10D)
                .saleTime("20000101110000")
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord3 = new TbQrCodeRecord();
        qrCodeRecord3.setContent(JsonUtils.objToString(billInfoRpcDto3));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord3);
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("HKC").build());
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("JAKC").build());
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));


        BillInfoRpcDto billInfoRpcDto4 = BillInfoRpcDto.builder().amount(10D)
                .saleTime(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN))
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord4 = new TbQrCodeRecord();
        qrCodeRecord4.setContent(JsonUtils.objToString(billInfoRpcDto4));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord4);
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("JAKC").build());
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Assertions.assertThrows(BizNotFoundException.class, () -> gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

        Mockito.when(tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any()))
                .thenReturn(Boolean.TRUE);
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any()))
                .thenThrow(BizException.class);
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));

    }

    @Test
    void scanCodeAchievePointsCase1() {
        GagScanCodeAchievePointsResource resource = GagScanCodeAchievePointsResource.builder().build();
        BillInfoRpcDto billInfoRpcDto4 = BillInfoRpcDto.builder().amount(10D)
                .saleTime(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN))
                .rescode("OPEN_SUCCESS")
                .build();
        TbQrCodeRecord qrCodeRecord4 = new TbQrCodeRecord();
        qrCodeRecord4.setContent(JsonUtils.objToString(billInfoRpcDto4));
        Mockito.when(tbQrCodeRecordService.getByQrCode(Mockito.any())).thenReturn(qrCodeRecord4);
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().abbreviation("JAKC").build());
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Mockito.when(tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any()))
                .thenReturn(Boolean.TRUE);
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any()))
                .thenThrow(BizNotFoundException.class);
        Assertions.assertNotNull(gagSmartPosServiceImpl.scanCodeAchievePoints(resource, ""));
    }

}
