package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.MemberGradeChangeTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.RuleTypeEnum;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeRuleProgressMapper;
import com.kerryprops.kip.service.integral.model.dto.MemberYearAmountCalDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TbMemberGradeRuleProgressServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 07/31/2024 10:37
 **********************************************************************************************************************/

@Slf4j
class TbMemberGradeRuleProgressServiceTest{

    @InjectMocks
    private TbMemberGradeRuleProgressServiceImpl tbMemberGradeRuleProgressServiceImpl;
    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private TbMemberGradeRuleProgressMapper tbMemberGradeRuleProgressMapper;
    @Mock
    private TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    @Mock
    private MemberGradeStrategyService memberGradeStrategyService;
    @Mock
    private TbMemberGradeRuleService tbMemberGradeRuleService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("getProgressBar, getProgressBar, checkMemberIsHonorable")
    void getProgressBar() {
        LoginUser loginUser = LoginUser.builder().phoneNumber("***********").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        Assertions.assertThrows(PointBusinessException.class, ()-> tbMemberGradeRuleProgressServiceImpl.getProgressBar(loginUser, ""));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                        .thenReturn(TbMemberAsset.builder().grade("01").build());
        Mockito.when(tbMemberGradeService.getGradeSortAscByGroupIdWithException(Mockito.any()))
                        .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").upGradationStatus(0).build()));
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getProgressBar(loginUser, ""));

        Mockito.when(tbMemberGradeService.getGradeSortAscByGroupIdWithException(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("05").upGradationStatus(1).build()));
        Assertions.assertThrows(BizNotFoundException.class, ()-> tbMemberGradeRuleProgressServiceImpl.getProgressBar(loginUser, ""));

        Mockito.when(tbMemberGradeService.getGradeSortAscByGroupIdWithException(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").upGradationStatus(1).build()));
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getProgressBar(loginUser, ""));

        Mockito.when(tbMemberGradeService.getGradeSortAscByGroupIdWithException(Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("02").upGradationStatus(1).build()));

        Mockito.when(tbMemberGradeRuleService.queryUpgradeRule(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getProgressBar(loginUser, ""));

        Mockito.when(tbMemberGradeRuleService.getById(Mockito.any()))
                .thenReturn(TbMemberGradeRule.builder().money(BigDecimal.TEN).build());
        Mockito.when(memberGradeStrategyService.modifyUpgradeLeftMoney(Mockito.any(), Mockito.any()))
                .thenReturn("100");
        Mockito.when(tbSalesDetailService.getGradeRuleCacheAmount(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getProgressBar(loginUser, "12345"));

        Mockito.when(tbSalesDetailService.getGradeRuleCacheAmount(Mockito.any(), Mockito.any()))
                .thenReturn(100D);
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getProgressBar(loginUser, "12345"));
    }

    @Test
    void getRelegationCopywriter() {
        LoginUser loginUser = LoginUser.builder().phoneNumber("***********").build();
        Mockito.when(tbMemberGradeService.getGradeSortAscByGroupIdWithException(Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").sort(1).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("02").sort(2).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("03").sort(3).upGradationStatus(0).build()));
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("01").build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getRelegationCopywriter(loginUser));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("03").build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getRelegationCopywriter(loginUser));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("02").build());

        Mockito.when(tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getRelegationCopywriter(loginUser));

        Mockito.when(tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeRuleProgress.builder().build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getRelegationCopywriter(loginUser));

        Mockito.when(tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeRuleProgress
                        .builder()
                        .sustainProgressTextMax("")
                        .sustainProgressTextNotShow("sustainProgressTextNotShow")
                        .build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getRelegationCopywriter(loginUser));

        Mockito.when(tbMemberGradeRuleService.queryByGroupIdAndGradeAndType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGradeRule.builder().id(1L).ruleType(4).build(),
                        TbMemberGradeRule.builder().money(BigDecimal.TEN).build()));
        Mockito.when(memberGradeStrategyService.getCalculateYearAmountBeginDate(Mockito.any(),Mockito.any()))
                .thenReturn(MemberYearAmountCalDto.builder().cycleYear(true).build());
        Mockito.when(tbMemberGradeChangeDetailService.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(TbMemberGradeChangeDetail.builder().createDate(DateUtil.date()).changeType(MemberGradeChangeTypeEnum.TYPE_1.getCode()).build());
        Mockito.when(tbBaseShopService.getContractNoList(Mockito.any(),Mockito.any()))
                .thenReturn(new ArrayList<>());

        Mockito.when(tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyList()))
                .thenReturn(false);

        Mockito.when(memberGradeStrategyService.getRelegationRuleBeginDate(Mockito.any(),Mockito.anyBoolean(),Mockito.any(),Mockito.any()))
                .thenReturn(DateUtil.date());
        Mockito.when(tbSalesDetailService.getMemberSaleAmountBetweenTime(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyList()))
                        .thenReturn(100d);
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getRelegationCopywriter(loginUser));

        Mockito.when(tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeRuleProgress
                        .builder()
                        .sustainProgressTextMax("{1}")
                        .sustainProgressTextNotShow("{1}")
                        .build());
        Mockito.when(tbSalesDetailService.getMemberSaleAmountBetweenTime(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyList()))
                .thenReturn(100d);
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getRelegationCopywriter(loginUser));

        Mockito.when(tbSalesDetailService.getMemberSaleAmountBetweenTime(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyList()))
                .thenReturn(0d);
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getRelegationCopywriter(loginUser));
    }

    @Test
    void getByGroupIdAndMemberGrade() {
        Mockito.when(tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeRuleProgress.builder().build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getByGroupIdAndMemberGrade("", ""));
    }

    @Test
    void memberGradeComputeMethod() {
        LoginUser loginUser = LoginUser.builder().phoneNumber("***********").build();
        Assertions.assertThrows(BizNotFoundException.class, () -> tbMemberGradeRuleProgressServiceImpl.memberGradeComputeMethod(loginUser));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("01").build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.memberGradeComputeMethod(loginUser));

        Mockito.when(tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeRuleProgress.builder().build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.memberGradeComputeMethod(loginUser));

        Mockito.when(tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeRuleProgress
                        .builder()
                        .dayGradeRuleId("dayGradeRuleId")
                        .monthGradeRuleId("monthGradeRuleId")
                        .build());
        Mockito.when(tbMemberGradeRuleService.listByIds(Mockito.anyList()))
                .thenReturn(Arrays.asList(TbMemberGradeRule.builder().sumType(1).ruleType(RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue()).build(),
                        TbMemberGradeRule.builder().sumType(1).ruleType(RuleTypeEnum.ACCUMULATIVE_CONSUME_UPGRADE.getValue()).build(),
                        TbMemberGradeRule.builder().build()));
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.memberGradeComputeMethod(loginUser));
    }

    @Test
    void getUpgradeCopywriter() {
        LoginUser loginUser = LoginUser.builder().phoneNumber("***********").build();
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("04").build());
        Mockito.when(tbMemberGradeService.getGradeSortAscByGroupIdWithException(Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").sort(1).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("02").sort(2).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("03").sort(3).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("04").sort(4).upGradationStatus(0).build()));
        // 尊享卡
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getUpgradeCopywriter(loginUser, ""));

        // 最高卡等
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("03").build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getUpgradeCopywriter(loginUser, ""));

        // 卡等不存在
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("05").build());
        Assertions.assertThrows(BizNotFoundException.class, () -> tbMemberGradeRuleProgressServiceImpl.getUpgradeCopywriter(loginUser, ""));

        // 当前卡等为最高卡等，传入卡等小于当前卡等
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("02").build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getUpgradeCopywriter(loginUser, "01"));


        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("01").build());
        //输入卡等为空
        // queryByGroupIdAndGrade为null
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getUpgradeCopywriter(loginUser, null));

        // queryByGroupIdAndGrade的getUpProgressTextNotShow为null
        Mockito.when(tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                        .thenReturn(TbMemberGradeRuleProgress.builder().upProgressTextNotShow("").build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getUpgradeCopywriter(loginUser, null));

        // queryGradeRulesByGroupIdAndGradeAndRuleTypes为null
        Mockito.when(tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeRuleProgress.builder().upProgressTextNotShow("upProgressTextNotShow").build());
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getUpgradeCopywriter(loginUser, null));

        // isMinGrade为false
        Mockito.when(tbMemberGradeRuleService.queryGradeRulesByGroupIdAndGradeAndRuleTypes(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGradeRule.builder().build()));
        Mockito.when(memberGradeStrategyService.getCalculateYearAmountBeginDate(Mockito.any(), Mockito.any()))
                .thenReturn(MemberYearAmountCalDto.builder().minGrade(Boolean.FALSE).build());
        Mockito.when(tbMemberGradeChangeDetailService.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeChangeDetail.builder().createDate(DateUtil.offsetDay(DateUtil.date(), -1)).build());
        // amount为null
        Mockito.when(tbSalesDetailService.getGradeRuleCacheAmount(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Mockito.when(tbBaseShopService.getContractNoList(Mockito.any(),Mockito.any()))
                .thenReturn(new ArrayList<>());
        Mockito.when(memberGradeStrategyService.getUpgradeRuleBeginDate(Mockito.any(), Mockito.any()))
                .thenReturn(DateUtil.offsetDay(DateUtil.date(), -1));
        Mockito.when(tbSalesDetailService.getMemberSaleAmountBetweenTime(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyList()))
                .thenReturn(0d);
        Assertions.assertNotNull(tbMemberGradeRuleProgressServiceImpl.getUpgradeCopywriter(loginUser, null));
    }

    @Test
    void getUpgradeMoney() {
        LoginUser loginUser = LoginUser.builder().build();
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("04").build());
        Mockito.when(tbMemberGradeService.getGradeSortAscByGroupIdWithException(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("04").sort(4).upGradationStatus(0).build()));
        Assertions.assertDoesNotThrow(() -> tbMemberGradeRuleProgressServiceImpl.getUpgradeMoney(loginUser));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("08").build());
        Assertions.assertThrows(Exception.class, () -> tbMemberGradeRuleProgressServiceImpl.getUpgradeMoney(loginUser));

        Mockito.when(tbMemberGradeService.getGradeSortAscByGroupIdWithException(Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").sort(1).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("02").sort(2).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("03").sort(3).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("04").sort(4).upGradationStatus(0).build()));
        Assertions.assertThrows(Exception.class, () -> tbMemberGradeRuleProgressServiceImpl.getUpgradeMoney(loginUser));

        // 下一等级为null rules为空
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("03").build());
        Assertions.assertDoesNotThrow(() -> tbMemberGradeRuleProgressServiceImpl.getUpgradeMoney(loginUser));

        // 自然年最高等级
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("03").build());
        Mockito.when(tbMemberGradeRuleService.queryByGroupIdAndGradeAndType(Mockito.any(), Mockito.any(), Mockito.anyInt()))
                .thenReturn(Arrays.asList(
                        TbMemberGradeRule.builder().id(1L).ruleType(5).build(),
                        TbMemberGradeRule.builder().money(BigDecimal.TEN).build())
                );
        Mockito.when(memberGradeStrategyService.getCalculateYearAmountBeginDate(Mockito.any(), Mockito.any()))
                .thenReturn(MemberYearAmountCalDto.builder().minGrade(Boolean.FALSE).cycleYear(false).beginDate(DateUtil.date()).build());
        Mockito.when(tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(true);
        Assertions.assertDoesNotThrow(() -> tbMemberGradeRuleProgressServiceImpl.getUpgradeMoney(loginUser));

        Mockito.when(tbMemberGradeChangeDetailService.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeChangeDetail.builder().createDate(DateUtil.date()).build());
        Mockito.when(tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(false);
        Assertions.assertDoesNotThrow(() -> tbMemberGradeRuleProgressServiceImpl.getUpgradeMoney(loginUser));
    }

    @Test
    void getUpgradeMoneyCase1() {
        LoginUser loginUser = LoginUser.builder().build();

        Mockito.when(tbMemberGradeService.getGradeSortAscByGroupIdWithException(Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("01").sort(1).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("02").sort(2).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("03").sort(3).upGradationStatus(1).build(),
                        TbMemberGrade.builder().code("04").sort(4).upGradationStatus(0).build()));

        // 周期年非最高等级
        Mockito.when(memberGradeStrategyService.getCalculateYearAmountBeginDate(Mockito.any(), Mockito.any()))
                .thenReturn(MemberYearAmountCalDto.builder().minGrade(Boolean.FALSE).cycleYear(true).beginDate(DateUtil.date()).build());
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("02").build());
        Mockito.when(tbMemberGradeChangeDetailService.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberGradeChangeDetail.builder().createDate(DateUtil.date()).build());
        Mockito.when(tbMemberGradeRuleService.queryByGroupIdAndGradeAndType(Mockito.any(), Mockito.any(), Mockito.anyInt()))
                .thenReturn(Arrays.asList(
                        TbMemberGradeRule.builder().id(1L).ruleType(5).build(),
                        TbMemberGradeRule.builder().money(BigDecimal.TEN).ruleType(6).build())
                );
        // amount>rule的amount
        Mockito.when(tbSalesDetailService.getMemberSaleAmountBetweenTime(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(100D);
        Assertions.assertDoesNotThrow(() -> tbMemberGradeRuleProgressServiceImpl.getUpgradeMoney(loginUser));

        // amount<rule的amount
        Mockito.when(tbSalesDetailService.getMemberSaleAmountBetweenTime(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(1D);
        Assertions.assertDoesNotThrow(() -> tbMemberGradeRuleProgressServiceImpl.getUpgradeMoney(loginUser));
    }

}
