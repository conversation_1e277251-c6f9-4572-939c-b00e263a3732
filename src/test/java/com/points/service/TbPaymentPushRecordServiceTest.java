package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.mapper.TbPaymentPushRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.PaymentRecordDetailDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbMemberPointsChangeService;
import com.kerryprops.kip.service.integral.service.TbPointsDetailService;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.service.impl.TbPaymentPushRecordServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

@Slf4j
class TbPaymentPushRecordServiceTest {

    @InjectMocks
    private TbPaymentPushRecordServiceImpl tbPaymentPushRecordServiceImpl;

    @Mock
    private TbPaymentPushRecordMapper tbPaymentPushRecordMapper;
    @Mock
    private TbMemberPointsChangeService tbMemberPointsChangeService;
    @Mock
    private TbPointsDetailService tbPointsDetailService;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void savePushRecord() {
        Assertions.assertDoesNotThrow(() -> tbPaymentPushRecordServiceImpl.savePushRecord(null));

        PaymentRecordDetailDto detailDto = new PaymentRecordDetailDto();
        detailDto.setRefundBatchCode("refundBatchCode");
        Assertions.assertDoesNotThrow(() -> tbPaymentPushRecordServiceImpl.savePushRecord(detailDto));

        PaymentRecordDetailDto detailDto1 = new PaymentRecordDetailDto();
        detailDto.setRefundBatchCode("");
        Assertions.assertDoesNotThrow(() -> tbPaymentPushRecordServiceImpl.savePushRecord(detailDto1));
    }

    @Test
    void refundPosPoints() {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().build();
        Assertions.assertDoesNotThrow(() -> tbPaymentPushRecordServiceImpl.refundPosPoints(salesAutoPointsDto, 100));

        Mockito.when(tbPointsDetailService.queryAdjustList(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbPointsDetail.builder().amount(0).build());
        Assertions.assertDoesNotThrow(() -> tbPaymentPushRecordServiceImpl.refundPosPoints(salesAutoPointsDto, 100));

        Mockito.when(tbPointsDetailService.queryAdjustList(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbPointsDetail.builder().amount(10).build());
        Assertions.assertDoesNotThrow(() -> tbPaymentPushRecordServiceImpl.refundPosPoints(salesAutoPointsDto, 100));

        Mockito.when(tbSalesDetailService.querySaleBySellNo(Mockito.any()))
                .thenReturn(TbSalesDetail.builder().status(0).build());
        Assertions.assertDoesNotThrow(() -> tbPaymentPushRecordServiceImpl.refundPosPoints(salesAutoPointsDto, 100));

        Mockito.when(tbSalesDetailService.querySaleBySellNo(Mockito.any()))
                .thenReturn(TbSalesDetail.builder().status(1).build());
        Assertions.assertDoesNotThrow(() -> tbPaymentPushRecordServiceImpl.refundPosPoints(salesAutoPointsDto, 100));
    }

}