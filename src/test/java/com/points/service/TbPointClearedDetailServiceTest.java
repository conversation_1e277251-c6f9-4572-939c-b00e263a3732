package com.points.service;

import com.kerryprops.kip.service.integral.mapper.TbPointClearedDetailMapper;
import com.kerryprops.kip.service.integral.service.impl.TbPointClearedDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 08/05/2024 13:50
 **********************************************************************************************************************/

@Slf4j
class TbPointClearedDetailServiceTest {

    @InjectMocks
    private TbPointClearedDetailServiceImpl tbPointClearedDetailService;

    @Mock
    private TbPointClearedDetailMapper tbPointClearedDetailMapper;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getLatestMemberClearIntegral() {
        Assertions.assertDoesNotThrow(() -> tbPointClearedDetailService.getLatestMemberClearIntegral("12345", "12345"));
    }


}
