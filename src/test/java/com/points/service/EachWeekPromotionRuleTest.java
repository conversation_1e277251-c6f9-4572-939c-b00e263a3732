package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.impl.EachWeekPromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class EachWeekPromotionRuleTest {

    @InjectMocks
    private EachWeekPromotionRule eachWeekPromotionRule;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertNotNull(eachWeekPromotionRule.getRuleType());
    }

    @Test
    void checkRule() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build();
        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().build(), dto, DateUtil.date(), null)
        );
        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto, null, null)
        );

        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto, DateUtil.date(), null)
        );

        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto,  DateUtil.offsetDay(DateUtil.date(), 1), null)
        );

        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto,  DateUtil.offsetDay(DateUtil.date(), 2), null)
        );

        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto,  DateUtil.offsetDay(DateUtil.date(), 3), null)
        );

        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto,  DateUtil.offsetDay(DateUtil.date(), 4), null)
        );

        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto,  DateUtil.offsetDay(DateUtil.date(), 5), null)
        );

        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto,  DateUtil.offsetDay(DateUtil.date(), 6), null)
        );

        Assertions.assertFalse(
                eachWeekPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("123").build(), dto,  DateUtil.offsetDay(DateUtil.date(), 7), null)
        );

    }
}
