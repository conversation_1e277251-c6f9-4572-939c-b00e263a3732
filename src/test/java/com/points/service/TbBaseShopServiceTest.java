package com.points.service;

import com.kerryprops.kip.service.integral.client.HiveServiceClient;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.vo.*;
import com.kerryprops.kip.service.integral.service.impl.TbBaseShopServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 15:49
 **********************************************************************************************************************/

@Slf4j
class TbBaseShopServiceTest {

    @InjectMocks
    private TbBaseShopServiceImpl tbBaseShopService;
    @Mock
    private HiveVasClient hiveVasClient;
    @Mock
    private HiveServiceClient hiveServiceClient;
    @Mock
    private MallConfig mallConfig;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void selectByContractNo() {
        Assertions.assertNull(tbBaseShopService.selectByContractNo("JAKC00010001"));

        Mockito.when(hiveVasClient.getTenantInfo(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TenantAllVo.builder()
                        .tenant(TenantInfoVo.builder().status("0").build())
                        .build()));
        Assertions.assertNull(tbBaseShopService.selectByContractNo("JAKC00010001"));

        Mockito.when(hiveVasClient.getTenantInfo(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TenantAllVo.builder()
                                .buildings(Collections.singletonList(TenantBuildingVo.builder().formats("RETAIL").lbsId("12345").build()))
                        .tenant(TenantInfoVo.builder().status(IntegralConstant.SHOP_ENABLE_STATUS).doCoSet(Collections.singletonList("123")).build())
                                .floors(Collections.singletonList(TenantFloorVo.builder().build()))
                        .build()));
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertNotNull(tbBaseShopService.selectByContractNo("JAKC00010001"));
    }

    @Test
    void getByTenantId() {
        Assertions.assertNull(tbBaseShopService.getByTenantId("", ""));

        KerryResultDto<List<TenantAllVo>> tenantResult0 = new KerryResultDto<>();
        Mockito.when(hiveVasClient.getTenantById(Mockito.any(), Mockito.anyInt())).thenReturn(tenantResult0);
        Assertions.assertNull(tbBaseShopService.getByTenantId("12345", ""));

        KerryResultDto<List<TenantAllVo>> tenantResult = new KerryResultDto<>();
        tenantResult.setData(Collections.singletonList(TenantAllVo.builder().tenant(TenantInfoVo.builder().build()).build()));
        Mockito.when(hiveVasClient.getTenantById(Mockito.any(), Mockito.anyInt())).thenReturn(tenantResult);
        Assertions.assertNull(tbBaseShopService.getByTenantId("12345", ""));

        KerryResultDto<List<TenantAllVo>> tenantResult1 = new KerryResultDto<>();
        tenantResult1.setData(Collections.singletonList(TenantAllVo.builder().tenant(TenantInfoVo.builder()
                .status(IntegralConstant.SHOP_ENABLE_STATUS).build())
                .buildings(Collections.singletonList(TenantBuildingVo.builder().formats("RETAIL").lbsId("12345").build()))
                .build()));
        Mockito.when(hiveVasClient.getTenantById(Mockito.any(), Mockito.anyInt())).thenReturn(tenantResult1);
        // mallId为空 mallItem不为空
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertNotNull(tbBaseShopService.getByTenantId("12345", ""));

        // mallId不为空 mallItem为空
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(null);
        Assertions.assertNotNull(tbBaseShopService.getByTenantId("12345", "12345"));
    }

    @Test
    void getByContractNoAndMallId() {
        Assertions.assertNull(tbBaseShopService.getByContractNoAndMallId("", ""));

        Assertions.assertNull(tbBaseShopService.getByContractNoAndMallId("23456", "1233"));
    }

    @Test
    void getShopByDoCos() {
        KerryResultDto<List<TenantRespVo>> kerryResultDto = new KerryResultDto<>();
        Mockito.when(hiveVasClient.getShopByDoCos(Mockito.any())).thenReturn(kerryResultDto);
        Assertions.assertNull(tbBaseShopService.getShopByDoCos("", ""));

        KerryResultDto<List<TenantRespVo>> kerryResultDto1 = new KerryResultDto<>();
        Mockito.when(hiveVasClient.getShopByDoCos(Mockito.any())).thenReturn(kerryResultDto1);
        kerryResultDto1.setData(Collections.singletonList(null));
        Assertions.assertNull(tbBaseShopService.getShopByDoCos("", ""));

        TenantRespVo vo = new TenantRespVo();
        vo.setContractNo("12345678");
        KerryResultDto<List<TenantRespVo>> kerryResultDto2 = new KerryResultDto<>();
        Mockito.when(hiveVasClient.getShopByDoCos(Mockito.any())).thenReturn(kerryResultDto2);
        kerryResultDto2.setData(Collections.singletonList(vo));
        Mockito.when(hiveVasClient.getTenantInfo(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TenantAllVo.builder().build()));
        Assertions.assertNull(tbBaseShopService.getShopByDoCos("", "12345"));

        Mockito.when(hiveVasClient.getTenantInfo(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TenantAllVo.builder().buildings(Collections.singletonList(TenantBuildingVo.builder().lbsId("12").build())).build()));
        Assertions.assertNull(tbBaseShopService.getShopByDoCos("", "99999"));

        Mockito.when(hiveVasClient.getTenantInfo(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TenantAllVo.builder()
                        .tenant(TenantInfoVo.builder().status(IntegralConstant.SHOP_ENABLE_STATUS).build())
                        .buildings(Collections.singletonList(TenantBuildingVo.builder().lbsId("99999").build())).build()));
        Assertions.assertNotNull(tbBaseShopService.getShopByDoCos("", "99999"));

        Assertions.assertNotNull(tbBaseShopService.getShopByDoCos("", ""));

    }

    @Test
    void getContractNoListByGroupIdAndFormats() {
        Assertions.assertDoesNotThrow(() -> tbBaseShopService.getContractNoListByGroupIdAndFormats("8aaa82ea804d07cd01805174dd3b000c", Arrays.asList("all", "01", "02")));
    }

    @Test
    void getContractNoList() {
        Assertions.assertNotNull(tbBaseShopService.getContractNoList("123496", ""));
        String ruleFormats = "[{\"mallId\":\"8aaa81cb7c836c6b017c83e2c76f0000\",\"format\":\"all,01,1,2,3,4,0909090909090\",\"formatList\":[\"all\",\"01\",\"1\",\"2\",\"3\",\"4\",\"0909090909090\"]},{\"mallId\":\"8aaa81cb7c836c6b017c83e46b110001\",\"format\":\"all,01,1,2,3,4,0909090909090\",\"formatList\":[\"all\",\"01\",\"1\",\"2\",\"3\",\"4\",\"0909090909090\"]}]";
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(false);
        Assertions.assertDoesNotThrow(() -> tbBaseShopService.getContractNoList("8aaa82ea804d07cd01805174dd3b000c", ruleFormats));

        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(true);
        Assertions.assertDoesNotThrow(() -> tbBaseShopService.getContractNoList("8aaa82ea804d07cd01805174dd3b000c", "12345"));

        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(true);
        Assertions.assertDoesNotThrow(() -> tbBaseShopService.getContractNoList("8aaa82ea804d07cd01805174dd3b000c", ruleFormats));

        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(true);
        Mockito.when(hiveServiceClient.getShopCodesByFormats(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList("12345"));
        Assertions.assertDoesNotThrow(() -> tbBaseShopService.getContractNoList("8aaa82ea804d07cd01805174dd3b000c", ruleFormats));

    }

}
