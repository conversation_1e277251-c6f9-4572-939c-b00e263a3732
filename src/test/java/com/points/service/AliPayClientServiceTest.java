package com.points.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.MerchantCard;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.kerryprops.kip.service.integral.common.enums.TradingAreaEnum;
import com.kerryprops.kip.service.integral.config.AliPayConfig;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.TbInsensatePointsAuthRecordDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.AliPayClientServiceImpl;
import com.kerryprops.kip.service.integral.strategy.AliPayStrategy;
import com.kerryprops.kip.service.integral.webservice.resource.AlipayCardTemplateUpdateResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import static org.mockito.Mockito.when;

@Slf4j
class AliPayClientServiceTest {

    @InjectMocks
    private AliPayClientServiceImpl aliPayClientServiceImpl;

    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    @Mock
    private TbCardMemberRelationService tbCardMemberRelationService;
    @Mock
    private AliPayConfig aliPayConfig;
    @Mock
    private MallConfig mallConfig;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("参数为空")
    void openAlipayMarketingCard() throws Exception {
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        ActiveFormDto activeFormDto = new ActiveFormDto();
        activeFormDto.setGroupId("8a8884e77cc9e70a017cca188fae0003");
        aliPayAuthDto.setActiveForm(activeFormDto);
        Assertions.assertNotNull(aliPayClientServiceImpl.openAlipayMarketingCard(aliPayAuthDto));
    }

    @Test
    @DisplayName("支付场景会员为空")
    void openAlipayMarketingCardCase1() throws Exception {
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        ActiveFormDto activeFormDto = new ActiveFormDto();
        activeFormDto.setGroupId("8a8884e77cc9e70a017cca188fae0003");
        activeFormDto.setMobile("19089765432");
        aliPayAuthDto.setActiveForm(activeFormDto);
        Mockito.when(tbMemberAssetService.getOrCreateMember(Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(aliPayClientServiceImpl.openAlipayMarketingCard(aliPayAuthDto));
    }

    @Test
    @DisplayName("openAlipayMarketingCard流程")
    void openAlipayMarketingCardCase3() throws Exception {
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        aliPayAuthDto.setTemplateId("temPlateId");
        aliPayAuthDto.setGroupId("8a8884e77cc9e70a017cca188fae0003");
        ActiveFormDto activeFormDto = new ActiveFormDto();
        activeFormDto.setGroupId("8a8884e77cc9e70a017cca188fae0003");
        activeFormDto.setMobile("19089765432");
        aliPayAuthDto.setActiveForm(activeFormDto);

        AliPayMemberTokenDto aliPayMemberTokenDto = new AliPayMemberTokenDto();
        aliPayMemberTokenDto.setAccessToken("accessToken");
        aliPayAuthDto.setMemberToken(aliPayMemberTokenDto);

        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        aliPayAuthDto.setMallItem(mallItem);
        Mockito.when(tbMemberAssetService.getOrCreateMember(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().currentPoints(100).build());
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardOpenResponse response = new AlipayMarketingCardOpenResponse();
        MerchantCard merchantCard = new MerchantCard();
        merchantCard.setBizCardNo("BizCardNo");
        response.setCardInfo(merchantCard);
        when(alipayClient.execute(Mockito.any(), Mockito.any())).thenReturn(response);

        AlipayMarketingCardQueryResponse response1 = new AlipayMarketingCardQueryResponse();
        response1.setSchemaUrl("/ali/page");
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardQueryRequest.class))).thenReturn(response1);
        Assertions.assertNotNull(aliPayClientServiceImpl.openAlipayMarketingCard(aliPayAuthDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("querySchemaUrl进入异常")
    void querySchemaUrl() throws AlipayApiException {
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardQueryResponse response1 = new AlipayMarketingCardQueryResponse();
        response1.setSchemaUrl("/ali/page");
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardQueryRequest.class)))
                .thenThrow(new AlipayApiException("error"));
        Assertions.assertNotNull(aliPayClientServiceImpl.querySchemaUrl("",  MallItem.builder().abbreviation("HKC").build()));
        mockedStatic.close();
    }

    @Test
    @DisplayName("create流程")
    void create() throws AlipayApiException {
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient(TradingAreaEnum.SANDBOX.getAbbreviation())).thenReturn(alipayClient);

        AlipayTradePagePayResponse response = new AlipayTradePagePayResponse();
        when(alipayClient.pageExecute(Mockito.any(AlipayTradePagePayRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.create("", "", ""));
        mockedStatic.close();
    }

    @Test
    @DisplayName("create流程进入异常")
    void createCase1() throws AlipayApiException {
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient(TradingAreaEnum.SANDBOX.getAbbreviation())).thenReturn(alipayClient);

        when(alipayClient.pageExecute(Mockito.any(AlipayTradePagePayRequest.class)))
                .thenThrow(new AlipayApiException());
        Assertions.assertNull(aliPayClientServiceImpl.create("", "", ""));
        mockedStatic.close();
    }

    @Test
    @DisplayName("createAliPayCardUrl")
    void createAliPayCardUrl() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardActivateurlApplyResponse response = new AlipayMarketingCardActivateurlApplyResponse();
        response.setApplyCardUrl("/test/url");
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardActivateurlApplyRequest.class)))
                .thenReturn(response);
        Assertions.assertNotNull(aliPayClientServiceImpl.createAliPayCardUrl(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("createAliPayCardUrl进入异常")
    void createAliPayCardUrlCase1() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardActivateurlApplyResponse response = new AlipayMarketingCardActivateurlApplyResponse();
        response.setApplyCardUrl("/test/url");
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardActivateurlApplyRequest.class)))
                .thenThrow(new AlipayApiException("error"));
        Assertions.assertNull(aliPayClientServiceImpl.createAliPayCardUrl(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("cardMarketingTradeSubscribe")
    void cardMarketingTradeSubscribe() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayOpenAppMessageTopicSubscribeResponse response = new AlipayOpenAppMessageTopicSubscribeResponse();
        when(alipayClient.execute(Mockito.any(AlipayOpenAppMessageTopicSubscribeRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.cardMarketingTradeSubscribe(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("queryCardTemplate")
    void queryCardTemplate() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardTemplateQueryResponse response = new AlipayMarketingCardTemplateQueryResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardTemplateQueryRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.queryCardTemplate(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("getAlipayMarketingCardActivateurl")
    void getAlipayMarketingCardActivateurl() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardActivateurlApplyResponse response = new AlipayMarketingCardActivateurlApplyResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardActivateurlApplyRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.getAlipayMarketingCardActivateurl(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("getAlipayMarketingCardFormtemplateSetRequest")
    void getAlipayMarketingCardFormtemplateSetRequest() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardFormtemplateSetResponse response = new AlipayMarketingCardFormtemplateSetResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardFormtemplateSetRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.getAlipayMarketingCardFormtemplateSetRequest(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("configTemplate")
    void configTemplate() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardFormtemplateSetResponse response = new AlipayMarketingCardFormtemplateSetResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardFormtemplateSetRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.configTemplate(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("response为10000")
    void createCardTemplate() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardTemplateCreateResponse response = new AlipayMarketingCardTemplateCreateResponse();
        response.setCode("10000");
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardTemplateCreateRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.createCardTemplate(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("response为40004")
    void createCardTemplateCase1() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardTemplateCreateResponse response = new AlipayMarketingCardTemplateCreateResponse();
        response.setCode("40004");
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardTemplateCreateRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.createCardTemplate(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("response的Code为null")
    void createCardTemplateCase2() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().abbreviation("HKC").build();
        AliPayItem item = new AliPayItem();
        item.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(item);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardTemplateCreateResponse response = new AlipayMarketingCardTemplateCreateResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardTemplateCreateRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.createCardTemplate(mallItem));
        mockedStatic.close();
    }

    @Test
    @DisplayName("syncAlipayPoints-会员为空")
    void syncAlipayPointsCase1() {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.syncAlipayPoints(salesAutoPointsDto));
    }

    @Test
    @DisplayName("syncAlipayPoints-商场为空")
    void syncAlipayPointsCase2() {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("4678dd673cf24ff8a2b30706fc2c4747").build());
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.syncAlipayPoints(salesAutoPointsDto));
    }

    @Test
    @DisplayName("syncAlipayPoints-authRecord为空")
    void syncAlipayPointsCase3() {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("4678dd673cf24ff8a2b30706fc2c4747").build());
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(tbInsensatePointsAuthRecordService.getByAliUserId(Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.syncAlipayPoints(salesAutoPointsDto));
    }

    @Test
    @DisplayName("syncAlipayPoints-updateResponse.isSuccess")
    void syncAlipayPointsCase4() throws AlipayApiException {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().exchangePoints(10).appId("aliPayAppId").build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("4678dd673cf24ff8a2b30706fc2c4747").currentPoints(100).build());
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().aliCardTemplateId("aliPayAppId").build());
        Mockito.when(tbInsensatePointsAuthRecordService.getByAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbInsensatePointsAuthRecordDto.builder().build());
        Mockito.when(tbCardMemberRelationService.getByMallIdAndAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbCardMemberRelation.builder().build());

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardUpdateResponse response = new AlipayMarketingCardUpdateResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardUpdateRequest.class)))
                .thenReturn(response);

        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.syncAlipayPoints(salesAutoPointsDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("syncAlipayPoints-进入异常-areaEnum为空")
    void syncAlipayPointsCase5() throws AlipayApiException {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().exchangePoints(10).appId("aliPayAppId").build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("4678dd673cf24ff8a2b30706fc2c4747").currentPoints(100).build());
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().abbreviation("").build());
        Mockito.when(tbInsensatePointsAuthRecordService.getByAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbInsensatePointsAuthRecordDto.builder().build());
        Mockito.when(tbCardMemberRelationService.getByMallIdAndAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbCardMemberRelation.builder().build());

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardUpdateRequest.class)))
                .thenThrow(new AlipayApiException("error"));

        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.syncAlipayPoints(salesAutoPointsDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("syncAlipayPoints-进入异常-cardDetail为null")
    void syncAlipayPointsCase6() throws AlipayApiException {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().exchangePoints(10).appId("aliPayAppId").build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("4678dd673cf24ff8a2b30706fc2c4747").currentPoints(100).build());
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().abbreviation("HKC").build());
        Mockito.when(tbInsensatePointsAuthRecordService.getByAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbInsensatePointsAuthRecordDto.builder().build());
        Mockito.when(tbCardMemberRelationService.getByMallIdAndAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbCardMemberRelation.builder().build());

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);

        when(alipayClient.execute(Mockito.any(AlipayMarketingCardUpdateRequest.class)))
                .thenThrow(new AlipayApiException("error"));

        AliPayItem payItem = new AliPayItem();
        payItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(payItem);
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardQueryRequest.class)))
                .thenThrow(new AlipayApiException("error"));

        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.syncAlipayPoints(salesAutoPointsDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("syncAlipayPoints-queryAndReSyncAlipay异常")
    void syncAlipayPointsCase7() throws AlipayApiException {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().exchangePoints(10).appId("aliPayAppId").build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("4678dd673cf24ff8a2b30706fc2c4747").currentPoints(100).build());
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().abbreviation("HKC").build());
        Mockito.when(tbInsensatePointsAuthRecordService.getByAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbInsensatePointsAuthRecordDto.builder().build());
        Mockito.when(tbCardMemberRelationService.getByMallIdAndAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbCardMemberRelation.builder().build());

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);

        when(alipayClient.execute(Mockito.any(AlipayMarketingCardUpdateRequest.class)))
                .thenThrow(new AlipayApiException("error"));

        AliPayItem payItem = new AliPayItem();
        payItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(payItem);
        AlipayMarketingCardQueryResponse response = new AlipayMarketingCardQueryResponse();
        MerchantCard card = new MerchantCard();
        response.setCardInfo(card);
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardQueryRequest.class)))
                .thenReturn(response);

        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.syncAlipayPoints(salesAutoPointsDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("syncAlipayPoints-queryAndReSyncAlipay结束")
    void syncAlipayPointsCase8() throws AlipayApiException {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().exchangePoints(10).appId("aliPayAppId").build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("4678dd673cf24ff8a2b30706fc2c4747").currentPoints(100).build());
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().abbreviation("HKC").build());
        Mockito.when(tbInsensatePointsAuthRecordService.getByAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbInsensatePointsAuthRecordDto.builder().build());
        Mockito.when(tbCardMemberRelationService.getByMallIdAndAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbCardMemberRelation.builder().build());

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);

        when(alipayClient.execute(Mockito.any(AlipayMarketingCardUpdateRequest.class)))
                .thenThrow(new AlipayApiException("error"));

        AliPayItem payItem = new AliPayItem();
        payItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(payItem);
        AlipayMarketingCardQueryResponse response = new AlipayMarketingCardQueryResponse();
        MerchantCard card = new MerchantCard();
        response.setCardInfo(card);
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardQueryRequest.class)))
                .thenReturn(response);

        AlipayMarketingCardUpdateResponse updateResponse = new AlipayMarketingCardUpdateResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardUpdateRequest.class)))
                .thenReturn(updateResponse);

        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.syncAlipayPoints(salesAutoPointsDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("clearingNotify")
    void clearingNotify() throws AlipayApiException {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        aliPayBizContentDto.setBuyerId("buyerId");
        aliPayBizContentDto.setAppId("appId");
        SalesAutoPointsDto salesAutoPointsDto = new SalesAutoPointsDto();

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("appId")).thenReturn(alipayClient);
        AlipayBusinessMallPointsNotifyResponse alipayBusinessMallPointsNotifyResponse = new AlipayBusinessMallPointsNotifyResponse();
        when(alipayClient.execute(Mockito.any(AlipayBusinessMallPointsNotifyRequest.class)))
                .thenReturn(alipayBusinessMallPointsNotifyResponse);
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.clearingNotify(aliPayBizContentDto, salesAutoPointsDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("clearingNotify-进入异常")
    void clearingNotifyCase1() throws AlipayApiException {
        AliPayBizContentDto aliPayBizContentDto = new AliPayBizContentDto();
        aliPayBizContentDto.setBuyerId("buyerId");
        aliPayBizContentDto.setAppId("appId");
        SalesAutoPointsDto salesAutoPointsDto = new SalesAutoPointsDto();

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("appId")).thenReturn(alipayClient);
        when(alipayClient.execute(Mockito.any(AlipayBusinessMallPointsNotifyRequest.class)))
                .thenThrow(new AlipayApiException("error"));
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.clearingNotify(aliPayBizContentDto, salesAutoPointsDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("memberCardDetailByCardNo-null")
    void memberCardDetailByCardNo() throws AlipayApiException {
        Mockito.when(tbCardMemberRelationService.getByMallIdAndExternalCardNo(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.memberCardDetailByCardNo("", ""));
    }

    @Test
    @DisplayName("memberCardDetailByCardNo-走完")
    void memberCardDetailByCardNoCase1() throws AlipayApiException {
        Mockito.when(tbCardMemberRelationService.getByMallIdAndExternalCardNo(Mockito.any(), Mockito.any()))
                .thenReturn(TbCardMemberRelation.builder().build());
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(new AliPayItem());

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient(Mockito.any())).thenReturn(alipayClient);
        AlipayMarketingCardQueryResponse response = new AlipayMarketingCardQueryResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardQueryRequest.class)))
                .thenReturn(response);
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.memberCardDetailByCardNo("", ""));
        mockedStatic.close();
    }

    @Test
    @DisplayName("memberCardDetailByAliUserId-null")
    void memberCardDetailByAliUserId() {
        Mockito.when(tbCardMemberRelationService.getByMallIdAndAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.memberCardDetailByAliUserId("", ""));
    }

    @Test
    @DisplayName("memberCardDetailByMobile-null")
    void memberCardDetailByMobile() {
        Mockito.when(tbCardMemberRelationService.getByMallIdAndAliUserId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.memberCardDetailByMobile("", ""));
    }

    @Test
    @DisplayName("getAliMemberCardDetail")
    void getAliMemberCardDetail() throws AlipayApiException {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(new AliPayItem());
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient(Mockito.any())).thenReturn(alipayClient);
        AlipayMarketingCardQueryResponse response = new AlipayMarketingCardQueryResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardQueryRequest.class)))
                .thenReturn(response);
        Assertions.assertNull(aliPayClientServiceImpl.getAliMemberCardDetail("", "", ""));
        mockedStatic.close();
    }

    @Test
    @DisplayName("getAliMemberCardDetail-异常")
    void getAliMemberCardDetailCase1() throws AlipayApiException {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(new AliPayItem());
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient(Mockito.any())).thenReturn(alipayClient);
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardQueryRequest.class)))
                .thenThrow(new AlipayApiException("error"));
        Assertions.assertNull(aliPayClientServiceImpl.getAliMemberCardDetail("", "", ""));
        mockedStatic.close();
    }

    @Test
    @DisplayName("queryActiveForm")
    void queryActiveForm() throws AlipayApiException, JsonProcessingException {
        AliPayAuthDto aliPayAuthDto = new AliPayAuthDto();
        AliPayMemberTokenDto aliPayMemberTokenDto = new AliPayMemberTokenDto();
        aliPayMemberTokenDto.setAccessToken("accessToken");
        aliPayAuthDto.setMemberToken(aliPayMemberTokenDto);
        MallItem mallItem = MallItem.builder().build();
        aliPayAuthDto.setMallItem(mallItem);
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(aliPayItem);
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardActivateformQueryResponse response = new AlipayMarketingCardActivateformQueryResponse();
        response.setInfos("[{\"OPEN_FORM_FIELD_BIRTHDAY\":\"03-27\"},{\"OPEN_FORM_FIELD_GENDER\":\"女\"},{\"OPEN_FORM_FIELD_MOBILE\":\"15221024085\"},{\"OPEN_FORM_FIELD_NAME\":\"熊慧\"}]");
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardActivateformQueryRequest.class), Mockito.any()))
                .thenReturn(response);
        Assertions.assertNotNull(aliPayClientServiceImpl.queryActiveForm(aliPayAuthDto));
        mockedStatic.close();
    }

    @Test
    @DisplayName("getAccessToken")
    void getAccessToken() throws AlipayApiException {
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(aliPayItem);
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipaySystemOauthTokenResponse response = new AlipaySystemOauthTokenResponse();
        when(alipayClient.execute(Mockito.any(AlipaySystemOauthTokenRequest.class)))
                .thenReturn(response);
        Assertions.assertNotNull(aliPayClientServiceImpl.getAccessToken("", ""));
        mockedStatic.close();
    }

    @Test
    @DisplayName("getAccessToken-进入异常")
    void getAccessTokenCase1() throws AlipayApiException {
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(aliPayItem);
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        when(alipayClient.execute(Mockito.any(AlipaySystemOauthTokenRequest.class)))
                .thenThrow(new AlipayApiException("error"));
        Assertions.assertNull(aliPayClientServiceImpl.getAccessToken("", ""));
        mockedStatic.close();
    }

    @Test
    @DisplayName("uploadAlipayCardBackgroundImage")
    void uploadAlipayCardBackgroundImage() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().build();
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(mallItem);
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(aliPayItem);
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayOfflineMaterialImageUploadResponse response = new AlipayOfflineMaterialImageUploadResponse();
        when(alipayClient.execute(Mockito.any(AlipayOfflineMaterialImageUploadRequest.class)))
                .thenReturn(response);
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.uploadAlipayCardBackgroundImage("", ""));
        mockedStatic.close();
    }

    @Test
    @DisplayName("uploadAlipayCardBackgroundImage-进入异常")
    void uploadAlipayCardBackgroundImageCase1() throws AlipayApiException {
        MallItem mallItem = MallItem.builder().build();
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(mallItem);
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(aliPayItem);
        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        when(alipayClient.execute(Mockito.any(AlipayOfflineMaterialImageUploadRequest.class)))
                .thenThrow(new AlipayApiException("error"));
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.uploadAlipayCardBackgroundImage("", ""));
        mockedStatic.close();
    }

    @Test
    @DisplayName("updateAlipayCardTemplateRequest")
    void updateAlipayCardTemplateRequest() throws AlipayApiException {
        AlipayCardTemplateUpdateResource resource = new AlipayCardTemplateUpdateResource();

        MallItem mallItem = MallItem.builder().build();
        mallItem.setAbbreviation("BKC");
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(mallItem);
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(aliPayItem);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardTemplateModifyResponse response = new AlipayMarketingCardTemplateModifyResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardTemplateModifyRequest.class)))
                .thenReturn(response);
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.updateAlipayCardTemplateRequest(resource));
        mockedStatic.close();
    }

    @Test
    @DisplayName("updateAlipayCardTemplateRequest-进入异常")
    void updateAlipayCardTemplateRequestCase1() throws AlipayApiException {
        AlipayCardTemplateUpdateResource resource = new AlipayCardTemplateUpdateResource();

        MallItem mallItem = MallItem.builder().build();
        mallItem.setAbbreviation("BKC");
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(mallItem);
        AliPayItem aliPayItem = new AliPayItem();
        aliPayItem.setAliPayAppId("aliPayAppId");
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(aliPayItem);

        MockedStatic<AliPayStrategy> mockedStatic = Mockito.mockStatic(AliPayStrategy.class);
        AlipayClient alipayClient = Mockito.mock(AlipayClient.class);
        Mockito.when(AliPayStrategy.getClient("aliPayAppId")).thenReturn(alipayClient);
        AlipayMarketingCardTemplateModifyResponse response = new AlipayMarketingCardTemplateModifyResponse();
        when(alipayClient.execute(Mockito.any(AlipayMarketingCardTemplateModifyRequest.class)))
                .thenThrow(new AlipayApiException("error"));
        Assertions.assertDoesNotThrow(() -> aliPayClientServiceImpl.updateAlipayCardTemplateRequest(resource));
        mockedStatic.close();
    }

}