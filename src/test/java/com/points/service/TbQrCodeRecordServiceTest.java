package com.points.service;

import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.entity.TbQrCodeRecord;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeRuleMapper;
import com.kerryprops.kip.service.integral.mapper.TbQrCodeRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.BillInfoRpcDto;
import com.kerryprops.kip.service.integral.service.TbQrCodeRecordService;
import com.kerryprops.kip.service.integral.service.impl.TbMemberGradeRuleServiceImpl;
import com.kerryprops.kip.service.integral.service.impl.TbQrCodeRecordServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 07/31/2024 10:37
 **********************************************************************************************************************/

@Slf4j
class TbQrCodeRecordServiceTest {

    @InjectMocks
    private TbQrCodeRecordServiceImpl tbQrCodeRecordServiceImpl;
    @Mock
    private TbQrCodeRecordMapper tbQrCodeRecordMapper;
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getByQrCode() {
        Mockito.when(tbQrCodeRecordMapper.getByQrCode(Mockito.anyString())).thenReturn(new TbQrCodeRecord());
        Assertions.assertNotNull(tbQrCodeRecordServiceImpl.getByQrCode(Mockito.anyString()));
    }


    @Test
    void saveOrUpdate() {
        BillInfoRpcDto billInfoRpcDto = BillInfoRpcDto.builder().build();

        Mockito.when(tbQrCodeRecordMapper.getByQrCode(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbQrCodeRecordServiceImpl.saveOrUpdate("", billInfoRpcDto));

        Mockito.when(tbQrCodeRecordMapper.getByQrCode(Mockito.any())).thenReturn(TbQrCodeRecord.builder().build());
        Assertions.assertDoesNotThrow(() -> tbQrCodeRecordServiceImpl.saveOrUpdate("", billInfoRpcDto));
    }

}
