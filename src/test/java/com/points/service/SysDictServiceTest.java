package com.points.service;

import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.mapper.SysDictMapper;
import com.kerryprops.kip.service.integral.service.impl.SysDictServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/
@Slf4j
class SysDictServiceTest {

    @InjectMocks
    private SysDictServiceImpl sysDictService;

    @Mock
    private SysDictMapper sysDictMapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findDictNames() {
        Assertions.assertNotNull(sysDictService.findDictNames(null));

        Mockito.when(sysDictMapper.findByDictTypes(Mockito.anyList()))
                .thenReturn(Collections.singletonList(SysDict.builder().dictName("123").dictType("123").build()));
        Assertions.assertNotNull(sysDictService.findDictNames(Collections.singletonList("12345")));
    }

    @Test
    void findByDictType() {
        Mockito.when(sysDictMapper.findByDictType(Mockito.any())).thenReturn(SysDict.builder().dictType("123").dictType("123").build());
        Assertions.assertNotNull(sysDictService.findByDictType("123"));
    }

    @Test
    void getByTypes() {
        Mockito.when(sysDictMapper.getListByTypes(Mockito.any()))
                .thenReturn(null);
        Assertions.assertNull(sysDictService.getByTypes(Collections.singletonList("123")));
    }

}
