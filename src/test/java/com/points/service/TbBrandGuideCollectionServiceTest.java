package com.points.service;

import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbBrandGuideCollection;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.TbBrandGuideCollectionMapper;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.service.impl.TbBrandGuideCollectionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 07/18/2024 16:23
 **********************************************************************************************************************/

@Slf4j
class TbBrandGuideCollectionServiceTest {

    @InjectMocks
    private TbBrandGuideCollectionServiceImpl tbBrandGuideCollectionService;
    @Mock
    private TbBrandGuideCollectionMapper tbBrandGuideCollectionMapper;
    @Mock
    private RedisService redisService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void saveBrandGuide() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .lbsId("8aaa80b47c784020017c78b00d060022").build();
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.saveBrandGuide(loginUser, TbMemberAsset.builder().build(), "111"));

        Mockito.when(tbBrandGuideCollectionMapper.findByGroupIdAndVipcodeAndBrandGuideId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(TbBrandGuideCollection.builder().status(1).build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.saveBrandGuide(loginUser, TbMemberAsset.builder().build(), "111"));

        Mockito.when(tbBrandGuideCollectionMapper.findByGroupIdAndVipcodeAndBrandGuideId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(TbBrandGuideCollection.builder().status(0).build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.saveBrandGuide(loginUser, TbMemberAsset.builder().build(), "111"));
    }

    @Test
    void cancelBrandGuide() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.cancelBrandGuide(loginUser, TbMemberAsset.builder().build(), "111"));

        Mockito.when(tbBrandGuideCollectionMapper.findByGroupIdAndVipcodeAndBrandGuideId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(TbBrandGuideCollection.builder().status(0).build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.cancelBrandGuide(loginUser, TbMemberAsset.builder().build(), "111"));

        Mockito.when(tbBrandGuideCollectionMapper.findByGroupIdAndVipcodeAndBrandGuideId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(TbBrandGuideCollection.builder().status(1).build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.cancelBrandGuide(loginUser, TbMemberAsset.builder().build(), "111"));
    }

    @Test
    void saveOrCancelBrandGuide() {
        TbBrandGuideCollection collection = TbBrandGuideCollection.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022")
                .brandGuideId("222").vipcode("KERRY100213877").status(1).createDate(new Date()).build();
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.saveOrCancelBrandGuide(collection));

        Mockito.when(tbBrandGuideCollectionMapper.findByGroupIdAndVipcodeAndBrandGuideId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(TbBrandGuideCollection.builder().status(1).build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.saveOrCancelBrandGuide(collection));

        Mockito.when(tbBrandGuideCollectionMapper.findByGroupIdAndVipcodeAndBrandGuideId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(TbBrandGuideCollection.builder().status(0).build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.saveOrCancelBrandGuide(collection));
    }

    @Test
    void getBrandGuideList() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        Mockito.when(redisService.hasKey(Mockito.any())).thenReturn(true);
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.getBrandGuideList(loginUser.getBrandId(), loginUser.getLbsId(), "12345"));

        Mockito.when(redisService.hasKey(Mockito.any())).thenReturn(false);
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.getBrandGuideList(loginUser.getBrandId(), loginUser.getLbsId(), "12345"));
    }

    @Test
    void getBrandGuideFromDb() {
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionService.getBrandGuideFromDb("12345", "12345", "12345"));
    }

}
