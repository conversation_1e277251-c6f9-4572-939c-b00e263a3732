package com.points.service;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.mapper.TbPhotoReviewMapper;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TbPhotoReviewServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 07/31/2024 10:37
 **********************************************************************************************************************/

@Slf4j
class TbPhotoReviewServiceTest {

    @InjectMocks
    private TbPhotoReviewServiceImpl tbPhotoReviewService;
    @Mock
    private TbPhotoReviewMapper tbPhotoReviewMapper;
    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private RedisService redisService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private Mapper mapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void geTbBonusSelfList() {
        Assertions.assertNotNull(tbPhotoReviewService.geTbBonusSelfList(BonusSelfQueryDto.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").build()));
    }

    @Test
    void updateById() {
        Mockito.when(tbPhotoReviewMapper.updateById(Mockito.any())).thenReturn(1);
        Assertions.assertTrue(tbPhotoReviewService.updateById(TbPhotoReview.builder().build()));

        Mockito.when(tbPhotoReviewMapper.updateById(Mockito.any())).thenReturn(0);
        Assertions.assertFalse(tbPhotoReviewService.updateById(TbPhotoReview.builder().build()));
    }

    @Test
    void saveRecord() {
        BonusSelfUploadDto dto1 = BonusSelfUploadDto.builder()
                .groupId("8aaa82ea804d07cd01805174dd3b000c")
                .mallId("8aaa81cb7c836c6b017c83e2c76f0000")
                .vipcode("KERRY100216282")
                .mobile("18710068717")
                .type("3")
                .imageUrl("")
                .status("1")
                .build();
        Mockito.when(mapper.map(dto1, TbPhotoReview.class)).thenReturn(TbPhotoReview.builder()
                .groupId("8aaa82ea804d07cd01805174dd3b000c")
                .mallId("8aaa81cb7c836c6b017c83e2c76f0000")
                .vipcode("KERRY100216282")
                .mobile("18710068717")
                .type("3")
                .imageUrl("")
                .status("1")
                .build());
        assertDoesNotThrow(() -> tbPhotoReviewService.saveRecord(dto1));

        BonusSelfUploadDto dto2 = BonusSelfUploadDto.builder()
                .groupId("8aaa82ea804d07cd01805174dd3b000c")
                .mallId("8aaa81cb7c836c6b017c83e2c76f0000")
                .vipcode("KERRY100216282")
                .mobile("18710068717")
                .type("3")
                .imageUrl("")
                .status("1")
                .reason("12345")
                .description("12345")
                .qrcode("12345")
                .build();
        Mockito.when(mapper.map(dto2, TbPhotoReview.class)).thenReturn(TbPhotoReview.builder()
                .groupId("8aaa82ea804d07cd01805174dd3b000c")
                .mallId("8aaa81cb7c836c6b017c83e2c76f0000")
                .vipcode("KERRY100216282")
                .mobile("18710068717")
                .type("3")
                .imageUrl("")
                .status("1")
                .reason("12345")
                .description("12345")
                .qrcode("12345")
                .build());
        assertDoesNotThrow(() -> tbPhotoReviewService.saveRecord(dto2));
    }

    @Test
    void auditRecord0() {
        TakePhotoAuditDto dto = TakePhotoAuditDto.builder().id(10L).shopNo("HKC00001").mallId("8aaa81cb7c836c6b017c83e2c76f0000")
                .state("3").reason("测试").build();
        Mockito.when(tbPhotoReviewMapper.selectById(Mockito.any())).thenReturn(TbPhotoReview.builder().status("1").build());
        assertDoesNotThrow(() -> tbPhotoReviewService.auditRecord(dto));

        TakePhotoAuditDto dto2 = TakePhotoAuditDto.builder().id(10L).shopNo("HKC00001").mallId("8aaa81cb7c836c6b017c83e2c76f0000")
                .state("2").reason("测试").build();
        BizException exception = assertThrows(BizException.class, () -> tbPhotoReviewService.auditRecord(dto2));
        assertEquals(PointsEnum.MEMBER_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void auditRecord01() {
        Mockito.when(tbPhotoReviewMapper.selectById(Mockito.any())).thenReturn(TbPhotoReview.builder().status("1").build());

        TakePhotoAuditDto dto2 = TakePhotoAuditDto.builder().id(10L).shopNo("HKC00001").mallId("8aaa81cb7c836c6b017c83e2c76f0000")
                .state("2").reason("测试").build();
        BizNotFoundException exception = assertThrows(BizNotFoundException.class, () -> tbPhotoReviewService.auditRecord(dto2));
        assertEquals(PointsEnum.MEMBER_NOT_EXISTS.getCode(), exception.getCode());
    }

    @Test
    void auditRecord() {
        Assertions.assertThrows(Exception.class, ()->tbPhotoReviewService.auditRecord(TakePhotoAuditDto.builder().build()));

        Mockito.when(tbPhotoReviewMapper.selectById(Mockito.any())).thenReturn(TbPhotoReview.builder().status("2").build());
        Assertions.assertThrows(Exception.class, ()->tbPhotoReviewService.auditRecord(TakePhotoAuditDto.builder().build()));

        Mockito.when(tbPhotoReviewMapper.selectById(Mockito.any())).thenReturn(TbPhotoReview.builder().status("1").build());
        Assertions.assertThrows(Exception.class, ()->tbPhotoReviewService.auditRecord(TakePhotoAuditDto.builder().build()));

        Mockito.when(tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertThrows(Exception.class, ()->tbPhotoReviewService.auditRecord(TakePhotoAuditDto.builder().state("2").build()));

        // tbBaseShop为空 state为3，shopNo为空
        assertDoesNotThrow(() ->tbPhotoReviewService.auditRecord(TakePhotoAuditDto.builder().state("3").build()));

        // tbBaseShop不为空 state不为3，shopNo不为空
        Mockito.when(tbPhotoReviewMapper.selectById(Mockito.any())).thenReturn(TbPhotoReview.builder().status("1").build());
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(new TbBaseShop());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any())).thenReturn(100);
        assertDoesNotThrow(() ->tbPhotoReviewService
                .auditRecord(TakePhotoAuditDto.builder().shopNo("12345").money(BigDecimal.TEN).sellDate("2025-02-17 00:00:00").state("1").build()));

        // 进入异常
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any())).thenThrow(new BizException(400, ""));
        Assertions.assertThrows(Exception.class, () ->tbPhotoReviewService
                .auditRecord(TakePhotoAuditDto.builder().shopNo("12345").money(BigDecimal.TEN).sellDate("2025-02-17 00:00:00").state("1").build()));
    }

    @Test
    void getBonusTotal() {
        assertDoesNotThrow(() -> tbPhotoReviewService.getBonusTotal(BonusSelfQueryDto.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").build()));
    }

    @Test
    void getReviewPage() {
        Assertions.assertNotNull(tbPhotoReviewService.getReviewPage(BonusSelfQueryDto.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").build()));
        
    }

    @Test
    void findByIdAndVipcode() {
        assertDoesNotThrow(()->tbPhotoReviewService.findByIdAndVipcode(1L, "KERRY100213904"));
    }

    @Test
    void getForPrevPage() {
        Mockito.when(tbPhotoReviewMapper.getForPrevPage(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(TbPhotoReview.builder().build());
        Assertions.assertNotNull(tbPhotoReviewService.getForPrevPage("1724653223199", "8aaa81cb7c836c6b017c83e2c76f0000", "1717344000000", "1725292799000"));
        
    }

    @Test
    void getForNextPage() {
        Mockito.when(tbPhotoReviewMapper.getForNextPage(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNull(tbPhotoReviewService.getForNextPage("1724653223199", "8aaa81cb7c836c6b017c83e2c76f0000", "1717344000000", "1725292799000"));
        
    }

    @Test
    void getNextImage() {
        Mockito.when(tbPhotoReviewMapper.getNextImage(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNull(tbPhotoReviewService.getNextImage("1724653223199", "8aaa81cb7c836c6b017c83e2c76f0000", "1717344000000", "1725292799000"));
    }

    @Test
    void fillPhotoViewInfo() {
        TbMemberAsset asset = TbMemberAsset.builder().currentPoints(100).grade("01").build();
        TbBaseShop baseShop = TbBaseShop.builder().retailBrandName("").firstFormatCode("").build();
        assertDoesNotThrow(() -> tbPhotoReviewService.fillPhotoViewInfo(asset, TbPhotoReview.builder().build(), baseShop, 100, "1"));
    }

    @Test
    void updatePhotoViewRejectReason() {
        TbSalesDetail salesDetail = TbSalesDetail.builder().vipcode("KERRY100213904").shopNo("JAKC00011019").shopName("test").orderNo("1010200303").payAmount(new BigDecimal("100")).build();
        TbBaseShop baseShop = TbBaseShop.builder().retailBrandName("").firstFormatCode("").build();
        assertDoesNotThrow(() -> tbPhotoReviewService.updatePhotoViewRejectReason(TbPhotoReview.builder().build(), salesDetail, baseShop));
    }

    @Test
    void updatePhotoViewStatus() {
        assertDoesNotThrow(() ->
                tbPhotoReviewService.updatePhotoViewStatus(TbPhotoReview.builder().shopName("12345").build(), PhotoReviewStatusEnum.TYPE_4));

        assertDoesNotThrow(() ->
                tbPhotoReviewService.updatePhotoViewStatus(TbPhotoReview.builder().shopNo("12345").build(), PhotoReviewStatusEnum.TYPE_4));

        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(TbBaseShop.builder().build());
        assertDoesNotThrow(() ->
                tbPhotoReviewService.updatePhotoViewStatus(TbPhotoReview.builder().shopNo("12345").build(), PhotoReviewStatusEnum.TYPE_4));

        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().retailBrandName("12345").build());
        assertDoesNotThrow(() ->
                tbPhotoReviewService.updatePhotoViewStatus(TbPhotoReview.builder().shopNo("12345").build(), PhotoReviewStatusEnum.TYPE_4));
    }

    @Test
    void findByOcrTaskId() {
        assertDoesNotThrow(() ->tbPhotoReviewService.findByOcrTaskId("240731_621add5716c547f6883e020a29366900_uat"));
    }

    @Test
    void findByQrCode() {
        assertDoesNotThrow(() -> tbPhotoReviewService.findByQrCode("1234"));
    }

    @Test
    void checkPhotoReviewRecord() {
        PhotoReviewCheckDto checkDto = PhotoReviewCheckDto.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").vipcode("KERRY100213886").ticketNo("2024080242000001659").shopNo("JAKC00011019")
                .amount(Double.parseDouble("1500.00")).tradingDate("2024-08-02").timestamp("16:59:10").idNotIn(Collections.singletonList(10L)).build();
        assertDoesNotThrow(() ->tbPhotoReviewService.checkPhotoReviewRecord(checkDto));
    }

    @Test
    void checkQrCodeExists() {
        assertDoesNotThrow(() -> tbPhotoReviewService.checkQrCodeExists("25qwe432"));
    }

}
