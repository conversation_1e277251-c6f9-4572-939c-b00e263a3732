package com.points.service;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.entity.TbKoIntegralClearRecord;
import com.kerryprops.kip.service.integral.mapper.TbKoIntegralClearRecordMapper;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.TbKoIntegralClearRecordServiceImpl;
import com.kerryprops.kip.service.integral.webservice.resource.IntegralClearFileResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

@Slf4j
class TbKoIntegralClearRecordServiceTest {

    @InjectMocks
    private TbKoIntegralClearRecordServiceImpl tbKoIntegralClearRecordService;

    @Mock
    private TbKoIntegralClearRecordMapper tbKoIntegralClearRecordMapper;
    @Mock
    private Mapper mapper;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void saveRecords() {
        Assertions.assertDoesNotThrow(() -> tbKoIntegralClearRecordService.saveRecords(null));

        Mockito.when(mapper.map(IntegralClearFileResource.builder().build(), TbKoIntegralClearRecord.class)).thenReturn(TbKoIntegralClearRecord.builder().build());
        Assertions.assertDoesNotThrow(() -> tbKoIntegralClearRecordService.saveRecords(Collections.singletonList(IntegralClearFileResource.builder().build())));
    }

    @Test
    void getRecords() {
        Mockito.when(tbKoIntegralClearRecordMapper.total()).thenReturn(10);
        Assertions.assertEquals(10, tbKoIntegralClearRecordService.total());
    }

}