package com.points.service;

import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.client.SmallTicketRecognitionClient;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.common.utils.OcrQrCodeUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.model.dto.SmallTicketUploadDto;
import com.kerryprops.kip.service.integral.service.SmallTicketRecognitionService;
import com.kerryprops.kip.service.integral.service.TbBaseShopService;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import com.kerryprops.kip.service.integral.service.TbPhotoReviewService;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketCallbackResource;
import com.kerryprops.kip.service.integral.webservice.response.OcrPhotoResponse;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.web.context.request.async.DeferredResult;

import static org.mockito.ArgumentMatchers.any;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 08/02/2024 09:14
 **********************************************************************************************************************/

@Slf4j
class SmallTicketRecognitionServiceTest extends BaseTest {

    @Autowired
    private SmallTicketRecognitionService smallTicketRecognitionService;
    @Autowired
    private TbMemberAssetService tbMemberAssetService;
    @Autowired
    private TbPhotoReviewService tbPhotoReviewService;
    @Autowired
    private MallConfig mallConfig;
    @MockBean
    private SmallTicketRecognitionClient smallTicketRecognitionClient;
    @MockBean
    private TbBaseShopService tbBaseShopService;

    @Test
    void asyncUploadImgToHeHe() throws InterruptedException {
        DeferredResult<OcrPhotoResponse> result = new DeferredResult<>(2000L);
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY100213686").build());

        MallItem mallItem = mallConfig.getByMallId("8aaa80b47c784020017c78b00d060022");

        Mockito.when(smallTicketRecognitionClient.uploadFile(any(String.class), any(String.class), any(String.class), any(String.class), any(Integer.class), any(), any(byte[].class)))
                .thenReturn(SmallTicketUploadDto.builder().code(0).taskId(IdUtil.simpleUUID()).build());

        Assertions.assertDoesNotThrow(() -> smallTicketRecognitionService.asyncUploadImgToHeHe(result, tbMemberAsset, mallItem, "https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/ac35d2e2f46b4e03a1ae0bf60eb71ef1_n9URObnlemkq4d37d36d625edef943d7fd0925161809.jpg"));
    }

    @Test
    void matchUploadImgToHeHe() {
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").vipcode("KERRY100213686").build());
        MallItem mallItem = mallConfig.getByMallId("8aaa80b47c784020017c78b00d060022");

        Mockito.when(smallTicketRecognitionClient.uploadFile(any(String.class), any(String.class), any(String.class), any(String.class), any(Integer.class), any(), any(byte[].class)))
                .thenReturn(SmallTicketUploadDto.builder().code(0).taskId(IdUtil.simpleUUID()).build());

        smallTicketRecognitionService.matchUploadImgToHeHe(tbMemberAsset, mallItem, "https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/ac35d2e2f46b4e03a1ae0bf60eb71ef1_n9URObnlemkq4d37d36d625edef943d7fd0925161809.jpg");
    }

    @Test
    void getUploadResult() {
        String taskId = IdUtil.simpleUUID();
        Mockito.when(smallTicketRecognitionClient.queryUploadResult(any(String.class), any(String.class), any(String.class), any(String.class)))
                .thenReturn(SmallTicketCallbackResource.builder().isPass(Boolean.TRUE).isRobot(Boolean.FALSE).mall("test").taskId(taskId).shop("test").amount("120.00").ticketNo("test1234").transTime("2024-08-02 10:00:00").build());
        Assertions.assertNotNull(smallTicketRecognitionService.getUploadResult(taskId));
    }

    @Test
    void getUploadResultByOuterId() {
        String outerId = OcrQrCodeUtils.simpleQrCode();
        SmallTicketCallbackResource response = SmallTicketCallbackResource.builder().isPass(Boolean.TRUE).isRobot(Boolean.FALSE).mall("test").outerId(outerId).shop("test").amount("120.00").ticketNo("test1234").transTime("2024-08-02 10:00:00").build();
        Mockito.when(smallTicketRecognitionClient.queryUploadResultByOuterId(any(), any(), any(), any())).thenReturn(response);
        SmallTicketCallbackResource resource = smallTicketRecognitionService.getUploadResultByOuterId(outerId);
        Assertions.assertNotNull(resource);
    }

    @Test
    void fillShopInfo() {
        String shopJson = "{\"id\":\"8aaa81a27f8cb15d017f8cbd0dd90000\",\"name\":\"杭州百美汇影院有限公司,上海香珍食品有限公司杭州庆春路分公司\",\"brandName\":\"香珍食品\",\"status\":\"ENABLE\",\"useFlexibleSpace\":true,\"salesReported\":true,\"area\":381.47,\"identityAuthenticationSwitch\":true,\"identityAuthenticationWay\":\"VERIFY\",\"verifyEmailSuffix\":true,\"identityAuthenticationTimeAutoExtensionSwitch\":false,\"identityAuthenticationExtensionDay\":0,\"visitorInvitationSwitch\":true,\"importantVisitorCarSwitch\":false,\"permanentVisitor\":false,\"visitorAccessSwitch\":true,\"visitorAccessApproveSwitch\":true,\"temporaryVisitorSwitch\":true,\"accessPermitSwitch\":true,\"administrativeToiletEnabled\":true,\"administrativeToiletConfigManageWay\":\"PROPERTYMANAGE\",\"roomIdSet\":[\"4028e3817c77b5bc017c77bc6c3a0344\",\"4028e3817c77b5bc017c77bc6e43034c\"],\"floorIdSet\":[\"8aaa80ae7c741565017c749fc3690004\"],\"buildingIdSet\":[\"HKC-R1\"],\"doCoSet\":[\"127962\"],\"contracts\":[{\"doco\":\"127962\",\"version\":1,\"status\":\"TAKE_EFFECT\",\"rooms\":[{\"id\":\"4028e3817c77b5bc017c77bc6c3a0344\",\"floorId\":\"8aaa80ae7c741565017c749fc3690004\"},{\"id\":\"4028e3817c77b5bc017c77bc6e43034c\",\"floorId\":\"8aaa80ae7c741565017c749fc3690004\"}]}],\"contractNo\":\"HKC00041001\",\"shopName\":\"餐饮1\",\"retailBrandId\":\"1448187541341081602\",\"retailBrandName\":\"旧元素餐饮长沙重庆音乐快乐\",\"firstFormatCode\":\"02\",\"firstFormatName\":\"餐饮\",\"secondFormatCode\":\"0201\",\"secondFormatName\":\"中餐\",\"thirdFormatCode\":\"020101\",\"thirdFormatName\":\"地方菜1\",\"password\":\"21232f297a57a5a743894a0e4a801fc3\",\"crmOperateStatus\":true}";
        Mockito.when(tbBaseShopService.getByTenantId(any(String.class), any(String.class)))
                .thenReturn(JsonUtils.stringToObj(shopJson, TbBaseShop.class));

        TbPhotoReview review = tbPhotoReviewService.getById("3");
        TbBaseShop baseShop = tbBaseShopService.getByTenantId("8aaa81a27f8cb15d017f8cbd0dd90000", "8aaa80b47c784020017c78b00d060022");
        Assertions.assertDoesNotThrow(() -> smallTicketRecognitionService.fillShopInfo(review, baseShop));
    }

    @Test
    void fillPhotoInfo() {
        TbPhotoReview review = tbPhotoReviewService.getById("3");

        String json = "{\"isPass\":true,\"isRobot\":false,\"mall\":\"8aaa81cb7c836c6b017c83e2c76f0000\",\"shop\":\"JAKC00011019\",\"amount\":\"80.00\",\"ticketNo\":\"202408014200000\",\"transTime\":\"2024-08-01 10:34:10\",\"rejectReason\":{\"id\":12,\"name\":\"小票信息不完整-五要素不全\",\"missingFields\":[\"shop\",\"amount\",\"ticketNo\",\"transTime\"],\"type\":\"\"},\"task_id\":\"240801_cf40e307a3794fc2be53e63ec49aaa8a_uat\"}";

        SmallTicketCallbackResource resource = JsonUtils.stringToObj(json, SmallTicketCallbackResource.class);
        Assertions.assertDoesNotThrow(() -> smallTicketRecognitionService.fillPhotoInfo(review, resource));
    }

}
