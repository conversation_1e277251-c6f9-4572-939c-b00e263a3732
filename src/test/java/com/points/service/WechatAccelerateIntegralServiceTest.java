package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.pmw.client.resource.*;
import com.kerryprops.kip.pmw.client.service.BizCircleClientService;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.EventTypeEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.impl.WechatAccelerateIntegralServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationEventPublisher;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> nancy
 * Created Date - 08/29/2023 11:55
 **********************************************************************************************************************/
class WechatAccelerateIntegralServiceTest {

    @InjectMocks
    private WechatAccelerateIntegralServiceImpl wechatAccelerateIntegralServiceImpl;

    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private ApplicationEventPublisher applicationEventPublisher;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    @Mock
    private TbPointsDetailService tbPointsDetailService;
    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;
    @Mock
    private BizCircleClientService bizCircleClientService;
    @Mock
    private HeaderResource headerResource;
    @Mock
    private TbInsensatePointsPushRecordService tbInsensatePointsPushRecordService;
    @Mock
    private RedisService redisService;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private TbMemberCardOpenRecordService tbMemberCardOpenRecordService;
    @Mock
    private TbAutoPointsConfigService tbAutoPointsConfigService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void payCallback() {
        PointsPaymentConfirmResource resource = Mockito.mock(PointsPaymentConfirmResource.class);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.payCallback(resource));

        PointsPaymentConfirmResource.PointsPaymentDetailResource detailResource1 = new PointsPaymentConfirmResource.PointsPaymentDetailResource();
        PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource body1 = new PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource();
        body1.setEventType(EventTypeEnum.TRANS_SUCC.getName());
        body1.setResource(detailResource1);
        PointsPaymentConfirmResource resource1 = new PointsPaymentConfirmResource(null, body1, "");
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.payCallback(resource1));

        PointsPaymentConfirmResource.PointsPaymentDetailResource detailResource2 = new PointsPaymentConfirmResource.PointsPaymentDetailResource();
        detailResource2.setOpenId("openId");
        detailResource2.setMchId("mchId");
        detailResource2.setShopNumber("shopNumber");
        detailResource2.setAmount(100);
        PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource body2 = new PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource();
        body2.setEventType(EventTypeEnum.TRANS_SUCC.getName());
        body2.setResource(detailResource2);
        PointsPaymentConfirmResource resource2 = new PointsPaymentConfirmResource(null, body2, "");
        when(mallConfig.getByMchId(any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.payCallback(resource2));

        when(mallConfig.getByMchId(any())).thenReturn(MallItem.builder().build());
        when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(any(), any(), any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.payCallback(resource2));

        when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(any(), any(), any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("kipUserId").build());
        when(tbBaseShopService.getByContractNoAndMallId(any(), any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.payCallback(resource2));

        when(tbBaseShopService.getByContractNoAndMallId(any(), any())).thenReturn(new TbBaseShop());
        when(redisService.setSaleNoIfAbsent(any(), any())).thenReturn(true);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.payCallback(resource2));
    }

    @Test
    void refundCallback() {
        PointsRefundConfirmResource resource = Mockito.mock(PointsRefundConfirmResource .class);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.refundCallback(resource));

        PointsRefundConfirmResource.PointsRefundDetailResource detailResource1 = new PointsRefundConfirmResource.PointsRefundDetailResource();
        PointsRefundConfirmResource.PointsRefundConfirmBodyResource body1 = new PointsRefundConfirmResource.PointsRefundConfirmBodyResource();
        body1.setEventType(EventTypeEnum.TRANS_SUCC.getName());
        body1.setResource(detailResource1);
        PointsRefundConfirmResource resource1 = new PointsRefundConfirmResource(null, body1, "");
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.refundCallback(resource1));

        PointsRefundConfirmResource.PointsRefundDetailResource detailResource2 = new PointsRefundConfirmResource.PointsRefundDetailResource();
        detailResource2.setRefundAmount(100);
        detailResource2.setPayAmount(100);
        PointsRefundConfirmResource.PointsRefundConfirmBodyResource body2 = new PointsRefundConfirmResource.PointsRefundConfirmBodyResource();
        body2.setEventType(EventTypeEnum.REFUND_SUCC.getName());
        body2.setResource(detailResource2);
        PointsRefundConfirmResource resource2 = new PointsRefundConfirmResource(null, body2, "");
        when(mallConfig.getByMchId(any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.refundCallback(resource2));

        when(mallConfig.getByMchId(any())).thenReturn(MallItem.builder().build());
        when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(any(), any(), any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.refundCallback(resource2));

        when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(any(), any(), any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("kipUserId").build());
        when(redisService.setSaleNoIfAbsent(any(), any())).thenReturn(false);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.refundCallback(resource2));

        when(redisService.setSaleNoIfAbsent(any(), any())).thenReturn(true);
        when(memberSalePointsProcessService.refundSalePointsProcess(any()))
                .thenThrow(new BizException(400 , ""));
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.refundCallback(resource2));
    }

    @Test
    void memberPointsAuthCase1() {
        PointsActivationConfirmResource.PointsActivationDetailResource detailResource = new PointsActivationConfirmResource.PointsActivationDetailResource();
        detailResource.setOpenId("openId");
        PointsActivationConfirmResource.PointsActivationConfirmBodyResource body = new PointsActivationConfirmResource.PointsActivationConfirmBodyResource();
        body.setResource(detailResource);
        PointsActivationConfirmResource resource = new PointsActivationConfirmResource(null, body, "");
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.memberPointsAuth(resource));
    }

    @Test
    void memberPointsAuthCase2() {
        PointsActivationConfirmResource.PointsActivationDetailResource detailResource = new PointsActivationConfirmResource.PointsActivationDetailResource();
        detailResource.setOpenId("openId");
        detailResource.setMchId("mchId");
        PointsActivationConfirmResource.PointsActivationConfirmBodyResource body = new PointsActivationConfirmResource.PointsActivationConfirmBodyResource();
        body.setResource(detailResource);
        PointsActivationConfirmResource resource = new PointsActivationConfirmResource(null, body, "");
        when(mallConfig.getByMchId(any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.memberPointsAuth(resource));

        when(mallConfig.getByMchId(any())).thenReturn(MallItem.builder().build());
        when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(any(), any(), any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.memberPointsAuth(resource));

        when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(any(), any(), any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("kipUserId").build());
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.memberPointsAuth(resource));
    }

    @Test
    void memberCardOpenEvent() {
        PointsActivationConfirmResource Resource = Mockito.mock(PointsActivationConfirmResource.class);
        when(tbInsensatePointsAuthRecordService.getByCardNoAndOrigin(any(), Mockito.anyInt())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.memberCardOpenEvent(Resource));
    }

    @Test
    void syncAliPay() {
        when(redisService.setSaleNoIfAbsent(any(), any())).thenReturn(false);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.syncAliPay());

        when(redisService.setSaleNoIfAbsent(any(), any())).thenReturn(true);
        Mockito.doThrow(new BizException(400, "")).when(tbInsensatePointsAuthRecordService).insertBatchFromCardMemberRelation();
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.syncAliPay());
    }

    @Test
    void syncWechatPoints() {
        SalesAutoPointsDto autoPointsDto = new SalesAutoPointsDto();
        when(tbInsensatePointsPushRecordService.findByTransactionIdAndEventTypeAndOrigin(any(), any(), Mockito.anyInt())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.syncWechatPoints(autoPointsDto));

        when(tbInsensatePointsPushRecordService.findByTransactionIdAndEventTypeAndOrigin(any(), any(), Mockito.anyInt()))
                .thenReturn(new InsensatePointsPushRecordDto());
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.syncWechatPoints(autoPointsDto));

        InsensatePointsPushRecordDto insensatePointsPushRecordDto = new InsensatePointsPushRecordDto();
        String transactionStr = "{\"mchId\":10247801,\"merchantName\": \"HKC\",\"shopName\": \"HKC0001\"," +
                "\"appId\":null,\"openId\":\"weChat001\",\"amount\":\"10\"," +
                "\"description\":\"微信支付订单号\"}";
        insensatePointsPushRecordDto.setTransactionInfo(transactionStr);
        when(tbInsensatePointsPushRecordService.findByTransactionIdAndEventTypeAndOrigin(any(), any(), Mockito.anyInt()))
                .thenReturn(insensatePointsPushRecordDto);
        autoPointsDto.setExchangePoints(100);
        when(bizCircleClientService.earnRewardPoints(any()))
                .thenThrow(new BizException(400 , "error"));
        when(redisService.setRetryCount(any())).thenReturn(10L);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.syncWechatPoints(autoPointsDto));

        when(redisService.setRetryCount(any())).thenReturn(3L);
        Assertions.assertThrows(Exception.class, () ->wechatAccelerateIntegralServiceImpl.syncWechatPoints(autoPointsDto));
    }

    @Test
    void queryMemberPointsCommitStatus() {
        LoginUser user = LoginUser.builder().build();
        when(mallConfig.getByMallId(any())).thenReturn(MallItem.builder().build());
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.queryMemberPointsCommitStatus(user));

        MallItem mallItem = new MallItem();
        mallItem.setWxBrandId(809);
        when(mallConfig.getByMallId(any())).thenReturn(mallItem);
        when(tbAutoPointsConfigService.findByLbsId(any())).thenReturn(TbAutoPointsConfig.builder().build());
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.queryMemberPointsCommitStatus(user));

        TbAutoPointsConfig tbAutoPointsConfig = new TbAutoPointsConfig();
        tbAutoPointsConfig.setCode("wxpay");
        when(tbAutoPointsConfigService.findByLbsId(any())).thenReturn(tbAutoPointsConfig);
        when(tbInsensatePointsAuthRecordService.getByKipUserIdAndMallIdAndOrigin(any(), any(), Mockito.anyInt())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.queryMemberPointsCommitStatus(user));

        TbInsensatePointsAuthRecord authRecord1 = new TbInsensatePointsAuthRecord();
        authRecord1.setOpenId("openId");
        when(tbInsensatePointsAuthRecordService.getByKipUserIdAndMallIdAndOrigin(any(), any(), Mockito.anyInt()))
                .thenReturn(authRecord1);

        QueryPointsCommitStatusOutputResource outputResource = Mockito.mock(QueryPointsCommitStatusOutputResource.class);
        when(bizCircleClientService.queryPointsCommitStatus(any())).thenReturn(outputResource);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.queryMemberPointsCommitStatus(user));

        when(bizCircleClientService.queryPointsCommitStatus(any())).thenReturn(outputResource);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.queryMemberPointsCommitStatus(user));

        TbInsensatePointsAuthRecord authRecord = TbInsensatePointsAuthRecord.builder()
                .createDate(DateUtil.parseDateTime("2025-05-20 00:00:00")).openId("Test0001").id(100L).status(0).build();
        when(tbInsensatePointsAuthRecordService.getByKipUserIdAndMallIdAndOrigin(any(), any(), Mockito.anyInt()))
                .thenReturn(authRecord);

        LoginUser loginUser = LoginUser.builder().brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022")
                .openId("Test0001").build();
        when(mallConfig.isHkc(any())).thenReturn(Boolean.FALSE);
        when(bizCircleClientService.queryPointsCommitStatus(any())).thenReturn(outputResource);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.queryMemberPointsCommitStatus(loginUser));

        when(bizCircleClientService.queryPointsCommitStatus(any()))
                .thenReturn(outputResource);
        when(mallConfig.isHkc(any())).thenReturn(Boolean.TRUE);
        when(tbInsensatePointsAuthRecordService.getByKipUserIdAndMallIdAndOrigin(any(), any(), Mockito.anyInt())).thenReturn(authRecord);
        Assertions.assertDoesNotThrow(() -> wechatAccelerateIntegralServiceImpl.queryMemberPointsCommitStatus(loginUser));

        when(bizCircleClientService.queryPointsCommitStatus(any())).thenThrow(BizException.error(400, "服务异常"));
        try{
            wechatAccelerateIntegralServiceImpl.queryMemberPointsCommitStatus(user);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
