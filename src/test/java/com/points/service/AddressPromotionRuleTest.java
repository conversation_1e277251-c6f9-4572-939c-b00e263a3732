package com.points.service;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.impl.AddressPromotionRule;
import com.kerryprops.kip.service.integral.service.promotion.impl.GenderPromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/30/2024 17:45
 **********************************************************************************************************************/
@Slf4j
class AddressPromotionRuleTest {

    @InjectMocks
    private AddressPromotionRule addressPromotionRule;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getRuleType() {
        Assertions.assertNotNull(addressPromotionRule.getRuleType());
    }

    @Test
    void checkRule() {
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().member(TbMemberAsset.builder().build()).build();
        Assertions.assertFalse(
                addressPromotionRule.checkRule(TbActivityPromotionCondition.builder().build(), dto, DateUtil.date(), null)
        );

        Assertions.assertFalse(
                addressPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("content").build(), dto, DateUtil.date(), null)
        );

        TbMemberAsset asset = TbMemberAsset.builder().provinceAddress("province").cityAddress("city").districtAddress("district").build();
        SalesAutoPointsDto dto1 = SalesAutoPointsDto.builder().member(asset).build();
        Assertions.assertFalse(
                addressPromotionRule.checkRule(TbActivityPromotionCondition.builder().promotionConditionContent("content").build(), dto1, DateUtil.date(), null)
        );
    }
}
