package com.points.listener;

import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.listener.MemberGradeUpgradeCheckListener;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.kerryprops.kip.service.integral.service.KerryStaffService;
import com.kerryprops.kip.service.integral.service.TbBaseShopService;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 01/21/2025 14:31
 **********************************************************************************************************************/

@Slf4j
public class MemberGradeUpgradeCheckListenerTest extends BaseTest {

    @Autowired
    private MemberGradeUpgradeCheckListener memberGradeUpgradeCheckListener;
    @MockBean
    private TbBaseShopService tbBaseShopService;
    @MockBean
    private KerryStaffService kerryStaffService;

    @Test
    void memberGradeUpgradeCheckAfterSales() {
        String json = "{\"saleId\":\"16605\"}";

        List<String> list = Arrays.asList("HKC00041002", "HKC00021024");
        when(tbBaseShopService.getContractNoList("4028e3817c2b3f79017c2b48c54c0000", "3,4,2")).thenReturn(list);

        List<String> list1 = Arrays.asList("HKC00041002","HKC00021025");
        when(tbBaseShopService.getContractNoList("4028e3817c2b3f79017c2b48c54c0000", "3,4,1")).thenReturn(list1);

        List<String> list2 = Collections.singletonList("HKC00041002");
        when(tbBaseShopService.getContractNoList("4028e3817c2b3f79017c2b48c54c0000", "4")).thenReturn(list2);

        List<String> list3 = Arrays.asList("HKC00041001","HKC00021031","HKC00041004","HKC00021028");
        when(tbBaseShopService.getContractNoList("4028e3817c2b3f79017c2b48c54c0000", "02")).thenReturn(list3);

        when(tbBaseShopService.getByContractNoAndMallId(any(), any())).thenReturn(TbBaseShop.builder().contractNo("HKC00041004").build());

        String userJson = "{\"id\":\"2c9d850c870bea4101870cfbc08f0000\",\"areaCode\":\"+86\",\"phoneNumber\":\"131*****625\",\"email\":\"<EMAIL>\",\"nickName\":\"B**t\",\"realName\":\"张*良\",\"avatar\":\"https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/tmp_116504886fbce44bfe15bd5a40ec0edc173bbd6d22ec7061.jpg\",\"gender\":1,\"birthDate\":\"1990-08-08\",\"certificateType\":1,\"certificateNum\":\"111*********333\",\"maritalStatus\":1,\"babyStatus\":1,\"provinceAddress\":\"上海市\",\"cityAddress\":\"上海\",\"districtAddress\":\"浦东新区\",\"address\":\"天目西路228号\",\"companyName\":\"嘉里建设\",\"job\":\"经理\",\"nation\":\"汉族\",\"homePhone\":\"021-89217654\",\"education\":null,\"profession\":null}";
        when(kerryStaffService.findByKipUserId(any())).thenReturn(JsonUtils.stringToObj(userJson, CustomerUserDto.class));
        Message message = new Message(json.getBytes());
        memberGradeUpgradeCheckListener.memberGradeUpgradeCheckAfterSales(message);
    }

}
