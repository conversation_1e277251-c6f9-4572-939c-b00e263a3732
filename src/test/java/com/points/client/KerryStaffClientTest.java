package com.points.client;

import com.kerryprops.kip.service.integral.client.KerryStaffClient;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/08/2022 11:46
 **********************************************************************************************************************/

@Slf4j
public class KerryStaffClientTest extends BaseTest {

    @MockBean
    private KerryStaffClient kerryStaffClient;

    @Test
    public void testClient() {
        when(kerryStaffClient.findByOpenId(any(String.class))).thenReturn(CustomerUserDto.builder().build());
        CustomerUserDto userDto = kerryStaffClient.findByOpenId("oudWQ5cmU4OUq7lgRq0x_9KEX5Hs");
        log.info("---- Feign Client Get Response ---" + userDto);
    }

}
