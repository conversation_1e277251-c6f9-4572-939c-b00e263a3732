package com.points;

import com.kerryprops.kip.service.integral.IntegralApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;


/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 08/29/2023 09:18
 **********************************************************************************************************************/

@Slf4j
@ExtendWith(SpringExtension.class)
@ActiveProfiles(value="test")
@SpringBootTest(classes = {IntegralApplication.class, BaseTest.class}
        , webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BaseTest {

    @BeforeAll
    public static void before() {
    }

}
