package com.points.webservice;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsAuthRecordService;
import com.kerryprops.kip.service.integral.service.WechatAccelerateIntegralService;
import com.kerryprops.kip.service.integral.webservice.AutoPointsController;
import com.kerryprops.kip.service.integral.webservice.resource.MemberAutoPointResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/14/2022 17:27
 **********************************************************************************************************************/

@Slf4j
class AutoPointsControllerTest {

    @InjectMocks
    AutoPointsController autoPointsController;
    @Mock
    private TbInsensatePointsAuthRecordService insensatePointsAuthRecordService;
    @Mock
    private WechatAccelerateIntegralService wechatAccelerateIntegralService;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private RedisService redisService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getMemberPointsAuth() {
        when(insensatePointsAuthRecordService.getAutoPointInfo(any(), any(), any()))
                .thenReturn(MemberAutoPointResource.builder().build());
        Assertions.assertNotNull(autoPointsController.getMemberPointsAuth(null, "", ""));
    }

    @Test
    void checkMemberWxAuthorizeOldStatus() {
        LoginUser loginUser = LoginUser.builder().cId("1111").build();

        assertDoesNotThrow(() -> autoPointsController.checkMemberWxAuthorizeOldStatus(loginUser, "111", "222"));
    }

    @Test
    void checkMemberWxAuthorizeOldStatus1() {
        LoginUser loginUser = LoginUser.builder().cId("1111").build();
        when(mallConfig.isHkc(any())).thenReturn(Boolean.TRUE);
        assertDoesNotThrow(() -> autoPointsController.checkMemberWxAuthorizeOldStatus(loginUser, "111", "222"));
    }

    @Test
    void checkMemberWxAuthorizeOldStatus2() {
        LoginUser loginUser = LoginUser.builder().cId("1111").build();
        when(mallConfig.isHkc(any())).thenReturn(Boolean.TRUE);
        TbInsensatePointsAuthRecord record = TbInsensatePointsAuthRecord.builder()
                .kipUserId("1111").mchid("0001").origin(0).status(0).groupId("222").mallId("111")
                .createDate(new Date()).openId("openId11111").build();
        when(insensatePointsAuthRecordService.checkHkcMemberAuthStatus(any(), any(), any(int.class)))
                .thenReturn(record);
        assertDoesNotThrow(() -> autoPointsController.checkMemberWxAuthorizeOldStatus(loginUser, "111", "222"));
    }

    @Test
    void checkMemberWxAuthorizeOldStatus3() {
        LoginUser loginUser = LoginUser.builder().cId("1111").build();
        when(mallConfig.isHkc(any())).thenReturn(Boolean.TRUE);
        Date hkcMigrationToKoDate = DateUtil.parseDateTime("2025-04-02 00:00:00");
        TbInsensatePointsAuthRecord record = TbInsensatePointsAuthRecord.builder()
                .kipUserId("1111").mchid("0001").origin(0).status(0).groupId("222").mallId("111")
                .createDate(hkcMigrationToKoDate).openId("openId11111").build();
        when(insensatePointsAuthRecordService.checkHkcMemberAuthStatus(any(), any(), any(int.class)))
                .thenReturn(record);
        assertDoesNotThrow(() -> autoPointsController.checkMemberWxAuthorizeOldStatus(loginUser, "111", "222"));
    }

    @Test
    void checkMemberWxAuthorizeOldStatus4() {
        LoginUser loginUser = LoginUser.builder().cId("1111").openId("Test00001").build();
        when(mallConfig.isHkc(any())).thenReturn(Boolean.TRUE);
        Date hkcMigrationToKoDate = DateUtil.parseDateTime("2025-03-02 00:00:00");
        TbInsensatePointsAuthRecord record = TbInsensatePointsAuthRecord.builder()
                .kipUserId("1111").mchid("0001").origin(0).status(0).groupId("222").mallId("111")
                .createDate(hkcMigrationToKoDate).openId("openId11111").build();
        when(insensatePointsAuthRecordService.checkHkcMemberAuthStatus(any(), any(), any(int.class)))
                .thenReturn(record);
        when(redisService.getValue(any())).thenReturn("1");
        when(mallConfig.getByMallId(any())).thenReturn(MallItem.builder().mallId("111").wxMchId("1111").build());
        assertDoesNotThrow(() -> autoPointsController.checkMemberWxAuthorizeOldStatus(loginUser, "111", "222"));
    }

    @Test
    void getWxPointsCommitStatus() {
        Assertions.assertNull(autoPointsController.getWxPointsCommitStatus(null));
    }

    @Test
    void checkAliAuthPoints() {
        assertDoesNotThrow(() -> autoPointsController.checkAliAuthPoints("", ""));
    }

}
