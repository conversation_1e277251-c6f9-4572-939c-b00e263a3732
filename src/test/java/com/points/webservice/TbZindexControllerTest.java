package com.points.webservice;

import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.CustomizePageTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.ProviderEnum;
import com.kerryprops.kip.service.integral.common.enums.ZIndexModuleSignEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.service.CaffeineCacheService;
import com.kerryprops.kip.service.integral.service.MemberRegisterService;
import com.kerryprops.kip.service.integral.service.ZIndexResourcesService;
import com.kerryprops.kip.service.integral.service.ZIndexService;
import com.kerryprops.kip.service.integral.webservice.ZIndexController;
import com.kerryprops.kip.service.integral.webservice.response.BadgeListResponse;
import com.kerryprops.kip.service.integral.webservice.response.SelfDefiningContextResource;
import com.kerryprops.kip.service.integral.webservice.response.SelfDefiningModuleResponse;
import com.kerryprops.kip.service.integral.webservice.response.ZIndexListResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 03/15/2024 10:51
 **********************************************************************************************************************/
@Slf4j
class TbZindexControllerTest {

    @InjectMocks
    private ZIndexController zIndexController;

    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private ZIndexResourcesService zIndexResourcesService;
    @Mock
    private CaffeineCacheService caffeineCacheService;
    @Mock
    private ZIndexService zIndexService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testHomePage() {
        LoginUser loginUser = LoginUser.builder().lbsId("12345").phoneNumber("17730226729").build();
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getParameter("provider")).thenReturn("weixin");

        List<SelfDefiningContextResource> list = Collections.singletonList(SelfDefiningContextResource.builder().build());
        when(zIndexResourcesService.getBrandLogoByMallId(Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(list));
        when(caffeineCacheService.asyncGetHomePage(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(new ZIndexListResponse()));
        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForHomePage("12345","12345", loginUser, request));

        ZIndexListResponse response = new ZIndexListResponse();
        String  moduleText = "[{\"id\":\"ad93e943593d431faf3c5daa837d22d0\",\"listId\":null,\"moduleSign\":\"title\",\"sort\":1,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":null,\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1805135504801583000,\"imageUrl\":\"/zindex/zindexpic/7c467a6d4c5f49009d66a0cc146d0b86.jpeg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":null}],\"suspensionList\":null,\"waterfallsFlowList\":null},{\"id\":\"d05bc873db62420b9b48b568c2902349\",\"listId\":null,\"moduleSign\":\"loop\",\"sort\":2,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":1,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"33333\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611186003065929700,\"imageUrl\":\"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":\"2025-01-15 00:00:00,2026-02-01 23:59:59\",\"showAliMini\":null,\"memberGrade\":null}],\"suspensionList\":null,\"waterfallsFlowList\":null},{\"id\":\"1762433691098042370\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"advertisement\",\"sort\":3,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691131596800,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691098042370\",\"ballName\":\"广告图\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183336117731300,\"imageUrl\":\"/zindex/zindexpic/2222db509a0d45bb9c5b4f7d6df5435e.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691131596800,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691098042370\",\"tabChName\":\"广告图\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/2222db509a0d45bb9c5b4f7d6df5435e.jpg\",\"contentLinkName\":\"广告图\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691194511362\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"advertisement\",\"sort\":4,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691211288600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691194511362\",\"ballName\":\"直播入口\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183508994359300,\"imageUrl\":\"/zindex/zindexpic/09d36951e71e471494712cc0291ccdf1.png\",\"linkType\":\"0\",\"insideType\":\"0\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/activity/liveList\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"直播大厅\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691211288600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691194511362\",\"tabChName\":\"直播入口\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/09d36951e71e471494712cc0291ccdf1.png\",\"contentLinkName\":\"直播入口\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691244843009\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"user\",\"sort\":5,\"brandLogoTitle\":\"\",\"brandLogoBg\":\"/zindex/zindex-default/brandLogoBg.jpg\",\"showAliMini\":1,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691261620200,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691244843009\",\"ballName\":\"zindex.jsRemark1\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":\"\",\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691274203100,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691244843009\",\"ballName\":\"zindex.jsRemark1\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":\"\",\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691261620200,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691244843009\",\"tabChName\":\"zindex.jsRemark1\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"contentLinkName\":\"zindex.jsRemark1\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691274203100,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691244843009\",\"tabChName\":\"zindex.jsRemark1\",\"tabEnName\":null,\"sort\":2,\"selectedUrl\":null,\"unselectedUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"contentLinkName\":\"zindex.jsRemark1\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691349700609\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"navigation\",\"sort\":6,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":1,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691362283500,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"ballName\":\"首页\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611184499319865300,\"imageUrl\":\"/zindex/zindexpic/5208988f79004e0d946e6a6591325050.223\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/automatic/automaticIndex\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":\"/zindex/zindexpic/f73a411a273641bb97f1a367656e82f2.113\",\"imageCheckId\":\"1611184741767413762\",\"fontSelectColor\":\"rgb(58, 130, 248)\",\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"首页\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"首页\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":\"rgb(58, 130, 248)\",\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691383255000,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"ballName\":\"我的会员码\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611184577220673500,\"imageUrl\":\"/zindex/zindexpic/7e41709eddb04e62bb0c56ec452fc288.222\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"myQrcode\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":\"/zindex/zindexpic/fbfadd5c89004835b42859d1350e4fb6.112\",\"imageCheckId\":\"1611184634372259841\",\"fontSelectColor\":null,\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"我的会员码\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"会员工具#我的会员码\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691400032300,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"ballName\":\"我的\",\"sort\":3,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611184791096623000,\"imageUrl\":\"/zindex/zindexpic/b0b881b2498a4551a6824e4d6e2748c4.221\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/automatic/automaticMy\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":\"/zindex/zindexpic/3103eee1888a43f7a737393aadbad469.111\",\"imageCheckId\":\"1611184691305742338\",\"fontSelectColor\":\"rgb(58, 130, 248)\",\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"我的\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"我的\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":\"rgb(58, 130, 248)\",\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691362283500,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"tabChName\":\"首页\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":\"/zindex/zindexpic/f73a411a273641bb97f1a367656e82f2.113\",\"unselectedUrl\":\"/zindex/zindexpic/5208988f79004e0d946e6a6591325050.223\",\"contentLinkName\":\"首页\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691383255000,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"tabChName\":\"我的会员码\",\"tabEnName\":null,\"sort\":2,\"selectedUrl\":\"/zindex/zindexpic/fbfadd5c89004835b42859d1350e4fb6.112\",\"unselectedUrl\":\"/zindex/zindexpic/7e41709eddb04e62bb0c56ec452fc288.222\",\"contentLinkName\":\"我的会员码\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691400032300,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"tabChName\":\"我的\",\"tabEnName\":null,\"sort\":3,\"selectedUrl\":\"/zindex/zindexpic/3103eee1888a43f7a737393aadbad469.111\",\"unselectedUrl\":\"/zindex/zindexpic/b0b881b2498a4551a6824e4d6e2748c4.221\",\"contentLinkName\":\"我的\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691462946817\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"ball\",\"sort\":7,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691483918300,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"ballName\":\"停车缴费首页\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183787722637300,\"imageUrl\":\"/zindex/zindexpic/51d4e97403904ba4a58661ade3eff30d.png\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/parkingFee/parkingFee\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"停车缴费首页\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"会员工具#停车缴费首页\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":1,\"memberGrade\":null},{\"id\":1762433691496501200,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"ballName\":\"自助积分\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183864067358700,\"imageUrl\":\"/zindex/zindexpic/e33556c448774e4980f678dd7044ce31.png\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/accumulatePoints/selfServicePoints\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"自助积分\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"会员工具#自助积分\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691513278500,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"ballName\":\"积分商城\",\"sort\":3,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183945671737300,\"imageUrl\":\"/zindex/zindexpic/0fa3ab45a8624efeb6a1956c9e337a28.png\",\"linkType\":\"0\",\"insideType\":\"0\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/pointsMall/pointsMall\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"积分商城\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691525861400,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"ballName\":\"报名活动\",\"sort\":4,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611184054602006500,\"imageUrl\":\"/zindex/zindexpic/f5fc62993352414090ce8583f7604e53.png\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/activity/activity\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"报名活动\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"会员工具#报名活动\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691483918300,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"tabChName\":\"停车缴费首页\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/51d4e97403904ba4a58661ade3eff30d.png\",\"contentLinkName\":\"停车缴费首页\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691496501200,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"tabChName\":\"自助积分\",\"tabEnName\":null,\"sort\":2,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/e33556c448774e4980f678dd7044ce31.png\",\"contentLinkName\":\"自助积分\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691513278500,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"tabChName\":\"积分商城\",\"tabEnName\":null,\"sort\":3,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/0fa3ab45a8624efeb6a1956c9e337a28.png\",\"contentLinkName\":\"积分商城\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691525861400,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"tabChName\":\"报名活动\",\"tabEnName\":null,\"sort\":4,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/f5fc62993352414090ce8583f7604e53.png\",\"contentLinkName\":\"报名活动\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691697827842\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"brandLogo\",\"sort\":8,\"brandLogoTitle\":\"\",\"brandLogoBg\":\"/images/default-img.png\",\"showAliMini\":1,\"multipleLineList\":[],\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"zindex.jsRemark1\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1450371719088406588,\"imageUrl\":\"/brandGuide/8006936f70bd486ba7b331bc178032b8.jpg\",\"linkType\":\"3\",\"insideType\":\"0\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"HKC00021026\",\"activityName\":\"12555\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":\"HKC00021026\",\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"tabChName\":\"zindex.jsRemark1\",\"tabEnName\":null,\"sort\":null,\"selectedUrl\":null,\"unselectedUrl\":\"/brandGuide/8006936f70bd486ba7b331bc178032b8.jpg\",\"contentLinkName\":\"zindex.jsRemark1\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691949486082\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"advertisement\",\"sort\":9,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":1,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691962069000,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691949486082\",\"ballName\":\"广告图HKC02\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611186003065929700,\"imageUrl\":\"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691962069000,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691949486082\",\"tabChName\":\"广告图HKC02\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg\",\"contentLinkName\":\"广告图HKC02\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691999817730\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"waterfall\",\"sort\":10,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433692012400600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691999817730\",\"ballName\":\"去去去去去去去\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"/ticket/ticketpic/62743e6cc6ac4cd9b7ae0028978679c0.jpg\",\"linkType\":\"0\",\"insideType\":\"2\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"../ticket/ticketDetailsNew?id=1697138788908564481&isSubscribe=0\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":\"1697138788908564481\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"去去去去去去去\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":1,\"memberGrade\":null},{\"id\":1762433692024983600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691999817730\",\"ballName\":\"商品名称\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433692012400600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691999817730\",\"tabChName\":\"去去去去去去去\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/ticket/ticketpic/62743e6cc6ac4cd9b7ae0028978679c0.jpg\",\"contentLinkName\":\"去去去去去去去\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433692024983600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691999817730\",\"tabChName\":\"商品名称\",\"tabEnName\":null,\"sort\":2,\"selectedUrl\":null,\"unselectedUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"contentLinkName\":\"商品名称\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"2f1310a9562c4d469e094f006e342bf6\",\"listId\":null,\"moduleSign\":\"screen\",\"sort\":11,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":1,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"888\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1805135504801583000,\"imageUrl\":\"/zindex/zindexpic/7c467a6d4c5f49009d66a0cc146d0b86.jpeg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":\"\",\"showAliMini\":null,\"memberGrade\":null}],\"suspensionList\":null,\"waterfallsFlowList\":null},{\"id\":\"d21e725a4a4a4bcd80f0ea4c0c86ead8\",\"listId\":null,\"moduleSign\":\"suspen\",\"sort\":12,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"悬浮图标\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1699965970679099400,\"imageUrl\":\"/zindex/zindexpic/40cd5cb024fc4c6c8ba49ef6abca60a2.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":[]}],\"suspensionList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"悬浮图标\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1699965970679099400,\"imageUrl\":\"/zindex/zindexpic/40cd5cb024fc4c6c8ba49ef6abca60a2.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":2,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":[]}],\"waterfallsFlowList\":null}]";
        List<SelfDefiningModuleResponse> modulesList = JsonUtils.stringToList(moduleText, SelfDefiningModuleResponse.class);
        response.setModuleList(modulesList);

        when(caffeineCacheService.asyncGetHomePage(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(response));

        when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForHomePage("12345","12345", loginUser, request));

        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForHomePage("12345","12345", null, request));

        when(request.getHeader(Mockito.any())).thenReturn("alipay");
        when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForHomePage("12345","12345", loginUser, request));

        when(zIndexResourcesService.getBrandLogoByMallId(Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));
        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForHomePage("12345","12345", loginUser, request));

        when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any())).thenThrow(new BizException(400, ""));
        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForHomePage("12345","12345", loginUser, request));
    }

    @Test
    void getZIndexForMyPage() {
        LoginUser loginUser = LoginUser.builder().build();
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getParameter("provider")).thenReturn("weixin");

        ZIndexListResponse response1 = new ZIndexListResponse();
        response1.setModuleList(new ArrayList<>());
        when(caffeineCacheService.asyncGetMyPage(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(response1));
        when(caffeineCacheService.asyncGetMyBadge(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(BadgeListResponse.builder().build()));
        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForMyPage(loginUser,"4028e3817c2b3f79017c2b48c54c0000","8aaa80b47c784020017c78b00d060022", request));

        ZIndexListResponse response = new ZIndexListResponse();
        String  moduleText = "[{\"id\":\"ad93e943593d431faf3c5daa837d22d0\",\"listId\":null,\"moduleSign\":\"title\",\"sort\":1,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":null,\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1805135504801583000,\"imageUrl\":\"/zindex/zindexpic/7c467a6d4c5f49009d66a0cc146d0b86.jpeg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":null}],\"suspensionList\":null,\"waterfallsFlowList\":null},{\"id\":\"d05bc873db62420b9b48b568c2902349\",\"listId\":null,\"moduleSign\":\"loop\",\"sort\":2,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":1,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"33333\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611186003065929700,\"imageUrl\":\"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":\"2025-01-15 00:00:00,2026-02-01 23:59:59\",\"showAliMini\":null,\"memberGrade\":null}],\"suspensionList\":null,\"waterfallsFlowList\":null},{\"id\":\"1762433691098042370\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"advertisement\",\"sort\":3,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691131596800,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691098042370\",\"ballName\":\"广告图\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183336117731300,\"imageUrl\":\"/zindex/zindexpic/2222db509a0d45bb9c5b4f7d6df5435e.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691131596800,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691098042370\",\"tabChName\":\"广告图\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/2222db509a0d45bb9c5b4f7d6df5435e.jpg\",\"contentLinkName\":\"广告图\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691194511362\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"advertisement\",\"sort\":4,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691211288600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691194511362\",\"ballName\":\"直播入口\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183508994359300,\"imageUrl\":\"/zindex/zindexpic/09d36951e71e471494712cc0291ccdf1.png\",\"linkType\":\"0\",\"insideType\":\"0\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/activity/liveList\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"直播大厅\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691211288600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691194511362\",\"tabChName\":\"直播入口\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/09d36951e71e471494712cc0291ccdf1.png\",\"contentLinkName\":\"直播入口\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691244843009\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"user\",\"sort\":5,\"brandLogoTitle\":\"\",\"brandLogoBg\":\"/zindex/zindex-default/brandLogoBg.jpg\",\"showAliMini\":1,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691261620200,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691244843009\",\"ballName\":\"zindex.jsRemark1\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":\"\",\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691274203100,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691244843009\",\"ballName\":\"zindex.jsRemark1\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":\"\",\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691261620200,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691244843009\",\"tabChName\":\"zindex.jsRemark1\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"contentLinkName\":\"zindex.jsRemark1\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691274203100,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691244843009\",\"tabChName\":\"zindex.jsRemark1\",\"tabEnName\":null,\"sort\":2,\"selectedUrl\":null,\"unselectedUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"contentLinkName\":\"zindex.jsRemark1\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691349700609\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"navigation\",\"sort\":6,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":1,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691362283500,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"ballName\":\"首页\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611184499319865300,\"imageUrl\":\"/zindex/zindexpic/5208988f79004e0d946e6a6591325050.223\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/automatic/automaticIndex\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":\"/zindex/zindexpic/f73a411a273641bb97f1a367656e82f2.113\",\"imageCheckId\":\"1611184741767413762\",\"fontSelectColor\":\"rgb(58, 130, 248)\",\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"首页\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"首页\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":\"rgb(58, 130, 248)\",\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691383255000,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"ballName\":\"我的会员码\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611184577220673500,\"imageUrl\":\"/zindex/zindexpic/7e41709eddb04e62bb0c56ec452fc288.222\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"myQrcode\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":\"/zindex/zindexpic/fbfadd5c89004835b42859d1350e4fb6.112\",\"imageCheckId\":\"1611184634372259841\",\"fontSelectColor\":null,\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"我的会员码\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"会员工具#我的会员码\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691400032300,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"ballName\":\"我的\",\"sort\":3,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611184791096623000,\"imageUrl\":\"/zindex/zindexpic/b0b881b2498a4551a6824e4d6e2748c4.221\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/automatic/automaticMy\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":\"/zindex/zindexpic/3103eee1888a43f7a737393aadbad469.111\",\"imageCheckId\":\"1611184691305742338\",\"fontSelectColor\":\"rgb(58, 130, 248)\",\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"我的\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"我的\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":\"rgb(58, 130, 248)\",\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691362283500,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"tabChName\":\"首页\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":\"/zindex/zindexpic/f73a411a273641bb97f1a367656e82f2.113\",\"unselectedUrl\":\"/zindex/zindexpic/5208988f79004e0d946e6a6591325050.223\",\"contentLinkName\":\"首页\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691383255000,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"tabChName\":\"我的会员码\",\"tabEnName\":null,\"sort\":2,\"selectedUrl\":\"/zindex/zindexpic/fbfadd5c89004835b42859d1350e4fb6.112\",\"unselectedUrl\":\"/zindex/zindexpic/7e41709eddb04e62bb0c56ec452fc288.222\",\"contentLinkName\":\"我的会员码\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691400032300,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691349700609\",\"tabChName\":\"我的\",\"tabEnName\":null,\"sort\":3,\"selectedUrl\":\"/zindex/zindexpic/3103eee1888a43f7a737393aadbad469.111\",\"unselectedUrl\":\"/zindex/zindexpic/b0b881b2498a4551a6824e4d6e2748c4.221\",\"contentLinkName\":\"我的\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691462946817\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"ball\",\"sort\":7,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691483918300,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"ballName\":\"停车缴费首页\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183787722637300,\"imageUrl\":\"/zindex/zindexpic/51d4e97403904ba4a58661ade3eff30d.png\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/parkingFee/parkingFee\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"停车缴费首页\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"会员工具#停车缴费首页\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":1,\"memberGrade\":null},{\"id\":1762433691496501200,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"ballName\":\"自助积分\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183864067358700,\"imageUrl\":\"/zindex/zindexpic/e33556c448774e4980f678dd7044ce31.png\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/accumulatePoints/selfServicePoints\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"自助积分\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"会员工具#自助积分\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691513278500,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"ballName\":\"积分商城\",\"sort\":3,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611183945671737300,\"imageUrl\":\"/zindex/zindexpic/0fa3ab45a8624efeb6a1956c9e337a28.png\",\"linkType\":\"0\",\"insideType\":\"0\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/pointsMall/pointsMall\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"积分商城\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":1762433691525861400,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"ballName\":\"报名活动\",\"sort\":4,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611184054602006500,\"imageUrl\":\"/zindex/zindexpic/f5fc62993352414090ce8583f7604e53.png\",\"linkType\":\"0\",\"insideType\":\"3\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"/pages/activity/activity\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":\"\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"报名活动\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":0,\"titleInput\":false,\"twolevelLinkage\":\"会员工具#报名活动\",\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691483918300,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"tabChName\":\"停车缴费首页\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/51d4e97403904ba4a58661ade3eff30d.png\",\"contentLinkName\":\"停车缴费首页\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691496501200,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"tabChName\":\"自助积分\",\"tabEnName\":null,\"sort\":2,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/e33556c448774e4980f678dd7044ce31.png\",\"contentLinkName\":\"自助积分\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691513278500,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"tabChName\":\"积分商城\",\"tabEnName\":null,\"sort\":3,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/0fa3ab45a8624efeb6a1956c9e337a28.png\",\"contentLinkName\":\"积分商城\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433691525861400,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691462946817\",\"tabChName\":\"报名活动\",\"tabEnName\":null,\"sort\":4,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/f5fc62993352414090ce8583f7604e53.png\",\"contentLinkName\":\"报名活动\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691697827842\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"brandLogo\",\"sort\":8,\"brandLogoTitle\":\"\",\"brandLogoBg\":\"/images/default-img.png\",\"showAliMini\":1,\"multipleLineList\":[],\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"zindex.jsRemark1\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1450371719088406588,\"imageUrl\":\"/brandGuide/8006936f70bd486ba7b331bc178032b8.jpg\",\"linkType\":\"3\",\"insideType\":\"0\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"HKC00021026\",\"activityName\":\"12555\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":\"HKC00021026\",\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"tabChName\":\"zindex.jsRemark1\",\"tabEnName\":null,\"sort\":null,\"selectedUrl\":null,\"unselectedUrl\":\"/brandGuide/8006936f70bd486ba7b331bc178032b8.jpg\",\"contentLinkName\":\"zindex.jsRemark1\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691949486082\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"advertisement\",\"sort\":9,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":1,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433691962069000,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691949486082\",\"ballName\":\"广告图HKC02\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611186003065929700,\"imageUrl\":\"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433691962069000,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691949486082\",\"tabChName\":\"广告图HKC02\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg\",\"contentLinkName\":\"广告图HKC02\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"1762433691999817730\",\"listId\":\"1603209107648757761\",\"moduleSign\":\"waterfall\",\"sort\":10,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":[],\"imgList\":[{\"id\":1762433692012400600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691999817730\",\"ballName\":\"去去去去去去去\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"/ticket/ticketpic/62743e6cc6ac4cd9b7ae0028978679c0.jpg\",\"linkType\":\"0\",\"insideType\":\"2\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"../ticket/ticketDetailsNew?id=1697138788908564481&isSubscribe=0\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":\"1697138788908564481\",\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"去去去去去去去\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":1,\"memberGrade\":null},{\"id\":1762433692024983600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691999817730\",\"ballName\":\"商品名称\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":43,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":[],\"waterfallsFlowList\":[{\"id\":1762433692012400600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691999817730\",\"tabChName\":\"去去去去去去去\",\"tabEnName\":null,\"sort\":1,\"selectedUrl\":null,\"unselectedUrl\":\"/ticket/ticketpic/62743e6cc6ac4cd9b7ae0028978679c0.jpg\",\"contentLinkName\":\"去去去去去去去\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null},{\"id\":1762433692024983600,\"groupId\":null,\"mallId\":null,\"moduleId\":\"1762433691999817730\",\"tabChName\":\"商品名称\",\"tabEnName\":null,\"sort\":2,\"selectedUrl\":null,\"unselectedUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"contentLinkName\":\"商品名称\",\"contentLinkType\":null,\"showNum\":null,\"moreUrl\":null}]},{\"id\":\"2f1310a9562c4d469e094f006e342bf6\",\"listId\":null,\"moduleSign\":\"screen\",\"sort\":11,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":1,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"888\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1805135504801583000,\"imageUrl\":\"/zindex/zindexpic/7c467a6d4c5f49009d66a0cc146d0b86.jpeg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":\"\",\"showAliMini\":null,\"memberGrade\":null}],\"suspensionList\":null,\"waterfallsFlowList\":null},{\"id\":\"d21e725a4a4a4bcd80f0ea4c0c86ead8\",\"listId\":null,\"moduleSign\":\"suspen\",\"sort\":12,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"悬浮图标\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1699965970679099400,\"imageUrl\":\"/zindex/zindexpic/40cd5cb024fc4c6c8ba49ef6abca60a2.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":[]}],\"suspensionList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"悬浮图标\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1699965970679099400,\"imageUrl\":\"/zindex/zindexpic/40cd5cb024fc4c6c8ba49ef6abca60a2.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":2,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":[]}],\"waterfallsFlowList\":null}]";
        List<SelfDefiningModuleResponse> modulesList = JsonUtils.stringToList(moduleText, SelfDefiningModuleResponse.class);
        response.setModuleList(modulesList);

        when(caffeineCacheService.asyncGetMyPage(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(response));
        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForMyPage(loginUser,"4028e3817c2b3f79017c2b48c54c0000","8aaa80b47c784020017c78b00d060022", request));

        when(request.getHeader(Mockito.any())).thenReturn("alipay");
        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForMyPage(loginUser,"4028e3817c2b3f79017c2b48c54c0000","8aaa80b47c784020017c78b00d060022", request));

        when(caffeineCacheService.asyncGetMyPage(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(null));
        Assertions.assertDoesNotThrow(() -> zIndexController.getZIndexForMyPage(loginUser,"4028e3817c2b3f79017c2b48c54c0000","8aaa80b47c784020017c78b00d060022", request));
    }

    @Test
    void getCustomerPage() {
        LoginUser loginUser = LoginUser.builder().build();
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getParameter("provider")).thenReturn("weixin");
        ZIndexListResponse response = new ZIndexListResponse();
        String  moduleText = "[{\"id\":\"ba39fa5233164d009703e7b6db8a139b\",\"listId\":null,\"moduleSign\":\"ball\",\"sort\":1,\"brandLogoTitle\":null,\"brandLogoBg\":null,\"showAliMini\":0,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"功能球\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1699965970679099400,\"imageUrl\":\"/zindex/zindexpic/40cd5cb024fc4c6c8ba49ef6abca60a2.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"功能球\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1611186003065929700,\"imageUrl\":\"/zindex/zindexpic/13f1ca0d9a6e416b86b9f992957408ec.jpg\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"功能球\",\"sort\":3,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null},{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"功能球\",\"sort\":4,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":null,\"imageUrl\":\"https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/images/default-img.png\",\"linkType\":\"2\",\"insideType\":\"\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"\",\"activityName\":\"\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":null,\"shopno\":null,\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":0,\"memberGrade\":null}],\"suspensionList\":null,\"waterfallsFlowList\":null},{\"id\":\"a1faefa7af24444b992b183709fd19e3\",\"listId\":null,\"moduleSign\":\"brandLogo\",\"sort\":2,\"brandLogoTitle\":\"\",\"brandLogoBg\":\"/images/default-img.png\",\"showAliMini\":1,\"multipleLineList\":null,\"imgList\":[{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"zindex.jsRemark1\",\"sort\":1,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1450371719088406588,\"imageUrl\":\"/brandGuide/8006936f70bd486ba7b331bc178032b8.jpg\",\"linkType\":\"3\",\"insideType\":\"0\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"HKC00021026\",\"activityName\":\"12555\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":\"HKC00021026\",\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":null},{\"id\":null,\"groupId\":null,\"mallId\":null,\"moduleId\":null,\"ballName\":\"zindex.jsRemark1\",\"sort\":2,\"appFlag\":null,\"appUrl\":null,\"htmlUrl\":null,\"title\":null,\"folderId\":null,\"imageId\":1450371719088406614,\"imageUrl\":\"https://kip-public-dev.oss-cn-shanghai.aliyuncs.com/601926bbceec4abdb6bc701d6fe7132d_1.jpg\",\"linkType\":\"3\",\"insideType\":\"0\",\"outsideType\":\"\",\"outsideAppid\":\"\",\"outsideUrlName\":\"\",\"outsideUrl\":\"\",\"insideUrl\":\"\",\"openFlag\":null,\"beginTime\":null,\"endTime\":null,\"createDate\":null,\"creator\":null,\"updateDate\":null,\"updater\":null,\"deptId\":null,\"imageCheckUrl\":null,\"imageCheckId\":null,\"fontSelectColor\":null,\"tpId\":null,\"salePrice\":null,\"marketPrice\":null,\"shopName\":null,\"versions\":null,\"insideId\":\"HKC00021027\",\"activityName\":\"test\",\"activityParams\":null,\"pageUrl\":null,\"activityType\":null,\"titleInput\":false,\"twolevelLinkage\":null,\"circleAuthorize\":0,\"memberAuthorize\":0,\"showImgFlag\":0,\"shopno\":\"HKC00021027\",\"resourcesType\":0,\"convertible\":null,\"fontUnSelectColor\":null,\"showTime\":null,\"showAliMini\":null,\"memberGrade\":null}],\"suspensionList\":null,\"waterfallsFlowList\":null}]";
        List<SelfDefiningModuleResponse> modulesList = JsonUtils.stringToList(moduleText, SelfDefiningModuleResponse.class);
        response.setModuleList(modulesList);
        response.setType("2");
        response.setOpenFlag("1");

        List<SelfDefiningContextResource> resourcesList = new ArrayList<>();
        SelfDefiningContextResource resource = SelfDefiningContextResource.builder().build();
        SelfDefiningContextResource.builder().id("").groupId("groupId").mallId("mallId").sort(4)
                .imageUrl("imageUrl").imageId("").linkType("3").insideUrl("/pages/brand/brandDetails?shopNo=")
                .insideId("inside").shopno("hkc001").activityName("activity").showImgFlag(0).circleAuthorize(0).memberAuthorize(0).build();
        resourcesList.add(resource);

        when(zIndexService.getPageById(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(response);
        when(zIndexResourcesService.getBrandLogoByMallId(Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(resourcesList));
        Assertions.assertDoesNotThrow(() -> zIndexController.getCustomerPage("4028e3817c2b3f79017c2b48c54c0000","8aaa80b47c784020017c78b00d060022", "1", loginUser, request));

        when(request.getHeader(Mockito.any())).thenReturn("alipay");
        Assertions.assertDoesNotThrow(() -> zIndexController.getCustomerPage("4028e3817c2b3f79017c2b48c54c0000","8aaa80b47c784020017c78b00d060022", "1", loginUser, request));

        when(zIndexService.getPageById(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(new ZIndexListResponse());
        Assertions.assertNull(zIndexController.getCustomerPage("4028e3817c2b3f79017c2b48c54c0000","8aaa80b47c784020017c78b00d060022", "1", loginUser, request));
    }

    @Test
    void getCustomerPageCase1() {
        // 阿里覆盖fixedAliMiniShow
        LoginUser loginUser = LoginUser.builder().build();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Mockito.when(request.getHeader(Mockito.any())).thenReturn(ProviderEnum.ALI.getVal());

        when(zIndexResourcesService.getBrandLogoByMallId(Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));
        List<SelfDefiningModuleResponse> moduleList = new ArrayList<>();
        SelfDefiningModuleResponse response1 = SelfDefiningModuleResponse.builder()
                .moduleSign(ZIndexModuleSignEnum.ADVERTISEMENT.getVal()).imgList(null).build();
        SelfDefiningModuleResponse response2 = SelfDefiningModuleResponse.builder()
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().build())).moduleSign("12345").build();
        SelfDefiningModuleResponse response3 = SelfDefiningModuleResponse.builder()
                .moduleSign(ZIndexModuleSignEnum.ADVERTISEMENT.getVal())
                .imgList(Arrays.asList(SelfDefiningContextResource.builder().build(),
                        SelfDefiningContextResource.builder().showTime("2023-12-12#2023-12-11,12,12#2023-12-12 00:00:00,2323-12-12 00:00:00").build()))
                .build();
        SelfDefiningModuleResponse response4 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.BALL.getVal())
                .build();
        SelfDefiningModuleResponse response5 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.BALL.getVal())
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        SelfDefiningModuleResponse response6 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.MY.getVal())
                .showAliMini(0)
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .multipleLineList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        SelfDefiningModuleResponse response7 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.NAVIGATION.getVal())
                .showAliMini(1)
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .multipleLineList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .suspensionList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        moduleList.add(response1);
        moduleList.add(response2);
        moduleList.add(response3);
        moduleList.add(response4);
        moduleList.add(response5);
        moduleList.add(response6);
        moduleList.add(response7);
        ZIndexListResponse response = new ZIndexListResponse();
        response.setModuleList(moduleList);
        response.setFloatingBtnShow(1);
        response.setType("2");
        response.setOpenFlag("1");
        when(zIndexService.getPageById(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Assertions.assertNotNull(zIndexController.getCustomerPage("","", "1", loginUser, request));
    }

    @Test
    void getCustomerPageCase2() {
        // 阿里覆盖fixedAliMiniShow
        LoginUser loginUser = LoginUser.builder().build();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Mockito.when(request.getHeader(Mockito.any())).thenReturn(ProviderEnum.WX.getVal());

        when(zIndexResourcesService.getBrandLogoByMallId(Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));
        List<SelfDefiningModuleResponse> moduleList = new ArrayList<>();
        SelfDefiningModuleResponse response1 = SelfDefiningModuleResponse.builder()
                .moduleSign(ZIndexModuleSignEnum.ADVERTISEMENT.getVal()).imgList(null).build();
        SelfDefiningModuleResponse response2 = SelfDefiningModuleResponse.builder()
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().build())).moduleSign("12345").build();
        SelfDefiningModuleResponse response3 = SelfDefiningModuleResponse.builder()
                .moduleSign(ZIndexModuleSignEnum.ADVERTISEMENT.getVal())
                .imgList(Arrays.asList(SelfDefiningContextResource.builder().build(),
                        SelfDefiningContextResource.builder().showTime("2023-12-12#2023-12-11,12,12#2023-12-12 00:00:00,2323-12-12 00:00:00").build()))
                .build();
        SelfDefiningModuleResponse response4 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.BALL.getVal())
                .build();
        SelfDefiningModuleResponse response5 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.BALL.getVal())
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        SelfDefiningModuleResponse response6 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.MY.getVal())
                .showAliMini(0)
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .multipleLineList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        SelfDefiningModuleResponse response7 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.NAVIGATION.getVal())
                .showAliMini(1)
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .multipleLineList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .suspensionList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        moduleList.add(response1);
        moduleList.add(response2);
        moduleList.add(response3);
        moduleList.add(response4);
        moduleList.add(response5);
        moduleList.add(response6);
        moduleList.add(response7);
        ZIndexListResponse response = new ZIndexListResponse();
        response.setModuleList(moduleList);
        response.setFloatingBtnShow(0);
        response.setType("2");
        response.setOpenFlag("1");
        when(zIndexService.getPageById(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Assertions.assertNotNull(zIndexController.getCustomerPage("","", "1", loginUser, request));

    }

    @Test
    void getCustomerPageCase3() {
        // 进异常
        LoginUser loginUser = LoginUser.builder().build();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Mockito.when(request.getHeader(Mockito.any())).thenReturn(ProviderEnum.WX.getVal());

        when(zIndexResourcesService.getBrandLogoByMallId(Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));
        List<SelfDefiningModuleResponse> moduleList = new ArrayList<>();
        SelfDefiningModuleResponse response1 = SelfDefiningModuleResponse.builder()
                .moduleSign(ZIndexModuleSignEnum.ADVERTISEMENT.getVal()).imgList(null).build();
        SelfDefiningModuleResponse response2 = SelfDefiningModuleResponse.builder()
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().build())).moduleSign("12345").build();
        SelfDefiningModuleResponse response3 = SelfDefiningModuleResponse.builder()
                .moduleSign(ZIndexModuleSignEnum.ADVERTISEMENT.getVal())
                .imgList(Arrays.asList(SelfDefiningContextResource.builder().build(),
                        SelfDefiningContextResource.builder().showTime("2023-12-12#2023-12-11,12,12#2023-12-12 00:00:00,2323-12-12 00:00:00").build()))
                .build();
        moduleList.add(response1);
        moduleList.add(response2);
        moduleList.add(response3);
        ZIndexListResponse response = new ZIndexListResponse();
        response.setModuleList(moduleList);
        response.setFloatingBtnShow(0);
        when(zIndexService.getPageById(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);
        Assertions.assertNotNull(zIndexController.getCustomerPage("","", "1", loginUser, request));

    }

    @Test
    void getCustomerPageCase4() {
        // 覆盖fixedFloatingBallLogic grade不为空 非myPage
        LoginUser loginUser = LoginUser.builder().build();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Mockito.when(request.getHeader(Mockito.any())).thenReturn(ProviderEnum.WX.getVal());

        when(zIndexResourcesService.getBrandLogoByMallId(Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

        when(zIndexService.getPageById(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ZIndexListResponse());
        Assertions.assertNull(zIndexController.getCustomerPage("","", "1", loginUser, request));

        List<SelfDefiningModuleResponse> moduleList = new ArrayList<>();
        SelfDefiningModuleResponse response4 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.SUSPEN.getVal())
                .build();
        SelfDefiningModuleResponse response5 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.SUSPEN.getVal())
                .suspensionList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        SelfDefiningModuleResponse response6 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.MY.getVal())
                .showAliMini(0)
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .multipleLineList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        SelfDefiningModuleResponse response7 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.BRANDLOGO.getVal())
                .showAliMini(1)
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .multipleLineList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        moduleList.add(response4);
        moduleList.add(response5);
        moduleList.add(response6);
        moduleList.add(response7);
        ZIndexListResponse response = new ZIndexListResponse();
        response.setModuleList(moduleList);
        response.setFloatingBtnShow(0);
        when(zIndexService.getPageById(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);

        when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any())).thenReturn(TbMemberAsset.builder().grade("01").build());
        Assertions.assertNotNull(zIndexController.getCustomerPage("","", "1", loginUser, request));

    }

    @Test
    void getCustomerPageCase5() {
        // 覆盖fixedFloatingBallLogic grade不为空 myPage
        LoginUser loginUser = LoginUser.builder().build();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Mockito.when(request.getHeader(Mockito.any())).thenReturn(ProviderEnum.WX.getVal());

        when(zIndexResourcesService.getBrandLogoByMallId(Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build())));

        List<SelfDefiningModuleResponse> moduleList = new ArrayList<>();
        SelfDefiningModuleResponse response6 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.MY.getVal())
                .showAliMini(0)
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .multipleLineList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        SelfDefiningModuleResponse response7 = SelfDefiningModuleResponse
                .builder()
                .moduleSign(ZIndexModuleSignEnum.BRANDLOGO.getVal())
                .showAliMini(1)
                .imgList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .multipleLineList(Collections.singletonList(SelfDefiningContextResource.builder().showAliMini(IntegralConstant.SHOW_ALI_MINI).build()))
                .build();
        moduleList.add(response6);
        moduleList.add(response7);
        ZIndexListResponse response = new ZIndexListResponse();
        response.setModuleList(moduleList);
        response.setFloatingBtnShow(0);
        response.setType(CustomizePageTypeEnum.MY_PAGE.getValue());
        when(zIndexService.getPageById(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response);

        when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any())).thenReturn(TbMemberAsset.builder().grade("01").build());
        Assertions.assertNotNull(zIndexController.getCustomerPage("","", "1", loginUser, request));
    }


}
