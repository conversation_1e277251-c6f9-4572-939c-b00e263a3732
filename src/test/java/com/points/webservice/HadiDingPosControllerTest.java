package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.enums.RefundStatusEnum;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.kerryprops.kip.service.integral.model.dto.PointsRedemptionDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.HadiDingPosController;
import com.kerryprops.kip.service.integral.webservice.resource.HaiDingPosParamResource;
import com.kerryprops.kip.service.integral.webservice.resource.HaiDingPosSaleResource;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberForPosResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/14/2022 17:27
 **********************************************************************************************************************/

@Slf4j
class HadiDingPosControllerTest {

    @InjectMocks
    private HadiDingPosController hadiDingPosController;
    @Mock
    private TbSetshoprateService tbSetshoprateService;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private Mapper mapper;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private TbMemberPointsChangeService tbMemberPointsChangeService;
    @Mock
    private KerryStaffService kerryStaffService;
    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private TbInsensatePointsPushRecordService tbInsensatePointsPushRecordService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private TbPaymentPushRecordService tbPaymentPushRecordService;
    @Mock
    private TbPointsDetailService tbPointsDetailService;
    @Mock
    private RedisService redisService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetMemberForPos(){
        Assertions.assertThrows(PointBusinessException.class, () ->
                hadiDingPosController.getMemberForPos("","","","",""));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertThrows(PointBusinessException.class, () ->
                hadiDingPosController.getMemberForPos("","","KERRY190200303","","SKC0001"));

        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any()))
                .thenReturn(CustomerUserDto.builder().build());
        Mockito.when(mapper.map(TbMemberAsset.builder().amountOfPoints(0).build(), TbMemberForPosResponse.class))
                .thenReturn(TbMemberForPosResponse.builder().build());
        Assertions.assertNotNull(hadiDingPosController.getMemberForPos("","","KERRY190200303","","SKC0001"));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("12345").build());
        Mockito.when(tbSetshoprateService.getMaxAmountOfStoreCashOut(Mockito.any()))
                .thenReturn(PointsRedemptionDto.builder().amount(100).build());
        Mockito.when(kerryStaffService.findByKipUserId(Mockito.any()))
                .thenReturn(CustomerUserDto.builder().build());
        Mockito.when(mapper.map(TbMemberAsset.builder().amountOfPoints(100).kipUserId("12345").build(), TbMemberForPosResponse.class))
                .thenReturn(TbMemberForPosResponse.builder().build());
        Assertions.assertNotNull(hadiDingPosController.getMemberForPos("","","KERRY190200303","","SKC0001"));
    }

    @Test
    void redeemPoints(){
        HaiDingPosParamResource resource = HaiDingPosParamResource
                .builder()
                .groupId("groupId")
                .vipcode("vipcode")
                .shopNo("shopNo")
                .mallId("mallId")
                .docNo("1203030404")
                .amount("1300")
                .amountOfPoints(0)
                .build();
        Mockito.when(tbPointsDetailService.queryAdjustList(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(TbPointsDetail.builder().build());
        Assertions.assertThrows(PointBusinessException.class, () ->
                hadiDingPosController.redeemPoints(resource));

        Mockito.when(tbPointsDetailService.queryAdjustList(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(null);
        Assertions.assertThrows(PointBusinessException.class, () ->
                hadiDingPosController.redeemPoints(resource));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertThrows(PointBusinessException.class, () ->
                hadiDingPosController.redeemPoints(resource));

        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().build());
        Assertions.assertNotNull(hadiDingPosController.redeemPoints(resource));

        HaiDingPosParamResource resource1 = HaiDingPosParamResource
                .builder()
                .groupId("groupId")
                .vipcode("vipcode")
                .shopNo("shopNo")
                .mallId("mallId")
                .docNo("1203030404")
                .amount("1300")
                .amountOfPoints(100)
                .build();
        Assertions.assertThrows(PointBusinessException.class, () ->
                hadiDingPosController.redeemPoints(resource1));

        Mockito.when(tbSetshoprateService.getMaxAmountOfStoreCashOut(Mockito.any()))
                .thenReturn(PointsRedemptionDto.builder().amount(200).build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any()))
                .thenReturn(false);
        Assertions.assertThrows(PointBusinessException.class, () ->
                hadiDingPosController.redeemPoints(resource1));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        Mockito.when(tbSetshoprateService.salesToIntegral(Mockito.any()))
                .thenReturn(100);
        Assertions.assertNotNull(hadiDingPosController.redeemPoints(resource1));

        Mockito.when(tbSetshoprateService.salesToIntegral(Mockito.any()))
                .thenThrow(PointBusinessException.class);
        Assertions.assertNotNull(hadiDingPosController.redeemPoints(resource1));
    }

    @Test
    @DisplayName("退款")
    void posSaleRecord(){
        HaiDingPosSaleResource resource = HaiDingPosSaleResource
                .builder()
                .groupId("groupId")
                .vipcode("vipcode")
                .shopNo("shopNo")
                .mallId("mallId")
                .docNo("1203030404")
                .saleDate("2023-01-01 12:00:00")
                .discounts("0")
                .optType(IntegralConstant.REFUND)
                .build();
        Mockito.when(tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any()))
                .thenReturn(false);
        Assertions.assertThrows(BizException.class, () ->
                hadiDingPosController.posSaleRecord(resource));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        Assertions.assertNotNull(hadiDingPosController.posSaleRecord(resource));

        Mockito.when(tbSalesDetailService.selectBySellNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbSalesDetail.builder().status(RefundStatusEnum.REFUND.getValue()).build());
        Assertions.assertNotNull(hadiDingPosController.posSaleRecord(resource));

        Mockito.when(tbSalesDetailService.selectBySellNoAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbSalesDetail.builder().status(RefundStatusEnum.PAY.getValue()).payAmount(BigDecimal.TEN).build());
        Assertions.assertNotNull(hadiDingPosController.posSaleRecord(resource));

        Mockito.when(memberSalePointsProcessService.refundSalePointsProcess(Mockito.any()))
                .thenReturn(0);
        Assertions.assertNotNull(hadiDingPosController.posSaleRecord(resource));

        Mockito.when(memberSalePointsProcessService.refundSalePointsProcess(Mockito.any()))
                .thenReturn(100);
        Assertions.assertNotNull(hadiDingPosController.posSaleRecord(resource));
    }

    @Test
    @DisplayName("支付")
    void posSaleRecordCase1(){
        HaiDingPosSaleResource resource = HaiDingPosSaleResource
                .builder()
                .groupId("groupId")
                .vipcode("vipcode")
                .shopNo("shopNo")
                .mallId("mallId")
                .docNo("1203030404")
                .saleDate("2023-01-01 12:00:00")
                .discounts("0")
                .amountOfPoints("0")
                .optType(IntegralConstant.PAYMENT)
                .build();
        Mockito.when(tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any()))
                .thenReturn(false);
        Assertions.assertThrows(BizException.class, () ->
                hadiDingPosController.posSaleRecord(resource));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any()))
                .thenReturn(100);
        Assertions.assertNotNull(hadiDingPosController.posSaleRecord(resource));

        HaiDingPosSaleResource resource1 = HaiDingPosSaleResource
                .builder()
                .groupId("groupId")
                .vipcode("vipcode")
                .shopNo("shopNo")
                .mallId("mallId")
                .docNo("1203030404")
                .saleDate("2023-01-01 12:00:00")
                .discounts("0")
                .amountOfPoints("100")
                .optType(IntegralConstant.PAYMENT)
                .build();
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any()))
                .thenThrow(BizException.class);
        Assertions.assertThrows(BizException.class, () ->
                hadiDingPosController.posSaleRecord(resource1));
    }

}
