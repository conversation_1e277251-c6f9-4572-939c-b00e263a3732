package com.points.webservice;

import cn.hutool.core.date.DateUtil;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.TakePhotoAuditDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbPhotoPointsController;
import com.kerryprops.kip.service.integral.webservice.resource.BonusSelfUploadResource;
import com.kerryprops.kip.service.integral.webservice.resource.TakePhotoAuditResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.PageRequest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 03/15/2024 10:51
 **********************************************************************************************************************/
@Slf4j
class TbPhotoPointsControllerTest {

    @InjectMocks
    private TbPhotoPointsController tbPhotoPointsController;

    @Mock
    private TbPhotoReviewService tbPhotoReviewService;
    @Mock
    private CrmVipcodeService crmVipcodeService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private RedisService redisService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private TbPointsDetailService tbPointsDetailService;
    @Mock
    private ApplicationEventPublisher applicationEventPublisher;
    @Mock
    private Mapper mapper;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private SmallTicketRecognitionService smallTicketRecognitionService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getMemberIntegralAdjustRecord() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").lbsId("8aaa80b47c784020017c78b00d060022").phoneNumber("17730226729").build();
        PageRequest page = PageRequest.of(1, 101);

        when(hiveVasService.getMallByLbsId(any())).thenReturn(null);
        Assertions.assertNotNull(tbPhotoPointsController.getMemberIntegralAdjustRecord(loginUser, page, PhotoReviewStatusEnum.TYPE_1.getCode()));

        when(hiveVasService.getMallByLbsId(any())).thenReturn(MallItem.builder().mallId("12345").build());
        when(tbPhotoReviewService.getBonusTotal(any())).thenReturn(0);
        Assertions.assertNotNull(tbPhotoPointsController.getMemberIntegralAdjustRecord(loginUser, page, PhotoReviewStatusEnum.TYPE_1.getCode()));


        PageRequest page1 = PageRequest.of(1, 10);
        when(tbPhotoReviewService.getBonusTotal(any())).thenReturn(10);
        when(tbPhotoReviewService.geTbBonusSelfList(any()))
                .thenReturn(Arrays.asList(TbPhotoReview.builder()
                                .mallId("12345")
                                .updateDate(DateUtil.date())
                                .status(PhotoReviewStatusEnum.TYPE_1.getCode())
                                .imageUrl("kerryprops.com")
                                .integralAdjustId("123").build(),
                        TbPhotoReview.builder()
                                .mallId("12345")
                                .updateDate(DateUtil.date())
                                .status(PhotoReviewStatusEnum.TYPE_1.getCode())
                                .imageUrl("kerryprops.com")
                                .integralAdjustId("456").build(),
                        TbPhotoReview.builder().mallId("56789")
                                .updateDate(DateUtil.date())
                                .status(PhotoReviewStatusEnum.TYPE_2.getCode())
                                .integralAdjustId("1256").build(),
                        TbPhotoReview.builder().mallId("12345")
                                .updateDate(DateUtil.date())
                                .status(PhotoReviewStatusEnum.TYPE_4.getCode())
                                .integralAdjustId("1256").build()));
        when(hiveVasService.getMallName(any())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        Map<String, TbPointsDetail> detailMap = new HashMap<>();
        detailMap.put("123", TbPointsDetail.builder().mallId("12345").amount(100).reasonDesc("Reason").build());
        detailMap.put("456", TbPointsDetail.builder().mallId("12345").amount(200).reasonDesc("").build());
        when(tbPointsDetailService.findByIds(any())).thenReturn(CompletableFuture.completedFuture(detailMap));
        Assertions.assertNotNull(tbPhotoPointsController.getMemberIntegralAdjustRecord(loginUser, page1, "1"));

        when(tbPhotoReviewService.getBonusTotal(any())).thenReturn(11);
        Assertions.assertNotNull(tbPhotoPointsController.getMemberIntegralAdjustRecord(loginUser, page1, "1"));

    }

    @Test
    void bonusSelfUpload() {
        LoginUser loginUser = LoginUser.builder()
                .cId("2c9d85bc8489e0ba01849e4629760003")
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("17730226729")
                .build();
        String key = "crm:photo:access:limit:8aaa80b47c784020017c78b00d060022:2c9d85bc8489e0ba01849e4629760003";
        Mockito.when(redisService.setIfAbsentWithExpire(key, "2c9d85bc8489e0ba01849e4629760003", 1L, TimeUnit.SECONDS))
                .thenReturn(null);
        Assertions.assertThrows(Exception.class, () ->
                tbPhotoPointsController.bonusSelfUpload(loginUser, BonusSelfUploadResource.builder().imgUrl("").build()));

        Mockito.when(redisService.setIfAbsentWithExpire(key, "2c9d85bc8489e0ba01849e4629760003", 1L, TimeUnit.SECONDS))
                .thenReturn(false);
        Assertions.assertThrows(Exception.class, () ->
                tbPhotoPointsController.bonusSelfUpload(loginUser, BonusSelfUploadResource.builder().imgUrl("").build()));

        Mockito.when(redisService.setIfAbsentWithExpire(key, "2c9d85bc8489e0ba01849e4629760003", 1L, TimeUnit.SECONDS))
                .thenReturn(true);
        Mockito.when(hiveVasService.getMallByLbsId(any())).thenReturn(null);
        Assertions.assertThrows(Exception.class, () ->
                tbPhotoPointsController.bonusSelfUpload(loginUser, BonusSelfUploadResource.builder().imgUrl("").build()));

        Mockito.when(hiveVasService.getMallByLbsId(any()))
                .thenReturn(MallItem.builder().groupId("123").build());
        Mockito.when(tbMemberAssetService.findByKipUserIdAndGroupIdWithException(any(), any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(true);
        Assertions.assertDoesNotThrow(() -> tbPhotoPointsController.bonusSelfUpload(loginUser, BonusSelfUploadResource.builder().imgUrl("").build()));

        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(false);
        Assertions.assertDoesNotThrow(() -> tbPhotoPointsController.bonusSelfUpload(loginUser, BonusSelfUploadResource.builder().imgUrl("").build()));

    }

    @Test
    void photoAudit() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003")
                .nickName("Test").lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("17730226729").build();

        TakePhotoAuditResource resource = TakePhotoAuditResource.builder()
                .id("3")
                .state("3")
                .mallId("8aaa80b47c784020017c78b00d060022").build();
        when(mapper.map(resource, TakePhotoAuditDto.class)).thenReturn(TakePhotoAuditDto.builder().build());
        when(tbPhotoReviewService.auditRecord(TakePhotoAuditDto.builder().build())).thenReturn(100);
        Assertions.assertDoesNotThrow(() -> tbPhotoPointsController.photoAudit(loginUser, resource));

        LoginUser loginUser1 = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("17730226729").build();
        Assertions.assertDoesNotThrow(() -> tbPhotoPointsController.photoAudit(loginUser1, resource));
    }
}
