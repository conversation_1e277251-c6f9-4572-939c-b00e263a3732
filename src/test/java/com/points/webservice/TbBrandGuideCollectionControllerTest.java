package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.BrandGuideQueryDto;
import com.kerryprops.kip.service.integral.model.dto.BrandGuideRespDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbBrandGuideCollectionController;
import com.kerryprops.kip.service.integral.webservice.resource.BrandGuideQueryResource;
import com.kerryprops.kip.service.integral.webservice.resource.BrandGuideSaveOrCancelResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 **********************************************************************************************************************/

@Slf4j
class TbBrandGuideCollectionControllerTest {

    @InjectMocks
    private TbBrandGuideCollectionController tbBrandGuideCollectionController;
    @Mock
    private TbBrandGuideCollectionService tbBrandGuideCollectionService;
    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private HiveVasClient hiveVasClient;
    @Mock
    private RedisService redisService;
    @Mock
    private Mapper mapper;
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void saveBrandGuide() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        BrandGuideSaveOrCancelResource resource = BrandGuideSaveOrCancelResource.builder().brandGuideId("12345").build();
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(),Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Assertions.assertThrows(BizException.class, () -> tbBrandGuideCollectionController.saveBrandGuide(loginUser, resource));

        Mockito.when(hiveVasClient.getBrandGuideDetail(Mockito.any())).thenReturn(Collections.singletonList(BrandGuideRespDto.builder().build()));
        Mockito.when(tbBrandGuideCollectionService.getBrandGuideKey(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("");
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionController.saveBrandGuide(loginUser, resource));
    }

    @Test
    void cancelBrandGuide() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        BrandGuideSaveOrCancelResource resource = BrandGuideSaveOrCancelResource.builder().brandGuideId("12345").build();
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(),Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionController.cancelBrandGuide(loginUser, resource));
    }

    @Test
    void getBrandGuideDetail() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(),Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionController.getBrandGuideDetail(loginUser, "111"));
    }

    @Test
    void brandGuideList() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        BrandGuideQueryResource resource = BrandGuideQueryResource.builder().collectFlag(true).build();
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(),Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Mockito.when(tbBrandGuideCollectionService.getBrandGuideList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.when(mapper.map(BrandGuideQueryResource.builder().collectFlag(true).build(), BrandGuideQueryDto.class))
                .thenReturn(BrandGuideQueryDto.builder().build());
        Mockito.when(hiveVasClient.getBrandGuideList(Mockito.any())).thenReturn(Collections.singletonList(BrandGuideRespDto.builder().build()));
        Assertions.assertDoesNotThrow(() ->
                tbBrandGuideCollectionController.brandGuideList(loginUser, "", "", resource));

        Mockito.when(tbBrandGuideCollectionService.getBrandGuideList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(Collections.singletonList("12345"));
        Assertions.assertDoesNotThrow(() ->
                tbBrandGuideCollectionController.brandGuideList(loginUser, "4028e3817c2b3f79017c2b48c54c0000", "8aaa80b47c784020017c78b00d060022", resource));
    }

    @Test
    void getMyCollection() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d850c870bea4101870cfbc08f0000").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionController.getMyCollection(loginUser));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(),Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionController.getMyCollection(loginUser));

        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionController.getMyCollection(loginUser));

        Mockito.when(tbBrandGuideCollectionService.getBrandGuideFromDb(Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(Collections.singletonList("123"));
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionController.getMyCollection(loginUser));

        Mockito.when(hiveVasClient.getBrandGuideList(Mockito.any()))
                .thenReturn(Arrays.asList(BrandGuideRespDto.builder().id("123").build(), BrandGuideRespDto.builder().id("345").build()));
        Assertions.assertDoesNotThrow(() -> tbBrandGuideCollectionController.getMyCollection(loginUser));
    }

}
