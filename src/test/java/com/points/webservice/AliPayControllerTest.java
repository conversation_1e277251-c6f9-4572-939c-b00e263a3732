package com.points.webservice;

import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.AliPayConfig;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayBizContentDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayItem;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.AliPayController;
import com.kerryprops.kip.service.integral.webservice.resource.CrmMemberOpenCardResource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@Slf4j
class AliPayControllerTest {

    @InjectMocks
    private AliPayController aliPayController;

    @Mock
    private MallConfig mallConfig;

    @Mock
    private AliPayConfig aliPayConfig;

    @Mock
    private AliPayService aliPayService;

    @Mock
    private RabbitMqService rabbitMqService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void config() {
        Mockito.when(aliPayConfig.getByName(Mockito.any())).thenReturn(new AliPayItem());
        assertNotNull(aliPayController.config(Mockito.any()));
    }

    @Test
    void alipayAuthCallback() throws IOException {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayService.authCallback(Mockito.any())).thenReturn("1");
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        Assertions.assertDoesNotThrow(() -> aliPayController.alipayAuthCallback(request, response, "0003", "900120231227001",
                "90001", "8a8884e77cc9e70a017cca1011970001",
                "ali009", "10200303", "8a8883557cca9463017ccb002b360001"));
    }

    @Test
    void crmMemberOpenCard() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayService.memberAuth(Mockito.any())).thenReturn("");
        Assertions.assertNotNull(aliPayController.crmMemberOpenCard(CrmMemberOpenCardResource.builder().build()));
    }

    @Test
    void getSmartPointsCallback() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayService.smartPointsCallback(Mockito.any())).thenReturn("");
        Assertions.assertNotNull(aliPayController.getSmartPointsCallback(CrmMemberOpenCardResource.builder().build()));
    }

    @Test
    void alipayCallback() throws IOException {
        AliPayBizContentDto dto = AliPayBizContentDto.builder().build();
        Map<String, String> map = new HashMap<>();
        map.put("biz_content", JsonUtils.objToString(dto));
        Mockito.when(mallConfig.getByAliMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayService.callback(Mockito.any())).thenReturn("");
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        when(response.getWriter()).thenReturn(new PrintWriter("200"));
        Assertions.assertDoesNotThrow(() -> aliPayController.alipayCallback(map, request, response));
    }

    @Test
    void sendAliPaySms() {
        Assertions.assertDoesNotThrow(() -> aliPayController.sendAliPaySms(Mockito.any(), Mockito.any(), Mockito.any()));
    }

}
