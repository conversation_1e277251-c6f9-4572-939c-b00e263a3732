package com.points.webservice;

import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import com.kerryprops.kip.service.integral.model.dto.CustomerIdentityDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbMemberIdentityController;
import com.kerryprops.kip.service.integral.webservice.resource.MemberIdentityQueryResource;
import com.kerryprops.kip.service.integral.webservice.resource.MemberIdentityResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Nancy
 **********************************************************************************************************************/

@Slf4j
class TbMemberIdentityControllerTest {

    @InjectMocks
    private TbMemberIdentityController tbMemberIdentityController;

    @Mock
    private ProfileServiceClient profileServiceClient;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("分页查询长度为0")
    void kipSyncMemberIdentity() {
        MemberIdentityResource resource = MemberIdentityResource.builder().build();
        Assertions.assertDoesNotThrow(() -> tbMemberIdentityController.kipSyncMemberIdentity(resource));
    }

    @Test
    @DisplayName("getMemberIdentityInfo-会员冻结")
    void getMemberIdentityInfo() {
        MemberIdentityQueryResource resource = MemberIdentityQueryResource.builder().build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().status("0").build());
        Assertions.assertThrows(PointBusinessException.class, () -> tbMemberIdentityController.getMemberIdentityInfo(resource));
    }

    @Test
    @DisplayName("getMemberIdentityInfo-商场为空")
    void getMemberIdentityInfoCase1() {
        MemberIdentityQueryResource resource = MemberIdentityQueryResource.builder().build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().status("1").build());
        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbMemberIdentityController.getMemberIdentityInfo(resource));
    }

    @Test
    @DisplayName("getMemberIdentityInfo-身份认证为空")
    void getMemberIdentityInfoCase2() {
        MemberIdentityQueryResource resource = MemberIdentityQueryResource.builder().build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().status("1").build());
        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(profileServiceClient.getIdentityResponse(Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbMemberIdentityController.getMemberIdentityInfo(resource));
    }

    @Test
    @DisplayName("getMemberIdentityInfo-走完")
    void getMemberIdentityInfoCase3() {
        MemberIdentityQueryResource resource = MemberIdentityQueryResource.builder().build();
        Mockito.when(tbMemberAssetService.findByVipcodeAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().status("1").build());
        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(profileServiceClient.getIdentityResponse(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(CustomerIdentityDto.builder().build()));
        Assertions.assertDoesNotThrow(() -> tbMemberIdentityController.getMemberIdentityInfo(resource));
    }
}
