package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbAutoSaleMemberTag;
import com.kerryprops.kip.service.integral.entity.TbTagMemberNologic;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbMemberTagController;
import com.kerryprops.kip.service.integral.webservice.resource.TbAutoSaleMemberTagResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Nancy
 **********************************************************************************************************************/

@Slf4j
class TbMemberTagControllerTest {

    @InjectMocks
    private TbMemberTagController tbMemberTagController;

    @Mock
    private TbAutoSaleMemberTagService tbAutoSaleMemberTagService;
    @Mock
    private CrmVipcodeService crmVipcodeService;
    @Mock
    private Mapper mapper;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("getSecondMemberTagList")
    void getSecondMemberTagList() {
        Mockito.when(tbAutoSaleMemberTagService.getBySecSortIdAndType("2" ,2))
                .thenReturn(Collections.singletonList(TbAutoSaleMemberTag.builder().build()));
        Mockito.when(mapper.map(TbAutoSaleMemberTag.builder().build(), TbAutoSaleMemberTagResponse.class))
                .thenReturn(TbAutoSaleMemberTagResponse.builder().build());
        Assertions.assertNotNull(tbMemberTagController.getSecondMemberTagList(""));
    }

    @Test
    @DisplayName("getSecondMemberTagList-tagIds为空")
    void getMemberTagBound() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        Mockito.when(crmVipcodeService.getVipcode(Mockito.any(), Mockito.any()))
                .thenReturn("KERRY90199931");
        Mockito.when(tbAutoSaleMemberTagService.getByGroupIdAndVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(TbTagMemberNologic.builder().tagIds("").build());
        Assertions.assertNotNull(tbMemberTagController.getMemberTagBound(loginUser));
    }

    @Test
    @DisplayName("getSecondMemberTagList-tagList为空")
    void getMemberTagBoundCase1() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        Mockito.when(crmVipcodeService.getVipcode(Mockito.any(), Mockito.any()))
                .thenReturn("KERRY90199931");
        Mockito.when(tbAutoSaleMemberTagService.getByGroupIdAndVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(TbTagMemberNologic.builder().tagIds("1234").build());
        Mockito.when(tbAutoSaleMemberTagService.getByIds(Mockito.any())).thenReturn(null);
        Assertions.assertNotNull(tbMemberTagController.getMemberTagBound(loginUser));
    }

    @Test
    @DisplayName("getSecondMemberTagList-走完")
    void getMemberTagBoundCase2() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        Mockito.when(crmVipcodeService.getVipcode(Mockito.any(), Mockito.any()))
                .thenReturn("KERRY90199931");
        Mockito.when(tbAutoSaleMemberTagService.getByGroupIdAndVipcode(Mockito.any(), Mockito.any()))
                .thenReturn(TbTagMemberNologic.builder().tagIds("1234").build());
        Mockito.when(tbAutoSaleMemberTagService.getByIds(Mockito.any()))
                .thenReturn(Collections.singletonList(TbAutoSaleMemberTag.builder().secSortId(1L).build()));
        Mockito.when(mapper.map(TbAutoSaleMemberTag.builder().secSortId(1L).build(), TbAutoSaleMemberTagResponse.class))
                .thenReturn(TbAutoSaleMemberTagResponse.builder().build());
        Assertions.assertNotNull(tbMemberTagController.getMemberTagBound(loginUser));
    }


}
