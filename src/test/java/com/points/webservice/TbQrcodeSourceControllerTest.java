package com.points.webservice;

import com.kerryprops.kip.service.integral.webservice.TbQrcodeSourceController;
import com.kerryprops.kip.service.integral.webservice.resource.MaterialQrcodeResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Nancy
 **********************************************************************************************************************/

@Slf4j
class TbQrcodeSourceControllerTest {

    @InjectMocks
    private TbQrcodeSourceController tbQrcodeSourceController;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getMiniCodeParam() {
        MaterialQrcodeResource resource =  MaterialQrcodeResource.builder().scene("AAA9B28387CA25D4F01C0A18961061C0").build();
        Assertions.assertNotNull(tbQrcodeSourceController.getMiniCodeParam(resource));
    }

    @Test
    void getMiniCodeParamCase1() {
        MaterialQrcodeResource resource =  MaterialQrcodeResource.builder().scene("12345").build();
        Assertions.assertNotNull(tbQrcodeSourceController.getMiniCodeParam(resource));
    }
}
