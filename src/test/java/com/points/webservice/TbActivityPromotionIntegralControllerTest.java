package com.points.webservice;

import com.kerryprops.kip.service.integral.webservice.TbActivityPromotionIntegralController;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Map;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 12/23/2024 14:06
 **********************************************************************************************************************/

@Slf4j
class TbActivityPromotionIntegralControllerTest extends BaseTest {

    @Autowired
    private TbActivityPromotionIntegralController tbActivityPromotionIntegralController;

    @Test
    void getMatchedPromotions() {
        Map<String, String> map = tbActivityPromotionIntegralController.getMatchedPromotions(Arrays.asList("48ead58ac95740d2999978e6960f6f56", "e4d8dccd3bd9492694f5dcebd2f774f0"));
        Assertions.assertTrue(!map.isEmpty());
    }


}
