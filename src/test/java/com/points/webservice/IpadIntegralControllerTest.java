package com.points.webservice;

import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.IpadIntegralController;
import com.kerryprops.kip.service.integral.webservice.resource.BatchCalculatePointsResource;
import com.kerryprops.kip.service.integral.webservice.resource.IpadIntegralResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Collections;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@Slf4j
class IpadIntegralControllerTest {

    @InjectMocks
    private IpadIntegralController ipadIntegralController;

    @Mock
    private TbMemberAssetService tbMemberAssetService;

    @Mock
    private TbSalesDetailService tbSalesDetailService;

    @Mock
    private SysDictService sysDictService;

    @Mock
    private TbBaseShopService tbBaseShopService;

    @Mock
    private TbSetshoprateService tbSetshoprateService;

    @Mock
    private TbActivityPromotionService tbActivityPromotionService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test

    void calculateIntegralCase1() {
        IpadIntegralResource resource = IpadIntegralResource.builder().discounts(BigDecimal.ZERO).remark(IntegralConstant.ORDER_REFUND).useMoney(BigDecimal.ONE).build();
        Assertions.assertDoesNotThrow(() -> ipadIntegralController.integralRecordList(resource));
        IpadIntegralResource resource1 = IpadIntegralResource.builder().discounts(BigDecimal.ZERO).id("12345")
                .remark("XS0025")
                .useMoney(BigDecimal.ONE).build();
        Mockito.when(tbMemberAssetService.getMemberAllInfoById(Mockito.any())).thenReturn(TbMemberAsset.builder().build());
        Mockito.when(sysDictService.findByDictType(Mockito.any())).thenReturn(SysDict.builder().build());
        Assertions.assertThrows(BizNotFoundException.class, () -> ipadIntegralController.integralRecordList(resource1));
    }

    @Test
    void calculateIntegral() {
        IpadIntegralResource resource = IpadIntegralResource.builder().discounts(BigDecimal.ZERO).useMoney(BigDecimal.ONE).remark("").build();
        when(tbMemberAssetService.findAllByDto(SingleMemberQueryDto.builder().build())).thenReturn(TbMemberAsset.builder().status("1").build());
        when(sysDictService.findByDictType(Mockito.any())).thenReturn(SysDict.builder().build());
        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(TbBaseShop.builder().build());
        Mockito.when(tbSetshoprateService.salesToIntegral(Mockito.any())).thenReturn(1);
        Mockito.when(tbActivityPromotionService.handlePromotionActivity(SalesAutoPointsDto.builder().discountAmount(BigDecimal.ZERO.toString()).amount(BigDecimal.ZERO.toString()).build(), Boolean.FALSE)).thenReturn(0);
        Assertions.assertDoesNotThrow(() -> ipadIntegralController.integralRecordList(resource));
    }

}
