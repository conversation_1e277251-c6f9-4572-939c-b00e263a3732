package com.points.webservice;

import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.service.TbMemberRefineService;
import com.kerryprops.kip.service.integral.webservice.TbMemberRefineController;
import com.kerryprops.kip.service.integral.webservice.resource.MemberProfileCheckResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> Nancy
 * Created Date - 09/14/2022 17:27
 **********************************************************************************************************************/

@Slf4j
class TbMemberRefineControllerTest {

    @InjectMocks
    TbMemberRefineController tbMemberRefineController;

    @Mock
    private TbMemberRefineService tbMemberRefineService;
    @Mock
    private RabbitMqService rabbitMqService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findConfigByMallId() {
        Assertions.assertNull(tbMemberRefineController.findConfigByMallId(LoginUser.builder().build()));

        Mockito.when(tbMemberRefineService.getByMallId(Mockito.any())).thenReturn(null);
        Assertions.assertNull(tbMemberRefineController.findConfigByMallId(LoginUser.builder().lbsId("12345").build()));
    }

    @Test
    void judgeMemberInfo() {
        Assertions.assertDoesNotThrow(() -> tbMemberRefineController.judgeMemberInfo(MemberProfileCheckResource.builder().brandId("123").lbsId("123").kipUserId("123").build()));
    }

}
