package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PointsRedemptionEnum;
import com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig;
import com.kerryprops.kip.service.integral.service.TbOnlineShopConfigService;
import com.kerryprops.kip.service.integral.webservice.TbOnlineShopConfigController;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopConfigResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopSaveOrUpdateResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 10/31/2024 09:35
 **********************************************************************************************************************/

@Slf4j
class TbOnlineShopConfigControllerTest {

    @InjectMocks
    private TbOnlineShopConfigController tbOnlineShopConfigController;
    
    @Mock
    private TbOnlineShopConfigService tbOnlineShopConfigService;
    @Mock
    private Mapper mapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void onlineShopPage() {
        String groupId = "4028e3817c2b3f79017c2b48c54c0000";
        String mallIds = "8aaa80b47c784020017c78b00d060022";
        int page = 1;
        int size = 10;
        Mockito.when(tbOnlineShopConfigService.onlineShopTotal(Mockito.any())).thenReturn(0);
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.onlineShopPage(groupId, "", page, size));

        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.onlineShopPage(groupId, mallIds, page, size));

        Mockito.when(tbOnlineShopConfigService.onlineShopTotal(Mockito.any())).thenReturn(10);
        Mockito.when(tbOnlineShopConfigService.onlineShopPage(Mockito.any()))
                .thenReturn(Collections.emptyList());
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.onlineShopPage(groupId, mallIds, page, size));

        Mockito.when(tbOnlineShopConfigService.onlineShopPage(Mockito.any()))
                .thenReturn(Collections.singletonList(TbOnlineShopConfig.builder().build()));
        Mockito.when(mapper.map(TbOnlineShopConfig.builder().build(), TbOnlineShopConfigResponse.class))
                .thenReturn(TbOnlineShopConfigResponse.builder().businessType(PointsRedemptionEnum.POS_CASH_OUT.getCode()).build());
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.onlineShopPage(groupId, mallIds, page, size));
    }

    @Test
    void integralOutPage() {
        String groupId = "4028e3817c2b3f79017c2b48c54c0000";
        String mallIds = "8aaa80b47c784020017c78b00d060022";
        int page = 1;
        int size = 10;
        Mockito.when(tbOnlineShopConfigService.onlineShopTotal(Mockito.any())).thenReturn(0);
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.integralOutPage(groupId, "", page, size));

        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.integralOutPage(groupId, mallIds, page, size));

        Mockito.when(tbOnlineShopConfigService.onlineShopTotal(Mockito.any())).thenReturn(10);
        Mockito.when(tbOnlineShopConfigService.onlineShopPage(Mockito.any()))
                .thenReturn(Collections.singletonList(TbOnlineShopConfig.builder().build()));
        Mockito.when(mapper.map(TbOnlineShopConfig.builder().build(), TbOnlineShopConfigResponse.class))
                .thenReturn(TbOnlineShopConfigResponse.builder().build());
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.integralOutPage(groupId, mallIds, page, size));
    }

    @Test
    void saveOrUpdateOnlineShopConfig() {
        LoginUser loginUser = LoginUser
                .builder()
                .cId("2c9d85bc8489e0ba01849e4629760003")
                .nickName("Test").lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("17730226729").build();
        List<TbOnlineShopSaveOrUpdateResource> resource = Collections.singletonList(TbOnlineShopSaveOrUpdateResource.builder().build());
        Mockito.when(mapper.map(TbOnlineShopSaveOrUpdateResource.builder().build(), TbOnlineShopConfig.class))
                .thenReturn(TbOnlineShopConfig.builder().build());
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.saveOrUpdateOnlineShopConfig(loginUser, resource));
    }

    @Test
    void removeOnlineShopConfig() {
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.removeOnlineShopConfig(new Long[]{1L, 2L}));
    }

    @Test
    void getOnlineShop() {
        String mallId = "8aaa80b47c784020017c78b00d060022";
        String businessType = PointsRedemptionEnum.POS_CASH_OUT.getCode();
        Assertions.assertDoesNotThrow(() -> tbOnlineShopConfigController.getOnlineShop(mallId, businessType));
    }
}
