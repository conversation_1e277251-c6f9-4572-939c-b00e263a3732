package com.points.webservice;

import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbBkcSaleMember;
import com.kerryprops.kip.service.integral.service.TbBkcSaleMemberService;
import com.kerryprops.kip.service.integral.webservice.TbBkcSaleMemberController;
import com.kerryprops.kip.service.integral.webservice.resource.BkcMigrationConfirmResource;
import com.points.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 **********************************************************************************************************************/

@Slf4j
class TbBkcSaleMemberControllerTest extends BaseTest {

    @Autowired
    private TbBkcSaleMemberController tbBkcSaleMemberController;
    @MockBean
    private TbBkcSaleMemberService tbBkcSaleMemberService;

    @Test
    void bkcMigrationConfirmStatus() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        BkcMigrationConfirmResource resource = BkcMigrationConfirmResource.builder().confirmStatus(0).build();
        Assertions.assertNotNull(tbBkcSaleMemberController.bkcMigrationConfirmStatus(loginUser, resource));
    }

    @Test
    void bkcMigrationConfirmStatusCase1() {
        LoginUser loginUser = LoginUser.builder().cId("2c9d85bc8489e0ba01849e4629760003").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        BkcMigrationConfirmResource resource = BkcMigrationConfirmResource.builder().confirmStatus(1).build();
        Assertions.assertNotNull(tbBkcSaleMemberController.bkcMigrationConfirmStatus(loginUser, resource));
    }

    @Test
    void insertBkcCaseMember() {
        TbBkcSaleMember tbBkcSaleMember = TbBkcSaleMember.builder().id(1L).groupId("8a84853b7c91ac5b017c962dab55030e").confirmStatus(1).build();
        Assertions.assertNotNull(tbBkcSaleMemberController.insertBkcCaseMember(tbBkcSaleMember));
    }

}
