package com.points.webservice;

import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.service.CaffeineCacheService;
import com.kerryprops.kip.service.integral.service.MemberRegisterService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.webservice.TbMemberGradeController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 03/15/2024 10:51
 **********************************************************************************************************************/

@Slf4j
class TbMemberGradeControllerTest{

    @InjectMocks
    private TbMemberGradeController tbMemberGradeController;
    @Mock
    private CaffeineCacheService caffeineCacheService;
    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getMemberGradeList() {
        when(caffeineCacheService.getGradeList(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").upGradationStatus(0).build()));
        Assertions.assertDoesNotThrow(() -> tbMemberGradeController.getMemberGradeList(null, "4028e3817c2b3f79017c2b48c54c0000"));

        when(caffeineCacheService.getGradeList(Mockito.any()))
                .thenReturn(Arrays.asList(TbMemberGrade.builder().code("02").upGradationStatus(0).build(),
                        TbMemberGrade.builder().code("01").upGradationStatus(1).build()));
        Assertions.assertDoesNotThrow(() -> tbMemberGradeController.getMemberGradeList(LoginUser.builder().phoneNumber("16691027090").cId("2c9d85bc8489e0ba01849e4629760003")
                .lbsId("8aaa80b47c784020017c78b00d060022").build(), "4028e3817c2b3f79017c2b48c54c0000"));

        LoginUser user = LoginUser.builder().phoneNumber("16691027090").cId("2c9d85bc8489e0ba01849e4629760003")
                .brandId("8aaa80b47c784020017c78b00d060022").lbsId("8aaa80b47c784020017c78b00d060022").build();
        Assertions.assertThrows(BizException.class, () -> tbMemberGradeController.getMemberGradeList(user, "4028e3817c2b3f79017c2b48c54c0000"));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().grade("01").build());
        Assertions.assertDoesNotThrow(() -> tbMemberGradeController.getMemberGradeList(user, "4028e3817c2b3f79017c2b48c54c0000"));

    }

    @Test
    void getCurrentGrade() {
        LoginUser loginUser = LoginUser.builder().phoneNumber("16691027090").brandId("4028e3817c2b3f79017c2b48c54c0000").lbsId("8aaa80b47c784020017c78b00d060022").build();
        Assertions.assertDoesNotThrow(() -> tbMemberGradeController.getCurrentGrade(loginUser));

        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(tbMemberGradeService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").build()));
        Assertions.assertDoesNotThrow(() -> tbMemberGradeController.getCurrentGrade(loginUser));
    }

    @Test
    void getGradeListByGroupId() {
        Assertions.assertDoesNotThrow(() -> tbMemberGradeController.getGradeListByGroupId("4028e3817c2b3f79017c2b48c54c0000"));

        Mockito.when(caffeineCacheService.getGradeList(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("02").upGradationStatus(0).build()));
        Assertions.assertDoesNotThrow(() -> tbMemberGradeController.getGradeListByGroupId("4028e3817c2b3f79017c2b48c54c0000"));
    }

}
