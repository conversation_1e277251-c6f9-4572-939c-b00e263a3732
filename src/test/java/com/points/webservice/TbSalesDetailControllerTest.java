package com.points.webservice;

import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.webservice.TbQrcodeSourceController;
import com.kerryprops.kip.service.integral.webservice.TbSalesDetailController;
import com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class TbSalesDetailControllerTest {

    @InjectMocks
    private TbSalesDetailController tbSalesDetailController;
    @Mock
    private TbSalesDetailService tbSalesDetailService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void accumulatedSalesAmount() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailController.accumulatedSalesAmount(resource));
    }

    @Test
    void accumulatedSalesAmount1() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Collections.emptyList()).mallIds(Collections.emptyList())
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        BizException exception = assertThrows(BizException.class, () -> tbSalesDetailController.accumulatedSalesAmount(resource));
        assertEquals(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR.getCode(), exception.getCode());
    }

    @Test
    void consumeShopCount() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailController.consumeShopCount(resource));
    }

    @Test
    void consumeShopCount1() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Collections.emptyList()).mallIds(Collections.emptyList())
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        BizException exception = assertThrows(BizException.class, () -> tbSalesDetailController.accumulatedSalesAmount(resource));
        assertEquals(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR.getCode(), exception.getCode());
    }

    @Test
    void consumeFrequency() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailController.consumeFrequency(resource));
    }

    @Test
    void consumeFrequency1() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Collections.emptyList()).mallIds(Collections.emptyList())
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        BizException exception = assertThrows(BizException.class, () -> tbSalesDetailController.consumeFrequency(resource));
        assertEquals(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR.getCode(), exception.getCode());
    }

    @Test
    void consumeDays() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailController.consumeDays(resource));
    }

    @Test
    void consumeDays1() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Collections.emptyList()).mallIds(Collections.emptyList())
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        BizException exception = assertThrows(BizException.class, () -> tbSalesDetailController.consumeDays(resource));
        assertEquals(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR.getCode(), exception.getCode());
    }

    @Test
    void badgeShopGroupSalesDetails() {
        BadgeSalesResource resource = BadgeSalesResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .vipcode("KERRY100284140").shopNos(Arrays.asList("HKC00041002", "HKC00041004", "HKC00041005"))
                .startDate("2024-03-07 23:59:59").endDate("2025-03-07 23:59:59").build();
        assertDoesNotThrow(() -> tbSalesDetailController.badgeShopGroupSalesDetails(Collections.singletonList(resource)));
    }

    @Test
    void queryById() {
        assertDoesNotThrow(() -> tbSalesDetailController.queryById(1L));
    }

}
