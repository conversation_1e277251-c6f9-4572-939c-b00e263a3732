package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.service.TbIntegralCategoryRateService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.webservice.TbIntegralCategoryRateController;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralCategoryRateResource;
import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbIntegralCategoryRateResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageRequest;

import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Bert
 * Created Date - 07/31/2024 11:14
 **********************************************************************************************************************/

@Slf4j
class TbIntegralCategoryRateControllerTest {

    @InjectMocks
    private TbIntegralCategoryRateController tbIntegralCategoryRateController;

    @Mock
    private TbIntegralCategoryRateService tbIntegralCategoryRateService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private Mapper mapper;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("list 为空")
    void categoryRatePage() {
        PageRequest page1 = PageRequest.of(0, 10);
        Assertions.assertNotNull(tbIntegralCategoryRateController.categoryRatePage("", "", "", page1));

        PageRequest page = PageRequest.of(1, 10);
        Mockito.when(tbIntegralCategoryRateService.getTotal(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(0);
        Assertions.assertNotNull(tbIntegralCategoryRateController.categoryRatePage("", "", "", page));

        Mockito.when(tbIntegralCategoryRateService.getTotal(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(10);
        Mockito.when(tbIntegralCategoryRateService.categoryRatePage(Mockito.any(), Mockito.any(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.emptyList());
        Assertions.assertNotNull(tbIntegralCategoryRateController.categoryRatePage("", "", "", page));

        Mockito.when(tbIntegralCategoryRateService.categoryRatePage(Mockito.any(), Mockito.any(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TbIntegralCategoryRate.builder().categoryName("01&amp;02").build()));
        Mockito.when(mapper.map(TbIntegralCategoryRate.builder().categoryName("01&02").build(), TbIntegralCategoryRateResponse.class))
                .thenReturn(TbIntegralCategoryRateResponse.builder().build());
        Assertions.assertNotNull(tbIntegralCategoryRateController.categoryRatePage("8a8480487c96d58f017c9b7bab7d0020", "8a8481f57cca9442017ce02875b8004a", "09", page));
    }

    @Test
    void saveOrUpdateCategoryRate() {
        Assertions.assertNotNull(tbIntegralCategoryRateController.saveOrUpdateCategoryRate(TbIntegralCategoryRateResource.builder().build()));

        TbIntegralCategoryRateResource resource = TbIntegralCategoryRateResource.builder().build();
        resource.setGroupId("8a8480487c96d58f017c9b7bab7d0020");
        resource.setCategoryId("04");
        resource.setIsConsistent(1);
        List<GradeIntegralResponse> list = Collections.singletonList(GradeIntegralResponse.builder().build());
        resource.setList(list);
        Assertions.assertThrows(Exception.class, () -> tbIntegralCategoryRateController.saveOrUpdateCategoryRate(resource));

        Mockito.when(tbMemberGradeService.queryGradeSortAscByGroupId(Mockito.any())).thenReturn(Collections.singletonList(TbMemberGrade.builder().build()));
        Assertions.assertNotNull(tbIntegralCategoryRateController.saveOrUpdateCategoryRate(resource));
    }

    @Test
    @DisplayName("getCategoryRateList")
    void getCategoryRateList() {
        Assertions.assertNotNull(tbIntegralCategoryRateController.getCategoryRateList("", "", ""));

        Mockito.when(tbIntegralCategoryRateService.getCategoryRateList(Mockito.any()))
                .thenReturn(Collections.singletonList(TbIntegralCategoryRate.builder().build()));
        Assertions.assertNotNull(tbIntegralCategoryRateController.getCategoryRateList("", "", ""));
    }

    @Test
    @DisplayName("删除")
    void deleteCategoryRateList() {
        Assertions.assertThrows(Exception.class, () -> tbIntegralCategoryRateController.deleteCategoryRateList(null));

        Long[] ids1 = new Long[0];
        Assertions.assertThrows(Exception.class, () -> tbIntegralCategoryRateController.deleteCategoryRateList(ids1));

        Long[] ids = new Long[] {1L, 2L, 3L};
        Assertions.assertNotNull(tbIntegralCategoryRateController.deleteCategoryRateList(ids));
    }
}
