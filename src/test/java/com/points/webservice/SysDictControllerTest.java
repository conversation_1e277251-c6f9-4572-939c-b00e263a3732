package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.SysDictController;
import com.kerryprops.kip.service.integral.webservice.response.SysDictResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Bert
 * Created Date - 07/31/2024 11:14
 **********************************************************************************************************************/

@Slf4j
class SysDictControllerTest {

    @InjectMocks
    private SysDictController sysDictController;

    @Mock
    private SysDictService sysDictService;
    @Mock
    private Mapper mapper;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getComAndSaleDictType() {
        Mockito.when(sysDictService.getByTypes(Mockito.any())).thenReturn(Collections.singletonList(SysDict.builder().build()));
        Mockito.when(mapper.map(SysDict.builder().build(),SysDictResponse.class)).thenReturn(SysDictResponse.builder().build());
        Assertions.assertNotNull(sysDictController.getComAndSaleDictType());

        Mockito.when(sysDictService.getByTypes(Mockito.any())).thenReturn(Collections.emptyList());
        Assertions.assertNotNull(sysDictController.getComAndSaleDictType());
    }
}
