package com.points.webservice;

import com.kerryprops.kip.service.integral.client.FlexibleSpaceServiceClient;
import com.kerryprops.kip.service.integral.client.TempParkingClient;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.kerryprops.kip.service.integral.model.dto.PageableParkingVehicle;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.KerryVendorMemberController;
import com.kerryprops.kip.service.integral.webservice.resource.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@Slf4j
class KerryVendorMemberControllerTest {

    @InjectMocks
    private KerryVendorMemberController kerryVendorMemberController;

    @Mock
    private MallConfig mallConfig;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private TempParkingClient tempParkingClient;
    @Mock
    private KerryStaffService kerryStaffService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private TbMemberPointsChangeService tbMemberPointsChangeService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private FlexibleSpaceServiceClient flexibleSpaceServiceClient;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findMemberInfo() {
        SupplierQueryMemberResource resource = SupplierQueryMemberResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallid("8aaa80b47c784020017c78b00d060022").mobile("13000000000").build();
        when(mallConfig.isHkc(any())).thenReturn(Boolean.TRUE);
        assertDoesNotThrow(() -> kerryVendorMemberController.findMemberInfo(resource));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().kipUserId("12345").grade("03").currentPoints(100).build());
        when(flexibleSpaceServiceClient.checkMemberVipRoomOrderExists(any(), any())).thenReturn(false);
        when(tbMemberGradeService.queryMaxGroupGrade(Mockito.anyString())).thenReturn(TbMemberGrade.builder().code("03").build());
        Mockito.when(kerryStaffService.findByKipUserId(Mockito.any())).thenReturn(CustomerUserDto.builder().build());
        assertDoesNotThrow(() -> kerryVendorMemberController.findMemberInfo(resource));

        // 存在灵活空间的订单
        when(flexibleSpaceServiceClient.checkMemberVipRoomOrderExists(any(), any())).thenReturn(true);
        assertDoesNotThrow(() -> kerryVendorMemberController.findMemberInfo(resource));
    }

    @Test
    @DisplayName("hkc手机号和会员号均为空")
    void findMemberInfoCase1() {
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(
                SupplierQueryMemberResource.builder().build()));
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(
                SupplierQueryMemberResource.builder().groupId("12345").build()));
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(
                SupplierQueryMemberResource.builder().groupId("12345").mallid("12345").build()));

        SupplierQueryMemberResource resource = SupplierQueryMemberResource
                .builder()
                .groupId("8a8884e77cc9e70a017cca1011970001")
                .mallid("8a8883557cca9463017ccb002b360001")
                .plate("杭A19200K0")
                .build();
        when(mallConfig.isHkc(Mockito.anyString())).thenReturn(false);
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        when(mallConfig.isHkc(Mockito.anyString())).thenReturn(true);
        when(flexibleSpaceServiceClient.checkMemberVipRoomOrderExists(any(), any())).thenReturn(false);
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        Mockito.when(tempParkingClient.vehiclesByCarNo(Mockito.any())).thenReturn(new PageableParkingVehicle());
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        PageableParkingVehicle pageData = new PageableParkingVehicle();
        PageableParkingVehicle.VehicleData vehicleData = new PageableParkingVehicle.VehicleData();
        vehicleData.setId(120);
        vehicleData.setLicensePlateType(PageableParkingVehicle.VehicleData.LicensePlateType.SPECIAL_CAR_NO);
        pageData.setContent(Collections.singletonList(vehicleData));
        Mockito.when(tempParkingClient.vehiclesByCarNo(Mockito.any())).thenReturn(pageData);
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndKipUserIds(Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(TbMemberAsset.builder().kipUserId("12345").grade("01").currentPoints(100).build()));
        when(flexibleSpaceServiceClient.checkMemberVipRoomOrderExists(any(), any())).thenReturn(true);
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        when(tbMemberGradeService.queryMaxGroupGrade(Mockito.anyString())).thenReturn(TbMemberGrade.builder().code("03").build());
        when(flexibleSpaceServiceClient.checkMemberVipRoomOrderExists(any(), any())).thenReturn(false);
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        when(tbMemberGradeService.queryMaxGroupGrade(Mockito.anyString())).thenReturn(TbMemberGrade.builder().code("01").build());
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        when(kerryStaffService.findByKipUserId(Mockito.anyString())).thenReturn(CustomerUserDto.builder().build());
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));
    }

    @Test
    @DisplayName("HKC情况，车牌号不为空，其余的均为空")
    void findMemberInfoCase2() {
        PageableParkingVehicle vehicle = new PageableParkingVehicle();
        SupplierQueryMemberResource resource = SupplierQueryMemberResource
                .builder()
                .groupId("8a8884e77cc9e70a017cca1011970001")
                .mallid("8a8883557cca9463017ccb002b360001")
                .mobile("19098654321")
                .build();
        when(mallConfig.isHkc(Mockito.anyString())).thenReturn(true);
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().grade("01").build());
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        Mockito.when(tbMemberGradeService.queryMaxGroupGrade(Mockito.any())).thenReturn(TbMemberGrade.builder().code("03").build());
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));

        Mockito.when(tbMemberGradeService.queryMaxGroupGrade(Mockito.any())).thenReturn(TbMemberGrade.builder().code("01").build());
        Mockito.when(kerryStaffService.findByKipUserId(Mockito.any())).thenReturn(CustomerUserDto.builder().build());
        Assertions.assertNotNull(kerryVendorMemberController.findMemberInfo(resource));
    }

    @Test
    @DisplayName("查询会员信息-手机号会员号均为空")
    void getMemberInfo() {
        Assertions.assertNotNull(kerryVendorMemberController.getMemberInfo(
                SupplierQueryMemberResource.builder().build()));
        Assertions.assertNotNull(kerryVendorMemberController.getMemberInfo(
                SupplierQueryMemberResource.builder().groupId("12345").build()));
        Assertions.assertNotNull(kerryVendorMemberController.getMemberInfo(
                SupplierQueryMemberResource.builder().groupId("12345").mallid("12345").build()));

        SupplierQueryMemberResource resource = SupplierQueryMemberResource
                .builder()
                .groupId("8a8884e77cc9e70a017cca1011970001")
                .mallid("8a8883557cca9463017ccb002b360001")
                .plate("杭A19200K0")
                .build();
        PageableParkingVehicle pageData = new PageableParkingVehicle();
        PageableParkingVehicle.VehicleData vehicleData = new PageableParkingVehicle.VehicleData();
        vehicleData.setId(120);
        pageData.setContent(Collections.singletonList(vehicleData));
        Mockito.when(tempParkingClient.vehiclesByCarNo(Mockito.any())).thenReturn(pageData);
        Mockito.when(tbMemberAssetService.queryMemberByGroupIdAndKipUserIds(Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(TbMemberAsset.builder().kipUserId("12345").grade("01").currentPoints(100).build()));
        Assertions.assertNotNull(kerryVendorMemberController.getMemberInfo(resource));

        Mockito.when(tbMemberGradeService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().code("01").build()));
        Assertions.assertNotNull(kerryVendorMemberController.getMemberInfo(resource));
    }

    @Test
    @DisplayName("查询会员信息-手机号会员号均为空")
    void getMemberInfoCase1() {
        SupplierQueryMemberResource resource = SupplierQueryMemberResource
                .builder()
                .groupId("8a8884e77cc9e70a017cca1011970001")
                .mallid("8a8883557cca9463017ccb002b360001")
                .mobile("19808675432")
                .build();
        PageableParkingVehicle pageData = new PageableParkingVehicle();
        PageableParkingVehicle.VehicleData vehicleData = new PageableParkingVehicle.VehicleData();
        vehicleData.setId(120);
        pageData.setContent(Collections.singletonList(vehicleData));
        Assertions.assertNotNull(kerryVendorMemberController.getMemberInfo(resource));
    }

    @Test
    void getShopCode() {
        ShopCodeResource resource = ShopCodeResource
                .builder()
                .mallid("8a8883557cca9463017ccb002b360001")
                .contractNo("B000150")
                .build();
        Assertions.assertNotNull(kerryVendorMemberController.getShopCode(resource));


        when(tbBaseShopService.getShopByDoCos(Mockito.anyString(),  Mockito.anyString())).thenReturn(TbBaseShop.builder().build());
        Assertions.assertNotNull(kerryVendorMemberController.getShopCode(resource));
    }

    @Test
    void basicIntegralUpdate() {
        BasicIntegralUpdateResource resource = BasicIntegralUpdateResource
                .builder()
                .groupId("8a8884e77cc9e70a017cca1011970001")
                .mallid("8a8883557cca9463017ccb002b360001")
                .vipcode("KERRY100383486")
                .mobile("19809098976")
                .number(100D)
                .build();
        Assertions.assertNotNull(kerryVendorMemberController.basicIntegralUpdate(resource));

        when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().grade("03").build());
        when(tbMemberPointsChangeService.updateMemberPoints(Mockito.any())).thenReturn("002178fe8f1a4a238c5e796553503eb2");
        Assertions.assertNotNull(kerryVendorMemberController.basicIntegralUpdate(resource));
    }
}
