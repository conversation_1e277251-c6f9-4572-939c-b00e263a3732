package com.points.webservice;

import com.kerryprops.kip.service.integral.client.KerryStaffClient;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.DevController;
import com.kerryprops.kip.service.integral.webservice.resource.ExecuteSqlResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Collections;
/**
 * <AUTHOR>
 */
@Slf4j
class DevControllerTest {

    @InjectMocks
    private DevController devController;

    @Mock
    private TbInsensatePointsPushRecordService insensatePointsPushRecordService;

    @Mock
    private RedisService redisService;

    @Mock
    private SmsSendService smsSendService;

    @Mock
    private TbMemberAssetService tbMemberAssetService;

    @Mock
    private TbSalesDetailService tbSalesDetailService;

    @Mock
    private TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;

    @Mock
    private KerryStaffClient kerryStaffClient;

    @Mock
    private WechatPaymentAutoPointsCheckService wechatPaymentAutoPointsCheckService;

    @Mock
    private MallConfig mallConfig;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getPushRecordByMobileAppid() {
        Mockito.when(kerryStaffClient.getWxInfoByMobileAndAppId(Mockito.any(), Mockito.any())).thenReturn(new CustomerThirdPartyDto());
        Mockito.when(insensatePointsPushRecordService.selectByOpenId(Mockito.any())).thenReturn(Collections.emptyList());
        Assertions.assertNotNull(devController.getPushRecordByMobileAppid(Mockito.any(), Mockito.any()));
    }

    @Test
    void getPushRecordByMobileAppidCase1() {
        Mockito.when(kerryStaffClient.getWxInfoByMobileAndAppId(Mockito.any(), Mockito.any())).thenReturn(null);
        Assertions.assertNotNull(devController.getPushRecordByMobileAppid(Mockito.any(), Mockito.any()));
    }

    @Test
    void updateMallPointsAuthorizeStatus() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertNotNull(devController.updateMallPointsAuthorizeStatus(Mockito.any()));
    }

    @Test
    void updateMallPointsAuthorizeStatusCase1() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(null);
        Assertions.assertNotNull(devController.updateMallPointsAuthorizeStatus(Mockito.any()));
    }

    @Test
    void setMockAdditionalKey() {
        Assertions.assertDoesNotThrow(() -> devController.setMockAdditionalKey("test"));

    }

    @Test
    void handleMemberGradeData() {
        Assertions.assertDoesNotThrow(() -> devController.handleMemberGradeData(Mockito.any()));
    }

    @Test
    void modifyMemberSaleShopId() {
        Assertions.assertDoesNotThrow(() -> devController.modifyMemberSaleShopId(Mockito.any(), Mockito.any(), Mockito.any()));
    }

    @Test
    void fillMemberGradeChangeDetailMallId() {
        Assertions.assertDoesNotThrow(() -> devController.fillMemberGradeChangeDetailMallId(Mockito.any(), Mockito.any(), Mockito.any()));
    }

    @Test
    void syncCrmUser() {
        MultipartFile file = Mockito.mock(MultipartFile.class);
        try (InputStream resourceAsStream = this.getClass().getResourceAsStream("/templates/model_integral.xlsx")) {
            Mockito.when(file.getInputStream()).thenReturn(resourceAsStream);
            Assertions.assertNotNull(devController.syncCrmUser(file, "8a8884e77cc9e70a017cca1011970001"));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    void changeDate() {
        Assertions.assertDoesNotThrow(() -> devController.changeDate(Mockito.any(), Mockito.any()));
    }

    @Test
    void checkSalesUpgradeGrade() {
        Assertions.assertDoesNotThrow(() -> devController.checkSalesUpgradeGrade(Mockito.any(), Mockito.any(), Mockito.any()));
    }

    @Test
    void removeCacheKey() {
        Assertions.assertDoesNotThrow(() -> devController.removeCacheKey(Mockito.any()));
    }

    @Test
    void getCacheKey() {
        Assertions.assertDoesNotThrow(() ->devController.getCacheKey(Mockito.any()));
    }

    @Test
    void setOpsForValueKeyAndValue() {
        Assertions.assertDoesNotThrow(() -> devController.setOpsForValueKeyAndValue(RedisCacheKey.HKC_OLD_MEMBER_WX_PAY_AUDIT_SWITCH, "1"));
    }

    @Test
    void getKeys() {
        Assertions.assertDoesNotThrow(() -> devController.getKeys(Mockito.any()));
    }

    @Test
    void testSendEmail() {
        Assertions.assertDoesNotThrow(() -> devController.testSendEmail(Mockito.any(), Mockito.any()));
    }

    @Test
    void runSql() {
        ExecuteSqlResource resource = ExecuteSqlResource.builder().build();
        Assertions.assertDoesNotThrow(() -> devController.runSql(resource));
    }

    @Test
    void querySqlResult() {
        Assertions.assertDoesNotThrow(() -> devController.querySqlResult(ExecuteSqlResource.builder().build()));
    }
}
