package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbSaleTransferController;
import com.kerryprops.kip.service.integral.webservice.resource.BatchCalculatePointsResource;
import com.kerryprops.kip.service.integral.webservice.resource.MemberPointsChangeResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@Slf4j
class TbSaleTransferControllerTest {

    @InjectMocks
    private TbSaleTransferController tbSaleTransferController;

    @Mock
    private TbMemberAssetService tbMemberAssetService;

    @Mock
    private TbSalesDetailService tbSalesDetailService;

    @Mock
    private SysDictService sysDictService;

    @Mock
    private TbBaseShopService tbBaseShopService;

    @Mock
    private TbSetshoprateService tbSetshoprateService;

    @Mock
    private TbActivityPromotionService tbActivityPromotionService;

    @Mock
    private TbMemberPointsChangeService tbMemberPointsChangeService;

    @Mock
    private Mapper mapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void batchCalculatePoints() {
        BatchCalculatePointsResource resource = BatchCalculatePointsResource.builder().build();
        when(tbMemberAssetService.findAllByDto(SingleMemberQueryDto.builder().build())).thenReturn(TbMemberAsset.builder().status("1").build());
        Assertions.assertDoesNotThrow(() -> tbSaleTransferController.batchCalculatePoints(resource));

        when(tbSalesDetailService.listByIds(Mockito.any()))
                .thenReturn(Collections.singletonList(TbSalesDetail.builder().payAmount(BigDecimal.ZERO).saleType(SaleTypeEnum.TICKET.getValue()).discountAmount(BigDecimal.ZERO).build()));
        when(sysDictService.findByDictType(Mockito.any())).thenReturn(SysDict.builder().build());
        Assertions.assertDoesNotThrow(() -> tbSaleTransferController.batchCalculatePoints(resource));

        Mockito.when(tbBaseShopService.getByContractNoAndMallId(Mockito.any(), Mockito.any())).thenReturn(TbBaseShop.builder().build());
        Mockito.when(tbSetshoprateService.salesToIntegral(Mockito.any())).thenReturn(1);
        Mockito.when(tbActivityPromotionService.handlePromotionActivity(SalesAutoPointsDto.builder().discountAmount(BigDecimal.ZERO.toString()).amount(BigDecimal.ZERO.toString()).build(), Boolean.FALSE)).thenReturn(0);
        Assertions.assertDoesNotThrow(() -> tbSaleTransferController.batchCalculatePoints(resource));
    }

    @Test
    void batchAdjustPoints() {
        List<MemberPointsChangeResource> list = Arrays.asList(MemberPointsChangeResource.builder().changePointsNum(1).build(), MemberPointsChangeResource.builder().changePointsNum(0).build());
        Assertions.assertDoesNotThrow(() -> tbSaleTransferController.batchAdjustPoints(list));

        Assertions.assertDoesNotThrow(() -> tbSaleTransferController.batchAdjustPoints(null));
    }



}
