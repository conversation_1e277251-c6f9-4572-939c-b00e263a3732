package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbFieldSort;
import com.kerryprops.kip.service.integral.entity.TbOcrCallbackRecord;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.BonusSelfQueryDto;
import com.kerryprops.kip.service.integral.model.dto.TakePhotoAuditDto;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbPhotoReviewController;
import com.kerryprops.kip.service.integral.webservice.resource.PhotoReviewQueryResource;
import com.kerryprops.kip.service.integral.webservice.resource.TakePhotoAuditResource;
import com.kerryprops.kip.service.integral.webservice.response.KoPhotoReviewResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbReviewSelfResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.Mockito.mock;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> Nancy
 * Created Date - 09/14/2022 17:27
 **********************************************************************************************************************/

@Slf4j
class TbPhotoReviewControllerTest {

    @InjectMocks
    TbPhotoReviewController tbPhotoReviewController;

    @Mock
    private TbPhotoReviewService tbPhotoReviewService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private KerryStaffService kerryStaffService;
    @Mock
    private TbOcrCallbackRecordService tbOcrCallbackRecordService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private Mapper mapper;
    @Mock
    private TbFieldSortService tbFieldSortService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getReviewPage() {
        PhotoReviewQueryResource reviewQueryResource = PhotoReviewQueryResource
                .builder()
                .state(Collections.singletonList("1"))
                .uploadStartTime("2024-12-27")
                .uploadEndTime("2024-12-28")
                .examineStartTime("2024-12-29")
                .examineEndTime("2024-12-30")
                .desc(Arrays.asList("desc1", "desc2"))
                .asc(Arrays.asList("asc1", "asc2"))
                .page(1)
                .size(1)
                .build();
        BonusSelfQueryDto dto = BonusSelfQueryDto
                .builder()
                .status(Collections.singletonList("1"))
                .uploadStartTime("2024-12-27")
                .uploadEndTime("2024-12-28")
                .examineStartTime("2024-12-29")
                .examineEndTime("2024-12-30")
                .page(1)
                .size(1)
                .build();
        Mockito.when(mapper.map(reviewQueryResource, BonusSelfQueryDto.class)).thenReturn(dto);
        Assertions.assertNotNull(tbPhotoReviewController.getReviewPage(reviewQueryResource));

        Mockito.when(tbPhotoReviewService.getBonusTotal(dto)).thenReturn(10);
        Assertions.assertNotNull(tbPhotoReviewController.getReviewPage(reviewQueryResource));

        Mockito.when(tbPhotoReviewService.getReviewPage(dto))
                .thenReturn(Collections.singletonList(TbPhotoReview.builder().shopNo("12345")
                        .status(PhotoReviewStatusEnum.TYPE_1.getCode()).build()));

        Map<String, TenantInfoVo> tenantInfoVoMap = new HashMap<>();
        tenantInfoVoMap.put("12345", TenantInfoVo.builder().build());
        Mockito.when(hiveVasService.getTenantFutureFromHiveService(Mockito.anyList()))
                .thenReturn(CompletableFuture.completedFuture(tenantInfoVoMap));
        Mockito.when(hiveVasService.getMallName(Mockito.anyList()))
                .thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        Mockito.when(kerryStaffService.getMapFutureByMobile(Mockito.anyList())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        Mockito.when(tbMemberGradeService.getMapFutureByGroupId(Mockito.any())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));

        Mockito.when(mapper.map(TbPhotoReview.builder().shopNo("12345").status(PhotoReviewStatusEnum.TYPE_1.getCode()).build(),
                        TbReviewSelfResponse.class))
                .thenReturn(TbReviewSelfResponse.builder().shopNo("12345")
                        .status(PhotoReviewStatusEnum.TYPE_1.getCode()).build());
        Assertions.assertNotNull(tbPhotoReviewController.getReviewPage(reviewQueryResource));

        Mockito.when(mapper.map(TbPhotoReview.builder().shopNo("12345")
                        .status(PhotoReviewStatusEnum.TYPE_1.getCode()).build(), TbReviewSelfResponse.class))
                .thenReturn(TbReviewSelfResponse.builder()
                        .status(PhotoReviewStatusEnum.TYPE_6.getCode())
                        .checkTime("1999-01-01 00:00:00")
                        .tradingDate("1999-01-01 00:00:00")
                        .bonus("12345")
                        .build());
        Assertions.assertNotNull(tbPhotoReviewController.getReviewPage(reviewQueryResource));

        // 进入异常
        Mockito.when(mapper.map(TbPhotoReview.builder().shopNo("12345")
                .status(PhotoReviewStatusEnum.TYPE_1.getCode()).build(), TbReviewSelfResponse.class))
                .thenThrow(new BizException(400, ""));
        Assertions.assertNotNull(tbPhotoReviewController.getReviewPage(reviewQueryResource));
    }

    @Test
    void getDetail() {
        Assertions.assertThrows(Exception.class, ()->tbPhotoReviewController.getDetail("", "", ""));

        Mockito.when(tbPhotoReviewService.getById(Mockito.anyString())).thenReturn(TbPhotoReview.builder().id(100L).build());
        Mockito.when(mapper.map(TbPhotoReview.builder().id(100L).build(), TbReviewSelfResponse.class)).thenReturn(TbReviewSelfResponse.builder().id("100").checkTime("1999-01-01 00:00:00").build());
        Mockito.when(tbPhotoReviewService.getNextImage(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("");
        Mockito.when(tbOcrCallbackRecordService.findByPhotoId(Mockito.anyLong())).thenReturn(TbOcrCallbackRecord.builder().build());
        Mockito.when(kerryStaffService.getMapByMobiles(Mockito.anyList())).thenReturn(new HashMap<>());
        Assertions.assertNotNull(tbPhotoReviewController.getDetail("", "", ""));

        Mockito.when(mapper.map(TbPhotoReview.builder().id(100L).build(), TbReviewSelfResponse.class))
                .thenReturn(TbReviewSelfResponse.builder().id("100").tradingDate("1999-01-01 00:00:00").checkTime("1999-01-01 00:00:00").build());
        Assertions.assertNotNull(tbPhotoReviewController.getDetail("", "", ""));
    }

    @Test
    void getPreviousPage() {
        Mockito.when(tbPhotoReviewService.getForPrevPage(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.when(tbPhotoReviewService.getForPrevPage(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(null);
        Assertions.assertThrows(Exception.class, ()->tbPhotoReviewController.getPreviousPage("1", "2","3","4"));

        Mockito.when(tbPhotoReviewService.getForPrevPage(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(TbPhotoReview.builder().status("6").build());
        Mockito.when(mapper.map(TbPhotoReview.builder().status("6").build(), TbReviewSelfResponse.class))
                .thenReturn(TbReviewSelfResponse.builder().status("6").build());
        Mockito.when(kerryStaffService.getMapByMobiles(Mockito.anyList())).thenReturn(new HashMap<>());
        Assertions.assertNotNull(tbPhotoReviewController.getPreviousPage("1", "2","3","4"));
    }

    @Test
    void exportReviewDetail() {
        LoginUser loginUser = new LoginUser();
        PhotoReviewQueryResource resource = PhotoReviewQueryResource.builder().build();
        HttpServletResponse response = mock(HttpServletResponse.class);
        Mockito.when(mapper.map(resource, BonusSelfQueryDto.class)).thenReturn(BonusSelfQueryDto.builder().build());

        Mockito.when(tbPhotoReviewService.getBonusTotal(Mockito.any())).thenReturn(200000);
        Assertions.assertThrows(BizException.class, () -> tbPhotoReviewController.exportReviewDetail(resource, response, loginUser));

        Mockito.when(tbFieldSortService.findByUserIdAndPageType(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.emptyList());
        Mockito.when(tbPhotoReviewService.getBonusTotal(Mockito.any())).thenReturn(0);
        Assertions.assertThrows(Exception.class, () -> tbPhotoReviewController.exportReviewDetail(resource, response, loginUser));

    }

    @Test
    void exportReviewDetailCase1() {
        LoginUser loginUser = new LoginUser();
        PhotoReviewQueryResource resource = PhotoReviewQueryResource.builder().build();
        HttpServletResponse response = mock(HttpServletResponse.class);
        Mockito.when(mapper.map(resource, BonusSelfQueryDto.class)).thenReturn(BonusSelfQueryDto.builder().build());
        TbPhotoReview review = TbPhotoReview.builder().id(0L).status(PhotoReviewStatusEnum.TYPE_1.getCode()).build();
        Mockito.when(tbPhotoReviewService.getBonusTotal(Mockito.any())).thenReturn(10);
        Mockito.when(tbFieldSortService.findByUserIdAndPageType(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(TbFieldSort.builder().groupId("ALL").fieldCnName("cnName").build()));
        Mockito.when(tbPhotoReviewService.getReviewPage(Mockito.any()))
                .thenReturn(Collections.singletonList(review));
        TbReviewSelfResponse tbReviewSelfResponse = TbReviewSelfResponse
                .builder()
                .status(PhotoReviewStatusEnum.TYPE_6.getCode())
                .checkTime("1999-01-01 00:00:00")
                .tradingDate("1999-01-01 00:00:00")
                .build();
        Mockito.when(hiveVasService.getTenantFutureFromHiveService(Mockito.anyList())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        Mockito.when(hiveVasService.getMallName(Mockito.anyList())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        Mockito.when(kerryStaffService.getMapFutureByMobile(Mockito.anyList())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        Mockito.when(tbMemberGradeService.getMapFutureByGroupId(Mockito.any())).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        Mockito.when(mapper.map(review, TbReviewSelfResponse.class)).thenReturn(tbReviewSelfResponse);
        Assertions.assertThrows(Exception.class, () -> tbPhotoReviewController.exportReviewDetail(resource, response, loginUser));
    }

    @Test
    void getNextPage() {
        Mockito.when(tbPhotoReviewService.getForNextPage("1","2","3","4")).thenReturn(null);
        Mockito.when(tbPhotoReviewService.getForNextPage("","2","3","4"))
                .thenReturn(null);
        Assertions.assertThrows(Exception.class, ()-> tbPhotoReviewController.getNextPage("1", "2","3","4"));

        Mockito.when(tbPhotoReviewService.getForNextPage("1","2","3","4"))
                .thenReturn(TbPhotoReview.builder().id(100L).status("6").build());
        Mockito.when(mapper.map(TbPhotoReview.builder().id(100L).status("6").build(), TbReviewSelfResponse.class)).thenReturn(TbReviewSelfResponse.builder().id("100").status("6").build());
        Mockito.when(tbPhotoReviewService.getNextImage("1","2","3","4")).thenReturn(null);
        Mockito.when(tbPhotoReviewService.getNextImage("","2","3","4")).thenReturn("123");
        Mockito.when(kerryStaffService.getMapByMobiles(Mockito.anyList())).thenReturn(new HashMap<>());
        Mockito.when(tbOcrCallbackRecordService.findByPhotoId(Mockito.any())).thenReturn(TbOcrCallbackRecord.builder().build());
        Assertions.assertNotNull(tbPhotoReviewController.getNextPage("1", "2","3","4"));
    }

    @Test
    void photoAudit() {
        LoginUser loginUser = LoginUser.builder().nickName("test001").userId(100L).build();
        Mockito.when(mapper.map(TakePhotoAuditResource.builder().build(), TakePhotoAuditDto.class)).thenReturn(TakePhotoAuditDto.builder().build());
        Mockito.when(tbPhotoReviewService.auditRecord(Mockito.any(TakePhotoAuditDto.class))).thenReturn(100);
        Assertions.assertNotNull(tbPhotoReviewController.photoAudit(loginUser, TakePhotoAuditResource.builder().build()));
    }

    @Test
    void getStatusList() {
        Assertions.assertNotNull(tbPhotoReviewController.getStatusList());
    }

}
