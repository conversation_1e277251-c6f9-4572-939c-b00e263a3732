package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsIntercept;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbPointsInterceptApproveController;
import com.kerryprops.kip.service.integral.webservice.response.TbPointsInterceptDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> Nancy
 * Created Date - 09/14/2022 17:27
 **********************************************************************************************************************/

@Slf4j
class TbPointsInterceptApproveControllerTest {

    @InjectMocks
    TbPointsInterceptApproveController tbPointsInterceptApproveController;

    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private TbPointsInterceptApproveService interceptApproveService;
    @Mock
    private TbCardMemberRelationService tbCardMemberRelationService;
    @Mock
    private Mapper mapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void salePointsProcess() {
        SalesAutoPointsDto salesAutoPointsDto = SalesAutoPointsDto.builder().build();
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any()))
                .thenReturn(100);
        Assertions.assertNotNull(tbPointsInterceptApproveController.salePointsProcess(salesAutoPointsDto));
    }

    @Test
    @DisplayName("cardNo为空")
    void getInterceptRecord() {
        LoginUser user = LoginUser.builder().build();
        Assertions.assertNotNull(tbPointsInterceptApproveController.getInterceptRecord(user, "", 1, 100));

        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any()))
                .thenReturn(MallItem.builder().build());
        Assertions.assertNotNull(tbPointsInterceptApproveController.getInterceptRecord(user, "", 1, 100));

        Mockito.when(tbMemberAssetService.findByKipUserIdAndGroupId(Mockito.any(),Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(interceptApproveService.getTotal(Mockito.any()))
                .thenReturn(0);
        Assertions.assertNotNull(tbPointsInterceptApproveController.getInterceptRecord(user, "", 1, 100));


        Mockito.when(interceptApproveService.getTotal(Mockito.any()))
                .thenReturn(100);
        Mockito.when(interceptApproveService.getPageData(Mockito.any()))
                .thenReturn(Arrays.asList(
                        TbPointsIntercept.builder().mallId("123").saleType(SaleTypeEnum.CRM.getValue()).build(),
                        TbPointsIntercept.builder().saleType(SaleTypeEnum.TICKET.getValue()).build()));
        Mockito.when(mapper.map(TbPointsIntercept.builder().mallId("123").saleType(SaleTypeEnum.CRM.getValue()).build(), TbPointsInterceptDetailResponse.class))
                .thenReturn(TbPointsInterceptDetailResponse.builder().shopNo("123").mallId("123").saleType(SaleTypeEnum.CRM.getValue()).build());
        Mockito.when(mapper.map(TbPointsIntercept.builder().saleType(SaleTypeEnum.TICKET.getValue()).build(), TbPointsInterceptDetailResponse.class))
                .thenReturn(TbPointsInterceptDetailResponse.builder().shopNo("123").mallId("123").saleType(SaleTypeEnum.TICKET.getValue()).build());
        Map<String, TenantInfoVo> shopMap = new HashMap<>();
        shopMap.put("123", TenantInfoVo.builder().build());
        Mockito.when(hiveVasService.getTenantFuture(Mockito.anyList()))
                .thenReturn(CompletableFuture.completedFuture(shopMap));

        Map<String, String> mallNameMap = new HashMap<>();
        mallNameMap.put("123", "123");
        Mockito.when(hiveVasService.getMallName(Mockito.any()))
                .thenReturn(CompletableFuture.completedFuture(mallNameMap));
        Assertions.assertNotNull(tbPointsInterceptApproveController.getInterceptRecord(user, "", 1, 100));
    }

    @Test
    @DisplayName("cardNo不为空")
    void getInterceptRecordCase1() {
        LoginUser user = LoginUser.builder().build();
        Assertions.assertNotNull(tbPointsInterceptApproveController.getInterceptRecord(user, "123", 1, 100));

        Mockito.when(tbCardMemberRelationService.findByExternalCardNo(Mockito.any()))
                .thenReturn(TbCardMemberRelation.builder().build());
        Assertions.assertNotNull(tbPointsInterceptApproveController.getInterceptRecord(user, "123", 1, 100));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(interceptApproveService.getTotal(Mockito.any()))
                .thenReturn(0);
        Assertions.assertNotNull(tbPointsInterceptApproveController.getInterceptRecord(user, "123", 1, 100));
    }

}
