package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import com.kerryprops.kip.service.integral.service.HiveVasService;
import com.kerryprops.kip.service.integral.service.TbIntegralShopRateService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.webservice.TbIntegralShopRateController;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralShopRateResource;
import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbIntegralShopRateResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageRequest;

import java.util.*;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Nancy
 **********************************************************************************************************************/

@Slf4j
class TbIntegralShopRateControllerTest {

    @InjectMocks
    private TbIntegralShopRateController tbIntegralShopRateController;

    @Mock
    private TbIntegralShopRateService tbIntegralShopRateService;
    @Mock
    private Mapper mapper;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("分页查询")
    void categoryRatePage() {
        PageRequest page1 = PageRequest.of(0, 10);
        Assertions.assertNotNull(tbIntegralShopRateController.shopRatePage("", "", "", page1));

        PageRequest page = PageRequest.of(0, 10);
        Mockito.when(tbIntegralShopRateService.getTotal(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(0);
        Assertions.assertNotNull(tbIntegralShopRateController.shopRatePage("", "12345", "09", page));

        Mockito.when(tbIntegralShopRateService.getTotal(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(10);
        Mockito.when(tbIntegralShopRateService.shopRatePage(Mockito.any(), Mockito.anyList(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(null);
        Assertions.assertNotNull(tbIntegralShopRateController.shopRatePage("", "12345", "09", page));

        Mockito.when(tbIntegralShopRateService.shopRatePage(Mockito.any(), Mockito.anyList(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Arrays.asList(TbIntegralShopRate.builder().shopId("HKC00901").build(),
                        TbIntegralShopRate.builder().shopId("").build(), TbIntegralShopRate.builder().shopId("11234").build()));

        Map<String, TenantInfoVo> shopMap = new HashMap<>();
        TenantInfoVo tenantInfoVo = new TenantInfoVo();
        shopMap.put("HKC00901", tenantInfoVo);
        Mockito.when(hiveVasService.getTenantInfoByShopNos(Mockito.anyList()))
                .thenReturn(shopMap);
        Mockito.when(mapper.map(TbIntegralShopRate.builder().shopId("HKC00901").status(0).isConsistent(0).build(),
                        TbIntegralShopRateResponse.class))
                .thenReturn(TbIntegralShopRateResponse.builder().shopId("HKC00901").build());
        Mockito.when(mapper.map(TbIntegralShopRate.builder().shopId("").status(0).isConsistent(0).build(),
                        TbIntegralShopRateResponse.class))
                .thenReturn(TbIntegralShopRateResponse.builder().shopId("").build());
        Mockito.when(mapper.map(TbIntegralShopRate.builder().shopId("11234").status(0).isConsistent(0).build(),
                        TbIntegralShopRateResponse.class))
                .thenReturn(TbIntegralShopRateResponse.builder().shopId("11234").build());
        Assertions.assertNotNull(tbIntegralShopRateController.shopRatePage("", "123456", "HKC00901", page));

    }

    @Test
    @DisplayName("保存或修改")
    void saveOrUpdateShopRateCase1() {
        Assertions.assertNotNull(tbIntegralShopRateController.saveOrUpdateShopRate(TbIntegralShopRateResource.builder().build()));

        TbIntegralShopRateResource resource = TbIntegralShopRateResource.builder().isConsistent(1).build();
        resource.setList(Arrays.asList(GradeIntegralResponse.builder().grade("01").build(), GradeIntegralResponse.builder().grade("02").build()));
        Assertions.assertThrows(BizException.class, () -> tbIntegralShopRateController.saveOrUpdateShopRate(resource));

        Mockito.when(tbMemberGradeService.queryGradeSortAscByGroupId(Mockito.any()))
                .thenReturn(Collections.singletonList(TbMemberGrade.builder().build()));
        Assertions.assertNotNull(tbIntegralShopRateController.saveOrUpdateShopRate(resource));
    }

    @Test
    @DisplayName("查询店铺积分配置")
    void getShopRateList() {
        Mockito.when(tbIntegralShopRateService.getShopRateList(Mockito.any())).thenReturn(null);
        Assertions.assertNotNull(tbIntegralShopRateController.getShopRateList("", "", ""));

        Mockito.when(tbIntegralShopRateService.getShopRateList(Mockito.any()))
                .thenReturn(Collections.singletonList(TbIntegralShopRate.builder().build()));
        Mockito.when(mapper.map(TbIntegralShopRate.builder().build(), TbIntegralShopRateResponse.class))
                .thenReturn(TbIntegralShopRateResponse.builder().build());
        Assertions.assertNotNull(tbIntegralShopRateController.getShopRateList("", "", ""));
    }

    @Test
    @DisplayName("删除")
    void deleteShopRateListCase1() {
        Assertions.assertThrows(Exception.class, () -> tbIntegralShopRateController.deleteShopRateList(null));

        Long[] ids1 = new Long[0];
        Assertions.assertThrows(Exception.class, () -> tbIntegralShopRateController.deleteShopRateList(ids1));

        Long[] ids = new Long[] {1L};
        Assertions.assertNotNull(tbIntegralShopRateController.deleteShopRateList(ids));
    }
}
