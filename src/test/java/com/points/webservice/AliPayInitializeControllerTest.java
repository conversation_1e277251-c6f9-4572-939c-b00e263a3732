package com.points.webservice;

import com.alipay.api.domain.MerchantCard;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.AliPayClientService;
import com.kerryprops.kip.service.integral.webservice.AliPayInitializeController;
import com.kerryprops.kip.service.integral.webservice.resource.AlipayCardTemplateUpdateResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 */
@Slf4j
class AliPayInitializeControllerTest {

    @InjectMocks
    private AliPayInitializeController aliPayInitializeController;

    @Mock
    private MallConfig mallConfig;

    @Mock
    private AliPayClientService aliPayClientService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void createAliPayCardUrl() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayClientService.createAliPayCardUrl(Mockito.any())).thenReturn("");
        assertNotNull(aliPayInitializeController.createAliPayCardUrl(Mockito.any()));
    }

    @Test
    void cardMarketingTradeSubscribe() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayClientService.cardMarketingTradeSubscribe(Mockito.any())).thenReturn("");
        assertNotNull(aliPayInitializeController.cardMarketingTradeSubscribe(Mockito.any()));
    }

    @Test
    void queryCardTemplate() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayClientService.queryCardTemplate(Mockito.any())).thenReturn("");
        assertNotNull(aliPayInitializeController.queryCardTemplate(Mockito.any()));
    }

    @Test
    void getAlipayMarketingCardActivateurl() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayClientService.getAlipayMarketingCardActivateurl(Mockito.any())).thenReturn("");
        assertNotNull(aliPayInitializeController.getAlipayMarketingCardActivateurl(Mockito.any()));
    }

    @Test
    void getAlipayMarketingCardFormtemplateSetRequest() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayClientService.getAlipayMarketingCardFormtemplateSetRequest(Mockito.any())).thenReturn("");
        assertNotNull(aliPayInitializeController.getAlipayMarketingCardFormtemplateSetRequest(Mockito.any()));
    }

    @Test
    void configTemplate() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayClientService.configTemplate(Mockito.any())).thenReturn("");
        assertNotNull(aliPayInitializeController.configTemplate(Mockito.any()));
    }

    @Test
    void createCardTemplate() {
        Mockito.when(mallConfig.getByMallId(Mockito.any())).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayClientService.createCardTemplate(Mockito.any())).thenReturn("");
        assertNotNull(aliPayInitializeController.createCardTemplate(Mockito.any()));
    }

    @Test
    void create() {
        Mockito.when(aliPayClientService.create(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("");
        assertNotNull(aliPayInitializeController.create(Mockito.any(), Mockito.any(), Mockito.any()));
    }

    @Test
    void uploadAlipayMemberCardBackgroundImage() {
        try {
            aliPayInitializeController.uploadAlipayMemberCardBackgroundImage(Mockito.any());
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    void updateAlipayCardTemplateInfo() {
        try {
            AlipayCardTemplateUpdateResource resource = AlipayCardTemplateUpdateResource.builder().build();
            resource.setBackgroundId("HZJLZX");
            resource.setCardShowName("会员卡模板名称");
            resource.setLogoId("73bc3427ad4b4a3aabe9e54432d14410");
            resource.setMallId("8a84853b7c91ac5b017c961a9b2a030d");
            aliPayInitializeController.updateAlipayCardTemplateInfo(resource);
        } catch (Exception e) {
            assertNotNull(e);
        }
    }

    @Test
    void getAliMemberCardDetail() {
        Mockito.when(aliPayClientService.memberCardDetailByCardNo(Mockito.any(), Mockito.any())).thenReturn(new MerchantCard());
        assertNotNull(aliPayInitializeController.getAliMemberCardDetail(Mockito.any(), Mockito.any()));
    }

    @Test
    void getAliMemberCardDetailByAliUserId() {
        Mockito.when(aliPayClientService.memberCardDetailByAliUserId(Mockito.any(), Mockito.any())).thenReturn(new MerchantCard());
        assertNotNull(aliPayInitializeController.getAliMemberCardDetailByAliUserId(Mockito.any(), Mockito.any()));
    }

    @Test
    void getAliMemberCardDetailByMobile() {
        Mockito.when(aliPayClientService.memberCardDetailByMobile(Mockito.any(), Mockito.any())).thenReturn(new MerchantCard());
        assertNotNull(aliPayInitializeController.getAliMemberCardDetailByMobile(Mockito.any(), Mockito.any()));
    }

    @Test
    void authSchemeUrl() {
        Mockito.when(mallConfig.getByMallId("8a888abd8ceb6c70018d117f469c0010")).thenReturn(MallItem.builder().build());
        Mockito.when(aliPayClientService.querySchemaUrl("ad830fe4d1e04988e", MallItem.builder().build())).thenReturn("");
        assertNotNull(aliPayInitializeController.authSchemeUrl("8a888abd8ceb6c70018d117f469c0010", "ad830fe4d1e04988e"));
    }

}
