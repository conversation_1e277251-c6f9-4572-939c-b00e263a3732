package com.points.webservice;

import com.kerryprops.kip.service.integral.common.ResultVO;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.service.GagSmartPosService;
import com.kerryprops.kip.service.integral.webservice.GagSmartPosController;
import com.kerryprops.kip.service.integral.webservice.resource.GagQrCodeQueryResource;
import com.kerryprops.kip.service.integral.webservice.resource.GagScanCodeAchievePointsResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 04/08/2024 13:41
 **********************************************************************************************************************/

@Slf4j
class GagSmartPosControllerTest {

    @InjectMocks
    private GagSmartPosController gagSmartPosController;
    @Mock
    private GagSmartPosService gagSmartPosService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void businessAndSaleIntegral() {
        Assertions.assertNotNull(gagSmartPosController.businessAndSaleIntegral(LoginUser.builder().build(), ""));

        Assertions.assertNotNull(gagSmartPosController.businessAndSaleIntegral(LoginUser.builder().brandId("123").build(), ""));

        Assertions.assertNotNull(gagSmartPosController.businessAndSaleIntegral(LoginUser.builder().brandId("123").lbsId("123").build(), ""));

        Assertions.assertNotNull(gagSmartPosController.businessAndSaleIntegral(LoginUser.builder().brandId("112").lbsId("123").build(), "123"));
    }

    @Test
    void ipadScanQrCode() {
        GagQrCodeQueryResource resource = GagQrCodeQueryResource.builder().billFileName("F878HHHHGqJkKjsHklkkslkisskohhklt").groupId("4028e3817c2b3f79017c2b48c54c0000").mallId("8aaa80b47c784020017c78b00d060022").build();
        Mockito.when(gagSmartPosService.qrcodeDetail(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNull(gagSmartPosController.ipadScanQrCode(resource));
    }

    @Test
    void scanCodeAchievePoints() {
        GagScanCodeAchievePointsResource resource = GagScanCodeAchievePointsResource.builder().build();
        when(gagSmartPosService.scanCodeAchievePoints(Mockito.any(), Mockito.any()))
                .thenReturn(ResultVO.fail("请检查入参."));
        LoginUser user = LoginUser.builder().build();
        Assertions.assertNotNull(gagSmartPosController.scanCodeAchievePoints(resource, user));
    }


}
