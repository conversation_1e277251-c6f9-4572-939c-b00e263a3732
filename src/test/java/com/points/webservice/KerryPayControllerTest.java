package com.points.webservice;

import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PointsRedemptionEnum;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbCashOutConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.PointsRedemptionDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.KerryPayController;
import com.kerryprops.kip.service.integral.webservice.resource.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 11/21/2024 13:34
 **********************************************************************************************************************/

@Slf4j
class KerryPayControllerTest {

    @InjectMocks
    private KerryPayController kerryPayController;
    @Mock
    private TbMemberPointsChangeService tbMemberPointsChangeService;
    @Mock
    private TbCashOutConfigService tbCashOutConfigService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private TbPointsDetailService tbPointsDetailService;
    @Mock
    private TbSetshoprateService tbSetshoprateService;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private RedisService redisService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void frontEndRedemptionAmount() {
        LoginUser loginUser = LoginUser.builder().build();
        // 店铺为空
        Assertions.assertThrows(Exception.class, () -> kerryPayController.frontEndRedemptionAmount(loginUser, PointsRedemptionResource.builder().build(), "", ""));

        // resource的amount<0
        Mockito.when(tbBaseShopService.getByTenantId(Mockito.any(), Mockito.any()))
                .thenReturn(TbBaseShop.builder().contractNo("12345").build());
        Assertions.assertDoesNotThrow(() -> kerryPayController.frontEndRedemptionAmount(loginUser, PointsRedemptionResource.builder().amount(BigDecimal.ZERO).build(), "", ""));

        // getHasValidActivity为false
        Assertions.assertDoesNotThrow(() -> kerryPayController.frontEndRedemptionAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(BigDecimal.TEN).build(),
                "12345",
                "12345"));

        // getCurrentPoints<=0
        Mockito.when(tbCashOutConfigService.getConfig(Mockito.any())).thenReturn(TbCashOutConfig.builder().shopNo("12345").build());
        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(0).build());
        Assertions.assertDoesNotThrow(() -> kerryPayController.frontEndRedemptionAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(BigDecimal.TEN).build(),
                "12345",
                "12345"));

        // 最大金额<=0
        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(10).build());
        Assertions.assertDoesNotThrow(() -> kerryPayController.frontEndRedemptionAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(BigDecimal.TEN).build(),
                "12345",
                "12345"));

        // hundred >= 3
        Mockito.when(tbSetshoprateService.getMaxAmountOfShopCashOut(Mockito.any(), Mockito.any()))
                .thenReturn(PointsRedemptionDto.builder().amount(600).build());
        Assertions.assertDoesNotThrow(() -> kerryPayController.frontEndRedemptionAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(new BigDecimal(400)).build(),
                "12345",
                "12345"));

        // hundred < 3
        Assertions.assertDoesNotThrow(() -> kerryPayController.frontEndRedemptionAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(new BigDecimal(201)).build(),
                "12345",
                "12345"));

    }

    @Test
    void frontEndCustomAmount() {
        LoginUser loginUser = LoginUser.builder().build();
        Assertions.assertThrows(Exception.class, () -> kerryPayController.frontEndCustomAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(BigDecimal.ZERO).build(),
                "12345",
                "12345"));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(0).build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.frontEndCustomAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(new BigDecimal(100)).build(),
                "12345",
                "12345"));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(10).build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.frontEndCustomAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(new BigDecimal(100)).build(),
                "12345",
                "12345"));

        // amountDto为null 最大金额为0
        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(10).build());
        Mockito.when(tbBaseShopService.getByTenantId(Mockito.any(), Mockito.any())).thenReturn(TbBaseShop.builder().contractNo("12345").build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.frontEndCustomAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(new BigDecimal(100)).build(),
                "12345",
                "12345"));

        // C端金额大于最大金额
        Mockito.when(tbSetshoprateService.getMaxAmountOfStoreCashOut(Mockito.any())).thenReturn(PointsRedemptionDto.builder().amount(600).build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.frontEndCustomAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(new BigDecimal(1000)).build(),
                "12345",
                "12345"));

        Assertions.assertDoesNotThrow(() -> kerryPayController.frontEndCustomAmount(
                loginUser,
                PointsRedemptionResource.builder().amount(new BigDecimal(10)).build(),
                "12345",
                "12345"));
    }

    @Test
    void verifyPointsRedemptionAmountValid() {
        Assertions.assertThrows(Exception.class, () -> kerryPayController.verifyPointsRedemptionAmountValid(
                VerifyPointsRedemptionAmountValidResource.builder().pointsRedemptionAmount(new BigDecimal(-10)).build()));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(0).build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.verifyPointsRedemptionAmountValid(
                VerifyPointsRedemptionAmountValidResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(10).build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.verifyPointsRedemptionAmountValid(
                VerifyPointsRedemptionAmountValidResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));

        Mockito.when(tbBaseShopService.getByTenantId(Mockito.any(), Mockito.any())).thenReturn(TbBaseShop.builder().contractNo("12345").build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.verifyPointsRedemptionAmountValid(
                VerifyPointsRedemptionAmountValidResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));

        Mockito.when(tbSetshoprateService.getMaxAmountOfStoreCashOut(Mockito.any())).thenReturn(PointsRedemptionDto.builder().amount(50).build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.verifyPointsRedemptionAmountValid(
                VerifyPointsRedemptionAmountValidResource.builder().pointsRedemptionAmount(new BigDecimal(100)).build()));

        Assertions.assertDoesNotThrow(() -> kerryPayController.verifyPointsRedemptionAmountValid(
                VerifyPointsRedemptionAmountValidResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));
    }

    @Test
    void kerryPayRedemptionDeductPoints() {
        Assertions.assertThrows(Exception.class, () -> kerryPayController.kerryPayRedemptionDeductPoints(
                RedemptionAmountDeductPointsResource.builder().pointsRedemptionAmount(new BigDecimal(-10)).build()));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(0).build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.kerryPayRedemptionDeductPoints(
                RedemptionAmountDeductPointsResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(10).build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.kerryPayRedemptionDeductPoints(
                RedemptionAmountDeductPointsResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));

        Mockito.when(tbBaseShopService.getByTenantId(Mockito.any(), Mockito.any())).thenReturn(TbBaseShop.builder().contractNo("12345").build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.kerryPayRedemptionDeductPoints(
                RedemptionAmountDeductPointsResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));

        Mockito.when(tbSetshoprateService.getMaxAmountOfStoreCashOut(Mockito.any())).thenReturn(PointsRedemptionDto.builder().amount(50).build());
        Assertions.assertThrows(Exception.class, () -> kerryPayController.kerryPayRedemptionDeductPoints(
                RedemptionAmountDeductPointsResource.builder().pointsRedemptionAmount(new BigDecimal(100)).build()));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(false);
        Assertions.assertThrows(Exception.class, () -> kerryPayController.kerryPayRedemptionDeductPoints(
                RedemptionAmountDeductPointsResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(tbSetshoprateService.getBigdecimalPoints(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(100);

        Mockito.when(tbMemberPointsChangeService.updateMemberPoints(Mockito.any())).thenReturn("");
        Assertions.assertDoesNotThrow(() -> kerryPayController.kerryPayRedemptionDeductPoints(
                RedemptionAmountDeductPointsResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));

        Mockito.when(tbMemberPointsChangeService.updateMemberPoints(Mockito.any())).thenThrow(new BizException(400, ""));
        Assertions.assertThrows(Exception.class, () -> kerryPayController.kerryPayRedemptionDeductPoints(
                RedemptionAmountDeductPointsResource.builder().pointsRedemptionAmount(new BigDecimal(10)).build()));
    }

    @Test
    void kerryPayOrderRefund() {
        Assertions.assertThrows(Exception.class, () -> kerryPayController.kerryPayOrderRefund(
                KerryPayRefundResource.builder().build()));

        Mockito.when(tbBaseShopService.getByTenantId(Mockito.any(), Mockito.any())).thenReturn(TbBaseShop.builder().contractNo("12345").build());
        Assertions.assertDoesNotThrow(() -> kerryPayController.kerryPayOrderRefund(
                KerryPayRefundResource.builder().build()));

        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().currentPoints(10).build());
        Assertions.assertDoesNotThrow(() -> kerryPayController.kerryPayOrderRefund(
                KerryPayRefundResource.builder().build()));

        Mockito.when(tbPointsDetailService.queryPointsRecordByConditions(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.emptyList());
        Assertions.assertDoesNotThrow(() -> kerryPayController.kerryPayOrderRefund(
                KerryPayRefundResource.builder().build()));

        Mockito.when(tbPointsDetailService.queryPointsRecordByConditions(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(TbPointsDetail.builder().reasonType(PointsRedemptionEnum.KERRY_PAY_CASH_OUT_REFUND.getCode()).build()));
        Assertions.assertDoesNotThrow(() -> kerryPayController.kerryPayOrderRefund(
                KerryPayRefundResource.builder().build()));

        Mockito.when(tbPointsDetailService.queryPointsRecordByConditions(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(TbPointsDetail.builder().build()));
        Assertions.assertDoesNotThrow(() -> kerryPayController.kerryPayOrderRefund(
                KerryPayRefundResource.builder().build()));

        Mockito.when(tbPointsDetailService.queryPointsRecordByConditions(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(TbPointsDetail.builder().amount(100).reasonType(PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode()).build()));
        Assertions.assertDoesNotThrow(() -> kerryPayController.kerryPayOrderRefund(
                KerryPayRefundResource.builder().build()));

        Mockito.when(tbPointsDetailService.queryPointsRecordByConditions(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(TbPointsDetail.builder().amount(0).reasonType(PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode()).build()));
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(false);
        Assertions.assertThrows(Exception.class, () -> kerryPayController.kerryPayOrderRefund(
                KerryPayRefundResource.builder().build()));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Assertions.assertDoesNotThrow(() -> kerryPayController.kerryPayOrderRefund(
                KerryPayRefundResource.builder().build()));

    }

}
