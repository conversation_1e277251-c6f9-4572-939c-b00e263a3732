package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.client.HiveServiceClient;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig;
import com.kerryprops.kip.service.integral.model.dto.HiveLbsInfoDto;
import com.kerryprops.kip.service.integral.model.dto.LbsIWithProjectIdDto;
import com.kerryprops.kip.service.integral.model.dto.LbsItemDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.SysDictService;
import com.kerryprops.kip.service.integral.service.TbAutoPointsConfigService;
import com.kerryprops.kip.service.integral.webservice.SysDictController;
import com.kerryprops.kip.service.integral.webservice.TbAutoPointsConfigController;
import com.kerryprops.kip.service.integral.webservice.resource.TbAutoPointsConfigResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbAutoPointsConfigSaveOrUpdateResource;
import com.kerryprops.kip.service.integral.webservice.response.SysDictResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Bert
 * Created Date - 07/31/2024 11:14
 **********************************************************************************************************************/

@Slf4j
class TbAutoPointsConfigControllerTest {

    @InjectMocks
    private TbAutoPointsConfigController tbAutoPointsConfigController;

    @Mock
    private TbAutoPointsConfigService tbAutoPointsConfigService;
    @Mock
    private HiveServiceClient hiveServiceClient;
    @Mock
    private HiveVasClient hiveVasClient;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private Mapper mapper;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void saveConfig() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        TbAutoPointsConfigSaveOrUpdateResource resource = TbAutoPointsConfigSaveOrUpdateResource.builder().build();
        Mockito.when(mapper.map(resource, TbAutoPointsConfig.class)).thenReturn(TbAutoPointsConfig.builder().build());
        Mockito.when(tbAutoPointsConfigService.saveConfig(Mockito.any())).thenReturn(TbAutoPointsConfig.builder().build());
        Mockito.when(mapper.map(TbAutoPointsConfig.builder().build(), TbAutoPointsConfigResponse.class)).thenReturn(TbAutoPointsConfigResponse.builder().build());
        Assertions.assertNotNull(tbAutoPointsConfigController.saveConfig(loginUser, resource));
    }

    @Test
    void updateConfig() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        TbAutoPointsConfigSaveOrUpdateResource resource = TbAutoPointsConfigSaveOrUpdateResource.builder().build();
        Mockito.when(mapper.map(resource, TbAutoPointsConfig.class)).thenReturn(TbAutoPointsConfig.builder().build());
        Mockito.when(tbAutoPointsConfigService.updateConfig(Mockito.any())).thenReturn(TbAutoPointsConfig.builder().build());
        Mockito.when(mapper.map(TbAutoPointsConfig.builder().build(), TbAutoPointsConfigResponse.class)).thenReturn(TbAutoPointsConfigResponse.builder().build());
        Assertions.assertNotNull(tbAutoPointsConfigController.updateConfig(loginUser, resource));
    }

    @Test
    void removeConfig() {
        Assertions.assertNotNull(tbAutoPointsConfigController.removeConfig(Mockito.any()));
    }

    @Test
    void pageData() {
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(true);
        Mockito.when(mallConfig.getByLbsIds(Mockito.any())).thenReturn(Collections.singletonList(MallItem.builder().projectId("120").build()));
        Mockito.when(tbAutoPointsConfigService.total(Mockito.any(), Mockito.any())).thenReturn(0);
        Assertions.assertNotNull(tbAutoPointsConfigController.pageData("", "", 1, 1));

        Mockito.when(tbAutoPointsConfigService.total(Mockito.any(), Mockito.any())).thenReturn(10);
        Assertions.assertNotNull(tbAutoPointsConfigController.pageData("", "", 1, 1));

        Mockito.when(tbAutoPointsConfigService.list(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TbAutoPointsConfig.builder().build()));
        Mockito.when(hiveVasClient.getLbsInfo(Mockito.any()))
                .thenReturn(Collections.singletonList(HiveLbsInfoDto.builder().lbs(LbsItemDto.builder().id("01").name("01").build()).build()));
        Mockito.when(mapper.map(TbAutoPointsConfig.builder().build(), TbAutoPointsConfigResponse.class))
                .thenReturn(TbAutoPointsConfigResponse.builder().build());
        Assertions.assertNotNull(tbAutoPointsConfigController.pageData("", "", 1, 1));


        // isKO为false
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(false);
        Mockito.when(tbAutoPointsConfigService.total(Mockito.any(), Mockito.any())).thenReturn(10);
        Assertions.assertNotNull(tbAutoPointsConfigController.pageData("", "", 1, 1));
    }

    @Test
    void getBrandLbsInfo() {
        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(true);
        Assertions.assertNotNull(tbAutoPointsConfigController.getBrandLbsInfo("123", "123"));

        Mockito.when(mallConfig.getByLbsIds(Mockito.any())).thenReturn(Collections.singletonList(MallItem.builder().projectId("120").build()));
        Mockito.when(hiveServiceClient.getLbsListByProjectIds(Mockito.any())).thenReturn(Collections.singletonList(LbsIWithProjectIdDto.builder().projectId("120").build()));
        Assertions.assertNotNull(tbAutoPointsConfigController.getBrandLbsInfo("123", "123"));

        Mockito.when(mallConfig.isKO(Mockito.any())).thenReturn(false);
        Mockito.when(hiveServiceClient.getLbsListByBrandId(Mockito.any())).thenReturn(Collections.singletonList(LbsIWithProjectIdDto.builder().projectId("120").build()));
        Assertions.assertNotNull(tbAutoPointsConfigController.getBrandLbsInfo("123", "123"));
    }

    @Test
    void checkAutoPointEntry() {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Assertions.assertNotNull(tbAutoPointsConfigController.checkAutoPointEntry(request));

        Mockito.when(request.getHeader(Mockito.any())).thenReturn("123");
        Mockito.when(tbAutoPointsConfigService.findByLbsId(Mockito.any())).thenReturn(TbAutoPointsConfig.builder().build());
        Assertions.assertNotNull(tbAutoPointsConfigController.checkAutoPointEntry(request));
    }
}
