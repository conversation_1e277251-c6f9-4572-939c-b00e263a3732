package com.points.webservice;

import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbOpenCardFailRecord;
import com.kerryprops.kip.service.integral.model.dto.CustomerThirdPartyDto;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbOpenCardFailRecordController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;


/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Nancy
 **********************************************************************************************************************/

@Slf4j
class TbOpenCardFailRecordControllerTest {

    @InjectMocks
    private TbOpenCardFailRecordController tbOpenCardFailRecordController;

    @Mock
    private TbOpenCardFailRecordService tbOpenCardFailRecordService;
    @Mock
    private TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    @Mock
    private MallConfig mallConfig;
    @Mock
    private KerryStaffService kerryStaffService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getSecondMemberTagList() {
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbOpenCardFailRecordController.checkAndInsertRecord("", ""));
    }

    @Test
    void getSecondMemberTagListCase1() {
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbOpenCardFailRecordController.checkAndInsertRecord("", ""));
    }

    @Test
    void getSecondMemberTagListCase2() {
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any()))
                .thenReturn(CustomerUserDto.builder().build());
        Mockito.when(kerryStaffService.getWxInfoByMobileAndAppId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbOpenCardFailRecordController.checkAndInsertRecord("", ""));
    }

    @Test
    void getSecondMemberTagListCase3() {
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any()))
                .thenReturn(CustomerUserDto.builder().build());
        Mockito.when(kerryStaffService.getWxInfoByMobileAndAppId(Mockito.any(), Mockito.any()))
                .thenReturn(new CustomerThirdPartyDto());
        Mockito.when(tbOpenCardFailRecordService.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbOpenCardFailRecordController.checkAndInsertRecord("", ""));
    }

    @Test
    void getSecondMemberTagListCase4() {
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any()))
                .thenReturn(CustomerUserDto.builder().build());
        Mockito.when(kerryStaffService.getWxInfoByMobileAndAppId(Mockito.any(), Mockito.any()))
                .thenReturn(new CustomerThirdPartyDto());
        Mockito.when(tbOpenCardFailRecordService.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbOpenCardFailRecord.builder().build());
        Mockito.when(tbInsensatePointsAuthRecordService.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbInsensatePointsAuthRecord.builder().build());
        Assertions.assertNotNull(tbOpenCardFailRecordController.checkAndInsertRecord("", ""));
    }

    @Test
    void getSecondMemberTagListCase5() {
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any()))
                .thenReturn(CustomerUserDto.builder().build());
        Mockito.when(kerryStaffService.getWxInfoByMobileAndAppId(Mockito.any(), Mockito.any()))
                .thenReturn(new CustomerThirdPartyDto());
        Mockito.when(tbOpenCardFailRecordService.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbOpenCardFailRecord.builder().build());
        Mockito.when(tbInsensatePointsAuthRecordService.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbOpenCardFailRecordController.checkAndInsertRecord("", ""));
    }

    @Test
    void getSecondMemberTagListCase6() {
        Mockito.when(mallConfig.getByMallId(Mockito.any()))
                .thenReturn(MallItem.builder().build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any()))
                .thenReturn(CustomerUserDto.builder().build());
        Mockito.when(kerryStaffService.getWxInfoByMobileAndAppId(Mockito.any(), Mockito.any()))
                .thenReturn(new CustomerThirdPartyDto());
        Mockito.when(tbOpenCardFailRecordService.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(TbOpenCardFailRecord.builder().content("{\"code\":10247801,\"authType\":\"C\",\"mchId\": \"B**t\"}").build());
        Mockito.when(tbInsensatePointsAuthRecordService.queryByOpenIdAndMallId(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        Assertions.assertNotNull(tbOpenCardFailRecordController.checkAndInsertRecord("", ""));
    }

}
