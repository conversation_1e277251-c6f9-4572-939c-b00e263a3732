package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.IntegralAdjustController;
import com.kerryprops.kip.service.integral.webservice.resource.IntegralAdjustResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.validation.BindException;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/14/2022 17:27
 **********************************************************************************************************************/

@Slf4j
class IntegralAdjustControllerTest {

    @InjectMocks
    private IntegralAdjustController integralAdjustController;
    @Mock
    private TbPointsDetailService tbPointsDetailService;
    @Mock
    private Mapper mapper;
    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private TbSetshoprateService tbSetshoprateService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private SysDictService sysDictService;
    @Mock
    private RedisService redisService;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void integralRecordList() {
        Mockito.when(tbPointsDetailService.integralRecordTotal(Mockito.any(),Mockito.any()))
                .thenReturn(0);
        Assertions.assertNotNull(integralAdjustController.integralRecordList("", "", 10, 2));

        Mockito.when(tbPointsDetailService.integralRecordTotal(Mockito.any(),Mockito.any()))
                .thenReturn(10);
        Mockito.when(tbPointsDetailService.integralRecordList(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(null);
        Assertions.assertNotNull(integralAdjustController.integralRecordList("", "", 10, 2));

        Mockito.when(tbPointsDetailService.integralRecordList(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(Collections.singletonList(TbPointsDetail.builder().orderNo("123").build()));
        Map<String, TbSalesDetail> map = new HashMap<>();
        map.put("123", TbSalesDetail.builder().orderNo("123").build());
        Mockito.when(tbSalesDetailService.getSalesList(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(map);
        Assertions.assertNotNull(integralAdjustController.integralRecordList("", "", 10, 2));
    }

    @Test
    @DisplayName("支付")
    void integralAdjustSave() {
        IntegralAdjustResource resource = IntegralAdjustResource
                .builder()
                .id("78933")
                .mallId("mallId")
                .remark(IntegralConstant.TAKE_PHOTO)
                .shopNo("shopNo")
                .build();
        LoginUser loginUser = LoginUser.builder().fromType("IPAD").build();
        Assertions.assertThrows(BizNotFoundException.class, () -> integralAdjustController.integralAdjustSave(loginUser, resource));

        Mockito.when(tbMemberAssetService.getMemberAllInfoById(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(sysDictService.findByDictType(Mockito.any())).thenReturn(SysDict.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(false);
        Assertions.assertThrows(BizException.class, () -> integralAdjustController.integralAdjustSave(loginUser, resource));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any())).thenReturn(100);
        Assertions.assertNotNull(integralAdjustController.integralAdjustSave(loginUser, resource));
    }

    @Test
    @DisplayName("有单退款")
    void integralAdjustSaveCase1() {
        IntegralAdjustResource resource = IntegralAdjustResource
                .builder()
                .id("78933")
                .mallId("mallId")
                .remark(IntegralConstant.ORDER_REFUND)
                .shopNo("shopNo")
                .build();
        LoginUser loginUser = LoginUser.builder().fromType("IPAD").build();
        Mockito.when(tbMemberAssetService.getMemberAllInfoById(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(sysDictService.findByDictType(Mockito.any())).thenReturn(SysDict.builder().build());
        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(false);
        Assertions.assertThrows(BizException.class, () -> integralAdjustController.integralAdjustSave(loginUser, resource));

        Mockito.when(redisService.setSaleNoIfAbsent(Mockito.any(), Mockito.any())).thenReturn(true);
        Assertions.assertThrows(BizException.class, () -> integralAdjustController.integralAdjustSave(loginUser, resource));

        IntegralAdjustResource resource1 = IntegralAdjustResource
                .builder()
                .id("78933")
                .mallId("mallId")
                .remark(IntegralConstant.ORDER_REFUND)
                .shopNo("shopNo")
                .refundMoney("100")
                .build();
        Mockito.when(memberSalePointsProcessService.salePointsProcess(Mockito.any())).thenReturn(100);
        Assertions.assertNotNull(integralAdjustController.integralAdjustSave(loginUser, resource1));
    }

    @Test
    @DisplayName("无单退款")
    void integralAdjustSaveCase2() {
        IntegralAdjustResource resource = IntegralAdjustResource
                .builder()
                .id("78933")
                .mallId("mallId")
                .remark(IntegralConstant.NO_ORDER_REFUND)
                .shopNo("shopNo")
                .useMoney(BigDecimal.ONE)
                .build();

        Mockito.when(tbMemberAssetService.getMemberAllInfoById(Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(sysDictService.findByDictType(Mockito.any())).thenReturn(SysDict.builder().build());
        Mockito.when(tbSetshoprateService.salesToIntegral(Mockito.any())).thenReturn(100);
        LoginUser loginUser = LoginUser.builder().fromType("IPAD").build();
        Assertions.assertNotNull(integralAdjustController.integralAdjustSave(loginUser, resource));


        IntegralAdjustResource resource1 = IntegralAdjustResource
                .builder()
                .id("78933")
                .mallId("mallId")
                .remark("12233")
                .shopNo("shopNo")
                .useMoney(BigDecimal.ONE)
                .build();
        Assertions.assertThrows(BizException.class, () -> integralAdjustController.integralAdjustSave(loginUser, resource1));
    }
}
