package com.points.webservice;

import com.kerryprops.kip.pmw.client.resource.BizCircleParkingNotifyInputResource;
import com.kerryprops.kip.pmw.client.resource.PointsActivationConfirmResource;
import com.kerryprops.kip.pmw.client.resource.PointsPaymentConfirmResource;
import com.kerryprops.kip.pmw.client.resource.PointsRefundConfirmResource;
import com.kerryprops.kip.service.integral.common.enums.EventTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.WxAuthTypeEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsPushRecord;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.WeChatPayController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/14/2022 17:27
 **********************************************************************************************************************/

@Slf4j
class WeChatControllerTest {

    @InjectMocks
    WeChatPayController weChatPayController;
    @Mock
    private WechatAccelerateIntegralService wechatAccelerateIntegralService;
    @Mock
    private TbInsensatePointsPushRecordService tbInsensatePointsPushRecordService;
    @Mock
    private TbOpenCardFailRecordService tbOpenCardFailRecordService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private MallConfig mallConfig;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void memberPointsAuth() {
        PointsActivationConfirmResource pointsActivationConfirmResource = new PointsActivationConfirmResource(null, new PointsActivationConfirmResource.PointsActivationConfirmBodyResource(), null);
        PointsActivationConfirmResource.PointsActivationConfirmBodyResource bodyResource = new PointsActivationConfirmResource.PointsActivationConfirmBodyResource();
        bodyResource.setCreateTime(new Date().toString());
        bodyResource.setEventType(EventTypeEnum.MALL_AUTH.getName());
        PointsActivationConfirmResource.PointsActivationDetailResource detailResource = new PointsActivationConfirmResource.PointsActivationDetailResource();
        detailResource.setAuthType(WxAuthTypeEnum.REGISTERED_AND_AUTHORIZATION_MODE.getValue());
        detailResource.setCode("code");
        detailResource.setMchId("mchId");
        detailResource.setOpenId("openId");
        bodyResource.setResource(detailResource);
        pointsActivationConfirmResource.setBody(bodyResource);
        Assertions.assertNotNull(weChatPayController.memberPointsAuth(pointsActivationConfirmResource));
    }

    @Test
    void payCallback() {
        PointsPaymentConfirmResource.PointsPaymentDetailResource detailResource = new PointsPaymentConfirmResource.PointsPaymentDetailResource();
        detailResource.setAmount(100);
        detailResource.setOpenId("openId");
        detailResource.setAppId("appId");
        detailResource.setMchId("mchId");
        detailResource.setTransactionId("transactionId");
        detailResource.setShopNumber("shopNumber");
        PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource bodyResource = new PointsPaymentConfirmResource.PointsPaymentConfirmBodyResource();
        bodyResource.setCreateTime("");
        bodyResource.setEventType(EventTypeEnum.TRANS_SUCC.getName());
        bodyResource.setResource(detailResource);
        PointsPaymentConfirmResource pointsPaymentConfirmResource = new PointsPaymentConfirmResource(null, bodyResource, null);
        Mockito.when(mallConfig.getByMchId(Mockito.anyString())).thenReturn(MallItem.builder().build());
        Mockito.when(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(TbMemberAsset.builder().build());
        Assertions.assertNotNull(weChatPayController.payCallback(pointsPaymentConfirmResource));
    }

    @Test
    void refundCallback() {
        PointsRefundConfirmResource.PointsRefundDetailResource detailResource = new PointsRefundConfirmResource.PointsRefundDetailResource();
        detailResource.setRefundAmount(100);
        detailResource.setOpenId("openId");
        detailResource.setAppId("appId");
        detailResource.setMchId("mchId");
        detailResource.setTransactionId("transactionId");
        detailResource.setShopNumber("shopNumber");
        PointsRefundConfirmResource.PointsRefundConfirmBodyResource bodyResource = new PointsRefundConfirmResource.PointsRefundConfirmBodyResource();
        bodyResource.setCreateTime("");
        bodyResource.setEventType(EventTypeEnum.TRANS_SUCC.getName());
        bodyResource.setResource(detailResource);
        PointsRefundConfirmResource pointsPaymentConfirmResource = new PointsRefundConfirmResource(null, bodyResource, null);
        Assertions.assertNotNull(weChatPayController.refundCallback(pointsPaymentConfirmResource));
    }

    @Test
    void memberCardOpenEvent() {
        PointsActivationConfirmResource pointsActivationConfirmResource = new PointsActivationConfirmResource(null, new PointsActivationConfirmResource.PointsActivationConfirmBodyResource(), null);
        PointsActivationConfirmResource.PointsActivationConfirmBodyResource bodyResource = new PointsActivationConfirmResource.PointsActivationConfirmBodyResource();
        bodyResource.setCreateTime(new Date().toString());
        bodyResource.setEventType(EventTypeEnum.MALL_AUTH.getName());
        PointsActivationConfirmResource.PointsActivationDetailResource detailResource = new PointsActivationConfirmResource.PointsActivationDetailResource();
        detailResource.setAuthType(WxAuthTypeEnum.REGISTERED_AND_AUTHORIZATION_MODE.getValue());
        detailResource.setCode("code");
        detailResource.setMchId("mchId");
        detailResource.setOpenId("openId");
        bodyResource.setResource(detailResource);
        pointsActivationConfirmResource.setBody(bodyResource);
        Assertions.assertNotNull(weChatPayController.memberCardOpenEvent(pointsActivationConfirmResource));
    }

    @Test
    void syncParking() {
        BizCircleParkingNotifyInputResource resource = new BizCircleParkingNotifyInputResource(null, null, null);
        Assertions.assertNotNull(weChatPayController.syncParking(resource));
    }

    @Test
    @DisplayName("添加场景")
    void syncAliPay() {
        Assertions.assertNotNull(weChatPayController.syncAliPay());
    }

    @Test
    void queryByOrderId() {
        Mockito.when(tbInsensatePointsPushRecordService.findByTransactionId(Mockito.anyString())).thenReturn(TbInsensatePointsPushRecord.builder().build());
        Assertions.assertNotNull(weChatPayController.queryByOrderId(Mockito.anyString()));
    }

}
