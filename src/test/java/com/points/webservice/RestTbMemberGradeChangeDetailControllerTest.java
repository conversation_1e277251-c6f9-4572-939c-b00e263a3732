package com.points.webservice;

import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.RestTbMemberGradeChangeDetailController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@Slf4j
class RestTbMemberGradeChangeDetailControllerTest {

    @InjectMocks
    private RestTbMemberGradeChangeDetailController restTbMemberGradeChangeDetailController;

    @Mock
    private TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("HKC情况，vipcode不为空，车牌号为空")
    void getMemberLastGradeChangeDetail() {
        when(tbMemberGradeChangeDetailService.queryChangeMaxByGroupIdAndVipcode(Mockito.anyString(), Mockito.anyString())).thenReturn(TbMemberGradeChangeDetail.builder().build());
        Assertions.assertNotNull(restTbMemberGradeChangeDetailController.getMemberLastGradeChangeDetail(Mockito.anyString(), Mockito.anyString()));
    }
}
