package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbMemberAssetController;
import com.kerryprops.kip.service.integral.webservice.resource.*;
import com.kerryprops.kip.service.integral.webservice.response.CrmMemberResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbMemberAllResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbMemberDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 03/15/2024 10:51
 **********************************************************************************************************************/
@Slf4j
class TbMemberAssetControllerTest {

    @InjectMocks
    private TbMemberAssetController tbMemberAssetController;

    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private TbMemberPointsChangeService tbMemberPointsChangeService;
    @Mock
    private TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    @Mock
    private TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    @Mock
    private KerryStaffService kerryStaffService;
    @Mock
    private TbPointsDetailService tbPointsDetailService;
    @Mock
    private TbSalesDetailService tbSalesDetailService;
    @Mock
    private CrmVipcodeService crmVipcodeService;
    @Mock
    private TbPointsExpiredReminderService tbPointsExpiredReminderService;
    @Mock
    private TbCardMemberRelationService tbCardMemberRelationService;
    @Mock
    private SysDictService sysDictService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private Mapper mapper;
    @Mock
    private MallConfig mallConfig;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getMember() {
        LoginUser user = LoginUser.builder().brandId("4028e3817c2b3f79017c2b48c54c0000").cId("2c9d85bc8489e0ba01849e4629760003").build();
        when(tbMemberAssetService.getLoginMemberInfo(any())).thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.getMember(user));

        when(tbMemberAssetService.getLoginMemberInfo(any())).thenReturn(TbMemberResponse.builder().build());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.getMember(user));
    }

    @Test
    void getCrmMemberInfo() {
        when(memberRegisterService.findByGroupIdAndKipUserId(any(), any()))
                .thenReturn(TbMemberAsset.builder().build());
        when(mapper.map(TbMemberAsset.builder().build(), CrmMemberResponse.class)).thenReturn(CrmMemberResponse.builder().build());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.getCrmMemberInfo("", ""));

        when(memberRegisterService.findByGroupIdAndKipUserId(any(), any()))
                .thenReturn(null);
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.getCrmMemberInfo("", ""));

    }

    @Test
    void checkMembersByKipUserId() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.checkMembersByKipUserId(""));

        Mockito.when(memberRegisterService.findByKipUserId(Mockito.anyString()))
                .thenReturn(Collections.singletonList(TbMemberAsset.builder().build()));
        Mockito.when(mapper.map(TbMemberAsset.builder().build(), CrmMemberResponse.class))
                .thenReturn(CrmMemberResponse.builder().build());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.checkMembersByKipUserId(""));
    }

    @Test
    void testGetMember() {
        LoginUser loginUser = LoginUser.builder().lbsId("8aaa80b47c784020017c78b00d060022").phoneNumber("16691027090").build();
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.getMember(loginUser));
    }

    @Test
    void getMemberStatus() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.getMemberStatus("4028e3817c2b3f79017c2b48c54c0000", "2c9d85a18498fd2e01849e8059260001"));

        Mockito.when(tbMemberAssetService.findByKipUserIdAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(mapper.map(TbMemberAsset.builder().build(), TbMemberDetailResponse.class))
                .thenReturn(new TbMemberDetailResponse());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.getMemberStatus("4028e3817c2b3f79017c2b48c54c0000", "2c9d85a18498fd2e01849e8059260001"));
    }

    @Test
    void testUpdateMemberPoints() {
        MemberPointsChangeResource resource = MemberPointsChangeResource.builder()
                .groupId("4028e3817c2b3f79017c2b48c54c0000")
                .mallId("8aaa80b47c784020017c78b00d060022")
                .vipcode("KERRY100213686")
                .changePointsNum(1)
                .build();
        when(mapper.map(resource, MemberPointsChangeDto.class)).thenReturn(MemberPointsChangeDto.builder().build());
        when(tbMemberPointsChangeService.updateMemberPoints(MemberPointsChangeDto.builder().build())).thenReturn("");
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.updateMemberPoints(resource));
    }

    @Test
    void testMemberIntegralAdjustRecord() {
        PageRequest page = PageRequest.of(1, 10);
        LoginUser loginUser = LoginUser.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("16691027090")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .cId("2c9d85bc8489e0ba01849e46297600092")
                .build();
        Integer type = 1;
        Assertions.assertNotNull(tbMemberAssetController.getMemberIntegralAdjustRecord(loginUser, type, page));

        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberIntegralAdjustRecord(loginUser, type, page));

        Mockito.when(tbMemberAssetService.findByKipUserIdAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberIntegralAdjustRecord(loginUser, type, page));

        Mockito.when(tbPointsDetailService.getCount(Mockito.any()))
                .thenReturn(100);
        Mockito.when(tbPointsDetailService.recordList(Mockito.any()))
                .thenReturn(Collections.singletonList(TbPointsDetail.builder().orderNo("12345").build()));
        Map<String, TbSalesDetail> salesDetailMap = new HashMap<>();
        salesDetailMap.put("12345", TbSalesDetail.builder().orderNo("12345").shopNo("jako00203").build());
        Mockito.when(tbSalesDetailService.findSalesMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(CompletableFuture.completedFuture(salesDetailMap));
        Mockito.when(tbPointsDetailService.getMonthlyIntegral(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList()))
                .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
        Mockito.when(sysDictService.findDictNames(Mockito.anyList()))
                .thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        Mockito.when(hiveVasService.getMallName(Mockito.anyList()))
                .thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        Assertions.assertNotNull(tbMemberAssetController.getMemberIntegralAdjustRecord(loginUser, type, page));

        Mockito.when(tbPointsDetailService.recordList(Mockito.any()))
                .thenReturn(Collections.singletonList(TbPointsDetail.builder().orderNo("12345").moon("12").build()));
        Assertions.assertNotNull(tbMemberAssetController.getMemberIntegralAdjustRecord(loginUser, type, page));

    }

    @Test
    void testPointsDetail() {
        PageRequest page = PageRequest.of(1, 10);
        Assertions.assertNotNull(tbMemberAssetController.getPointsDetail("0001", 1, page));

        Mockito.when(tbCardMemberRelationService.findByExternalCardNo(Mockito.any())).thenReturn(TbCardMemberRelation.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getPointsDetail("0001", 1, page));
    }

    @Test
    void testMemberClearIntegralNum() {
        LoginUser loginUser = LoginUser.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("16691027090")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .cId("2c9d85a18498fd2e01849e8059260001")
                .build();
        Assertions.assertNotNull(tbMemberAssetController.getMemberClearIntegralNum(loginUser));

        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberClearIntegralNum(loginUser));

        Mockito.when(tbPointsExpiredReminderService.getPointsClearMessage(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(IntegralClearShowDto.builder().isShow(0).build());
        Assertions.assertNull(tbMemberAssetController.getMemberClearIntegralNum(loginUser));
    }

    @Test
    void getPointsClearMessage() {
        LoginUser loginUser = LoginUser.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("16691027090")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .cId("2c9d85a18498fd2e01849e8059260001")
                .build();
        when(crmVipcodeService.getVipcode(any(String.class), any(String.class))).thenReturn("KERRY100213689");
        Assertions.assertNull(tbMemberAssetController.getPointsClearMessage(loginUser));
    }

    @Test
    void getMemberByGroupIdAndKipUserId() {
        when(memberRegisterService.findByGroupIdAndKipUserId(any(String.class), any(String.class)))
                .thenReturn(TbMemberAsset.builder().kipUserId("2c9d85bc8489e0ba01849e4629760003").currentPoints(100).build());
        when(kerryStaffService.findByKipUserId(any(String.class))).thenReturn(CustomerUserDto.builder().id("2c9d85bc8489e0ba01849e4629760003").build());
        when(mapper.map(TbMemberAsset.builder().kipUserId("2c9d85bc8489e0ba01849e4629760003").currentPoints(100).build(), TbMemberWithProfileResponse.class)).thenReturn(TbMemberWithProfileResponse.builder().kipUserId("2c9d85bc8489e0ba01849e4629760003").build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberByGroupIdAndKipUserId("4028e3817c2b3f79017c2b48c54c0000", "2c9d85bc8489e0ba01849e4629760003", 0));
    }

    @Test
    void getMemberByGroupIdAndKipUserId1() {
        when(memberRegisterService.findByGroupIdAndKipUserId(any(String.class), any(String.class)))
                .thenReturn(TbMemberAsset.builder().kipUserId("2c9d85bc8489e0ba01849e4629760003").currentPoints(100).build());
        when(kerryStaffService.findByKipUserId(any(String.class))).thenReturn(CustomerUserDto.builder().id("2c9d85bc8489e0ba01849e4629760003").build());
        when(mapper.map(TbMemberAsset.builder().kipUserId("2c9d85bc8489e0ba01849e4629760003").currentPoints(100).build(), TbMemberWithProfileResponse.class)).thenReturn(TbMemberWithProfileResponse.builder().kipUserId("2c9d85bc8489e0ba01849e4629760003").build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberByGroupIdAndKipUserId("4028e3817c2b3f79017c2b48c54c0000", "2c9d85bc8489e0ba01849e4629760003", 1));
    }

    @Test
    void getMemberCurrent() {
        LoginUser loginUser = LoginUser.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("16691027090")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .cId("2c9d85a18498fd2e01849e8059260001")
                .build();
        when(memberRegisterService.findByGroupIdAndKipUserId(any(String.class), any(String.class))).thenReturn(TbMemberAsset.builder().build());
        when(kerryStaffService.findByMobile(any(String.class))).thenReturn(CustomerUserDto.builder().build());
        when(mapper.map(TbMemberAsset.builder().build(), TbMemberAllResponse.class)).thenReturn(TbMemberAllResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberCurrent(loginUser, "4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void getMemberDetail() {
        when(memberRegisterService.findByGroupIdAndKipUserId("4028e3817c2b3f79017c2b48c54c0000", "2c9d85a18498fd2e01849e8059260001"))
                .thenReturn(TbMemberAsset.builder().kipUserId("2c9d85a18498fd2e01849e8059260001").build());
        when(kerryStaffService.findByMobile(any(String.class))).thenReturn(CustomerUserDto.builder().id("2c9d85a18498fd2e01849e8059260001").build());
        when(mapper.map(TbMemberAsset.builder().kipUserId("2c9d85a18498fd2e01849e8059260001").build(), TbMemberAllResponse.class)).thenReturn(TbMemberAllResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberDetail("4028e3817c2b3f79017c2b48c54c0000", "2c9d85a18498fd2e01849e8059260001"));
    }

    @Test
    void getMemberByGroupIdAndVipcode() {
        when(tbMemberAssetService.findByVipcodeAndGroupId(any(String.class), any(String.class))).thenReturn(TbMemberAsset.builder().build());
        when(kerryStaffService.findByMobile(any(String.class))).thenReturn(CustomerUserDto.builder().build());
        when(mapper.map(TbMemberAsset.builder().build(), TbMemberWithProfileResponse.class)).thenReturn(TbMemberWithProfileResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberByGroupIdAndVipcode("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213686", 1));
    }

    @Test
    void getMemberByGroupIdAndVipcode0() {
        when(tbMemberAssetService.findByVipcodeAndGroupId(any(String.class), any(String.class))).thenReturn(TbMemberAsset.builder().build());
        when(kerryStaffService.findByMobile(any(String.class))).thenReturn(CustomerUserDto.builder().build());
        when(mapper.map(TbMemberAsset.builder().build(), TbMemberWithProfileResponse.class)).thenReturn(TbMemberWithProfileResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberByGroupIdAndVipcode("4028e3817c2b3f79017c2b48c54c0000", "KERRY100213686", 0));
    }

    @Test
    void getMemberByGroupIdAndMobile() {
        when(tbMemberAssetService.findByMobileAndGroupId(any(String.class), any(String.class))).thenReturn(TbMemberAsset.builder().build());
        when(kerryStaffService.findByMobile(any(String.class))).thenReturn(CustomerUserDto.builder().build());
        when(mapper.map(TbMemberAsset.builder().build(), TbMemberWithProfileResponse.class)).thenReturn(TbMemberWithProfileResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberByGroupIdAndMobile("4028e3817c2b3f79017c2b48c54c0000", "16691027090", 1));
    }

    @Test
    void getMemberByGroupIdAndMobile0() {
        when(tbMemberAssetService.findByMobileAndGroupId(any(String.class), any(String.class))).thenReturn(TbMemberAsset.builder().build());
        when(kerryStaffService.findByMobile(any(String.class))).thenReturn(CustomerUserDto.builder().build());
        when(mapper.map(TbMemberAsset.builder().build(), TbMemberWithProfileResponse.class)).thenReturn(TbMemberWithProfileResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberByGroupIdAndMobile("4028e3817c2b3f79017c2b48c54c0000", "16691027090", 0));
    }

    @Test
    void getMemberById() {
        when(tbMemberAssetService.getMemberById(any(String.class)))
                .thenReturn(TbMemberAsset.builder().currentPoints(-100).build());
        when(kerryStaffService.findByMobile(any(String.class))).thenReturn(CustomerUserDto.builder().build());
        when(mapper.map(TbMemberAsset.builder().currentPoints(0).build(), TbMemberWithProfileResponse.class)).thenReturn(TbMemberWithProfileResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberById("1699"));
    }

    @Test
    void updateRegister() {
        LoginUser loginUser = LoginUser.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("16691027090")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .cId("2c9d85a18498fd2e01849e8059260001")
                .build();
        MemberRegisterSourceResource resource = MemberRegisterSourceResource.builder().build();
        when(mapper.map(resource, MemberRegisterResourceDto.class)).thenReturn(MemberRegisterResourceDto.builder().build());
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.updateRegister(loginUser, resource));
    }

    @Test
    void kipSyncRegister() {
        KipSyncMemberResource resource = KipSyncMemberResource.builder()
                .lbsId("8aaa80b47c784020017c78b00d060022")
                .phoneNumber("16691027090")
                .brandId("4028e3817c2b3f79017c2b48c54c0000")
                .build();
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.kipSyncRegister(resource));
    }

    @Test
    void profileMobileModify() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.profileMobileModify("2c9d85bc8489e0ba01849e4629760003", "16691027090"));
    }

    @Test
    void brandMemberDelete() {
        BrandMemberDeleteResource resource = BrandMemberDeleteResource.builder().groupId("4028e3817c2b3f79017c2b48c54c0000").mobile("16691027090").build();
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.brandMemberDelete(resource));
    }

    @Test
    void kipUserInvalid() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.kipUserInvalid("2c9d85bc8489e0ba01849e4629760003", "16691027090"));
    }

    @Test
    void getMemberProfileInfos() {
        Assertions.assertNotNull(tbMemberAssetController.getMemberProfileInfos(Collections.singletonList("2c9d85bc8489e0ba01849e4629760003")));
    }

    @Test
    void getMemberProfileInfosNew() {
        Assertions.assertNotNull(tbMemberAssetController.getMemberProfileInfos(Collections.singletonList("KERRY100213686"), "4028e3817c2b3f79017c2b48c54c0000"));
    }

    @Test
    void findAllMemberInfo() {
        // groupId为空
        Assertions.assertThrows(Exception.class, () ->tbMemberAssetController.findAllMemberInfo(SingleMemberQueryResource.builder().build()));

        Assertions.assertThrows(Exception.class, () ->tbMemberAssetController.findAllMemberInfo(SingleMemberQueryResource.builder().groupId("12345").build()));

        Mockito.when(mapper.map(SingleMemberQueryResource.builder().groupId("12345").vipcode("12345").build(), SingleMemberQueryDto.class))
                .thenReturn(SingleMemberQueryDto.builder().groupId("12345").vipcode("12345").build());
        Assertions.assertThrows(Exception.class, () ->tbMemberAssetController.findAllMemberInfo(SingleMemberQueryResource.builder().groupId("12345").vipcode("12345").build()));

        // kipUserId不为空
        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().kipUserId("12345").currentPoints(-1).build());
        Mockito.when(kerryStaffService.findByKipUserId(Mockito.any())).thenReturn(CustomerUserDto.builder().build());
        // 不允许积分为负
        Mockito.when(mallConfig.isAllowNegativePointsGroup(Mockito.any())).thenReturn(false);
        Mockito.when(mapper.map(TbMemberAsset.builder().currentPoints(0).build(), TbMemberWithProfileResponse.class))
                .thenReturn(TbMemberWithProfileResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.findAllMemberInfo(SingleMemberQueryResource.builder().groupId("12345").vipcode("12345").build()));

        // kipUserId为空
        Mockito.when(tbMemberAssetService.findByDto(Mockito.any())).thenReturn(TbMemberAsset.builder().mobile("12345").currentPoints(-1).build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any())).thenReturn(CustomerUserDto.builder().build());
        // 允许积分为负
        Mockito.when(mallConfig.isAllowNegativePointsGroup(Mockito.any())).thenReturn(true);
        Mockito.when(mapper.map(TbMemberAsset.builder().currentPoints(-1).build(), TbMemberWithProfileResponse.class))
                .thenReturn(TbMemberWithProfileResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.findAllMemberInfo(SingleMemberQueryResource.builder().groupId("12345").vipcode("12345").build()));
    }

    @Test
    void revertMember() {
        Assertions.assertDoesNotThrow(() -> tbMemberAssetController.revertMember("1699"));
    }

    @Test
    void memberSignUp() {
        MemberSignUpResource resource = MemberSignUpResource.builder().build();
        Assertions.assertThrows(BizException.class, () -> tbMemberAssetController.memberSignUp(resource));

        Mockito.when(tbMemberAssetService.memberSignUp(Mockito.any())).thenReturn(TbMemberAsset .builder().build());
        Mockito.when(mapper.map(TbMemberAsset .builder().build(), TbMemberAllResponse.class))
                .thenReturn(TbMemberAllResponse.builder().build());
        Assertions.assertNotNull(tbMemberAssetController.memberSignUp(resource));

        Assertions.assertNotNull(tbMemberAssetController.memberSignUp(MemberSignUpResource.builder().nickName("nickName").build()));
    }

    @Test
    void getMemberDetailByMobile() {
        Mockito.when(tbMemberAssetService.findByMobileAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().currentPoints(-100).build());
        Mockito.when(kerryStaffService.findByMobile(Mockito.any())).thenReturn(CustomerUserDto.builder().build());
        Mockito.when(mapper.map(TbMemberAsset.builder().currentPoints(-100).build(), TbMemberAllResponse.class))
                .thenReturn(TbMemberAllResponse.builder().currentPoints(-100).mobile("19080706756").build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberDetailByMobile("", ""));

        Mockito.when(tbMemberAssetService.findByMobileAndGroupId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().currentPoints(10).kipUserId("12345").build());
        Mockito.when(kerryStaffService.findByKipUserId(Mockito.any())).thenReturn(CustomerUserDto.builder().build());
        Mockito.when(mapper.map(TbMemberAsset.builder().currentPoints(10).build(), TbMemberAllResponse.class))
                .thenReturn(TbMemberAllResponse.builder().mobile("19080706756").build());
        Assertions.assertNotNull(tbMemberAssetController.getMemberDetailByMobile("", ""));
    }

}
