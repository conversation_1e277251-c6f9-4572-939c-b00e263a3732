package com.points.webservice;

import com.kerryprops.kip.service.integral.webservice.HealthController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 11/21/2024 13:34
 **********************************************************************************************************************/

@Slf4j
class HealthControllerTest {

    @InjectMocks
    private HealthController healthController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void health() {
        Assertions.assertNotNull(healthController.health());
    }

}
