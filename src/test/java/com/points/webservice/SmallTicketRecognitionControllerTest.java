package com.points.webservice;

import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.client.AuthServiceClient;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.utils.OcrQrCodeUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.config.SmallTicketProperties;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbOcrCallbackRecord;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.model.dto.CheckJsCodeDto;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.SmallTicketRecognitionController;
import com.kerryprops.kip.service.integral.webservice.resource.BonusSelfUploadResource;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketCallbackResource;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketPointsResource;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketUploadResource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Bert
 * Created Date - 07/31/2024 11:14
 **********************************************************************************************************************/

@Slf4j
class SmallTicketRecognitionControllerTest{

    @InjectMocks
    private SmallTicketRecognitionController smallTicketRecognitionController;

    @Mock
    private MallConfig mallConfig;
    @Mock
    private RedisService redisService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private RabbitMqService rabbitMqService;
    @Mock
    private TbBaseShopService tbBaseShopService;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private SetOperations<String, Object> setOperations;
    @Mock
    private DefaultRedisScript<Long> accessLimitScript;
    @Mock
    private TbMemberAssetService tbMemberAssetService;
    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private TbPhotoReviewService tbPhotoReviewService;
    @Mock
    private SmallTicketProperties smallTicketProperties;
    @Mock
    private TbOcrCallbackRecordService tbOcrCallbackRecordService;
    @Mock
    private SmallTicketRecognitionService smallTicketRecognitionService;
    @Mock
    private MemberSalePointsProcessService memberSalePointsProcessService;
    @Mock
    private AuthServiceClient authServiceClient;
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getPhotoConfig() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        when(hiveVasService.getMallByLbsId(Mockito.anyString())).thenReturn(MallItem.builder().build());
        when(mallConfig.isKO(Mockito.anyString())).thenReturn(true);
        TbMemberAsset memberAsset = TbMemberAsset.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").mallId("222").currentPoints(0).vipcode("KERRY93001").build();
        when(tbMemberAssetService.findByKipUserIdAndGroupId(any(), any())).thenReturn(memberAsset);
        when(redisService.getValue(Mockito.anyString())).thenReturn("6");
        when(redisService.getKeyExpireTime(Mockito.anyString())).thenReturn(1L);
        Assertions.assertNotNull(smallTicketRecognitionController.getPhotoConfig(loginUser, ""));
    }

    @Test
    @DisplayName("重复上传0")
    void smallTicketPointsCase0() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        String appId = "KERRY91003";
        when(smallTicketProperties.getTimeout()).thenReturn(1L);
        when(hiveVasService.getMallByLbsId(Mockito.anyString())).thenReturn(MallItem.builder().build());
        TbMemberAsset memberAsset = TbMemberAsset.builder().groupId("111").mallId("222").currentPoints(0).vipcode("KERRY00001").build();
        when(tbMemberAssetService.findByKipUserIdAndGroupIdWithException(any(), any())).thenReturn(memberAsset);
        when(redisService.getValue(Mockito.anyString())).thenReturn("6");
        SmallTicketUploadResource resource = SmallTicketUploadResource.builder().imgUrl("https://test.jpg").build();
        when(redisService.setIfAbsentWithExpire(any(), any(), any(long.class), any())).thenReturn(Boolean.FALSE);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        when(redisTemplate.opsForSet()).thenReturn(setOperations);
        when(setOperations.isMember(any(), any(Object.class))).thenReturn(Boolean.TRUE);
        when(authServiceClient.checkMiniProgramJsCode(CheckJsCodeDto.builder().appId(appId).jsCode("test0001").build()))
                .thenReturn(KerryResultDto.ok());
        Assertions.assertNotNull(smallTicketRecognitionController.smallTicketUpload(loginUser, resource, appId, request));
    }

    @Test
    @DisplayName("重复上传")
    void smallTicketPointsCase() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        String appId = "KERRY91003";
        when(smallTicketProperties.getTimeout()).thenReturn(1L);
        when(hiveVasService.getMallByLbsId(Mockito.anyString())).thenReturn(MallItem.builder().build());
        TbMemberAsset memberAsset = TbMemberAsset.builder().groupId("111").mallId("222").currentPoints(0).vipcode("KERRY00001").build();
        when(tbMemberAssetService.findByKipUserIdAndGroupIdWithException(any(), any())).thenReturn(memberAsset);
        when(redisService.getValue(Mockito.anyString())).thenReturn("6");
        SmallTicketUploadResource resource = SmallTicketUploadResource.builder().imgUrl("https://test.jpg").build();
        when(redisService.setIfAbsentWithExpire(any(), any(), any(long.class), any())).thenReturn(Boolean.FALSE);
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        when(redisTemplate.opsForSet()).thenReturn(setOperations);
        when(authServiceClient.checkMiniProgramJsCode(CheckJsCodeDto.builder().appId(appId).jsCode("test0001").build()))
                .thenReturn(KerryResultDto.ok());
        Assertions.assertNotNull(smallTicketRecognitionController.smallTicketUpload(loginUser, resource, appId, request));
    }

    @Test
    @DisplayName("failCount大于5")
    void smallTicketPointsCase1() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        String appId = "KERRY91003";
        when(smallTicketProperties.getTimeout()).thenReturn(1L);
        when(hiveVasService.getMallByLbsId(Mockito.anyString())).thenReturn(MallItem.builder().build());
        TbMemberAsset memberAsset = TbMemberAsset.builder().groupId("111").mallId("222").currentPoints(0).vipcode("KERRY00001").build();
        when(tbMemberAssetService.findByKipUserIdAndGroupIdWithException(any(), any())).thenReturn(memberAsset);
        when(redisService.getValue(Mockito.anyString())).thenReturn("6");
        when(redisTemplate.opsForSet()).thenReturn(setOperations);
        when(authServiceClient.checkMiniProgramJsCode(CheckJsCodeDto.builder().appId(appId).jsCode("test0001").build()))
                .thenReturn(KerryResultDto.ok());
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        Assertions.assertNotNull(smallTicketRecognitionController.smallTicketUpload(loginUser, SmallTicketUploadResource.builder().build(), appId, request));
    }

    @Test
    @DisplayName("failCount<5")
    void smallTicketPointsCase2() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        String appId = "KERRY91003";
        when(smallTicketProperties.getTimeout()).thenReturn(1L);
        when(hiveVasService.getMallByLbsId(Mockito.anyString())).thenReturn(MallItem.builder().build());
        TbMemberAsset memberAsset = TbMemberAsset.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").mallId("222").currentPoints(0).vipcode("KERRY1098006").build();
        when(tbMemberAssetService.findByKipUserIdAndGroupIdWithException(any(), any()))
                .thenReturn(memberAsset);
        when(redisService.getValue(Mockito.anyString())).thenReturn("1");
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        when(redisTemplate.opsForSet()).thenReturn(setOperations);
        when(authServiceClient.checkMiniProgramJsCode(CheckJsCodeDto.builder().appId(appId).jsCode("test0001").build()))
                .thenReturn(KerryResultDto.ok());
        Assertions.assertNotNull(smallTicketRecognitionController.smallTicketUpload(loginUser, SmallTicketUploadResource.builder().build(), appId, request));
    }

    @Test
    void testSendFanoutMessage() {
        when(tbOcrCallbackRecordService.findByTaskId(Mockito.anyString())).thenReturn(TbOcrCallbackRecord.builder().build());
        Assertions.assertDoesNotThrow(() -> {
            smallTicketRecognitionController.testSendFanoutMessage(Mockito.anyString());
        });
    }

    @Test
    void getTaskDetail() {
        when(smallTicketRecognitionService.getUploadResult(any())).thenReturn(SmallTicketCallbackResource.builder().build());
        when(tbPhotoReviewService.findByOcrTaskId(Mockito.anyString())).thenReturn(TbPhotoReview.builder().build());
        when(tbOcrCallbackRecordService.findByTaskId(Mockito.anyString())).thenReturn(null);
        Assertions.assertNotNull(smallTicketRecognitionController.getTaskDetail(Mockito.anyString()));
    }

    @Test
    void getTaskDetailByOuterId() {
        String qrCode = OcrQrCodeUtils.simpleQrCode();
        SmallTicketCallbackResource resource = SmallTicketCallbackResource.builder().code(0).taskId(IdUtil.simpleUUID()).outerId(qrCode).ticketNo("Test0001").amount("100.00").isPass(true)
                .isRobot(true).shop("DEMO00001").mall("001").build();
        when(smallTicketRecognitionService.getUploadResultByOuterId(any())).thenReturn(resource);
        when(tbPhotoReviewService.findByQrCode(Mockito.anyString())).thenReturn(TbPhotoReview.builder().build());
        when(tbOcrCallbackRecordService.findByTaskId(Mockito.anyString())).thenReturn(null);
        Assertions.assertNotNull(smallTicketRecognitionController.getTaskDetailByOuterId(Mockito.anyString()));
    }

    @Test
    void smallTicketCallback() {
        SmallTicketCallbackResource resource = SmallTicketCallbackResource
                .builder()
                .taskId("240730_3b29f510c47f4462afb9b02468153280_uat")
                .isPass(Boolean.TRUE)
                .build();
        when(tbOcrCallbackRecordService.findByTaskId(any())).thenReturn(TbOcrCallbackRecord.builder().build());
        Assertions.assertNotNull(smallTicketRecognitionController.smallTicketCallback(resource));
    }

    @Test
    void smallTicketPoints() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        SmallTicketPointsResource resource = SmallTicketPointsResource
                .builder()
                .taskId("240730_3b29f510c47f4462afb9b02468153280_uat")
                .build();
        when(tbPhotoReviewService.findByOcrTaskId(any()))
                .thenReturn(TbPhotoReview.builder().vipcode("KERRY93001").status("6").build());
        when(hiveVasService.getMallByLbsId(any())).thenReturn(MallItem.builder().build());
        when(tbOcrCallbackRecordService.findByTaskId(any())).thenReturn(TbOcrCallbackRecord.builder().build());
        TbMemberAsset memberAsset = TbMemberAsset.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").mallId("222").currentPoints(0).vipcode("KERRY93001").build();
        when(tbMemberAssetService.getAllByGroupIdAndKipUserId(any(), any())).thenReturn(memberAsset);
        when(tbBaseShopService.getByContractNoAndMallId(any(), any())).thenReturn(TbBaseShop.builder().build());
        when(redisService.setSaleNoIfAbsent(any(), any())).thenReturn(Boolean.TRUE);
        when(memberSalePointsProcessService.salePointsProcess(any())).thenReturn(10);
        Assertions.assertNotNull(smallTicketRecognitionController.smallTicketPoints(loginUser, resource));
    }

    @Test
    void smallTicketCancelPoints() {
        LoginUser loginUser = LoginUser.builder().fromType("C").cId("8aaa87bb8051012901805941a9ff0000").phoneNumber("13122957625")
                .brandId("8aaa82ea804d07cd01805174dd3b000c").lbsId("8aaa81cb7c836c6b017c83e2c76f0000").build();
        SmallTicketPointsResource resource = SmallTicketPointsResource
                .builder()
                .taskId("240730_3b29f510c47f4462afb9b02468153280_uat")
                .build();
        when(tbPhotoReviewService.findByOcrTaskId(any()))
                .thenReturn(TbPhotoReview.builder().vipcode("KERRY93001").status("2").build());
        TbMemberAsset memberAsset = TbMemberAsset.builder().groupId("8aaa82ea804d07cd01805174dd3b000c").mallId("222").currentPoints(0).vipcode("KERRY93001").build();
        when(tbMemberAssetService.getAllByGroupIdAndKipUserId(any(), any()))
                .thenReturn(memberAsset);
        when(tbBaseShopService.getByContractNoAndMallId(Mockito.anyString(), Mockito.anyString())).thenReturn(TbBaseShop.builder().build());
        when(redisService.setSaleNoIfAbsent(any(), any())).thenReturn(Boolean.TRUE);
        when(memberSalePointsProcessService.salePointsProcess(any())).thenReturn(10);
        Assertions.assertNotNull(smallTicketRecognitionController.smallTicketCancelPoints(loginUser, resource));
    }

}
