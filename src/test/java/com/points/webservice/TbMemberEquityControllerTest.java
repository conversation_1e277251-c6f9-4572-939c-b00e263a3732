package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberBenefitConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberEquity;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.TbMemberEquityController;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberEquityResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - zhangxiliang
 * Created Date - 03/15/2024 10:51
 **********************************************************************************************************************/

@Slf4j
class TbMemberEquityControllerTest {

    @InjectMocks
    private TbMemberEquityController tbMemberEquityController;
    @Mock
    private TbMemberEquityService tbMemberEquityService;
    @Mock
    private TbMemberBenefitsConfigService tbMemberBenefitsConfigService;
    @Mock
    private TbMemberGradeService tbMemberGradeService;
    @Mock
    private MemberRegisterService memberRegisterService;
    @Mock
    private HiveVasService hiveVasService;
    @Mock
    private Mapper mapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getMemberEquities() {
        Assertions.assertDoesNotThrow(() -> tbMemberEquityController.getMemberEquities("", ""));

        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any())).thenReturn(MallItem.builder().build());
        TbMemberEquity tbMemberEquity = new TbMemberEquity();
        Mockito.when(tbMemberEquityService.findByMallIdAndType(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(tbMemberEquity));
        Mockito.when(mapper.map(tbMemberEquity, TbMemberEquityResponse.class)).thenReturn(new TbMemberEquityResponse());
        Assertions.assertDoesNotThrow(() -> tbMemberEquityController.getMemberEquities("", ""));
    }

    @Test
    void getBenefitsConfig() {
        LoginUser loginUser = LoginUser.builder().cId("123456789101112131415").build();
        Mockito.when(tbMemberBenefitsConfigService.findBenefitConfigByByMallIdAndGrade(Mockito.any(), Mockito.any()))
                .thenReturn(Collections.singletonList(new TbMemberBenefitConfig()));
        Mockito.when(memberRegisterService.findByGroupIdAndKipUserId(Mockito.any(), Mockito.any()))
                .thenReturn(TbMemberAsset.builder().build());
        Mockito.when(tbMemberGradeService.queryMinGroupGrade(Mockito.any()))
                .thenReturn(TbMemberGrade.builder().build());

        Assertions.assertDoesNotThrow(() -> tbMemberEquityController.getBenefitsConfig(loginUser, "", ""));

        Mockito.when(hiveVasService.getMallByLbsId(Mockito.any())).thenReturn(MallItem.builder().build());
        Assertions.assertDoesNotThrow(() -> tbMemberEquityController.getBenefitsConfig(loginUser, "", ""));

        Assertions.assertDoesNotThrow(() -> tbMemberEquityController.getBenefitsConfig(loginUser, "", "undefined"));

        Assertions.assertDoesNotThrow(() -> tbMemberEquityController.getBenefitsConfig(null, "", "undefined"));

        Assertions.assertDoesNotThrow(() -> tbMemberEquityController.getBenefitsConfig(LoginUser.builder().cId("").build(), "", "undefined"));
    }

}
