package com.points.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeEffectiveConfig;
import com.kerryprops.kip.service.integral.model.dto.MemberGradeProgressSwitchDto;
import com.kerryprops.kip.service.integral.service.TbMemberGradeEffectiveConfigService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeRuleProgressService;
import com.kerryprops.kip.service.integral.webservice.MemberGradeProgressController;
import com.kerryprops.kip.service.integral.webservice.resource.MemberUpgradeAmountResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - nancy
 * Created Date - 12/23/2024 10:48
 **********************************************************************************************************************/

@Slf4j
class MemberGradeProgressControllerTest {

    @InjectMocks
    private MemberGradeProgressController memberGradeProgressController;

    @Mock
    private TbMemberGradeRuleProgressService tbMemberGradeRuleProgressService;
    @Mock
    private TbMemberGradeEffectiveConfigService tbMemberGradeEffectiveConfigService;
    @Mock
    private Mapper mapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getProgressBar() {
        Assertions.assertThrows(Exception.class, () -> memberGradeProgressController.getProgressBar(LoginUser.builder().build(), ""));

        LoginUser user = LoginUser.builder().brandId("4028e3817c2b3f79017c2b48c54c0000").cId("2c9d85bc8489e0ba01849e4629760003").build();
        Mockito.when(tbMemberGradeRuleProgressService.getProgressBar(Mockito.any(), Mockito.any()))
                        .thenReturn(100);
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getProgressBar(user, "123"));
    }

    @Test
    void getRelegationCopywriter() {
        LoginUser user = LoginUser.builder().brandId("4028e3817c2b3f79017c2b48c54c0000")
                .cId("2c9d85bc8489e0ba01849e4629760003").build();
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getRelegationCopywriter(user));

        LoginUser user2 = LoginUser.builder().brandId("")
                .cId("2c9d85bc8489e0ba01849e4629760003").build();
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getRelegationCopywriter(user2));

        LoginUser user1 = LoginUser.builder().brandId("4028e3817c2b3f79017c2b48c54c0000")
                .phoneNumber("18976543212")
                .cId("2c9d85bc8489e0ba01849e4629760003").build();
        Mockito.when(tbMemberGradeRuleProgressService.getRelegationCopywriter(Mockito.any()))
                        .thenReturn("123");
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getRelegationCopywriter(user1));
    }

    @Test
    void getUpgradeCopywriter() {
        LoginUser user = LoginUser.builder().brandId("")
                .cId("2c9d85bc8489e0ba01849e4629760003").build();
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getUpgradeCopywriter(user, ""));

        LoginUser user1 = LoginUser.builder().brandId("4028e3817c2b3f79017c2b48c54c0000")
                .cId("2c9d85bc8489e0ba01849e4629760003").build();
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getUpgradeCopywriter(user1, ""));

        LoginUser user2 = LoginUser.builder().brandId("4028e3817c2b3f79017c2b48c54c0000")
                .phoneNumber("18976543212")
                .cId("2c9d85bc8489e0ba01849e4629760003").build();
        Mockito.when(tbMemberGradeRuleProgressService.getUpgradeCopywriter(Mockito.any(),Mockito.any()))
                .thenReturn("123");
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getUpgradeCopywriter(user2, ""));
    }

    @Test
    void getUpgradeMoney() {
        LoginUser user = LoginUser.builder().brandId("")
                .cId("2c9d85bc8489e0ba01849e4629760003").build();
        Mockito.when(tbMemberGradeRuleProgressService.getUpgradeMoney(user))
                .thenReturn(MemberUpgradeAmountResource.builder().build());
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getUpgradeMoney(user, ""));

        Mockito.when(tbMemberGradeEffectiveConfigService.findByGroupId(Mockito.any()))
                .thenReturn(TbMemberGradeEffectiveConfig.builder().enableDowngradeJob(true).build());
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getUpgradeMoney(user, ""));
    }

    @Test
    void getMemberGrowthProgressSwitchMethod() {
        LoginUser user = LoginUser.builder().brandId("")
                .cId("2c9d85bc8489e0ba01849e4629760003").build();
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getMemberGrowthProgressSwitchMethod(user));

        Mockito.when(tbMemberGradeRuleProgressService.memberGradeComputeMethod(Mockito.any()))
                .thenReturn(Collections.singletonList(MemberGradeProgressSwitchDto.builder().build()));
        Assertions.assertDoesNotThrow(() -> memberGradeProgressController.getMemberGrowthProgressSwitchMethod(user));


    }

}
