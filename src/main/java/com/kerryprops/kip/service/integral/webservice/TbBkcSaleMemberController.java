package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbBkcSaleMember;
import com.kerryprops.kip.service.integral.service.TbBkcSaleMemberService;
import com.kerryprops.kip.service.integral.webservice.resource.BkcMigrationConfirmResource;
import com.kerryprops.kip.service.integral.webservice.resource.CommonResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 12/14/2023 09:17
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/bkc_migration")
@RestController
@RequiredArgsConstructor
@Tag(name = "BKC迁移相关API")
public class TbBkcSaleMemberController {

    private final TbBkcSaleMemberService tbBkcSaleMemberService;

    @PutMapping("/status_confirm")
    @Operation( summary = "BKC迁移弹窗确认")
    public CommonResponse bkcMigrationConfirmStatus(@CurrentUser LoginUser loginUser, @RequestBody @Valid BkcMigrationConfirmResource resource) {
        if (0 == resource.getConfirmStatus()) {
            return CommonResponse.builder().success(Boolean.TRUE).build();
        }
        tbBkcSaleMemberService.bkcMigrationConfirmStatus(loginUser, resource.getConfirmStatus());
        return CommonResponse.builder().success(Boolean.TRUE).build();
    }

    @PostMapping("/create")
    @Hidden
    public String insertBkcCaseMember(@RequestBody TbBkcSaleMember member) {
        tbBkcSaleMemberService.insertBkcCaseMember(member);
        return "成功";
    }

}
