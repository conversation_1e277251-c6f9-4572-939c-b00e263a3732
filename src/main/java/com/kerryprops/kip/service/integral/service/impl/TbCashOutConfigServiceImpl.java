package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbCashOutConfig;
import com.kerryprops.kip.service.integral.mapper.TbCashOutConfigMapper;
import com.kerryprops.kip.service.integral.webservice.resource.TbCashOutConfigResource;
import com.kerryprops.kip.service.integral.service.TbCashOutConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 16:08
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbCashOutConfigServiceImpl extends ServiceImpl<TbCashOutConfigMapper, TbCashOutConfig> implements TbCashOutConfigService {

    private final TbCashOutConfigMapper tbCashOutConfigMapper;

    @Override
    public TbCashOutConfig getConfig(TbCashOutConfigResource resource) {
        return tbCashOutConfigMapper.getConfig(resource);
    }
}
