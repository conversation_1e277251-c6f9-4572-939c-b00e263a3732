package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - member-points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 06/05/2023 14:31
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_grade_effective_config")
public class TbMemberGradeEffectiveConfig implements Serializable {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("group_id")
    private String groupId;

    /**
     * 是否开启保级降级定时JOB(0: 否；1:是)
     */
    @TableField("enable_downgrade_job")
    private Boolean enableDowngradeJob;

    /**
     * 是否开启退货降级判断(0: 否；1:是)
     */
    @TableField("enable_refund_downgrade")
    private Boolean enableRefundDowngrade;

    @TableField("create_date")
    private Date createDate;

    @TableField("create_user")
    private String createUser;

    @TableField("update_date")
    private Date updateDate;

    @TableField("update_user")
    private String updateUser;

}
