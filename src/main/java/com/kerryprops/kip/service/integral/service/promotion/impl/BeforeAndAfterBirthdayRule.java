package com.kerryprops.kip.service.integral.service.promotion.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 * @DESC: 生日前后多少天消费
 * Author - zhangxiliang
 * Created Date - 09/25/2024 11:51
 **********************************************************************************************************************/


@Slf4j
@Component
public class BeforeAndAfterBirthdayRule implements PromotionRule {

    private static final String MONTH_DAY = "MM-dd";

    @Override
    public String getRuleType() {
        return "13";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        if (StringUtils.isBlank(condition.getPromotionConditionContent())) {
            return false;
        }
        TbMemberAsset member = dto.getMember();
        log.info("BeforeAndAfterBirthdayRule {}, {}, {}", dto.getSaleNo(), member.getVipcode(), condition.getPromotionConditionContent());
        // 2:生日为空判断
        if (Objects.isNull(member.getBirthDate())) {
            return false;
        }
        List<Integer> list = CharSequenceUtil.split(condition.getPromotionConditionContent(), ",").stream().map(Integer::parseInt).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        Date now = new Date();
        // 当前年的生日日期
        String birthday = String.format("%s-%s", DateUtil.year(now), DateUtil.format(member.getBirthDate(), MONTH_DAY));
        // 转化日期
        DateTime birthTime = DateUtil.parse(birthday, DatePattern.NORM_DATE_FORMAT);
        // 规则匹配-开始时间
        DateTime startTime = DateUtil.beginOfDay(DateUtil.offsetDay(birthTime, (list.get(0) <= 0 ? list.get(0) : -list.get(0))));
        // 规则匹配-结束时间
        DateTime endTime = DateUtil.endOfDay(DateUtil.offsetDay(birthTime, (list.get(1) >= 0 ? list.get(1) : 0)));
        // 销售时间
        DateTime saleTime = DateUtil.parse(dto.getSaleDate());
        return startTime.isBefore(saleTime) && endTime.isAfter(saleTime);
    }

}
