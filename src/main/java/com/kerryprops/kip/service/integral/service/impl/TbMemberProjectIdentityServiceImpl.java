//package com.kerryprops.kip.service.integral.service.impl;
//
//import cn.hutool.core.text.CharSequenceUtil;
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.github.dozermapper.core.Mapper;
//import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
//import com.kerryprops.kip.service.integral.config.MallConfig;
//import com.kerryprops.kip.service.integral.entity.TbMemberProjectIdentity;
//import com.kerryprops.kip.service.integral.mapper.TbMemberProjectIdentityMapper;
//import com.kerryprops.kip.service.integral.model.dto.CustomerIdentityDto;
//import com.kerryprops.kip.service.integral.model.dto.MallItem;
//import com.kerryprops.kip.service.integral.service.TbMemberProjectIdentityService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//
//import java.util.Arrays;
//import java.util.List;
//import java.util.stream.Collectors;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 04/26/2023 11:45
// **********************************************************************************************************************/
//
//@Slf4j
//@Service
//@DS("points")
//@RequiredArgsConstructor
//public class TbMemberProjectIdentityServiceImpl extends ServiceImpl<TbMemberProjectIdentityMapper, TbMemberProjectIdentity> implements TbMemberProjectIdentityService {
//
//    private final TbMemberProjectIdentityMapper tbMemberProjectIdentityMapper;
//    private final ProfileServiceClient profileServiceClient;
//    private final MallConfig mallConfig;
//    private final Mapper mapper;
//
//    @Async
//    @Override
//    public void saveIdentity(String kipUserId) {
//        List<CustomerIdentityDto> list = profileServiceClient.getIdentityResponse(kipUserId, null);
//        if (CollectionUtils.isEmpty(list)) {
//            // 删除身份认证
//            tbMemberProjectIdentityMapper.deleteByKipUserId(kipUserId);
//            return;
//        }
//        // 楼盘id
//        List<String> projectIds = list.stream().map(CustomerIdentityDto::getProjectId).distinct().collect(Collectors.toList());
//        // 删除已没有身份认证的楼盘信息
//        tbMemberProjectIdentityMapper.removeByUserIdAndNotInProjectIds(kipUserId, projectIds);
//        // 新增或修改身份信息
//        List<TbMemberProjectIdentity> identities = list.stream().map(item -> {
//            TbMemberProjectIdentity identity = mapper.map(item, TbMemberProjectIdentity.class);
//            identity.setKipUserId(kipUserId);
//            return identity;
//        }).collect(Collectors.toList());
//        tbMemberProjectIdentityMapper.saveOrUpdateBatch(identities);
//    }
//
//    @Override
//    public CustomerIdentityDto findByKipUserIdAndProjectId(String kipUserId, String projectId) {
//        List<TbMemberProjectIdentity> identityList = tbMemberProjectIdentityMapper.findByKipUserIdAndProjectIds(kipUserId, StringUtils.isNotBlank(projectId) ? CharSequenceUtil.split(projectId, ",") : null);
//        if (CollectionUtils.isEmpty(identityList)) {
//            return CustomerIdentityDto.builder().projectId(projectId).office(Boolean.FALSE).apartment(Boolean.FALSE).residence(Boolean.FALSE).build();
//        }
//        return CustomerIdentityDto.builder()
//                .office(identityList.stream().anyMatch(TbMemberProjectIdentity::isOffice))
//                .apartment(identityList.stream().anyMatch(TbMemberProjectIdentity::isApartment))
//                .residence(identityList.stream().anyMatch(TbMemberProjectIdentity::isResidence))
//                .projectId(projectId)
//                .build();
//    }
//
//    @Async
//    @Override
//    public void removeIdentity(String kipUserId) {
//        if (StringUtils.isBlank(kipUserId)) {
//            return;
//        }
//        tbMemberProjectIdentityMapper.deleteByKipUserId(kipUserId);
//    }
//
//    @Override
//    public void removeByKipUserIdAndGroupId(String kipUserId, String groupId) {
//        List<MallItem> items = mallConfig.getByGroupId(groupId);
//        if (CollectionUtils.isEmpty(items)) {
//            return;
//        }
//        // 获取groupId
//        List<String> projectIds = items.stream().map(MallItem::getProjectId).filter(StringUtils::isNotBlank).map(str -> str.split(","))
//                .flatMap(Arrays::stream).distinct().collect(Collectors.toList());
//        tbMemberProjectIdentityMapper.deleteByKipUserIdAndProjectIds(kipUserId, projectIds);
//    }
//
//}
