package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/21/2022 16:18
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "CRM会员基本信息响应类")
public class TbMemberResponse implements Serializable {

    @Schema( description = "CRM会员id")
    private String id;

    @Schema( description = "品牌ID")
    private String groupId;

    @Schema( description = "商场ID")
    private String mallid;

    @Schema( description = "会员vipcode")
    private String vipcode;

    @Schema( description = "手机号")
    private String mobile;

    @Schema( description = "kip用户id")
    private String kipUserId;

    @Schema( description = "当前积分数")
    private Integer currnentintegral;

    @Schema( description = "加入时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date jointime;

    @Schema( description = "是否黑名单(0:是 1:不是),供活动使用")
    private Integer whetherBlacklist;

    @Schema( description = "是否完善信息（0:未完善；1:完善)")
    private String isCompleted;

    @Schema( description = "会员卡封面图片地址（首页）")
    private String cardCoverHomeUrl;

    @Schema( description = "会员卡封面图片地址")
    private String cardCoverUrl;

    @Schema( description = "会员等级code")
    private String grade;

    @Schema( description = "会员等级名称")
    private String gradeName;

    @Schema( description = "会员等级排序值")
    private Integer sort;

    @Schema( description = "集团下全部会员等级编码数组")
    private List<String> grades;

    @Schema( description = "BKC拆分会员所属case")
    public Integer memberCase;

    @Schema( description = "BKC迁移弹窗确认状态, 存在null, 0, 1三种情况, null为不是bkc迁移case会员，0：未确认，1：已确认")
    public Integer bkcMigrationConfirmStatus;

    @Schema( description = "会员等级是否参与升级，保级降级  1 参与   0 不参与")
    public Integer upGradationStatus;

    public static TbMemberResponse getResponse(TbMemberAsset member) {
        TbMemberResponse response = new TbMemberResponse();
        response.setId(String.valueOf(member.getId()));
        response.setGroupId(member.getGroupId());
        response.setMallid(member.getMallId());
        response.setVipcode(member.getVipcode());
        response.setKipUserId(member.getKipUserId());
        response.setCurrnentintegral(member.getCurrentPoints());
        response.setJointime(member.getJoinTime());
        response.setWhetherBlacklist(member.getWhetherBlacklist());
        response.setIsCompleted(member.getIsCompleted());
        response.setGrade(member.getGrade());
        response.setMobile(member.getMobile());
        // BKC case会员类型
        response.setMemberCase(0);
        // BKC迁移弹窗确认状态
        response.setBkcMigrationConfirmStatus(1);
        return response;
    }

}
