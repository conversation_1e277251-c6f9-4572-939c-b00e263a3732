package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/02/2024 09:11
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "OCR拍照积分返回信息")
public class OcrPhotoResponse implements Serializable {

    @Schema( description = "响应code, 0:成功，其他错误码，去对应的错误信息")
    private Integer code;

    @Schema( description = "错误信息")
    private String msg;

    @Schema( description = "缺失五元素错误信息")
    private Map<String, String> missingFields;

    @Schema( description = "ocr任务id")
    private String taskId;

    @Schema( description = "商场名称")
    private String mallName;

    @Schema( description = "店铺号")
    private String shopNo;

    @Schema( description = "店铺名称")
    private String shopName;

    @Schema( description = "交易单号")
    private String ticketNo;

    @Schema( description = "交易时间")
    private String transTime;

    @Schema( description = "交易金额")
    private String amount;

}
