package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_points_expired_reminder
 * <AUTHOR> @Desc: 积分清零配置表
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_points_expired_reminder")
public class TbPointsExpiredReminder implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场ID
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * C端是否展示即将过期积分（0、不展示；1、展示年底过期；2、展示非年底过期）
     */
    @TableField("is_show_c")
    private Integer isShowC;

    /**
     * 展示类型（1、按月；2、按天）
     */
    @TableField("show_type")
    private Integer showType;

    /**
     * 提前多少月
     */
    @TableField("ahead_month_num")
    private Integer aheadMonthNum;

    /**
     * 提前多少天
     */
    @TableField("ahead_day_num")
    private Integer aheadDayNum;

    /**
     * 年底展示文案
     */
    @TableField("year_end")
    private String yearEnd;

    /**
     * 非年底展示文案
     */
    @TableField("not_year_end")
    private String notYearEnd;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

}