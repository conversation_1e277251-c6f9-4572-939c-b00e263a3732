package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.SelfDefiningPage;
import com.kerryprops.kip.service.integral.webservice.response.ZIndexListResponse;

/**
 * @description 自定义页面列表服务层
 * <AUTHOR>
 * @date 2022-12-15
 */
public interface SelfDefiningPageService extends IService<SelfDefiningPage> {

    /**
     * @param groupId 集团编号
     * @param mallId  商场编号
     * @param type    页面类型 0:首页 1:自定义 2:我的
     * @return 配置信息
     */
    SelfDefiningPage getZIndex(String groupId, String mallId, String type);

    /**
     * 根据id查询页面
     * @param groupId groupId
     * @param mallId mallId
     * @param id id
     * @return dto
     */
    SelfDefiningPage getPageById(String groupId, String mallId, String id);

}