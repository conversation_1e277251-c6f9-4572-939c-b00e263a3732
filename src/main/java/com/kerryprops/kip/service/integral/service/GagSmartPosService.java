package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.common.ResultVO;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.webservice.resource.GagScanCodeAchievePointsResource;
import com.kerryprops.kip.service.integral.webservice.response.IpadGagQrCodeResponse;

/**
 * <AUTHOR>
 * @ClassName BonusselfService
 * @Description BonusselfService
 * @date 2022/10/25 13:50
 * @Version 1.0
 */
public interface GagSmartPosService {

    Integer smartPosAutoPoints(LoginUser loginUser, String billFileName);

    ResultVO<IpadGagQrCodeResponse> qrcodeDetail(String billFileName, String mallId);

    /**
     * ipad端扫码实现自动积分
     * @param resource
     * @param createUser
     * @return
     */
    ResultVO<Integer> scanCodeAchievePoints(GagScanCodeAchievePointsResource resource, String createUser);

}
