package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/01/2022 14:42
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberPointsChangeDto implements Serializable {

    /**
     * 调整记录记录ID
     */
    private String adjustPointsId;

    private String groupId;

    private String mallId;

    private String vipcode;

    /**
     * 销售类型(1.CRM销售,2.POS销售,3.微信小票,4.微信扫码,5.口碑,6.线上商城,7.微信商圈,8.支付宝商圈)
     */
    private String saleType;

    private String dictValue;

    private String dictName;

    /**
     * 变更积分数
     * >0: 加
     * <0: 减
     */
    private Integer changePointsNum;

    private String saleNo;

    private BigDecimal saleAmount;

    /**
     * 积分调整类型：
     * A: 普通调整
     * S: 销售调整
     */
    private String type;

    /**
     * 积分变更明细关联业务id，如积分清零时，关联当前执行的积分清零任务id
     */
    private String associatedBusinessId;

    private String shopNo;

    /**
     * 积分清零备注信息
     */
    private String remark;

    private String creator;

    /**
     * 单独的备注信息，如无，则积分明细内记录remark信息
     */
    private String content;

    /**
     * 执行该积分清零操作时，是否检验会员状态
     * null或0：需要校验
     * 1：不需要校验
     */
    private Integer checkMemberStatus;

}
