package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbMemberEquity;
import com.kerryprops.kip.service.integral.mapper.TbMemberEquityMapper;
import com.kerryprops.kip.service.integral.service.TbMemberEquityService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/01/2022 10:30
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbMemberEquityServiceImpl extends ServiceImpl<TbMemberEquityMapper, TbMemberEquity> implements TbMemberEquityService {

    private final TbMemberEquityMapper tbMemberEquityMapper;

    @Override
    public List<TbMemberEquity> findByMallIdAndType(String groupId, String mallId, String type) {
        List<Integer> types = Collections.emptyList();
        if (StringUtils.isNotBlank(type)) {
            types = CharSequenceUtil.split(type, ",").stream().map(Integer::parseInt).toList();
        }
        return tbMemberEquityMapper.findByMallIdAndType(groupId, mallId, types);
    }

}
