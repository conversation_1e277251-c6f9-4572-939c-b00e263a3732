package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 09/18/2023 21:34
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberYearAmountCalDto implements Serializable {

    private Date beginDate;

    private Boolean minGrade;

    /**
     * 保级降级/人工调整会员等级记录初始等级
     */
    private String orgGrade;

    /**
     * 是否是周期年
     */
    private Boolean cycleYear;

}
