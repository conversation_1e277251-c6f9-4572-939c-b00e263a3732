package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayAuthDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayBizContentDto;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝无感积分相关服务（基础）
 * @createDate 2022/10/16
 * @updateDate 2022/10/16
 */
public interface AliPayService {

    String AUTH_CODE = "authorization_code";
    String CODE_OK = "10000";
    String ACTIVATEFORM_QUERY_RESPONSE = "alipay_marketing_card_activateform_query_response";
    String ERROR_PAGE = "http://aqhcrm.aegeangroup.com.cn/KouBei/modules/h5/error.html";
    String ALIPAY = "alipay";

    String TRADE_PAY_NOTIFY = "alipay.business.mall.tradeapply.notify";

    String callback(AliPayBizContentDto callbackDto);

    String authCallback(AliPayAuthDto dto);

    String memberAuth(AliPayAuthDto dto);

    String smartPointsCallback(AliPayAuthDto dto);

    boolean mallAuth(AliPayAuthDto authDto);
}
