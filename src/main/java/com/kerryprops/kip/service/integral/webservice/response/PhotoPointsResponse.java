package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 08/01/2024 14:49
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "拍照积分页面配置类")
public class PhotoPointsResponse implements Serializable {

    @Schema( description = "是否是OCR拍照积分，true:是，false:否")
    private Boolean ocr;

    @Schema( description = "错误时间提示，null:正常，false:不允许拍照积分")
    private String time;

}
