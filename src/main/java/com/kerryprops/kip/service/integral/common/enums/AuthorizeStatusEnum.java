package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/21/2022 10:16
 **********************************************************************************************************************/

@Getter
public enum AuthorizeStatusEnum {

    /**
     * 未授权
     */
    UNAUTHORIZED(1),
    /**
     * 已授权
     */
    AUTHORIZED(0),
    /**
     * 已取消授权
     */
    DEAUTHORIZE(2);

    private final int val;

    AuthorizeStatusEnum(int val) {
        this.val = val;
    }

    public static AuthorizeStatusEnum getByName(String name) {
        for (AuthorizeStatusEnum e: values()) {
            if (Objects.equals(e.name(), name)) {
                return e;
            }
        }
        return AuthorizeStatusEnum.UNAUTHORIZED;
    }

}
