package com.kerryprops.kip.service.integral.service.promotion.impl;

import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC 会员性别
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:20
 **********************************************************************************************************************/

@Slf4j
@Component
public class GenderPromotionRule implements PromotionRule {

    private final static String CRM_SEX = "U,F,M";

    @Override
    public String getRuleType() {
        return "1";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        TbMemberAsset member = dto.getMember();
        String sex = member.getGender() + "";
        if (StringUtils.isBlank(condition.getPromotionConditionContent())) {
            return false;
        }
        String content = condition.getPromotionConditionContent();
        // 性别字段值转换
        if (CRM_SEX.contains(content)) {
            content = this.changeGender(content);
        }
        return content.contains(sex);
    }

    private String changeGender(String sex) {
        if (StringUtils.isBlank(sex)) {
            return "0";
        }
        if (Objects.equals(sex, "M")) {
            return "1";
        } else if (Objects.equals(sex, "F")) {
            return "2";
        } else {
            return "0";
        }
    }

}
