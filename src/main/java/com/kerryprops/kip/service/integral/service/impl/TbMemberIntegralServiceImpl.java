//package com.kerryprops.kip.service.integral.service.impl;
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
//import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
//import com.kerryprops.kip.service.integral.exception.PointBusinessException;
//import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
//import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
//import com.kerryprops.kip.service.integral.service.*;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Propagation;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.Objects;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Desc: 会员积分操作类
// * Created Date - 10/28/2022 09:45
// **********************************************************************************************************************/
//
//@Slf4j
//@Service
//@DS("xcrm")
//@AllArgsConstructor
//public class TbMemberIntegralServiceImpl extends BaseMemberStatus implements TbMemberIntegralService {
//
//    private final TbMemberAssetService tbMemberAssetService;
//    private final TbPointsDetailService tbPointsDetailService;
//
//    @Override
//    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
//    public int updateMemberIntegral(SalesAutoPointsDto dto) {
//        TbMemberAsset tbMemberAsset;
//        if (Objects.nonNull(dto.getMember())) {
//            if (StringUtils.isNotBlank(dto.getMember().getKipUserId())) {
//                tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndKipUserId(dto.getGroupId(), dto.getMember().getKipUserId());
//            } else {
//                tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndMobile(dto.getGroupId(), dto.getMember().getMobile());
//            }
//        } else {
//            tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndVipCode(dto.getGroupId(), dto.getVipcode());
//        }
//        // 检查会员是否存在及是否冻结
//        this.checkMemberStatus(tbMemberAsset);
//        log.info("正在更新会员: [{}]，调整积分数: [{}]", tbMemberAsset.getVipcode(), dto.getExchangePoints());
//        // 重新设置TbMember信息
//        dto.setMember(tbMemberAsset);
//        tbMemberAsset.setAdjustIntegralNum(dto.getExchangePoints());
//        int count = tbMemberAssetService.updatePoints(tbMemberAsset);
//        if (count > 0) {
//            // 记录积分变更明细
//            tbPointsDetailService.savePointsChangeRecord(dto);
//        }
//        return count;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public int updateMemberPoints(MemberPointsChangeDto dto) {
//        TbMemberAsset tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndVipCode(dto.getGroupId(), dto.getVipcode());
//        // 检查会员是否存在及是否冻结（添加是否判断会员状态）
//        if (Objects.nonNull(dto.getCheckMemberStatus()) && 1 == dto.getCheckMemberStatus()) {
//            if (Objects.isNull(tbMemberAsset)) {
//                throw PointBusinessException.error(PointsEnum.MEMBER_NOT_EXISTS);
//            }
//        } else {
//            this.checkMemberStatus(tbMemberAsset);
//        }
//        // 扣减积分，则判断用户当前积分数是否大于0
//        if (dto.getChangePointsNum() < 0 && tbMemberAsset.getCurrentPoints() <= 0) {
//            throw PointBusinessException.error(PointsEnum.MEMBER_INTEGRAL_INSUFFICIENT_ERROR);
//        }
//        // 判断扣减积分数是否充足
//        if (dto.getChangePointsNum() < 0 && ((tbMemberAsset.getCurrentPoints() + dto.getChangePointsNum()) < 0)) {
//            throw PointBusinessException.error(PointsEnum.MEMBER_INTEGRAL_INSUFFICIENT_ERROR);
//        }
//        tbMemberAsset.setAdjustIntegralNum(dto.getChangePointsNum());
//        int count = tbMemberAssetService.updatePoints(tbMemberAsset);
//        if (count > 0) {
//            // 记录积分变更明细
//            tbPointsDetailService.savePointsAdjustRecordNoMessage(dto, tbMemberAsset);
//        }
//        return count;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
//    public void clearKoIntegral(MemberPointsChangeDto changeDto) {
//        TbMemberAsset tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndVipCode(changeDto.getGroupId(), changeDto.getVipcode());
//        // 检查会员是否存在及是否冻结
//        this.checkMemberStatus(tbMemberAsset);
//        if (changeDto.getChangePointsNum() < 0 && tbMemberAsset.getCurrentPoints() <= 0) {
//            log.info("会员积分为0，不做积分扣减操作: {}", changeDto.getVipcode());
//            return;
//        }
//        // 判断扣减积分数是否充足
//        if (changeDto.getChangePointsNum() < 0 && ((tbMemberAsset.getCurrentPoints() + changeDto.getChangePointsNum()) < 0)) {
//            tbMemberAsset.setAdjustIntegralNum(-tbMemberAsset.getCurrentPoints());
//        } else {
//            tbMemberAsset.setAdjustIntegralNum(changeDto.getChangePointsNum());
//        }
//        int count = tbMemberAssetService.updatePoints(tbMemberAsset);
//        if (count > 0) {
//            // 记录积分变更明细
//            tbPointsDetailService.savePointsAdjustRecordNoMessage(changeDto, tbMemberAsset);
//        }
//    }
//
//
//}
