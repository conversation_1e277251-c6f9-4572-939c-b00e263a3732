//package com.kerryprops.kip.service.integral.service.impl;
//
//import cn.hutool.core.util.IdUtil;
//import com.kerryprops.kip.service.integral.common.IntegralConstant;
//import com.kerryprops.kip.service.integral.entity.*;
//import com.kerryprops.kip.service.integral.mapper.TbEmailSendRecordDetailMapper;
//import com.kerryprops.kip.service.integral.mapper.TbEmailSendRecordMapper;
//import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
//import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
//import com.kerryprops.kip.service.integral.service.TbEmailSendRecordService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 03/06/2023 15:14
// **********************************************************************************************************************/
//
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class TbEmailSendRecordServiceImpl implements TbEmailSendRecordService {
//
//    private final TbEmailSendRecordMapper tbEmailSendRecordMapper;
//    private final TbEmailSendRecordDetailMapper tbEmailSendRecordDetailMapper;
//
//    @Async
//    @Override
//    public void saveRecord(SendMessageDto dto, TbEmailTemplateConfig config, KerryResultDto result, TbMemberAsset member) {
//        Date currentDate = new Date();
//        boolean succeed = Objects.equals(IntegralConstant.DEF_SUCC, result.getCode());
//        TbEmailSendRecord record = TbEmailSendRecord.builder().id(IdUtil.getSnowflakeNextId())
//                .mallId(dto.getMallId()).groupId(dto.getGroupId()).templateId(config.getId())
//                .emailContent(dto.getSmsContent()).emailSubject(config.getEmailSubject()).sendTime(currentDate).status(succeed ? 0 : 1)
//                .createUser("sys").createDate(currentDate).failCause(succeed ? null : result.getMessage()).build();
//        tbEmailSendRecordMapper.saveRecord(record);
//        // 保存明细
//        List<TbEmailSendRecordDetail> details = new ArrayList<>();
//        details.add(TbEmailSendRecordDetail.builder().id(IdUtil.getSnowflakeNextId())
//                .recordId(record.getId()).sendEmail(member.getEmail()).build());
//        tbEmailSendRecordDetailMapper.saveBatch(details);
//    }
//}
