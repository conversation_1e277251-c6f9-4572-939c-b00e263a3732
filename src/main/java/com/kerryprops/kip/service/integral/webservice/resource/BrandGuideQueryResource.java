package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/18/2024 15:56
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "品牌导览查询类")
public class BrandGuideQueryResource implements Serializable {

    @Schema( description = "业态id")
    private String classificationId;

    @Schema( description = "收藏条件查询，true: 收藏，false：未收藏，null：全部")
    private Boolean collectFlag;

    @Schema( description = "楼层id")
    private String floorId;

    @Schema( description = "店铺名称")
    private String name;

}
