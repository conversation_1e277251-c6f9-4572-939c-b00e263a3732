package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.entity.ZindexResources;
import com.kerryprops.kip.service.integral.mapper.ZindexResourcesMapper;
import com.kerryprops.kip.service.integral.model.dto.BrandGuideRespDto;
import com.kerryprops.kip.service.integral.service.ZIndexResourcesService;
import com.kerryprops.kip.service.integral.webservice.response.SelfDefiningContextResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description 自定义页面资源表
 * @date 2022-12-15
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ZIndexResourcesServiceImpl extends ServiceImpl<ZindexResourcesMapper, ZindexResources> implements ZIndexResourcesService {

    private final HiveVasClient hiveVasClient;

    @Async
    @Override
    public CompletableFuture<List<SelfDefiningContextResource>> getBrandLogoByMallId(String mallId) {
        List<BrandGuideRespDto> brandList = hiveVasClient.getBrandGuideHomePage(mallId);
        if (CollectionUtils.isEmpty(brandList)) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
        List<SelfDefiningContextResource> responses = new ArrayList<>();
        for (BrandGuideRespDto dto: brandList) {
            responses.add(SelfDefiningContextResource.builder().id(dto.getId()).groupId(dto.getGroupId()).mallId(dto.getLbsId()).sort(dto.getSort())
                    .imageUrl(dto.getLogoPic()).imageId(dto.getId()).linkType("3").insideUrl(dto.getRedirectDetailSwitch() ? "/pages/brand/brandDetails?shopNo=" + dto.getShopNo() + "&lbsId=" + mallId : "")
                    .insideId(dto.getShopNo()).shopno(dto.getShopNo()).activityName(dto.getShopName()).showImgFlag(0).circleAuthorize(0).memberAuthorize(0).build());
        }
        return CompletableFuture.completedFuture(responses);
    }

}