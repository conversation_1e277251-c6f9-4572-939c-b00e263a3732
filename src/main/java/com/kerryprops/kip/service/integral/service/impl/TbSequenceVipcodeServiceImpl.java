package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.service.TbSequenceVipcodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/25/2022 12:07
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbSequenceVipcodeServiceImpl implements TbSequenceVipcodeService {

    private final TbMemberAssetMapper tbMemberAssetMapper;
    private final RedisService redisService;

    @Override
    public String nextId() {
        if (redisService.hasKey(RedisCacheKey.KIP_MEMBER_VIPCODE_KEY)) {
            Long autoId = redisService.incrementOne(RedisCacheKey.KIP_MEMBER_VIPCODE_KEY);
            return String.format("KERRY%s", autoId);
        } else {
            TbMemberAsset memberAsset = tbMemberAssetMapper.getLatestMemberAsset();
            Long nextId = Long.parseLong(memberAsset.getVipcode().replace("KERRY", "")) + 1000L;
            // 存入redis值
            redisService.incrementLimitedSteps(RedisCacheKey.KIP_MEMBER_VIPCODE_KEY, nextId);
            return String.format("KERRY%s", nextId);
        }
    }
}
