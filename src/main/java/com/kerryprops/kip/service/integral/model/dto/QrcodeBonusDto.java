package com.kerryprops.kip.service.integral.model.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName QrcodeBonusDto
 * @Description QrcodeBonusDto
 * @date 2022/10/26 13:54
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QrcodeBonusDto implements Serializable {

    /**
     * 店铺号
     */
    private String contractno;
    /**
     * 优惠金额
     */
    private String discount;

    private String groupId;

    private String mallid;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 交易金额
     */
    private Double money;
    /**
     * 流水号
     */
    private String serialnum;
    /**
     * 时间戳
     */
    private String timestamp;
    /**
     * 交易日期
     */
    private String tradingDate;
    /**
     * 上传时间
     */
    private Date uploadDate;
    /**
     * 会员号
     */
    private String vipcode;

}
