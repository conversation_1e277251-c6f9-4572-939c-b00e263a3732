package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberRefineField;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 09/14/2023 11:16
 **********************************************************************************************************************/
public interface TbMemberRefineDetailMapper extends BaseMapper<TbMemberRefineField> {

    /**
     * findByConfigId
     * @param isRefine isRefine
     * @param configId configId
     * @return dto dto
     */
    List<TbMemberRefineField> findByConfigId(@Param("configId") Long configId, @Param("isRefine") Integer isRefine);

    /**
     * 查询商场完善信息配置项信息
     * @param mallId mallId
     * @return list list
     */
    List<TbMemberRefineField> findMallRefineFields(@Param("mallId") String mallId);

}