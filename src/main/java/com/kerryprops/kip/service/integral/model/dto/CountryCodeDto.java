package com.kerryprops.kip.service.integral.model.dto;



import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - profile-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/22/2023 11:23
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "手机号区号响应信息")
public class CountryCodeDto implements Serializable {

    @Schema( name = "区号")
    private String areaCode;

    @Schema( name = "中文国家名")
    private String zhName;

    @Schema( name = "英文国家名")
    private String enName;

}
