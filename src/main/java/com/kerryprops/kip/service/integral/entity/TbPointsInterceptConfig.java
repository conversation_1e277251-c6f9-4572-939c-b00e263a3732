package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - member-points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> zhangxiliang
 * Created Date - 04/22/2024 10:09
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_points_intercept_config")
public class TbPointsInterceptConfig implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 集团ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场编号
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 规则类型 1 单人单店铺
     */
    @TableField("rule_type")
    private Integer ruleType;

    /**
     * 时间类型 1:日 2:自然周 3：周期周(连续7天) 4:自然月
     */
    @TableField("time_type")
    private Integer timeType;

    /**
     * 计数类型 1:累计
     */
    @TableField("count_type")
    private Integer countType;

    /**
     * 次数
     */
    @TableField("count")
    private Integer count;

    /**
     * 是否配置白名单，0:否，1:是
     */
    @TableField("white_list")
    private Integer whiteList;

    /**
     * 白名单店铺列表，用英文逗号分隔
     */
    @TableField("shop_no")
    private String shopNo;

    /**
     * 创建时间
     */
    @Builder.Default
    @TableField("create_date")
    private Date createDate = new Date();

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @Builder.Default
    @TableField("update_date")
    private Date updateDate = new Date();

    /**
     * 修改人
     */
    @TableField("update_user")
    private String updateUser;

}
