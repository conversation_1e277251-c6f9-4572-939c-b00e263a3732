package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_template_message
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbTemplateMessage implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商场编号
     */
    private String mallId;

    /**
     * 集团ID
     */
    private String groupId;

    /**
     * 模板ID
     */
    private String tid;

    /**
     * 模板标题
     */
    private String title;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 模板示例
     */
    private String example;

    /**
     * h5跳转链接
     */
    private String url;

    /**
     * 小程序appid
     */
    private String miniprogramAppid;

    /**
     * 小程序页面路径
     */
    private String miniprogramPagepath;

    /**
     * 跳转类型（0、不跳转；1、h5链接2、小程序链接）
     */
    private String jumpType;

    /**
     * 模板类型（电子券到期：ticketDueMsg , 电子券兑换成功通知：ticketExchangeMsg )
     */
    private String templateType;

    /**
     * 是否启用（0、启用；1、不启用）
     */
    private String state;

    /**
     * 类型（公众号:1；  小程序:2)
     */
    private String type;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人
     */
    private Long updater;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 会员卡等级(1:银卡,2金卡,3铂金卡)
     */
    private String memberGrade;

    /**
     * 生效时间
     */
    private Date startDate;

    /**
     * 失效时间
     */
    private Date endDate;

    /**
     * 生效时间是否限制,0:(默认)不限制,1限制
     */
    private Integer startDateStatus;

    /**
     * 是否显示，0显示，1不显示，默认0
     */
    private Integer isShow;

    /**
     * 授权节点
     */
    private String authorizationNode;

    /**
     * 发送成功是否发送短信（0否1是）
     */
    private String isSendSms;

}