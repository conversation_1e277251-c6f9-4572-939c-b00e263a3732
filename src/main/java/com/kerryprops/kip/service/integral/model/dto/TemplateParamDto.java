package com.kerryprops.kip.service.integral.model.dto;

import com.kerryprops.kip.service.integral.common.enums.WxTemplatePlaceholderEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 08/15/2022 14:18
 **********************************************************************************************************************/
@Data
public class TemplateParamDto {
    private String key;
    private Map<WxTemplatePlaceholderEnum, String> enumMap;
    private String finalValue;

    public TemplateParamDto(String key, Map<WxTemplatePlaceholderEnum, String> enumMap) {
        this.key = key;
        this.enumMap = enumMap;
    }

    public TemplateParamDto(String key, Map<WxTemplatePlaceholderEnum, String> enumMap, String finalValue) {
        this.key = key;
        this.enumMap = enumMap;
        this.finalValue = finalValue;
    }

    public String buildFinalValue() {
        Stream<String> stringStream = this.enumMap.entrySet().stream().map(entry -> "{" + entry.getKey() + "}@" + entry.getValue());
        String value = stringStream.collect(Collectors.joining("&"));
        if (StringUtils.isNotBlank(value)) {
            this.finalValue = value;
            return value;
        }
        return "";
    }
}
