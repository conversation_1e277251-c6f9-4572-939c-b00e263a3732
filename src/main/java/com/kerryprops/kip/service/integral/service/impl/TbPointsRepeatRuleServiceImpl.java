package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.SaleRepeatEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbPointsRepeatRule;
import com.kerryprops.kip.service.integral.mapper.TbPointsRepeatRuleMapper;
import com.kerryprops.kip.service.integral.model.dto.MemberSaleRepeatQueryDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbPointsRepeatRuleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/19/2022 20:33
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbPointsRepeatRuleServiceImpl extends ServiceImpl<TbPointsRepeatRuleMapper, TbPointsRepeatRule> implements TbPointsRepeatRuleService {

    private final TbPointsRepeatRuleMapper tbPointsRepeatRuleMapper;

    @Override
    public TbPointsRepeatRule getRuleByMallIdAndGroupId(String mallId, String groupId) {
        return tbPointsRepeatRuleMapper.queryRulesByMallIdAndGroupId(mallId, groupId);
    }

    @Override
    public MemberSaleRepeatQueryDto getSaleRepeatCondition(SalesAutoPointsDto dto, boolean isAutoPoints) {
        TbPointsRepeatRule repeatRule = this.getRuleByMallIdAndGroupId(dto.getMallId(), dto.getGroupId());
        // 白名单店铺判断
        if (Objects.nonNull(repeatRule) &&
                (1 == repeatRule.getWhiteList()) &&
                StringUtils.isNotBlank(repeatRule.getShopNo()) &&
                Stream.of(repeatRule.getShopNo().split(CommonSeparators.COMMA_SEPARATOR)).anyMatch(item -> StringUtils.equals(item, dto.getShopId()))) {
                return null;
        }
        // 查询条件
        MemberSaleRepeatQueryDto queryDto = MemberSaleRepeatQueryDto.builder().groupId(dto.getGroupId()).build();
        if (Objects.isNull(repeatRule)) {
            queryDto.setShopId(dto.getShopId());
            queryDto.setUseMoney(dto.getAmount());
            // 前后各一分钟
            Date saleDate = DateUtil.parse(dto.getSaleDate());
            queryDto.setSaleDateBefore(DateUtil.formatDateTime(DateUtil.offsetMinute(saleDate, -1)));
            queryDto.setSaleDateAfter(DateUtil.formatDateTime(DateUtil.offsetMinute(saleDate, 1)));
            queryDto.setVipcode(dto.getMember().getVipcode());
        } else {
            List<String> ruleCodes = StrUtil.split(repeatRule.getRepeatRule(), CommonSeparators.COMMA_SEPARATOR);
            if (ruleCodes.contains(SaleRepeatEnum.SHOP.getCode())) {
                queryDto.setShopId(dto.getShopId());
            }
            if (ruleCodes.contains(SaleRepeatEnum.AMOUNT.getCode())) {
                queryDto.setUseMoney(dto.getAmount());
            }
            if (ruleCodes.contains(SaleRepeatEnum.SALE_TIME.getCode())) {
                // 根据实际设置值判断
                Date saleDate = DateUtil.parse(dto.getSaleDate());
                queryDto.setSaleDateBefore(DateUtil.formatDateTime(DateUtil.offsetMinute(saleDate, -repeatRule.getSalesTimeDifference())));
                queryDto.setSaleDateAfter(DateUtil.formatDateTime(DateUtil.offsetMinute(saleDate, repeatRule.getSalesTimeDifference())));
            }
            if (ruleCodes.contains(SaleRepeatEnum.VIP_CODE.getCode())) {
                queryDto.setVipcode(dto.getMember().getVipcode());
            }
        }
        return queryDto;
    }

}
