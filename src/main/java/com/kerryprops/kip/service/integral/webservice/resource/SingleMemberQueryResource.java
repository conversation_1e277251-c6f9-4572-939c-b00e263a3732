package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/19/2023 09:22
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员信息查询请求类")
public class SingleMemberQueryResource implements Serializable {

    @NotBlank(message = "groupId不能为空.")
    @Schema( description = "所属group id")
    private String groupId;

    @Schema( description = "vipcode或mobile或kipUserId三者不能同时为空")
    private String vipcode;

    @Schema( description = "vipcode或mobile或kipUserId三者不能同时为空")
    private String mobile;

    @Schema( description = "vipcode或mobile或kipUserId三者不能同时为空")
    private String kipUserId;

}
