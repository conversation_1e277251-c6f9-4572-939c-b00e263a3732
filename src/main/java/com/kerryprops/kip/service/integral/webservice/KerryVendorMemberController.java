package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.kerryprops.kip.service.integral.client.FlexibleSpaceServiceClient;
import com.kerryprops.kip.service.integral.client.TempParkingClient;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.ResultVO;
import com.kerryprops.kip.service.integral.common.aop.CheckUserApi;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.common.utils.MemberConvertUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.model.dto.CustomerVehicleVo;
import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.model.dto.PageableParkingVehicle;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.BasicIntegralUpdateResource;
import com.kerryprops.kip.service.integral.webservice.resource.ShopCodeResource;
import com.kerryprops.kip.service.integral.webservice.resource.SupplierQueryMemberResource;
import com.kerryprops.kip.service.integral.webservice.response.CarNoResponse;
import com.kerryprops.kip.service.integral.webservice.response.SupplierMemberResponse;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

import static java.util.stream.Collectors.toList;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 10/30/2024 11:51
 **********************************************************************************************************************/

/**
 * 嘉里供应商查询会员信息
 */

@Slf4j
@RequestMapping("/kerry/suppliers")
@RestController
@RequiredArgsConstructor
public class KerryVendorMemberController {

    private final MallConfig mallConfig;
    private final RabbitMqService rabbitMqService;
    private final TempParkingClient tempParkingClient;
    private final KerryStaffService kerryStaffService;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbMemberPointsChangeService tbMemberPointsChangeService;
    private final TbMemberGradeService tbMemberGradeService;
    private final TbBaseShopService tbBaseShopService;
    private final FlexibleSpaceServiceClient flexibleSpaceServiceClient;

    private static final String TYPE_A = "A";

    /**
     * 立方调用CRM查询会员信息-仅限立方调用
     * 添加了非营运时间判断，与getMemberInfo接口做区分
     * @param resource
     * @return
     */
    @PostMapping("/findMemberInfo")
    @CheckUserApi(value = "会员查询，立方查询HKC VIP贵宾室使用", isCheck = true)
    public ResultVO findMemberInfo(@RequestBody SupplierQueryMemberResource resource) {
        if (StringUtils.isBlank(resource.getGroupId())
                || StringUtils.isBlank(resource.getMallid())
                || StringUtils.isAllBlank(resource.getMobile(), resource.getVipcode(), resource.getPlate())) {
            return ResultVO.fail(500,"必填参数缺失.");
        }
        log.info("findMemberInfo HKC VVIP贵宾室查询会员信息.");
        // 解析出当天的小时
        int hour = DateUtil.hour(new Date(), true);
        if (mallConfig.isHkc(resource.getGroupId())) {
            // HKC 非营业时间点（10点-22点之外），统一返回错误信息
            if (hour < 10 || hour >= 22) {
                log.info("HKC_VVIP查询会员信息，非营运时间, {}", hour);
                return ResultVO.fail(10500, "非营运时间.");
            }
        } else {
            return ResultVO.fail(10500, "非HKC查询会员信息错误.");
        }
        ResultVO resultVO;
        if (StringUtils.isAllBlank(resource.getMobile(), resource.getVipcode())) {
            resultVO = this.getMemberByCarNoAndGroupId(resource.getPlate(), resource.getGroupId(), TYPE_A);
        } else {
            resultVO = this.getResult(resource, TYPE_A);
        }
        // 发送访问通知
        if (Objects.nonNull(resultVO.getData())) {
            this.sendVipLoungeVisitNotify((SupplierMemberResponse) resultVO.getData(), resource.getMallid());
        }
        return resultVO;
    }

    /**
     * VIP LOUNGE访问通知
     * @param response
     * @param mallId
     */
    private void sendVipLoungeVisitNotify(SupplierMemberResponse response, String mallId) {
        Map<String, String> params = new HashMap<>(8);
        params.put("groupId", response.getGroupId());
        params.put("mallId", StringUtils.isNotBlank(mallId) ? mallId : response.getMallid());
        params.put("vipcode", response.getVipcode());
        // 发送通知消息
        try {
            rabbitMqService.sendMessage(RabbitMqConstant.HKC_VIP_LOUNGE_VISIT_NOTIFY, JsonUtils.objToString(params));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 会员查询接口改造（手机号,会员号,车牌三选一）
     *
     * @param resource
     * @return
     */
    @RequestMapping("/getMemberInfo")
    @CheckUserApi(value = "查询会员信息，适用于所有供应商")
    public ResultVO getMemberInfo(@RequestBody SupplierQueryMemberResource resource) {
        if (StringUtils.isBlank(resource.getGroupId())
                || StringUtils.isBlank(resource.getMallid())
                || StringUtils.isAllBlank(resource.getMobile(), resource.getVipcode(), resource.getPlate())) {
            return ResultVO.fail(500,"必填参数缺失");
        }
        log.info("getMemberInfo: {}", JsonUtils.objToString(resource));
        // 通过车牌查询
        if (StringUtils.isAllBlank(resource.getMobile(), resource.getVipcode())) {
            return this.getMemberByCarNoAndGroupId(resource.getPlate(), resource.getGroupId(), null);
        } else {
            return this.getResult(resource, null);
        }
    }

    private ResultVO getResult(SupplierQueryMemberResource resource, String type) {
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(resource.getGroupId()).vipcode(resource.getVipcode()).mobile(resource.getMobile()).build());
        if (Objects.nonNull(tbMemberAsset)) {
            // viplongue判断会员最高卡等
            if (StringUtils.isNotBlank(type) && TYPE_A.equals(type)) {
                boolean orderExists = flexibleSpaceServiceClient.checkMemberVipRoomOrderExists(tbMemberAsset.getGroupId(), tbMemberAsset.getKipUserId());
                if (orderExists) {
                    log.info("会员: {}-{}存在灵活空间预约单，通知viplongue扫码开门", tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode());
                } else {
                    // 查询集团最高卡等
                    TbMemberGrade grade = tbMemberGradeService.queryMaxGroupGrade(resource.getGroupId());
                    // 非最高卡等，不返回会员信息
                    if (Objects.isNull(grade) || !StringUtils.equals(grade.getCode(), tbMemberAsset.getGrade())) {
                        return ResultVO.fail(500, "会员不存在");
                    }
                }
            }
            // 补齐会员信息
            Optional.ofNullable(kerryStaffService.findByKipUserId(tbMemberAsset.getKipUserId())).ifPresent(tbMemberAsset::fillOtherInfo);
            // 对象转化
            return ResultVO.success(0, MemberConvertUtils.convertMemberEntity(tbMemberAsset), "success");
        } else {
            return ResultVO.fail(500, "会员不存在");
        }
    }

    private ResultVO getMemberByCarNoAndGroupId(String carNo, String groupId, String type) {
        PageableParkingVehicle pageData = tempParkingClient.vehiclesByCarNo(carNo);
        if (Objects.isNull(pageData) || CollectionUtils.isEmpty(pageData.getContent())) {
            return ResultVO.fail(500, "会员不存在");
        }
        List<CustomerVehicleVo> carList = pageData.getContent().stream()
                .map(PageableParkingVehicle.VehicleData::toVo)
                .collect(toList());
        // 查询会员列表
        List<TbMemberAsset> assetList = tbMemberAssetService.queryMemberByGroupIdAndKipUserIds(groupId, carList.stream().filter(item -> StringUtils.isNotBlank(item.getKipUserId()))
                .map(CustomerVehicleVo::getKipUserId).collect(toList()));
        if (CollectionUtils.isEmpty(assetList)) {
            return ResultVO.fail(500, "会员不存在");
        }
        // 查询集团最高卡等
        TbMemberAsset tbMemberAsset = null;
        if (StringUtils.isNotBlank(type) && TYPE_A.equals(type)) {
            TbMemberGrade grade = tbMemberGradeService.queryMaxGroupGrade(groupId);
            if (Objects.isNull(grade)) {
                return ResultVO.fail(500, "会员不存在");
            }
            tbMemberAsset = assetList.stream().filter(item -> StringUtils.equals(item.getGrade(), grade.getCode())).findFirst().orElse(null);
        } else {
            List<TbMemberGrade> gradeList = tbMemberGradeService.queryGradeSortAscByGroupId(groupId);
            if (CollectionUtils.isEmpty(gradeList)) {
                return ResultVO.fail(500, "会员不存在");
            }
            // 通过车牌查询，取等级最高的会员
            List<String> gradeCodes = assetList.stream().map(TbMemberAsset::getGrade).distinct().toList();
            TbMemberGrade gradeEntity = gradeList.stream().filter(it -> gradeCodes.contains(it.getCode())).max(Comparator.comparing(TbMemberGrade::getSort)).orElse(null);
            if (Objects.nonNull(gradeEntity)) {
                tbMemberAsset = assetList.stream().filter(it -> StringUtils.equals(it.getGrade(), gradeEntity.getCode())).findFirst().orElse(null);
            }
        }
        if (Objects.isNull(tbMemberAsset)) {
            return ResultVO.fail(500, "会员不存在");
        }
        // 补齐会员信息
        Optional.ofNullable(kerryStaffService.findByKipUserId(tbMemberAsset.getKipUserId())).ifPresent(tbMemberAsset::fillOtherInfo);
        SupplierMemberResponse response = MemberConvertUtils.convertMemberEntity(tbMemberAsset);
        Assert.notNull(response, "会员数据为空.");
        response.setPlateList(carList.stream().filter(item -> StringUtils.equals(item.getKipUserId(), response.getKipUserId()))
                .map(item -> CarNoResponse.builder().id(item.getId()).plate(item.getCarno()).type(item.getCarType()).build()).toList());
        return ResultVO.success(response);
    }

    @PostMapping("/getShopCode")
    @Operation(summary = "沈阳海鼎POS-根据店铺合同号查询店铺编号", method = "GET")
    public ResultVO getShopCode(@RequestBody @Valid ShopCodeResource shopCodeResource) {
        TbBaseShop baseShop = tbBaseShopService.getShopByDoCos(shopCodeResource.getContractNo(), shopCodeResource.getMallid());
        if (Objects.isNull(baseShop)) {
            return ResultVO.fail(500,"此合同号尚未存在crm中！");
        }
        Map<String, String> data = new HashMap<>(4);
        data.put("shopCode", baseShop.getContractNo());
        return ResultVO.success(data);
    }

    @PostMapping("/basicIntegralUpdate")
    @Operation(summary = "Kinetic调整积分API", method = "POST")
    @CheckUserApi(value = "Kinetic调整积分API", isCheck = true)
    public ResultVO basicIntegralUpdate(@RequestBody @Valid BasicIntegralUpdateResource resource) {
        TbMemberAsset asset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(resource.getGroupId()).mobile(resource.getMobile()).vipcode(resource.getVipcode()).build());
        if (Objects.isNull(asset)) {
            return ResultVO.fail(500,"会员信息不存在.");
        }
        log.info("basicIntegralUpdate: {}", JsonUtils.objToString(resource));
        MemberPointsChangeDto changeDto = MemberPointsChangeDto.builder().groupId(resource.getGroupId()).mallId(resource.getMallid()).vipcode(asset.getVipcode())
                .saleType("0").dictValue(resource.getRemark()).changePointsNum(resource.getNumber().intValue()).type("A").build();
        // 执行积分变更操作
        String adjustId = tbMemberPointsChangeService.updateMemberPoints(changeDto);
        Map<String, Object> map = new HashMap<>(8);
        map.put("integralAdjustId", adjustId);
        map.put("currentbonus", NumberUtil.add(resource.getNumber().intValue(), asset.getCurrentPoints()));
        map.put("bonusearn", resource.getNumber().intValue());
        return ResultVO.success(0, map, "success");
    }

}
