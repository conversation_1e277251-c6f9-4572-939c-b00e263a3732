package com.kerryprops.kip.service.integral.webservice.resource;



import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/10/2023 14:16
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "自助积分入口判断响应类")
public class AutoPointEntryResponse implements Serializable {

    @Schema( description = "自助积分入口是否打开, true:打开, false:关闭")
    private boolean open;

}
