package com.kerryprops.kip.service.integral.service.promotion.impl;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC 生日当月
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:24
 **********************************************************************************************************************/

@Slf4j
@Component
public class BirthMonthPromotionRule implements PromotionRule {
    @Override
    public String getRuleType() {
        return "3";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        TbMemberAsset member = dto.getMember();
        if (Objects.isNull(member.getBirthDate())) {
            return false;
        }
        int month = DateUtil.month(saleDate);
        int month1 = DateUtil.month(member.getBirthDate());
        return (month == month1);
    }
}
