package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbFieldSort;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.BonusSelfQueryDto;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.kerryprops.kip.service.integral.model.dto.TakePhotoAuditDto;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.PhotoReviewQueryResource;
import com.kerryprops.kip.service.integral.webservice.resource.TakePhotoAuditResource;
import com.kerryprops.kip.service.integral.webservice.response.PhotoReviewsResponse;
import com.kerryprops.kip.service.integral.webservice.response.TakePhotoAuditResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbReviewSelfResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 02/27/2023 10:03
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/photo/review")
@RestController
@RefreshScope
@RequiredArgsConstructor
@Tag(name = "拍照积分admin端api")
public class TbPhotoReviewController {

    private final TbPhotoReviewService tbPhotoReviewService;
    private final HiveVasService hiveVasService;
    private final KerryStaffService kerryStaffService;
    private final TbOcrCallbackRecordService tbOcrCallbackRecordService;
    private final TbMemberGradeService tbMemberGradeService;
    private final TbFieldSortService tbFieldSortService;
    private final Mapper mapper;

    @GetMapping("/getReviewPage")
    @Operation(summary="拍照积分分页查询", method = "GET")
    public Page<TbReviewSelfResponse> getReviewPage(@Valid PhotoReviewQueryResource resource) {
        BonusSelfQueryDto dto = mapper.map(resource, BonusSelfQueryDto.class);
        this.fillParam(dto, resource);
        int total = tbPhotoReviewService.getBonusTotal(dto);
        if (total <= 0) {
            return Page.of(resource.getPage(), resource.getSize(), total);
        }
        dto.setPage(resource.getPage() - 1);
        dto.setSize(resource.getSize());
        List<TbPhotoReview> list = tbPhotoReviewService.getReviewPage(dto);
        Page<TbReviewSelfResponse> pageData = Page.of(resource.getPage(), resource.getSize(), total);
        pageData.setRecords(this.fillData(list, 1));
        return pageData;
    }

    private List<TbReviewSelfResponse> fillData(List<TbPhotoReview> list, Integer sort) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<TbReviewSelfResponse> res = new ArrayList<>();
        List<String> shopNos = list.stream().map(TbPhotoReview::getShopNo).filter(StringUtils::isNotBlank).distinct().toList();
        CompletableFuture<Map<String, TenantInfoVo>> nameFuture = hiveVasService.getTenantFutureFromHiveService(shopNos);
        List<String> mallIds = list.stream().map(TbPhotoReview::getMallId).filter(StringUtils::isNotBlank).distinct().toList();
        CompletableFuture<Map<String, String>> mallFuture = hiveVasService.getMallName(mallIds);
        List<String> mobile = list.stream().map(TbPhotoReview::getMobile).filter(StringUtils::isNotBlank).distinct().toList();
        CompletableFuture<Map<String, CustomerUserDto>> userFuture = kerryStaffService.getMapFutureByMobile(mobile);
        CompletableFuture<Map<String, String>> gradeNameFuture = tbMemberGradeService.getMapFutureByGroupId(list.get(0).getGroupId());
        CompletableFuture.allOf(nameFuture, mallFuture, userFuture, gradeNameFuture).join();
        try {
            Map<String, TenantInfoVo> tenantMap = nameFuture.get();
            Map<String, String> mallMap = mallFuture.get();
            Map<String, CustomerUserDto> userMap = userFuture.get();
            Map<String, String> gradeNameMap = gradeNameFuture.get();
            for (TbPhotoReview self : list) {
                TbReviewSelfResponse dto = mapper.map(self, TbReviewSelfResponse.class);
                // ocr任务id
                dto.setOcrTaskId(self.getTaskId());
                // 填充状态中文名称
                this.specialStatusFixed(dto);
                // 审核通过的填充店铺名等信息
                Optional.ofNullable(MapUtils.getObject(tenantMap, self.getShopNo())).ifPresent(tenant -> {
                    // 店铺别名
                    dto.setShopAliasName(tenant.getShopName());
                    // 店铺名
                    dto.setShopName(tenant.getBrandName());
                });
                dto.setSort(sort);
                if (PhotoReviewStatusEnum.TYPE_1.getCode().equals(self.getStatus()) && StringUtils.isBlank(dto.getBonus())) {
                    dto.setBonus("0");
                }
                // 补齐用户昵称
                Optional.ofNullable(MapUtils.getObject(userMap, dto.getMobile())).ifPresent(user -> dto.setNickName(user.getNickName()));
                // 补齐商场名称
                Optional.ofNullable(MapUtils.getString(mallMap, self.getMallId())).ifPresent(dto::setMallName);
                // 补齐等级名称
                Optional.ofNullable(MapUtils.getString(gradeNameMap, dto.getOrgGrade())).ifPresent(dto::setOrgGradeName);
                if (StringUtils.equals("1999-01-01 00:00:00", dto.getCheckTime())) {
                    dto.setCheckTime("");
                }
                if (StringUtils.equals("1999-01-01 00:00:00", dto.getTradingDate())) {
                    dto.setTradingDate("");
                }
                res.add(dto);
                sort ++;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    @GetMapping("/getDetail")
    @Operation(summary="拍照积分审核详情", method = "GET")
    public TbReviewSelfResponse getDetail(@RequestParam String id,
                                          @RequestParam String startTime,
                                          @RequestParam String endTime) {
        TbPhotoReview self = tbPhotoReviewService.getById(id);
        if (Objects.isNull(self)) {
            throw BizException.error(PointsEnum.BONUS_RECORD_NOT_FOUND);
        }
        TbReviewSelfResponse res = mapper.map(self, TbReviewSelfResponse.class);
        this.fillNextImage(res, startTime, endTime);
        this.fillNickName(res);
        if (StringUtils.equals("1999-01-01 00:00:00", res.getTradingDate())) {
            res.setTradingDate("");
        }
        return res;
    }

    @GetMapping("/previousPage")
    @Operation(summary="拍照积分前一页", method = "GET")
    public TbReviewSelfResponse getPreviousPage(@RequestParam String timestamp,
                                                @RequestParam String mallId,
                                                @RequestParam String startTime,
                                                @RequestParam String endTime) {
        TbPhotoReview self = tbPhotoReviewService.getForPrevPage(timestamp, mallId, startTime, endTime);
        if (Objects.isNull(self)) {
            self = tbPhotoReviewService.getForPrevPage("", mallId, startTime, endTime);
        }
        if (Objects.isNull(self)) {
            throw BizException.error(PointsEnum.PHOTO_REVIEW_NO_DATA);
        }
        TbReviewSelfResponse res = mapper.map(self, TbReviewSelfResponse.class);
        this.fillNickName(res);
        return res;
    }

    @GetMapping("/exportReviewDetail")
    @Operation(summary="拍照积分导出", method = "GET")
    public void exportReviewDetail(PhotoReviewQueryResource resource, HttpServletResponse response, @CurrentUser LoginUser loginUser) throws Exception {
        BonusSelfQueryDto dto = mapper.map(resource, BonusSelfQueryDto.class);
        this.fillParam(dto, resource);
        Integer sort = 1;
        int total = tbPhotoReviewService.getBonusTotal(dto);
        // 添加导出数据量大小限制判断
        if (total > IntegralConstant.MAX_EXPORT_SIZE) {
            throw BizException.error(PointsEnum.EXPORT_SIZE_OVER_LIMITED);
        }
        response.setContentType("application/octet-stream; charset=utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("拍照积分审核列表", IntegralConstant.DEFAULT_ENC) + ExcelTypeEnum.XLSX.getValue());
        List<TbFieldSort> fieldSorts = tbFieldSortService.findByUserIdAndPageType(loginUser.getUserId(), "1");
        // 做字段的过滤
        if (CollectionUtils.isNotEmpty(fieldSorts)) {
            fieldSorts = fieldSorts.stream().filter(item -> (StringUtils.equalsIgnoreCase(item.getGroupId(), "ALL") || item.getGroupId().contains(resource.getGroupId()))).toList();
        }
        ExcelWriter writer = new ExcelWriterBuilder()
                .autoCloseStream(true)
                .file(response.getOutputStream())
                .head(fieldSorts.stream().map(item -> Collections.singletonList(item.getFieldCnName())).toList())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build();
        WriteSheet writeSheet = new WriteSheet();
        writeSheet.setSheetName("拍照积分审核");
        if (total <= 0) {
            writer.write(new ArrayList<>(), writeSheet);
        } else {
            // 设置每页查询数量
            dto.setSize(10000);
            int pages = total/10000 + (total % 10000 == 0 ? 0 : 1);
            List<TbPhotoReview> list = null;
            for (int i = 0; i < pages; i++) {
                dto.setPage(i);
                list = tbPhotoReviewService.getReviewPage(dto);
                // 先转换
                writer.write(this.fillExportData(list, sort, fieldSorts), writeSheet);
            }
        }
        writer.finish();
    }

    /**
     * 动态导出自定义的字段
     * @param list
     * @param sort
     * @param fieldSorts
     * @return
     */
    private List<List<Object>> fillExportData(List<TbPhotoReview> list, Integer sort, List<TbFieldSort> fieldSorts) {
        List<List<Object>> allList = new ArrayList<>(list.size());
        List<String> shopNos = list.stream().map(TbPhotoReview::getShopNo).filter(StringUtils::isNotBlank).distinct().toList();
        CompletableFuture<Map<String, TenantInfoVo>> nameFuture = hiveVasService.getTenantFutureFromHiveService(shopNos);
        List<String> mallIds = list.stream().map(TbPhotoReview::getMallId).filter(StringUtils::isNotBlank).distinct().toList();
        CompletableFuture<Map<String, String>> mallFuture = hiveVasService.getMallName(mallIds);
        List<String> mobile = list.stream().map(TbPhotoReview::getMobile).filter(StringUtils::isNotBlank).distinct().toList();
        CompletableFuture<Map<String, CustomerUserDto>> userFuture = kerryStaffService.getMapFutureByMobile(mobile);
        CompletableFuture<Map<String, String>> gradeNameFuture = tbMemberGradeService.getMapFutureByGroupId(list.get(0).getGroupId());
        CompletableFuture.allOf(nameFuture, mallFuture, userFuture, gradeNameFuture).join();
        try {
            Map<String, TenantInfoVo> tenantMap = nameFuture.get();
            Map<String, String> mallMap = mallFuture.get();
            Map<String, CustomerUserDto> userMap = userFuture.get();
            Map<String, String> gradeNameMap = gradeNameFuture.get();
            List<Object> itemList = null;
            List<String> fieldList = fieldSorts.stream().map(TbFieldSort::getFieldName).distinct().toList();
            for (TbPhotoReview self : list) {
                itemList = new ArrayList<>(fieldSorts.size());
                TbReviewSelfResponse dto = mapper.map(self, TbReviewSelfResponse.class);
                // ocr任务id
                dto.setOcrTaskId(self.getTaskId());
                // 填充状态中文名称
                this.specialStatusFixed(dto);
                // 审核通过的填充店铺名等信息
                Optional.ofNullable(MapUtils.getObject(tenantMap, self.getShopNo())).ifPresent(tenant -> {
                    // 店铺别名
                    dto.setShopAliasName(tenant.getShopName());
                    // 店铺名
                    dto.setShopName(tenant.getBrandName());
                });
                dto.setSort(sort);
                if (PhotoReviewStatusEnum.TYPE_1.getCode().equals(self.getStatus()) && StringUtils.isBlank(dto.getBonus())) {
                    dto.setBonus("0");
                }
                // 补齐用户昵称
                Optional.ofNullable(MapUtils.getObject(userMap, dto.getMobile())).ifPresent(user -> dto.setNickName(user.getNickName()));
                // 补齐商场名称
                Optional.ofNullable(MapUtils.getString(mallMap, self.getMallId())).ifPresent(dto::setMallName);
                // 补齐等级名称
                Optional.ofNullable(MapUtils.getString(gradeNameMap, dto.getOrgGrade())).ifPresent(dto::setOrgGradeName);
                if (StringUtils.equals("1999-01-01 00:00:00", dto.getCheckTime())) {
                    dto.setCheckTime("");
                }
                if (StringUtils.equals("1999-01-01 00:00:00", dto.getTradingDate())) {
                    dto.setTradingDate("");
                }
                Map<String, Object> objectMap = JsonUtils.objToMap(dto);
                for (String field: fieldList) {
                    if (!"mobile".equals(field)) {
                        itemList.add(MapUtils.getObject(objectMap, field));
                    } else {
                        String phoneNumber = MapUtils.getString(objectMap, field);
                        if (Pattern.matches(IntegralConstant.PHONE_NUMBER_REGEX, phoneNumber)) {
                            itemList.add(DesensitizedUtil.mobilePhone(phoneNumber));
                        } else {
                            itemList.add(CharSequenceUtil.hide(phoneNumber, 0, phoneNumber.length() - 4));
                        }
                    }
                }
                allList.add(itemList);
                sort++;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return allList;
    }

    private void fillParam(BonusSelfQueryDto dto, PhotoReviewQueryResource resource) {
        if (CollectionUtils.isNotEmpty(resource.getState())) {
            List<String> stateList = resource.getState();
            // 判断是否有已审核的状态查询，如有，则添加已审核(未积分)的状态查询
            if (stateList.contains(PhotoReviewStatusEnum.TYPE_1.getCode())) {
                stateList.add(PhotoReviewStatusEnum.TYPE_6.getCode());
            }
            dto.setStatus(stateList);
        }
        if (StringUtils.isNotBlank(dto.getUploadStartTime())) {
            dto.setUploadStartTime(String.format("%s 00:00:00", dto.getUploadStartTime()));
        }
        if (StringUtils.isNotBlank(dto.getUploadEndTime())) {
            dto.setUploadEndTime(String.format("%s 23:59:59", dto.getUploadEndTime()));
        }
        if (StringUtils.isNotBlank(dto.getExamineStartTime()) && StringUtils.isNotBlank(dto.getExamineEndTime())) {
            dto.setExamineStartTime(String.format("%s 00:00:00", dto.getExamineStartTime()));
            dto.setExamineEndTime(String.format("%s 23:59:59", dto.getExamineEndTime()));
        }
        StringJoiner joiner = new StringJoiner(", ");
        if (CollectionUtils.isNotEmpty(resource.getDesc())) {
            resource.getDesc().forEach(item -> joiner.add(item + " desc"));
        }
        if (CollectionUtils.isNotEmpty(resource.getAsc())) {
            resource.getAsc().forEach(item -> joiner.add(item + " asc"));
        }
        dto.setOrderBy(joiner.toString());
    }

    @GetMapping("/nextPage")
    @Operation(summary="拍照积分后一页", method = "GET")
    public TbReviewSelfResponse getNextPage(@RequestParam String timestamp,
                                            @RequestParam String mallId,
                                            @RequestParam String startTime,
                                            @RequestParam String endTime) {
        TbPhotoReview self = tbPhotoReviewService.getForNextPage(timestamp, mallId, startTime, endTime);
        if (Objects.isNull(self)) {
            self = tbPhotoReviewService.getForNextPage("", mallId, startTime, endTime);
        }
        if (Objects.isNull(self)) {
            throw BizException.error(PointsEnum.PHOTO_REVIEW_NO_CHECK_DATA);
        }
        TbReviewSelfResponse res = mapper.map(self, TbReviewSelfResponse.class);
        this.fillNextImage(res, startTime, endTime);
        this.fillNickName(res);
        return res;
    }

    private void fillNextImage(TbReviewSelfResponse res, String startTime, String endTime) {
        String nextImage = tbPhotoReviewService.getNextImage(res.getTimestamp(), res.getMallId(), startTime, endTime);
        if(StringUtils.isBlank(nextImage)) {
            nextImage = tbPhotoReviewService.getNextImage("", res.getMallId(), startTime, endTime);
        }
        // 补齐OCR任务id
        Optional.ofNullable(tbOcrCallbackRecordService.findByPhotoId(Long.parseLong(res.getId()))).ifPresent(task -> res.setOcrTaskId(task.getTaskId()));
        res.setNextImage(nextImage);
    }

    /**
     * 填充用户昵称
     * @param res
     */
    private void fillNickName(TbReviewSelfResponse res) {
        // 填充状态中文名称
        this.specialStatusFixed(res);
        Map<String, CustomerUserDto> map = kerryStaffService.getMapByMobiles(Collections.singletonList(res.getMobile()));
        Optional.ofNullable(MapUtils.getObject(map, res.getMobile())).ifPresent(user -> res.setNickName(user.getNickName()));
    }

    /**
     * 特殊处理已审核(未积分)的状态
     * @param res
     */
    private void specialStatusFixed(TbReviewSelfResponse res) {
        if (PhotoReviewStatusEnum.TYPE_6.getCode().equals(res.getStatus())) {
            res.setStatus(PhotoReviewStatusEnum.TYPE_1.getCode());
            res.setStatusStr(PhotoReviewStatusEnum.TYPE_1.getName());
        } else {
            res.setStatusStr(PhotoReviewStatusEnum.getName(res.getStatus()));
        }
    }

    @PostMapping("/photo/audit")
    @Operation(summary="CRM Admin端拍照积分审核", method = "POST")
    public TakePhotoAuditResponse photoAudit(@CurrentUser LoginUser loginUser, @RequestBody @Valid TakePhotoAuditResource resource) {
        TakePhotoAuditDto auditDto = mapper.map(resource, TakePhotoAuditDto.class);
        auditDto.setAuditor(loginUser.getNickName());
        Integer points = tbPhotoReviewService.auditRecord(auditDto);
        return TakePhotoAuditResponse.builder().points(points).build();
    }

    @GetMapping("/getStatusList")
    @Operation(summary="拍照积分审核状态", method = "GET")
    public List<PhotoReviewsResponse> getStatusList() {
        return PhotoReviewStatusEnum.getPhotoReviewStatusEnumList();
    }

}
