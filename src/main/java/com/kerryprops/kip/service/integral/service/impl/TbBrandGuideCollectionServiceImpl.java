package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbBrandGuideCollection;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.TbBrandGuideCollectionMapper;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.service.TbBrandGuideCollectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/18/2024 14:07
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbBrandGuideCollectionServiceImpl extends ServiceImpl<TbBrandGuideCollectionMapper, TbBrandGuideCollection> implements TbBrandGuideCollectionService {

    private final TbBrandGuideCollectionMapper tbBrandGuideCollectionMapper;
    private final RedisService redisService;

    @Async
    @Override
    public void saveBrandGuide(LoginUser loginUser, TbMemberAsset tbMemberAsset, String brandGuideId) {
        TbBrandGuideCollection collection = tbBrandGuideCollectionMapper.findByGroupIdAndVipcodeAndBrandGuideId(tbMemberAsset.getGroupId(), loginUser.getLbsId(), tbMemberAsset.getVipcode(), brandGuideId);
        if (Objects.isNull(collection)) {
            collection = TbBrandGuideCollection.builder().brandGuideId(brandGuideId).groupId(loginUser.getBrandId()).mallId(loginUser.getLbsId())
                    .vipcode(tbMemberAsset.getVipcode()).status(1).build();
            tbBrandGuideCollectionMapper.insert(collection);
        } else {
            if (Objects.nonNull(collection.getStatus()) && 1 == collection.getStatus()) {
                return;
            }
            // 设置为收藏已状态
            collection.setStatus(1);
            tbBrandGuideCollectionMapper.updateById(collection);
        }
    }

    @Async
    @Override
    public void cancelBrandGuide(LoginUser loginUser, TbMemberAsset tbMemberAsset, String brandGuideId) {
        TbBrandGuideCollection collection = tbBrandGuideCollectionMapper.findByGroupIdAndVipcodeAndBrandGuideId(tbMemberAsset.getGroupId(), loginUser.getLbsId(), tbMemberAsset.getVipcode(), brandGuideId);
        if (Objects.isNull(collection)) {
            return;
        }
        if (Objects.nonNull(collection.getStatus()) && 0 == collection.getStatus()) {
            return;
        }
        // 设置为取消状态
        collection.setStatus(0);
        tbBrandGuideCollectionMapper.updateById(collection);
    }

    @Override
    public void saveOrCancelBrandGuide(TbBrandGuideCollection collection) {
        TbBrandGuideCollection item = tbBrandGuideCollectionMapper.findByGroupIdAndVipcodeAndBrandGuideId(collection.getGroupId(), collection.getMallId(), collection.getVipcode(), collection.getBrandGuideId());
        if (Objects.isNull(item)) {
            tbBrandGuideCollectionMapper.insert(collection);
        } else {
            if (item.getStatus().equals(collection.getStatus())) {
                return;
            }
            item.setStatus(collection.getStatus());
            // 更新创建时间
            item.setCreateDate(new Date());
            tbBrandGuideCollectionMapper.updateById(item);
        }
    }

    @Override
    public List<String> getBrandGuideList(String groupId, String mallId, String vipcode) {
        String redisKey = this.getBrandGuideKey(groupId, vipcode, mallId);
        if (redisService.hasKey(redisKey)) {
            return redisService.getFavoriteBrands(redisKey);
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> getBrandGuideFromDb(String groupId, String mallId, String vipcode) {
        return tbBrandGuideCollectionMapper.findByGroupIdAndMallIdAndVipcode(groupId, mallId, vipcode);
    }

    public String getBrandGuideKey(String groupId, String vipcode, String mallId) {
        return String.format(RedisCacheKey.BRAND_GUIDE_CACHE_KEY, groupId, vipcode, mallId);
    }

}
