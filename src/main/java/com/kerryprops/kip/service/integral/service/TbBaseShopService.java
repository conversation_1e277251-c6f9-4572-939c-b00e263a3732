package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbBaseShop;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 15:53
 **********************************************************************************************************************/
public interface TbBaseShopService {

    /**
     * 通过CRM自定义的店铺编号查询店铺信息
     * @param contractNo
     * @return
     */
    TbBaseShop selectByContractNo(String contractNo);

    /**
     * 优惠买单自动积分传店铺id
     * @param tenantId 店铺id
     * @param mallId 商场id
     * @return
     */
    TbBaseShop getByTenantId(String tenantId, String mallId);

    TbBaseShop getByContractNoAndMallId(String contractNo, String mallId);

    /**
     * 通过合同号获取店铺信息
     * @param doCos 合同号
     * @param mallId 商场号
     * @return
     */
    TbBaseShop getShopByDoCos(String doCos, String mallId);

    /**
     * 根据groupId + 业态列表查询店铺编号
     * @param groupId
     * @param formats
     * @return
     */
    List<String> getContractNoListByGroupIdAndFormats(String groupId, List<String> formats);

    /**
     * 根据不同业态获取不同店铺
     * @param groupId
     * @param formats
     * @return
     */
    List<String> getContractNoList(String groupId, String formats);

}
