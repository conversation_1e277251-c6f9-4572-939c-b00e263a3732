package com.kerryprops.kip.service.integral.webservice.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/21/2024 15:26
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "积分抵现前端自定义金额响应类")
public class PointsCustomAmountResponse implements Serializable {

    @Schema(description = "自定义金额")
    private BigDecimal amount;

    @Schema(description = "自定义金额对应积分数")
    private Integer points;

}
