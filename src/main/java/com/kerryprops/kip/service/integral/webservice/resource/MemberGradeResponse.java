package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/28/2022 15:34
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "查询品牌下会员等级响应类")
public class MemberGradeResponse implements Serializable {

    @Schema( description = "主键ID")
    private String id;

    @Schema( description = "等级编码")
    private String membergrade;

    @Schema( description = "等级名称")
    private String desc;

    @Schema( description = "等级顺序")
    private Integer sort;

    @Schema( description = "会员等级说明")
    private String description;

    @Schema( description = "描述备注")
    private String remark;

    @Schema( description = "品牌ID")
    private String groupId;

    @Schema( description = "会员卡封面图片地址")
    private String cardCoverUrl;

    @Schema( description = "会员卡封面图片地址")
    private String cardCoverHomeUrl;

    @Schema( description = "是否参与升级，保级降级  1 参与   0 不参与")
    private Integer upGradationStatus;

}
