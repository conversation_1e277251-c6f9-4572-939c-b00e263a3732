package com.kerryprops.kip.service.integral.webservice.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/28/2022 15:34
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "异常明细")
public class PagePointsInterceptDetailResponse implements Serializable {

    @Schema( name = "异常明细")
    private List<TbPointsInterceptDetailResponse> content;

    private int totalElements;

    private int totalPages;
}
