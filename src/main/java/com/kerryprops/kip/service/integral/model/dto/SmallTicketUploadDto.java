package com.kerryprops.kip.service.integral.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 06/27/2024 14:26
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmallTicketUploadDto implements Serializable {

    private Integer code;

    @JsonProperty(value = "task_id")
    private String taskId;

    /**
     * 报错时，错误信息
     */
    private String msg;

}
