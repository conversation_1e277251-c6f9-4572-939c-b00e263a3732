package com.kerryprops.kip.service.integral.service.promotion.impl;

import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/17/2023 12:25
 **********************************************************************************************************************/

@Slf4j
@Component
public class WechatAutoPointsPromotionRule implements PromotionRule {

    @Override
    public String getRuleType() {
        return "9";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        String content = condition.getPromotionConditionContent();
        if (StringUtils.isBlank(content)) {
            return false;
        }
        if (content.contains("1") && StringUtils.equals(dto.getRemark(), IntegralConstant.WECHAT_POINTS)) {
            return true;
        }
        if (content.contains("2") && StringUtils.equals(dto.getRemark(), IntegralConstant.ALIPAY_POINTS)) {
            return true;
        }
        return false;
    }
}
