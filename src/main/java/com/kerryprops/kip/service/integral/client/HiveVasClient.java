package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.vo.TenantAllVo;
import com.kerryprops.kip.service.integral.model.vo.TenantBuildingResVo;
import com.kerryprops.kip.service.integral.model.vo.TenantRespVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/06/2022 12:03
 **********************************************************************************************************************/

@FeignClient(name = "hive-vas", url = "${kerry.services.hive-vas:default}")
public interface HiveVasClient {

    @GetMapping("/hiveas/tenant")
    List<TenantAllVo> getTenantInfo(@RequestParam("contractNo") String contractNo, @RequestParam("needRetailField") Integer needRetailField, @RequestParam("onlyTenant") Integer onlyTenant);

    @GetMapping("/hiveas/lbs")
    List<HiveLbsInfoDto> getLbsInfo(@RequestParam("lbsId") List<String> lbsId);

    @GetMapping("/hiveas/lbs")
    List<HiveLbsInfoDto> getLbsList(@RequestParam("brandId") List<String> brandId, @RequestParam("formats") String formats);

    @GetMapping(value = "/hiveas/brand_guide_home_page")
    List<BrandGuideRespDto> getBrandGuideHomePage(@RequestParam("lbsId") String mallId);

    @GetMapping(value = "/hiveas/v2/tenant/id/{doCos}?needRetailField=1")
    KerryResultDto<List<TenantRespVo>> getShopByDoCos(@RequestParam("doCos") String doCos);

    @GetMapping(value = "/hiveas/v2/building/list/{buildingIds}")
    KerryResultDto<List<TenantBuildingResVo>> getBuildingByBuildingIds(@RequestParam("buildingIds") String buildingIds);

    @PostMapping(value = "/brand_guide/brand_guide_search")
    List<BrandGuideRespDto> getBrandGuideList(@RequestBody BrandGuideQueryDto dto);

    /**
     * 品牌导览详情
     * @param id
     * @return
     */
    @GetMapping(value = "/brand_guide/{id}")
    List<BrandGuideRespDto> getBrandGuideDetail(@PathVariable("id") String id);

    /**
     * 根据租户id查询租户信息
     * @param tenantIds
     * @return
     */
    @GetMapping(value = "/hiveas/v2/tenant/list/{tenantIds}")
    KerryResultDto<List<TenantAllVo>> getTenantById(@PathVariable("tenantIds") String tenantIds, @RequestParam("needRetailField") Integer needRetailField);

}
