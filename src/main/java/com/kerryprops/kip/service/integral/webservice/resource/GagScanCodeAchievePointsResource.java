package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/08/2024 10:38
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "扫码积分实现积分请求类")
public class GagScanCodeAchievePointsResource implements Serializable {

    @NotBlank(message = "必填参数不能为空")
    @Schema( description = "二维码单号")
    private String serialnum;

    @Schema( description = "销售金额")
    private BigDecimal money;

    @Schema( description = "销售日期")
    private String tradingDate;

    @Schema( description = "优惠金额")
    private BigDecimal discount;

    @NotBlank(message = "必填参数不能为空")
    @Schema( description = "集团id")
    private String groupId;

    @Schema( description = "店铺号")
    private String contractno;

    @NotBlank(message = "必填参数不能为空")
    @Schema( description = "所属商场id")
    private String mallId;

    @NotBlank(message = "必填参数不能为空")
    @Schema( description = "手机号")
    private String mobile;

    @NotBlank(message = "必填参数不能为空")
    @Schema( description = "会员号")
    private String vipcode;

    @Schema( description = "店铺号")
    private String shopid;

    @Schema( description = "来源")
    private String source;

}
