package com.kerryprops.kip.service.integral.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - saas
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/08/2022 17:25
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsSendVo implements Serializable {

    private List<String> phoneNumbers;

    private String content;

    /**
     * 楼盘id
     */
    private String projectId;

    @Builder.Default
    private String smsType = "NOTIFICATION";

    /**
     * 业态
     */
    @Builder.Default
    private String assetType = "RETAIL";

}
