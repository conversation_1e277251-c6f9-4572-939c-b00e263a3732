package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeRule
 * @Description 会员等级变更规则
 * @date 2022/9/27 14:50
 * @Version 1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_grade_rule")
@ToString
public class TbMemberGradeRule implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场id
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 上级id
     */
    @TableField("pid")
    private Long pid;

    /**
     * 会员等级编码
     */
    @TableField("grade_code")
    private String gradeCode;

    /**
     * 规则类型（1、每日消费金额升级；2、累计消费金额升级；3、入会当日消费金额升级；4、会员身份认证升级；5、每日消费保级；6、累计消费保级；7、会员身份认证保级；8、会员月累计消费升级；9、会员月累计消费保级）
     */
    @TableField("rule_type")
    private Integer ruleType;

    /**
     * 消费金额（适用1、2、3）
     */
    @TableField("money")
    private BigDecimal money;

    /**
     * 业态编码（多条业态编码逗号分隔，为空则为全业态)
     */
    @TableField("formats")
    private String formats;

    /**
     * 业态描述
     */
    @TableField("formats_desc")
    private String formatsDesc;

    /**
     * 累计类型（1、自然年；2、周期年；适用于规则类型条件2和6）
     */
    @TableField("sum_type")
    private Integer sumType;

    /**
     * 类型（0、保级；1、升级）
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 是否用于计算成长进度（0、否；1、是）
     */
    @TableField("is_count")
    private Integer isCount;

    /**
     * 规则生效日期，
     * https://kerryprops.atlassian.net/browse/SCRM-7647（Tick 4.4.0）
     */
    @TableField("effective_date")
    private Date effectiveDate;

    /**
     * 创建者
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    @TableField("certification")
    private String certification;

    private transient List<TbMemberGradeRule> joinDayParallelRules;

}
