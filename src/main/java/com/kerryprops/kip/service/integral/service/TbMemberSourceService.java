package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberSource;
import com.kerryprops.kip.service.integral.model.dto.MemberRegisterResourceDto;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/23/2022 15:19
 **********************************************************************************************************************/
public interface TbMemberSourceService extends IService<TbMemberSource> {

    void checkAndSave(MemberRegisterResourceDto dto, TbMemberAsset tbMemberAsset);

}
