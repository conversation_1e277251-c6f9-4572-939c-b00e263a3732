package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cfg_authorizer_wx
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_cfg_authorizer_wx")
public class TbCfgAuthorizerWx implements Serializable {
    /**
     * 唯一值，主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 商场编号
     */
    @TableField("mallid")
    private String mallId;

    /**
     * 授权方昵称
     */
    @TableField("nickName")
    private String nickName;

    /**
     * 授权方头像
     */
    @TableField("headImg")
    private String headImg;

    /**
     * 授权方公众号类型，0代表订阅号，1代表由历史老帐号升级后的订阅号，2代表服务号
     */
    @TableField("serviceTypeInfo")
    private String serviceTypeInfo;

    /**
     * 授权方认证类型，-1代表未认证，0代表微信认证，1代表新浪微博认证，2代表腾讯微博认证，3代表已资质认证通过但还未通过名称认证，4代表已资质认证通过、还未通过名称认证，但通过了新浪微博认证，5代表已资质认证通过、还未通过名称认证，但通过了腾讯微博认证
     */
    @TableField("verifyTypeInfo")
    private String verifyTypeInfo;

    /**
     * 授权方公众号的原始ID
     */
    @TableField("userName")
    private String userName;

    /**
     * 公众号的主体名称
     */
    @TableField("principalName")
    private String principalName;

    @TableField("businessInfo")
    private String businessInfo;

    /**
     * 授权方公众号所设置的微信号，可能为空
     */
    @TableField("alias")
    private String alias;

    /**
     * 二维码图片的URL，开发者最好自行也进行保存
     */
    @TableField("qrcodeUrl")
    private String qrcodeUrl;

    /**
     * 用户授权的公众号appId
     */
    @TableField("authorizerAppid")
    private String authorizerAppId;

    /**
     * 授权给开发者的权限集列表
     */
    @TableField("funcInfo")
    private String funcinfo;

    /**
     * 账号介绍
     */
    @TableField("signature")
    private String signature;

    /**
     * 可根据这个字段判断是否为小程序类型授权
     */
    @TableField("miniProgramInfo")
    private String miniProgramInfo;

    /**
     * 1代表顾客小程序，2代表商户小程序，3代表商管小程序，4代表公众号，5代表商户公众号
     */
    @TableField("type")
    private String type;

    /**
     * 创建人
     */
    @TableField("creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField("updater")
    private Long updater;

    /**
     * 创建时间
     */
    @TableField("create_Date")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField("update_Date")
    private Date updateDate;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

}