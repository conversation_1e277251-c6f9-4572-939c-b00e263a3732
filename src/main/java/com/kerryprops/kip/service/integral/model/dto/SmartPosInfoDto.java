package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName SmartPosInfoDto
 * @Description SmartPosInfoDto
 * @date 2022/10/25 13:54
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmartPosInfoDto implements Serializable {

    private Date tradingDate;

    private String contractNo;

    private String shopName;

    private Double paidinamount;

    private Double receivableAmount;

    private String traceno;

    private String maiilName;

    private String mallid;

    private String groupId;

}
