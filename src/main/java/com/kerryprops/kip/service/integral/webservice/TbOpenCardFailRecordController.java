package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.pmw.client.resource.PointsActivationConfirmResource;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbOpenCardFailRecord;
import com.kerryprops.kip.service.integral.model.dto.CustomerThirdPartyDto;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.KerryStaffService;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsAuthRecordService;
import com.kerryprops.kip.service.integral.service.TbOpenCardFailRecordService;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 01/16/2024 15:40
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/open_card/fail")
@RestController
@Hidden
@RequiredArgsConstructor
public class TbOpenCardFailRecordController {

    private final TbOpenCardFailRecordService tbOpenCardFailRecordService;
    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private final MallConfig mallConfig;
    private final KerryStaffService kerryStaffService;

    @GetMapping("check_and_save_wechat_record")
    public String checkAndInsertRecord(@RequestParam("mobile") String mobile, @RequestParam("mallId") String mallId) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        if (Objects.isNull(mallItem)) {
            return "商场不存在，请核实";
        }
        CustomerUserDto userDto = kerryStaffService.findByMobile(mobile);
        if (Objects.isNull(userDto)) {
            return "手机号对应的用户不存在";
        }
        CustomerThirdPartyDto thirdPartyDto = kerryStaffService.getWxInfoByMobileAndAppId(mobile, mallItem.getAppId());
        if (Objects.isNull(thirdPartyDto)) {
            return "手机号对应的用户openId不存在";
        }
        TbOpenCardFailRecord cardFailRecord = tbOpenCardFailRecordService.queryByOpenIdAndMallId(thirdPartyDto.getOpenId(), mallItem.getMallId());
        if (Objects.isNull(cardFailRecord)) {
            return "手机号对应的用户无微信支付即积分授权失败记录";
        }
        // 授权记录
        TbInsensatePointsAuthRecord authRecord = tbInsensatePointsAuthRecordService.queryByOpenIdAndMallId(thirdPartyDto.getOpenId(), mallItem.getMallId());
        if (Objects.nonNull(authRecord)) {
            return "手机号对应的用户微信授权信息已存在";
        }
        PointsActivationConfirmResource.PointsActivationDetailResource body = JsonUtils.stringToObj(cardFailRecord.getContent(), PointsActivationConfirmResource.PointsActivationDetailResource.class);
        if (Objects.isNull(body)) {
            return "授权失败记录内内容为空";
        }
        authRecord = TbInsensatePointsAuthRecord.builder().openId(cardFailRecord.getOpenId()).kipUserId(userDto.getId()).cardNo(body.getCode())
                .groupId(mallItem.getGroupId()).mallId(mallItem.getMallId()).status(0).origin(InsensateOriginEnum.WECHAT.getValue()).mchid(body.getMchId()).build();
        tbInsensatePointsAuthRecordService.insertJudgment(authRecord);
        return "新增成功";
    }

}
