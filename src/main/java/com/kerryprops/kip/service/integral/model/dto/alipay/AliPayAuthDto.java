package com.kerryprops.kip.service.integral.model.dto.alipay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝授权参数接受类
 * @createDate 2022/10/20
 * @updateDate 2022/10/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
public class AliPayAuthDto extends AliPayNotifyDto {

    /**
     * 用户授权token
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private AliPayMemberTokenDto memberToken;

    /**
     * 用户授权提交表单信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private ActiveFormDto activeForm;

    /**
     * auth_code作为换取access_token的票据，每次用户授权完成，回调地址中的auth_code将不一样，auth_code只能使用一次，一天未被使用自动过期
     */
    @JsonProperty("auth_code")
    private String authCode;

    /**
     * template_id : 模板id auth_code
     */
    @JsonProperty("template_id")
    private String templateId;

    /**
     * request_id 参数使用“会员卡表单信息查询接口”从支付宝查询用户此次领取会员卡表单提交的信息，用户后续会员卡开卡
     */
    @JsonProperty("request_id")
    private String requestId;

    /**
     * 商户授权,用来换取app_auth_token
     */
    @JsonProperty("app_auth_code")
    private String appAuthCode;

}
