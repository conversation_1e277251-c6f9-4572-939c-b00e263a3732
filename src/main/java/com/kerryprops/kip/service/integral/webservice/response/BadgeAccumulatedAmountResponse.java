package com.kerryprops.kip.service.integral.webservice.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "徽章累计金额返回对象")
public class BadgeAccumulatedAmountResponse implements Serializable {

    @Schema( name = "累计金额")
    private BigDecimal amount;

}
