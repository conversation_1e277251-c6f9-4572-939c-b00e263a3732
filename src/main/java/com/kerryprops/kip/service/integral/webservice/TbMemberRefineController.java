package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.service.TbMemberRefineService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberProfileCheckResource;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberRefineResource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;


/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/17/2022 14:14
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/member/refine")
@RestController
@AllArgsConstructor
@Tag(name = "完善配置相关API")
public class TbMemberRefineController {

    private final TbMemberRefineService tbMemberRefineService;
    private final RabbitMqService rabbitMqService;

    @GetMapping("/config")
    @Operation(summary="获取完善信息页面配置信息", method = "GET")
    public TbMemberRefineResource findConfigByMallId(@CurrentUser LoginUser loginUser) {
        if (StringUtils.isBlank(loginUser.getLbsId())) {
            return null;
        }
        return tbMemberRefineService.getByMallId(loginUser.getLbsId());
    }

    /**
     * 完善信息必填项是否已完善逻辑判断-内部服务之间使用
     * @param resource
     */
    @PostMapping("/judgement")
    public void judgeMemberInfo(@Valid @RequestBody MemberProfileCheckResource resource) {
        rabbitMqService.sendMessage(RabbitMqConstant.XCRM_MEMBER_REFINE_CHECK, JsonUtils.objToString(resource));
    }

}
