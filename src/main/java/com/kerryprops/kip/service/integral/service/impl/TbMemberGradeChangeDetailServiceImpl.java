package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.MessageTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeChangeDetailMapper;
import com.kerryprops.kip.service.integral.model.dto.ClearMemberSalesSumDto;
import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeChangeDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbMemberGradeChangeDetailServiceImpl extends ServiceImpl<TbMemberGradeChangeDetailMapper, TbMemberGradeChangeDetail> implements TbMemberGradeChangeDetailService {

    private final TbMemberGradeChangeDetailMapper tbMemberGradeChangeDetailMapper;
    private final RabbitMqService rabbitMqService;

    @Override
    public TbMemberGradeChangeDetail queryChangeMaxByGroupIdAndVipcode(String groupId, String vipcode) {
        return tbMemberGradeChangeDetailMapper.queryChangeMaxByGroupIdAndVipcode(groupId, vipcode);
    }

    @Override
    public TbMemberGradeChangeDetail getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(String groupId, String vipcode, String overDate) {
        return tbMemberGradeChangeDetailMapper.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(groupId, vipcode, overDate);
    }

    @Override
    public List<TbMemberGradeChangeDetail> getChangeDetailList(String groupId, String vipcode) {
        return tbMemberGradeChangeDetailMapper.getChangeDetailList(groupId, vipcode);
    }

    @Override
    public void fillMemberGradeChangeDetailMallId(String groupId, String vipcode, String mallId) {
        TbMemberGradeChangeDetail detail = tbMemberGradeChangeDetailMapper.queryChangeMaxByGroupIdAndVipcode(groupId, vipcode);
        if (Objects.isNull(detail) || StringUtils.isNotBlank(detail.getMallId())) {
            return;
        }
        detail.setMallId(mallId);
        tbMemberGradeChangeDetailMapper.updateById(detail);
    }

    @Async
    @Override
    public void reduceMemberGradeChangeDate(String groupId, String date) {
        String createDate = String.format("%s 00:00:00", date);
        List<TbMemberGradeChangeDetail> details = tbMemberGradeChangeDetailMapper.queryDetailByGroupIdAndDate(groupId, createDate);
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        log.info("总共查询到: {} 会员等级变更数据时间错误.", details.size());
        for (TbMemberGradeChangeDetail detail : details) {
            if (Objects.isNull(detail.getCreateDate())) {
                continue;
            }
            log.info("vipcode: [{}]正在变更时间.", detail.getVipcode());
            Date initDate = DateUtil.offsetMonth(detail.getCreateDate(), -12);
            detail.setCreateDate(initDate);
            tbMemberGradeChangeDetailMapper.updateChangeDate(detail);
        }
        log.info("***** 会员等级变更数据时间错误处理已完成 *******");
    }

    @Override
    public void saveDetail(TbMemberGradeChangeDetail changeDetail) {
        tbMemberGradeChangeDetailMapper.insert(changeDetail);
        // 删缓存及发模版消息
        this.unifiedOperation(changeDetail);
    }

    /**
     * 删除redis内缓存的会员销售额数据及发送等级变更模版消息
     * @param detail
     */
    private void unifiedOperation(TbMemberGradeChangeDetail detail) {
        // 前后等级不同，则说明等级发生了变更
        if (!StringUtils.equals(detail.getOldGrade(), detail.getNewGrade())) {
            SendMessageDto messageDto = SendMessageDto.builder().messageType(MessageTypeEnum.GRADE_CHANGE.getType()).templateType(1)
                    .memberId(detail.getMemberId()).mallId(detail.getMallId()).build();
            rabbitMqService.sendLazyMessage(RabbitMqConstant.XCRM_MEMBER_INTEGRAL_GRADE_CHANGE, JsonUtils.objToString(messageDto), 2);
        }
        // 删除缓存的年月日销售金额数据
        this.removeMemberSaleData(detail.getGroupId(), detail.getVipcode(), detail.getKipUserId());
    }

    /**
     * 删除会员等级缓存的消费金额信息
     * @param groupId
     * @param vipcode
     */
    private void removeMemberSaleData(String groupId, String vipcode, String kipUserId) {
        if (StringUtils.isAnyBlank(groupId, vipcode)) {
            return;
        }
        log.info("开始清除会员销售金额缓存数据-{}", vipcode);
        ClearMemberSalesSumDto sumDto = ClearMemberSalesSumDto.builder().groupId(groupId).vipcode(vipcode).kipUserId(kipUserId).build();
        rabbitMqService.sendMessage(RabbitMqConstant.XCRM_MEMBER_SALE_SUM, JsonUtils.objToString(sumDto));
    }

    @Override
    public void saveMemberGradeChangeRecord(TbMemberAsset tbMemberAsset, Integer changeType, String oldGrade, String newGrade, String gradeDesc, String userName) {
        TbMemberGradeChangeDetail entity = new TbMemberGradeChangeDetail();
        entity.setId(IdUtil.simpleUUID());
        entity.setOldGrade(oldGrade);
        entity.setNewGrade(newGrade);
        entity.setVipcode(tbMemberAsset.getVipcode());
        entity.setRemark(gradeDesc);
        entity.setCreateDate(new Date());
        entity.setGroupId(tbMemberAsset.getGroupId());
        entity.setChangeType(changeType);
        entity.setNickName(tbMemberAsset.getNickName());
        entity.setMobile(tbMemberAsset.getMobile());
        entity.setMallId(tbMemberAsset.getMallId());
        entity.setCreateUser(userName);
        entity.setAuthorizedMallId(tbMemberAsset.getAuthorizedMallId());
        tbMemberGradeChangeDetailMapper.insert(entity);
        // 删缓存及发模版消息
        entity.setMemberId(String.valueOf(tbMemberAsset.getId()));
        entity.setKipUserId(tbMemberAsset.getKipUserId());
        this.unifiedOperation(entity);
    }

    @Override
    public List<TbMemberGradeChangeDetail> queryChangeGradeListBySaleDate(String groupId, String vipcode, String saleDate) {
        return tbMemberGradeChangeDetailMapper.queryChangeGradeListBySaleDate(groupId, vipcode, saleDate);
    }

    @Override
    public TbMemberGradeChangeDetail getMaxRelegationRecordByDate(String groupId, String vipcode, String createDate, List<Integer> changeTypes) {
        return tbMemberGradeChangeDetailMapper.getMaxRelegationRecordByDate(groupId, vipcode, createDate, changeTypes);
    }

    @Override
    public List<TbMemberGradeChangeDetail> getChangeDetailGtCreateDate(String groupId, String vipcode, String createDate, List<Integer> changeTypes) {
        return tbMemberGradeChangeDetailMapper.getChangeDetailGtCreateDate(groupId, vipcode, createDate, changeTypes);
    }

    @Override
    public void changeMemberGradeChangeDetailDate(List<TbMemberGradeChangeDetail> details, Date createDate) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        int second = details.size();
        for (TbMemberGradeChangeDetail detail: details) {
            detail.setCreateDate(DateUtil.offsetSecond(createDate, -second));
            tbMemberGradeChangeDetailMapper.updateById(detail);
            second -= 1;
        }
    }

    @Override
    public TbMemberGradeChangeDetail queryNearChangeGradeBySaleDate(String groupId, String vipcode, String saleDate) {
        return tbMemberGradeChangeDetailMapper.queryNearChangeGradeBySaleDate(groupId, vipcode, saleDate);
    }

    @Override
    public String getJoinDayMaxGrade(String vipcode, String groupId, String joinTime) {
        return tbMemberGradeChangeDetailMapper.getJoinDayMaxGrade(vipcode, groupId, joinTime);
    }
}
