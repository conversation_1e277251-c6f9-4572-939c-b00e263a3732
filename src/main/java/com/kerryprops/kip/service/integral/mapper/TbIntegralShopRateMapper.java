package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/13/2023 09:17
 **********************************************************************************************************************/
public interface TbIntegralShopRateMapper extends BaseMapper<TbIntegralShopRate> {

    List<TbIntegralShopRate> shopRatePage(@Param("groupId") String groupId, @Param("mallIds") List<String> mallIds, @Param("shopId") String shopId, @Param("offset") int offset, @Param("size") int size);

    int getTotal(@Param("groupId") String groupId, @Param("mallIds") List<String> mallIds, @Param("shopId") String shopId);

    List<TbIntegralShopRate> getShopRateList(TbIntegralShopRate rate);

    void saveShopRateList(@Param("rateList") List<TbIntegralShopRate> rateList);

    void deleteByParams(@Param("mallId") String mallId, @Param("shopIdList") List<String> shopIdList);

    TbIntegralShopRate getShopRateDto(TbIntegralShopRate build);
}
