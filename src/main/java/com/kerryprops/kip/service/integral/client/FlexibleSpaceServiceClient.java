package com.kerryprops.kip.service.integral.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "flexible-space", url = "${kerry.services.flexible-space:default}")
public interface FlexibleSpaceServiceClient {

    @GetMapping(value = "/vip-room/order/exist/{brandId}/{kipUserId}")
    boolean checkMemberVipRoomOrderExists(@PathVariable("brandId") String groupId, @PathVariable("kipUserId") String kipUserId);

}
