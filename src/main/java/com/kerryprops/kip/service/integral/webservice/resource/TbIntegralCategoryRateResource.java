package com.kerryprops.kip.service.integral.webservice.resource;

import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/17/2023 13:39
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbIntegralCategoryRateResource implements Serializable {

    @NotBlank(message = "groupId不能为空")
    private String groupId;

    @NotBlank(message = "categoryId不能为空")
    private String categoryId;

    private String mallId;

    private String categoryName;

    private int status;

    private String creator;

    /**
     * 比例是否统一 0 不统一 1 统一
     */
    private int isConsistent;

    private List<GradeIntegralResponse> list;

}