package com.kerryprops.kip.service.integral.service;

import com.alipay.api.domain.MerchantCard;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.ActiveFormDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayAuthDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayBizContentDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayMemberTokenDto;
import com.kerryprops.kip.service.integral.webservice.resource.AlipayCardTemplateUpdateResource;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AliPayClientService {
    String ERROR_PAGE = "http://aqhcrm.aegeangroup.com.cn/KouBei/modules/h5/error.html";
    String AUTH_CODE = "authorization_code";
    String BIZ_CARD = "BIZ_CARD";
    String EXTERNAL_CARD = "EXTERNAL_CARD";
    String MEMBER_CARD = "MEMBER_CARD";
    String VALID_DATE = "3022-10-26 18:04:25";

    /**
     * 开卡URL
     */
    String OPEN_CARD_URL = "https://memberprod.alipay.com/account/openform/activecard.html?";

    /**
     * 支付宝会员开卡form表单属性
     */
    String OPEN_FORM_FIELD_BIRTHDAY = "OPEN_FORM_FIELD_BIRTHDAY";
    String OPEN_FORM_FIELD_GENDER = "OPEN_FORM_FIELD_GENDER";
    String OPEN_FORM_FIELD_MOBILE = "OPEN_FORM_FIELD_MOBILE";
    String OPEN_FORM_FIELD_NAME = "OPEN_FORM_FIELD_NAME";

    /**
     * 订阅支付消息推送
     * @throws Exception Exception
     * @param dto dto
     * @return String String
     */
    String openAlipayMarketingCard(AliPayAuthDto dto) throws Exception;

    /**
     * querySchemaUrl
     * @param cardNo cardNo
     * @param  mallItem mallItem
     * @return String String
     */
    String querySchemaUrl(String cardNo, MallItem mallItem);

    /**
     * 订阅支付消息推送
     * @param mallItem mallItem
     * @return String String
     */
    String cardMarketingTradeSubscribe(MallItem mallItem);

    /**
     * 创建会员
     * @param orderId orderId
     * @param orderName orderName
     * @param payPrice payPrice
     * @return  创建支付订单
     */
    String create(String orderId, String orderName, String payPrice);

    String createAliPayCardUrl(MallItem mallId);

    String queryCardTemplate(MallItem mallItem);

    String getAlipayMarketingCardActivateurl(MallItem mallItem);

    String getAlipayMarketingCardFormtemplateSetRequest(MallItem mallItem);

    String configTemplate(MallItem mallItem);

    String createCardTemplate(MallItem mallItem);

    Boolean validateSignature(Map<String, String> params, String abbreviation);

    /**
     * 经过积分调整操作后的用户表，应该具有member表原有的字段与响应的积分变动记录信息
     * @param dto dto
     */
    void syncAlipayPoints(SalesAutoPointsDto dto);

    /**
     * 查询支付宝开通的商圈会员卡明细
     * @param userUniId 支付宝会员id
     * @param bizCardNo bizCardNo
     * @param mallId mallId
     * @return MerchantCard MerchantCard
     */
    MerchantCard getAliMemberCardDetail(String userUniId, String bizCardNo, String mallId);

    /**
     * clearingNotify
     * @param callbackDto callbackDto
     * @param member      经过积分调整操作后的用户表，应该具有member表原有的字段与响应的积分变动记录信息
     */
    void clearingNotify(AliPayBizContentDto callbackDto, SalesAutoPointsDto member);

    /**
     * @param cardNo 会员卡号
     * @return 会员卡相关信息
     */
    MerchantCard memberCardDetailByCardNo(String cardNo, String mallId);

    /**
     * @param aliUserId 阿里用户id
     * @return 会员卡相关信息
     */
    MerchantCard memberCardDetailByAliUserId(String aliUserId, String mallId);

    /**
     * memberCardDetailByMobile
     * @param mallId  mallId
     * @param mobile 手机号
     * @return 会员卡相关信息
     */
    MerchantCard memberCardDetailByMobile(String mobile, String mallId);

    /**
     * 获取支付宝AccessToken
     * @throws  JsonProcessingException JsonProcessingException
     * @param dto dto
     * @return AliPayMemberTokenDto
     */
    ActiveFormDto queryActiveForm(AliPayAuthDto dto) throws JsonProcessingException;

    /**
     * 获取支付宝AccessToken
     * @param authCode authCode
     * @param abbreviation abbreviation
     * @return AliPayMemberTokenDto
     */
    AliPayMemberTokenDto getAccessToken(String authCode, String abbreviation);

    /**
     * 支付宝无感积分会员卡背景图片上传
     * @param path path
     * @param mallId mallId
     */
    void uploadAlipayCardBackgroundImage(String path, String mallId);

    /**
     * 更新会员卡配置
     * @throws Exception Exception
     * @param resource resource
     */
    void updateAlipayCardTemplateRequest(AlipayCardTemplateUpdateResource resource) throws Exception;

}
