package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.service.TbMemberGradeChangeDetailService;
import com.kerryprops.kip.service.integral.webservice.response.LastProtectionGradeDateResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/11/2023 13:39
 **********************************************************************************************************************/

@Slf4j
@RestController
@RequestMapping("/member/grade_change")
@RequiredArgsConstructor
@Tag(name="会员升降级明细API")
public class RestTbMemberGradeChangeDetailController {

    private final TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;

    @GetMapping("/next_relegation_date")
    @Operation(summary="查询会员下一次保级时间API", method = "GET")
    public LastProtectionGradeDateResponse getMemberLastGradeChangeDetail(@RequestParam("groupId") String groupId, @RequestParam("vipcode") String vipcode) {
        TbMemberGradeChangeDetail detail = tbMemberGradeChangeDetailService.queryChangeMaxByGroupIdAndVipcode(groupId, vipcode);
        if (Objects.isNull(detail)) {
            return null;
        }
        return LastProtectionGradeDateResponse.builder().nextDate(DateUtil.offsetMonth(detail.getCreateDate(), 12)).build();
    }

}
