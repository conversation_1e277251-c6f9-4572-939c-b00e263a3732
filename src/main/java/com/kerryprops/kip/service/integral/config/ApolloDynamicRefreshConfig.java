package com.kerryprops.kip.service.integral.config;

import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.scope.refresh.RefreshScope;
import org.springframework.context.annotation.Configuration;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/09/2023 21:52
 **********************************************************************************************************************/


@Slf4j
@Configuration
@RequiredArgsConstructor
public class ApolloDynamicRefreshConfig {

    private final RefreshScope refreshScope;
    private static final String MALL_LIST = "mall";
    private static final String AB_NORMAL =  "abnormal";
    private static final String TEXT_IN =  "textin";

    @ApolloConfigChangeListener(interestedKeyPrefixes = {MALL_LIST, AB_NORMAL, TEXT_IN}, value = "application.yml")
    public void changeHandler(ConfigChangeEvent changeEvent) {
        for (String changedKey : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(changedKey);
            log.info("配置发生变化 {}", change.toString());
            if (changedKey.startsWith(MALL_LIST)) {
                refreshScope.refresh("mallConfig");
            } else if (changedKey.startsWith(AB_NORMAL)){
                refreshScope.refresh("notifyEmailProperties");
            } else if (changedKey.startsWith(TEXT_IN)) {
                // 动态刷新ocr配置信息
                refreshScope.refresh("smallTicketProperties");
            }
        }
    }

}
