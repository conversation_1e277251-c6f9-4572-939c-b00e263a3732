package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/09/2022 12:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "商圈会员待积分状态查询相应类")
public class QueryPointsCommitStatusResource implements Serializable {

    @Schema( description = "商圈会员待积分状态: PENDING: 需要弹窗提示补录, FINISHED: 不需要提示补录")
    private String pointsCommitStatus;

    @Schema( description = "微信商圈id")
    private String mchId;

    @Schema( description = "是否授权微信无感积分， true: 已授权，false：未授权")
    private Boolean wxAuth;

    @Schema( description = "会员微信openId")
    private String openId;

    @Schema( description = "该商场是否开通OCR拍照积分")
    private Boolean ocr;

}
