package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.TbInsensatePointsAuthRecordDto;
import com.kerryprops.kip.service.integral.webservice.resource.MemberAutoPointResource;

import java.util.List;

/**
 * @description 无感积分 微信、支付宝授权记录表
 * <AUTHOR>
 * @date 2022-09-08
 */
public interface TbInsensatePointsAuthRecordService extends IService<TbInsensatePointsAuthRecord> {

    /**
     * 新增授权标识数据插入判断
     * @param tbInsensatePointsAuthRecord
     */
    void insertJudgment(TbInsensatePointsAuthRecord tbInsensatePointsAuthRecord);

    /**
     * 补齐授权记录
     * @param record
     */
    void insertAuthRecord(TbInsensatePointsAuthRecord record);

    /**
     * 删除
     *
     * @return
     */

    /**
     * 更新
     */
    int update(TbInsensatePointsAuthRecord tbInsensatePointsAuthRecord);

    /**
    * 根据主键 id 查询
    */
    TbInsensatePointsAuthRecordDto getByWeChatOpenId(String openid, String mallId, String groupId);

    /**
     * 查询用户对应的openId信息
     * @param kipUserId
     * @param mallId
     * @param origin
     * @return
     */
    TbInsensatePointsAuthRecord getByKipUserIdAndMallIdAndOrigin(String kipUserId, String mallId, int origin);

    /**
     * 检查HKC用户在老的嘉里中心微信无感积分授权状态
     * @param kipUserId
     * @param mallId
     * @param origin
     * @return
     */
    TbInsensatePointsAuthRecord checkHkcMemberAuthStatus(String kipUserId, String mallId, int origin);

    /**
     * 通过openId+mallId+origin查询授权记录是否存在
     * @param openId
     * @param mallId
     * @return
     */
    TbInsensatePointsAuthRecord queryByOpenIdAndMallId(String openId, String mallId);

    /**
     * @param openid
     * @param mallId
     * @return  获取支付宝用户信息
     */
    TbInsensatePointsAuthRecordDto getByAliUserId(String openid, String mallId);

    /**
     * 获取自助积分页面配置信息
     * @param loginUser
     * @param provider
     * @param lbsId
     * @return
     */
    MemberAutoPointResource getAutoPointInfo(LoginUser loginUser, String provider, String lbsId);

    /**
     * 定时job批量拉取数据验证微信无感积分授权情况
     * @param id
     * @return
     */
    List<TbInsensatePointsAuthRecord> getBatchData(Long id, int batchSize);

    /**
     * 定时job批量拉取数据验证微信无感积分授权情况-根据商场区分
     * @param id
     * @param batchSize
     * @return
     */
    List<TbInsensatePointsAuthRecord> getMallBatchData(String mallId, Long id, int batchSize);

    /**
     * 支付宝无感积分授权状态判断
     * @param record
     */
    void checkAndSaveAliMemberOpenCard(TbInsensatePointsAuthRecord record);

    /**
     * 补齐授权记录方法
     * @param mobile
     * @param mallId
     */
    void saveAuthRecord(String mobile, String mallId);

    /**
     * 根据openId和mallId查询
     * @param openId
     * @param mallId
     * @return dto
     */
    TbInsensatePointsAuthRecord getByOpenIdAndMallId(String openId, String mallId);

    /**
     * 保存
     * @return dto
     */
    void insertBatchFromCardMemberRelation();

    /**
     * 根据openId和mallId查询
     * @param cardNo
     * @param origin
     * @return dto
     */
    TbInsensatePointsAuthRecord getByCardNoAndOrigin(String cardNo, Integer origin);

    /**
     * 检查授权状态
     * @param authRecord
     * @param mallItem
     */
    void checkAuthStatus(TbInsensatePointsAuthRecord authRecord, MallItem mallItem);

    /**
     * 查询用户微信支付即积分授权记录
     * @param kipUserId
     * @return
     */
    void recheckWxAuthRecordByKipUserId(String kipUserId);
}