package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbAutoSaleMemberTag;
import com.kerryprops.kip.service.integral.entity.TbTagMemberNologic;
import com.kerryprops.kip.service.integral.webservice.resource.MemberProfileCheckResource;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/20/2022 11:39
 **********************************************************************************************************************/
public interface TbAutoSaleMemberTagService extends IService<TbAutoSaleMemberTag> {

    /**
     * 获取二级分类标签列表
     * @param secSortId
     * @param type: 标签类型（1：逻辑标签；2：无逻辑标签）
     * @return
     */
    List<TbAutoSaleMemberTag> getBySecSortIdAndType(String secSortId, Integer type);

    List<TbAutoSaleMemberTag> getByIds(List<Long> ids);

    TbTagMemberNologic getByGroupIdAndVipcode(String groupId, String vipcode);

    /**
     * 更新会员完善信息的标签信息
     * @param resource
     */
    void updateMemberLabels(MemberProfileCheckResource resource);

}
