package com.kerryprops.kip.service.integral.webservice.resource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 09/21/2023 13:50
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlipayCardTemplateUpdateResource implements Serializable {

    @NotBlank(message = "商场id不能为空")
    private String mallId;

    /**
     * 会员卡模版logoId
     */
    @NotBlank(message = "会员卡模版logoId不能为空")
    private String logoId;

    /**
     * 背景图片id
     */
    @NotBlank(message = "会员卡模版背景图片id不能为空")
    private String backgroundId;

    /**
     * 会员卡模版名称
     */
    @NotBlank(message = "会员卡模版名称不能为空")
    private String cardShowName;

}
