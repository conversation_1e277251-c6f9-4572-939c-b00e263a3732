package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.mapper.TbAutoPointsConfigMapper;
import com.kerryprops.kip.service.integral.model.dto.HiveLbsInfoDto;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.service.TbAutoPointsConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/06/2023 16:08
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbAutoPointsConfigServiceImpl extends ServiceImpl<TbAutoPointsConfigMapper, TbAutoPointsConfig> implements TbAutoPointsConfigService {

    private final TbAutoPointsConfigMapper tbAutoPointsConfigMapper;
    private final HiveVasClient hiveVasClient;
    private final RedisService redisService;

    @Override
    @Cacheable(value = RedisCacheKey.CRM_AUTO_POINT_ENTRY_KEY, key = "#lbsId", unless = "#result == null")
    public TbAutoPointsConfig findByLbsId(String lbsId) {
        return tbAutoPointsConfigMapper.findByLbsId(lbsId);
    }

    @Override
    public TbAutoPointsConfig saveConfig(TbAutoPointsConfig config) {
        TbAutoPointsConfig autoPointsConfig = tbAutoPointsConfigMapper.findByLbsId(config.getLbsId());
        if (Objects.nonNull(autoPointsConfig)) {
            throw BizException.error(PointsEnum.LBS_CONFIG_EXISTS_ERROR);
        }
        // 填充楼盘id
        if (StringUtils.isBlank(config.getProjectId())) {
            List<HiveLbsInfoDto> lbsList = hiveVasClient.getLbsInfo(Collections.singletonList(config.getLbsId()));
            if (CollectionUtils.isNotEmpty(lbsList)) {
                config.setProjectId(lbsList.get(0).getProject().getId());
            }
        }
        Date date = new Date();
        // 默认启用
        config.setStatus(1);
        config.setCreateDate(date);
        config.setUpdateDate(date);
        if (StringUtils.isBlank(config.getUpdater())) {
            config.setUpdater(config.getCreator());
        }
        tbAutoPointsConfigMapper.insert(config);
        return config;
    }

    @Override
    public TbAutoPointsConfig updateConfig(TbAutoPointsConfig config) {
        TbAutoPointsConfig autoPointsConfig = tbAutoPointsConfigMapper.selectById(config.getId());
        if (Objects.isNull(autoPointsConfig)) {
            throw BizException.error(PointsEnum.LBS_CONFIG_NOT_EXISTS_ERROR);
        }
        autoPointsConfig.setCode(config.getCode());
        autoPointsConfig.setUpdater(config.getUpdater());
        autoPointsConfig.setUpdateDate(new Date());
        tbAutoPointsConfigMapper.updateById(autoPointsConfig);
        // 删除缓存
        this.removeCache(autoPointsConfig.getLbsId());
        return autoPointsConfig;
    }

    @Override
    public void removeConfig(Long id) {
        TbAutoPointsConfig autoPointsConfig = tbAutoPointsConfigMapper.selectById(id);
        if (Objects.isNull(autoPointsConfig)) {
            throw BizException.error(PointsEnum.LBS_CONFIG_NOT_EXISTS_ERROR);
        }
        tbAutoPointsConfigMapper.deleteById(id);
        // 删除缓存
        this.removeCache(autoPointsConfig.getLbsId());
    }

    /**
     * 删除缓存
     * @param lbsId
     */
    private void removeCache(String lbsId) {
        String entryKey = String.format("%s::%s", RedisCacheKey.CRM_AUTO_POINT_ENTRY_KEY, lbsId);
        redisService.delKeys(Collections.singletonList(entryKey));
    }

    @Override
    public TbAutoPointsConfig findById(Long id) {
        return tbAutoPointsConfigMapper.selectById(id);
    }

    @Override
    public List<TbAutoPointsConfig> list(String groupId, List<String> projectIds, int page, int size) {
        return tbAutoPointsConfigMapper.listData(groupId, projectIds,page * size, size);
    }

    @Override
    public int total(String groupId, List<String> projectIds) {
        return tbAutoPointsConfigMapper.total(groupId, projectIds);
    }
}
