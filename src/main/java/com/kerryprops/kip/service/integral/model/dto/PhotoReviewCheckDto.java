package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 08/13/2024 08:54
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhotoReviewCheckDto implements Serializable {

    /**
     * 集团id
     */
    private String groupId;

    /**
     * 商场id
     */
    private String mallId;

    /**
     * 会员号
     */
    private String vipcode;

    /**
     * 交易单号
     */
    private String ticketNo;

    /**
     * 交易店铺
     */
    private String shopNo;

    /**
     * 交易金额
     */
    private Double amount;

    /**
     * OCR拍照积分判重，只与审核通过的拍照记录比较
     */
    private String status;

    /**
     * 交易时间，格式：年-月-日 时-分-秒
     */
    private String tradingDate;

    private String timestamp;

    private List<Long> idNotIn;

}
