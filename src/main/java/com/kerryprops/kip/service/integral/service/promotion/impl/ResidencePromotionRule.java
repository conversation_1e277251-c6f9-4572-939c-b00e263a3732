//package com.kerryprops.kip.service.integral.service.promotion.impl;
//
//import com.ctrip.framework.apollo.core.utils.StringUtils;
//import com.kerryprops.kip.service.integral.config.MallConfig;
//import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
//import com.kerryprops.kip.service.integral.entity.TbActivityPromotioncondition;
//import com.kerryprops.kip.service.integral.entity.TbMember;
//import com.kerryprops.kip.service.integral.model.dto.CustomerIdentityDto;
//import com.kerryprops.kip.service.integral.model.dto.MallItem;
//import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
//import com.kerryprops.kip.service.integral.service.TbMemberProjectIdentityService;
//import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//import java.util.Optional;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// * @DESC 住户用户
// * <AUTHOR> Bert
// * Created Date - 03/09/2023 11:18
// **********************************************************************************************************************/
//
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class ResidencePromotionRule implements PromotionRule {
//
//    private final TbMemberProjectIdentityService tbMemberProjectIdentityService;
//    private final MallConfig mallConfig;
//
//    @Override
//    public String getRuleType() {
//        return "13";
//    }
//
//    @Override
//    public boolean checkRule(TbActivityPromotioncondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
//        TbMember member = dto.getMember();
//        if (StringUtils.isBlank(member.getKipUserId())) {
//            return false;
//        }
//        MallItem mallItem = mallConfig.getByMallId(dto.getMallId());
//        if (Objects.isNull(mallItem)) {
//            return false;
//        }
//        CustomerIdentityDto identity = Optional.ofNullable(dto.getIdentityDto())
//                .orElseGet(() -> tbMemberProjectIdentityService.findByKipUserIdAndProjectId(member.getKipUserId(), mallItem.getProjectId()));
//        if (Objects.isNull(dto.getIdentityDto())) {
//            dto.setIdentityDto(identity);
//        }
//        return Objects.nonNull(identity) && identity.isResidence();
//    }
//}
