package com.kerryprops.kip.service.integral.webservice.response;

import com.kerryprops.kip.service.integral.webservice.resource.TbBonusSelfResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/27/2023 11:19
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageBonusDetailResponse implements Serializable {

    private List<TbBonusSelfResponse> content;

    private int totalElements;

    private int totalPages;

}
