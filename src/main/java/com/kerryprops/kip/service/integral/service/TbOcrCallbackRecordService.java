package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbOcrCallbackRecord;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/01/2024 17:06
 **********************************************************************************************************************/
public interface TbOcrCallbackRecordService extends IService<TbOcrCallbackRecord> {

    void saveRecord(String taskId, Long photoId);

    TbOcrCallbackRecord findByTaskId(String taskId);

    TbOcrCallbackRecord findByPhotoId(Long photoId);

    /**
     * 通过拍照积分记录id，反查OCR 任务id
     * @param photoIds
     * @return
     */
    CompletableFuture<Map<Long, String>> findByPhotoIds(List<Long> photoIds);

}
