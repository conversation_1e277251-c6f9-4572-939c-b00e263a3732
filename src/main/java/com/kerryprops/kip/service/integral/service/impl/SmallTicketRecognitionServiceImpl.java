package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.client.SmallTicketRecognitionClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.OcrErrorCodeMappingEnum;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.DeferredResultCacheUtils;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.common.utils.OcrQrCodeUtils;
import com.kerryprops.kip.service.integral.common.utils.RedisCacheKeyUtils;
import com.kerryprops.kip.service.integral.config.SmallTicketProperties;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.service.defered.DeferredResultCompletion;
import com.kerryprops.kip.service.integral.service.defered.DeferredResultTimeOut;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketCallbackResource;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketRejectResource;
import com.kerryprops.kip.service.integral.webservice.response.OcrPhotoResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 06/27/2024 14:25
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class SmallTicketRecognitionServiceImpl implements SmallTicketRecognitionService {

    private final RedisService redisService;
    private final HiveVasService hiveVasService;
    private final SmallTicketProperties properties;
    private final TbBaseShopService tbBaseShopService;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbPhotoReviewService tbPhotoReviewService;
    private final TbSalesDetailService tbSalesDetailService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final DefaultRedisScript<Long> checkAndDelScript;
    private final SmallTicketProperties smallTicketProperties;
    private final TbOcrCallbackRecordService tbOcrCallbackRecordService;
    private final SmallTicketRecognitionClient smallTicketRecognitionClient;
    private final MemberSalePointsProcessService memberSalePointsProcessService;

    @Async
    @Override
    public void asyncUploadImgToHeHe(DeferredResult<OcrPhotoResponse> result, TbMemberAsset tbMemberAsset, MallItem mallItem, String imgUrl) {
        // 嘉里生成的唯一标识，传给合合，解决图片上传超时的问题，通过该唯一标识反查任务id
        String simpleQrCode = this.getQrCode();
        // 上传合合科技
        SmallTicketUploadDto uploadResult = smallTicketRecognitionClient.uploadFile(properties.getAppId(), properties.getSecret(), mallItem.getMallId(), properties.getEnv(), 9, simpleQrCode,
                this.getImageFromNetByUrl(imgUrl));
        // mock结果给到前端
        if (Boolean.TRUE.equals(smallTicketProperties.getMock())) {
            result.setResult(OcrPhotoResponse.builder().taskId(uploadResult.getTaskId()).code(0).ticketNo("xxxxx").amount("100").shopNo("xxxxx").shopName("测试").build());
        } else {
            if (Objects.nonNull(uploadResult)) {
                // 保存记录
                BonusSelfUploadDto uploadDto = BonusSelfUploadDto.builder().type("3").status(PhotoReviewStatusEnum.TYPE_0.getCode()).imageUrl(imgUrl).mallId(mallItem.getMallId())
                        .groupId(mallItem.getGroupId()).vipcode(tbMemberAsset.getVipcode()).mobile(tbMemberAsset.getMobile()).description("1").qrcode(simpleQrCode)
                        .orgGrade(tbMemberAsset.getGrade()).orgPoints(tbMemberAsset.getCurrentPoints()).build();
                // 判断调用合合是否成功
                if (Objects.nonNull(uploadResult.getCode()) && 0 == uploadResult.getCode() && StringUtils.isNotBlank(uploadResult.getTaskId())) {
                    log.info("smallTicketUpload TaskId: {}", uploadResult.getTaskId());
                    // 设置超时处理方法
                    result.onTimeout(new DeferredResultTimeOut(result, uploadResult.getTaskId(), redisService));
                    // 设置完成时处理方法
                    result.onCompletion(new DeferredResultCompletion(uploadResult.getTaskId()));
                    // 添加到缓存内
                    DeferredResultCacheUtils.putResultMap(uploadResult.getTaskId(), result);
                } else {
                    // KO设置为上传失败
                    uploadDto.setStatus(PhotoReviewStatusEnum.TYPE_3.getCode());
                    // 记录合合的报错信息
                    if (StringUtils.isNotBlank(uploadResult.getMsg())) {
                        uploadDto.setReason("拍照积分异常:" + uploadResult.getMsg());
                    }
                    // 上传拍照小票失败
                    result.setErrorResult(OcrPhotoResponse.builder().code(OcrErrorCodeMappingEnum.ERR_50.getOcrErrorCode()).msg(OcrErrorCodeMappingEnum.ERR_50.getErrorMsg()).build());
                }
                // 保存上传记录
                Long photoReviewId = tbPhotoReviewService.saveRecord(uploadDto);
                if (StringUtils.isNotBlank(uploadResult.getTaskId())) {
                    tbOcrCallbackRecordService.saveRecord(uploadResult.getTaskId(), photoReviewId);
                }
            } else {
                // 上传拍照小票失败
                result.setErrorResult(OcrPhotoResponse.builder().code(OcrErrorCodeMappingEnum.ERR_50.getOcrErrorCode()).msg(OcrErrorCodeMappingEnum.ERR_50.getErrorMsg()).build());
            }
        }
    }

    /**
     * 不能生成重复的8为单号
     * @return
     */
    private String getQrCode() {
        String simpleQrCode = OcrQrCodeUtils.simpleQrCode();
        while (tbPhotoReviewService.checkQrCodeExists(simpleQrCode)) {
            simpleQrCode = OcrQrCodeUtils.simpleQrCode();
        }
        return simpleQrCode;
    }

    @Override
    public void matchUploadImgToHeHe(TbMemberAsset tbMemberAsset, MallItem mallItem, String imgUrl) {
        // 嘉里生成的唯一标识，传给合合，解决图片上传超时的问题，通过该唯一标识反查任务id
        String simpleQrCode = this.getQrCode();
        // 上传合合科技
        SmallTicketUploadDto uploadResult = smallTicketRecognitionClient.uploadFile(properties.getAppId(), properties.getSecret(), mallItem.getMallId(), properties.getEnv(), 9, simpleQrCode,
                this.getImageFromNetByUrl(imgUrl));
        if (Objects.nonNull(uploadResult)) {
            // 保存记录
            BonusSelfUploadDto uploadDto = BonusSelfUploadDto.builder().type("3").status(PhotoReviewStatusEnum.TYPE_0.getCode()).imageUrl(imgUrl).mallId(mallItem.getMallId())
                    .groupId(mallItem.getGroupId()).vipcode(tbMemberAsset.getVipcode()).mobile(tbMemberAsset.getMobile()).description("1").qrcode(simpleQrCode)
                    .orgGrade(tbMemberAsset.getGrade()).orgPoints(tbMemberAsset.getCurrentPoints()).build();
            // 判断调用合合是否成功
            if (Objects.nonNull(uploadResult.getCode()) && 0 == uploadResult.getCode() && StringUtils.isNotBlank(uploadResult.getTaskId())) {
                log.info("smallTicketUpload TaskId: {}", uploadResult.getTaskId());
            } else {
                // KO设置为上传失败
                uploadDto.setStatus(PhotoReviewStatusEnum.TYPE_3.getCode());
                // 记录合合的报错信息
                if (StringUtils.isNotBlank(uploadResult.getMsg())) {
                    uploadDto.setReason("拍照积分异常:" + uploadResult.getMsg());
                }
            }
            // 保存上传记录
            Long photoReviewId = tbPhotoReviewService.saveRecord(uploadDto);
            if (StringUtils.isNotBlank(uploadResult.getTaskId())) {
                tbOcrCallbackRecordService.saveRecord(uploadResult.getTaskId(), photoReviewId);
            }
        } else {
            throw BizException.error(PointsEnum.IMAGE_UPLOAD_FAILED);
        }
    }

    @Override
    public SmallTicketCallbackResource getUploadResult(String taskId) {
        return smallTicketRecognitionClient.queryUploadResult(properties.getAppId(), properties.getSecret(), taskId, this.getVerifyToken(taskId));
    }

    @Override
    public SmallTicketCallbackResource getUploadResultByOuterId(String outerId) {
        return smallTicketRecognitionClient.queryUploadResultByOuterId(properties.getAppId(), properties.getSecret(), outerId, this.getVerifyToken(outerId));
    }

    @Override
    public void ocrRecognitionTimeoutAutomaticPoints(SmallTicketCallbackResource resource, TbPhotoReview photoReview) {
        // 更新拍照积分记录回传信息
        tbPhotoReviewService.updateById(photoReview);
        // 只有自动识别/人工审核通过的拍照记录才能自动积分，或OCR识别不成功的，则无需提交自动积分
        if (!PhotoReviewStatusEnum.autoPointsStatus(photoReview.getStatus()) || resource.fail()) {
            log.info("OCR超时回调自动积分返回识别失败: [{}]", resource.getTaskId());
            return;
        }
        // 拍照积分上传记录重复验证
        PhotoReviewCheckDto checkDto = PhotoReviewCheckDto.builder().groupId(photoReview.getGroupId()).mallId(photoReview.getMallId()).ticketNo(resource.getTicketNo()).shopNo(resource.getShop())
                .amount(Double.parseDouble(resource.getAmount())).tradingDate(resource.getTransTime()).idNotIn(Collections.singletonList(photoReview.getId())).status(PhotoReviewStatusEnum.TYPE_1.getCode()).build();
        TbPhotoReview repeatedReview = tbPhotoReviewService.checkPhotoReviewRecord(checkDto);
        // 出现重复，则更新小票被驳回的原因为重复上传
        if (Objects.nonNull(repeatedReview)) {
            photoReview.setReason("此小票重复上传，如有疑问，请前往客服台咨询。");
            // 小票重重
            photoReview.setDescription(OcrErrorCodeMappingEnum.ERR_10.getOcrErrorCode() + "");
            // 小票重复上传，更新为已驳回
            tbPhotoReviewService.updatePhotoViewStatus(photoReview, PhotoReviewStatusEnum.TYPE_2);
            return;
        }
        // 删除记录的错误次数redis key
        String memberOcrFailNumberKey = RedisCacheKeyUtils.getOcrFailNumberKey(photoReview.getGroupId(), photoReview.getVipcode());
        redisService.delKeys(Collections.singletonList(memberOcrFailNumberKey));
        // 该拍照积分是否已积分
        if (StringUtils.isNotBlank(photoReview.getIntegralAdjustId()) && Objects.nonNull(photoReview.getBonus())) {
            return;
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(photoReview.getVipcode(), photoReview.getGroupId());
        if (Objects.isNull(tbMemberAsset)) {
            return;
        }
        // 查询租户信息
        TbBaseShop baseShop = tbBaseShopService.getByContractNoAndMallId(photoReview.getShopNo(), photoReview.getMallId());
        if (Objects.isNull(baseShop)) {
            photoReview.setReason(OcrErrorCodeMappingEnum.ERR_70.getErrorMsg());
            // 对应的店铺信息不存在，更新为已驳回
            tbPhotoReviewService.updatePhotoViewStatus(photoReview, PhotoReviewStatusEnum.TYPE_2);
            return;
        }
        // 填充店铺信息
        this.fillShopInfo(photoReview, baseShop);
        // 添加数据重复验证
        String saleNoKey = String.format(RedisCacheKey.SALES_NO_KEY, resource.getTaskId());
        SalesAutoPointsDto pointsDto = null;
        Integer points = 0;
        if (Boolean.FALSE.equals(redisService.setSaleNoIfAbsent(saleNoKey, IdUtil.simpleUUID()))) {
            log.info("OCR同一时间内重复回传: [{}]", resource.getTaskId());
            return;
        }
        try {
            // 通用销售记录去重及计算积分逻辑
            pointsDto = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId(photoReview.getGroupId())
                    .mallId(photoReview.getMallId()).member(tbMemberAsset).amount(photoReview.getMoney() + "").vipcode(tbMemberAsset.getVipcode())
                    .remark(IntegralConstant.TAKE_PHOTO).remarkName("拍照积分").shopId(photoReview.getShopNo()).baseShop(baseShop).imageUrl(photoReview.getImageUrl())
                    .saleNo(photoReview.getSerialNum()).saleDate(DateUtil.formatDateTime(photoReview.getTradingDate())).createUser(photoReview.getCheckName()).saleType(SaleTypeEnum.TICKET.getValue()).refund(false).build();
            points = memberSalePointsProcessService.salePointsProcess(pointsDto);
            // 补充拍照积分内的信息
            tbPhotoReviewService.fillPhotoViewInfo(tbMemberAsset, photoReview, baseShop, points, pointsDto.getIntegralAdjustId());
        } catch (Exception e) {
            e.printStackTrace();
            log.info("OCR拍照积分自动积分报错: [{}]", e.getMessage());
            // 重复销售，记录异常原因
            if (Objects.nonNull(pointsDto) && Objects.nonNull(pointsDto.getMemberSale())) {
                // 已实时积分小票
                photoReview.setDescription(OcrErrorCodeMappingEnum.ERR_11.getOcrErrorCode() + "");
                tbPhotoReviewService.updatePhotoViewRejectReason(photoReview, pointsDto.getMemberSale(), baseShop);
            }
            // 进入积分异常拦截
            if (Objects.nonNull(pointsDto) ) {
                if (4 == pointsDto.getValidateStep()) {
                    // 更新拍照积分的状态为异常积分审核中
                    tbPhotoReviewService.updatePhotoViewStatus(photoReview, PhotoReviewStatusEnum.TYPE_4);
                } else {
                    // 该笔已进入异常积分审核，属于重复上传
                    tbPhotoReviewService.updatePhotoViewStatus(photoReview, PhotoReviewStatusEnum.TYPE_2);
                }
                // 输出错误日志
                log.info("会员OCR小票拍照记录进入积分异常拦截: {}-{}-{}-{}", pointsDto.getGroupId(), pointsDto.getVipcode(), pointsDto.getSaleNo(), pointsDto.getAmount());
            }
        } finally {
            redisService.delKeys(Collections.singletonList(saleNoKey));
        }
    }

    private static final String AUTO_COMMIT_POINTS = "ocr:auto:commit:timeout:%s";

    @Async
    @Override
    public void ocrCallbackHandler(SmallTicketCallbackResource resource, String msg) {
        TbPhotoReview photoReview = tbPhotoReviewService.findByOcrTaskId(resource.getTaskId());
        if (Objects.isNull(photoReview)) {
            log.info("OCR TaskId对应的拍照积分记录不存在: [{}]", resource.getTaskId());
            return;
        }
        // 合合未完成
        if (Objects.nonNull(resource.getCode()) && (101 == resource.getCode() || 404 == resource.getCode())) {
            return;
        }
        // 系统当前时间
        Date currentDate = new Date();
        // 填充拍照积分记录OCR回调信息
        this.fillPhotoInfo(photoReview, resource);
        // 记录错误次数redis key
        String memberOcrFailNumberKey = RedisCacheKeyUtils.getOcrFailNumberKey(photoReview.getGroupId(), photoReview.getVipcode());
        // 获取缓存的前端请求
        DeferredResult<OcrPhotoResponse> deferredResult = DeferredResultCacheUtils.getResultMap(resource.getTaskId());
        if (Objects.isNull(deferredResult)) {
            log.info("OCR回调服务器与请求发起服务器不在同一台服务器上: {}", resource.getTaskId());
            // 检查该OCR识别任务是否超时，如超时，且是识别通过的，则自动提交积分
            String ocrPhotoTimeoutKey = String.format(RedisCacheKey.OCR_PHOTO_IDENTITY_TIMEOUT, resource.getTaskId());
            Long returnVal = redisTemplate.execute(checkAndDelScript, Collections.singletonList(ocrPhotoTimeoutKey));
            if (Objects.nonNull(returnVal) && 1 == returnVal) {
                log.info("OCR回调超时，系统自动提交积分: [{}]", resource.getTaskId());
                // 超时回调，自动提交积分
                this.ocrRecognitionTimeoutAutomaticPoints(resource, photoReview);
                // 判断是否是重复拍照，记录redis值标记
                if (Objects.nonNull(resource.getRejectReason()) && OcrErrorCodeMappingEnum.isMissFieldsOrDuplicated(resource.getRejectReason().getId())) {
                    this.setUserRedisErrorCount(memberOcrFailNumberKey);
                }
            } else if (DateUtil.between(currentDate, photoReview.getUploadDate(), DateUnit.SECOND) > 13) {
                String autoCommitKey = String.format(AUTO_COMMIT_POINTS, resource.getTaskId());
                // 超时，自动提交积分
                if (redisService.setIfAbsentWithExpire(autoCommitKey, resource.getTaskId(), 10L, TimeUnit.SECONDS)) {
                    this.ocrRecognitionTimeoutAutomaticPoints(resource, photoReview);
                }
            }
            return;
        }
        log.info("进入通知前端处理逻辑流程: [{}]", resource.getTaskId());
        // 组装响应前端的数据
        OcrPhotoResponse response = OcrPhotoResponse.builder().code(0).taskId(resource.getTaskId()).msg("成功").build();
        // OCR识别成功处理逻辑
        if (resource.success()) {
            // 查询店铺信息
            TbBaseShop baseShop = tbBaseShopService.getByContractNoAndMallId(resource.getShop(), resource.getMall());
            if (Objects.isNull(baseShop)) {
                response.setCode(OcrErrorCodeMappingEnum.ERR_70.getOcrErrorCode());
                response.setMsg(OcrErrorCodeMappingEnum.ERR_70.getErrorMsg());
                // 更新拍照积分状态，标记为店铺信息不存在
                photoReview.setReason(String.format("店铺编号: [%s]在所属商场不存在", resource.getShop()));
                // 更新状态为已驳回-店铺信息不存在
                photoReview.setStatus(PhotoReviewStatusEnum.TYPE_2.getCode());
            } else {
                // 填充店铺信息
                this.fillShopInfo(photoReview, baseShop);
                // 查询商场信息
                Optional.ofNullable(hiveVasService.getLbsName(resource.getMall())).ifPresent(item -> response.setMallName(item.getLbs().getName()));
                // 店铺号
                response.setShopNo(baseShop.getContractNo());
                // 店铺别名
                response.setShopName(StringUtils.isNotBlank(baseShop.getShopAliasName()) ? baseShop.getShopAliasName() : baseShop.getRetailBrandName());
                // 交易时间
                response.setTransTime(resource.getTransTime());
                // 小票单号
                response.setTicketNo(resource.getTicketNo());
                // 交易金额
                response.setAmount(resource.getAmount());
                // 查询拍照积分记录
                TbPhotoReview repeatedRecord = null;
                // 检查OCR拍照记录重复逻辑
                if (StringUtils.isNotBlank(resource.getTransTime())) {
                    PhotoReviewCheckDto checkDto = PhotoReviewCheckDto.builder().groupId(photoReview.getGroupId()).mallId(photoReview.getMallId()).ticketNo(resource.getTicketNo()).shopNo(resource.getShop())
                            .amount(Double.parseDouble(resource.getAmount())).tradingDate(resource.getTransTime()).idNotIn(Collections.singletonList(photoReview.getId())).status(PhotoReviewStatusEnum.TYPE_1.getCode()).build();
                    repeatedRecord = tbPhotoReviewService.checkPhotoReviewRecord(checkDto);
                    // 出现拍照记录重复，直接驳回并记录驳回原因
                    if (Objects.nonNull(repeatedRecord)) {
                        response.setCode(OcrErrorCodeMappingEnum.ERR_10.getOcrErrorCode());
                        response.setMsg(OcrErrorCodeMappingEnum.ERR_10.getErrorMsg());
                        // 更新小票被驳回的原因为重复上传
                        photoReview.setReason(response.getMsg());
                        // 更新状态为已驳回-重复上传
                        photoReview.setStatus(PhotoReviewStatusEnum.TYPE_2.getCode());
                        // 小票重重
                        photoReview.setDescription(OcrErrorCodeMappingEnum.ERR_10.getOcrErrorCode() + "");
                    }
                }
                // 判断该小票对应的销售记录是否存在
                if (Objects.isNull(repeatedRecord)) {
                    TbSalesDetail salesDetail = tbSalesDetailService.checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto.builder().groupId(photoReview.getGroupId()).mallId(resource.getMall())
                            .shopId(resource.getShop()).useMoney(resource.getAmount()).saleNo(resource.getTicketNo()).saleDateBefore(resource.getTransTime()).saleDateAfter(resource.getTransTime()).build());
                    if (Objects.nonNull(salesDetail)) {
                        response.setCode(OcrErrorCodeMappingEnum.ERR_11.getOcrErrorCode());
                        response.setMsg(OcrErrorCodeMappingEnum.ERR_11.getErrorMsg());
                        String rejectReason = String.format("此销售与会员号: %s，所在店铺: %s-%s，销售单号: %s，销售金额: %s的销售记录发生重复积分",
                                salesDetail.getVipcode(), salesDetail.getShopNo(), salesDetail.getShopName(), salesDetail.getOrderNo(), salesDetail.getPayAmount());
                        // 更新状态为已驳回-销售记录重复积分
                        photoReview.setStatus(PhotoReviewStatusEnum.TYPE_2.getCode());
                        // 更新该笔OCR拍照积分未积分成功的原因
                        photoReview.setReason(rejectReason);
                        // 已实时积分小票
                        photoReview.setDescription(OcrErrorCodeMappingEnum.ERR_11.getOcrErrorCode() + "");
                    }
                }
            }
        } else {
            // 处理错误信息
            if (Objects.nonNull(resource.getRejectReason())) {
                OcrErrorCodeMappingEnum mappingEnum = OcrErrorCodeMappingEnum.getEnumByErrorCode(resource.getRejectReason().getId());
                response.setCode(mappingEnum.getOcrErrorCode());
                response.setMsg(mappingEnum.getErrorMsg());
                // 缺失五元素错误信息
                response.setMissingFields(this.getMissingFields(resource.getRejectReason(), mappingEnum));
            } else {
                log.info("OCR回传报错信息为空: [{}-{}]", resource.getTaskId(), msg);
            }
        }
        log.info("进入通知前端处理逻辑流程: [{}]", JsonUtils.objToString(response));
        deferredResult.setResult(response);
        // 更新拍照积分记录信息
        tbPhotoReviewService.updateById(photoReview);
        // 是五元素不全或重复积分的错误，则添加redis错误次数记录
        if (OcrErrorCodeMappingEnum.isMissFieldsOrDuplicated(response.getCode())) {
            this.setUserRedisErrorCount(memberOcrFailNumberKey);
        }
        // 识别成功后，删除之前缓存的错误次数
        if (0 == response.getCode()) {
            redisTemplate.delete(Collections.singleton(memberOcrFailNumberKey));
        }
    }

    /**
     * OCR拍照积分失败次数添加
     * @param memberOcrFailNumberKey
     */
    private void setUserRedisErrorCount(String memberOcrFailNumberKey) {
        // 判断错误次数key是否存在
        Boolean hasMemberOcrFailNumberKey = redisTemplate.hasKey(memberOcrFailNumberKey);
        Long step = redisTemplate.opsForValue().increment(memberOcrFailNumberKey, 1L);
        if (Objects.isNull(hasMemberOcrFailNumberKey) || Boolean.FALSE.equals(hasMemberOcrFailNumberKey)) {
            redisTemplate.expire(memberOcrFailNumberKey, 5L, TimeUnit.MINUTES);
        } else {
            // == 5次时，则设置key的过期时间为1小时
            if (Objects.nonNull(step) && (step == 5)) {
                redisTemplate.expire(memberOcrFailNumberKey, 1L, TimeUnit.HOURS);
            }
        }
    }

    /**
     * 补齐五要素缺失哪些信息
     * @param resource
     * @param mappingEnum
     * @return
     */
    private Map<String, String> getMissingFields(SmallTicketRejectResource resource, OcrErrorCodeMappingEnum mappingEnum) {
        if (OcrErrorCodeMappingEnum.ERR_12.equals(mappingEnum)) {
            Map<String, String> errorMap = new HashMap<>(8);
            List<String> missingFields = resource.getMissingFields();
            if (CollectionUtils.isNotEmpty(missingFields)) {
                for (String name: missingFields) {
                    if ("mall".equalsIgnoreCase(name)) {
                        errorMap.put("商场名称", "XXXXXXX商场");
                    } else if ("shop".equalsIgnoreCase(name)) {
                        errorMap.put("店铺名称", "XXXXXXX店铺");
                    } else if ("amount".equalsIgnoreCase(name)) {
                        errorMap.put("交易金额", "XXXXX.XX元");
                    } else if ("transTime".equalsIgnoreCase(name)) {
                        errorMap.put("交易时间", "XXXX-XX-XX XX:XX:XX");
                    } else if ("ticketNo".equalsIgnoreCase(name)) {
                        errorMap.put("交易单号", "XXXXXXXXXX");
                    }
                }
            }
            return errorMap;
        }
        return Collections.emptyMap();
    }

    /**
     * 完善拍照积分记录OCR回调信息
     * @param review
     * @param resource
     */
    @Override
    public void fillPhotoInfo(TbPhotoReview review, SmallTicketCallbackResource resource) {
        // OCR识别成功，则description记0，代表成功
        if (resource.success()) {
            review.setDescription("0");
            // 是待审核的状态，则更新为已审核(未积分)，其他状态则状态不做更新
            if (PhotoReviewStatusEnum.autoPointsStatus(review.getStatus())) {
                // 已经审核通过的，或已经自动积分的单子，状态不做变更
                if (StringUtils.isNotBlank(review.getIntegralAdjustId()) || PhotoReviewStatusEnum.TYPE_1.getCode().equals(review.getStatus())) {
                    log.info("该OCR任务已处理完成，无需更新状态: [{}-{}]", review.getId(), resource.getTaskId());
                } else {
                    review.setStatus(PhotoReviewStatusEnum.TYPE_6.getCode());
                }
            } else {
                log.info("该小票不是待审核状态，不做任何更新: {}-{}", resource.getTaskId(), review.getStatus());
            }
        } else {
            if (Objects.nonNull(resource.getRejectReason())) {
                // 记录合合错误码
                review.setDescription(resource.getRejectReason().getId()+"");
                // 根据合合错误码匹配KIP定义的错误信息枚举值
                OcrErrorCodeMappingEnum mappingEnum = OcrErrorCodeMappingEnum.getEnumByErrorCode(resource.getRejectReason().getId());
                // 非消费小票 （合合OCR是不收费的），状态记为已失败，其他的记为已驳回
                review.setStatus(Objects.equals(OcrErrorCodeMappingEnum.ERR_13, mappingEnum) ? PhotoReviewStatusEnum.TYPE_3.getCode() : PhotoReviewStatusEnum.TYPE_2.getCode());
                // 如果是审核不通过，记录异常原因
                if (Objects.equals(OcrErrorCodeMappingEnum.ERR_12, mappingEnum)) {
                    review.setReason(mappingEnum.getErrorMsg());
                } else {
                    review.setReason(Objects.equals(OcrErrorCodeMappingEnum.ERR_60, mappingEnum) ? mappingEnum.getErrorMsg() : "审核不通过，" + mappingEnum.getErrorMsg());
                }
            } else {
                review.setStatus(PhotoReviewStatusEnum.TYPE_3.getCode());
                // 合合未返回报错信息，系统默认填充
                review.setReason("审核不通过，如有疑问，请前往客服台咨询。");
            }
        }
        // OCR返回的商场id与拍照记录的所属商场id不一致，则更新
        if (StringUtils.isNotBlank(resource.getMall()) && !StringUtils.equals(resource.getMall(), review.getMallId())) {
            review.setMallId(resource.getMall());
        }
        review.setCheckTime(new Date());
        review.setShopNo(StringUtils.isNotBlank(resource.getShop()) ? resource.getShop() : "");
        review.setCheckName(Boolean.TRUE.equals(resource.getIsRobot()) ? "OCR-自动审核" : "OCR-人工审核");
        review.setUpdateDate(new Date());
        review.setUpdateUser("OCR回调");
        // 设置回调金额
        review.setMoney(StringUtils.isNotBlank(resource.getAmount()) ? Double.parseDouble(resource.getAmount()) : 0D);
        // 小票流水号
        review.setSerialNum(StringUtils.isNotBlank(resource.getTicketNo()) ? resource.getTicketNo() : "");
        // 填充销售时间
        if (StringUtils.isNotBlank(resource.getTransTime())) {
            review.setTradingDate(DateUtil.parseDateTime(resource.getTransTime()));
        }
    }

    /**
     * 填充拍照记录店铺信息
     * @param review
     * @param baseShop
     */
    @Override
    public void fillShopInfo(TbPhotoReview review, TbBaseShop baseShop) {
        review.setShopName(StringUtils.isNotBlank(baseShop.getShopAliasName()) ? baseShop.getShopAliasName() : baseShop.getRetailBrandName());
        String formatIds = Stream.of(baseShop.getFirstFormatCode(), baseShop.getSecondFormatCode(), baseShop.getThirdFormatCode()).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        review.setFormatId(formatIds);
        String formatNames = Stream.of(baseShop.getFirstFormatName(), baseShop.getSecondFormatName(), baseShop.getThirdFormatName()).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        review.setFormatName(formatNames);
    }

    /**
     * 生成verifyToken
     * @return
     */
    private String getVerifyToken(String taskId) {
        return DigestUtils.md5Hex(properties.getAppId() + "mall_receipt" + taskId);
    }

    /**
     * 根据地址获得数据的字节流
     * @param fileUrl 网络连接地址
     * @return
     */
    private byte[] getImageFromNetByUrl(String fileUrl) {
        String internalFileUrl = fileUrl.replace(smallTicketProperties.getOcrFileExternalDomain(), smallTicketProperties.getOcrFileInternalDomain());
        log.info("OCRInternalFileUrl: [{}]", internalFileUrl);
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            HttpURLConnection conn = (HttpURLConnection) (new URL(internalFileUrl)).openConnection();
            conn.setRequestMethod("GET");
            // 连接超时时间设置
            conn.setConnectTimeout(10 * 1000);
            // 读取超时时间设置
            conn.setReadTimeout(10 * 1000);
            // 转化为图片的二进制数据
            return readInputStream(conn);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("OCR上传文件读取失败: [{}-{}]", fileUrl, internalFileUrl);
        } finally {
            watch.stop();
            log.info("getImageFromNetByUrl: [{}-{}]", fileUrl, watch.getTotalTimeMillis());
        }
        return new byte[0];
    }

    /**
     * 从输入流中获取数据
     * @param conn 图片网络地址
     * @return
     * @throws IOException
     * @throws Exception
     */
    private byte[] readInputStream(HttpURLConnection conn) throws IOException {
        // 通过输入流获取图片数据
        try (InputStream inStream = conn.getInputStream();
             ByteArrayOutputStream outStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = inStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            return outStream.toByteArray();
        }
    }

}
