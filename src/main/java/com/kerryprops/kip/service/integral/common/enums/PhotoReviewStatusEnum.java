package com.kerryprops.kip.service.integral.common.enums;

import com.kerryprops.kip.service.integral.webservice.response.PhotoReviewsResponse;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - saas
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 06/30/2022 15:09
 **********************************************************************************************************************/

@Getter
public enum PhotoReviewStatusEnum {

    TYPE_0("1", "未审核"),
    TYPE_1("2", "已审核"),
    TYPE_2("3", "已驳回"),
    TYPE_3("4", "已失败"),
    TYPE_4("5", "异常积分审核中"),
    // 状态6标记为：已审核(未积分)
    TYPE_6("6", "已审核");

    private final String code;
    private final String name;

    PhotoReviewStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 待审核
     * @return
     */
    public static List<String> getPendingApprovalStatus() {
        return Stream.of(TYPE_0, TYPE_4).map(PhotoReviewStatusEnum::getCode).toList();
    }

    /**
     * 已审核
     * @return
     */
    public static List<String> getAuditedStatus() {
        return Stream.of(TYPE_1, TYPE_2, TYPE_3).map(PhotoReviewStatusEnum::getCode).toList();
    }

    public static String getName(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        PhotoReviewStatusEnum changeTypeEnum = null;
        for (PhotoReviewStatusEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                changeTypeEnum = StringUtils.equals("6", code) ? TYPE_0 : typeEnum;
                break;
            }
        }
        return Objects.nonNull(changeTypeEnum) ? changeTypeEnum.getName() : "";
    }

    /**
     * 能自动积分的状态
     * @param status
     * @return
     */
    public static boolean autoPointsStatus(String status) {
        return Stream.of(TYPE_0, TYPE_1, TYPE_6).anyMatch(item -> StringUtils.equals(item.getCode(), status));
    }

    /**
     * 已审核的状态
     * @param status
     * @return
     */
    public static boolean auditedStatus(String status) {
        return Stream.of(TYPE_1, TYPE_6).anyMatch(item -> StringUtils.equals(item.getCode(), status));
    }

    public static List<PhotoReviewsResponse> getPhotoReviewStatusEnumList() {
        List<PhotoReviewsResponse> list = new ArrayList<>();
        for (PhotoReviewStatusEnum typeEnum : values()) {
            if (typeEnum.getCode().equals("6")) {
                continue;
            }
            PhotoReviewsResponse resp = PhotoReviewsResponse
                    .builder()
                    .code(typeEnum.getCode())
                    .name(typeEnum.getName())
                    .build();
            list.add(resp);
        }
        return list;
    }


}
