package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.enums.RuleTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeRuleMapper;
import com.kerryprops.kip.service.integral.service.TbMemberGradeRuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeRuleServiceImpl
 * @Description TbMemberGradeRuleServiceImpl
 * @date 2022/9/27 14:52
 * @Version 1.0
 */
@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbMemberGradeRuleServiceImpl extends ServiceImpl<TbMemberGradeRuleMapper, TbMemberGradeRule> implements TbMemberGradeRuleService {

    private final TbMemberGradeRuleMapper tbMemberGradeRuleMapper;

    @Override
    public List<TbMemberGradeRule> queryGradeRulesByGroupIdAndGradeAndRuleTypes(String groupId, String grade, List<Integer> ruleTypes) {
        return tbMemberGradeRuleMapper.queryGradeRulesByGroupIdAndGradeAndRuleTypes(groupId, grade, ruleTypes);
    }

    @Override
    public TbMemberGradeRule queryUpgradeRule(String groupId, String grade) {
        if (ObjectUtils.anyNull(groupId, grade)) {
            return null;
        }
        QueryWrapper<TbMemberGradeRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(TbMemberGradeRule::getGroupId, groupId)
                .eq(TbMemberGradeRule::getGradeCode, grade)
                .eq(TbMemberGradeRule::getType, 1)
                .eq(TbMemberGradeRule::getRuleType, RuleTypeEnum.ACCUMULATIVE_CONSUME_UPGRADE.getValue())
                .orderByDesc(TbMemberGradeRule::getIsCount).last("limit 1");
        return tbMemberGradeRuleMapper.selectOne(queryWrapper);
    }

    @Override
    public List<TbMemberGradeRule> queryByGroupIdAndType(String groupId, Integer type) {
        return tbMemberGradeRuleMapper.findGradeRulesByGroupIdAndType(groupId, type);
    }

    @Override
    public List<TbMemberGradeRule> queryByGroupIdAndRuleType(String groupId, Integer ruleType) {
        return tbMemberGradeRuleMapper.findGradeRulesByGroupIdAndRuleType(groupId, ruleType);
    }

    @Override
    public List<TbMemberGradeRule> queryByGroupIdAndGradeAndType(String groupId, String grade, Integer type) {
        return tbMemberGradeRuleMapper.queryGradeRulesByGroupIdAndGradeAndType(groupId, grade, type);
    }

    @Override
    public boolean isCycleYear(String groupId, Integer type) {
        return tbMemberGradeRuleMapper.isCycleYear(groupId, type);
    }
}
