package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/01/2022 09:57
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员积分变更请求类")
public class MemberPointsChangeResource implements Serializable {

    @NotBlank(message = "所属集团不能为空")
    @Schema(description = "所属品牌", required = true)
    private String groupId;

    @NotBlank(message = "所属商场不能为空")
    @Schema(description = "所属商场", required = true)
    private String mallId;

    @NotBlank(message = "会员vipcode不能为空")
    @Schema(description = "会员vipcode", required = true)
    private String vipcode;

    @Schema(description = "销售类型(1.CRM销售,2.POS销售,3.微信小票,4.微信扫码,5.口碑,6.线上商城,7.微信商圈,8.支付宝商圈,9.H5小游戏)，没有的话默认传: 0", required = true)
    private String saleType;

    @NotBlank(message = "积分变更类型不能为空")
    @Schema(description = "对应字典值，即字典code", required = true)
    private String dictValue;

    @Schema(description = "对应字典名称，即字典名称", required = false)
    private String dictName;

    @NotNull(message = "变更积分数不能为空")
    @Schema(description = "变更积分数, 有正负之分，正值是加积分，负值是减积分", required = true)
    private Integer changePointsNum;

    @Schema(description = "销售单号，可以不传", required = false)
    private String saleNo;

    @Schema(description = "销售金额，可以不传", required = false)
    private BigDecimal saleAmount;

    @Schema(description = "积分调整类型，A->普通调整， S->销售调整，默认为销售调整", required = false)
    private String type = "A";

    @Schema(description = "关联业务的ID，如不传，会生成默认的UUID", required = false)
    private String associatedBusinessId;

    @Schema(description = "店铺编码，可以不传", required = false)
    private String shopNo;

    @Schema(description = "创建者", required = false)
    private String creator;

    @Schema(description = "备注", required = false)
    private String content;

    @Schema(description = "积分变更记录id", required = false)
    private String adjustPointsId;

    @Schema(description = "是否检查会员状态，0或null检查， 1不检查", hidden = true)
    private Integer checkMemberStatus;

}
