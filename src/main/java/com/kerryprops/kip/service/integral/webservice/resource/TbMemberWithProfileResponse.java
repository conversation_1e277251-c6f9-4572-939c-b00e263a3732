package com.kerryprops.kip.service.integral.webservice.resource;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/01/2022 10:45
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "CRM会员信息+Profile信息")
public class TbMemberWithProfileResponse implements Serializable {

    @Schema( description = "会员ID")
    private String id;

    @Schema( description = "集团ID")
    private String groupId;

    @Schema( description = "商场Id")
    private String mallId;

    @Schema( description = "开卡商场名称")
    private String mallName;

    @Schema( description = "授权商场")
    private String authorizedMallId;

    @Schema( description = "授权商场")
    private String authorizedMallName;

    @Schema( description = "授权时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date authorizedDate;

    @Schema( description = "手机区号")
    private String areaCode;

    @Schema( description = "手机区号名称")
    private String areaCodeName;

    @Schema( description = "手机号码")
    private String mobile;

    @Schema( description = "会员编号")
    private String vipcode;

    @Schema( description = "当前积分")
    private Integer currentIntegral;

    @Schema( description = "当前会员卡等[编码]")
    private String grade;

    @Schema( description = "会员状态（0:冻结；1:非冻结)")
    private String status;

    @Schema( description = "会员邮箱")
    private String email;

    @Schema( description = "备注信息")
    private String remark;

    @Schema( description = "微信激活商场")
    private String wxopenmarket;

    @Schema( description = "兴趣标签（维度维护）多选")
    private String hobys;

    @Schema( description = "加入日期")
    private Date joinTime;

    @Schema( description = "是否完善信息（0:未完善；1:完善)")
    private String isCompleted;

    @Schema( description = "kip对应的user_id")
    private String kipUserId;

    @Schema( description = "是否黑名单(0:是 1:不是),供活动使用")
    private Integer whetherBlacklist;

    @Schema( description = "注册来源")
    private String registerSource;

    @Schema( description = "注册来源-附注信息(例如：活动，电子券)")
    private String registerSourceLabel;

    @Schema( description = "来源名称")
    private String registerSourceRemark;

    @Schema( description = "昵称")
    private String nickName;

    @Schema( description = "真实姓名")
    private String realName;

    @Schema( description = "头像")
    private String avatar;

    @Schema( description = "性别，0:未知, 1:男, 2:女")
    private Integer gender;

    @Schema( description = "生日")
    private Date birthDate;

    @Schema( description = "证件类型（1：身份证、2：驾驶证、3：护照、4：其他证件）")
    private Integer certificateType;

    @Schema( description = "证件号码")
    private String certificateNum;

    @Schema( description = "婚姻状态：0：未知，1：已婚，2：未婚")
    private Integer maritalStatus;

    @Schema( description = "是否有宝宝：0：未知，1：有，2：无")
    private Integer babyStatus;

    @Schema( description = "省地址")
    private String provinceAddress;

    @Schema( description = "市地址")
    private String cityAddress;

    @Schema( description = "区地址")
    private String districtAddress;

    @Schema( description = "详细地址")
    private String address;

    @Schema( description = "公司名称")
    private String companyName;

    @Schema( description = "职位")
    private String job;

    @Schema( description = "民族")
    private String nation;

    @Schema( description = "家庭电话")
    private String homePhone;

    @Schema( description = "学历")
    private Integer education;

    @Schema( description = "职业")
    private String profession;

    @Schema( description = "积分抵扣金额数")
    private String amountOfPoints;

}
