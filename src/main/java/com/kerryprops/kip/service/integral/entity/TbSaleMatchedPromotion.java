package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/17/2023 16:43
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_sale_matched_promotion")
public class TbSaleMatchedPromotion implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("sell_no")
    private String sellNo;

    @TableField("integral_adjust_id")
    private String integralAdjustId;

    @TableField("promotion_id")
    private String promotionId;

    @TableField("promotion_name")
    private String promotionName;

    @TableField("create_date")
    private Date createDate;

    @TableField("update_date")
    private Date updateDate;


}
