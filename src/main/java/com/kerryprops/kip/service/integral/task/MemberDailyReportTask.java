//package com.kerryprops.kip.service.integral.task;
//
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.util.NumberUtil;
//import com.kerryprops.kip.service.integral.config.MallConfig;
//import com.kerryprops.kip.service.integral.entity.TbMallDailySummary;
//import com.kerryprops.kip.service.integral.mapper.TbMemberIntegraladjustMapper;
//import com.kerryprops.kip.service.integral.mapper.TbMemberMapper;
//import com.kerryprops.kip.service.integral.mapper.TbMemberSaleMapper;
//import com.kerryprops.kip.service.integral.model.dto.DailyMemberStatisticsInfo;
//import com.kerryprops.kip.service.integral.model.dto.MallItem;
//import com.kerryprops.kip.service.integral.service.MemberService;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 12/13/2022 09:35
// **********************************************************************************************************************/
//
//@Slf4j
//@Component
//@AllArgsConstructor
//@ConditionalOnProperty(value = "distributed.jobs.enabled", havingValue = "true")
//public class MemberDailyReportTask {
//
//    private static final String MINI_PROGRAM = "miniProgram";
//    private final MemberService memberService;
//    private final TbMemberIntegraladjustMapper tbMemberIntegraladjustMapper;
//    private final TbMemberSaleMapper tbMemberSaleMapper;
//    private final MallConfig mallConfig;
//
//    /**
//     * 统计前30天的数据
//     */
//    private static final int BEFORE_DAYS = 30;
//
//    /**
//     * 每日晚上11:40
//     */
////    @Scheduled(cron = "0 40 23 * * ?")
//    @SchedulerLock(name = "memberDailyReportTask", lockAtMostFor = "PT15M")
//    public void dailyReportTask() {
//        log.info("----------- MemberDailyReportTask Start--------------");
//        Date currentDate = DateUtil.date();
//        for (int i = 0; i <= BEFORE_DAYS; i++) {
//            Date tmpDate = DateUtil.offsetDay(currentDate, -i);
//            String startTime = DateUtil.formatDateTime(DateUtil.beginOfDay(tmpDate));
//            String endTime = DateUtil.formatDateTime(DateUtil.endOfDay(tmpDate));
//            for (MallItem mallItem: mallConfig.getList()) {
//                log.info("每日会员分析数据汇总: mallId: {}, 时间段: {} - {}", mallItem.getMallId(), startTime, endTime);
//                // 小程序当天注册会员数
//                int miniMemberDaisyCount = memberService.getMemberCountBetweenDate(mallItem.getGroupId(), mallItem.getMallId(), startTime, endTime, MINI_PROGRAM);
//                // 当天注册会员数
//                int memberDaisyCount = memberService.getMemberCountBetweenDate(mallItem.getGroupId(), mallItem.getMallId(), startTime, endTime, null);
//                // 当日会员新增，消耗积分及人数
//                List<DailyMemberStatisticsInfo> infoList = tbMemberIntegraladjustMapper.getDailyIntegralInfo(mallItem.getGroupId(), mallItem.getMallId(), startTime, endTime);
//                // 当日销售数据
//                DailyMemberStatisticsInfo saleInfo = tbMemberSaleMapper.getDailyMemberStatisticsInfo(mallItem.getGroupId(), mallItem.getMallId(), startTime, endTime);
//                // 每日统计数据
//                TbMallDailySummary dailySummary = TbMallDailySummary.builder().mallId(mallItem.getMallId()).groupId(mallItem.getGroupId()).miniMemberCount(miniMemberDaisyCount)
//                        .memberCount(memberDaisyCount).saleAmount(NumberUtil.toBigDecimal(saleInfo.getIntegralNum()))
//                        .eachSaleAmount(NumberUtil.toBigDecimal(saleInfo.getEachMoney())).saleCount(saleInfo.getType()).build();
//                if (Objects.nonNull(saleInfo) && saleInfo.getMemNum() > 0) {
//                    dailySummary.setEachMemberAmount(NumberUtil.div(saleInfo.getIntegralNum()+"", saleInfo.getMemNum()+"", 2, RoundingMode.HALF_UP));
//                } else {
//                    dailySummary.setEachMemberAmount(BigDecimal.ZERO);
//                }
//                if (CollectionUtils.isNotEmpty(infoList)) {
//                    for (DailyMemberStatisticsInfo info: infoList) {
//                        if (0 == info.getType()) {
//                            dailySummary.setAddIntegral(info.getIntegralNum());
//                            dailySummary.setAddIntegralMemberCount(info.getMemNum());
//                        } else if (1 == info.getType()) {
//                            dailySummary.setSubIntegral(info.getIntegralNum());
//                            dailySummary.setSubIntegralMemberCount(info.getMemNum());
//                        } else {
//                            dailySummary.setSaleIntegral(info.getIntegralNum());
//                            dailySummary.setSaleIntegralMemberCount(info.getMemNum());
//                        }
//                    }
//                }
//                log.info("each mall: {}, info: {}", mallItem.getMallId(), dailySummary.toString());
//            }
//        }
//        log.info("----------- MemberDailyReportTask End--------------");
//    }
//
//}
