package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member_integraladjust
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_integraladjust")
public class TbMemberIntegraladjust implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 集团ID
     */
    @TableField(value = "group_id")
    private String groupId;

    /**
     * 调整类型 A->普通调整， S->销售调整
     */
    private String type;

    /**
     * 商场编号
     */
    @TableField(value = "mallid")
    private String mallId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 会员编号
     */
    private String vipcode;

    /**
     * 提出当前选中会员积分
     */
    @TableField(value = "currentIntegral")
    private Double currentIntegral;

    /**
     * 调整积分数量
     */
    private Double number;

    /**
     * 剩余可用积分数
     */
    @TableField(value = "left_points")
    private Integer leftPoints;

    /**
     * 红星积分调整数量
     */
    @TableField(value = "hxintegral")
    private Double hxIntegral;

    /**
     * 积分调整原因
     */
    private String remark;

    /**
     * 冗余调整原因
     */
    @TableField(value = "remarkName")
    private String remarkName;

    /**
     * 没有值为积分不过期(普通调整)
     */
    @TableField(value = "integralDead")
    private String integralDead;

    /**
     * 销售单号(销售调整)
     */
    @TableField(value = "sellNo")
    private String sellNo;

    /**
     * 收银机号(销售调整)
     */
    @TableField(value = "cashNo")
    private String cashNo;

    /**
     * 商场编号
     */
    @TableField(value = "shopId")
    private String shopId;

    /**
     * 销售日期(销售调整)
     */
    @TableField(value = "sellDate")
    private Date sellDate;

    /**
     * 额外积分
     */
    @TableField(value = "extraIntegral")
    private String extraIntegral;

    /**
     * 积分规则
     */
    @TableField(value = "integralRule")
    private String integralRule;

    /**
     * 优惠金额
     */
    private Double discounts;

    /**
     * 销售金额对应的积分
     */
    @TableField(value = "numberToIntegra")
    private Double numberToIntegral;

    /**
     * 备注
     */
    private String content;

    /**
     * 流水号
     */
    @TableField(value = "expdate")
    private String expdate;

    /**
     * 扩展字段1
     */
    private String extend1;

    /**
     * 扩展字段2
     */
    private String extend2;

    /**
     * 扩展字段3
     */
    private String extend3;

    /**
     * 创建时间
     */
    @TableField(value = "create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField(value = "update_date")
    private Date updateDate;

    /**
     * 创建人
     */
    @TableField(value = "createUser")
    private String createUser;

    /**
     * 同步积分流水处理状态
0--未处理；
1--以处理；
     */
    @TableField(value = "centerdisposestatus")
    private Integer centerdisposestatus;

    /**
     * 更新人
     */
    @TableField(value = "updateUser")
    private String updateUser;

    /**
     * 上传积分图片
     */
    @TableField(value = "imageUrl")
    private String imageUrl;

    /**
     * 调整渠道（0pc端积分 1服务台积分默认0）
     */
    @TableField(value = "channerl_source")
    private String channerlSource;

    @TableField(value = "xf_amount")
    private Double xfAmount;

    /**
     * 变更前积分
     */
    @TableField(value = "beforeIntegral")
    private Integer beforeIntegral;

    /**
     * 变更前等级
     */
    @TableField(value = "beforeGrade")
    private String beforeGrade;

    @TableField(value = "operationsId")
    private String operationId;

    /**
     * 调整记录月份
     */
    private transient String moon;

}