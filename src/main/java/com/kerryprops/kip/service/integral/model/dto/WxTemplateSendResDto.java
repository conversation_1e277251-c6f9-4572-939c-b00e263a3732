package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/07/2023 11:23
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxTemplateSendResDto implements Serializable {

    private String appId;

    private String sendTime;

    private String status;

    private String title;

    private Integer totalSend;

    /**
     * 错误处理
     */
    private String errorCode;
    private String errorText;
    private String fieldName;
    private String detailInfo;
    private String correlationId;

}
