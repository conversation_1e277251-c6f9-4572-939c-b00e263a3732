package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * DESC - 微服务拆分后的销售明细表
 * Author - zhangxiliang
 * Created Date - 03/12/2024 11:14
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_sales_detail")
public class TbSalesDetail implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * crm迁移数据时的主键id
     */
    @TableField("crm_id")
    private String crmId;

    /**
     * 集团ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场编号
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 会员编号
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * profile用户id
     */
    @TableField("kip_user_id")
    private String kipUserId;

    /**
     * 对应积分变更记录id
     */
    @TableField("points_id")
    private String pointsId;

    /**
     * 变更积分数
     */
    @TableField("points_num")
    private Integer pointsNum;

    /**
     * 父订单号
     */
    @TableField("parent_order_no")
    private String parentOrderNo;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 订单总金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 积分抵现金额
     */
    @TableField("points_offset_amount")
    private BigDecimal pointsOffsetAmount;

    /**
     * 实付金额
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 销售日期
     */
    @TableField("sale_date")
    private Date saleDate;

    /**
     * 定铺号
     */
    @TableField("shop_no")
    private String shopNo;

    /**
     * 定铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 退货状态(0->未退货, 1->退货, 2->部分退款)
     */
    @TableField("status")
    private Integer status;

    /**
     * 销售类型1.CRM销售,2.POS销售,3.微信照片,4.微信扫码,5.口碑销售
     */
    @TableField("sale_type")
    private String saleType;

    /**
     * 有单退货扣积分 积分记录id
     */
    @TableField("refund_points_id")
    private String refundPointsId;

    /**
     * 退货金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退货积分数
     */
    @TableField("refund_points_num")
    private Integer refundPointsNum;

    /**
     * 扩展字段1, 是否办公楼会员
     */
    @TableField("extend1")
    private String extend1;

    /**
     * 扩展字段2， 是否公寓会员
     */
    @TableField("extend2")
    private String extend2;

    /**
     * 扩展字段3， 是否小区会员
     */
    @TableField("extend3")
    private String extend3;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 销售小票地址
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 变更前积分数
     */
    @TableField("org_points")
    private Integer orgPoints;

    /**
     * 变更前会员等级
     */
    @TableField("org_grade")
    private String orgGrade;

    /**
     * 创建时间
     */
    @TableField("create_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 修改人
     */
    @TableField("update_user")
    private String updateUser;

    public void setDefault() {
        if (StringUtils.isBlank(this.refundPointsId)) {
            this.refundPointsId = "";
        }
        if (StringUtils.isBlank(this.parentOrderNo)) {
            this.parentOrderNo = "";
        }
        if (StringUtils.isBlank(this.orderNo)) {
            this.orderNo = "";
        }
        if (Objects.isNull(this.refundAmount)) {
            this.refundAmount = BigDecimal.ZERO;
        }
        if (Objects.isNull(this.refundPointsNum)) {
            this.refundPointsNum = 0;
        }
        // 默认未退款
        if (Objects.isNull(this.status)) {
            this.status = 0;
        }
        if (StringUtils.isBlank(this.shopName)) {
            this.shopName = "";
        }
        if (Objects.isNull(this.saleDate)) {
            this.saleDate = new Date();
        }
        if (Objects.isNull(this.discountAmount)) {
            this.discountAmount = BigDecimal.ZERO;
        }
        if (Objects.isNull(this.pointsOffsetAmount)) {
            this.pointsOffsetAmount = BigDecimal.ZERO;
        }
        if (StringUtils.isBlank(this.extend1)) {
            this.extend1 = "0";
        }
        if (StringUtils.isBlank(this.extend2)) {
            this.extend2 = "0";
        }
        if (StringUtils.isBlank(this.extend3)) {
            this.extend3 = "0";
        }
        if (StringUtils.isBlank(this.imageUrl)) {
            this.imageUrl = "";
        }
        if (StringUtils.isBlank(this.remark)) {
            this.remark = "";
        }
    }

}
