package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.PointsRedemptionEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.mapper.TbIntegralShopRateMapper;
import com.kerryprops.kip.service.integral.model.dto.PointsRedemptionDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.TbCashOutConfigResource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 14:03
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbSetshoprateServiceImpl implements TbSetshoprateService {

    private final TbIntegralShopRateMapper tbIntegralShopRateMapper;
    private final TbIntegralCategoryRateService tbIntegralCategoryRateService;
    private final TbMemberGradeService tbMemberGradeService;
    private final TbBaseShopService tbBaseShopService;
    private final TbOnlineShopConfigService tbOnlineShopConfigService;
    private final TbCashOutConfigService tbCashOutConfigService;
    private final MallConfig mallConfig;

    @Override
    public Integer salesToIntegral(SalesAutoPointsDto points) {
        String amount = NumberUtil.sub(NumberUtil.toBigDecimal(points.getAmount()), NumberUtil.toBigDecimal(points.getRefundAmount())).toString();
        // 消费金额<=0判断
        if (NumberUtil.toBigDecimal(amount).compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }
        // 线上商场
        if (PointsRedemptionEnum.REWARDS_MALL.getCode().equals(points.getRemark())) {
            // 线上积分商城积分兑换比例
            TbOnlineShopConfig shopConfig = tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(points.getMallId(), points.getMember().getGrade(), points.getRemark());
            if (Objects.isNull(shopConfig)) {
                points.setSalesRemark(points.getRemarkName() + ", 未找到线上商城积分比例得:");
                return 0;
            } else {
                points.setSalesRemark(points.getRemarkName() + ", 满足线上商城积分比例得:");
                return this.getPoints(amount, shopConfig.getMoney(), shopConfig.getPointNum(), points.getGroupId());
            }
        } else if (PointsRedemptionEnum.POS_CASH_OUT.getCode().equals(points.getRemark())) {
            // 积分抵现
            TbOnlineShopConfig shopConfig = tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(points.getMallId(), points.getMember().getGrade(), points.getRemark());
            if (Objects.isNull(shopConfig)) {
                points.setSalesRemark(points.getRemarkName() + ", 未找到积分抵现比例得:");
                return 0;
            } else {
                points.setSalesRemark(points.getRemarkName() + ", 满足积分抵现比例得:");
                return this.getDeductPoints(amount, shopConfig.getMoney(), shopConfig.getPointNum());
            }
        } else {
            // 会员对应的等级信息
            TbMemberGrade grade = tbMemberGradeService.queryByGroupIdAndGrade(points.getGroupId(), points.getMember().getGrade());
            if (Objects.isNull(grade)) {
                points.setSalesRemark(points.getRemarkName() + ", 未找到等级信息得:");
                return 0;
            }
            TbIntegralShopRate shopRate = tbIntegralShopRateMapper.getShopRateDto(TbIntegralShopRate.builder().groupId(points.getGroupId()).gradeId(grade.getId()).shopId(points.getShopId()).build());
            if (Objects.nonNull(shopRate)) {
                points.setSalesRemark(points.getRemarkName() + ", 满足店铺积分比例得:");
                return this.getPoints(amount, shopRate.getMoney(), shopRate.getPointNum(), points.getGroupId());
            } else {
                TbBaseShop baseShop = points.getBaseShop();
                // 查询店铺信息
                if (Objects.isNull(baseShop)) {
                    baseShop = tbBaseShopService.getByContractNoAndMallId(points.getShopId(), points.getMallId());
                    Optional.ofNullable(baseShop).ifPresent(points::setBaseShop);
                }
                if (Objects.isNull(baseShop)) {
                    points.setSalesRemark(points.getRemarkName() + ", 未找到店铺信息得:");
                    return 0;
                }
                List<String> categoryIds = ListUtil.of(baseShop.getThirdFormatCode(), baseShop.getSecondFormatCode(), baseShop.getFirstFormatCode()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(categoryIds)) {
                    points.setSalesRemark(points.getRemarkName() + ", 未找到业态积分比例得:");
                    return 0;
                }
                // 查询该店铺所属三级业态下，等级最低匹配到的业态积分比例设置规则
                TbIntegralCategoryRate categoryRate = this.getTbSetCategory(points.getGroupId(), points.getMallId(),categoryIds, grade.getId(), baseShop);
                if (Objects.isNull(categoryRate)) {
                    points.setSalesRemark(points.getRemarkName() + ", 未找到业态积分比例得:");
                    return 0;
                }
                points.setSalesRemark(points.getRemarkName() + ", 满足" + categoryRate.getFormatIndex() + "级业态积分比例得:");
                return this.getPoints(amount, categoryRate.getMoney(), categoryRate.getPointNum(), points.getGroupId());
            }
        }
    }

    /**
     * 循环查找
     * @param mallId mallId
     * @param groupId groupId
     * @param categoryIds categoryIds
     * @param gradeId gradeId
     * @return TbIntegralCategoryRate TbIntegralCategoryRate
     */
    private TbIntegralCategoryRate getTbSetCategory(String groupId, String mallId, List<String> categoryIds, String gradeId, TbBaseShop baseShop) {
        TbIntegralCategoryRate queryDto = TbIntegralCategoryRate.builder().gradeId(gradeId).groupId(groupId).categoryList(categoryIds).build();
        // 如果是KO的话，需要把mallId传进去
        if (mallConfig.isKO(groupId)) {
            queryDto.setMallId(mallId);
        }
        List<TbIntegralCategoryRate> rateList = tbIntegralCategoryRateService.getCategoryRateList(queryDto);
        TbIntegralCategoryRate categoryRate = null;
        if (CollectionUtils.isNotEmpty(rateList)) {
            int index = 0;
            while (index < rateList.size()) {
                String code = categoryIds.get(index);
                categoryRate = rateList.stream().filter(it -> Objects.equals(it.getCategoryId(), code)).findFirst().orElse(null);
                if (Objects.nonNull(categoryRate)) {
                    break;
                }
                index++;
            }
            if (Objects.nonNull(categoryRate)) {
                if (StringUtils.equals(categoryRate.getCategoryId(), baseShop.getFirstFormatCode())) {
                    categoryRate.setFormatIndex(1);
                } else if (StringUtils.equals(categoryRate.getCategoryId(), baseShop.getSecondFormatCode())){
                    categoryRate.setFormatIndex(2);
                } else {
                    categoryRate.setFormatIndex(3);
                }
            }
        }
        return categoryRate;
    }

    /**
     * 根据销售金额及积分兑换计算出对应的积分值
     * @param saleAmount saleAmount
     * @param rateMoney rateMoney
     * @param ratePoints ratePoints
     * @return int int
     */
    private int getPoints(String saleAmount, BigDecimal rateMoney, BigDecimal ratePoints, String groupId) {
        // 防止配置兑换金额配置为0出现除0错误，增加判断
        if (rateMoney.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        BigDecimal saleMoney = NumberUtil.toBigDecimal(saleAmount);
        BigDecimal multiply = saleMoney.divide(rateMoney, 2, RoundingMode.HALF_UP).multiply(ratePoints);
        if (Objects.nonNull(groupId) && mallConfig.isKO(groupId) || mallConfig.isTestGroup(groupId)) {
            return multiply.setScale(0, RoundingMode.UP).intValue();
        }
        return multiply.intValue();
    }

    /**
     * 根据金额扣除会员积分，需要多扣
     * @param saleAmount saleAmount
     * @param rateMoney rateMoney
     * @param ratePoints ratePoints
     * @return int int
     */
    private int getDeductPoints(String saleAmount, BigDecimal rateMoney, BigDecimal ratePoints) {
        // 防止配置兑换金额配置为0出现除0错误，增加判断
        if (rateMoney.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        BigDecimal saleMoney = NumberUtil.toBigDecimal(saleAmount);
        BigDecimal multiply = saleMoney.divide(rateMoney, 2, RoundingMode.UP).multiply(ratePoints);
        return multiply.setScale(0, RoundingMode.UP).intValue();
    }

    @Override
    public PointsRedemptionDto getMaxAmountOfStoreCashOut(SalesAutoPointsDto points) {
        TbCashOutConfig config = tbCashOutConfigService.getConfig(TbCashOutConfigResource.builder().groupId(points.getGroupId()).mallId(points.getMallId())
                        .businessType(points.getRemark()).startTime(DateUtil.now()).build());
        if (Objects.isNull(config)) {
            return PointsRedemptionDto.builder().amount(0).build();
        }
        // 配置的店铺为空或者店铺不包含当前店铺
        if (StringUtils.isBlank(config.getShopNo()) || !CharSequenceUtil.split(config.getShopNo(), CommonSeparators.COMMA_SEPARATOR).contains(points.getShopId())) {
            return PointsRedemptionDto.builder().amount(0).build();
        }
        // 根据groupId，店铺id和会员级别在POS积分抵现表查询信息
        TbOnlineShopConfig shopConfig = tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(points.getMallId(), points.getMember().getGrade(), points.getRemark());
        if (Objects.isNull(shopConfig)) {
            return PointsRedemptionDto.builder().amount(0).build();
        }
        int amount = this.getAmount(points.getMember().getCurrentPoints(), shopConfig.getMoney(), shopConfig.getPointNum());
        // 判断积分抵现是否设置抵现金额限制，如有限制，取积分抵现金额与传入金额的最小值，无限制则取积分抵现金额
        return PointsRedemptionDto.builder()
                .amount((config.getIsLimit() == 1) ? Math.min(config.getMoney().intValue(), amount) : amount)
                .money(shopConfig.getMoney()).pointNum(shopConfig.getPointNum()).build();
    }

    @Override
    public PointsRedemptionDto getMaxAmountOfShopCashOut(SalesAutoPointsDto points, TbCashOutConfig config) {
        // 根据groupId，店铺id和会员级别在POS积分抵现表查询信息
        TbOnlineShopConfig shopConfig = tbOnlineShopConfigService.findByMallIdAndGradeAndBusinessType(points.getMallId(), points.getMember().getGrade(), points.getRemark());
        if (Objects.isNull(shopConfig)) {
            return PointsRedemptionDto.builder().amount(0).build();
        }
        int amount = this.getAmount(points.getMember().getCurrentPoints(), shopConfig.getMoney(), shopConfig.getPointNum());
        // 判断积分抵现是否设置抵现金额限制，如有限制，取积分抵现金额与传入金额的最小值，无限制则取积分抵现金额
        return PointsRedemptionDto.builder()
                .amount((config.getIsLimit() == 1) ? Math.min(config.getMoney().intValue(), amount) : amount)
                .money(shopConfig.getMoney()).pointNum(shopConfig.getPointNum()).build();
    }

    @Override
    public int getAmount(Integer currentIntegral, BigDecimal money, BigDecimal points) {
        if (points.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        BigDecimal multiply = BigDecimal.valueOf(currentIntegral).divide(points, 0, RoundingMode.FLOOR).multiply(money);
        return multiply.intValue();
    }

    @Override
    public int getPoints(Integer amount, BigDecimal money, BigDecimal points) {
        if (points.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        BigDecimal multiply = BigDecimal.valueOf(amount).divide(money, 0, RoundingMode.UP).multiply(points);
        return multiply.intValue();
    }

    @Override
    public int getBigdecimalPoints(BigDecimal amount, BigDecimal money, BigDecimal points) {
        if (Objects.isNull(money) || Objects.isNull(points) || points.compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }
        BigDecimal multiply = amount.divide(money, 2, RoundingMode.UP).multiply(points);
        return multiply.setScale(0, RoundingMode.UP).intValue();
    }
}
