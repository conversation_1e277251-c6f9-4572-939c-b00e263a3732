package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.enums.PointsRedemptionEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbCashOutConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.model.dto.PointsRedemptionDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.*;
import com.kerryprops.kip.service.integral.webservice.response.PointsCustomAmountResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/21/2024 09:10
 **********************************************************************************************************************/

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/kerry/pay")
@Tag(name = "优惠买单-积分抵现API")
public class KerryPayController extends BaseMemberStatus {

    private final TbMemberPointsChangeService tbMemberPointsChangeService;
    private final TbCashOutConfigService tbCashOutConfigService;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbPointsDetailService tbPointsDetailService;
    private final TbSetshoprateService tbSetshoprateService;
    private final TbBaseShopService tbBaseShopService;
    private final RedisService redisService;

    @PostMapping("/available_amount")
    @Operation(summary="优惠买单积分抵现可用金额", method = "POST")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    })
    public PointsRedemptionTotalResponse frontEndRedemptionAmount(@CurrentUser LoginUser loginUser,
                                                                  @RequestBody @Valid PointsRedemptionResource resource,
                                                                  @RequestHeader(value = "brandId") String brandId,
                                                                  @RequestHeader(value = "lbsId") String lbsId) {
        PointsRedemptionTotalResponse response = PointsRedemptionTotalResponse.builder().maxPoints(0).maxAmount(BigDecimal.ZERO).hasValidActivity(Boolean.FALSE).build();
        // 判断店铺号是否正常
        TbBaseShop tbBaseShop = tbBaseShopService.getByTenantId(resource.getTenantId(), lbsId);
        if (Objects.isNull(tbBaseShop)) {
            throw PointBusinessException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        // 查询是否存在有效的积分抵现活动
        TbCashOutConfig config = tbCashOutConfigService.getConfig(TbCashOutConfigResource.builder().groupId(brandId).mallId(lbsId)
                .businessType(PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode()).startTime(DateUtil.now()).build());
        // 判断是否存在有效的积分抵现活动
        response.setHasValidActivity(Objects.nonNull(config) && StringUtils.isNotBlank(config.getShopNo()) && CharSequenceUtil.split(config.getShopNo(), CommonSeparators.COMMA_SEPARATOR).contains(tbBaseShop.getContractNo()));
        if (resource.getAmount().compareTo(BigDecimal.ZERO) <= 0 || Boolean.FALSE.equals(response.getHasValidActivity())) {
            return response;
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(brandId).kipUserId(loginUser.getCId()).build());
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        if (tbMemberAsset.getCurrentPoints() <= 0) {
            return response;
        }
        // 查询店铺的最大抵现金额
        PointsRedemptionDto amountDto = tbSetshoprateService.getMaxAmountOfShopCashOut(SalesAutoPointsDto.builder().groupId(tbMemberAsset.getGroupId()).mallId(lbsId)
                .shopId(tbBaseShop.getContractNo()).member(tbMemberAsset).saleType(SaleTypeEnum.KERRY_PAY.getValue()).remark(PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode()).build(), config);
        // 取最大金额
        BigDecimal amountOfPoints = Objects.isNull(amountDto) ? BigDecimal.ZERO : BigDecimal.valueOf(amountDto.getAmount());
        if (amountOfPoints.compareTo(BigDecimal.ZERO) <= 0) {
            return response;
        }
        // 比较最大可用金额与C端传入的金额的大小
        if (amountOfPoints.compareTo(resource.getAmount()) > 0) {
            amountOfPoints = resource.getAmount();
        }
        response.setMaxAmount(amountOfPoints);
        response.setMaxPoints(tbSetshoprateService.getBigdecimalPoints(amountOfPoints, amountDto.getMoney(), amountDto.getPointNum()));
        response.setMoney(amountDto.getMoney());
        response.setPointNum(amountDto.getPointNum());
        List<RedemptionItemResponse> itemList = new ArrayList<>(5);
        // 最大抵现取整
        int amount = amountOfPoints.intValue();
        int hundred = isWholeNumber(amountOfPoints) ? (amount / 100 - (amount % 100 == 0 ? 1 : 0)) : (amount / 100);
        if (hundred >= 3) {
            itemList.add(RedemptionItemResponse.builder().amount(BigDecimal.valueOf(hundred * 100)).points(tbSetshoprateService.getBigdecimalPoints(BigDecimal.valueOf(hundred * 100), amountDto.getMoney(), amountDto.getPointNum())).build());
            itemList.add(RedemptionItemResponse.builder().amount(BigDecimal.valueOf((hundred - 1) * 100)).points(tbSetshoprateService.getBigdecimalPoints(BigDecimal.valueOf((hundred - 1) * 100), amountDto.getMoney(), amountDto.getPointNum())).build());
            itemList.add(RedemptionItemResponse.builder().amount(BigDecimal.valueOf((hundred - 2) * 100)).points(tbSetshoprateService.getBigdecimalPoints(BigDecimal.valueOf((hundred - 2) * 100), amountDto.getMoney(), amountDto.getPointNum())).build());
        } else {
            for (int i = hundred; i > 0; i--) {
                itemList.add(RedemptionItemResponse.builder().amount(BigDecimal.valueOf(i * 100)).points(tbSetshoprateService.getBigdecimalPoints(BigDecimal.valueOf(i * 100), amountDto.getMoney(), amountDto.getPointNum())).build());
            }
        }
        response.setItemList(itemList);
        return response;
    }

    private boolean isWholeNumber(BigDecimal number) {
        return number.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0;
    }

    @PostMapping("/custom_amount")
    @Operation(summary="优惠买单积分抵现-自定义金额计算扣除积分数", method = "POST")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true),
            @Parameter(name = "brandId", description = "请求头-品牌id", required = true),
            @Parameter(name = "lbsId", description = "请求头-lbsId", required = true)
    })
    public PointsCustomAmountResponse frontEndCustomAmount(@CurrentUser LoginUser loginUser,
                                                           @RequestBody @Valid PointsRedemptionResource resource,
                                                           @RequestHeader(value = "brandId") String brandId,
                                                           @RequestHeader(value = "lbsId") String lbsId) {
        if (resource.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw BizException.error(PointsEnum.AMOUNT_OF_POINTS_CHECK_ERROR);
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(brandId).kipUserId(loginUser.getCId()).build());
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        if (tbMemberAsset.getCurrentPoints() <= 0) {
            throw BizException.error(PointsEnum.MEMBER_INTEGRAL_INSUFFICIENT_ERROR);
        }
        // 判断店铺号是否正常
        TbBaseShop tbBaseShop = tbBaseShopService.getByTenantId(resource.getTenantId(), lbsId);
        if (Objects.isNull(tbBaseShop)) {
            throw PointBusinessException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        // 查询店铺的最大抵现金额
        PointsRedemptionDto amountDto = tbSetshoprateService.getMaxAmountOfStoreCashOut(SalesAutoPointsDto.builder().groupId(tbMemberAsset.getGroupId()).mallId(lbsId)
                .shopId(tbBaseShop.getContractNo()).member(tbMemberAsset).saleType(SaleTypeEnum.KERRY_PAY.getValue()).remark(PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode()).build());
        // 取最大金额
        BigDecimal amountOfPoints = Objects.isNull(amountDto) ? BigDecimal.ZERO : BigDecimal.valueOf(amountDto.getAmount());
        if (amountOfPoints.compareTo(BigDecimal.ZERO) <= 0) {
            throw BizException.error(PointsEnum.POINTS_REDEEM_NOT_ENOUGH);
        }
        // 比较最大可用金额与C端传入的金额的大小
        if (resource.getAmount().compareTo(amountOfPoints) > 0) {
            throw BizException.error(PointsEnum.POINTS_REDEEM_AMOUNT_OVER_LIMIT);
        }
        return PointsCustomAmountResponse.builder().amount(resource.getAmount())
                .points(tbSetshoprateService.getBigdecimalPoints(resource.getAmount(), amountDto.getMoney(), amountDto.getPointNum())).build();
    }

    @PostMapping("/verify_points_redemption_amount_valid")
    @Operation(summary="优惠买单积分抵现-KerryPay创建支付单时，校验积分抵现金额是否有效", method = "POST")
    public PointsCustomAmountResponse verifyPointsRedemptionAmountValid(@RequestBody @Valid VerifyPointsRedemptionAmountValidResource resource) {
        if (resource.getPointsRedemptionAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw BizException.error(PointsEnum.AMOUNT_OF_POINTS_CHECK_ERROR);
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(resource.getGroupId()).kipUserId(resource.getKipUserId()).build());
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        if (tbMemberAsset.getCurrentPoints() <= 0) {
            throw BizException.error(PointsEnum.MEMBER_INTEGRAL_INSUFFICIENT_ERROR);
        }
        // 判断店铺号是否正常
        TbBaseShop tbBaseShop = tbBaseShopService.getByTenantId(resource.getTenantId(), resource.getMallId());
        if (Objects.isNull(tbBaseShop)) {
            throw PointBusinessException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        // 查询店铺的最大抵现金额
        PointsRedemptionDto amountDto = tbSetshoprateService.getMaxAmountOfStoreCashOut(SalesAutoPointsDto.builder().groupId(tbMemberAsset.getGroupId()).mallId(resource.getMallId())
                .shopId(tbBaseShop.getContractNo()).member(tbMemberAsset).saleType(SaleTypeEnum.KERRY_PAY.getValue()).remark(PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode()).build());
        // 取最大金额
        BigDecimal amountOfPoints = Objects.isNull(amountDto) ? BigDecimal.ZERO : BigDecimal.valueOf(amountDto.getAmount());
        if (amountOfPoints.compareTo(BigDecimal.ZERO) <= 0) {
            throw BizException.error(PointsEnum.POINTS_REDEEM_NOT_ENOUGH);
        }
        // 比较最大可用金额与KerryPay后端端传入的金额的大小
        if (resource.getPointsRedemptionAmount().compareTo(amountOfPoints) > 0) {
            throw BizException.error(PointsEnum.POINTS_REDEEM_AMOUNT_OVER_LIMIT);
        }
        return PointsCustomAmountResponse.builder().amount(resource.getPointsRedemptionAmount())
                .points(tbSetshoprateService.getBigdecimalPoints(resource.getPointsRedemptionAmount(), amountDto.getMoney(), amountDto.getPointNum())).build();
    }

    @PostMapping("/redemption_deduct_points")
    @Operation(summary="优惠买单积分抵现-KerryPay支付成功扣除积分", method = "POST")
    public PointsCustomAmountResponse kerryPayRedemptionDeductPoints(@RequestBody @Valid RedemptionAmountDeductPointsResource resource) {
        if (resource.getPointsRedemptionAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw BizException.error(PointsEnum.AMOUNT_OF_POINTS_CHECK_ERROR);
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(resource.getGroupId()).kipUserId(resource.getKipUserId()).build());
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        if (tbMemberAsset.getCurrentPoints() <= 0) {
            throw BizException.error(PointsEnum.MEMBER_INTEGRAL_INSUFFICIENT_ERROR);
        }
        // 判断店铺号是否正常
        TbBaseShop tbBaseShop = tbBaseShopService.getByTenantId(resource.getTenantId(), resource.getMallId());
        if (Objects.isNull(tbBaseShop)) {
            throw PointBusinessException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        // 查询店铺的最大抵现金额
        PointsRedemptionDto amountDto = tbSetshoprateService.getMaxAmountOfStoreCashOut(SalesAutoPointsDto.builder().groupId(tbMemberAsset.getGroupId()).mallId(resource.getMallId())
                .saleType(SaleTypeEnum.KERRY_PAY.getValue()).shopId(tbBaseShop.getContractNo()).member(tbMemberAsset).saleType(SaleTypeEnum.KERRY_PAY.getValue())
                .remark(PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode()).remarkName(PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getDesc()).build());
        // 取最大金额
        BigDecimal amountOfPoints = Objects.isNull(amountDto) ? BigDecimal.ZERO : BigDecimal.valueOf(amountDto.getAmount());
        if (amountOfPoints.compareTo(BigDecimal.ZERO) <= 0) {
            throw BizException.error(PointsEnum.POINTS_REDEEM_NOT_ENOUGH);
        }
        // 比较最大可用金额与KerryPay后端端传入的金额的大小
        if (resource.getPointsRedemptionAmount().compareTo(amountOfPoints) > 0) {
            throw BizException.error(PointsEnum.POINTS_REDEEM_AMOUNT_OVER_LIMIT);
        }
        PointsRedemptionEnum redemptionEnum = PointsRedemptionEnum.KERRY_PAY_CASH_OUT;
        // 组装积分变更的数据类
        MemberPointsChangeDto changeDto = MemberPointsChangeDto.builder().adjustPointsId(IdUtil.simpleUUID())
                .groupId(resource.getGroupId()).mallId(resource.getMallId()).vipcode(tbMemberAsset.getVipcode()).saleNo(resource.getOrderNo())
                .saleType(SaleTypeEnum.KERRY_PAY.getValue()).saleAmount(resource.getPointsRedemptionAmount()).dictValue(redemptionEnum.getCode())
                .dictName(redemptionEnum.getDesc()).associatedBusinessId(resource.getOrderNo()).shopNo(tbBaseShop.getContractNo()).creator("KERRY_PAY")
                .type("S").build();
        // 重复请求判断
        String lockKey = String.format(RedisCacheKey.INTEGRAL_ADJUST_LOCK, "KERRY_PAY", resource.getMallId(), resource.getOrderNo());
        if (Boolean.FALSE.equals(redisService.setSaleNoIfAbsent(lockKey, IntegralConstant.KIP_POINTS))) {
            log.info("请勿重复积分抵现");
            throw PointBusinessException.error(PointsEnum.REPEAT_SALES_DATA_ERROR);
        }
        try {
            // 扣除积分数
            int points = tbSetshoprateService.getBigdecimalPoints(resource.getPointsRedemptionAmount(), amountDto.getMoney(), amountDto.getPointNum());
            if (points > 0) {
                changeDto.setChangePointsNum(-points);
                changeDto.setRemark(String.format("%s - 积分抵现金额: %s, 扣除积分: %s" , changeDto.getDictName(), resource.getPointsRedemptionAmount(), changeDto.getChangePointsNum()));
                // 执行积分变更
                tbMemberPointsChangeService.updateMemberPoints(changeDto);
            }
            return PointsCustomAmountResponse.builder().amount(resource.getPointsRedemptionAmount()).points(changeDto.getChangePointsNum()).build();
        } catch (Exception e) {
            e.printStackTrace();
            throw BizException.error(PointsEnum.INTERNAL_SERVER_ERROR);
        } finally {
            redisService.delKeys(Collections.singletonList(lockKey));
        }
    }

    @PutMapping("/order_refund")
    @Operation(summary="优惠买单积分抵现-订单发生退款返还积分抵现积分", method = "PUT")
    public void kerryPayOrderRefund(@RequestBody @Valid KerryPayRefundResource resource) {
        // 判断店铺号是否正常
        TbBaseShop tbBaseShop = tbBaseShopService.getByTenantId(resource.getTenantId(), resource.getMallId());
        if (Objects.isNull(tbBaseShop)) {
            throw PointBusinessException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        // 查询会员信息
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(resource.getGroupId()).kipUserId(resource.getKipUserId()).build());
        if (Objects.isNull(tbMemberAsset)) {
            return;
        }
        // 优惠买单-积分抵现退款枚举值
        PointsRedemptionEnum refundEnum = PointsRedemptionEnum.KERRY_PAY_CASH_OUT_REFUND;
        // 查询该单是否已退还积分记录
        List<TbPointsDetail> details = tbPointsDetailService.queryPointsRecordByConditions(resource.getGroupId(), resource.getMallId(), tbMemberAsset.getVipcode(), resource.getOrderNo(),
                tbBaseShop.getContractNo(), Arrays.asList(refundEnum.getCode(), PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode()));
        if (CollectionUtils.isEmpty(details) || details.stream().anyMatch(item -> StringUtils.equals(item.getReasonType(), refundEnum.getCode()))) {
            return;
        }
        // 查询该优惠买单积分抵现记录
        TbPointsDetail detail = details.stream().filter(item -> StringUtils.equals(item.getReasonType(), PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode())).findFirst().orElse(null);
        if (Objects.isNull(detail) || detail.getAmount() > 0) {
            return;
        }
        // 组装积分变更的数据类
        MemberPointsChangeDto changeDto = MemberPointsChangeDto.builder().adjustPointsId(IdUtil.simpleUUID())
                .groupId(resource.getGroupId()).mallId(resource.getMallId()).vipcode(tbMemberAsset.getVipcode()).saleNo(resource.getOrderNo())
                .saleType(SaleTypeEnum.KERRY_PAY.getValue()).saleAmount(BigDecimal.ZERO).dictValue(refundEnum.getCode())
                .dictName(refundEnum.getDesc()).associatedBusinessId(resource.getOrderNo()).shopNo(tbBaseShop.getContractNo()).creator("KERRY_PAY")
                .changePointsNum(Math.abs(detail.getAmount())).type("S")
                .remark(String.format("%s, 退回积分数: %s", refundEnum.getDesc(), Math.abs(detail.getAmount())))
                .build();
        // 重复请求判断
        String lockKey = String.format(RedisCacheKey.INTEGRAL_ADJUST_LOCK, "KERRY_PAY", resource.getMallId(), resource.getOrderNo());
        if (Boolean.FALSE.equals(redisService.setSaleNoIfAbsent(lockKey, IntegralConstant.KIP_POINTS))) {
            log.info("请勿重复推送优惠买单退款退积分的操作: {}-{}-{}", resource.getGroupId(), resource.getKipUserId(), resource.getOrderNo());
            throw PointBusinessException.error(PointsEnum.REPEAT_SALES_DATA_ERROR);
        }
        try {
            // 执行积分变更
            tbMemberPointsChangeService.updateMemberPoints(changeDto);
        } finally {
            redisService.delKeys(Collections.singletonList(lockKey));
        }
    }

}
