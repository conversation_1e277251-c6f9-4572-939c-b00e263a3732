package com.kerryprops.kip.service.integral.service.impl;

import com.kerryprops.kip.service.integral.client.MessageServiceClient;
import com.kerryprops.kip.service.integral.entity.TbTemplateMessage;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.dto.MessageQueryDto;
import com.kerryprops.kip.service.integral.model.dto.TbSmsTemplateConfig;
import com.kerryprops.kip.service.integral.model.vo.WxTemplateSendVo;
import com.kerryprops.kip.service.integral.service.MessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/23/2023 09:57
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class MessageServiceImpl implements MessageService {

    private final MessageServiceClient messageServiceClient;

    @Override
    public List<TbSmsTemplateConfig> getSmsTemplate(MessageQueryDto queryDto) {
        return messageServiceClient.getSmsTemplate(queryDto).getData();
    }

    @Override
    public KerryResultDto sendSmsMessage(MessageQueryDto query) {
        return messageServiceClient.sendSmsMessage(query);
    }

    @Override
    public List<TbTemplateMessage> getModelTemplate(MessageQueryDto query) {
        return messageServiceClient.getModelTemplate(query).getData();
    }

    @Override
    public KerryResultDto sendSmsTemplate(WxTemplateSendVo query) {
        return messageServiceClient.sendSmsTemplate(query);
    }
}
