package com.kerryprops.kip.service.integral.service.impl;

import com.google.common.collect.Maps;
import com.kerryprops.kip.service.integral.mapper.TicketMemberGradeMapper;
import com.kerryprops.kip.service.integral.model.dto.TicketMemberGradeDto;
import com.kerryprops.kip.service.integral.service.TicketMemberGradeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 06/27/2023 10:29
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class TicketMemberGradeServiceImpl implements TicketMemberGradeService {

    private final TicketMemberGradeMapper ticketMemberGradeMapper;

    @Override
    public Map<String, TicketMemberGradeDto> getTicketInfo(List<String> ticketIds, String grade) {
        if (CollectionUtils.isEmpty(ticketIds)) {
            return Maps.newHashMap();
        }
        List<TicketMemberGradeDto> list = ticketMemberGradeMapper.findList(ticketIds, grade);
        return list.stream().collect(Collectors.toMap(TicketMemberGradeDto::getTicketId, Function.identity(), (v1, v2) -> v1));
    }
}
