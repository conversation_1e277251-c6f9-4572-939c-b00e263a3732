package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbOcrCallbackRecord;
import com.kerryprops.kip.service.integral.mapper.TbOcrCallbackRecordMapper;
import com.kerryprops.kip.service.integral.service.TbOcrCallbackRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/01/2024 17:07
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbOcrCallbackRecordServiceImpl extends ServiceImpl<TbOcrCallbackRecordMapper, TbOcrCallbackRecord> implements TbOcrCallbackRecordService {

    private final TbOcrCallbackRecordMapper tbOcrCallbackRecordMapper;

    @Override
    public void saveRecord(String taskId, Long photoId) {
        TbOcrCallbackRecord record = TbOcrCallbackRecord.builder().taskId(taskId).photoId(photoId).createDate(new Date()).content("").build();
        tbOcrCallbackRecordMapper.insert(record);
    }

    @Override
    public TbOcrCallbackRecord findByTaskId(String taskId) {
        QueryWrapper<TbOcrCallbackRecord> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        return tbOcrCallbackRecordMapper.selectOne(queryWrapper);
    }

    @Override
    public TbOcrCallbackRecord findByPhotoId(Long photoId) {
        QueryWrapper<TbOcrCallbackRecord> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("photo_id", photoId);
        return tbOcrCallbackRecordMapper.selectOne(queryWrapper);
    }

    @Async
    @Override
    public CompletableFuture<Map<Long, String>> findByPhotoIds(List<Long> photoIds) {
        if (CollectionUtils.isEmpty(photoIds)) {
            return CompletableFuture.completedFuture(Collections.emptyMap());
        }
        List<TbOcrCallbackRecord> list = tbOcrCallbackRecordMapper.findByPhotoIds(photoIds);
        return CompletableFuture.completedFuture(CollectionUtils.isEmpty(list) ?
                        Collections.emptyMap() : list.stream().collect(Collectors.toMap(TbOcrCallbackRecord::getPhotoId, TbOcrCallbackRecord::getTaskId, (k1, k2) -> k1)));
    }
}
