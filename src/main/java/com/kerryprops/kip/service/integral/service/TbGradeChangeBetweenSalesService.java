package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbGradeChangeBetweenSales;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.model.dto.MemberYearAmountCalDto;

import java.util.Date;

public interface TbGradeChangeBetweenSalesService extends IService<TbGradeChangeBetweenSales> {

    /**
     * 通过会员等级变更记录id查询卡等级标记的销售区间
     * @param changeId
     * @return
     */
    TbGradeChangeBetweenSales findByChangeId(String changeId);

    TbGradeChangeBetweenSales saveDetail(String changeId, Date saleStartTime, Date saleEndTime);

    /**
     * 补齐等级变更记录关联的销售信息
     * @param detail: 会员卡等级变更记录
     * @param calDto: 是否是周期年，true:是， false:否，即自然年
     * @param minGrade: group下设置的最低卡等
     */
    TbGradeChangeBetweenSales checkChangeBetweenSales(TbMemberGradeChangeDetail detail, MemberYearAmountCalDto calDto, TbMemberGrade minGrade);

}
