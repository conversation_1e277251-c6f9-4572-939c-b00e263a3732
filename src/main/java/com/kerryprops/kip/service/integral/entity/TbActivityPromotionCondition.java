package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * tb_activity_promotion_condition
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_activity_promotion_condition")
public class TbActivityPromotionCondition implements Serializable {

    private String id;

    private String promotionId;

    private String promotionConditionGroupId;

    /**
     * 条件类型(0:会员等级、1:性别、2:生日当天、3:会员当月、4:每周、5:每月、6:此单购物是否当月首笔购物、7:地址、8、首次入会)
     */
    private String promotionConditionType;

    private String promotionConditionContent;

    /**
     * 活动上设置的店铺信息
     */
    private transient String shopId;

}