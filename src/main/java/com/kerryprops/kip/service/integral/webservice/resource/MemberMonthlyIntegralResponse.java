package com.kerryprops.kip.service.integral.webservice.resource;



import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/07/2022 12:20
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "月份积分收入，支出汇总")
public class MemberMonthlyIntegralResponse {

    @Schema( description = "月份")
    private String month;

    @Schema( description = "已收入积分数")
    private int incomePoints;

    @Schema( description = "已支出积分数")
    private int bonusPoints;

    @Schema( description = "月份下的积分明细")
    private List<MemberIntegralAdjustRecordResponse> details;

}
