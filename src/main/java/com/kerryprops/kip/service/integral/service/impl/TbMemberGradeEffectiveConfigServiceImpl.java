package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeEffectiveConfig;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeEffectiveConfigMapper;
import com.kerryprops.kip.service.integral.service.TbMemberGradeEffectiveConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - member-points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 06/05/2023 14:47
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbMemberGradeEffectiveConfigServiceImpl extends ServiceImpl<TbMemberGradeEffectiveConfigMapper, TbMemberGradeEffectiveConfig> implements TbMemberGradeEffectiveConfigService {

    private final TbMemberGradeEffectiveConfigMapper tbMemberGradeEffectiveConfigMapper;

    @Override
    public TbMemberGradeEffectiveConfig findByGroupId(String groupId) {
        return tbMemberGradeEffectiveConfigMapper.findByGroupId(groupId);
    }

    @Async
    @Override
    @Cacheable(value = RedisCacheKey.KIP_MEMBER_GRADE_EFFECTIVE_CONFIG_KEY, key = "#groupId", unless = "#result == null")
    public CompletableFuture<TbMemberGradeEffectiveConfig> getFutureByGroupId(String groupId) {
        return CompletableFuture.completedFuture(tbMemberGradeEffectiveConfigMapper.findByGroupId(groupId));
    }
}
