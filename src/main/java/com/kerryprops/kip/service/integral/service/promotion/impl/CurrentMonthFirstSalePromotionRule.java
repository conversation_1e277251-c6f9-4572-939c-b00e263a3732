package com.kerryprops.kip.service.integral.service.promotion.impl;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MemberSaleMonthNumberQueryDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:30
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class CurrentMonthFirstSalePromotionRule implements PromotionRule {

    private final TbSalesDetailService tbSalesDetailService;

    @Override
    public String getRuleType() {
        return "6";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        TbMemberAsset member = dto.getMember();
        String startDate = DateUtil.formatDateTime(DateUtil.beginOfMonth(saleDate));
        String endDate = DateUtil.formatDateTime(DateUtil.endOfMonth(saleDate));
        MemberSaleMonthNumberQueryDto queryDto = MemberSaleMonthNumberQueryDto.builder().startDate(startDate).endDate(endDate).vipcode(member.getVipcode()).groupId(dto.getGroupId()).build();
        return tbSalesDetailService.queryMemberMonthSalesNumber(queryDto) <= 0;
    }
}
