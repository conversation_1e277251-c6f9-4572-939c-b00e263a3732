package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/***********************************************************************************************************************
 * Project - auth-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/29/2022 18:28
 **********************************************************************************************************************/

@Getter
public enum ProviderEnum {

    WX("weixin"),
    ALI("alipay");

    private String val;

    ProviderEnum(String val) {
        this.val = val;
    }

    public static String getProvider(String provider) {
        String val = ProviderEnum.WX.getVal();
        if (StringUtils.isBlank(provider)) {
            return val;
        }
        for (ProviderEnum typeEnum : values()) {
            if (typeEnum.getVal().equals(provider)) {
                val = typeEnum.getVal();
                break;
            }
        }
        return val;
    }

}
