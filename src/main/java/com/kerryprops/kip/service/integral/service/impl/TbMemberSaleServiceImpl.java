//package com.kerryprops.kip.service.integral.service.impl;
//
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.map.MapUtil;
//import cn.hutool.core.util.IdUtil;
//import cn.hutool.core.util.StrUtil;
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
//import com.kerryprops.kip.service.integral.common.IntegralConstant;
//import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
//import com.kerryprops.kip.service.integral.common.RedisCacheKey;
//import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
//import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
//import com.kerryprops.kip.service.integral.config.MallConfig;
//import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
//import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
//import com.kerryprops.kip.service.integral.entity.TbMemberSale;
//import com.kerryprops.kip.service.integral.mapper.TbMemberSaleMapper;
//import com.kerryprops.kip.service.integral.model.dto.*;
//import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
//import com.kerryprops.kip.service.integral.service.*;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.collections4.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.*;
//import java.util.concurrent.CompletableFuture;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//
///***********************************************************************************************************************
// * Project - integral-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 08/16/2022 13:48
// **********************************************************************************************************************/
//
//@Slf4j
//@Service
//@DS("xcrm")
//@AllArgsConstructor
//public class TbMemberSaleServiceImpl extends ServiceImpl<TbMemberSaleMapper, TbMemberSale> implements TbMemberSaleService {
//
//    private final TbMemberSaleMapper tbMemberSaleMapper;
//    private final TbMemberAssetService tbMemberAssetService;
//    private final RabbitMqService rabbitMqService;
//    private final ProfileServiceClient profileServiceClient;
//    private final MallConfig mallConfig;
//    private final RedisService redisService;
//    private final HiveVasService hiveVasService;
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void saveMemberSale(TbMemberSale memberSale) {
//        tbMemberSaleMapper.insert(memberSale);
//    }
//
//    @Override
//    public TbMemberSale checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto queryDto) {
//        return tbMemberSaleMapper.checkSaleRecordRepeatedOrNot(queryDto);
//    }
//
//    @Override
//    public TbMemberSale querySaleBySellNo(String sellNo) {
//        return tbMemberSaleMapper.querySaleBySellNo(sellNo);
//    }
//
//    @Override
//    public TbMemberSale queryBySellNoAndSaleType(String sellNo, String saleType) {
//        return tbMemberSaleMapper.queryBySellNoAndSaleType(sellNo, saleType);
//    }
//
//    @Override
//    public int queryMemberMonthSalesNumber(MemberSaleMonthNumberQueryDto query) {
//        return tbMemberSaleMapper.queryMemberMonthSalesNumber(query);
//    }
//    @Override
//    public TbMemberSale selectBySellNoAndMallId(String sellNo, String mallId) {
//        return tbMemberSaleMapper.selectBySellNoAndMallId(sellNo, mallId);
//    }
//
//    @Override
//    public void saveSaleData(SalesAutoPointsDto dto) {
//        if (StringUtils.isBlank(dto.getIntegralAdjustId())) {
//            dto.setIntegralAdjustId(IdUtil.simpleUUID());
//        }
//        Date date = new Date();
//        TbMemberSale sale = new TbMemberSale();
//        sale.setId(dto.getId());
//        sale.setGroupId(dto.getGroupId());
//        sale.setMallid(dto.getMallId());
//        sale.setCreateDate(DateUtil.parse(dto.getSaleDate()));
//        sale.setUpdateDate(date);
//        sale.setCreateUser(dto.getCreateUser());
//        sale.setUpdateUser(dto.getCreateUser());
//        sale.setVipcode(dto.getMember().getVipcode());
//        sale.setSalemoney(StringUtils.isBlank(dto.getTotalAmount()) ? Double.valueOf(dto.getAmount()) : Double.valueOf(dto.getTotalAmount()));
//        sale.setSaleType(dto.getSaleType());
//        sale.setSellno(dto.getSaleNo());
//        sale.setShopid(dto.getShopId());
//        // 填充店铺业态品牌名称
//        Optional.ofNullable(dto.getBaseShop()).ifPresent(shop -> {
//            if (StringUtils.isNotBlank(shop.getRetailBrandName())) {
//                sale.setShopName(shop.getRetailBrandName());
//            } else {
//                sale.setShopName(shop.getShopName());
//            }
//        });
//        sale.setIntegral((double) dto.getExchangePoints());
//        // 未退货
//        sale.setStatus("0");
//        sale.setUsemoney(Double.valueOf(dto.getAmount()));
//        // 设置积分调整id
//        sale.setIntegraladjustid(dto.getIntegralAdjustId());
//        sale.setRemark(dto.getSalesRemark());
//        sale.setBeforeGrade(dto.getMember().getGrade());
//        sale.setBeforeIntegral(dto.getMember().getCurrentPoints());
//        // 优惠金额
//        try {
//            sale.setPreferentialmoney(StringUtils.isBlank(dto.getDiscountAmount()) ? 0 : Double.parseDouble(dto.getDiscountAmount()));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        // 拍照积分上传图片地址
//        if (StringUtils.isNotBlank(dto.getImageUrl())) {
//            sale.setImageUrl(dto.getImageUrl());
//        }
//        // 填充身份信息
//        this.fillIdentityParams(sale, dto);
//        // 新增销售记录
//        tbMemberSaleMapper.insert(sale);
//        // 排除线上商城-积分商城
//        if (StringUtils.equals(IntegralConstant.REWARDS_MALL, dto.getRemark())) {
//            return;
//        }
//        // 发MQ消息
////        rabbitMqService.sendMqMessage(sale, dto);
//    }
//
//    /**
//     * 填充身份信息参数
//     * @param sale
//     * @param dto
//     */
//    private void fillIdentityParams(TbMemberSale sale, SalesAutoPointsDto dto) {
//        MallItem mallItem = mallConfig.getByMallId(dto.getMallId());
//        if (Objects.isNull(mallItem)) {
//            return;
//        }
//        List<CustomerIdentityDto> identityList = null;
//        if (Objects.nonNull(dto.getMember()) && StringUtils.isNotBlank(dto.getMember().getKipUserId())) {
//            identityList = profileServiceClient.getIdentityResponse(dto.getMember().getKipUserId(), mallItem.getProjectId());
//        }
//        sale.setExtend1(CollectionUtils.isEmpty(identityList) ? "0" : (identityList.stream().anyMatch(CustomerIdentityDto::isOffice) ? "1" : "0"));
//        sale.setExtend2(CollectionUtils.isEmpty(identityList) ? "0" : (identityList.stream().anyMatch(CustomerIdentityDto::isApartment) ? "1" : "0"));
//        sale.setExtend3(CollectionUtils.isEmpty(identityList) ? "0" : (identityList.stream().anyMatch(CustomerIdentityDto::isResidence) ? "1" : "0"));
//    }
//
//    @Override
//    public Double getMemberSaleAmountBetweenTime(TbMemberAsset member, TbMemberGradeRule rule, Date beginDate, Date endDate, List<String> shopIds) {
//        Double amount = this.getGradeRuleCacheAmount(rule, member);
//        return Objects.nonNull(amount) ? amount : this.getMemberSaleAmountBetweenDate(member, shopIds, beginDate, endDate, rule.getId());
//    }
//
//    @Override
//    public Double getMemberSaleAmountBetweenDate(TbMemberAsset member, List<String> shopIds, Date beginDate, Date endDate, Long ruleId) {
//        MemberSaleAmountQueryDto queryDto = this.getSaleAmountQueryDto(member, shopIds, beginDate, endDate);
//        TbMemberSale memberSale = tbMemberSaleMapper.getMemberSaleAmountBetweenTime(queryDto);
//        BigDecimal moneyConsumed;
//        if (Objects.isNull(memberSale)) {
//            moneyConsumed = BigDecimal.ZERO;
//        } else {
//            moneyConsumed = BigDecimal.valueOf(memberSale.getUsemoney() - memberSale.getBackmoney()).setScale(2, RoundingMode.HALF_UP);
//        }
//        String key = String.format(RedisCacheKey.MEMBER_GRADE_RULE_SALE_MONEY, member.getGroupId(), member.getVipcode());
//        redisService.hSet(key, ruleId.toString(), moneyConsumed.toString(), 60 * 60 * 3);
//        return moneyConsumed.doubleValue();
//    }
//
//    /**
//     * 获取会员时间段内消费金额查询条件
//     * @param member
//     * @param shopIds
//     * @param beginDate
//     * @param endDate
//     * @return
//     */
//    private MemberSaleAmountQueryDto getSaleAmountQueryDto(TbMemberAsset member, List<String> shopIds, Date beginDate, Date endDate) {
//        MemberSaleAmountQueryDto queryDto = MemberSaleAmountQueryDto.builder().groupId(member.getGroupId()).vipcode(member.getVipcode()).shopIds(shopIds).build();
//        if (Objects.nonNull(beginDate)) {
//            queryDto.setStartDate(DateUtil.formatDateTime(beginDate));
//        }
//        if (Objects.nonNull(endDate)) {
//            queryDto.setEndDate(DateUtil.formatDateTime(endDate));
//        }
//        return queryDto;
//    }
//
//    @Override
//    public boolean checkMemberSaleAmountWhetherRelegationSucceeded(TbMemberAsset member, TbMemberGradeRule rule, Date beginDate, Date endDate, List<String> shopIds) {
//        MemberGradeWhetherRelegationQueryDto queryDto = MemberGradeWhetherRelegationQueryDto.builder().groupBy(rule.getRuleType())
//                .groupId(member.getGroupId()).vipcode(member.getVipcode()).build();
//        if (Objects.nonNull(beginDate)) {
//            queryDto.setStartDate(DateUtil.formatDateTime(beginDate));
//        }
//        if (Objects.nonNull(endDate)) {
//            queryDto.setEndDate(DateUtil.formatDateTime(endDate));
//        }
//        queryDto.setAmount(Objects.nonNull(rule.getMoney()) ? rule.getMoney().doubleValue() : 0);
//        queryDto.setShopIds(shopIds);
//        return tbMemberSaleMapper.checkMemberSaleAmountWhetherRelegationSucceeded(queryDto) > 0;
//    }
//
//    @Async
//    @Override
//    public void initMemberGradeData(String vipcode) {
//        log.info("*************** initMemberGradeData Start *****************");
//        List<String> dates = Arrays.asList("2022-11-02", "2022-11-03");
//        for (String date: dates) {
//            for (MallItem item: mallConfig.getList()) {
//                List<TbMemberSale> sales = tbMemberSaleMapper.initMemberGradeData(item.getMallId(), vipcode, String.format("%s 00:00:00", date), String.format("%s 23:59:59", date));
//                if (CollectionUtils.isEmpty(sales)) {
//                    continue;
//                }
//                log.info("商场: {}, 日期: {}, 销售数据总数: {}", item.getAbbreviation(), date, sales.size());
//                for (TbMemberSale sale: sales) {
//                    rabbitMqService.sendMessage(RabbitMqConstant.MEMBER_SALES_TRIGGER_UPGRADE_CHECK, JsonUtils.objToString(sale));
//                }
//            }
//        }
//        log.info("*************** initMemberGradeData End *****************");
//    }
//
//    @Override
//    public void modifyMemberSaleShopId(String shopIds, String mallId, String newIds) {
//        if (StringUtils.isAnyBlank(shopIds, newIds, mallId)) {
//            return;
//        }
//        List<String> oldIds = StrUtil.split(shopIds, CommonSeparators.COMMA_SEPARATOR);
//        List<String> replaceIds = StrUtil.split(newIds, CommonSeparators.COMMA_SEPARATOR);
//        if (oldIds.size() != replaceIds.size()) {
//            log.info("shopIds, newIds size not match");
//            return;
//        }
//        for (int i = 0, j = oldIds.size(); i < j; i++) {
//            List<String> salesIds = tbMemberSaleMapper.getShopMemberSalesIds(mallId, oldIds.get(i));
//            if (CollectionUtils.isEmpty(salesIds)) {
//                continue;
//            }
//            log.info("old shop id: {} query sales data: {}", oldIds.get(i), salesIds.size());
//            tbMemberSaleMapper.updateMemberSalesShopId(salesIds, replaceIds.get(i));
//        }
//        log.info("修改销售表内的店铺ID已完成.");
//    }
//
//    @Async
//    @Override
//    public CompletableFuture<Map<String, TbMemberSale>> findSalesInfoMap(String groupId, String mallId, String vipcode, List<String> sellNos) {
//        return CompletableFuture.completedFuture(getSalesInfoList(groupId, mallId, vipcode, sellNos));
//    }
//
//    @Override
//    public Map<String, TbMemberSale> getSalesInfoList(String groupId, String mallId, String vipcode, List<String> sellNos) {
//        if (CollectionUtils.isEmpty(sellNos)) {
//            return MapUtil.empty();
//        }
//        List<TbMemberSale> sales = tbMemberSaleMapper.findSalesInfo(groupId, mallId, vipcode, sellNos);
//        if (CollectionUtils.isEmpty(sales)) {
//            return MapUtil.empty();
//        }
//        List<String> shopIds = sales.stream().map(TbMemberSale::getShopid).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(shopIds)) {
//            return sales.stream().collect(Collectors.toMap(TbMemberSale::getSellno, Function.identity(), (k1, k2) -> k2));
//        }
//        // 通过店铺号查询店铺关联品牌名称
//        Map<String, TenantInfoVo> nameMap = hiveVasService.getTenantInfoByShopNos(shopIds);
//        sales.forEach(it -> {
//            if (StringUtils.isNotBlank(it.getShopid())) {
//                Optional.ofNullable(MapUtils.getObject(nameMap, it.getShopid())).ifPresent(item -> it.setShopName(item.getRetailBrandName()));
//            }
//        });
//        return sales.stream().collect(Collectors.toMap(TbMemberSale::getSellno, Function.identity(), (k1, k2) -> k2));
//    }
//
//    @Override
//    public void memberSaleRefundProcess(String saleId) {
//        TbMemberSale sale = tbMemberSaleMapper.getMemberSaleById(saleId);
//        if (Objects.isNull(sale)) {
//            log.error("销售记录ID: [{}]对应的记录不存在.", saleId);
//            return;
//        }
//        TbMemberAsset member = tbMemberAssetService.queryMemberByGroupIdAndVipCode(sale.getGroupId(), sale.getVipcode());
//        if (Objects.isNull(member)) {
//            log.error("销售记录对应的用户不存在: [{}-{}]", sale.getGroupId(), sale.getVipcode());
//            return;
//        }
//        log.info("销售单号: [{}]，退款发送满赠消息通知", sale.getSellno());
////        rabbitMqService.sendFullActivityReward(sale, member);
//    }
//
//    @Override
//    public TbMemberSale getMemberSaleById(String id) {
//        return tbMemberSaleMapper.getMemberSaleById(id);
//    }
//
//    @Override
//    public List<TbMemberSale> getSaleListCreateAsc(String groupId, String vipcode, Date beginDate, Date endDate, List<String> shopIds) {
//        return tbMemberSaleMapper.getSaleListCreateAsc(groupId, vipcode, beginDate, endDate, shopIds);
//    }
//
//    @Async
//    @Override
//    public void checkSalesUpgradeGrade(String groupId, String startDate, String endDate) {
//        log.info("CheckSalesUpgradeGrade Send MQ Begin: {}", groupId);
//        String startTime = String.format("%s 00:00:00", startDate);
//        String endTime = String.format("%s 23:59:59", endDate);
//        List<String> vipcodes = tbMemberSaleMapper.checkSalesUpgradeGrade(groupId, startTime, endTime);
//        if (CollectionUtils.isEmpty(vipcodes)) {
//            log.info("CheckSalesUpgradeGrade Send MQ End: {}", groupId);
//            return;
//        }
//        Map<String, Object> saleMap = null;
//        for (String vipcode: vipcodes) {
//            String saleId = tbMemberSaleMapper.getOneSalesBetweenTime(groupId, vipcode, startTime, endTime);
//            if (StringUtils.isBlank(saleId)) {
//                continue;
//            }
//            saleMap = new HashMap<>(4);
//            saleMap.put(IntegralConstant.SALE_ID, saleId);
//            // 用于标记是否发放满赠奖励
//            saleMap.put(IntegralConstant.SEND_FULL_REWARD, vipcode);
//            rabbitMqService.sendMessage(RabbitMqConstant.MEMBER_SALES_TRIGGER_UPGRADE_CHECK, JsonUtils.objToString(saleMap));
//        }
//        log.info("CheckSalesUpgradeGrade Send MQ End: {}", groupId);
//    }
//
//    @Override
//    public Double getGradeRuleCacheAmount(TbMemberGradeRule rule, TbMemberAsset tbMemberAsset) {
//        String key = String.format(RedisCacheKey.MEMBER_GRADE_RULE_SALE_MONEY, tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode());
//        Object amount = null;
//        try {
//            amount = redisService.hGet(key, rule.getId() + "");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return Objects.nonNull(amount) ? Double.parseDouble(amount + "") : null;
//    }
//}
