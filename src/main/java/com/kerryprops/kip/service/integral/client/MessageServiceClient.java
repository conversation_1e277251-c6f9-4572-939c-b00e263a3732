package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.entity.TbTemplateMessage;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.dto.MessageQueryDto;
import com.kerryprops.kip.service.integral.model.dto.TbSmsTemplateConfig;
import com.kerryprops.kip.service.integral.model.vo.WxTemplateSendVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.awt.color.ICC_Profile;
import java.util.List;

/***********************************************************************************************************************
 * Project - loyalty-engine-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/01/2023 17:59
 **********************************************************************************************************************/

@FeignClient(name = "message-distribution-service", url = "${kerry.services.message-distribution-service:default}")
public interface MessageServiceClient {

    /**
     * 查询短信模板(groupId, sendNodeDesc, memberGrade)
     *
     * @param dto dto
     * @return Result result
     */
    @PostMapping(value = "/template/smsTemplateConfig/getSmsTemplate")
    KerryResultDto<List<TbSmsTemplateConfig>> getSmsTemplate(@RequestBody MessageQueryDto dto);

    /**
     * 发送短信
     *
     * @param query query
     * @return Result result
     */
    @PostMapping(value = "/template/smsTemplateConfig/sendSmsMessage")
    KerryResultDto sendSmsMessage(MessageQueryDto query);

    /**
     * 查询模板消息(mallId, sendNodeDesc, memberGrade)
     *
     * @param query query
     * @return Result result
     */
    @PostMapping(value = "/template/wxTemplateConfig/selectTemplate")
    KerryResultDto<List<TbTemplateMessage>> getModelTemplate(MessageQueryDto query);

    /**
     * 发送模板消息
     *
     * @param query query
     * @return Result result
     */
    @PostMapping(value = "/template/wxTemplateConfig/sendMessage")
    KerryResultDto sendSmsTemplate(WxTemplateSendVo query);
}
