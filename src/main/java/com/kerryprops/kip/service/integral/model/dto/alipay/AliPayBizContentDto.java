package com.kerryprops.kip.service.integral.model.dto.alipay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝支付回调接口参数接收类
 * @createDate 2022/11/1
 * @updateDate 2022/11/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
public class AliPayBizContentDto extends AliPayNotifyDto {

    /**
     * 交易单号
     */
    @JsonProperty("trade_no")
    private String tradeNo;

    /**
     * 支付宝用户id
     */
    @JsonProperty("buyer_id")
    private String buyerId;

    /**
     * 支付宝的mall id
     */
    @JsonProperty("mall_id")
    private String mallIdAli;

    /**
     * 支付宝的store id
     */
    @JsonProperty("mall_store_id")
    private String mallStoreId;

    @JsonProperty("out_request_no")
    private String outRequestNo;

    /**
     * 总金额
     */
    @JsonProperty("total_amount")
    private String totalAmount;

    /**
     * 收据金额
     */
    @JsonProperty("receipt_amount")
    private String receiptAmount;

    /**
     * 退款金额
     */
    @JsonProperty("refund_fee")
    private String refundAmount;

    /**
     * 消息规则，alipay.business.mall.tradeapply.notify表示积分补录
     */
    @JsonProperty("msg_method")
    private String method;

    /**
     * 消息规则，alipay.business.mall.tradeapply.notify表示积分补录
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 支付时间
     */
    @JsonProperty(value = "gmt_payment")
    private String gmtPayment;

    /**
     * 退款时间
     */
    @JsonProperty("gmt_refund_pay")
    private String gmtRefundPay;

    /**
     * 传参信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String transactionInfo;

    @JsonIgnoreProperties(ignoreUnknown = true)
    private String cardNo;

}
