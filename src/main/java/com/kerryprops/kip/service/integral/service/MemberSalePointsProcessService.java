package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 13:38
 **********************************************************************************************************************/
public interface MemberSalePointsProcessService {

    /**
     * 销售转积分统一处理
     * @param pointsDto 参数对象
     * @return int: 此笔销售获得的积分数
     */
    Integer salePointsProcess(SalesAutoPointsDto pointsDto);

    /**
     * 退款操作
     * @param pointsDto 参数对象
     * @return int
     */
    int refundSalePointsProcess(SalesAutoPointsDto pointsDto);
}
