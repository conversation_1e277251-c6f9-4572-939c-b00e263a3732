package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate;
import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/17/2023 09:17
 **********************************************************************************************************************/
public interface TbIntegralCategoryRateMapper extends BaseMapper<TbIntegralCategoryRate> {

    List<TbIntegralCategoryRate> categoryRatePage(@Param("groupId") String groupId, @Param("categoryId") String categoryId, @Param("mallIds") List<String> mallId, @Param("offset") int offset, @Param("size") int size);

    int getTotal(@Param("groupId") String groupId, @Param("categoryId") String categoryId, @Param("mallIds") List<String> mallId);

    void deleteByParams(@Param("groupId") String groupId, @Param("mallId") String mallId, @Param("categoryId") String categoryId);

    void saveCategoryRateList(@Param("rateList") List<TbIntegralCategoryRate> rateList);

    List<TbIntegralCategoryRate> getCategoryRateList(TbIntegralCategoryRate rate);
}
