package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbBatchIntegralPresentDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TbBatchIntegralPresentDetailMapper extends BaseMapper<TbBatchIntegralPresentDetail>  {

    TbBatchIntegralPresentDetail queryDetailByVipcodeAndOverdueTime(@Param("vipcode") String vipcode,
                                                                    @Param("groupId") String groupId,
                                                                    @Param("overdueTime") String overdueTime);

}