package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.VirtualPhoneQueryDto;
import com.kerryprops.kip.service.integral.model.dto.VirtualPhoneResponseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "toolkit-service", url = "${kerry.services.toolkit-service:default}")
public interface ToolkitServiceClient {

    @PostMapping("/risk/assess")
    List<VirtualPhoneResponseDto> riskCheck(@RequestBody VirtualPhoneQueryDto queryDto);

}
