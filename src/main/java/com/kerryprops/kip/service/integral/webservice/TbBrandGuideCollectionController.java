package com.kerryprops.kip.service.integral.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbBrandGuideCollection;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.BrandGuideQueryDto;
import com.kerryprops.kip.service.integral.model.dto.BrandGuideRespDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.BrandGuideQueryResource;
import com.kerryprops.kip.service.integral.webservice.resource.BrandGuideSaveOrCancelResource;
import com.kerryprops.kip.service.integral.webservice.resource.CommonResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/18/2024 14:55
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/brand_guide")
@RestController
@RequiredArgsConstructor
@Tag(name = "品牌导览收藏API")
public class TbBrandGuideCollectionController {

    private final TbBrandGuideCollectionService tbBrandGuideCollectionService;
    private final MemberRegisterService memberRegisterService;
    private final RabbitMqService rabbitMqService;
    private final HiveVasService hiveVasService;
    private final HiveVasClient hiveVasClient;
    private final RedisService redisService;
    private final Mapper mapper;

    @PostMapping("/collect")
    @Operation(summary="收藏", method = "POST")
    public CommonResponse saveBrandGuide(@CurrentUser LoginUser loginUser, @RequestBody @Valid BrandGuideSaveOrCancelResource resource) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        // 验证导览id是否正确
        List<BrandGuideRespDto> details = hiveVasClient.getBrandGuideDetail(resource.getBrandGuideId());
        if (CollectionUtils.isEmpty(details)) {
            throw BizException.error(PointsEnum.VALIDATION_FAILED);
        }
        String redisKey = tbBrandGuideCollectionService.getBrandGuideKey(loginUser.getBrandId(), tbMemberAsset.getVipcode(), loginUser.getLbsId());
        // 加入收藏
        redisService.addFavoriteBrand(redisKey, resource.getBrandGuideId());
        // 发送MQ消息
        TbBrandGuideCollection collection = TbBrandGuideCollection.builder().groupId(loginUser.getBrandId()).mallId(loginUser.getLbsId()).brandGuideId(resource.getBrandGuideId())
                .vipcode(tbMemberAsset.getVipcode()).status(1).build();
        // 异步收藏品牌导览
        rabbitMqService.sendMessage(RabbitMqConstant.CRM_BRAND_GUIDE_COLLECT, JsonUtils.objToString(collection));
        return CommonResponse.builder().success(true).build();
    }

    @PutMapping("/cancel")
    @Operation(summary="取消收藏", method = "PUT")
    public CommonResponse cancelBrandGuide(@CurrentUser LoginUser loginUser, @RequestBody @Valid BrandGuideSaveOrCancelResource resource) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        String redisKey = tbBrandGuideCollectionService.getBrandGuideKey(loginUser.getBrandId(), tbMemberAsset.getVipcode(), loginUser.getLbsId());
        // 移除收藏
        redisService.removeFavoriteBrand(redisKey, resource.getBrandGuideId());
        // 发送MQ消息
        TbBrandGuideCollection collection = TbBrandGuideCollection.builder().groupId(loginUser.getBrandId()).mallId(loginUser.getLbsId()).brandGuideId(resource.getBrandGuideId())
                .vipcode(tbMemberAsset.getVipcode()).status(0).build();
        // 异步取消品牌导览
        rabbitMqService.sendMessage(RabbitMqConstant.CRM_BRAND_GUIDE_COLLECT, JsonUtils.objToString(collection));
        return CommonResponse.builder().success(true).build();
    }

    @GetMapping("/detail")
    @Operation(summary="查询品牌导览id是否已收藏", method = "GET")
    public CommonResponse getBrandGuideDetail(@CurrentUser LoginUser loginUser, @RequestParam("brandGuideId") String brandGuideId) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        String redisKey = tbBrandGuideCollectionService.getBrandGuideKey(loginUser.getBrandId(), tbMemberAsset.getVipcode(), loginUser.getLbsId());
        return CommonResponse.builder().success(redisService.inSet(redisKey, brandGuideId)).build();
    }

    @PostMapping("/collect_list")
    @Operation(summary="收藏/未收藏品牌导览列表", method = "POST")
    public List<BrandGuideRespDto> brandGuideList(@CurrentUser LoginUser loginUser,
                                                  @RequestHeader("brandId") String brandId,
                                                  @RequestHeader("lbsId") String lbsId,
                                                  @RequestBody BrandGuideQueryResource resource) {
        // 收藏列表
        List<String> brands = Collections.emptyList();
        if ((Objects.nonNull(loginUser) && StringUtils.isNotBlank(loginUser.getCId()))) {
            TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(brandId, loginUser.getCId());
            if (Objects.nonNull(tbMemberAsset)) {
                brands = tbBrandGuideCollectionService.getBrandGuideList(tbMemberAsset.getGroupId(), lbsId, tbMemberAsset.getVipcode());
            }
        }
        // 组装查询参数
        BrandGuideQueryDto queryDto = mapper.map(resource, BrandGuideQueryDto.class);
        queryDto.setLbsId(lbsId);
        queryDto.setIds(CollectionUtils.isEmpty(brands) ? Collections.emptyList() : brands);
        return hiveVasClient.getBrandGuideList(queryDto);
    }

    @GetMapping("/my_collection")
    @Operation(summary="我的收藏-品牌导览", method = "GET")
    public List<BrandGuideRespDto> getMyCollection(@CurrentUser LoginUser loginUser) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        if (Objects.isNull(tbMemberAsset)) {
            return Collections.emptyList();
        }
        MallItem mallItem = hiveVasService.getMallByLbsId(loginUser.getLbsId());
        if (Objects.isNull(mallItem)) {
            return Collections.emptyList();
        }
        // 查询用户品牌导览收藏的id列表
        List<String> brandGuideIds = tbBrandGuideCollectionService.getBrandGuideFromDb(tbMemberAsset.getGroupId(), mallItem.getMallId(), tbMemberAsset.getVipcode());
        if (CollectionUtils.isEmpty(brandGuideIds)) {
            return Collections.emptyList();
        }
        // 调用hive查询导览明细
        BrandGuideQueryDto queryDto = BrandGuideQueryDto.builder().collectFlag(Boolean.TRUE).lbsId(mallItem.getMallId()).ids(brandGuideIds).build();
        List<BrandGuideRespDto> brandGuideList = hiveVasClient.getBrandGuideList(queryDto);
        if (CollectionUtils.isEmpty(brandGuideList)) {
            return Collections.emptyList();
        }
        Map<String, BrandGuideRespDto> brandMap = brandGuideList.stream().collect(Collectors.toMap(BrandGuideRespDto::getId, Function.identity(), (k1, k2) -> k1));
        List<BrandGuideRespDto> collectList = new ArrayList<>(brandGuideList.size());
        BrandGuideRespDto respDto = null;
        for (String guideId: brandGuideIds) {
            respDto = MapUtils.getObject(brandMap, guideId);
            if (Objects.isNull(respDto)) {
                continue;
            }
            collectList.add(respDto);
        }
        return collectList;
    }

}
