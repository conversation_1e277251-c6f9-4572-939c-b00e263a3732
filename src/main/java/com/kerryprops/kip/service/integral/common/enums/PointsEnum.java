package com.kerryprops.kip.service.integral.common.enums;

import com.kerryprops.kip.service.integral.common.current.IError;
import lombok.Getter;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2022 16:43
 **********************************************************************************************************************/

@Getter
public enum PointsEnum implements IError {

    INTERNAL_SERVER_ERROR(500000, "服务器内部错误!"),
    VALIDATION_FAILED(4000001,"参数异常，请重试"),
    MEMBER_SALES_DUPLICATED(4000002, "会员销售数据重复积分"),
    MEMBER_NOT_EXISTS(4000003, "会员信息不存在."),
    BRAND_GRADE_NOT_EXISTS(4000004, "该品牌下会员等级设置不存在."),
    QR_CODE_SALES_DATA_NOT_FOUND(4000005, "未查询到该二维码的销售数据."),
    GAG_SALES_DATA_AMOUNT_ERROR(4000006, "查询销采金额错误."),
    MALL_SHOP_NOT_FOUND(4000007, "该商场店铺不存在."),
    GAG_QR_CODE_OVERDUE_ERROR(4000008, "二维码已过期."),
    MALL_NOT_EXISTS(4000009, "商场信息不存在."),
    REPEAT_SCANNING_CODE_ERROR(4000010, "正在处理中，请勿重复扫码."),
    QR_CODE_REPEATED_POINTS_ERROR(4000011, "二维码已自动积分."),
    MEMBER_INTEGRAL_UPDATE_ERROR(4000012, "会员积分修改异常."),
    MEMBER_INTEGRAL_INSUFFICIENT_ERROR(4000013, "会员积分数不足."),

    ALIPAY_UPDATE_CARD_ERROR(4000014, "支付宝更新会员卡信息失败"),
    LOCK_ERROR(4000015, "加锁失败"),
    REQUIRED_PARAMETER_NULL_ERROR(4000016, "必填参数不能为空"),
    MEMBER_GRADE_NOT_EXISTS(4000017, "该品牌下会员等级设置不存在."),
    MEMBER_GRADE_RULE_NOT_EXISTS(4000017, "该品牌下会员等级规则不存在."),
    GAG_SALE_INFO_EMPTY_ERROR(4000018, "销采系统返回店铺号为空."),
    Z_INDEX_NO_MY_MAGE_ERROR(4000019, "没有配置我的页面."),
    MEMBER_FREEZE_ERROR(4000020, "会员已冻结."),
    PROMOTION_RULE_PARAMS_ERROR(4000021, "营销积分规则参数错误."),
    GAG_QR_DATA_SALE_TIME_ERROR(4000022, "您的小票扫描结果异常，请重新扫描或前往客服台进行人工积分"),
    SEND_SMS_ERROR(4000023, "短信发送失败."),
    LBS_CONFIG_EXISTS_ERROR(4000024, "该LBS自助积分入口已配置."),
    LBS_CONFIG_NOT_EXISTS_ERROR(4000025, "自助积分入口配置不存在."),
    ONLINE_SHOP_CONFIG_EXISTS_ERROR(4000026, "该线上商城积分已配置."),
    ONLINE_SHOP_CONFIG_NOT_EXISTS_ERROR(4000027, "该线上商城积分配置不存在."),
    METHOD_NOT_ALLOW(4000028, "不支持的请求方式."),
    BONUS_RECORD_NOT_FOUND(4000029, "拍照积分记录不存在."),
    BONUS_RECORD_AUDITED(4000030, "拍照积分记录已审核."),
    BONUS_REPEATED_POINTS_ERROR(4000031, "根据积分去重规则判定为重复积分."),
    BONUS_RECORD_AUDIT_ERROR(4000032, "该拍照积分单号正在处理中，请勿重复提交."),
    ONLINE_MALL_RATE_MONEY_ERROR(4000033, "线上店铺积分比例配置错误."),

    SALE_NOT_EXISTS_ERROR(4000034, "该笔销售记录不存在."),
    SALE_IS_REFUND(4000035, "该笔销售记录已退款."),
    MEMBER_INTEGRAL_LESS_THEN_INTEGRAL(4000036, "会员积分低于销售积分，无法退货"),
    SALE_REMARK_NOT_FOUND(4000037, "销售录入类型不存在"),

    CHECK_STEP_ERROR(4000038, "销售录入校验步骤异常，请重新校验"),
    INTEGRAL_BY_OTHERS_ERROR(4000039, "已被其他会员积分"),
    INTEGRAL_BY_SELF_ERROR(4000040, "您已积分，请勿重复积分"),
    INTEGRAL_APPROVED_ERROR(4000041, "根据实际情况判断是否给予积分"),
    INTEGRAL_RATE_ID_ERROR(4000042, "积分设置删除需要传入id数组."),
    METHOD_SIGN_VALID_ERROR(4000043, "签名验证错误"),
    METHOD_SIGN_INVALID_ERROR(4000044, "签名已失效"),
    CASH_OUT_CONFIG_NOT_EXISTS_ERROR(4000045, "积分抵现配置不存在."),
    CASH_OUT_CONFIG_REPEAT_ERROR(4000046, "抵现活动时间不可以有交集"),
    AMOUNT_OF_POINTS_CHECK_ERROR(4000047, "积分抵现金额不合法"),
    DO_COS_OUT_OF_SYSTEM(4000048, "该合同号不存在"),
    USER_UNAUTHORIZED_SENSELESS_POINTS(4000049, "用户未授权无感积分"),
    MALL_NOT_FOUND(4000050, "该商场不存在."),
    PHOTO_REVIEW_NO_DATA(4000051, "没有上一页数据"),
    PHOTO_REVIEW_NO_CHECK_DATA(4000052, "没有可审核的会员积分记录了."),
    ORDER_ERROR_NOT_REFUND_POINTS(4000053, "该笔订单未退款无法退还抵现积分"),
    REFUND_MONEY_OUT_OF_MONEY(4000054, "退款金额超出实付金额"),
    FORBID_DUPLICATE_CASH_OUT(4000055, "一笔销售只能使用一次积分抵现"),
    REPEAT_SALES_DATA_ERROR(4000056, "正在处理中，请勿重复提交数据."),
    POINTS_IS_INTERCEPT(4000057, "该笔销售被拦截，请进入积分拦截页面审核"),
    REFUND_POINTS_IS_INTERCEPT(4000057, "该笔销售被拦截，退款操作成功"),
    INTERCEPT_NOT_EXISTS_ERROR(4000058, "该商场下不存在对应的销售记录和销售异常记录,建议重新选择商场"),
    POINTS_IS_INTERCEPT_REPEAT(4000059, "该笔销售已经被拦截,请勿重复操作"),
    POINTS_REDEEM_NOT_ENOUGH(4000060, "积分抵现活动不存在."),
    POINTS_REDEEM_AMOUNT_OVER_LIMIT(4000061, "积分抵现自定义金额超出限制"),
    ACCESS_TOO_FREQUENTLY(4000070, "请求过于频繁，请稍后再试"),

    ERROR_WITH_ZERO_CHANGE_IN_POINTS(4000080, "积分变更数不能为0."),
    EXPORT_SIZE_OVER_LIMITED(4000090, "导出数据量超出10w限制."),
    IMAGE_UPLOAD_FAILED(4000100, "拍照积分图片上传失败."),

    REPEATED_OPT_POINTS_ERROR(4000120, "重复操作积分."),
    ;

    private Integer code;
    private String msg;

    PointsEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }
}
