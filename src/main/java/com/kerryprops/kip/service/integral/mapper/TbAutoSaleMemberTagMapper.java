package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbAutoSaleMemberTag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/20/2022 11:31
 **********************************************************************************************************************/
public interface TbAutoSaleMemberTagMapper extends BaseMapper<TbAutoSaleMemberTag> {

    List<TbAutoSaleMemberTag> getBySecSortIdAndType(@Param("secSortId") Long secSortId, @Param("type") Integer type);

}
