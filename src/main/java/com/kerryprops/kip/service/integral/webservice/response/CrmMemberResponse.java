package com.kerryprops.kip.service.integral.webservice.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/07/2024 14:43
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "查询CRM会员信息响应类")
public class CrmMemberResponse implements Serializable {

    @Schema( name = "集团id")
    private String groupId;

    @Schema( name = "开卡商场id")
    private String mallId;

    @Schema( name = "会员号")
    private String vipcode;

    @Schema( name = "会员当前积分数")
    private int currentPoints;

    @Schema( name = "会员等级")
    private String grade;

    @Schema( name = "会员加入当前group的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    @Schema( name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

}
