package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description
 * @createDate 2022/9/20
 * @updateDate 2022/9/20
 */
@Getter
public enum CodeMessageEnum {


    /**
     * code 成功
     */
    SUCCESS_CODE("SUCCESS"),

    /**
     * code 错误
     */
    ERROR_CODE("ERROR"),

    /**
     * message 成功
     */
    SUCCESS_MESSAGE("成功"),

    /**
     * message 错误
     */
    ERROR_MESSAGE("失败"),

    /**
     * 微信无感积分通用回调接口返回值
     */
    ACCEPTED("accepted"),

    OK("000000"),

    SUCCESS("success"),

    FAIL("fail"),

    ;

    private final String value;

    CodeMessageEnum(String value) {
        this.value = value;
    }
}
