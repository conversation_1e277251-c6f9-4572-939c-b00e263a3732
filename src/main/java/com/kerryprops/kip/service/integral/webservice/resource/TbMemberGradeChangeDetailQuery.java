package com.kerryprops.kip.service.integral.webservice.resource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/11/2023 13:41
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbMemberGradeChangeDetailQuery implements Serializable {

    private String groupId;

    // 查询商场号
    private String mallid;

    //会员号
    private String vipcode;

    // 手机号
    private String mobile;

    //会员新等级
    private String newgrade;

    //会员旧等级
    private String oldgrade;

    // 查询条件卡变动类型
    private String queryChangeType;

    // 查询开始时间
    private String beginDate;

    // 查询结束时间
    private String endDate;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 偏移量
     */
    private Integer offset;

    /**
     * 分批查询出来，每批数据量的大小
     */
    private Integer size = 10;

}