package com.kerryprops.kip.service.integral.model.dto;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 会员表
 * @date 2022-09-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class TbMemberDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;

    /**
     * 集团ID
     */
    private String groupId;

    /**
     * 商场编号
     */
    private String mallid;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 会员编号（规则生成）
     */
    private String vipcode;

    /**
     * 当前积分
     */
    private Double currentIntegral;

    /**
     * 当前会员卡等[编码]
     */
    private String grade;

    /**
     * 会员状态（0:冻结；1:非冻结)
     */
    private String status;

    /**
     * ,微信激活商场
     */
    private String wxopenmarket;

    /**
     * 兴趣标签（维度维护）多选
     */
    private String hobys;

    /**
     * 加入日期
     */
    private Date jointime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否完善信息（0:未完善；1:完善)
     */
    private String iscompleted;

    /**
     * kip对应的user_id
     */
    private String kipUserId;

    /**
     * 是否黑名单(0:是 1:不是),供活动使用
     */
    private Integer whetherBlacklist;

    /**
     * 注册来源
     */
    private String registerSource;

    /**
     * 注册来源-附注信息(例如：活动，电子券)
     */
    private String registerSourceLabel;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 创建人
     */
    private String createBy;

}
