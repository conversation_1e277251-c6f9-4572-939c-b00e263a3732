package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource;
import com.kerryprops.kip.service.integral.webservice.response.BadgeShopGroupResultResponse;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 03/12/2024 12:02
 **********************************************************************************************************************/
public interface TbSalesDetailMapper extends BaseMapper<TbSalesDetail> {

    Integer checkMemberSaleExists(@Param("saleDate") String saleDate,
                                  @Param("shopNo") String shopNo,
                                  @Param("orderNo") String orderNo,
                                  @Param("mallId") String mallId);

    /**
     * 根据销售单号去重
     * @param orderNo
     * @return
     */
    TbSalesDetail queryByOrderNo(@Param("orderNo") String orderNo);

    TbSalesDetail queryByOrderNoAndSaleType(@Param("orderNo") String orderNo, @Param("saleType") String saleType);

    TbSalesDetail checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto dto);

    /**
     * 通过list条件查询是否存在重复的销售信息
     * @param list
     * @return
     */
    List<TbSalesDetail> checkSaleRepeatedByConditions(@Param("list") List<MemberSaleRepeatQueryDto> list);

    /**
     * 查询会员时间段内排除退款的销售笔数
     * @param query
     * @return
     */
    int queryMemberMonthSalesNumber(MemberSaleMonthNumberQueryDto query);

    /**
     * 查询会员时间段内不排除退款的销售笔数
     * @param query
     * @return
     */
    int queryMemberSaleCountBetweenTime(MemberSaleMonthNumberQueryDto query);

    TbSalesDetail queryByOrderNoAndMallId(@Param("orderNo") String orderNo, @Param("mallId") String mallId);

    /**
     * 查询会员时间段内的销售记录
     * @param groupId
     * @param vipcode
     * @param beginDate
     * @param endDate
     * @param shopIds 店铺号列表
     * @return
     */
    List<TbSalesDetail> getSaleListCreateAsc(@Param("groupId") String groupId, @Param("vipcode") String vipcode, @Param("beginDate") Date beginDate, @Param("endDate") Date endDate, @Param("shopNos") List<String> shopNos);

    /**
     * 根据条件查询会员的销售金额
     * @param queryDto
     * @return
     */
    TbSalesDetail getSaleAmountBetweenTime(MemberSaleAmountQueryDto queryDto);

    /**
     * 查询保级金额是否匹配
     * @param queryDto
     * @return
     */
    int checkMemberSaleAmountWhetherRelegationSucceeded(MemberGradeWhetherRelegationQueryDto queryDto);

    /**
     * 处理11/2, 11/3号两天由于bug导致会员未升级的数据
     * @param mallId
     * @param beginDate
     * @param endDate
     * @return
     */
    List<TbSalesDetail> initMemberGradeData(@Param("mallId") String mallId, @Param("vipcode") String vipcode,
                                           @Param("beginDate") String beginDate, @Param("endDate") String endDate);

    List<Long> getShopMemberSalesIds(@Param("mallId") String mallId, @Param("shopNo") String shopNo);

    void updateMemberSalesShopId(@Param("salesIds") List<Long> salesIds, @Param("shopNo") String shopNo);

    List<TbSalesDetail> findSalesList(@Param("groupId") String groupId, @Param("mallId") String mallId,
                                     @Param("vipcode") String vipcode, @Param("orderNos") List<String> orderNos);

    TbSalesDetail getMemberSaleById(@Param("id") Long id);

    List<String> checkSalesUpgradeGrade(@Param("groupId") String groupId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询时间段内会员的一条销售记录
     * @param groupId
     * @param vipcode
     * @param startTime
     * @param endTime
     * @return
     */
    String getOneSalesBetweenTime(@Param("groupId") String groupId, @Param("vipcode") String vipcode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询积分拦截对应数量
     * @param queryDto
     * @return int
     */
    List<String> querySaleIsIntercept(SaleDetailQueryDto queryDto);

    List<TbSalesDetail> getMemberSaleList(@Param("groupId") String groupId, @Param("vipcode") String vipcode);

    /**
     * 修复数据sql
     * @param sql
     * @return
     */
    int executeSql(@Param("sql") String sql);

    /**
     * 查询原生sql返回的结果信息
     * @param sql
     * @return
     */
    List<Map<String, Object>> querySqlResult(@Param("sql") String sql);

    /**
     * 徽章累计金额计算
     * @param resource
     * @return
     */
    BigDecimal accumulatedSalesAmount(BadgeSalesResource resource);

    /**
     * 徽章查询消费店铺数量
     * @param resource
     * @return
     */
    List<String> consumeShopCount(BadgeSalesResource resource);

    /**
     * 徽章查询会员消费频次
     * @param resource
     * @return
     */
    int consumeFrequency(BadgeSalesResource resource);

    /**
     * 徽章查询会员消费天数
     * @param resource
     * @return
     */
    List<String> consumeDays(BadgeSalesResource resource);

    /**
     * 徽章关联的店铺组销售明细
     * @param list
     * @return
     */
    List<BadgeShopGroupResultResponse> queryBadgeShopGroupDetails(@Param("list") List<BadgeSalesResource> list);

    /**
     * KO卡等变更触发会员升级
     * @param groupId
     * @param date
     * @return
     */
    List<String> gradeChangeTriggerMemberUpgrade(@Param("groupId") String groupId, @Param("date") String date);

    /**
     * 查询会员最近一条销售记录
     * @param groupId
     * @param vipcode
     * @return
     */
    TbSalesDetail getLatestSalesRecord(@Param("groupId") String groupId, @Param("vipcode") String vipcode);

}
