package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.webservice.response.HomePageDisplayMyPageResponse;
import com.kerryprops.kip.service.integral.webservice.response.SelfDefiningModuleResponse;
import com.kerryprops.kip.service.integral.webservice.response.ZIndexListResponse;

/**
 * <AUTHOR>
 */
public interface ZIndexService {

    void setModulesForCustomize(ZIndexListResponse ZIndexListResponse, String moduleText);

    /**
     * 根据groupId + mallId查询设置的首页配置
     * @param groupId groupId
     * @param mallId mallId
     * @return dto
     */
    ZIndexListResponse getHomePageByGroupIdAndMallId(String groupId, String mallId);

    /**
     * 根据groupId + mallId查询设置的我的配置
     * @param groupId groupId
     * @param mallId mallId
     * @return dto
     */
    ZIndexListResponse getMyPageByGroupIdAndMallId(String groupId, String mallId);

    /**
     * 查询首页需要展示在我的页面的模块
     * @param groupId groupId
     * @param mallId mallId
     * @param myModule myModule
     * @return dto
     */
    HomePageDisplayMyPageResponse getHomePageDisplayInMyPageModules(String groupId, String mallId, SelfDefiningModuleResponse myModule);

    /**
     * 根据id查询页面
     * @param groupId groupId
     * @param mallId mallId
     * @param id id
     * @return dto
     */
    ZIndexListResponse getPageById(String groupId, String mallId, String id);
}
