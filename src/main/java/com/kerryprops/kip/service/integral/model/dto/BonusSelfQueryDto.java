package com.kerryprops.kip.service.integral.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/27/2023 10:53
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BonusSelfQueryDto implements Serializable {

    /**
     * 导出时根据id做过滤
     */
    private Long id;

    private String groupId;

    private String vipcode;

    private String mobile;

    /**
     * 4: 小程序扫码  3: 小票审核  1: CRM后台录入
     */
    private String type;

    /**
     * 状态
     */
    private List<String> status;

    private String mallId;

    private String shopNo;

    /**
     * 小票号
     */
    private String serialNum;

    private String examineStartTime;

    private String examineEndTime;

    private String uploadStartTime;

    private String uploadEndTime;

    private Double moneyStart;

    private Double moneyEnd;

    private String qrcode;

    private int page;

    private int size;

    private int offset;

    public int getOffset() {
        return this.page * this.size;
    }

    /**
     * 前端传递的排序字段
     */
    private String orderBy;

}
