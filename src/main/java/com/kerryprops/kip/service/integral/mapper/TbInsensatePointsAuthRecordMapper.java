package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description 无感积分 微信、支付宝授权记录表
 * <AUTHOR>
 * @date 2022-09-08
 */
@Mapper
public interface TbInsensatePointsAuthRecordMapper extends BaseMapper<TbInsensatePointsAuthRecord> {

    /**
    * 查询 根据open id 查询
    * <AUTHOR>
    * @date 2022/09/08
    **/
    TbInsensatePointsAuthRecord selectByOpenId(String openId, String mallId, String groupId, int origin, Integer status);

    TbInsensatePointsAuthRecord getByOpenIdAndMallId(@Param("openId") String openId, @Param("mallId") String mallId);

    TbInsensatePointsAuthRecord checkExists(String openId, String groupId, String mallId, int origin);

    List<TbInsensatePointsAuthRecord> selectByKipUserIdAndMallId(String kipUserId, String mallId);

    TbInsensatePointsAuthRecord getByKipUserIdAndMallIdAndOrigin(String kipUserId, String mallId, int origin);

    void insertBatchFromCardMemberRelation();

    List<TbInsensatePointsAuthRecord> getBatchData(@Param("id") Long id, @Param("batchSize") int batchSize);

    List<TbInsensatePointsAuthRecord> getMallBatchData(@Param("mallId") String mallId, @Param("id") Long id, @Param("batchSize") int batchSize);

    TbInsensatePointsAuthRecord getByCardNoAndOrigin(@Param("cardNo") String cardNo, @Param("origin") int origin);

    /**
     * 检查用户微信无感积分授权状态是否有效
     * @param kipUserId
     * @return
     */
    List<TbInsensatePointsAuthRecord> findWxAuthRecordByKipUserId(@Param("kipUserId") String kipUserId);

}