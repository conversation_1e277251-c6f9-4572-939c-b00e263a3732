package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbCashOutConfig;
import com.kerryprops.kip.service.integral.webservice.resource.TbCashOutConfigResource;


/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 16:01
 **********************************************************************************************************************/
public interface TbCashOutConfigMapper extends BaseMapper<TbCashOutConfig> {

    /**
     * 分页查询
     * @param resource resource
     * @return list list
     */
    TbCashOutConfig getConfig(TbCashOutConfigResource resource);
}
