package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralCategoryRateResource;
import com.kerryprops.kip.service.integral.webservice.response.TbIntegralCategoryRateResponse;

import java.util.List;

public interface TbIntegralCategoryRateService {
    List<TbIntegralCategoryRate> categoryRatePage(String groupId, String categoryId, List<String> mallId, int page, int size);

    int getTotal(String groupId, String categoryId, List<String> mallId);

    void saveOrUpdateCategoryRate(TbIntegralCategoryRateResource resource);

    List<TbIntegralCategoryRate> getCategoryRateList(TbIntegralCategoryRate rate);

    void deleteCategoryRateList(Long[] ids);
}
