package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_qr_code_record
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("tb_qr_code_record")
public class TbQrCodeRecord implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 扫码积分二维码编号
     */
    @TableField("qr_code")
    private String qrCode;

    /**
     * 销售记录json字符串
     */
    private String content;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

}