package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.vo.KipMemberVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/09/2023 11:44
 **********************************************************************************************************************/

@FeignClient(name = "profile-service", url = "${kerry.services.profile:default}")
public interface ProfileServiceClient {

    /**
     * CRM营销积分活动查询会员身份
     * @param kipUserId
     * @param projectId，支持传多个，中间英文逗号分隔
     * @return
     */
    @GetMapping("/customer/identities/crm_promotion")
    List<CustomerIdentityDto> getIdentityResponse(@RequestParam("kipUserId") String kipUserId, @RequestParam(value = "projectId", required = false) String projectId);

    /**
     * 获取用户在楼盘下的详情身份信息
     * @param kipUserId
     * @param projectIds，支持传多个，英文逗号分隔
     * @return
     */
    @GetMapping("/customer/identities/multi_project_identity")
    List<MemberIdentityInfo> getProjectIdentityResponse(@RequestParam("kipUserId") String kipUserId, @RequestParam("projectIds") String projectIds);

    @PostMapping("/country/codes/detail")
    CountryCodeDto getByAreaCodeDetail(@RequestBody @Validated AreaCodeQueryDto dto);

    @PostMapping("/customer/users/alipay/create_user")
    IdDto alipayCreateUser(@Validated @RequestBody KipMemberVO resource);

}
