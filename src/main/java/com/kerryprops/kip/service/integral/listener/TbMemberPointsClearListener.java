package com.kerryprops.kip.service.integral.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.kerryprops.kip.service.integral.service.TbKoIntegralClearRecordService;
import com.kerryprops.kip.service.integral.webservice.resource.IntegralClearFileResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/18/2023 09:10
 **********************************************************************************************************************/

@Slf4j
public class TbMemberPointsClearListener extends AnalysisEventListener<IntegralClearFileResource> {

    private final TbKoIntegralClearRecordService tbKoIntegralClearRecordService;

    // 分批
    private static final int BATCH_COUNT = 500;
    private final AtomicInteger atomic = new AtomicInteger();
    List<IntegralClearFileResource> list = new ArrayList<>();

    public TbMemberPointsClearListener(TbKoIntegralClearRecordService tbKoIntegralClearRecordService) {
        this.tbKoIntegralClearRecordService = tbKoIntegralClearRecordService;
    }

    @Override
    public void invoke(IntegralClearFileResource resource, AnalysisContext analysisContext) {
        list.add(resource);
        atomic.incrementAndGet();
        if (list.size() >= BATCH_COUNT) {
            this.updateStatus(list);
            // 存储完成清理 list
            list.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        this.updateStatus(list);
        log.info("Total Parse Data Size: {}", atomic.get());
    }

    private void updateStatus(List<IntegralClearFileResource> list) {
        tbKoIntegralClearRecordService.saveRecords(list);
        log.info("插入KO积分清零数据成功.");
    }
}