package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbQrCodeRecord;
import com.kerryprops.kip.service.integral.mapper.TbQrCodeRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.BillInfoRpcDto;
import com.kerryprops.kip.service.integral.service.TbQrCodeRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/11/2022 14:08
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbQrCodeRecordServiceImpl extends ServiceImpl<TbQrCodeRecordMapper, TbQrCodeRecord> implements TbQrCodeRecordService {

    private final TbQrCodeRecordMapper tbQrCodeRecordMapper;

    @Override
    public TbQrCodeRecord getByQrCode(String qrCode) {
        return tbQrCodeRecordMapper.getByQrCode(qrCode);
    }

    @Async
    @Override
    public void saveOrUpdate(String qrCode, BillInfoRpcDto dto) {
        TbQrCodeRecord record = tbQrCodeRecordMapper.getByQrCode(qrCode);
        if (Objects.nonNull(record)) {
            record.setContent(JsonUtils.objToString(dto));
            tbQrCodeRecordMapper.updateById(record);
        } else {
            record = TbQrCodeRecord.builder().qrCode(qrCode).content(JsonUtils.objToString(dto)).build();
            tbQrCodeRecordMapper.insert(record);
        }
    }
}
