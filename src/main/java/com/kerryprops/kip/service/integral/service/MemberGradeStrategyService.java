package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.model.dto.MemberYearAmountCalDto;

import java.util.Date;

/**
 * @description 会员等级策略服务类
 * <AUTHOR>
 * @date 2022-11-24
 */
public interface MemberGradeStrategyService {

    /**
     * @param member             会员基本信息
     * @return 更新金额后的升级文案（ruleType=1 每日消费升级）
     */
    String modifyUpgradeLeftMoney(TbMemberGradeRule memberGradeRule, TbMemberAsset member);

    /**
     * 获取自然年开始时间
     * @param changeDetail
     * @param now
     * @return
     */
    Date getNaturalYearStartDate(TbMemberGradeChangeDetail changeDetail, Date now);

    /**
     * 获取周期年开始时间
     * @param changeDetail
     * @param now
     * @return
     */
    Date getCycleYearStartDate(TbMemberGradeChangeDetail changeDetail, Date now);

    /**
     * 根据统计规则，获取会员销售统计开始时间
     * @param changeDetail 会员等级变更明细
     * @param isCycleYear 是否是周期年
     * @param calculateType 统计类型(年，月，日)
     * @return
     */
    Date getRelegationRuleBeginDate(TbMemberGradeChangeDetail changeDetail, boolean isCycleYear, String calculateType, Date beginDate);

    /**
     * 根据统计规则，获取会员销售统计开始时间
     * @param calculateType 统计类型(年，月，日)
     * @param beginDate 统计金额开始时间
     * @return
     */
    Date getUpgradeRuleBeginDate(String calculateType, Date beginDate);

    /**
     * 统计会员年消费金额的开始时间
     * @param groupId
     * @param vipcode
     * @return
     */
    MemberYearAmountCalDto getCalculateYearAmountBeginDate(String groupId, String vipcode);

    /**
     * 统计会员年消费金额的开始时间-没有缓存信息
     * @param groupId
     * @param vipcode
     * @return
     */
    MemberYearAmountCalDto getNoCacheCalculateYearAmountBeginDate(String groupId, String vipcode);

}