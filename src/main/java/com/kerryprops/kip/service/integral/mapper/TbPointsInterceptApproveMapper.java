package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbPointsIntercept;
import com.kerryprops.kip.service.integral.model.dto.TbPointsInterceptQueryDto;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 15:32
 **********************************************************************************************************************/
public interface TbPointsInterceptApproveMapper extends BaseMapper<TbPointsIntercept> {

    void saveIntercept(TbPointsIntercept intercept);

    /**
     * 获取记录
     * @param queryDto queryDto
     * @return list list
     */
    List<TbPointsIntercept> getPageData(TbPointsInterceptQueryDto queryDto);

    /**
     * 获取总数
     * @param queryDto queryDto
     * @return int int
     */
    int getTotal(TbPointsInterceptQueryDto queryDto);

    /**
     * 获取记录
     * @param queryDto queryDto
     * @return list list
     */
    TbPointsIntercept getInterceptDto(TbPointsInterceptQueryDto queryDto);
}
