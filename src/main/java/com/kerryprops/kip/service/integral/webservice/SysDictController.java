package com.kerryprops.kip.service.integral.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.service.SysDictService;
import com.kerryprops.kip.service.integral.webservice.response.SysDictResponse;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/12/2023 15:37
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/sys_dict")
@RestController
@RequiredArgsConstructor
@Hidden
public class SysDictController {

    private final SysDictService sysDictService;
    private final Mapper mapper;

    @GetMapping("/a_s_dict_list")
    public List<SysDictResponse> getComAndSaleDictType() {
        List<SysDict> types = sysDictService.getByTypes(Arrays.asList("S", "A"));
        if (CollectionUtils.isEmpty(types)) {
            return Collections.emptyList();
        }
        return types.stream().map(it -> mapper.map(it, SysDictResponse.class)).collect(Collectors.toList());
    }

}
