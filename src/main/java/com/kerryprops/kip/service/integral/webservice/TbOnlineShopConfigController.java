package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PointsRedemptionEnum;
import com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig;
import com.kerryprops.kip.service.integral.service.BaseMemberStatus;
import com.kerryprops.kip.service.integral.service.TbOnlineShopConfigService;
import com.kerryprops.kip.service.integral.webservice.resource.CommonResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopConfigResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopResource;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopSaveOrUpdateResource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 16:38
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/online_shop")
@RestController
@RequiredArgsConstructor
@Tag(name = "CRM Admin/小程序端线上商城积分API,积分抵现API")
public class TbOnlineShopConfigController extends BaseMemberStatus {

    private final TbOnlineShopConfigService tbOnlineShopConfigService;
    private final Mapper mapper;

    /**
     * 1、线上商场积分比例配置
     * @param groupId groupId
     * @param mallIds mallIds
     * @param page page
     * @param size size
     * @return Page
     */
    @GetMapping("/onlineShopPage")
    @Operation(summary="线上商场积分分页查询", method = "GET")
    public Page<TbOnlineShopConfigResponse> onlineShopPage(@RequestParam("groupId") String groupId, @RequestParam("mallIds") String mallIds, @RequestParam("page") int page, @RequestParam("size") int size) {
        List<String> mallIdList = StringUtils.isNotBlank(mallIds) ? CharSequenceUtil.split(mallIds, ",") : new ArrayList<>();
        TbOnlineShopResource resource = TbOnlineShopResource.builder().groupId(groupId).mallIdList(mallIdList).businessTypes(PointsRedemptionEnum.getConfigEnumList()).page(page).size(size).build();
        int total = tbOnlineShopConfigService.onlineShopTotal(resource);
        if (total <= 0) {
            return Page.of(page, size, total);
        }
        List<TbOnlineShopConfig> list = tbOnlineShopConfigService.onlineShopPage(resource);
        Page<TbOnlineShopConfigResponse> pageData = Page.of(page, size, total);
        pageData.setRecords(this.getDataList(list));
        return pageData;
    }

    /**
     * 1、POS积分抵现
     * 2、优惠买单积分抵现
     * @param groupId groupId
     * @param mallIds mallIds
     * @param page page
     * @param size size
     * @return Page
     */
    @GetMapping("/integralOutPage")
    @Operation(summary="积分抵现分页查询", method = "GET")
    public Page<TbOnlineShopConfigResponse> integralOutPage(@RequestParam("groupId") String groupId, @RequestParam("mallIds") String mallIds, @RequestParam("page") int page, @RequestParam("size") int size) {
        List<String> mallIdList = StringUtils.isNotBlank(mallIds) ? CharSequenceUtil.split(mallIds, ",") : new ArrayList<>();
        TbOnlineShopResource resource = TbOnlineShopResource.builder().groupId(groupId).mallIdList(mallIdList)
                .businessTypes(PointsRedemptionEnum.getCashEnumList()).page(page).size(size).build();
        int total = tbOnlineShopConfigService.onlineShopTotal(resource);
        if (total <= 0) {
            return Page.of(page, size, total);
        }
        List<TbOnlineShopConfig> list = tbOnlineShopConfigService.onlineShopPage(resource);
        Page<TbOnlineShopConfigResponse> pageData = Page.of(page, size, total);
        pageData.setRecords(this.getDataList(list));
        return pageData;
    }

    /**
     * 类型转化
     * @param list
     * @return List
     */
    private List<TbOnlineShopConfigResponse> getDataList(List<TbOnlineShopConfig> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(it -> {
            TbOnlineShopConfigResponse response = mapper.map(it, TbOnlineShopConfigResponse.class);
            Optional.ofNullable(PointsRedemptionEnum.getEnumByCode(response.getBusinessType())).ifPresent(item -> response.setBusinessName(item.getDesc()));
            return response;
        }).toList();
    }

    @PostMapping("/saveOrUpdateOnlineShopConfig")
    @Operation(summary="新增或修改线上商城的积分配置", method = "POST")
    public CommonResponse saveOrUpdateOnlineShopConfig(@CurrentUser LoginUser loginUser, @RequestBody @Validated List<TbOnlineShopSaveOrUpdateResource> resource) {
        if (Objects.nonNull(loginUser) && StringUtils.isNotBlank(loginUser.getNickName())) {
            resource.forEach(item -> item.setCreator(loginUser.getNickName()));
        }
        tbOnlineShopConfigService.saveOrUpdateOnlineShopConfig(resource.stream().map(it -> mapper.map(it, TbOnlineShopConfig.class)).collect(Collectors.toList()));
        return CommonResponse.builder().success(Boolean.TRUE).build();
    }

    @DeleteMapping("/removeOnlineShopConfig")
    @Operation(summary="删除自助积分入口配置", method = "DELETE")
    public CommonResponse removeOnlineShopConfig(@RequestBody Long[] ids) {
        tbOnlineShopConfigService.removeOnlineShopConfig(ids);
        return CommonResponse.builder().success(Boolean.TRUE).build();
    }

    @GetMapping("/getOnlineShop")
    @Operation(summary="获取线上商场配置", method = "GET")
    public List<TbOnlineShopConfigResponse> getOnlineShop(@RequestParam("mallId") String mallId, @RequestParam("businessType") String businessType) {
        TbOnlineShopConfig configParam = TbOnlineShopConfig.builder().mallId(mallId).businessType(businessType).build();
        List<TbOnlineShopConfig> list = tbOnlineShopConfigService.getOnlineShop(configParam);
        return this.getDataList(list);
    }

}
