package com.kerryprops.kip.service.integral.config;

import cn.hutool.core.text.CharSequenceUtil;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/14/2022 16:03
 **********************************************************************************************************************/

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "mall")
public class MallConfig {

    private List<MallItem> list;
    /**
     * 允许积分兑换为负分的集团id，多个以英文逗号隔开
     */
    private String negativeMallGroup;
    /**
     * KO group包含的集团id，多个以英文逗号隔开
     */
    private String koBigGroup;

    public MallItem getByMallId(String mallId) {
        if (StringUtils.isBlank(mallId)) {
            return null;
        }
        return list.stream().filter(it -> StringUtils.equals(it.getMallId(), mallId)).findFirst().orElse(null);
    }

    public List<MallItem> getByGroupId(String groupId) {
        if (StringUtils.isBlank(groupId)) {
            return Collections.emptyList();
        }
        return list.stream().filter(it -> StringUtils.equals(it.getGroupId(), groupId)).collect(Collectors.toList());
    }

    public MallItem getByMchId(String mchId) {
        return list.stream().filter(it -> StringUtils.equals(it.getWxMchId(), mchId)).findFirst().orElse(null);
    }

    public MallItem getByAliMallId(String aliMallId) {
        return list.stream().filter(it -> StringUtils.isNotBlank(it.getAliMallId())).filter(it -> it.getAliMallId().contains(aliMallId)).findFirst().orElse(null);
    }

    public MallItem getByAbb(String abb) {
        return list.stream().filter(it -> StringUtils.isNotBlank(it.getAliMallId())).filter(it -> StringUtils.equals(it.getAbbreviation(), abb)).findFirst().orElse(null);
    }

    public boolean isHkc(String groupId) {
        if (StringUtils.isBlank(groupId)) {
            return false;
        }
        return list.stream().filter(item -> StringUtils.equals(item.getGroupId(), groupId))
                .anyMatch(item -> StringUtils.equals(item.getAbbreviation(), "HKC"));
    }

    /**
     * 用户判断当前品牌是否属于crm系统
     * @param groupId
     * @return
     */
    public boolean isCrmGroupId(String groupId) {
        return list.stream().anyMatch(it -> StringUtils.equals(it.getGroupId(), groupId));
    }

    public boolean isKO(String groupId) {
        return list.stream().anyMatch(it -> (StringUtils.equals(it.getGroupId(), groupId) &&
                CharSequenceUtil.split(IntegralConstant.DEFAULT_KO, CommonSeparators.COMMA_SEPARATOR).contains(it.getAbbreviation())));
    }

    /**
     * 是否是测试楼盘
     * @param groupId
     * @return
     */
    public boolean isQats(String groupId) {
        return list.stream().filter(item -> StringUtils.equals(item.getGroupId(), groupId)).anyMatch(it -> (StringUtils.equalsIgnoreCase(it.getAbbreviation(), "QATS")));
    }

    public boolean isAllowNegativePointsGroup(String groupId) {
        return list.stream().anyMatch(it -> CharSequenceUtil.split(negativeMallGroup, CommonSeparators.COMMA_SEPARATOR).contains(groupId));
    }

    /**
     * 通过楼盘id查询商场信息
     * @param projectId
     * @return
     */
    public MallItem getByProjectId(String projectId) {
        if (StringUtils.isBlank(projectId)) {
            return null;
        }
        // 楼盘id以英文逗号分开
        List<String> projectIds = CharSequenceUtil.split(projectId, CommonSeparators.COMMA_SEPARATOR);
        return list.stream().filter(item -> StringUtils.isNotBlank(item.getProjectId()))
                .filter(item -> CharSequenceUtil.split(item.getProjectId(), CommonSeparators.COMMA_SEPARATOR).stream().anyMatch(projectIds::contains)).findFirst().orElse(null);
    }

    public List<MallItem> getByLbsIds(String lbsIds) {
        if (StringUtils.isBlank(lbsIds)) {
            return Collections.emptyList();
        }
        return list.stream().filter(it -> lbsIds.contains(it.getMallId())).collect(Collectors.toList());
    }

    /**
     * 获取小程序AppId列表
     * @return
     */
    public List<String> getAppIdList() {
        return list.stream().map(MallItem::getAppId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    public boolean isTestGroup(String groupId) {
        if (StringUtils.isBlank(groupId)) {
            return Boolean.FALSE;
        }
        return list.stream().anyMatch(it -> (StringUtils.equals(it.getGroupId(), groupId) &&
                StringUtils.equals(IntegralConstant.TEST_GROUP, it.getAbbreviation())));
    }

    public boolean isBkc(String groupId) {
        return list.stream().anyMatch(it -> (StringUtils.equals(it.getGroupId(), groupId) &&
                StringUtils.equals("BKC", it.getAbbreviation())));
    }

    /**
     * 手动调整的会员等级，升级金额开始区间排除的group
     * @param groupId
     * @return
     */
    public boolean noEffectGroups(String groupId) {
        return list.stream().filter(item -> StringUtils.equals(item.getGroupId(), groupId))
                .anyMatch(item -> CharSequenceUtil.split(IntegralConstant.NO_EFFECT_GROUPS, CommonSeparators.COMMA_SEPARATOR).contains(item.getAbbreviation()));
    }

    /**
     * 判断groupId是否是KO大group
     * @param groupId
     * @return
     */
    public boolean isKoBigGroup(String groupId) {
        return StringUtils.isNotBlank(this.koBigGroup) && CharSequenceUtil.split(this.koBigGroup, CommonSeparators.COMMA_SEPARATOR).contains(groupId);
    }

    public List<String> getKoBigGroupIds() {
        if (StringUtils.isBlank(this.koBigGroup)) {
            return Collections.emptyList();
        }
        return CharSequenceUtil.split(this.koBigGroup, CommonSeparators.COMMA_SEPARATOR);
    }

}
