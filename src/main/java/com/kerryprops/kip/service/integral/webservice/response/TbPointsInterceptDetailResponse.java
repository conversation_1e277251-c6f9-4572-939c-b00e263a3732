package com.kerryprops.kip.service.integral.webservice.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/28/2022 15:34
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "积分拦截信息")
public class TbPointsInterceptDetailResponse implements Serializable {

    @Schema( name = "主键ID")
    private String id;

    @Schema( name = "集团id")
    private String groupId;

    @Schema( name = "商场id")
    private String mallId;

    @Schema( name = "手机号")
    private String mobile;

    @Schema( name = "会员信息id")
    private String kipUserId;

    @Schema( name = "销售id")
    private String saleId;

    @Schema( name = "销售单号流水号")
    private String orderNo;

    @Schema( name = "销售金额")
    private BigDecimal payAmount;

    @Schema( name = "销售时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date saleDate;

    @Schema( name = "退货状态,详见PointsInterceptStatusEnum")
    private Integer status;

    @Schema( name = "异常原因")
    private String interceptReason;

    @Schema( name = "驳回原因")
    private String rejectReason;
    
    @Schema( name = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @Schema( name = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    @Schema( name = "创建者")
    private String createUser;

    @Schema( name = "更新人")
    private String updateUser;

    @Schema( name = "店铺号")
    private String shopNo;

    @Schema( name = "店铺名")
    private String shopName;

    @Schema( name = "店铺别名")
    private String shopAliasName;

    @Schema( name = "品牌")
    private String retailBrandName;

    @Schema( name = "产生积分")
    private Integer salePoints;

    @Schema( name = "会员号")
    private String vipcode;

    @Schema( name = "会员等级")
    private String grade;

    @Schema( name = "会员等级名称")
    private String gradeName;

    @Schema( name = "积分数")
    private Integer currentPoints;

    @Schema( name = "入会时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    @Schema( name = "会员名")
    private String nickName;

    @Schema( name = "商场名")
    private String mallName;

    @Schema( name = "销售类型1.CRM销售,2.POS销售,3.微信照片,4.微信扫码,5.口碑销售")
    private String saleType;

    @Schema( name = "销售类型1.CRM销售,2.POS销售,3.微信照片,4.微信扫码,5.口碑销售")
    private String saleTypeName;

    @Schema( name = "销售类型对应remark")
    private String remark;
}
