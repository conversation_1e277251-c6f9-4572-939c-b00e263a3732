package com.kerryprops.kip.service.integral.webservice.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/07/2023 10:45
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "海鼎返回信息")
public class HaiDingResult<T> implements Serializable {

    private int code = 0;

    private String msg = "success";

    private T data;

    public HaiDingResult<T> error(String msg) {
        this.code = 500;
        this.msg = msg;
        return this;
    }
}
