package com.kerryprops.kip.service.integral.common.enums;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 06/14/2024 08:58
 **********************************************************************************************************************/

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * CRM Admin端自助积分功能位配置
 */

@Getter
public enum AutoPointsEnum {

    QR("qr", "扫码积分"),
    PHOTO("photo", "拍照积分"),
    WXPAY("wxpay", "微信无感积分"),
    ALIPAY("alipay", "支付宝无感积分");

    private final String val;
    private final String desc;

    AutoPointsEnum(String val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    /**
     * 是否包含微信无感积分
     * @param val
     * @return
     */
    public static boolean containWxPay(String val) {
        if (StringUtils.isBlank(val)) {
            return false;
        }
        return val.contains(WXPAY.getVal());
    }

    /**
     * 是否包含微信无感积分
     * @param val
     * @return
     */
    public static boolean isWxPay(String val) {
        if (StringUtils.isBlank(val)) {
            return false;
        }
        return WXPAY.getVal().equalsIgnoreCase(val);
    }

}
