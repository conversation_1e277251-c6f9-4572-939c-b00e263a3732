package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/21/2022 12:08
 **********************************************************************************************************************/
public interface RabbitMqService {

    void sendMessage(String routeKey, String msg);

    /**
     * 发送topic消息
     * @param routeKey
     * @param msg
     */
    void sendTopicMessage(String routeKey, String msg);

    /**
     * 发送延时消息，time为秒
     * @param routeKey
     * @param msg
     * @param time
     */
    void sendLazyMessage(String routeKey, String msg, int time);

    /**
     * 会员新注册事件
     * @param member
     * @param member
     */
    void sendMemberNewMsg(TbMemberAsset member, boolean isInvitation);

    /**
     * 会员新增销售记录:
     * 1、清除缓存的会员升级进度条金额
     * 2、发送会员消费金额是否升级的逻辑计算
     * 3、退款满赠或会员邀约奖励发放(消费的已从: XCRM_MEMBER_GRADE 内发送了)
     * @param detail
     * @param dto
     */
    void sendMqMessage(TbSalesDetail detail, SalesAutoPointsDto dto);

    /**
     * 满赠及会员邀约活动及停车券活动
     * @param detail detail
     * @param dto dto
     * @param fromRefund true：来自退款 false：自动积分
     */
    void sendFullActivityReward(TbSalesDetail detail, SalesAutoPointsDto dto, Boolean fromRefund);

    /**
     * 发送广播消息
     * @param msg
     */
    void sendFanoutMessage(String exchange, String msg, String routeKey);

}