package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member_card_open_record
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_open_card_record")
public class TbMemberCardOpenRecord implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场编号
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * open id
     */
    @TableField("open_id")
    private String openId;

    /**
     * kip系统中用户唯一id
     */
    @TableField("kip_user_id")
    private String kipUserId;

    /**
     * 会员卡号ID
     */
    @TableField("card_id")
    private String cardId;

    /**
     * 会员卡号
     */
    @TableField("card_no")
    private String cardNo;

    /**
     * 事件类型
     */
    @TableField("event_type")
    private String eventType;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    public void fillDefaultVal() {
        if (StringUtils.isBlank(this.groupId)) {
            this.groupId = "";
        }
        if (StringUtils.isBlank(this.mallId)) {
            this.mallId = "";
        }
        if (StringUtils.isBlank(this.openId)) {
            this.openId = "";
        }
        if (StringUtils.isBlank(this.kipUserId)) {
            this.kipUserId = "";
        }
        if (StringUtils.isBlank(this.cardId)) {
            this.cardId = "";
        }
        if (StringUtils.isBlank(this.cardNo)) {
            this.cardNo = "";
        }
        if (StringUtils.isBlank(this.eventType)) {
            this.eventType = "";
        }
    }

}