package com.kerryprops.kip.service.integral.webservice.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema( description = "徽章列表-响应类")
public class BadgeListResponse implements Serializable {

    @Schema( description = "徽章总数")
    private Integer total;

    @Schema( description = "已获得徽章总数")
    private Integer receivedCount;

    @Schema( description = "徽章列表")
    private List<TbBadgeWithCategoryResponse> badges;

}
