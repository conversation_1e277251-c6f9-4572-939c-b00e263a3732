package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/21/2024 11:26
 **********************************************************************************************************************/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "积分抵现前端请求金额响应类")
public class PointsRedemptionTotalResponse implements Serializable {

    @Schema(description = "是否存在有效的积分抵现活动：true:存在；false:不存在")
    private Boolean hasValidActivity;

    @Schema(description = "最大抵金额")
    private BigDecimal maxAmount;

    @Schema(description = "最大抵扣积分")
    private Integer maxPoints;

    @Schema(description = "其他金额展示")
    private List<RedemptionItemResponse> itemList;

    @Schema(description = "店铺设置的金额比例")
    private BigDecimal money;

    @Schema(description = "店铺设置的积分比例")
    private BigDecimal pointNum;

}
