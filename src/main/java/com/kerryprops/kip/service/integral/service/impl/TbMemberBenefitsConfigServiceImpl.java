package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.entity.TbMemberBenefitConfig;
import com.kerryprops.kip.service.integral.mapper.TbMemberBenefitConfigMapper;
import com.kerryprops.kip.service.integral.service.TbMemberBenefitsConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/08/2023 11:07
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbMemberBenefitsConfigServiceImpl implements TbMemberBenefitsConfigService {

    private final TbMemberBenefitConfigMapper tbMemberBenefitsConfigMapper;

    @Override
    public List<TbMemberBenefitConfig> findBenefitConfigByByMallIdAndGrade(String mallId, String grade) {
        return tbMemberBenefitsConfigMapper.findByMallIdAndMemberGrade(mallId, grade);
    }

}
