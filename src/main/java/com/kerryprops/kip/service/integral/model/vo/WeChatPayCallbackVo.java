package com.kerryprops.kip.service.integral.model.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 微信支付回调vo
 * @createDate 2022/9/7
 * @updateDate 2022/9/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeChatPayCallbackVo {

    /**
     * 错误码，SUCCESS为接收成功，其他错误码为失败。
     */
    @Schema(name = "错误码，SUCCESS为接收成功，其他错误码为失败。")
    private String code;

    /**
     * 返回信息，如非空，为错误原因。
     */
    @Schema(name = "返回信息，如非空，为错误原因。")
    private String message;
}
