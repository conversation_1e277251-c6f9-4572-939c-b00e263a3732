//package com.kerryprops.kip.service.integral.service;
//
//import com.kerryprops.kip.service.integral.entity.TbEmailTemplateConfig;
//import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
//import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
//import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 03/06/2023 15:13
// **********************************************************************************************************************/
//public interface TbEmailSendRecordService {
//
//    void saveRecord(SendMessageDto dto, TbEmailTemplateConfig config, KerryResultDto result, TbMemberAsset member);
//
//}
