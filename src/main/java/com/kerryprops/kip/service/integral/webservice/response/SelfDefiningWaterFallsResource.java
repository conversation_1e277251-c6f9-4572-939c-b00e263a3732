package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * 自定义页面瀑布流tab资源表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2021-11-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "自定义页面瀑布流资源表")
public class SelfDefiningWaterFallsResource implements Serializable {

	@Schema(name = "主键")
	private String id;

	@Schema(name = "集团ID")
	private String groupId;

	@Schema(name = "商场编号")
	private String mallId;

	@Schema(name = "模块id")
	private String moduleId;

	@Schema(name = "tab中文名称")
	private String tabChName;

	@Schema(name = "tab英文名称")
	private String tabEnName;

	@Schema(name = "排序")
	private Integer sort;

	@Schema(name = "选中图标url路径")
	private String selectedUrl;

	@Schema(name = "未选中图标url路径")
	private String unselectedUrl;

	@Schema(name = "内容链接名称")
	private String contentLinkName;

	@Schema(name = "链接类型：0活动 1积分商城")
	private String contentLinkType;

	@Schema(name = "内容最多展示个数")
	private Integer showNum;

	@Schema(name = "更多跳转url路径")
	private String moreUrl;

}