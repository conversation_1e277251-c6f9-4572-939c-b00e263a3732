package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/07/2022 12:36
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员积分明细")
public class PageIntegralDetailResponse implements Serializable {

    @Schema( description = "积分记录明细")
    private List<MemberMonthlyIntegralResponse> content;

    /**
     * 当前积分数
     */
    @Schema( description = "会员当前积分数，仅支付宝会员卡积分明细页面有值")
    private Integer currentIntegral;

    private int totalElements;

    private int totalPages;

}
