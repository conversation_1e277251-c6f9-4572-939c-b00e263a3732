package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VirtualPhoneResponseDto implements Serializable {

    private String phoneNumber;

    /**
     * 风险等级
     *  low, high, unknown
     */
    private String riskLevel;

    /**
     * 风险类型
     * fraud, none
     */
    private List<String> riskTypes;

    private String lastUpdated;

}
