package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 01/19/2023 11:56
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员升级金额响应类")
public class MemberUpgradeAmountResource implements Serializable {

    @Schema( description = "是否是最高卡等，true/false")
    private Boolean topGrade;

    @Schema( description = "还需多少金额才能升级，0:最高卡等保级成功，其他：金额")
    private int amount;

    @Schema( description = "等级有效期")
    private String gradeExpire;

    @Schema( description = "是否开启保级降级功能，true: 开启， false: 未开启")
    private Boolean enableDownGrade;

    @Schema( description = "进度条长度")
    private Integer progressBar;

}
