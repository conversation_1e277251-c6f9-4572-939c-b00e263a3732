package com.kerryprops.kip.service.integral.config;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class OkHttpLoggingInterceptor implements Interceptor {

    public static final long BYTE_COUNT = (long) 1024 * 1024;

    @Override
    public Response intercept(Chain chain) throws IOException {
        //这个chain里面包含了request和response，所以你要什么都可以从这里拿
        Request request = chain.request();

        long t1 = System.nanoTime();
        log.info(String.format("发送请求 %s on %s%n%s", request.url(), chain.connection(), request.headers()));
        log.info("请求参数: {}", this.bodyToString(request));
        Response response = chain.proceed(request);
        long t2 = System.nanoTime();

        ResponseBody responseBody = response.peekBody(BYTE_COUNT);

        log.info(String.format("接收响应: [%s] %n返回json:【%s】 %.1fms%n%s", response.request().url(),
                responseBody.string(), (t2 - t1) / 1e6d, response.headers()));
        return response;
    }

    /**
     * 获取请求参数
     * @param request
     * @return
     */
    private String bodyToString(final Request request){
        final Request copy = request.newBuilder().build();
        try (Buffer buffer = new Buffer()) {
            if (Objects.nonNull(copy.body())) {
                copy.body().writeTo(buffer);
            }
            return buffer.readUtf8();
        } catch (Exception e) {
            e.printStackTrace();
            return "error";
        }
    }

}
