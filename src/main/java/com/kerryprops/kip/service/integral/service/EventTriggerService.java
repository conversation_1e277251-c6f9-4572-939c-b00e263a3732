package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.client.EventTriggerClient;
import com.kerryprops.kip.service.integral.model.dto.PerfectMemberActivityDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/28/2023 15:43
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class EventTriggerService {

    private final EventTriggerClient eventTriggerClient;

    @Async
    public CompletableFuture<PerfectMemberActivityDto> obtainRewardForInfo(String groupId, String mallId) {
        PerfectMemberActivityDto activityDto = null;
        try {
            activityDto = eventTriggerClient.getPerfectMemberInfo(groupId, mallId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return CompletableFuture.completedFuture(activityDto);
    }

}
