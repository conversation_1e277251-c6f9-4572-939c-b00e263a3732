package com.kerryprops.kip.service.integral.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 02/08/2025 17:19
 **********************************************************************************************************************/

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OcrQrCodeUtils {

    /**
     * OCR嘉里生成唯一的二维码-8位
     * 前2位：年份后2位
     * 后6位：随机数，去除o，l易混淆的字符串
     * @return
     */
    public static String simpleQrCode() {
        Date date = new Date();
        String yy = DateUtil.format(date, "yy");
        return yy + RandomUtil.randomString("abcdefghijkmnpqrstuvwxyz0123456789",6);
    }

}
