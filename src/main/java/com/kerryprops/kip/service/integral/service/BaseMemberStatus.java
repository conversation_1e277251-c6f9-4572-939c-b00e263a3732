package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import org.apache.commons.lang3.StringUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/13/2023 17:51
 **********************************************************************************************************************/
public abstract class BaseMemberStatus {

    /**
     * 检查用户状态
     * @param member 会员信息
     */
    public void checkMemberStatus(TbMemberAsset member) {
        // 用户信息不存在
        if (Objects.isNull(member)) {
            throw PointBusinessException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        // 会员冻结判断
        if (StringUtils.equals(member.getStatus(), IntegralConstant.MEMBER_FREEZE_STATUS)) {
            throw PointBusinessException.error(PointsEnum.MEMBER_FREEZE_ERROR);
        }
    }

    /**
     * 获取头部参数
     * @param request request
     * @param parameter parameter
     * @return str
     */
    public String obtainHeader(HttpServletRequest request, String parameter) {
        String result =  request.getHeader(parameter);
        return StringUtils.isBlank(result) ? null : result;
    }

}
