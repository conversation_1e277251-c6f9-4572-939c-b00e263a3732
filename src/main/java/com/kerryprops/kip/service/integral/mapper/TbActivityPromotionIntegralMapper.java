package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionIntegral;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbActivityPromotionIntegralMapper extends BaseMapper<TbActivityPromotionIntegral> {

    void saveBatch(@Param("list") List<TbActivityPromotionIntegral> list);

    /**
     * 通过积分调整记录id，查询匹配到的营销积分活动
     * @param adjustIds
     * @return
     */
    List<TbActivityPromotionIntegral> getMatchedPromotions(@Param("adjustIds") List<String> adjustIds);

    List<TbActivityPromotionIntegral> getListByVipCode(@Param("vipCode") String vipCode);

    /**
     * 查询销售单号匹配的营销积分活动
     * @param vipcode
     * @param sellNo
     * @return
     */
    List<TbActivityPromotionIntegral> getSaleMatchedActivityPromotions(@Param("vipcode") String vipcode, @Param("sellNo") String sellNo);

}