package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/20/2022 14:00
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员已绑定的二级分类标签类")
public class MemberBoundTagResponse implements Serializable {

    @Schema( description = "二级分类ID")
    private String secSortId;

    @Schema( description = "该二级分类下绑定的标签列表")
    private List<TbAutoSaleMemberTagResponse> tagList;
    
}
