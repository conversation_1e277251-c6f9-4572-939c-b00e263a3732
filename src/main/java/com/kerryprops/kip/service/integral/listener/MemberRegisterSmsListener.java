package com.kerryprops.kip.service.integral.listener;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.MessageTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.model.dto.MemberIdDto;
import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.service.TemplateMessageSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC: 会员注册短信发送监听QUEUE
 * <AUTHOR> Bert
 * Created Date - 03/21/2023 17:50
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class MemberRegisterSmsListener {

    private final TemplateMessageSendService templateMessageSendService;
    private final RabbitMqService rabbitMqService;

    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.XCRM_MEMBER_REGISTER_SMS_NOTIFY}, containerFactory = "crmContainerFactory")
    public void sendMessage(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("会员注册短信提醒消息体为空.");
            return;
        }
        log.info("MemberRegisterSmsListener: {}" , msg);
        MemberIdDto dto = JsonUtils.stringToObj(msg, MemberIdDto.class);
        if (Objects.isNull(dto)) {
            return;
        }
        // 用户注册次日短信提醒
        Date date = new Date();
        // 两个日期相差多少秒
        int second = (int) DateUtil.between(date, this.getNextDayTenClock(date), DateUnit.SECOND);
        log.info("XCRM_SIGN_NEXT_DAY_NOTIFICATION: {}-{}", msg, second);
        MemberIdDto nextDayDto = MemberIdDto.builder().id(dto.getId()).messageType(MessageTypeEnum.NEXT_DAY.getType()).build();
        rabbitMqService.sendLazyMessage(RabbitMqConstant.XCRM_SIGN_NEXT_DAY_NOTIFICATION, JsonUtils.objToString(nextDayDto), second);

        // 用户注册短信提醒
        SendMessageDto messageDto = SendMessageDto.builder().memberId(dto.getId()).templateType(1)
                .messageType(dto.getMessageType()).build();
        templateMessageSendService.sendMessage(messageDto);
    }

    /**
     * 获取次日上午10点短信提醒
     * @return
     */
    private Date getNextDayTenClock(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 10);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

}
