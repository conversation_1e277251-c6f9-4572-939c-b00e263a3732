package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/06/2023 16:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "自助积分配置响应类")
public class TbAutoPointsConfigResponse implements Serializable {

    @Schema( description = "主键id, 更新时必填")
    private Long id;

    @Schema( description = "业态")
    private String format;

    @Schema( description = "groupId")
    private String groupId;

    @Schema( description = "lbsId")
    private String lbsId;

    @Schema( description = "楼盘id")
    private String projectId;

    @Schema( description = "lbs名称")
    private String lbsName;

    @Schema( description = "可使用的积分方式")
    private String code;

    @Schema( description = "创建时间")
    @JsonFormat(timezone="GMT+8", pattern="yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date createDate;

    @Schema( description = "创建人")
    private String creator;

    @Schema( description = "修改时间")
    @JsonFormat(timezone="GMT+8", pattern="yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date updateDate;

    @Schema( description = "更新人")
    private String updater;

}
