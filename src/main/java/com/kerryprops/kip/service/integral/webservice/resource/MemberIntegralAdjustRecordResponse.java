package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/06/2022 17:10
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "积分明细响应类")
public class MemberIntegralAdjustRecordResponse implements Serializable {

    @Schema( description = "调整记录ID")
    private String id;

    @Schema( description = "集团ID")
    private String groupId;

    @Schema( description = "调整类型 A->普通调整， S->销售调整")
    private String type;

    @Schema( description = "商场ID")
    private String mallId;

    @Schema( description = "商场名称")
    private String mallName;

    @Schema( description = "会员编号")
    private String vipcode;

    @Schema( description = "当前选中会员积分")
    private Integer currentIntegral;

    @Schema( description = "调整积分数量")
    private Integer number;

    @Schema( description = "积分调整枚举值")
    private String remark;

    @Schema( description = "积分调整枚举值名称")
    private String remarkName;

    @Schema( description = "没有值为积分不过期(普通调整)")
    private String integralDead;

    @Schema( description = "销售单号(销售调整)")
    private String sellNo;

    @Schema( description = "店铺号")
    private String shopId;

    @Schema( description = "店铺名称")
    private String shopName;

    @Schema( description = "消费金额")
    private BigDecimal useMoney;

    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema( description = "销售日期(销售调整)")
    private Date sellDate;

    @Schema( description = "额外积分")
    private String extraIntegral;

    @Schema( description = "销售金额对应的积分")
    private Integer numberToIntegral;

    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema( description = "调整时间")
    private Date createDate;

    @Schema( description = "年月")
    private String moon;

}
