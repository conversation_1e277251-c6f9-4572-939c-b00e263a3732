package com.kerryprops.kip.service.integral.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.vo.TenantAllVo;
import com.kerryprops.kip.service.integral.model.vo.TenantFloorVo;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.annotation.Transient;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * tb_base_shop
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbBaseShop implements Serializable {

	/**
	 * id
	 */
	private String id;
	/**
	 * 合同编号(CRM使用的店铺编号)
	 */
	private String contractNo;

	/**
	 * 集团id
	 */
	private String groupId;
    /**
     * 商场编号
     */
	private String mallId;
    /**
     * 商场名称
     */
	private String marketName;
    /**
     * 楼栋id
     */
	private Long buildingId;
    /**
     * 楼层编号
     */
	private String floorNo;
    /**
     * 楼层名
     */
	private String floorName;
    /**
     * 一级业态编码
     */
	private String firstFormatCode;
    /**
     * 一级业态名称
     */
	private String firstFormatName;
    /**
     * 二级业态编码
     */
	private String secondFormatCode;
    /**
     * 二级业态名称
     */
	private String secondFormatName;
    /**
     * 三级业态编码
     */
	private String thirdFormatCode;
    /**
     * 三级业态名称
     */
	private String thirdFormatName;
    /**
     * 店铺状态0生效1失效
     */
	private String shopStatus;
    /**
     * 店铺名
     */
	private String shopName;
    /**
     * 经营品牌
     */
	private String brand;
    /**
     * 品牌名
     */
	private String brandName;

	/**
	 * 业态品牌名称
	 */
	private String retailBrandName;

    /**
     * 删除状态 0未删除1删除
     */
	private String delFlag;
	/**
     * 有效期开始时间
     */
	private Date beginDate;
    /**
     * 有效期结束时间
     */
	private Date endDate;
    /**
     * 是否长期有效（1、是，0、否）
     */
	private String isEnable;
	/**
     * 是否入爱闪购商城(0 : 未入驻，1：入驻)
     */
	private Integer isEnterShop;
	/**
	 * 店铺编号
	 */
	private String shopCode;
	/**
	 * 店铺类型
	 */
	private String shopType;
	/**
	 * 店铺状态(40为合同终止)
	 */
	private String state;
	/**
     * 是否入驻POS(0 : 未入驻，1：入驻)
     */
	private Integer isEnterPos;

	/**
	 * 密码
	 */
	private String password;
	/**
	 * hive店铺编号
	 */
	private String hiveShopCode;
	/**
	 * ali店铺编号
	 */
	private String aliShopCode;
	/**
	 * 商城编码
	 */
	private String mallCode;
	/**
	 * 创建者
	 */
	private Long creator;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	private Date updateDate;

	/**
	 * 店铺别名
	 */
	@Transient
	private String shopAliasName;

	public static TbBaseShop getTbBaseShop(TenantAllVo allVo, MallItem mallItem) {
		TenantInfoVo infoVo = allVo.getTenant();
		TbBaseShop shop = TbBaseShop.builder().id(infoVo.getId()).brandName(infoVo.getRetailBrandName()).contractNo(infoVo.getContractNo())
				.shopCode(infoVo.getId()).shopName(infoVo.getBrandName()).brand(infoVo.getRetailBrandId()).firstFormatCode(infoVo.getFirstFormatCode())
				.firstFormatName(infoVo.getFirstFormatName()).secondFormatCode(infoVo.getSecondFormatCode()).secondFormatName(infoVo.getSecondFormatName())
				.thirdFormatCode(infoVo.getThirdFormatCode()).thirdFormatName(infoVo.getThirdFormatName()).state(infoVo.getStatus()).shopStatus("0")
				.shopType(infoVo.getShopType()).retailBrandName(infoVo.getRetailBrandName()).delFlag("0").shopAliasName(infoVo.getShopName()).build();
		if (Objects.nonNull(mallItem)) {
			shop.setMallId(mallItem.getMallId());
			shop.setGroupId(mallItem.getGroupId());
			shop.setMallCode(mallItem.getAbbreviation());
		}
		// 租户合同信息
		if (CollectionUtils.isNotEmpty(infoVo.getDoCoSet())) {
			shop.setHiveShopCode(StrUtil.join(CommonSeparators.COMMA_SEPARATOR, infoVo.getDoCoSet()));
		}
		// 楼层信息
		if (CollectionUtils.isNotEmpty(allVo.getFloors())) {
			shop.setFloorNo(allVo.getFloors().stream().map(TenantFloorVo::getName).collect(Collectors.joining(CommonSeparators.SEMICOLON)));
			shop.setFloorName(allVo.getFloors().stream().map(it -> String.format("商场/%s", it.getName())).collect(Collectors.joining(CommonSeparators.SEMICOLON)));
		}
		return shop;
	}

}