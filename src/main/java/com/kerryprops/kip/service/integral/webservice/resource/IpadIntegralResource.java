package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 05/31/2023 13:43
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IpadIntegralResource implements Serializable {

    @Schema( description = "会员id")
    private String id;

    @Schema( description = "会员号")
    private String vipcode;

    @NotBlank(message = "shopNo不能为空")
    @Schema( description = "店铺编号")
    private String shopNo;

    @Schema( description = "消费金额")
    private BigDecimal useMoney;

    @Schema( description = "优惠金额")
    private BigDecimal discounts;

    @NotBlank(message = "销售日期不能为空")
    @Schema( description = "销售日期")
    private String sellDate;

    @NotBlank(message = "调整原因不能为空")
    @Schema( description = "调整原因")
    private String remark;

    @NotBlank(message = "groupId不能为空")
    @Schema( description = "集团id")
    private String groupId;

    @NotBlank(message = "mallId不能为空")
    @Schema( description = "商场id")
    private String mallId;

}
