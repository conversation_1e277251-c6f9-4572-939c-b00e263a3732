package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 12/14/2023 09:19
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "BKC迁移弹窗确认请求类")
public class BkcMigrationConfirmResource implements Serializable {

    @NotNull(message = "确认状态不能为空.")
    @Schema( description = "确认状态: 0:未确认或取消, 1:已确认")
    private Integer confirmStatus;

}
