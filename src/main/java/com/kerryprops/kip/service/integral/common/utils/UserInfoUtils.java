package com.kerryprops.kip.service.integral.common.utils;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.net.URLDecoder;
import java.util.Objects;
import java.util.Optional;

/***********************************************************************************************************************
 * Project - decoration-review-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - Bert
 * Created Date - 06/28/2021 16:08
 **********************************************************************************************************************/

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UserInfoUtils {

    private static ThreadLocal<LoginUser> userLocal = new TransmittableThreadLocal();

    public static void setUser(String userString, String brandId, String lbsId, String appId) {
        LoginUser loginUser = JsonUtils.stringToObj(userString, LoginUser.class);
        if (Objects.nonNull(loginUser)) {
            loginUser.setBrandId(brandId);
            loginUser.setLbsId(lbsId);
            loginUser.setAppId(StringUtils.isBlank(appId) ? "KIP-APP" : appId);
        }
        // 反编译昵称
        loginUser.setNickName(decodeNickName(loginUser.getNickName()));
        userLocal.set(loginUser);
    }

    private static String decodeNickName(String nickName) {
        if (StringUtils.isBlank(nickName)) {
            return "";
        }
        try {
            nickName = URLDecoder.decode(nickName, IntegralConstant.DEFAULT_ENC).replaceAll("\\+", " ");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return nickName;
    }

    public static void removeUser() {
        userLocal.remove();
    }

    public static LoginUser getUser() {
        return userLocal.get();
    }

    public static String getUserId() {
        return Optional.ofNullable(userLocal.get()).map(LoginUser::getCId).orElse(null);
    }

    public static String getFromType() {
        return Optional.ofNullable(userLocal.get()).map(LoginUser::getFromType).orElse("");
    }

}
