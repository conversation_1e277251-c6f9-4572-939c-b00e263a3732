package com.kerryprops.kip.service.integral.service.promotion.impl;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC 生日当天
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:23
 **********************************************************************************************************************/

@Slf4j
@Component
public class BirthdayPromotionRule implements PromotionRule {

    private static final String MONTH_DAY = "MM-dd";

    @Override
    public String getRuleType() {
        return "2";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        TbMemberAsset member = dto.getMember();
        // 2:生日当天
        if (Objects.isNull(member.getBirthDate())) {
            return false;
        }
        // 生日月份+日期
        String birthday = DateUtil.format(member.getBirthDate(), MONTH_DAY);
        // 销售记录月份+日期
        String currentDay = DateUtil.format(saleDate, MONTH_DAY);
        return StringUtils.equals(birthday, currentDay);
    }
}
