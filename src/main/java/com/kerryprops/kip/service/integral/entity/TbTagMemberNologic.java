package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员无逻辑标签数据表
 * @TableName tb_tag_member_nologic
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_tag_member_no_logic")
public class TbTagMemberNologic implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 集团编号
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场号
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 会员号
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * 会员无逻辑标签组
     */
    @TableField("tag_ids")
    private String tagIds;

    /**
     * 逻辑标签
     */
    @TableField("lables")
    private  String lables;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

}