package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbMemberAsset;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/28/2022 16:14
 **********************************************************************************************************************/
public interface MemberRegisterService {

    int saveMember(TbMemberAsset member);

    void fillMemberKipUserId(TbMemberAsset member, String kipUserId);

    /**
     * 查询会员信息带缓存
     * @param groupId
     * @param kipUserId
     * @return
     */
    TbMemberAsset findByGroupIdAndKipUserId(String groupId, String kipUserId);

    /**
     * 通过kipUserId查询是否存在账号信息，用于crm端账号删除时，检查CRM内是否存在其他账号
     * @param kipUserId
     * @return
     */
    List<TbMemberAsset> findByKipUserId(String kipUserId);

    /**
     * 查询会员信息带缓存
     * @param groupId
     * @param kipUserId
     * @return
     */
    TbMemberAsset queryByGroupIdAndKipUserId(String groupId, String kipUserId);

    /**
     * 查询会员信息带缓存
     * @param groupId
     * @param mobile
     * @return
     */
    TbMemberAsset queryByGroupIdAndMobile(String groupId, String mobile);

    /**
     * 查询会员信息带缓存
     * @param groupId
     * @param vipcode
     * @return
     */
    TbMemberAsset queryByGroupIdAndVipcode(String groupId, String vipcode);

    /**
     * 通过groupId+vipcode反查用户的kipUserId
     * @param groupId
     * @param vipcode
     * @return
     */
    String findKipUserIdByGroupIdAndVipcode(String groupId, String vipcode);

    /**
     * 删除缓存的会员信息
     * @param groupId
     * @param kipUserId
     */
    void removeCacheMember(String groupId, String kipUserId);

}
