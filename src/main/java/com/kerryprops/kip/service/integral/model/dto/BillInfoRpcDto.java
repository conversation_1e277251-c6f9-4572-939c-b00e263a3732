package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName BillInfoRpcDto
 * @Description 账单信息
 * @date 2022/10/25 15:10
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillInfoRpcDto implements Serializable {

    private String rescode;

    private String resmsg;

    private String uuid;

    private Double amount;

    private String billserialnumber;

    private String merchantCode;

    private String merchantName;

    private String sign;

    private String saleTime;

}
