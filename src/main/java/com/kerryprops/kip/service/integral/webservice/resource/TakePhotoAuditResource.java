package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/20/2023 13:44
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "拍照积分审核请求类")
public class TakePhotoAuditResource implements Serializable {

    @NotBlank(message = "拍照记录id不能为空")
    @Schema( description = "主键ID")
    private String id;

    @Schema( description = "商场ID")
    private String mallId;

    @NotBlank(message = "审核状态不能为空")
    @Schema( description = "审核状态: PhotoReviewStatusEnum")
    private String state;

    @Schema( description = "销售单号")
    private String sellNo;

    @Schema( description = "店铺单号")
    private String shopNo;

    @Schema( description = "金额")
    private BigDecimal money;

    @Schema( description = "销售时间，格式：yyyy-MM-dd hh:mm:ss")
    private String sellDate;

    @Schema( description = "审核不通过原因")
    private String reason;

    @Schema( description = "审核人")
    private String auditor;

    @Schema( description = "是否支持重复录入，1:支持，0:不支持")
    private String approved;

}
