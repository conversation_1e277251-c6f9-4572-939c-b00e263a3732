package com.kerryprops.kip.service.integral.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/26/2022 16:14
 **********************************************************************************************************************/

@Data
@Component
@ConfigurationProperties(prefix = "smart-pos")
public class SmartPosProperties {

    private String url;

    private String appKey;

    private String appSecret;

    private String messageFormat;

    private String method;

    private String version;

    private String signMethod;

}
