package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
import com.kerryprops.kip.service.integral.service.TemplateMessageSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.*;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/06/2023 14:03
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class PointInterceptReviewListener {

    private final TemplateMessageSendService templateMessageSendService;

    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.REJECT_POINTS_INTERCEPT_RECORD}, containerFactory = "crmContainerFactory")
    public void sendMessage(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("积分拦截驳回信息为空.");
            return;
        }
        log.info("PointInterceptReviewListener: {}", msg);
        SendMessageDto messageDto = JsonUtils.stringToObj(msg, SendMessageDto.class);
        if (Objects.isNull(messageDto)) {
            log.info("积分拦截驳回信息: {}", msg);
            return;
        }
        // 发送短信，模版消息及邮件
        templateMessageSendService.sendMessage(messageDto);
    }

}
