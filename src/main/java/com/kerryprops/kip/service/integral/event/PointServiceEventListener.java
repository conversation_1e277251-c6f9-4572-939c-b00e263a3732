package com.kerryprops.kip.service.integral.event;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.pmw.client.resource.PointsActivationConfirmResource;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.MessageTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbOpenCardFailRecord;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 09:17
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class PointServiceEventListener {

    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private final TbOpenCardFailRecordService tbOpenCardFailRecordService;
    private final TbPhotoReviewService tbPhotoReviewService;
    private final TbMemberAssetService tbMemberAssetService;
    private final RabbitMqService rabbitMqService;
    private final SmsSendService smsSendService;

    @EventListener(value = UserPointAuthorizationEvent.class)
    @Async
    @Order(1)
    public void userPointAuthorizationEvent(UserPointAuthorizationEvent authEvent) {
        if (StringUtils.isBlank(authEvent.getCardNo())) {
            TbOpenCardFailRecord failRecord = tbOpenCardFailRecordService.queryByOpenIdAndMallId(authEvent.getOpenId(), authEvent.getMallId());
            if (Objects.nonNull(failRecord)) {
                PointsActivationConfirmResource.PointsActivationDetailResource body = JsonUtils.stringToObj(failRecord.getContent(), PointsActivationConfirmResource.PointsActivationDetailResource.class);
                Optional.ofNullable(body).ifPresent(item -> authEvent.setCardNo(item.getCode()));
            }
        }
        log.info("userPointAuthorizationEvent: [{}]", JsonUtils.objToString(authEvent));
        tbInsensatePointsAuthRecordService.insertJudgment(TbInsensatePointsAuthRecord.builder().kipUserId(authEvent.getKipUserId())
                .openId(authEvent.getOpenId()).groupId(authEvent.getGroupId()).cardNo(authEvent.getCardNo())
                .mallId(authEvent.getMallId()).origin(authEvent.getOrigin()).mchid(authEvent.getMchid()).build());
    }

    @EventListener(value = EmailApproveSendEvent.class)
    @Async
    @Order(1)
    public void sendEmailAfterPointsEvent(EmailApproveSendEvent emailEvent) {
        log.info("sendEmailAfterPointsEvent {}", JsonUtils.objToString(emailEvent));
        smsSendService.sendPointsEmail(emailEvent.getMallId(), emailEvent.getSubscriptionType());
    }

    @EventListener(value = OcrPhotoRejectEvent.class)
    @Async
    @Order(1)
    public void sendOcrPhotoRejectEvent(OcrPhotoRejectEvent event) {
        log.info("sendOcrPhotoRejectEvent {}", JsonUtils.objToString(event));
        TbPhotoReview photoReview = tbPhotoReviewService.getById(event.getId());
        if (Objects.isNull(photoReview)) {
            return;
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(photoReview.getGroupId()).vipcode(photoReview.getVipcode()).build());
        if (Objects.isNull(tbMemberAsset)) {
            return;
        }
        if (!PhotoReviewStatusEnum.TYPE_2.getCode().equals(photoReview.getStatus())) {
            return;
        }
        // 拍照积分-驳回短信发送
        SendMessageDto messageDto = SendMessageDto.builder().memberId(String.valueOf(tbMemberAsset.getId())).dictType(IntegralConstant.TAKE_PHOTO)
                .groupId(photoReview.getGroupId()).mallId(photoReview.getMallId()).messageType(MessageTypeEnum.PHOTO_POINTS_REJECT.getType())
                .templateType(1).templateActivityid(IdUtil.simpleUUID()).updateDate(DateUtil.formatDateTime(photoReview.getUploadDate()))
                .remark(photoReview.getReason()).build();
        rabbitMqService.sendLazyMessage(RabbitMqConstant.PHOTO_POINTS_REJECT_QUEUE, JsonUtils.objToString(messageDto), 3);
    }

    @EventListener(value = FillMemberKipUserIdEvent.class)
    @Async
    @Order(1)
    public void fillMemberKipUserIdEvent(FillMemberKipUserIdEvent event) {
        if (Objects.isNull(event.getMemberId()) || StringUtils.isBlank(event.getKipUserId())) {
            return;
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.getMemberById(event.getMemberId() + "");
        if (Objects.isNull(tbMemberAsset) || (StringUtils.isNotBlank(tbMemberAsset.getKipUserId()) && !"false".equalsIgnoreCase(tbMemberAsset.getKipUserId()))) {
            return;
        }
        tbMemberAsset.setKipUserId(event.getKipUserId());
        tbMemberAssetService.updateById(tbMemberAsset);
    }
}
