package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/08/2024 09:53
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "扫码积分查询请求类")
public class GagQrCodeQueryResource implements Serializable {

    @NotBlank(message = "必填参数不能为空")
    @Schema( description = "二维码内参数")
    private String billFileName;

    @NotBlank(message = "必填参数不能为空")
    @Schema( description = "集团id")
    private String groupId;

    @NotBlank(message = "必填参数不能为空")
    @Schema( description = "商场id")
    private String mallId;

}
