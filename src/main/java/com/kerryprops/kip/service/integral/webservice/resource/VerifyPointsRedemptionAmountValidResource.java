package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/22/2024 12:01
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "校验积分抵现金额是否有效请求类")
public class VerifyPointsRedemptionAmountValidResource implements Serializable {

    @NotBlank(message = "集团id不能为空.")
    @Schema(description = "集团id")
    private String groupId;

    @NotBlank(message = "商场id不能为空.")
    @Schema(description = "商场id")
    private String mallId;

    @NotBlank(message = "kip用户id不能为空.")
    @Schema(description = "kip用户id")
    private String kipUserId;

    @NotBlank(message = "商户id不能为空.")
    @Schema(description = "商户id")
    private String tenantId;

    @NotNull(message = "积分抵现金额不能为空.")
    @Schema(description = "积分抵现金额")
    private BigDecimal pointsRedemptionAmount;

}
