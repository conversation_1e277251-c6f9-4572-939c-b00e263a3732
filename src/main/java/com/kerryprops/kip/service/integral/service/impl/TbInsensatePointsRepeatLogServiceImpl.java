package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsRepeatLog;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.mapper.TbInsensatePointsRepeatLogMapper;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsRepeatLogService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/26/2022 10:43
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbInsensatePointsRepeatLogServiceImpl implements TbInsensatePointsRepeatLogService {

    private final TbInsensatePointsRepeatLogMapper tbInsensatePointsRepeatLogMapper;

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRepeatLog(SalesAutoPointsDto dto, TbSalesDetail detail) {
        TbInsensatePointsRepeatLog log = tbInsensatePointsRepeatLogMapper.getByTransactionIdAndOrigin(dto.getSaleNo(), SaleTypeEnum.WECHAT.getValue().equals(dto.getSaleType()) ? InsensateOriginEnum.WECHAT.getValue() : InsensateOriginEnum.ALIPAY.getValue());
        if (Objects.nonNull(log)) {
            return;
        }
        log = TbInsensatePointsRepeatLog.builder().transactionId(dto.getSaleNo()).repeatSaleId(detail.getCrmId())
                .origin(SaleTypeEnum.WECHAT.getValue().equals(dto.getSaleType()) ? InsensateOriginEnum.WECHAT.getValue() : InsensateOriginEnum.ALIPAY.getValue()).createDate(new Date()).build();
        tbInsensatePointsRepeatLogMapper.insert(log);
    }
}
