package com.kerryprops.kip.service.integral.config;

import com.github.dozermapper.spring.DozerBeanMapperFactoryBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import java.io.IOException;

/**
 * @Author: Bert
 * @Date: 2021/6/1 6:06 下午
 * @Description: default
 */
@Configuration
public class DozerConfig {

    @Bean
    public DozerBeanMapperFactoryBean dozerMapper(@Value("classpath:dozer/*.xml") Resource[] resources) throws IOException {
        DozerBeanMapperFactoryBean factoryBean = new DozerBeanMapperFactoryBean();
        factoryBean.setMappingFiles(resources);
        return factoryBean;
    }

    /**
     * 异常积分拦截邮箱提醒
     * @param resource
     * @return
     */
    @Bean
    public Resource notifyResource(@Value("classpath:emails/notify.html") Resource resource) {
        return resource;
    }

}
