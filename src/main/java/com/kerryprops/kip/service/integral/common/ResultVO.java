package com.kerryprops.kip.service.integral.common;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/08/2024 10:06
 **********************************************************************************************************************/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "定义一统一的响应对象")
public class ResultVO<T> implements Serializable {

    @Schema(name = "响应状态码")
    private Integer code;

    @Schema(name = "响应的消息")
    private String msg;

    @Schema(name = "响应返回数据")
    private T data;

    public static <T> ResultVO success(T data){
        return new ResultVO(0,"成功", data);
    }

    public static <T> ResultVO success(Integer code, T data, String message){
        return new ResultVO(code, message, data);
    }

    public static <T> ResultVO success(){
        return new ResultVO(0,"成功", null);
    }

    public static <T> ResultVO success(Integer code, T data){
        return new ResultVO(code,"成功", data);
    }

    public static <T> ResultVO<T> fail(String message) {
        return new ResultVO<T>(403, message, null);
    }

    public static <T> ResultVO<T> fail(Integer code, String message) {
        return new ResultVO<T>(code, message, null);
    }

}
