package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper;
import com.kerryprops.kip.service.integral.service.MemberRegisterService;
import com.kerryprops.kip.service.integral.service.RedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/28/2022 16:14
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class MemberRegisterServiceImpl implements MemberRegisterService {

    private final TbMemberAssetMapper tbMemberAssetMapper;
    private final RedisService redisService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveMember(TbMemberAsset member) {
        if (StringUtils.isAnyBlank(member.getGroupId(), member.getMobile())) {
            throw BizException.error(PointsEnum.VALIDATION_FAILED);
        }
        if (StringUtils.isBlank(member.getCrmId())) {
            member.setCrmId(IdUtil.simpleUUID());
        }
        return tbMemberAssetMapper.insert(member);
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fillMemberKipUserId(TbMemberAsset member, String kipUserId) {
        if (Objects.isNull(member) || Objects.isNull(member.getId())) {
            return;
        }
        member.setKipUserId(kipUserId);
        tbMemberAssetMapper.fillMemberKipUserId(member);
    }

    @Override
    @Cacheable(value = RedisCacheKey.INDEX_CRM_MEMBER_BY_KIP_USERID, key = "#groupId +':' + #kipUserId", unless = "#result == null")
    public TbMemberAsset findByGroupIdAndKipUserId(String groupId, String kipUserId) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(groupId, kipUserId);
    }

    @Override
    public List<TbMemberAsset> findByKipUserId(String kipUserId) {
        return tbMemberAssetMapper.findByKipUserId(kipUserId);
    }

    @Override
    public TbMemberAsset queryByGroupIdAndKipUserId(String groupId, String kipUserId) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(groupId, kipUserId);
    }

    @Override
    public TbMemberAsset queryByGroupIdAndMobile(String groupId, String mobile) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndMobile(groupId, mobile);
    }

    @Override
    public TbMemberAsset queryByGroupIdAndVipcode(String groupId, String vipcode) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndVipcode(groupId, vipcode);
    }

    @Override
    @Cacheable(value = RedisCacheKey.INDEX_GROUP_VIPCODE_TO_KIP_USERID, key = "#groupId +':' + #vipcode", unless = "#result == null")
    public String findKipUserIdByGroupIdAndVipcode(String groupId, String vipcode) {
        return tbMemberAssetMapper.findKipUserIdByGroupIdAndVipcode(groupId, vipcode);
    }

    @Async
    @Override
    public void removeCacheMember(String groupId, String kipUserId) {
        if (StringUtils.isAnyBlank(groupId, kipUserId)) {
            return;
        }
        log.info("清除会员信息缓存: {}-{}", groupId, kipUserId);
        String memberKey = String.format("%s::%s:%s", RedisCacheKey.INDEX_CRM_MEMBER_BY_KIP_USERID, groupId, kipUserId);
        redisService.delKeys(Collections.singletonList(memberKey));
    }

}
