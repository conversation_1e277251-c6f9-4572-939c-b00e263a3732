package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeMapper;
import com.kerryprops.kip.service.integral.service.TbMemberGradeWithCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/07/2023 09:58
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbMemberGradeWithCacheServiceImpl implements TbMemberGradeWithCacheService {

    private final TbMemberGradeMapper tbMemberGradeMapper;

    @Override
    @Cacheable(value = RedisCacheKey.CRM_MEMBER_GRADE_KEY, key = "#groupId", unless = "#result == null")
    public List<TbMemberGrade> queryGradeSortAscByGroupId(String groupId) {
        return tbMemberGradeMapper.getGradeSortAscByGroupId(groupId);
    }
}
