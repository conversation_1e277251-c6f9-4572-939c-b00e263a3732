package com.kerryprops.kip.service.integral.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.pattern.ConverterKeys;
import org.apache.logging.log4j.core.pattern.LogEventPatternConverter;
import org.apache.logging.log4j.message.Message;
import org.apache.logging.log4j.message.MultiformatMessage;

import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/***********************************************************************************************************************
 * Project - profile-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/31/2021 14:06
 **********************************************************************************************************************/

@Slf4j
@Plugin(name = "LogMaskConverter", category = "Converter")
@ConverterKeys({"m"})
public class LogMaskConverter extends LogEventPatternConverter {

    private List<PatternHolder> patternHolders = new LinkedList<>();
    private String[] formats;
    private static LogMaskConverter logMaskConverter;

    private LogMaskConverter(String[] options) {
        super("m", "m");
        this.formats = options;
        this.init();
    }

    public static synchronized LogMaskConverter newInstance(final String[] options) {
        log.info("create LogMaskConverter Thread: " + Thread.currentThread().getName());
        if (Objects.isNull(LogMaskConverter.logMaskConverter)) {
            LogMaskConverter.logMaskConverter = new LogMaskConverter(options);
        }
        return LogMaskConverter.logMaskConverter;
    }

    @Override
    public void format(LogEvent event, StringBuilder builder) {
        final Message msg = event.getMessage();
        String result = null;
        if (Objects.nonNull(msg)) {
            if (msg instanceof MultiformatMessage) {
                result = ((MultiformatMessage) msg).getFormattedMessage(formats);
            } else {
                result = msg.getFormattedMessage();
            }
        }
        mask(result, builder);
    }

    protected void init() {
        try (Scanner scanner = new Scanner(this.getClass().getResourceAsStream("/log-masking-patterns.txt"))) {
            while(scanner.hasNextLine()) {
                String conf = scanner.nextLine().trim();
                doInit(conf);
            }
        } catch (Exception e) {
            log.error("Failed to load masking patterns for log4j: " + e.getStackTrace()[0].getClassName()
                    + "." + e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
        }
    }

    private void doInit(String conf) {
        try {
            if (!"".equals(conf) && conf.contains(";;")) {
                PatternHolder patternHolder = LogMaskPatternBuilder.fromConfig(conf).build();
                patternHolders.add(patternHolder);
                log.info("add pattern: " + patternHolder.getPattern().toString() + ", padding: "
                        + patternHolder.getLeftPartLength() + "," + patternHolder.getRightPartLength());
            }
        } catch (Exception e) {
            log.error(conf + ": Failed to load patterns for log4j: " + e.getStackTrace()[0].getClassName()
                    + "." + e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber()
                    + ":" + e.getMessage());
        }
    }

    /**
     * @param msg Log message.
     * @param builder StringBuilder.
     */
    private void mask(String msg, StringBuilder builder) {
        String message = msg;
        if (Objects.nonNull(message)) {
            for (PatternHolder patternHolder : patternHolders) {
                message = replace(patternHolder, message);
            }
            builder.append(message);
        } else {
            builder.append("null");
        }
    }

    private static String replace(PatternHolder patternHolder, String source) {
        if (Objects.isNull(patternHolder.getPattern())) {
            return source;
        }
        Matcher matcher = patternHolder.getPattern().matcher(source);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, patternHolder.getReplacementFun().apply(matcher));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static class PatternHolder {
        private Pattern pattern;
        private int rightPartLength;
        private int leftPartLength;
        private String fieldName;
        private Function<Matcher, String> replacementFun;
        public Pattern getPattern() {
            return pattern;
        }
        public void setPattern(Pattern pattern) {
            this.pattern = pattern;
        }
        public int getRightPartLength() {
            return rightPartLength;
        }
        public void setRightPartLength(int rightPartLength) {
            this.rightPartLength = rightPartLength;
        }
        public String getFieldName() {
            return fieldName;
        }
        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }
        public int getLeftPartLength() {
            return leftPartLength;
        }
        public void setLeftPartLength(int leftPartLength) {
            this.leftPartLength = leftPartLength;
        }
        public Function<Matcher, String> getReplacementFun() {
            return replacementFun;
        }
        public void setReplacementFun(Function<Matcher, String> replacementFun) {
            this.replacementFun = replacementFun;
        }
    }

    public static class BootLogger {
        private final String messagePattern;
        private final PrintStream out;
        private final PrintStream err;

        public BootLogger(String loggerName) {
            this(loggerName, System.out, System.err);
        }

        public BootLogger(String loggerName, PrintStream out, PrintStream err) {
            if (loggerName == null) {
                throw new NullPointerException("loggerName must not be null");
            }
            this.messagePattern = "{0,date,yyyy-MM-dd HH:mm:ss} [{1}](" + loggerName + ") {2}{3}";
            this.out = out;
            this.err = err;
        }
        public static BootLogger getLogger(String loggerName) {
            return new BootLogger(loggerName);
        }

        private String format(String logLevel, String msg, String exceptionMessage) {
            exceptionMessage = defaultString(exceptionMessage, "");
            MessageFormat messageFormat = new MessageFormat(this.messagePattern);
            long date = System.currentTimeMillis();
            Object[] parameter = {date, logLevel, msg, exceptionMessage };
            return messageFormat.format(parameter);
        }
        public void info(String msg) {
            String formatMessage = format("INFO ", msg, "");
            this.out.println(formatMessage);
        }

        public void error(String msg) {
            error(msg, null);
        }

        public void error(String msg, Throwable throwable) {
            String exceptionMessage = toString(throwable);
            String formatMessage = format("ERROR ", msg, exceptionMessage);
            this.err.println(formatMessage);
        }

        private String toString(Throwable throwable) {
            if (throwable == null) {
                return "";
            }
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            pw.println();
            throwable.printStackTrace(pw);
            pw.close();
            return sw.toString();
        }

        private String defaultString(String exceptionMessage, String defaultValue) {
            if (exceptionMessage == null) {
                return defaultValue;
            }
            return exceptionMessage;
        }
    }

    public abstract static class LogMaskPatternBuilder {

        public static final String BASE_STR_FORMAT = "(.*?)";
        public static final String TAG_SUFFIX_FORMAT = "</";
        public static final String JSON_SUFFIX_FORMAT = "\"";
        public static final String JSON_INTER_FIELD_FORMAT = ": *\"";
        public static final String PLAIN_SUFFIX_FORMAT = "[,|)]";

        private static final String MASK_CHA = "*";

        protected PatternHolder patternHolder = new PatternHolder();

        abstract PatternHolder build();

        public static LogMaskPatternBuilder fromConfig(String conf) {
            if (conf.startsWith("<")) {
                return new XmlMaskPatternBuilder(conf);
            } else if  (conf.startsWith("\"")) {
                return new JsonMaskPatternBuilder(conf);
            } else {
                return new PlainTextPatternBuilder(conf);
            }
        }

        protected void splitFieldNameAndPadding(String conf) {
            String[] elements = conf.split(";;");
            String fieldName = elements[0].trim();
            String paddingConf = elements.length > 1 ? elements[1].trim() : "";
            if ("".equals(fieldName)) {
                return;
            }
            this.patternHolder.setFieldName(fieldName);
            if ("".equals(paddingConf)) {
                this.patternHolder.setRightPartLength(0);
                this.patternHolder.setLeftPartLength(0);
            } else {
                this.splitPadding(paddingConf);
            }
        }

        protected void splitPadding(String paddingConf) {
            String[] elements = paddingConf.split(",");
            String leftPadding = elements[0].trim();
            String rightPadding = elements.length > 1 ? elements[1].trim() : "0";
            Optional.of(Integer.parseInt(rightPadding)).filter(i -> i > 0).ifPresent(patternHolder::setRightPartLength);
            Optional.of(Integer.parseInt(leftPadding)).filter(i -> i > 0).ifPresent(patternHolder::setLeftPartLength);
        }

        public static class JsonMaskPatternBuilder extends LogMaskPatternBuilder {
            public JsonMaskPatternBuilder(String conf) {
                splitFieldNameAndPadding(conf);
            }
            @Override
            PatternHolder build() {
                patternHolder.setPattern(Pattern.compile(patternHolder.getFieldName() + JSON_INTER_FIELD_FORMAT
                        + BASE_STR_FORMAT + JSON_SUFFIX_FORMAT));
                patternHolder.setReplacementFun(matcher -> patternHolder.getFieldName() + ": \""
                        + remain(matcher.group(1), patternHolder.getLeftPartLength(), patternHolder.getRightPartLength())
                        + JSON_SUFFIX_FORMAT);
                return patternHolder;
            }
        }

        public static class XmlMaskPatternBuilder extends LogMaskPatternBuilder {
            public XmlMaskPatternBuilder(String conf) {
                splitFieldNameAndPadding(conf);
            }
            @Override
            PatternHolder build() {
                patternHolder.setPattern(Pattern.compile(patternHolder.getFieldName()
                        + BASE_STR_FORMAT + TAG_SUFFIX_FORMAT));
                patternHolder.setReplacementFun(matcher -> patternHolder.getFieldName()
                        + remain(matcher.group(1), patternHolder.getLeftPartLength(), patternHolder.getRightPartLength())
                        + TAG_SUFFIX_FORMAT);
                return patternHolder;
            }
        }

        public static class PlainTextPatternBuilder extends LogMaskPatternBuilder {
            public PlainTextPatternBuilder(String conf) {
                splitFieldNameAndPadding(conf);
            }
            @Override
            PatternHolder build() {
                patternHolder.setPattern(Pattern.compile(patternHolder.getFieldName() + "="
                        + BASE_STR_FORMAT + PLAIN_SUFFIX_FORMAT));
                patternHolder.setReplacementFun(matcher -> {
                    String value =matcher.group();
                    return patternHolder.getFieldName() + "="
                            + remain(matcher.group(1), patternHolder.getLeftPartLength(), patternHolder.getRightPartLength())
                            + value.substring(value.length() - 1);
                });
                return patternHolder;
            }
        }

        public static String remain(final String str, final int remainLeft, final int remainRight) {
            if (Objects.isNull(str)) {
                return null;
            }
            if ((remainLeft + remainRight) >= str.length()) {
                return str;
            }
            int length = str.length();
            return str.substring(0, remainLeft)
                    + MASK_CHA.repeat(length - (remainLeft + remainRight))
                    + str.substring(length - remainRight);
        }
    }

}
