package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.model.dto.BonusSelfQueryDto;
import com.kerryprops.kip.service.integral.model.dto.PhotoReviewCheckDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/27/2023 10:37
 **********************************************************************************************************************/
public interface TbPhotoReviewMapper extends BaseMapper<TbPhotoReview> {

    /**
     * 小程序端分页查询
     * @param dto dto
     * @return list list
     */
    List<TbPhotoReview> getBonusList(BonusSelfQueryDto dto);

    /**
     * S端分页查询
     * @param dto dto
     * @return int int
     */
    int getBonusTotal(BonusSelfQueryDto dto);

    /**
     * S端分页查询
     * @param resource resource
     * @return list list
     */
    List<TbPhotoReview> getReviewPage(BonusSelfQueryDto resource);

    /**
     * 查询前一页
     * @param mallId mallId
     * @param endTime endTime
     * @param startTime startTime
     * @param timestamp timestamp
     * @return dto dto
     */
    TbPhotoReview getForPrevPage(@Param("timestamp") String timestamp,@Param("mallId") String mallId,@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 查询下一页未审核的信息
     * @param mallId mallId
     * @param endTime endTime
     * @param startTime startTime
     * @param timestamp timestamp
     * @return dto dto
     */
    TbPhotoReview getForNextPage(@Param("timestamp") String timestamp,@Param("mallId") String mallId,@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 查询下一页图片信息
     * @param mallId mallId
     * @param endTime endTime
     * @param startTime startTime
     * @param timestamp timestamp
     * @return dto dto
     */
    String getNextImage(@Param("timestamp") String timestamp,@Param("mallId") String mallId,@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 通过OCR taskId查询拍照积分记录
     * @param taskId
     * @return
     */
    TbPhotoReview findByOcrTaskId(@Param("taskId") String taskId);

    /**
     *
     * @param qrCode
     * @return
     */
    TbPhotoReview findByQrCode(@Param("qrCode") String qrCode);

    /**
     * 判断OCR拍照积分是否存在
     * @param checkDto
     * @return
     */
    TbPhotoReview checkPhotoReviewRecord(PhotoReviewCheckDto checkDto);

    /**
     * 校验生成的单号是否已存在
     * @param qrCode
     * @return
     */
    int checkQrCodeExists(@Param("qrCode") String qrCode);

}
