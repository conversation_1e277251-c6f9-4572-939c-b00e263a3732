package com.kerryprops.kip.service.integral.webservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.client.HiveServiceClient;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig;
import com.kerryprops.kip.service.integral.model.dto.HiveLbsInfoDto;
import com.kerryprops.kip.service.integral.model.dto.LbsIWithProjectIdDto;
import com.kerryprops.kip.service.integral.model.dto.LbsItemDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.BaseMemberStatus;
import com.kerryprops.kip.service.integral.service.TbAutoPointsConfigService;
import com.kerryprops.kip.service.integral.webservice.resource.AutoPointEntryResponse;
import com.kerryprops.kip.service.integral.webservice.resource.CommonResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbAutoPointsConfigResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbAutoPointsConfigSaveOrUpdateResource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Update;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/06/2023 16:38
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/auto_points")
@RestController
@RequiredArgsConstructor
@Tag(name = "CRM Admin/小程序端自助积分入口配置API")
public class TbAutoPointsConfigController extends BaseMemberStatus {

    private final TbAutoPointsConfigService tbAutoPointsConfigService;
    private final HiveServiceClient hiveServiceClient;
    private final HiveVasClient hiveVasClient;
    private final MallConfig mallConfig;
    private final Mapper mapper;

    @PostMapping("/establish")
    @Operation(summary="新增自助积分入口配置", method = "POST")
    public TbAutoPointsConfigResponse saveConfig(@CurrentUser LoginUser loginUser, @RequestBody @Validated({Insert.class}) TbAutoPointsConfigSaveOrUpdateResource resource) {
        TbAutoPointsConfig config = mapper.map(resource, TbAutoPointsConfig.class);
        config.setCreator(loginUser.getNickName());
        config = tbAutoPointsConfigService.saveConfig(config);
        return mapper.map(config, TbAutoPointsConfigResponse.class);
    }

    @PutMapping("/revise")
    @Operation(summary="修改自助积分入口配置", method = "PUT")
    public TbAutoPointsConfigResponse updateConfig(@CurrentUser LoginUser loginUser, @RequestBody @Validated({Update.class}) TbAutoPointsConfigSaveOrUpdateResource resource) {
        TbAutoPointsConfig config = mapper.map(resource, TbAutoPointsConfig.class);
        config.setUpdater(loginUser.getNickName());
        config = tbAutoPointsConfigService.updateConfig(config);
        return mapper.map(config, TbAutoPointsConfigResponse.class);
    }

    @DeleteMapping("/{id}")
    @Operation(summary="删除自助积分入口配置", method = "DELETE")
    public CommonResponse removeConfig(@PathVariable("id") Long id) {
        tbAutoPointsConfigService.removeConfig(id);
        return CommonResponse.builder().success(Boolean.TRUE).build();
    }

    @GetMapping("/page")
    @Operation(summary="自助积分入口配置分页查询", method = "GET")
    public Page<TbAutoPointsConfigResponse> pageData(@RequestParam("groupId") String groupId, @RequestParam("lbsIds") String lbsIds, @RequestParam("page") int page, @RequestParam("size") int size) {
        boolean isKO = mallConfig.isKO(groupId);
        List<String> projectIds = null;
        if (isKO) {
            List<MallItem> items = mallConfig.getByLbsIds(lbsIds);
            if (CollectionUtils.isNotEmpty(items)) {
                projectIds = items.stream().map(MallItem::getProjectId).collect(Collectors.toList());
            }
        }
        int total = tbAutoPointsConfigService.total(isKO ? null : groupId, isKO ? projectIds : null);
        if (total <= 0) {
            return Page.of(page, size, total);
        }
        List<TbAutoPointsConfig> list = tbAutoPointsConfigService.list(isKO ? null : groupId, isKO ? projectIds : null, page, size);
        Page<TbAutoPointsConfigResponse> pageData = Page.of(page, size, total);
        pageData.setRecords(this.getDataList(list));
        return pageData;
    }

    /**
     * 补全LBS名称
     * @param list
     * @return
     */
    private List<TbAutoPointsConfigResponse> getDataList(List<TbAutoPointsConfig> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> lbsIds = list.stream().map(TbAutoPointsConfig::getLbsId).distinct().collect(Collectors.toList());
        List<HiveLbsInfoDto> lbsList = hiveVasClient.getLbsInfo(lbsIds);
        Map<String, String> lbsNameMap = lbsList.stream().map(HiveLbsInfoDto::getLbs).collect(Collectors.toMap(LbsItemDto::getId, LbsItemDto::getName, (k1, k2) -> k2));
        return list.stream().map(it -> {
            TbAutoPointsConfigResponse response = mapper.map(it, TbAutoPointsConfigResponse.class);
            response.setLbsName(MapUtils.getString(lbsNameMap, it.getLbsId()));
            return response;
        }).collect(Collectors.toList());
    }

    @GetMapping("/lbs_list")
    @Operation(summary="KIP自助积分入口是否打开", method = "GET")
    public List<LbsIWithProjectIdDto> getBrandLbsInfo(@RequestParam("groupId") String groupId, @RequestParam(value = "lbsIds", required = false) String lbsIds) {
        boolean isKO = mallConfig.isKO(groupId);
        if (isKO && StringUtils.isNotBlank(lbsIds)) {
            List<MallItem> items = mallConfig.getByLbsIds(lbsIds);
            return CollectionUtils.isEmpty(items) ? Collections.emptyList() :
                    hiveServiceClient.getLbsListByProjectIds(items.stream().map(MallItem::getProjectId).collect(Collectors.joining(",")));
        } else {
            return hiveServiceClient.getLbsListByBrandId(groupId);
        }
    }

    @GetMapping("/entry/verify")
    @Operation(summary="KIP-C端小程序-自助积分入口是否打开", method = "GET")
    public AutoPointEntryResponse checkAutoPointEntry(HttpServletRequest request) {
        String lbsId = this.obtainHeader(request, IntegralConstant.LBS_ID);
        if (StringUtils.isBlank(lbsId)) {
            return AutoPointEntryResponse.builder().open(Boolean.FALSE).build();
        }
        TbAutoPointsConfig config = tbAutoPointsConfigService.findByLbsId(lbsId);
        // 返回自助积分开关
        return AutoPointEntryResponse.builder().open(Objects.nonNull(config)).build();
    }

}
