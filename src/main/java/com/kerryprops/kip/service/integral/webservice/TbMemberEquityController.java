package com.kerryprops.kip.service.integral.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.MemberEquityEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberBenefitConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberEquity;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberBenefitsConfigResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberEquityResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/01/2022 10:35
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/member")
@RestController
@RequiredArgsConstructor
@Tag(name = "会员权益API")
public class TbMemberEquityController {

    private final TbMemberEquityService tbMemberEquityService;
    private final TbMemberBenefitsConfigService tbMemberBenefitsConfigService;
    private final TbMemberGradeService tbMemberGradeService;
    private final MemberRegisterService memberRegisterService;
    private final HiveVasService hiveVasService;
    private final Mapper mapper;

    private static final String UNDEFINED = "undefined";

    @GetMapping("/equities")
    @Operation(summary="查询会员权益API", method = "GET")
    @Parameters({
            @Parameter(name = "type", description = "权益类型: 1:会员权益(扫码积分);2:优惠信息;3:服务协议;4:自助积分指南;5:微信无感积分;6:支付宝无感积分;7:客服台积分;8:拍照积分;", required = false)
    })
    public List<TbMemberEquityResponse> getMemberEquities(@RequestHeader("lbsId") String lbsId, @RequestParam(value = "type", required = false) String type) {
        MallItem mallItem = hiveVasService.getMallByLbsId(lbsId);
        if (Objects.isNull(mallItem)) {
            return Collections.emptyList();
        }
        List<TbMemberEquity> equities = tbMemberEquityService.findByMallIdAndType(mallItem.getGroupId(), mallItem.getMallId(), type);
        return equities.stream().map(it -> {
            TbMemberEquityResponse response = mapper.map(it, TbMemberEquityResponse.class);
            response.setId(it.getId() + "");
            Optional.ofNullable(MemberEquityEnum.getByType(response.getType())).ifPresent(num -> response.setTypeName(num.getTypeName()));
            return response;
        }).toList();
    }

    @GetMapping("/benefits/config")
    @Operation(summary="查询会员权益配置API", method = "GET")
    public List<TbMemberBenefitsConfigResponse> getBenefitsConfig(@CurrentUser LoginUser loginUser, @RequestHeader("lbsId") String lbsId, @RequestParam(value = "grade", required = false) String grade) {
        MallItem mallItem = hiveVasService.getMallByLbsId(lbsId);
        if (Objects.isNull(mallItem)) {
            return Collections.emptyList();
        }
        // 前端传值为空或undefined，则做特殊处理
        if (StringUtils.isBlank(grade) || UNDEFINED.equals(grade)) {
            if (Objects.nonNull(loginUser) && StringUtils.isNotBlank(loginUser.getCId())) {
                // 登录用户当前等级会员权益
                grade = Optional.ofNullable(memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId())).map(TbMemberAsset::getGrade).orElse("-1");
            } else {
                // 前端不传会员等级，可能是游客模式，默认取最低卡等会员权益
                grade = Optional.ofNullable(tbMemberGradeService.queryMinGroupGrade(mallItem.getGroupId())).map(TbMemberGrade::getCode).orElse("-1");
            }
        }
        List<TbMemberBenefitConfig> configs = tbMemberBenefitsConfigService.findBenefitConfigByByMallIdAndGrade(mallItem.getMallId(), grade);
        return configs.stream().map(it -> mapper.map(it, TbMemberBenefitsConfigResponse.class)).toList();
    }

}
