package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_batch_integral_present_detail
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper=false)
@TableName("tb_batch_point_present_detail")
public class TbBatchIntegralPresentDetail implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 集团id
     */
    private String groupId;

    /**
     * 导入记录id
     */
    private String recordId;

    /**
     * 状态(1:未生效，2:已生效，3:已过期)
     */
    private Integer status;

    /**
     * 会员手机号
     */
    private String mobile;

    /**
     * 会员号
     */
    private String vipcode;

    /**
     * 当前积分
     */
    private Integer currentIntegral;

    /**
     * 执行结果(1:成功，0:失败)
     */
    private Integer execResult;

    /**
     * 执行备注
     */
    private String execRemark;

    /**
     * 导入积分
     */
    private Integer importIntegral;

    /**
     * 生效时间
     */
    private Date validTime;

    /**
     * 赠送积分类型
     */
    private String integralType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 过期时间
     */
    private Date overdueTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long creator;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人id
     */
    private Long updater;

    /**
     * 更新人
     */
    private String updateUser;

}