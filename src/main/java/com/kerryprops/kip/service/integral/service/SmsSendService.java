package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.vo.EmailSendVo;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC 发送短信及记录发送日志
 * <AUTHOR> Bert
 * Created Date - 02/23/2023 09:56
 **********************************************************************************************************************/
public interface SmsSendService {

    String CRM_ADMIN_URL = "{CRM_ADMIN_URL}";
    String CRM_PHOTO_POINTS_URL = "{CRM_PHOTO_POINTS_URL}";

    /**
     * 订阅邮件提醒
     * @param sendVo sendVo
     * @return KerryResultDto dto
     */
    KerryResultDto sendEmail(EmailSendVo sendVo);

    /**
     * 业务人员订阅邮件提醒, 只能内网使用
     * @param sendVo sendVo
     * @return KerryResultDto dto
     */
    KerryResultDto emailAliCloudSend(EmailSendVo sendVo);


    /**
     * 订阅邮件提醒
     * @param mallId mallId
     * @param subscriptionType 订阅类型 0积分拦截 1拍照积分
     */
    void sendPointsEmail(String mallId, Integer subscriptionType);
}
