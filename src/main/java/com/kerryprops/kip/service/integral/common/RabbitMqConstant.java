package com.kerryprops.kip.service.integral.common;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/15/2022 14:18
 **********************************************************************************************************************/
public class RabbitMqConstant {

    /**
     * 交换器名称
     */
    public static final String DIRECT_EXCHANGE= "directExchange";

    /**
     * 主题交换器
     */
    public static final String TOPIC_EXCHANGE = "topicExchange";

    /**
     * 广播交换器-OCR
     */
    public static final String OCR_FANOUT_EXCHANGE = "ocr-fanoutExchange";

    /**
     * 广播交换器-销售变动广播
     */
    public static final String SALES_EXCHANGE_FANOUT = "sale-exchange-fanoutExchange";

    /**
     * 延时交换器名称
     */
    public static final String DIRECT_DELAY_EXCHANGE= "directDelayExchange";

    /**
     * 会员积分变更
     */
    public static final String XCRM_MEMBER_INTEGRAL_GRADE_CHANGE = "xcrm-member-integral-grade-alter";

    /**
     * 积分拦截驳回
     */
    public static final String REJECT_POINTS_INTERCEPT_RECORD = "reject-points-intercept-record";

    public static final String WECHAT_PAYMENT_SYNC_ROUTE_KEY = "crm-wechat-auto-points-route";

    /**
     * 微信无感积分退款
     */
    public static final String WECHAT_REFUND_SYNC_ROUTE_KEY = "wechat-auto-points-refund-route";

    /**
     * 商圈会员积分同步
     */
    public static final String WECHAT_POINTS_SYNC_ROUTE_KEY = "wechat-points-sync-route";

    /**
     * 商圈会员积分同步
     */
    public static final String POS_CASH_OUT_ROUTE_KEY = "pos-cash-out-points";

    /**
     * 支付包无感积分，更新会员卡积分信息
     */
    public static final String ALIPAY_POINTS_SYNC_ROUTE_KEY = "alipay-points-sync-route";

    /**
     * 会员销售后判断等级是否需要升级
     */
    public static final String MEMBER_SALES_TRIGGER_UPGRADE_CHECK = "member-sales-trigger-upgrade-check-v2";

    /**
     * 集团卡等变更触发会员升级判断
     */
    public static final String GROUP_MEMBER_GRADE_CHANGE_TRIGGER_UPGRADE = "group-member-grade-change-trigger-upgrade";

    /**
     * 会员等级变更
     */
    public static final String XCRM_MEMBER_GRADE= "xcrm-memberGrade";

    /**
     * 会员年消费金额
     */
    public static final String XCRM_MEMBER_SALE_SUM = "xcrm-member-sale-sum-v2";

    /**
     * 会员消费金额-发生了退款
     */
    public static final String XCRM_MEMBER_SALE_REFUND = "xcrm-member-sale-refund-delay";

    /**
     * 新注册用户场景触发奖励
     */
    public static final String XCRM_MEMBER_REGISTER_ACTIVITY_REWARD = "xcrm-member-register-activity-reward";

    /**
     * 会员注册短信通知
     */
    public static final String XCRM_MEMBER_REGISTER_SMS_NOTIFY = "xcrm-member-register-sms-notify";

    /**
     * 会员注册次日通知
     */
    public static final String XCRM_SIGN_NEXT_DAY_NOTIFICATION = "xcrm-sign-next-day-notification";

    /**
     * 身份认证完善信息必填项是否完善
     */
    public static final String XCRM_MEMBER_REFINE_CHECK = "xcrm-member-refine-check";

    /**
     * 完善信息场景触发奖励
     */
    public static final String MEMBER_PREFECT_INFO_REWARD = "perfect-profile-senseless-reward";

    /**
     * 会员注册来源
     */
    public static final String XCRM_REGISTER_SOURCE = "xcrm-register-source-new";

    /**
     * 支付宝会员开通会员卡授权推送
     */
    public static final String ALI_MEMBER_OPEN_CARD_KEY = "ali-member-open-card-queue";

    /**
     * kip同步会员信息至crm
     */
    public static final String KIP_SYNC_MEMBER_CRM = "kip-sync-member-to-crm";

    /**
     * kip用户注销
     */
    public static final String KIP_USER_INVALID_CRM = "kip-user-invalid-crm";

    /**
     * kip同步会员身份信息
     */
    public static final String KIP_SYNC_MEMBER_IDENTITY = "kip-sync-member-identity";

    /**
     * 拍照积分拒绝发送消息
     */
    public static final String PHOTO_POINTS_REJECT_QUEUE = "photo-points-reject";

    /**
     * 会员销售发生退单后判断会员等级
     */
    public static final String MEMBER_SALE_REFUND_CHECK_GRADE = "member-sale-refund-check-refund";

    /**
     * 支付模块-topic exchange
     */
    public static final String PAYMENT_TOPIC_EXCHANGE = "payment-middleware-service-DomainEventTopicExchange";

    /**
     * 同步支付记录的routingKey
     */
    public static final String PAYMENT_SYNC_PAY_RECORD_ROUTE_KEY = "com.kerryprops.kip.pmw.data.entity.OrderInfo.PaymentConfirmedDomainEvent";
    public static final String PAYMENT_SYNC_PAY_RECORD_QUEUE = "payment-middleware-sync-payment-records";

    /**
     * 同步退款记录
     */
    public static final String REFUND_SYNC_MALL_RECORD_ROUTE_KEY = "com.kerryprops.kip.pmw.data.entity.OrderInfo.RefundConfirmedDomainEvent";
    public static final String REFUND_SYNC_MALL_RECORD_QUEUE = "refund-middleware-sync-mall-records";

    /**
     * 会员消费积分监听队列
     */
    public static final String CRM_MEMBER_CONSUME_POINTS_QUEUE = "crm-member-consume-points-queue";

    //************************ studio-z *************************************
    /**
     * 支付宝/微信无感积分绑定奖励
     */
    public static final String MEMBER_SENSELESS_REWARD = "member-senseless-reward";

    /**
     * 销售记录满赠活动奖励
     */
    public static final String SALE_ACTIVITY_REWARD = "sale-activity-reward";

    /**
     * 会员场景触发奖励-会员升级
     */
    public static final String MEMBER_SCENE_UPGRADE_QUEUE= "com.kerry.profile.member.upgrade.*";

    /**
     * 场景触发奖励-会员注册
     */
    public static final String MEMBER_SCENE_TRIGGER_QUEUE = "trigger-member-register";

    /**
     * 场景触发-会员身份认证奖励
     */
    public static final String MEMBER_IDENTITY_REWARD = "member-identity-reward";

    /**
     * 小票上传OCR识别队列
     */
    public static final String SMALL_TICKET_OCR_RECOGNITION = "small-ticket-ocr-recognition";

    /**
     * 小票上传OCR识别成功，人工取消自动积分
     */
    public static final String SMALL_TICKET_OCR_MANUAL_CANCEL = "small-ticket-ocr-manual-cancel";

    /**
     * 品牌导览收藏或取消MQ发送
     */
    public static final String CRM_BRAND_GUIDE_COLLECT = "crm-brand-guide-collect";

    /**
     * HKC vip lounge访问通知
     */
    public static final String HKC_VIP_LOUNGE_VISIT_NOTIFY = "hkc-vip-lounge-visit-notify";

    /**
     * 初始化会员积分明细流水
     */
    public static final String INIT_POINTS_REDEEM_DETAIL = "init-points-redeem-detail";
}