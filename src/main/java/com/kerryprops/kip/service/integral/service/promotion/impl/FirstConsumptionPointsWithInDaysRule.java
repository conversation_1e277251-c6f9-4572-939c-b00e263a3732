package com.kerryprops.kip.service.integral.service.promotion.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MemberSaleMonthNumberQueryDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC: 新会员注册N天内首次消费积分
 * Author - zhangxiliang
 * Created Date - 05/21/2024 11:43
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class FirstConsumptionPointsWithInDaysRule implements PromotionRule {

    private final TbSalesDetailService tbSalesDetailService;

    @Override
    public String getRuleType() {
        return "12";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        if (StringUtils.isBlank(condition.getPromotionConditionContent())) {
            return false;
        }
        TbMemberAsset member = dto.getMember();
        // N天内注册会员且首次消费积分
        int days = Integer.parseInt(condition.getPromotionConditionContent());
        // N天后
        DateTime offsetDay = DateUtil.endOfDay(DateUtil.offsetDay(member.getJoinTime(), days));
        // 当前时间
        DateTime currentTime = DateTime.now();
        if (currentTime.getTime() > offsetDay.getTime()) {
            return false;
        }
        // 查询是否是首次消费
        MemberSaleMonthNumberQueryDto queryDto = MemberSaleMonthNumberQueryDto.builder().startDate(DateUtil.formatDateTime(member.getJoinTime()))
                .endDate(DateUtil.formatDateTime(currentTime)).vipcode(member.getVipcode()).groupId(dto.getGroupId()).build();
        return tbSalesDetailService.queryMemberSaleCountBetweenTime(queryDto) <= 0;
    }
}
