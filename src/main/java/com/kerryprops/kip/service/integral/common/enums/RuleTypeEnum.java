package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description     销售类型枚举
 * @createDate 2022/9/21
 * @updateDate 2022/9/21
 */
@Getter
public enum RuleTypeEnum {

    /**
     * 每日消费金额升级
     */
    DAILY_CONSUME_UPGRADE(1),

    /**
     * 年累计消费金额升级
     */
    ACCUMULATIVE_CONSUME_UPGRADE(2),

    /**
     * 入会当日消费金额升级
     */
    JOIN_CONSUME_UPGRADE(3),

    /**
     * 会员身份认证升级
     */
    MEMBER_IDENTITY_UPGRADE(4),

    /**
     * 每日消费保级
     */
    DAILY_CONSUME_RELEGATION(5),

    /**
     * 累计消费保级
     */
    ACCUMULATIVE_CONSUME_RELEGATION(6),

    /**
     * 会员身份认证保级
     */
    MEMBER_IDENTITY_RELEGATION(7),

    /**
     * 会员月累计消费升级
     */
    MONTHLY_ACCUMULATIVE_CONSUME_UPGRADE(8),

    /**
     * 会员月累计消费保级
     */
    MONTHLY_ACCUMULATIVE_CONSUME_RELEGATION(9),
    ;

    private final Integer value;

    RuleTypeEnum(Integer value) {
        this.value = value;
    }

    public static RuleTypeEnum getByValue(Integer value) {
        return Arrays.stream(RuleTypeEnum.values()).filter(p -> p.getValue().equals(value)).findFirst().orElse(null);
    }

    /**
     * 获取每日，每月，每年升级规则值
     * @return
     */
    public static List<Integer> getUpgradeVal() {
        return Arrays.asList(DAILY_CONSUME_UPGRADE.getValue(), MONTHLY_ACCUMULATIVE_CONSUME_UPGRADE.getValue(), ACCUMULATIVE_CONSUME_UPGRADE.getValue());
    }

    public static String getRuleType(Integer ruleType) {
        if (RuleTypeEnum.DAILY_CONSUME_RELEGATION.getValue().equals(ruleType) || RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue().equals(ruleType)) {
            return RuleTimeEnum.DAY.getValue();
        } else if (RuleTypeEnum.ACCUMULATIVE_CONSUME_RELEGATION.getValue().equals(ruleType) || RuleTypeEnum.ACCUMULATIVE_CONSUME_UPGRADE.getValue().equals(ruleType)) {
            return RuleTimeEnum.YEAR.getValue();
        }
        return RuleTimeEnum.MONTH.getValue();
    }

    /**
     * 是否是身份认证升级或保级规则
     * @param type
     * @return
     */
    public static boolean isIdentityRule(Integer type) {
        return Stream.of(MEMBER_IDENTITY_UPGRADE, MEMBER_IDENTITY_RELEGATION).anyMatch(item -> Objects.equals(item.getValue(), type));
    }

}
