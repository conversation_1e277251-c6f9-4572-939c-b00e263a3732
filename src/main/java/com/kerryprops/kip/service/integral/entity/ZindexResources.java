package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 自定义页面资源表
 * <AUTHOR>
 * @date 2022-12-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_zindex_resources")
public class ZindexResources implements Serializable {

    /**
    * 主键
    */
    private Long id;

    /**
    * 集团id collate utf8mb4_general_ci
    */
    private String groupId;

    /**
    * 商场编号 collate utf8mb4_general_ci
    */
    private String mallid;

    private String deptId;

    /**
    * 模块id
    */
    private Long moduleId;

    /**
    * 名称 collate utf8mb4_general_ci
    */
    private String ballName;

    /**
    * 权重
    */
    private Integer sort;

    /**
    * 是否小程序：0否 1是 collate utf8mb4_general_ci
    */
    private String appFlag;

    /**
    * 小程序链接 collate utf8mb4_general_ci
    */
    private String appUrl;

    /**
    * h5链接 collate utf8mb4_general_ci
    */
    private String htmlUrl;

    /**
    * 标题 collate utf8mb4_general_ci
    */
    private String title;

    /**
    * 文件夹id
    */
    private Long folderId;

    /**
    * 图片id
    */
    private Long imageId;

    /**
    * 图片路径 collate utf8mb4_general_ci
    */
    private String imageUrl;

    /**
    * 链接类型：0站内 1站外2无类型 collate utf8mb4_general_ci
    */
    private String linkType;

    /**
    * 内部链接类型：0自定义1商品2券3页面4自定义页面 collate utf8mb4_general_ci
    */
    private String insideType;

    /**
    * 外部链接类型：0h5、1小程序 collate utf8mb4_general_ci
    */
    private String outsideType;

    /**
    * 外部appid collate utf8mb4_general_ci
    */
    private String outsideAppid;

    /**
    * 外部链接名称 collate utf8mb4_general_ci
    */
    private String outsideUrlName;

    /**
    * 外部链接url collate utf8mb4_general_ci
    */
    private String outsideUrl;

    /**
    * 内部链接url collate utf8mb4_general_ci
    */
    private String insideUrl;

    /**
    * 是否启用：0停用1启用 collate utf8mb4_general_ci
    */
    private String openFlag;

    /**
    * 开始时间
    */
    private Date beginTime;

    /**
    * 结束时间
    */
    private Date endTime;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 创建者
    */
    private Long creator;

    /**
    * 更新时间
    */
    private Date updateDate;

    /**
    * 更新者
    */
    private Long updater;

    /**
    * 选中图片路径 collate utf8mb4_general_ci
    */
    private String imageCheckUrl;

    /**
    * 选中的id collate utf8mb4_general_ci
    */
    private String imageCheckId;

    /**
    * 选中颜色 collate utf8mb4_general_ci
    */
    private String fontSelectColor;

    /**
    * 券或商品id collate utf8mb4_general_ci
    */
    private String tpId;

    /**
    * 店铺名称 collate utf8mb4_general_ci
    */
    private String shopName;

    /**
    * 版本号
    */
    private Integer versions;

    /**
    * 内部链接选中id collate utf8mb4_general_ci
    */
    private String insideId;

    /**
    * 活动名称 collate utf8mb4_general_ci
    */
    private String activityName;

    /**
    * 活动参数 collate utf8mb4_general_ci
    */
    private String activityParams;

    /**
    * 页面路径辅助字段 collate utf8mb4_general_ci
    */
    private String pageUrl;

    /**
    * 是否有活动0无1有
    */
    private Integer activityType;

    /**
    * 修改名称的开关
    */
    private Integer titleInput;

    /**
    * 二级联动回显 collate utf8mb4_general_ci
    */
    private String twolevelLinkage;

    /**
    * 0不授权1授权默认0
    */
    private Integer memberAuthorize;

    /**
    * 0不授权1授权默认0
    */
    private Integer circleAuthorize;

    /**
    * 是否只显示图片0否1是
    */
    private Integer showImgFlag;

    /**
    * 店铺号 collate utf8mb4_general_ci
    */
    private String shopno;

    /**
    * 资源列表类型：0：功能球 1：多行按钮内容 2：悬浮图标 collate utf8mb4_general_ci
    */
    private String resourcesType;

    /**
    * 未被选中颜色 collate utf8mb4_general_ci
    */
    private String fontUnSelectColor;

    /**
    * 名称英文 collate utf8mb4_general_ci
    */
    private String ballNameEn;

    /**
    * 图片id英文
    */
    private Long imageIdEn;

    /**
    * 图片路径英文 collate utf8mb4_general_ci
    */
    private String imageUrlEn;

    /**
     * 是否展示在支付宝小程序 1: 展示 0: 不展示
     */
    @TableField("show_ali_mini")
    private Integer showAliMini;


}