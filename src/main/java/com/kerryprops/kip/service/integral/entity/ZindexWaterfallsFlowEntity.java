package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 自定义页面瀑布流tab资源表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2021-11-05
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_zindex_waterfalls_flow")
public class ZindexWaterfallsFlowEntity implements Serializable {

	/**
	 * id
	 */
	@TableId
	private Long id;

    /**
     * 集团ID
     */
	private String groupId;
    /**
     * 商场编号
     */
	private String mallid;
	/**
	 * 模块id
	 */
	private Long moduleId;
    /**
     * tab中文名称
     */
	private String tabChName;
    /**
     * tab英文名称
     */
	private String tabEnName;
    /**
     * 排序
     */
	private Integer sort;
    /**
     * 选中图标url路径
     */
	private String selectedUrl;
    /**
     * 未选中图标url路径
     */
	private String unselectedUrl;
    /**
     * 内容链接名称
     */
	private String contentLinkName;
	/**
	 * 链接类型：0活动 1积分商城
	 */
	private String contentLinkType;
    /**
     * 内容最多展示个数
     */
	private Integer showNum;
    /**
     * 更多跳转url路径
     */
	private String moreUrl;

	/**
	 * 创建者
	 */
	@TableField(fill = FieldFill.INSERT)
	private Long creator;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;

    /**
     * 更新时间
     */
	private Date updateDate;
    /**
     * 更新者
     */
	private Long updater;

}