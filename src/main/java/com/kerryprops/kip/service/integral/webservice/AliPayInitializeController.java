package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.lang.Assert;
import com.alipay.api.domain.MerchantCard;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.AliPayClientService;
import com.kerryprops.kip.service.integral.webservice.resource.AlipayCardTemplateUpdateResource;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 微信支付相关api
 * @createDate 2022/9/7
 * @updateDate 2022/9/7
 */
@Slf4j
@RequestMapping("/alipay")
@RestController
@RequiredArgsConstructor
@Tag(name = "支付宝无感积分API")
public class AliPayInitializeController {

    private final MallConfig mallConfig;
    private final AliPayClientService aliPayClientService;

    @GetMapping(value = "/create_ali_pay_card_url")
    @Operation(summary="获取会员卡领卡投放链接", method = "GET")
    public String createAliPayCardUrl(@RequestParam String mallId) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        if (Objects.isNull(mallItem)) {
            return null;
        }
        return aliPayClientService.createAliPayCardUrl(mallItem);
    }

    @GetMapping(value = "/card_market_trade_subscribe")
    @Operation(summary="代商户订阅交易通知消息", method = "GET")
    public String cardMarketingTradeSubscribe(@RequestParam String mallId) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        if (Objects.isNull(mallItem)) {
            return null;
        }
        return aliPayClientService.cardMarketingTradeSubscribe(mallItem);
    }

    @GetMapping(value = "/card_template/detail")
    @Operation(summary="会员卡模板详情查询接口", method = "GET")
    public String queryCardTemplate(@RequestParam String mallId) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        if (Objects.isNull(mallItem)) {
            return null;
        }
        return aliPayClientService.queryCardTemplate(mallItem);
    }

    @GetMapping(value = "/card_template/activate_url")
    @Operation(summary="会员卡开卡链接查询API", method = "GET")
    public String getAlipayMarketingCardActivateurl(@RequestParam String mallId) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        if (Objects.isNull(mallItem)) {
            return null;
        }
        return aliPayClientService.getAlipayMarketingCardActivateurl(mallItem);
    }

    @GetMapping(value = "/card_template/card_template_set")
    @Operation(summary="会员卡开卡链接查询API", method = "GET")
    public String getAlipayMarketingCardFormtemplateSetRequest(@RequestParam String mallId) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        if (Objects.isNull(mallItem)) {
            return null;
        }
        return aliPayClientService.getAlipayMarketingCardFormtemplateSetRequest(mallItem);
    }

    @GetMapping(value = "/card_template/config")
    @Operation(summary="会员卡开卡表单模板配置", method = "GET")
    public String configTemplate(@RequestParam String mallId) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        if (Objects.isNull(mallItem)) {
            return null;
        }
        return aliPayClientService.configTemplate(mallItem);
    }

    @Operation(summary="创建会员卡模板", method = "GET")
    @GetMapping(value = "/card_template/create")
    public String createCardTemplate(@RequestParam("mallId") String mallId) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        if (Objects.isNull(mallItem)) {
            return null;
        }
        return aliPayClientService.createCardTemplate(mallItem);
    }

    @ResponseBody
    @Operation(summary="创建支付", method = "GET")
    @RequestMapping(value = "/create", produces = "text/html;charset=utf-8")
    public String create(@RequestParam("orderNo") String orderNo,
                         @RequestParam("orderName") String orderName,
                         @RequestParam("payPrice") String payPrice) {
        return aliPayClientService.create(orderNo, orderName, payPrice);
    }

    @GetMapping(value = "/card_template/image_upload")
    @Operation(summary="alipay会员卡背景图片上传", method = "GET")
    public void uploadAlipayMemberCardBackgroundImage(@RequestParam("mallId") String mallId) {
        String filePath = "/Users/<USER>/Downloads/SZKP_CRM_MEMBER_CARD_BG.jpg";
        // 上传会员卡背景图片
        aliPayClientService.uploadAlipayCardBackgroundImage(filePath, mallId);
    }

    @PutMapping(value = "/card_template/update")
    @Operation(summary="alipay会员卡配置更新API", method = "GET")
    public void updateAlipayCardTemplateInfo(@RequestBody @Validated AlipayCardTemplateUpdateResource resource) throws Exception {
        // 更新会员卡模版配置
        aliPayClientService.updateAlipayCardTemplateRequest(resource);
    }

    @Hidden
    @GetMapping(value = "/member_card/detailByCardNo")
    public MerchantCard getAliMemberCardDetail(@RequestParam("cardNo") String cardNo, @RequestParam("mallId") String mallId) {
        return aliPayClientService.memberCardDetailByCardNo(cardNo, mallId);
    }

    @Hidden
    @GetMapping(value = "/member_card/detailByAliUserId")
    public MerchantCard getAliMemberCardDetailByAliUserId(@RequestParam("aliUserId") String aliUserId, @RequestParam("mallId") String mallId) {
        return aliPayClientService.memberCardDetailByAliUserId(aliUserId, mallId);
    }

    @Hidden
    @GetMapping(value = "/member_card/detailByMobile")
    public MerchantCard getAliMemberCardDetailByMobile(@RequestParam("mobile") String mobile, @RequestParam("mallId") String mallId) {
        return aliPayClientService.memberCardDetailByMobile(mobile, mallId);
    }

    @Hidden
    @GetMapping(value = "/auth_info/scheme_url")
    public String authSchemeUrl(@RequestParam("mallId") String mallId, @RequestParam("aliBizCard") String aliBizCard) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        Assert.notNull(mallItem);
        return aliPayClientService.querySchemaUrl(aliBizCard, mallItem);
    }

}
