package com.kerryprops.kip.service.integral.common.utils;

import com.kerryprops.kip.service.integral.webservice.response.OcrPhotoResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.HashMap;
import java.util.Map;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/24/2024 11:34
 **********************************************************************************************************************/

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DeferredResultCacheUtils {

    private static Map<String, DeferredResult<OcrPhotoResponse>> RESULT_MAP = new HashMap<>();

    /**
     * 设置DeferredResult
     * @param key
     * @param result
     */
    public static void putResultMap(String key, DeferredResult<OcrPhotoResponse> result) {
        RESULT_MAP.put(key, result);
    }

    /**
     * 查询DeferredResult
     * @param key
     * @return
     */
    public static DeferredResult<OcrPhotoResponse> getResultMap(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return RESULT_MAP.get(key);
    }

    /**
     * 删除DeferredResult
     * @param key
     * @return
     */
    public static void removeResultMap(String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        RESULT_MAP.remove(key);
    }

    /**
     * 获取map内还存在多少个任务
     * @return
     */
    public static int getSize() {
        return RESULT_MAP.size();
    }

    /**
     * 判断是否包含任务id
     * @param taskId
     * @return
     */
    public static boolean containsKey(String taskId) {
        return RESULT_MAP.containsKey(taskId);
    }

}
