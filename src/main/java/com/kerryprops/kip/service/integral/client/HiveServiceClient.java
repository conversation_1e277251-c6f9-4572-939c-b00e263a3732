package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.LbsIWithProjectIdDto;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/06/2022 11:21
 **********************************************************************************************************************/

@FeignClient(name = "hive-service", url = "${kerry.services.hive-service:default}")
public interface HiveServiceClient {

    @GetMapping("/tenant/retail_format")
    List<String> getShopCodesByFormats(@RequestParam("retailFormatCode") List<String> formats, @RequestParam("groupId") String groupId, @RequestParam("lbsId") String lbsId);

    @GetMapping("/lbs/all")
    List<LbsIWithProjectIdDto> getLbsListByProjectIds(@RequestParam("projectId") String projectId);

    @GetMapping("/lbs/all")
    List<LbsIWithProjectIdDto> getLbsListByBrandId(@RequestParam("brandId") String brandId);

    @GetMapping("/lbs/all")
    List<LbsIWithProjectIdDto> getLbsList(@RequestParam(value = "brandId", required = false) String brandId,
                                                   @RequestParam(value = "needBuilding", required = false) Integer needBuilding,
                                                   @RequestParam(value = "showRetailAdmin", required = false) Integer showRetailAdmin,
                                                   @RequestParam(value = "format", required = false) String format);

    @GetMapping("/tenant/shop_no/{shopNos}")
    List<TenantInfoVo> findByShopNos(@PathVariable("shopNos") String shopNos);

}
