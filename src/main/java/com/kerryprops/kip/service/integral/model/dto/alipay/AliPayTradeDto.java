package com.kerryprops.kip.service.integral.model.dto.alipay;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 适用于alipay.trade.pay 交易接口的传参实体类
 * @createDate 2022/11/11
 * @updateDate 2022/11/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AliPayTradeDto {
    @JSONField(name = "buyer_id")
    private String buyerId;

    @JSONField(name = "trade_no")
    private String tradeNo;

    @JSONField(name = "earn_points")
    private boolean earnPoints;

    @JSONField(name = "obtain_points")
    private int obtainPoints;

    @JSONField(name = "points_update_time")
    private Date pointsUpdateTime;

}
