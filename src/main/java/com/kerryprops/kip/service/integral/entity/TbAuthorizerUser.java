package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/14/2024 13:58
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_authorizer_user")
public class TbAuthorizerUser implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 请求参数用户名
     */
    private String user;

    /**
     * 请求参数用户密码
     */
    private String password;

    /**
     * 用户关联请求ip
     */
    private String ips;

    /**
     * 0 : 不可用; 1 : 可用
     */
    private Integer isUse;

    private Integer isCheck;

    private String authorizerWxType;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

}
