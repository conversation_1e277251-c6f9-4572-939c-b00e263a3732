package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.kerryprops.kip.service.integral.client.HiveServiceClient;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.TbMemberGradeFormat;
import com.kerryprops.kip.service.integral.model.vo.TenantAllVo;
import com.kerryprops.kip.service.integral.model.vo.TenantBuildingVo;
import com.kerryprops.kip.service.integral.model.vo.TenantRespVo;
import com.kerryprops.kip.service.integral.service.TbBaseShopService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 15:54
 **********************************************************************************************************************/
@Slf4j
@Service
@AllArgsConstructor
public class TbBaseShopServiceImpl implements TbBaseShopService {

    private final HiveVasClient hiveVasClient;
    private final HiveServiceClient hiveServiceClient;
    private final MallConfig mallConfig;

    @Override
    public TbBaseShop selectByContractNo(String contractNo) {
        return this.getBaseShop(contractNo, null);
    }

    @Override
    public TbBaseShop getByTenantId(String tenantId, String mallId) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        KerryResultDto<List<TenantAllVo>> tenantResult = hiveVasClient.getTenantById(tenantId, 1);
        if (CollectionUtils.isEmpty(tenantResult.getData())) {
            return null;
        }
        TenantAllVo allVo = tenantResult.getData().get(0);
        // 判断当前店铺状态是否可用
        if (!IntegralConstant.SHOP_ENABLE_STATUS.equals(allVo.getTenant().getStatus())) {
            log.info("该店铺未启用. {}", tenantId);
            return null;
        }
        String tmpMallId = mallId;
        if (StringUtils.isBlank(tmpMallId)) {
            tmpMallId = allVo.getBuildings().stream().filter(it -> StringUtils.isNotBlank(it.getFormats()) && it.getFormats().contains("RETAIL")).map(TenantBuildingVo::getLbsId).findFirst().orElse(mallId);
        }
        MallItem mallItem = mallConfig.getByMallId(tmpMallId);
        return TbBaseShop.getTbBaseShop(allVo, mallItem);
    }

    @Override
    public TbBaseShop getByContractNoAndMallId(String contractNo, String mallId) {
        // 如果店铺号都为空，则直接返回null
        if (StringUtils.isBlank(contractNo)) {
            log.info("查询店铺信息传入的contractNo为空.");
            return null;
        }
        return this.getBaseShop(contractNo, mallId);
    }

    @Override
    public TbBaseShop getShopByDoCos(String doCos, String mallId) {
        List<TenantRespVo> voList = hiveVasClient.getShopByDoCos(doCos).getData();
        if (CollectionUtils.isEmpty(voList)) {
            log.info("合同号doCos: {}未查询到信息.", doCos);
            return null;
        }
        TenantRespVo vo = voList.stream().filter(Objects::nonNull).findFirst().orElse(null);
        if (Objects.isNull(vo)) {
            return null;
        }
        return this.getBaseShop(vo.getContractNo(), mallId);
    }

    /**
     * hive拿到的店铺信息转 -> TbBaseShop
     * @param contractNo
     * @param mallId
     * @return
     */
    private TbBaseShop getBaseShop(String contractNo, String mallId) {
        List<TenantAllVo> allVos = hiveVasClient.getTenantInfo(contractNo, 1, 0);
        if (CollectionUtils.isEmpty(allVos)) {
            log.info("店铺code: {}-{}未查询到信息.", contractNo, mallId);
            return null;
        }
        TenantAllVo allVo = allVos.get(0);
        // 判断当前店铺是否属于该lbs
        if (StringUtils.isNotBlank(mallId) && (CollectionUtils.isEmpty(allVo.getBuildings()) || allVo.getBuildings().stream().noneMatch(it -> StringUtils.equals(it.getLbsId(), mallId)))) {
            log.info("店铺code: {}-{}未查询到信息.", contractNo, mallId);
            return null;
        }
        // 判断当前店铺状态是否可用
        if (!StringUtils.equals(allVo.getTenant().getStatus(), IntegralConstant.SHOP_ENABLE_STATUS)) {
            log.info("该店铺未启用. {}", contractNo);
            return null;
        }

        String tmpMallId = mallId;
        if (StringUtils.isBlank(tmpMallId)) {
            tmpMallId = allVo.getBuildings().stream().filter(it -> StringUtils.isNotBlank(it.getFormats()) && it.getFormats().contains("RETAIL")).map(TenantBuildingVo::getLbsId).findFirst().orElse(null);
        }
        MallItem mallItem = mallConfig.getByMallId(tmpMallId);
        return TbBaseShop.getTbBaseShop(allVo, mallItem);
    }

    /**
     * 使用hive-service api查询对应业态下的店铺编号
     * @param groupId groupId
     * @param formats formats
     * @return list
     */
    @Override
    public List<String> getContractNoListByGroupIdAndFormats(String groupId, List<String> formats) {
        return hiveServiceClient.getShopCodesByFormats(formats, groupId, "");
    }

    @Override
    public List<String> getContractNoList(String groupId, String formats) {
        if (StringUtils.isBlank(formats)) {
            return Collections.emptyList();
        }
        if (mallConfig.isKO(groupId)) {
            List<TbMemberGradeFormat> gradeFormats = JsonUtils.stringToList(formats, TbMemberGradeFormat.class);
            if (CollectionUtils.isEmpty(gradeFormats)) {
                return Collections.emptyList();
            }
            List<String> shopNoList = new ArrayList<>();
            for (TbMemberGradeFormat formatDto : gradeFormats) {
                List<String> shopNos = hiveServiceClient.getShopCodesByFormats(formatDto.getFormatList(), groupId, formatDto.getMallId());
                shopNoList.addAll(shopNos);
            }
            return CollectionUtils.isEmpty(shopNoList) ? Collections.emptyList() : shopNoList.stream().filter(StringUtils::isNotBlank).distinct().toList();
        } else {
            List<String> formatsList = CharSequenceUtil.split(formats, ",");
            return hiveServiceClient.getShopCodesByFormats(formatsList, groupId, "");
        }
    }
}
