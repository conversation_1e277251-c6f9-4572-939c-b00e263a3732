package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 05/09/2023 13:47
 **********************************************************************************************************************/

@Getter
public enum PointsInterceptStatusEnum {
    // 积分拦截状态

    // 审核通过
    INTERCEPT_SUCCESS_STATUS(0, "INTERCEPT_SUCCESS_STATUS"),
    // 待审核
    INTERCEPT_WAIT_STATUS(3, "INTERCEPT_WAIT_STATUS"),
    // 审核驳回
    INTERCEPT_REJECT_STATUS(4, "INTERCEPT_REJECT_STATUS"),
    // 全额退款
    INTERCEPT_REFUND_STATUS(1, "INTERCEPT_REFUND_STATUS");

    private final int type;
    private final String desc;

    PointsInterceptStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PointsInterceptStatusEnum getByType(Integer type) {
        return Arrays.stream(values()).filter(it -> Objects.equals(it.getType(), type)).findFirst().orElse(null);
    }

    public static String getDescByType(Integer type) {
        return Arrays.stream(values()).filter(it -> Objects.equals(it.getType(), type)).findFirst().map(PointsInterceptStatusEnum::getDesc).orElse(null);
    }

}
