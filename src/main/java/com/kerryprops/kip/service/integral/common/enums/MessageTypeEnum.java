package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.Arrays;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/22/2023 18:14
 **********************************************************************************************************************/

@Getter
public enum MessageTypeEnum {
    // [27,28,29,30,38]     [5, 6, 7,8, 38]
    GRADE_CHANGE("4", 7, "MEMBER_LEVEL_CHANGE"),
    // [27,29,31,32,38]    [5, 10, 11, 38]
    POINTS_CHANGE("5", 8, "INTEGRAL_CHANGE"),
    // [38,39,40,41,38]   [38,39,40,41]
    CLEAR_POINTS("14", 0, "INTEGRAL_DUMP"),
    // [38,37,28,44,27,43]  [38,37,28,44,27,43]
    NEW_MEMBER("16", 17, "MEMBER_REGISTRATION"),
    // [38,37,28,44,27,43]   [38,44,37,28,27,43]
    NEXT_DAY("17", 18, "MEMBER_REGISTRATION_NEXT_DAY_REMINDER"),
    // 拍照积分审核驳回
    // [46,45,38,43,27]  [46,45,38,43,27]
    PHOTO_POINTS_REJECT("18", 19, "INTEGRAL_REJECTION"),
    // [37, 44]     [38]
    WX_AUTO_POINTS("19", 20, "WECHAT_SENSELESS_PAYMENT"),
    // [38,37,51]  [38,37,51]
    ALIPAY_SENSELESS_PAYMENT("25", 25, "ALIPAY_SENSELESS_PAYMENT"),
    // 5,6,7,8,10,11,27,28,29,30,31,32,37,38,39,40,41,43,44,45,46,51
    // 11 没有 38,39,40,41给积分清零，属于loy服务

    MEMBER_DOWNGRADE_NOTIFICATION("27", 27, "MEMBER_DOWNGRADE_NOTIFICATION"),
    // [54,45,38,43,27]
    ABNORMAL_POINTS_REVIEW_REJECTION("30", 30, "ABNORMAL_POINTS_REVIEW_REJECTION");

    /**
     * 微信模板
     */
    private String type;
    /**
     * 邮箱节点
     */
    private int sendNode;
    /**
     * 短信发送节点
     */
    private String msgType;

    MessageTypeEnum(String type, int sendNode, String msgType) {
        this.type = type;
        this.sendNode = sendNode;
        this.msgType = msgType;
    }

    public static MessageTypeEnum getByType(String type) {
        return Arrays.stream(MessageTypeEnum.values()).filter(p -> p.getType().equals(type)).findFirst().orElse(MessageTypeEnum.WX_AUTO_POINTS);
    }

}
