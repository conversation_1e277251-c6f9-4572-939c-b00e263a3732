package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "拍照积分上传请求类")
public class SmallTicketUploadResource implements Serializable {

    @NotBlank(message = "图片URL不能为空")
    @Schema( description = "上传图片地址")
    private String imgUrl;

    @NotBlank(message = "小程序信息不能为空")
    @Schema( description = "小程序jsCode")
    private String jsCode;

}
