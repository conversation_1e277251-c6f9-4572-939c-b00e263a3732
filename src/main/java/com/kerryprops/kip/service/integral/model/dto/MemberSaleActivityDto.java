package com.kerryprops.kip.service.integral.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *  满赠活动推送信息类
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/28/2022 09:27
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberSaleActivityDto implements Serializable {

    /**
     * 会员id
     */
    private String memberId;

    /**
     * 商场id
     */
    private String mallId;

    /**
     * 店铺id
     */
    private String shopId;

    /**
     * 销售单号
     */
    private String sellNo;

    /**
     * 销售id
     */
    private String saleId;

    /**
     * 销售时间/退款时间 yyyy-MM-dd HH:mm:ss
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String createDate;

    /**
     * 操作人
     */
    private String createUser;

    /**
     * 7.微信无感积分,8.支付宝无感积分
     */
    private String saleType;

    /**
     * isRefund=true: 退款金额
     * isRefund=false: 支付金额
     */
    private BigDecimal money;

    /**
     * true: 退款
     * false: 支付
     */
    private boolean isRefund;

    /**
     * 销售来源，区分ipad/admin端，有消息提醒
     */
    private String fromType;

    /**
     * 会员产生销售时的原始卡等级，徽章的发放，根据会员原始卡等判断
     */
    private String orgGrade;

}
