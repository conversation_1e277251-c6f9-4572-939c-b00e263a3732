package com.kerryprops.kip.service.integral.webservice.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/08/2024 09:58
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "IPAD端扫码积分明细")
public class IpadGagQrCodeResponse implements Serializable {

    @Schema( name = "交易单号")
    private String traceno;

    @Schema( name = "店铺号")
    private String contractNo;

    @Schema( name = "交易日期")
    @JsonFormat(timezone="GMT+8", pattern="yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date tradingDate;

    @Schema( name = "积分日期")
    @JsonFormat(timezone="GMT+8", pattern="yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date pointDate;

    @Schema( name = "积分会员信息")
    private String nameAndMobile;

    @Schema( name = "实付金额")
    private BigDecimal receivableAmount;

    @Schema( name = "当前积分")
    private Integer currnentIntegral;

    @Schema( name = "店铺名称")
    private String shopName;

    @Schema( name = "是否已积分: 0:已积分, 1:未积分")
    private String completedPoints;

}
