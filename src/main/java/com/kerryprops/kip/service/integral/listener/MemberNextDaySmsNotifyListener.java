package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.model.dto.MemberIdDto;
import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
import com.kerryprops.kip.service.integral.service.TemplateMessageSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC CRM会员注册次日短信提醒
 * <AUTHOR> Bert
 * Created Date - 03/22/2023 00:05
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class MemberNextDaySmsNotifyListener {

    private final TemplateMessageSendService templateMessageSendService;

    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.XCRM_SIGN_NEXT_DAY_NOTIFICATION}, containerFactory = "crmContainerFactory")
    public void receiveMsg(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("会员注册次日短信提醒消息体为空.");
            return;
        }
        log.info("MemberNextDaySmsNotifyListener: {}" , msg);
        MemberIdDto dto = JsonUtils.stringToObj(msg, MemberIdDto.class);
        if (Objects.isNull(dto)) {
            return;
        }
        SendMessageDto messageDto = SendMessageDto.builder().memberId(String.valueOf(dto.getId())).templateType(1)
                .messageType(dto.getMessageType()).build();
        // 发送短信
        templateMessageSendService.sendMessage(messageDto);
    }

}
