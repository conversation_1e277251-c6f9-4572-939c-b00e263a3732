package com.kerryprops.kip.service.integral.config.mq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 05/04/2023 15:26
 **********************************************************************************************************************/

@Data
@Component
@ConfigurationProperties(prefix = "kerry-crm.rabbitmq")
public class CrmRabbitProperties {

    private String password;

    private String username;

    private String addresses;

    private String virtualHost;

    private Integer requestedHeartbeat;

}
