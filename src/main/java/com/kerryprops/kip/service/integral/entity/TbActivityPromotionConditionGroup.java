package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * tb_activity_promotion_condition_group
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_activity_promotion_condition_group")
public class TbActivityPromotionConditionGroup implements Serializable {
    /**
     * 营销积分条件分组表
     */
    private String id;

    private String promotionId;

}