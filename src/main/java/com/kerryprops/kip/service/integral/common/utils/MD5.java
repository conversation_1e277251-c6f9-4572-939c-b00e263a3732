package com.kerryprops.kip.service.integral.common.utils;

import com.kerryprops.kip.service.integral.common.IntegralConstant;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.util.Locale;

@Slf4j
public class MD5 {

	public static String getMD5(String srcTxt) throws Exception {
		MessageDigest digest;
		String algorithm = "MD5";
		String result = "";
		digest = MessageDigest.getInstance(algorithm);
		log.info("getMD5 use charset:{}",
                Charset.defaultCharset().displayName(Locale.SIMPLIFIED_CHINESE));
		digest.update(srcTxt.getBytes());
		byte[] byteRes = digest.digest();
		int length = byteRes.length;
		for (int i = 0; i < length; i++) {
			result = result + byteHEX(byteRes[i]);
		}
		return result;
	}

	private static String byteHEX(byte ib) {
		char[] DigitNormal = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
		char[] ob = new char[2];
		ob[0] = DigitNormal[(ib >>> 4) & 0X0F];
		ob[1] = DigitNormal[ib & 0X0F];
		return new String(ob);
	}

	public static String getMD5_32_big(String genjoinstr) {
		try {
			String result = "";
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			md5.update((genjoinstr).getBytes(IntegralConstant.DEFAULT_ENC));
			byte b[] = md5.digest();

			int i;
			StringBuffer buf = new StringBuffer("");

			for(int offset=0; offset<b.length; offset++){
				i = b[offset];
				if(i<0){
					i+=256;
				}
				if(i<16){
					buf.append("0");
				}
				buf.append(Integer.toHexString(i));
			}

			result = buf.toString();
			return buf.toString().toUpperCase();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}

}