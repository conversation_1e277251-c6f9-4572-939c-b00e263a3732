package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2022 12:27
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxAutoPointsMessageDto implements Serializable {

    /**
     * 会员id
     */
    private String memberId;

    /**
     * 商场id
     */
    private String mallId;

    /**
     * 授权时间 yyyy-MM-dd HH:mm:ss
     */
    private String queryDate;

    /**
     * 1(支付宝) 2(微信)
     */
    private String type;

}
