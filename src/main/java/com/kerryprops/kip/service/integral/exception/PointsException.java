package com.kerryprops.kip.service.integral.exception;

import com.kerryprops.kip.service.integral.common.current.IError;
import lombok.Data;

import java.util.function.Supplier;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> nancy
 * Created Date - 09/27/2022 16:57
 **********************************************************************************************************************/

@Data
public class PointsException extends RuntimeException  {

    protected final Integer code;
    protected final String msg;
    protected final String data;

    public PointsException(Integer code, String msg, String data) {
        super(msg);
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
}
