package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 10/22/2024 10:08
 **********************************************************************************************************************/

@Getter
public enum SaleRepeatEnum {

    SHOP("1", "店铺"),
    AMOUNT("2", "金额"),
    SALE_TIME("3", "销售时间"),
    VIP_CODE("4", "会员");

    private final String code;
    private final String desc;

    SaleRepeatEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
