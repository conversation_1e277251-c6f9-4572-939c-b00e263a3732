package com.kerryprops.kip.service.integral.common.current;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
 * @author: Bert
 * @date: 2021/5/20 12:30 下午
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LoginUser implements Serializable {

    @Schema( description = "用户id")
    private Long userId;

    @Schema( description = "登录来源，C:客户端；B: 企业用户端；S: 嘉里员工端")
    private String fromType;

    @Schema( description = "C端用户的uuid")
    private String cId;

    @Schema( description = "用户昵称")
    private String nickName;

    @Schema( description = "用户手机号")
    private String phoneNumber;

    @Schema( description = "来源于哪个小程序")
    private String source;

    @Schema( description = "公众号或小程序的用户openId")
    private String openId;

    @Schema( description = "品牌id")
    private String brandId;

    @Schema( description = "lbs id")
    private String lbsId;

    @Schema( description = "访问的小程序")
    private String appId;

}
