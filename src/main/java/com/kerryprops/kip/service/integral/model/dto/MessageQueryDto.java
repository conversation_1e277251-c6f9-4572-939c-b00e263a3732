package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/***********************************************************************************************************************
 * Project - user-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 06/02/2022 14:21
 **********************************************************************************************************************/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageQueryDto implements Serializable {

    private String groupId;

    private String sendNodeDesc;

    private String membergrade;

    private List<String> mobileParam;

    private List<Map<String, String>> smsParam;

    private String createUser;

    private String mallid;

    private String state;

    private String templateType;

}
