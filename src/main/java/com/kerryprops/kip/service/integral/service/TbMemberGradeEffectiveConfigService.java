package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeEffectiveConfig;

import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - member-points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 06/05/2023 14:46
 **********************************************************************************************************************/
public interface TbMemberGradeEffectiveConfigService extends IService<TbMemberGradeEffectiveConfig> {

    /**

     * 通过groupId查找保级降级生效配置
     * @param groupId
     * @return
     */
    TbMemberGradeEffectiveConfig findByGroupId(String groupId);

    CompletableFuture<TbMemberGradeEffectiveConfig> getFutureByGroupId(String groupId);

}
