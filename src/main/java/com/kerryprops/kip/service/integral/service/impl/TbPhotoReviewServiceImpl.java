package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.event.OcrPhotoRejectEvent;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.mapper.TbPhotoReviewMapper;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/27/2023 10:42
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbPhotoReviewServiceImpl extends ServiceImpl<TbPhotoReviewMapper, TbPhotoReview> implements TbPhotoReviewService {

    private final TbPhotoReviewMapper tbPhotoReviewMapper;
    private final MemberSalePointsProcessService memberSalePointsProcessService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbBaseShopService tbBaseShopService;
    private final RedisService redisService;
    private final Mapper mapper;

    @Override
    public List<TbPhotoReview> geTbBonusSelfList(BonusSelfQueryDto dto) {
        return tbPhotoReviewMapper.getBonusList(dto);
    }

    @Override
    public boolean updateById(TbPhotoReview review) {
        review.setUpdateDate(new Date());
        int count = tbPhotoReviewMapper.updateById(review);
        // 拍照积分-驳回短信发送
        applicationEventPublisher.publishEvent(OcrPhotoRejectEvent.builder().id(review.getId()).build());
        return count == 1;
    }

    @Override
    public int getBonusTotal(BonusSelfQueryDto dto) {
        return tbPhotoReviewMapper.getBonusTotal(dto);
    }

    @Override
    public Long saveRecord(BonusSelfUploadDto dto) {
        TbPhotoReview entity = mapper.map(dto, TbPhotoReview.class);
        Date currentDate = new Date();
        entity.setUploadDate(currentDate);
        entity.setCreateDate(currentDate);
        entity.setUpdateDate(currentDate);
        entity.setTimestamp(currentDate.getTime()+"");
        // 以下字段默认给空
        entity.setSerialNum("");
        entity.setMoney(0D);
        entity.setBonus(0);
        entity.setQrcode(StringUtils.isNotBlank(dto.getQrcode()) ? dto.getQrcode() : "");
        // tick 3.16，ocr使用description记录ocr回传的异常code
        entity.setDescription(StringUtils.isNotBlank(dto.getDescription()) ? dto.getDescription() : "");
        // 合合OCR上传失败，异常原因
        entity.setReason(StringUtils.isNotBlank(dto.getReason()) ? dto.getReason() : "");
        entity.setShopNo("");
        entity.setShopName("");
        entity.setTradingDate(DateUtil.parseDateTime("1999-01-01 00:00:00"));
        entity.setCheckTime(DateUtil.parseDateTime("1999-01-01 00:00:00"));
        entity.setCheckName("");
        entity.setFormatId("");
        entity.setFormatName("");
        entity.setIntegralAdjustId("");
        entity.setOrgGrade("");
        entity.setOrgPoints(0);
        entity.setCreateUser("");
        entity.setUpdateUser("");
        tbPhotoReviewMapper.insert(entity);
        return entity.getId();
    }

    @Override
    public Integer auditRecord(TakePhotoAuditDto dto) {
        TbPhotoReview bonusSelf = tbPhotoReviewMapper.selectById(dto.getId());
        if (Objects.isNull(bonusSelf)) {
            throw BizNotFoundException.error(PointsEnum.BONUS_RECORD_NOT_FOUND);
        }
        if (!StringUtils.equals(bonusSelf.getStatus(), "1")) {
            throw BizException.error(PointsEnum.BONUS_RECORD_AUDITED);
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(bonusSelf.getVipcode(), bonusSelf.getGroupId());
        if (Objects.isNull(tbMemberAsset)) {
            // TICk4.5: 已注销的账号的拍照积分审核，支持驳回
            if (StringUtils.equals(dto.getState(), "3")) {
                bonusSelf.setStatus(dto.getState());
                bonusSelf.setUpdateDate(new Date());
                bonusSelf.setReason(StringUtils.isNotBlank(dto.getReason()) ? dto.getReason() : "会员已注销账号，驳回申请");
                tbPhotoReviewMapper.updateById(bonusSelf);
                return 0;
            } else {
                throw BizNotFoundException.error(PointsEnum.MEMBER_NOT_EXISTS);
            }
        }
        TbBaseShop tbBaseShop = tbBaseShopService.getByContractNoAndMallId(dto.getShopNo(), bonusSelf.getMallId());
        if (Objects.isNull(tbBaseShop) && StringUtils.equals(dto.getState(), "2")) {
            throw BizNotFoundException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        Integer points = 0;
        bonusSelf.setStatus(dto.getState());
        bonusSelf.setReason(dto.getReason());
        bonusSelf.setShopNo(StringUtils.isBlank(dto.getShopNo()) ? "" : dto.getShopNo());
        bonusSelf.setShopName(Objects.isNull(tbBaseShop) ? ""  : tbBaseShop.getBrandName());
        String formatIds = Objects.isNull(tbBaseShop) ? "" : Stream.of(tbBaseShop.getFirstFormatCode(), tbBaseShop.getSecondFormatCode(), tbBaseShop.getThirdFormatCode()).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        bonusSelf.setFormatId(formatIds);
        String formatNames = Objects.isNull(tbBaseShop) ? "" : Stream.of(tbBaseShop.getFirstFormatName(), tbBaseShop.getSecondFormatName(), tbBaseShop.getThirdFormatName()).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        bonusSelf.setFormatName(formatNames);
        bonusSelf.setCheckTime(new Date());
        bonusSelf.setCheckName(dto.getAuditor());
        bonusSelf.setUpdateDate(new Date());
        // 驳回
        if (StringUtils.equals(dto.getState(), "3")) {
            tbPhotoReviewMapper.updateById(bonusSelf);
            // 拍照积分-驳回短信发送
            applicationEventPublisher.publishEvent(OcrPhotoRejectEvent.builder().id(bonusSelf.getId()).build());
            return points;
        }
        bonusSelf.setSerialNum(dto.getSellNo());
        bonusSelf.setOrgGrade(tbMemberAsset.getGrade());
        bonusSelf.setOrgPoints(tbMemberAsset.getCurrentPoints());
        bonusSelf.setTradingDate(DateUtil.parseDateTime(dto.getSellDate()));
        bonusSelf.setMoney(dto.getMoney().doubleValue());
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId(tbMemberAsset.getGroupId())
                .mallId(bonusSelf.getMallId()).member(tbMemberAsset).amount(dto.getMoney().toString()).vipcode(tbMemberAsset.getVipcode())
                .remark(IntegralConstant.TAKE_PHOTO).remarkName("拍照积分").shopId(tbBaseShop.getContractNo()).baseShop(tbBaseShop).approved(dto.getApproved())
                .saleNo(dto.getSellNo()).saleDate(StringUtils.isBlank(dto.getSellDate()) ? DateUtil.formatDateTime(new Date()) : dto.getSellDate())
                .createUser(dto.getAuditor()).saleType(SaleTypeEnum.TICKET.getValue()).imageUrl(bonusSelf.getImageUrl()).refund(false).build();
        String smartPosKey = String.format(RedisCacheKey.SALES_NO_KEY, dto.getSellNo());
        if (redisService.setSaleNoIfAbsent(smartPosKey, IntegralConstant.KIP_POINTS)) {
            try {
                points = memberSalePointsProcessService.salePointsProcess(pointsDto);
                // 回填积分调整记录id
                bonusSelf.setIntegralAdjustId(pointsDto.getIntegralAdjustId());
                bonusSelf.setBonus(points);
                // 更新拍照积分状态
                tbPhotoReviewMapper.updateById(bonusSelf);
            } catch (Exception e) {
                log.error("拍照积分审核发生异常{}", e);
                // 积分异常 填充的数据保留，但是要改成锁定了的
                if (Objects.equals(pointsDto.getValidateStep(), 4)){
                    bonusSelf.setStatus(PhotoReviewStatusEnum.TYPE_4.getCode());
                    tbPhotoReviewMapper.updateById(bonusSelf);
                }
                // 说明跟异常积分拦截的数据重复啦
                if (Objects.equals(pointsDto.getValidateStep(), 5)){
                    bonusSelf.setStatus(PhotoReviewStatusEnum.TYPE_2.getCode());
                    tbPhotoReviewMapper.updateById(bonusSelf);
                }
                // 判断是否重复积分
                if (Objects.nonNull(pointsDto.getMemberSale())) {
                    log.info("拍照积分审核发生异常-1.");
                    TbSalesDetail detail = pointsDto.getMemberSale();
                    TbMemberAsset member = tbMemberAssetService.findByVipcodeAndGroupId(detail.getVipcode(), detail.getGroupId());
                    if (Objects.isNull(member)) {
                        throw BizException.error(PointsEnum.BONUS_REPEATED_POINTS_ERROR);
                    } else {
                        throw BizException.error(PointsEnum.BONUS_REPEATED_POINTS_ERROR.getCode(),
                                String.format("该单号已有积分记录，积分店铺：%s，积分会员手机号: %s，请勿重复积分", detail.getShopName(), member.getMobile()));
                    }
                } else {
                    log.info("拍照积分审核发生异常-2.");
                    throw e;
                }
            } finally {
                redisService.delKeys(Collections.singletonList(smartPosKey));
            }
        } else {
            throw BizException.error(PointsEnum.BONUS_RECORD_AUDIT_ERROR);
        }
        return points;
    }

    @Override
    public List<TbPhotoReview> getReviewPage(BonusSelfQueryDto dto) {
        return tbPhotoReviewMapper.getReviewPage(dto);
    }

    @Override
    public TbPhotoReview findByIdAndVipcode(Long id, String vipcode) {
        QueryWrapper<TbPhotoReview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("vipcode", vipcode);
        return tbPhotoReviewMapper.selectOne(queryWrapper);
    }

    @Override
    public TbPhotoReview getForPrevPage(String timestamp, String mallId, String startTime, String endTime) {
        return tbPhotoReviewMapper.getForPrevPage(timestamp, mallId, startTime, endTime);
    }

    @Override
    public TbPhotoReview getForNextPage(String timestamp, String mallId, String startTime, String endTime) {
        return tbPhotoReviewMapper.getForNextPage(timestamp, mallId, startTime, endTime);
    }

    @Override
    public String getNextImage(String timestamp, String mallId, String startTime, String endTime) {
        return tbPhotoReviewMapper.getNextImage(timestamp, mallId, startTime, endTime);
    }

    @Override
    public void fillPhotoViewInfo(TbMemberAsset tbMemberAsset, TbPhotoReview review, TbBaseShop baseShop, Integer points, String pointsDetailId) {
        review.setBonus(points);
        review.setIntegralAdjustId(pointsDetailId);
        // 拍照记录状态变更为已审核
        review.setStatus(PhotoReviewStatusEnum.TYPE_1.getCode());
        review.setOrgPoints(tbMemberAsset.getCurrentPoints());
        review.setOrgGrade(tbMemberAsset.getGrade());
        // 审核时间
        review.setCheckTime(new Date());
        // 填充店铺信息
        this.fillShopInfo(review, baseShop);
        tbPhotoReviewMapper.updateById(review);
    }

    @Async
    @Override
    public void updatePhotoViewRejectReason(TbPhotoReview review, TbSalesDetail memberSale, TbBaseShop baseShop) {
        String rejectReason = String.format("此销售与会员号: %s，所在店铺: %s-%s，销售单号: %s，销售金额: %s的销售记录发生重复积分",
                memberSale.getVipcode(), memberSale.getShopNo(), memberSale.getShopName(), memberSale.getOrderNo(), memberSale.getPayAmount());
        // 更新该笔OCR拍照积分未积分成功的原因
        review.setReason(rejectReason);
        // 审核时间
        review.setCheckTime(new Date());
        // 重复积分，更新拍照积分记录为已驳回
        review.setStatus(PhotoReviewStatusEnum.TYPE_2.getCode());
        // 填充店铺信息
        this.fillShopInfo(review, baseShop);
        // 设置更新时间
        review.setUpdateDate(new Date());
        tbPhotoReviewMapper.updateById(review);
        // 拍照积分-驳回短信发送
        applicationEventPublisher.publishEvent(OcrPhotoRejectEvent.builder().id(review.getId()).build());
    }

    @Override
    public void updatePhotoViewStatus(TbPhotoReview review, PhotoReviewStatusEnum statusEnum) {
        review.setStatus(statusEnum.getCode());
        // 审核时间
        review.setCheckTime(new Date());
        // 补充店铺信息
        if (StringUtils.isNotBlank(review.getShopNo()) && StringUtils.isBlank(review.getShopName())) {
            TbBaseShop baseShop = tbBaseShopService.getByContractNoAndMallId(review.getShopNo(), review.getMallId());
            if (Objects.nonNull(baseShop)) {
                this.fillShopInfo(review, baseShop);
            }
        }
        // 设置更新时间
        review.setUpdateDate(new Date());
        tbPhotoReviewMapper.updateById(review);
        // 拍照积分-驳回短信发送
        applicationEventPublisher.publishEvent(OcrPhotoRejectEvent.builder().id(review.getId()).build());
    }

    /**
     * 填充店铺信息
     * @param review
     * @param baseShop
     */
    private void fillShopInfo(TbPhotoReview review, TbBaseShop baseShop) {
        review.setShopName(StringUtils.isNotBlank(baseShop.getRetailBrandName()) ? baseShop.getRetailBrandName() : baseShop.getShopName());
        String formatIds = Stream.of(baseShop.getFirstFormatCode(), baseShop.getSecondFormatCode(), baseShop.getThirdFormatCode()).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        review.setFormatId(formatIds);
        String formatNames = Stream.of(baseShop.getFirstFormatName(), baseShop.getSecondFormatName(), baseShop.getThirdFormatName()).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        review.setFormatName(formatNames);
    }

    @Override
    public TbPhotoReview findByOcrTaskId(String taskId) {
        return tbPhotoReviewMapper.findByOcrTaskId(taskId);
    }

    @Override
    public TbPhotoReview findByQrCode(String qrCode) {
        return tbPhotoReviewMapper.findByQrCode(qrCode);
    }

    @Override
    public TbPhotoReview checkPhotoReviewRecord(PhotoReviewCheckDto checkDto) {
        return tbPhotoReviewMapper.checkPhotoReviewRecord(checkDto);
    }

    @Override
    public boolean checkQrCodeExists(String qrCode) {
        int count = tbPhotoReviewMapper.checkQrCodeExists(qrCode);
        return count > 0;
    }
}
