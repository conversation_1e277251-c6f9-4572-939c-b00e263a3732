package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.enums.SubscriptionTypeEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.event.EmailApproveSendEvent;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.BonusSelfQueryDto;
import com.kerryprops.kip.service.integral.model.dto.BonusSelfUploadDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.TakePhotoAuditDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.BonusSelfUploadResource;
import com.kerryprops.kip.service.integral.webservice.resource.TakePhotoAuditResource;
import com.kerryprops.kip.service.integral.webservice.resource.TbBonusSelfResponse;
import com.kerryprops.kip.service.integral.webservice.response.PageBonusDetailResponse;
import com.kerryprops.kip.service.integral.webservice.response.TakePhotoAuditResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/27/2023 10:03
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/bonus")
@RestController
@RefreshScope
@RequiredArgsConstructor
@Tag(name = "拍照积分api")
public class TbPhotoPointsController {

    private final TbPhotoReviewService tbPhotoReviewService;
    private final CrmVipcodeService crmVipcodeService;
    private final HiveVasService hiveVasService;
    private final MallConfig mallConfig;
    private final RedisService redisService;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbPointsDetailService tbPointsDetailService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final SmallTicketRecognitionService smallTicketRecognitionService;
    private final Mapper mapper;

    @Value("${crm.image.domain:default}")
    private String imageDomain;

    private static final String HTTPS = "https";

    @GetMapping("/photo/page")
    @Operation(summary="查询拍照积分明细，带分页参数", method = "GET")
    @Parameters({
            @Parameter(name = "state", description = "拍照积分审核状态 1:待审核; 2:已审核", required = false)
    })
    public PageBonusDetailResponse getMemberIntegralAdjustRecord(@CurrentUser LoginUser loginUser,
                                                                 @PageableDefault(page = 0, size = 10) Pageable pageable,
                                                                 @RequestParam(value = "state", required = false) String state) {
        MallItem mallItem = hiveVasService.getMallByLbsId(loginUser.getLbsId());
        if (Objects.isNull(mallItem)) {
            return PageBonusDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).build();
        }
        // 组装查询参数
        BonusSelfQueryDto queryDto = BonusSelfQueryDto.builder().groupId(mallItem.getGroupId())
                .vipcode(crmVipcodeService.getVipcode(mallItem.getGroupId(), loginUser.getCId())).type("3")
                .status(PhotoReviewStatusEnum.TYPE_0.getCode().equals(state) ? PhotoReviewStatusEnum.getPendingApprovalStatus() : PhotoReviewStatusEnum.getAuditedStatus())
                .page(pageable.getPageNumber()).size((pageable.getPageSize() >= 100) ? 50 : pageable.getPageSize()).build();
        int count = tbPhotoReviewService.getBonusTotal(queryDto);
        if (count <= 0) {
            return PageBonusDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).build();
        }
        List<TbPhotoReview> list = tbPhotoReviewService.geTbBonusSelfList(queryDto);
        return this.fillData(list, queryDto, count);
    }

    private PageBonusDetailResponse fillData(List<TbPhotoReview> list, BonusSelfQueryDto queryDto, int count) {
        // 商场id
        List<String> mallIds = list.stream().map(TbPhotoReview::getMallId).filter(StringUtils::isNotBlank).distinct().toList();
        // 积分调整记录ID
        List<String> adjustIds = list.stream().map(TbPhotoReview::getIntegralAdjustId).filter(StringUtils::isNotBlank).distinct().toList();
        // 商场名称
        CompletableFuture<Map<String, String>> mallNameFuture = hiveVasService.getMallName(mallIds);
        // 积分变更明细
        CompletableFuture<Map<String, TbPointsDetail>> integralAdjustFuture = tbPointsDetailService.findByIds(adjustIds);
        // 等待多个任务完成
        CompletableFuture.allOf(integralAdjustFuture, mallNameFuture).join();
        int totalPage = (count % queryDto.getSize() == 0) ? (count / queryDto.getSize()) : (count / queryDto.getSize()) + 1;
        PageBonusDetailResponse page = PageBonusDetailResponse.builder().totalPages(totalPage).totalElements(count).build();
        try {
            Map<String, String> mallNameMap = mallNameFuture.get();
            Map<String, TbPointsDetail> detailMap = integralAdjustFuture.get();
            page.setContent(list.stream().map(it -> {
                TbBonusSelfResponse response = this.parseItem(it);
                Optional.ofNullable(MapUtils.getString(mallNameMap, it.getMallId())).ifPresent(response::setMallName);
                if (Objects.equals(it.getStatus(), PhotoReviewStatusEnum.TYPE_1.getCode())) {
                    this.fillPointsInfo(response, detailMap, it.getIntegralAdjustId());
                } else if (Objects.equals(it.getStatus(), PhotoReviewStatusEnum.TYPE_2.getCode())) {
                    response.setDesc(String.format("积分审核未通过：%s", it.getReason()));
                } else if (Objects.equals(it.getStatus(), PhotoReviewStatusEnum.TYPE_6.getCode())) {
                    if (StringUtils.isNotBlank(it.getIntegralAdjustId())) {
                        this.fillPointsInfo(response, detailMap, it.getIntegralAdjustId());
                        // 重置状态
                        response.setState(PhotoReviewStatusEnum.TYPE_1.getCode());
                    } else {
                        response.setDesc(String.format("您于%s上传了一张小票，待审核", response.getTitle()));
                    }
                } else {
                    response.setDesc(String.format("您于%s上传了一张小票，待审核", response.getTitle()));
                }
                if (!mallConfig.isKO(response.getGroupId())) {
                    response.setQrcode(null);
                }
                // 回填状态名称
                Optional.ofNullable(PhotoReviewStatusEnum.getName(it.getStatus())).ifPresent(response::setStatusName);
                // 填充图片域名地址
                if (StringUtils.isNotBlank(response.getImgUrl()) && !response.getImgUrl().contains(HTTPS)) {
                    response.setImgUrl(imageDomain + response.getImgUrl());
                }
                return response;
            }).collect(Collectors.toList()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return page;
    }

    /**
     * 填充拍照记录获得积分信息
     * @param response
     * @param detailMap
     * @param integralAdjustId
     */
    private void fillPointsInfo(TbBonusSelfResponse response,
                                Map<String, TbPointsDetail> detailMap,
                                String integralAdjustId) {
        TbPointsDetail detail = MapUtils.getObject(detailMap, integralAdjustId);
        if (Objects.nonNull(detail)) {
            if (StringUtils.isNotBlank(detail.getReasonDesc())) {
                response.setDesc(String.format("您通过%s获得%s积分，总积分%s", detail.getReasonDesc(), detail.getAmount() + "", detail.getCurrentPoints() + ""));
            } else {
                response.setDesc(String.format("您于%s获得%s积分，总积分%s", DateUtil.formatDateTime(detail.getCreateDate()), detail.getAmount() + "", detail.getCurrentPoints() + ""));
            }
            response.setIntegralNumber(detail.getAmount());
        }
    }

    private TbBonusSelfResponse parseItem(TbPhotoReview self) {
        return TbBonusSelfResponse.builder().id(String.valueOf(self.getId())).title(DateUtil.formatDateTime(self.getUploadDate()))
                .state(self.getStatus()).imgUrl(self.getImageUrl()).money(self.getMoney()).storeId(self.getShopNo()).groupId(self.getGroupId())
                .storeName(self.getShopName()).mallId(self.getMallId()).reason(self.getReason()).qrcode(self.getQrcode()).build();
    }

    @PostMapping("/photo/upload")
    @Operation(summary="拍照积分上传API", method = "POST")
    public void bonusSelfUpload(@CurrentUser LoginUser loginUser, @Valid @RequestBody BonusSelfUploadResource resource) {
        String accessLimitKey = String.format(RedisCacheKey.PHOTO_POINTS_ACCESS_LIMIT_KEY, loginUser.getLbsId(), loginUser.getCId());
        Boolean absent = redisService.setIfAbsentWithExpire(accessLimitKey, loginUser.getCId(), 1L, TimeUnit.SECONDS);
        if (Objects.isNull(absent) || Boolean.FALSE.equals(absent)) {
            throw BizException.error(PointsEnum.ACCESS_TOO_FREQUENTLY);
        }
        MallItem mallItem = hiveVasService.getMallByLbsId(loginUser.getLbsId());
        if (Objects.isNull(mallItem)) {
            throw BizException.error(PointsEnum.MALL_NOT_EXISTS);
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByKipUserIdAndGroupIdWithException(loginUser.getCId(), mallItem.getGroupId());
        // 是KO，则走OCR拍照积分模式，解决前端无法确定是否是OCR拍照积分问题
        if (mallConfig.isKO(mallItem.getGroupId())) {
            smallTicketRecognitionService.matchUploadImgToHeHe(tbMemberAsset, mallItem, resource.getImgUrl());
        } else {
            // 保存记录
            BonusSelfUploadDto uploadDto = BonusSelfUploadDto.builder().type("3").status("1").imageUrl(resource.getImgUrl()).mallId(mallItem.getMallId())
                    .groupId(mallItem.getGroupId()).vipcode(tbMemberAsset.getVipcode()).mobile(loginUser.getPhoneNumber())
                    .orgGrade(tbMemberAsset.getGrade()).orgPoints(tbMemberAsset.getCurrentPoints()).build();
            // 保存上传记录
            tbPhotoReviewService.saveRecord(uploadDto);
            // 拍照积分审核邮件提醒事件
            applicationEventPublisher.publishEvent(EmailApproveSendEvent.builder().groupId(mallItem.getGroupId()).mallId(mallItem.getMallId()).subscriptionType(SubscriptionTypeEnum.POINTS_PHOTO.getType()).build());
        }
    }

    @PutMapping("/photo/audit")
    @Operation(summary="CRM Admin端拍照积分审核", method = "PUT")
    public TakePhotoAuditResponse photoAudit(@CurrentUser LoginUser loginUser, @RequestBody @Valid TakePhotoAuditResource resource) {
        TakePhotoAuditDto auditDto = mapper.map(resource, TakePhotoAuditDto.class);
        if (StringUtils.isNotBlank(loginUser.getNickName())) {
            auditDto.setAuditor(loginUser.getNickName());
        }
        Integer points = tbPhotoReviewService.auditRecord(auditDto);
        return TakePhotoAuditResponse.builder().points(points).build();
    }

}
