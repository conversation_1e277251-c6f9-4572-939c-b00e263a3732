package com.kerryprops.kip.service.integral.model.dto;



import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - saas
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/29/2023 15:51
 **********************************************************************************************************************/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema( name = "品牌导览")
public class BrandGuideRespDto implements Serializable {

    @Schema(name = "id")
    private String id;

    @Schema(name = "集团id")
    private String groupId;

    @Schema(name = "店铺名称")
    private String shopName;

    @Schema(name = "店铺号")
    private String shopNo;

    @Schema(name = "logo")
    private String logoPic;

    @Schema(name = "背景图")
    private String backgroundPic;

    @Schema(name = "楼层id")
    private String floorId;

    @Schema(name = "业态id")
    private String classificationId;

    @Schema(name = "排序")
    private Integer sort;

    @Schema(name = "是否显示0不显示1显示（不影响创建品牌导览）")
    private Integer isShow;

    @Schema(name = "营业时间")
    private String businessHours;

    @Schema(name = "门店位置")
    private String shopLocation;

    @Schema(name = "门店详情（富文本框）")
    private String shopDetail;

    @Schema(name = "所属商场id")
    private String lbsId;

    @Schema(name = "楼层名字")
    private String floorName;

    @Schema(name = "业态名")
    private String classificationName;

    @Schema(name = "楼层ids")
    private String floorIds;

    @Schema(name = "是否显示在首页,true显示,false不显示")
    private Boolean homePageShow;

    @Schema(name = "是否可跳转详情开关,true 可跳转,false 不可跳转")
    private Boolean redirectDetailSwitch;

    @Schema(name = "电话")
    private String phone;

    @Schema(name = "是否已收藏,true 已收藏 false 未收藏")
    private Boolean collectFlag;

    @Schema(name = "单元号")
    private String roomNo;

}
