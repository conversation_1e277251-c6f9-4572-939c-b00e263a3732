package com.kerryprops.kip.service.integral.common.enums;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum WxTemplatePlaceholderEnum {
    MOBILE("{MOBILE}", "mobile", "手机号"),
    MALL_NAME("{MALL_NAME}", "mallName", "商场名字"),
    POINTS_CHANGE_NUMBER("{POINTS_CHANGE_NUMBER}", "number", "0"),
    MEMBER_LEVEL("{MEMBER_LEVEL}", "memberGradeDesc", "会员等级"),
    UPDATE_DATE("{UPDATE_DATE}", "updateDate", "2099-01-01"),
    UPDATE_DATE_2("{UPDATE_DATE_2}", "updateDate", "2099-01-01"),
    EVENT_TIME("{EVENT_TIME}", "updateDate", "2099-01-01"),
    MEMBER_POINTS("{MEMBER_POINTS}", "currentIntegral", "0"),
    MEMBER_CARD_NO("{MEMBER_CARD_NO}", "vipcode", "10001"),
    JOIN_TIME("{JOIN_TIME}", "updateDate", "2099-01-01"),
    VIP_CODE("{VIP_CODE}", "vipcode", "10001"),
    MEMBER_GRADE("{MEMBER_GRADE}", "memberGradeDesc", "01"),
    CURRENT_INTEGRAL("{CURRENT_INTEGRAL}", "currentIntegral", "0"),
    MEMBER_NICKNAME("{MEMBER_NICKNAME}", "nickName", "会员用户"),
    NICKNAME("{NICKNAME}", "nickName", "会员用户"),
    POINTS_NUMBER("{POINTS_NUMBER}", "number", "0"),
    SMS_PREFIX("{SMS_PREFIX}", "smsPrefix", "【嘉里建设】"),
    UPLOAD_TIME("{UPLOAD_TIME}", "updateDate", "2099-01-01"),
    REJECT_REASON("{REJECT_REASON}", "remark", "发送短息"),
    POINTS_INTERCEPT_REASON("{POINTS_INTERCEPT_REASON}", "remark", "积分拦截"),
    QH_PREFIX("{QH_PREFIX}", "QH_PREFIX", "【前海嘉里中心嘉湾汇】"),
    ABB_QHKC("{ABB_QHKC}", "ABB_QHKC", "QHKC"),
    FIRST("{FIRST}", "FIRST", "first"),
    KEYWORD("{KEYWORD}", "KEYWORD", "keyword"),
    REMARK("{REMARK}", "REMARK", "remark"),
    vipcode("{vipcode}","vipcode", "10001"),
    member_grade("{member_grade}","memberGradeDesc", "01"),
    currentintegral("{currentintegral}", "currentIntegral", "0"),
    update_date("{update_date}", "updateDate", "2099-01-01"),
    updateDate("{updateDate}", "updateDate", "2099-01-01"),
    OPEN_TIME("{OPEN_TIME}", "updateDate", "2099-01-01"),
    number("{number}", "number", "0"),
    TRADING_HOURS("{TRADING_HOURS}", "updateDate", "2099-01-01");

    /**
     * 占位符名称
     */
    private final String name;

    /**
     * 对应字段名称
     */
    private final String field;

    /**
     * 描述
     */
    private final String desc;

    WxTemplatePlaceholderEnum(String name, String field, String desc) {
        this.name = name;
        this.field = field;
        this.desc = desc;
    }

    public static List<WxTemplatePlaceholderEnum> match(String name) {
        return Arrays.stream(WxTemplatePlaceholderEnum.values()).filter(o -> name.contains(o.getName())).collect(Collectors.toList());
    }
}
