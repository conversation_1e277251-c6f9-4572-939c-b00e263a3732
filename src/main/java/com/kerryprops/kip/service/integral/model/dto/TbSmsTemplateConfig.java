package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 08/15/2022 14:18
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbSmsTemplateConfig implements Serializable {
    /**
     * 短信模板id
     */
    private Long id;

    /**
     * 短信模板名称
     */
    private String name;

    /**
     * 商场编号
     */
    private String mallid;

    /**
     * 集团id
     */
    private String groupId;

    /**
     * 短信发送节点(0:注册,1:登陆,2:活动取消(活动取消审核通过),3:活动报名成功,4:活动取消,5:活动即将开始,6:活动取消(会员S端被取消报名),7:会员等级变更,8:积分变更,9:积分即将过期,10:电子券兑换成功,11:电子券核销成功,12:电子券即将过期)
     */
    private Integer smsSendNode;

    /**
     * 会员卡等级(1:银卡,2金卡,3铂金卡)
     */
    private String memberGrade;

    /**
     * 生效时间
     */
    private Date startDate;

    /**
     * 失效时间
     */
    private Date endDate;

    /**
     * 短信预览
     */
    private String smsPreview;

    /**
     * 模板生效状态(0:(默认)生效状态,1:不生效）
     */
    private Integer status;

    /**
     * 生效时间是否限制,0:(默认)不限制,1限制
     */
    private Integer startDateStatus;

}