package com.kerryprops.kip.service.integral.webservice.resource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 15:56
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbOnlineShopResource implements Serializable {

    private String groupId;

    private List<String> mallIdList;

    private List<String> businessTypes;

    private int page;

    private int size;

    private int offset;

}
