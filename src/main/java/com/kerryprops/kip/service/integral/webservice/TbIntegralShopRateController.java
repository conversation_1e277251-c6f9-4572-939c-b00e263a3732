package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import com.kerryprops.kip.service.integral.service.HiveVasService;
import com.kerryprops.kip.service.integral.service.TbIntegralShopRateService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.webservice.resource.CommonResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralShopRateResource;
import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbIntegralShopRateResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/13/2023 14:32
 **********************************************************************************************************************/

@Slf4j
@Hidden
@RestController
@RequiredArgsConstructor
@RequestMapping("/integral/shopRate")
public class TbIntegralShopRateController {
    private final TbIntegralShopRateService tbIntegralShopRateService;
    private final Mapper mapper;
    private final HiveVasService hiveVasService;
    private final TbMemberGradeService tbMemberGradeService;

    @GetMapping("/shopRatePage")
    @Operation(summary="分页查询店铺积分配置", method = "GET")
    public Page<TbIntegralShopRateResponse> shopRatePage(@RequestParam String groupId,
                                                         @RequestParam(required = false) String mallId,
                                                         @RequestParam(required = false) String shopId,
                                                         @PageableDefault(page = 1, size = 10) Pageable pageable) {
        int page = pageable.getPageNumber() <= 0 ? 0: pageable.getPageNumber() - 1;
        int size = pageable.getPageSize();
        List<String> mallIds = StringUtils.isBlank(mallId) ? Collections.emptyList() : CharSequenceUtil.split(mallId, CommonSeparators.COMMA_SEPARATOR);
        int total = tbIntegralShopRateService.getTotal(groupId, mallIds, shopId);
        if (total <= 0) {
            return PageDTO.of(page, size, total);
        }
        List<TbIntegralShopRate> list = tbIntegralShopRateService.shopRatePage(groupId, mallIds, shopId, page, size);
        Page<TbIntegralShopRateResponse> pageData = Page.of(page, size, total);
        if (CollectionUtils.isEmpty(list)) {
            pageData.setRecords(Collections.emptyList());
            return pageData;
        }
        Map<String, TenantInfoVo> shopMap = hiveVasService.getTenantInfoByShopNos(list.stream().map(TbIntegralShopRate::getShopId).distinct().collect(Collectors.toList()));
        List<TbIntegralShopRateResponse> records = list.stream().map(it -> mapper.map(it,TbIntegralShopRateResponse.class)).collect(Collectors.toList());
        TenantInfoVo tenant;
        for (TbIntegralShopRateResponse rate : records) {
            if (StringUtils.isBlank(rate.getShopId())) {
                continue;
            }
            tenant = MapUtils.getObject(shopMap, rate.getShopId());
            if (Objects.nonNull(tenant)) {
                // 店铺别名
                rate.setShopAliasName(tenant.getShopName());
                // 店铺名
                rate.setShopName(tenant.getBrandName());
                // 店铺品牌名称
                rate.setRetailBrandName(tenant.getRetailBrandName());
            } else {
                // 店铺别名
                rate.setShopAliasName(rate.getShopName());
            }
        }
        pageData.setRecords(records);
        return pageData;
    }

    @PostMapping("/saveOrUpdateShopRate")
    @Operation(summary="新增或修改店铺积分配置", method = "POST")
    public CommonResponse saveOrUpdateShopRate(@RequestBody @Validated TbIntegralShopRateResource resource) {
        if (CollectionUtils.isEmpty(resource.getList())) {
            return CommonResponse.builder().success(Boolean.FALSE).build();
        }
        // 积分比例统一
        if (1 == resource.getIsConsistent()) {
            List<GradeIntegralResponse> list = this.setPointsList(resource.getGroupId(), resource.getList().get(0));
            resource.setList(list);
        }
        tbIntegralShopRateService.saveOrUpdateShopRate(resource);
        return CommonResponse.builder().success(Boolean.TRUE).build();
    }

    private List<GradeIntegralResponse> setPointsList(String groupId, GradeIntegralResponse gradeIntegralResponse) {
        List<GradeIntegralResponse> gradeIntegralList = new ArrayList<>();
        List<TbMemberGrade> gradeList = tbMemberGradeService.queryGradeSortAscByGroupId(groupId);
        if (CollectionUtils.isEmpty(gradeList)) {
            throw BizException.error(PointsEnum.BRAND_GRADE_NOT_EXISTS);
        }
        for (TbMemberGrade grade : gradeList) {
            GradeIntegralResponse gradeResponse = GradeIntegralResponse
                    .builder()
                    .gradeId(grade.getId())
                    .grade(grade.getCode())
                    .gradeName(grade.getName())
                    .pointNum(gradeIntegralResponse.getPointNum())
                    .money(gradeIntegralResponse.getMoney())
                    .build();
            gradeIntegralList.add(gradeResponse);
        }
        return gradeIntegralList;
    }

    @GetMapping("/getShopRateList")
    @Operation(summary="查询店铺积分配置", method = "GET")
    public List<TbIntegralShopRateResponse> getShopRateList(@RequestParam("mallId") String mallId,
                                                            @RequestParam("shopId") String shopId,
                                                            @RequestParam("groupId") String groupId) {
        TbIntegralShopRate rate = TbIntegralShopRate.builder()
                .mallId(mallId)
                .shopId(shopId)
                .groupId(groupId)
                .build();
        List<TbIntegralShopRate> list = tbIntegralShopRateService.getShopRateList(rate);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(it -> mapper.map(it, TbIntegralShopRateResponse.class)).toList();
    }

    @DeleteMapping("/deleteShopRateList")
    @Operation(summary="批量删除店铺积分配置", method = "DELETE")
    public CommonResponse deleteShopRateList(@RequestBody Long[] ids) {
        if (null == ids || ids.length <= 0) {
            throw BizException.error(PointsEnum.INTEGRAL_RATE_ID_ERROR);
        }
        tbIntegralShopRateService.deleteShopRateList(ids);
        return CommonResponse.builder().success(Boolean.TRUE).build();
    }

}
