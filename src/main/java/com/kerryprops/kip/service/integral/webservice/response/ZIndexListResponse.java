package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 自定义页面列表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-15
 */
@Data
@Schema(name = "自定义页面列表")
public class ZIndexListResponse implements Serializable {

	@Schema(name = "主键")
	private String id;

	@Schema(name = "页面名称")
	private String pageName;

	@Schema(name = "集团编号")
	private String groupId;

	@Schema(name = "商场编号")
	private String mallid;

	@Schema(name = "商场")
	private String mallId;

	@Schema(name = "是否启用：0关闭 1启用")
	private String openFlag;
	
	@Schema(name = "页面类型 0首页 1自定义2我的")
	private String type;

	@Schema(name = "版本号")
	private Integer	versions;
	
	@Schema(name = "我的订单是否显示")
	private Integer	myOrderShow;
	
	@Schema(name = "头部导航开关0关1开")
	private Integer	headNavigationShow;
	
	@Schema(name = "背景图地址")
	private String	backgroundImageUrl;
	
	@Schema(name = "背景图开关0关1开")
	private Integer	backgroundImageFlag;
	
	@Schema(name = "导航条字体颜色")
	private String 	headNavigationColor;
	
	@Schema(name = "悬浮胶囊颜色（0黑1白）")
	private Integer capsuleColor;
	
	@Schema(name = "下划线开关（0关1开）")
	private Integer underline;
	
	@Schema(name = "背景图全图开关（0关1开）")
	private Integer completeFlag;
	
	private Integer myWaterfallShow;

	@Schema(name = "广告图开关    0关1开")
	private Integer myAdShow;

	/**
	 * 广告图片地址
	 */
	@Schema(name = "广告图片地址")
	private String myAdUrl;

	@Schema(name = "资源列表每行显示个数")
	private Integer showLineNum;

	@Schema(name = "徽章是否展示，0:不展示; 1:展示")
	private Integer showBadge;

	/**
	 * 显示悬浮按钮开关    0关1开
	 */
	private Integer floatingBtnShow;
	/**
	 * 悬浮按钮图片地址
	 */
	private String floatingBtnUrl;
	/**
	 * 显示卡等（针对页面类型为我的），可多个，逗号拼接
	 */
	private String myShowGrade;

	private String myAdUrlEn;

	private List<SelfDefiningModuleResponse> moduleList;

	private String myPageId;

	private String isVVIP;

	@Schema(name = "我的页面-显示我的徽章列表")
	private BadgeListResponse myBadge;
}