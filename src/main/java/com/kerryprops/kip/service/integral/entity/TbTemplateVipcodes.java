package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 模板消息发送会员详情
 *
 * <AUTHOR> 
 * @since 1.0.0 2020-08-31
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_template_vipcodes")
public class TbTemplateVipcodes implements Serializable {

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

    /**
     * 模板活动ID
     */
	@TableField("template_activityid")
	private String templateActivityId;

    /**
     * 活动名称
     */
	@TableField("activity_name")
	private String activityName;
    /**
     * 会员号
     */
	@TableField("vipcode")
	private String vipcode;

    /**
     * 手机号
     */
	@TableField("mobile")
	private String mobile;

    /**
     * 会员openID
     */
	@TableField("openid")
	private String openId;

    /**
     * 点击状态(0:未点击;1:已点击)
     */
	@TableField("click_state")
	private Integer clickState;

    /**
     * 发送状态(0:未发送;1:已发送;2:发送失败)
     */
	@TableField("send_state")
	private Integer sendState;

    /**
     * 商场号
     */
	@TableField("mallid")
	private String mallId;

    /**
     * 小程序或公众号原始号
     */
	@TableField("account_id")
	private String accountId;

    /**
     * 发送时间
     */
	@TableField("send_date")
	private Date sendDate;

    /**
     * 模板消息类型(1:小程序;2:公众号)
     */
	@TableField("template_type")
	private Integer templateType;

	/**
	 * 实际发送模板内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 发送报错时微信源码
	 */
	@TableField("message")
	private String message;

	/**
	 * 创建者
	 */
	@TableField(value = "creator", fill = FieldFill.INSERT)
	private Long creator;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_date", fill = FieldFill.INSERT)
	private Date createDate;

    /**
     * 更新时间
     */
	@TableField(value = "update_date", fill = FieldFill.UPDATE)
	private Date updateDate;

    /**
     * 更新人
     */
	@TableField(value = "updater", fill = FieldFill.UPDATE)
	private Long updater;

    /**
     * 集团ID
     */
	@TableField("group_id")
	private String groupId;

	/**
	 * 模板名称
	 */
	@TableField("title")
	private String title;

	/**
	 * 发送节点
	 */
	@TableField("send_node")
	private String sendNode;

	/**
	 * 模版id
	 */
	@TableField("template_id")
	private Long templateId;

	/**
	 * 发送模板消息时需要替换参数的map
	 */
	@TableField("params")
	private String params;

}