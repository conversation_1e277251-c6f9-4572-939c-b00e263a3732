package com.kerryprops.kip.service.integral.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description
 * @createDate 2022/10/26
 * @updateDate 2022/10/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema( name = "返回数据类")
public class CipResultDto<T> implements Serializable {

    /** 响应状态. */
    @Schema( name = "响应状态")
    private Integer status;

    /** 响应信息. */
    @Schema( name = "响应信息")
    private String msg;

    /** 具体的内容. */
    @Schema( name = "响应体")
    private T data;
}
