package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.service.TbIntegralCategoryRateService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.webservice.resource.CommonResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralCategoryRateResource;
import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbIntegralCategoryRateResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/17/2023 14:32
 **********************************************************************************************************************/

@Slf4j
@Hidden
@RestController
@RequiredArgsConstructor
@RequestMapping("/integral/categoryRate")
public class TbIntegralCategoryRateController {
    private final TbIntegralCategoryRateService tbIntegralCategoryRateService;
    private final TbMemberGradeService tbMemberGradeService;
    private final Mapper mapper;

    @GetMapping("/categoryRatePage")
    @Operation(summary="分页查询业态积分配置", method = "GET")
    public Page<TbIntegralCategoryRateResponse> categoryRatePage(@RequestParam("groupId") String groupId,
                                                                 @RequestParam(required = false) String mallId,
                                                                 @RequestParam(required = false) String categoryId,
                                                                 @PageableDefault(page = 1, size = 10) Pageable pageable) {
        int page = pageable.getPageNumber() <= 0 ? 0: pageable.getPageNumber() - 1;
        int size = pageable.getPageSize();
        int total = tbIntegralCategoryRateService.getTotal(groupId,categoryId, CharSequenceUtil.split(mallId, ","));
        if (total <= 0) {
            return Page.of(page, size, total);
        }
        List<TbIntegralCategoryRate> list = tbIntegralCategoryRateService.categoryRatePage(groupId, categoryId, CharSequenceUtil.split(mallId, ","), page, size);
        Page<TbIntegralCategoryRateResponse> pageData = Page.of(page, size, total);
        if (CollectionUtils.isEmpty(list)) {
            pageData.setRecords(Collections.emptyList());
            return pageData;
        }
        this.charTransform(list);
        pageData.setRecords(list.stream().map(it -> mapper.map(it,TbIntegralCategoryRateResponse.class)).collect(Collectors.toList()));
        return pageData;
    }

    private void charTransform(List<TbIntegralCategoryRate> list) {
        list.forEach(rate -> {
            String name = rate.getCategoryName();
            if (StringUtils.isNotBlank(name) && name.contains("&amp;")) {
                rate.setCategoryName(name.replaceAll("&amp;", "&"));
            }
        });
    }

    @PostMapping("/saveOrUpdateCategoryRate")
    @Operation(summary="新增或修改业态积分配置", method = "POST")
    public CommonResponse saveOrUpdateCategoryRate(@RequestBody @Validated TbIntegralCategoryRateResource resource) {
        if (CollectionUtils.isEmpty(resource.getList())) {
            return CommonResponse.builder().success(Boolean.FALSE).build();
        }
        // 积分比例统一
        if (1 == resource.getIsConsistent()) {
            List<GradeIntegralResponse> list = this.setPointsList(resource.getGroupId(), resource.getList().get(0));
            resource.setList(list);
        }
        tbIntegralCategoryRateService.saveOrUpdateCategoryRate(resource);
        return CommonResponse.builder().success(Boolean.TRUE).build();
    }

    private List<GradeIntegralResponse> setPointsList(String groupId, GradeIntegralResponse gradeIntegralResponse) {
        List<GradeIntegralResponse> gradeIntegralList = new ArrayList<>();
        List<TbMemberGrade> gradeList = tbMemberGradeService.queryGradeSortAscByGroupId(groupId);
        if (CollectionUtils.isEmpty(gradeList)) {
            throw BizException.error(PointsEnum.BRAND_GRADE_NOT_EXISTS);
        }
        for (TbMemberGrade grade : gradeList) {
            GradeIntegralResponse gradeResponse = GradeIntegralResponse
                    .builder()
                    .gradeId(grade.getId())
                    .grade(grade.getCode())
                    .gradeName(grade.getName())
                    .pointNum(gradeIntegralResponse.getPointNum())
                    .money(gradeIntegralResponse.getMoney())
                    .build();
            gradeIntegralList.add(gradeResponse);
        }
        return gradeIntegralList;
    }

    @GetMapping("/getCategoryRateList")
    @Operation(summary="查询业态积分配置", method = "GET")
    public List<TbIntegralCategoryRateResponse> getCategoryRateList(@RequestParam(required = false) String categoryId,
                                                                    @RequestParam(required = false) String mallId,
                                                                    @RequestParam(required = false) String groupId) {
        TbIntegralCategoryRate rate = TbIntegralCategoryRate.builder()
                .categoryId(categoryId)
                .mallId(mallId)
                .groupId(groupId)
                .build();
        List<TbIntegralCategoryRate> list = tbIntegralCategoryRateService.getCategoryRateList(rate);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        this.charTransform(list);
        return list.stream().map(it -> mapper.map(it, TbIntegralCategoryRateResponse.class)).toList();
    }

    @DeleteMapping("/deleteCategoryRateList")
    @Operation(summary="批量删除业态积分配置", method = "DELETE")
    public CommonResponse deleteCategoryRateList(@RequestBody Long[] ids) {
        if (null == ids || ids.length == 0) {
            throw BizException.error(PointsEnum.INTEGRAL_RATE_ID_ERROR);
        }
        tbIntegralCategoryRateService.deleteCategoryRateList(ids);
        return CommonResponse.builder().success(Boolean.TRUE).build();
    }

}
