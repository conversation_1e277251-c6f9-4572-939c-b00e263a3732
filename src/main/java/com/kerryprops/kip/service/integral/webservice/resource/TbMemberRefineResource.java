package com.kerryprops.kip.service.integral.webservice.resource;



import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/17/2022 13:55
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "完善信息页面配置类")
public class TbMemberRefineResource implements Serializable {

    @Schema( description = "配置ID")
    private String id;

    @Schema( description = "商场号")
    private String mallId;

    @Schema( description = "集团id")
    private String groupId;

    @Schema( description = "页面头图图片地址")
    private String topImg;

    @Schema( description = "简介")
    private String synopsis;

    @Schema( description = "完善信息赠送积分数")
    private Integer integral;

    @Schema( description = "配置对应的字段列表")
    private List<TbMemberRefineDetailResource> fieldDTOS;

    @Schema( description = "配置对应的标签列表")
    private List<TbMemberRefineDetailResource> labelDTOS;

}
