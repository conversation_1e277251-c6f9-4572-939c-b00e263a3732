package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-05-15 16:31:50
 */
@Data
@TableName("tb_ticket_membergrade")
public class TicketMemberGrade implements Serializable {
	private static final long serialVersionUID = 1L;
	
	/**
	 * id
	 */
	private String id;
	/**
	 * 等级编码
	 */
	private String membergrade;
	/**
	 * 等级描述
	 */
	private String desc;
	/**
	 * 积分
	 */
	private Integer integral;
	/**
	 * 现金
	 */
	private Double money;
	/**
	 * 电子券id
	 */
	private String ticketid;
	/**
	 * 创建时间
	 */
	private Date createdate;
	/**
	 * 更新时间
	 */
	private Date updatedate;
	/**
	 * 创建人
	 */
	private String createuser;
	/**
	 * 更新人
	 */
	private String updateuser;
	/**
	 * 创建人
	 */
	private Long creator;
	/**
	 * 更新人
	 */
	private Long updater;
	/**
	 * 集团id
	 */
	private String groupid;
	/**
	 * 部门id
	 */
	private String deptid;

	/**
	 * 购买限制数量
	 */
	private Integer purchaseLimitNum;
	/**
	 * 购买限制类型
	 */
	private Integer purchaseLimitType;

}
