package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeMapper;
import com.kerryprops.kip.service.integral.model.dto.TbMemberGradeCacheDto;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeWithCacheService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 14:45
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbMemberGradeServiceImpl extends ServiceImpl<TbMemberGradeMapper, TbMemberGrade> implements TbMemberGradeService {

    private final TbMemberGradeMapper tbMemberGradeMapper;
    private final TbMemberGradeWithCacheService tbMemberGradeWithCacheService;

    @Override
    public List<TbMemberGrade> queryGradeSortAscByGroupId(String groupId) {
        return tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(groupId);
    }

    @Override
    public CompletableFuture<Map<String, String>> getMapFutureByGroupId(String groupId) {
        List<TbMemberGrade> grades = tbMemberGradeMapper.getGradeSortDescByGroupId(groupId);
        if (CollectionUtils.isEmpty(grades)) {
            return CompletableFuture.completedFuture(MapUtil.empty());
        }
        return CompletableFuture.completedFuture(grades.stream().collect(Collectors.toMap(TbMemberGrade::getCode, TbMemberGrade::getName, (v1, v2) -> v1)));
    }

    @Override
    public List<TbMemberGrade> queryGradeSortDescByGroupId(String groupId) {
        return tbMemberGradeMapper.getGradeSortDescByGroupId(groupId);
    }

    @Override
    public List<TbMemberGrade> queryGradeSortDescWithoutCacheByGroupId(String groupId) {
        return tbMemberGradeMapper.getGradeSortDescByGroupId(groupId);
    }

    @Override
    public List<TbMemberGrade> queryGradeSortAscWithoutCacheByGroupId(String groupId) {
        return tbMemberGradeMapper.getGradeSortAscByGroupId(groupId);
    }

    @Override
    public List<TbMemberGrade> getGradeSortAscByGroupIdWithException(String groupId) {
        List<TbMemberGrade> grades = tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(groupId);
        if (CollectionUtils.isEmpty(grades)) {
            throw BizNotFoundException.error(PointsEnum.BRAND_GRADE_NOT_EXISTS);
        }
        return grades;
    }

    @Override
    public TbMemberGrade queryByGroupIdAndGrade(String groupId, String grade) {
        return tbMemberGradeMapper.getByGradeAndGroupId(grade, groupId);
    }

    @Override
    public TbMemberGrade queryMinGroupGrade(String groupId) {
        List<TbMemberGrade> grades = tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(groupId);
        return CollectionUtils.isEmpty(grades) ? null : grades.stream().min(Comparator.comparing(TbMemberGrade::getSort)).orElse(null);
    }

    @Override
    public TbMemberGrade queryMaxGroupGrade(String groupId) {
        List<TbMemberGrade> grades = tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(groupId);
        return CollectionUtils.isEmpty(grades) ? null : grades.stream().max(Comparator.comparing(TbMemberGrade::getSort)).orElse(null);
    }

    @Override
    public String getGroupMiniGradeCode(String groupId) {
        TbMemberGrade minGrade = tbMemberGradeMapper.getMinGradeByGroupId(groupId);
        return Objects.nonNull(minGrade) ? minGrade.getCode() : null;
    }

    @Override
    public TbMemberGradeCacheDto getGradeList(String groupId) {
        List<TbMemberGrade> grades = tbMemberGradeWithCacheService.queryGradeSortAscByGroupId(groupId);
        if (CollectionUtils.isEmpty(grades)) {
            return null;
        }
        return TbMemberGradeCacheDto.builder().list(grades).build();
    }
}

