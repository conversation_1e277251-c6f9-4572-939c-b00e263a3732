package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_email_template_config
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_email_template_config")
public class TbEmailTemplateConfig implements Serializable {
    /**
     * 邮件模板id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 邮件模板名称
     */
    @TableField("name")
    private String name;

    /**
     * 商场编号
     */
    @TableField("mallid")
    private String mallId;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 邮件发送节点(0:注册,1:登陆,2:活动取消(活动取消审核通过),3:活动报名成功,4:活动取消,5:活动即将开始,6:活动取消(会员S端被取消报名),7:会员等级变更,8:积分变更,9:积分即将过期,10:电子券兑换成功,11:电子券核销成功,12:电子券即将过期)
     */
    @TableField("email_send_node")
    private Integer emailSendNode;

    /**
     * 会员卡等级(1:银卡,2金卡,3铂金卡)
     */
    @TableField("memberGrade")
    private String memberGrade;

    /**
     * 生效时间
     */
    @TableField("memberGrade")
    private Date startDate;

    /**
     * 失效时间
     */
    @TableField("memberGrade")
    private Date endDate;

    /**
     * 邮件主题
     */
    @TableField("memberGrade")
    private String emailSubject;

    /**
     * 邮件内容
     */
    @TableField("memberGrade")
    private String emailContent;

    /**
     * 生效状态(0:生效,1:不生效）
     */
    @TableField("memberGrade")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("memberGrade")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField("memberGrade")
    private Date updateDate;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 创建者id
     */
    @TableField("creator")
    private Long creator;

    /**
     * 更新人id
     */
    @TableField("updater")
    private Long updater;

    /**
     * 生效时间是否限制,0:(默认)不限制,1限制
     */
    @TableField("start_date_status")
    private Integer startDateStatus;

}