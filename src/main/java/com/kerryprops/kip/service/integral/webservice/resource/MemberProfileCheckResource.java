package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/19/2022 12:28
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员完善信息检查类")
public class MemberProfileCheckResource implements Serializable {

    @NotBlank
    @Schema( description = "集团ID")
    private String brandId;

    @NotBlank
    @Schema( description = "商场ID")
    private String lbsId;

    @NotBlank
    @Schema( description = "profile用户ID")
    private String kipUserId;

    @Schema( description = "属性标签")
    private String labels;

}
