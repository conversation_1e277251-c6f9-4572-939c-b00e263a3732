package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 自定义页面资源表
 *
 * <AUTHOR> 
 * @since 1.0.0 2020-07-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "自定义页面资源表")
public class SelfDefiningContextResource implements Serializable {
    private static final long serialVersionUID = 1L;

	@Schema(name = "主键")
	private String id;

	@Schema(name = "集团编号")
	private String groupId;

	@Schema(name = "商场编号")
	private String mallId;

	@Schema(name = "部门id")
	private String deptId;

	@Schema(name = "模块id")
	private String moduleId;

	@Schema(name = "名称")
	private String ballName;

	@Schema(name = "权重")
	private Integer sort;

	@Schema(name = "是否小程序：0否 1是")
	private String appFlag;

	@Schema(name = "小程序链接")
	private String appUrl;

	@Schema(name = "h5链接")
	private String htmlUrl;

	@Schema(name = "标题")
	private String title;

	@Schema(name = "文件夹id")
	private Long folderId;

	@Schema(name = "图片id")
	private String imageId;

	@Schema(name = "图片路径")
	private String imageUrl;

	@Schema(name = "链接类型：0站内 1站外")
	private String linkType;

	@Schema(name = "内部链接类型：0自定义1商品2券3页面")
	private String insideType;

	@Schema(name = "外部链接类型：0h5、1小程序")
	private String outsideType;

	@Schema(name = "外部appid")
	private String outsideAppid;

	@Schema(name = "外部链接名称")
	private String outsideUrlName;

	@Schema(name = "外部链接url")
	private String outsideUrl;

	@Schema(name = "内部链接url")
	private String insideUrl;

	@Schema(name = "是否启用：0停用1启用")
	private String openFlag;

	@Schema(name = "开始时间")
	private Date beginTime;

	@Schema(name = "结束时间")
	private Date endTime;
	
	@Schema(name = "底部导航选中的url")
	private String imageCheckUrl;
	
	@Schema(name = "导航选中的id")
	private String imageCheckId;
	
	@Schema(name = "底部导航选中的颜色")
	private String fontSelectColor;
	
	@Schema(name = "券或商品ID")
	private String tpId;
	
	@Schema(name = "销售价格")
	private String salePrice;
	
	@Schema(name = "商城价格")
	private String marketPrice;
	
	@Schema(name = "店铺名称")
	private String shopName;
	
	@Schema(name = "版本号")
	private Integer	versions;
	
	@Schema(name = "内部链接选中ID")
	private String insideId;
	
	@Schema(name = "活动名称")
	private String activityName;
	
	@Schema(name = "活动参数")
	private String activityParams;
	
	@Schema(name = "页面辅助路径字段")
	private String pageUrl;
	
	@Schema(name = "是否有活动")
	private Integer activityType;
	
	@Schema(name = "修改名称开关")
	private boolean titleInput;
	
	@Schema(name = "二级联动回显")
	private String twolevelLinkage;
	
	@Schema(name = "是否商圈授权")
	private Integer circleAuthorize;
	
	@Schema(name = "是否会员授权")
	private Integer memberAuthorize;
	
	@Schema(name = "是否只展示图片")
	private Integer showImgFlag;
	
	@Schema(name = "店铺号")
	private String shopno;

	@Schema(name = "资源列表类型：0：功能球 1：多行按钮内容 2:悬浮图标")
	private String resourcesType;

	@Schema(name = "是否可兑换")
	private String convertible;

	@Schema(name = "底部导航未选中的颜色")
	private String fontUnSelectColor;

	@Schema(name = "title loop screen advertisement展示时间，默认为空")
	private String showTime;

	@Schema(name = "是否展示在支付宝小程序 1: 展示 0: 不展示")
	private Integer showAliMini;

	@Schema(name = "首页悬浮球的等级选择")
	private List<String> memberGrade;

}