package com.kerryprops.kip.service.integral.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - profile-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 12/01/2023 15:28
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "区号明细查询类")
public class AreaCodeQueryDto implements Serializable {

    @NotBlank(message = "手机区号不能为空")
    @Schema( name = "手机区号")
    private String areaCode;

}
