package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 10/31/2024 09:10
 **********************************************************************************************************************/

/**
 * 积分抵现配置枚举类
 */
@Getter
public enum PointsRedemptionEnum {

    // 积分抵现枚举值
    POS_CASH_OUT("XS0110", "POS积分抵现"),
    POS_CASH_OUT_REFUND("XS0022", "POS积分抵现退款退积分"),
    KERRY_PAY_CASH_OUT("YM0120", "优惠买单积分抵现"),
    KERRY_PAY_CASH_OUT_REFUND("YM0121", "优惠买单积分抵现退款退积分"),
    // 现金转积分比例配置
    REWARDS_MALL("REWARDS_MALL", "线上商城积分比例配置");

    private final String code;
    private final String desc;

    PointsRedemptionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 积分抵现配置枚举值
     * @return
     */
    public static List<String> getCashEnumList() {
        return Stream.of(POS_CASH_OUT, KERRY_PAY_CASH_OUT).map(PointsRedemptionEnum::getCode).collect(Collectors.toList());
    }

    /**
     * 获取积分比例配置枚举值
     * @return
     */
    public static List<String> getConfigEnumList() {
        return Stream.of(REWARDS_MALL).map(PointsRedemptionEnum::getCode).collect(Collectors.toList());
    }

    public static PointsRedemptionEnum getEnumByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return Arrays.stream(values()).filter(item -> StringUtils.equals(item.getCode(), code)).findFirst().orElse(null);
    }

}
