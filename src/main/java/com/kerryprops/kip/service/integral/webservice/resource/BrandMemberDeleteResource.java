package com.kerryprops.kip.service.integral.webservice.resource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 12/26/2023 15:23
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrandMemberDeleteResource implements Serializable {

    /**
     * 集团id
     */
    private String groupId;

    /**
     * kip会员id
     */
    private String kipUserId;

    /**
     * 手机号
     */
    private String mobile;

}
