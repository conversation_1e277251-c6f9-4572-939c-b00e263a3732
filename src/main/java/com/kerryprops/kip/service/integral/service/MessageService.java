package com.kerryprops.kip.service.integral.service;


import com.kerryprops.kip.service.integral.entity.TbTemplateMessage;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.dto.MessageQueryDto;
import com.kerryprops.kip.service.integral.model.dto.TbSmsTemplateConfig;
import com.kerryprops.kip.service.integral.model.vo.WxTemplateSendVo;

import java.util.List;

/***********************************************************************************************************************
 * Project - saas
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 06/16/2022 17:11
 **********************************************************************************************************************/
public interface MessageService {

    /**
     * 查询短信模板
     * @param queryDto queryDto
     * @return List list
     */
    List<TbSmsTemplateConfig> getSmsTemplate(MessageQueryDto queryDto);

    /**
     * 发送短信
     * @param query query
     * @return KerryResultDto dto
     */
    KerryResultDto sendSmsMessage(MessageQueryDto query);

    /**
     * 查询微信模板
     * @param query query
     * @return List list
     */
    List<TbTemplateMessage> getModelTemplate(MessageQueryDto query);

    /**
     * 发送模板消息
     * @param query query
     * @return KerryResultDto dto
     */
    KerryResultDto sendSmsTemplate(WxTemplateSendVo query);
}
