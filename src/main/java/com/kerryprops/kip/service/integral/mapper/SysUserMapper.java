package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.SysUser;

import java.util.List;

/***********************************************************************************************************************
 * Project - member-points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> nancy
 * Created Date - 10/30/2023 09:21
 **********************************************************************************************************************/
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * getUserListByIds
     * @param userIds userIds
     * @return List list
     */
    List<SysUser> getUserListByIds(List<String> userIds);

    /**
     * getUserEmailList
     * @param userIds userIds
     * @return List list
     */
    List<String> getUserEmailList(List<String> userIds);
}
