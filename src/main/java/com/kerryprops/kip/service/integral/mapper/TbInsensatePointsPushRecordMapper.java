package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsPushRecord;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 微信、支付宝推送记录表
 * @date 2022-09-14
 */
@Mapper
public interface TbInsensatePointsPushRecordMapper extends BaseMapper<TbInsensatePointsPushRecord> {


    TbInsensatePointsPushRecord findByTransactionIdAndEventTypeAndOrigin(@Param("transactionId") String transactionId,
                                                             @Param("eventType") Integer eventType,
                                                             @Param("origin") int origin);

    TbInsensatePointsPushRecord findByOpenIdAndTransactionIdAndEventType(@Param("openId") String openId,
                                                                         @Param("transactionId") String transactionId,
                                                                         @Param("eventType") int eventType);

    List<InsensatePointsPushRecordDto> selectByOpenId(@Param("openId") String openId);

    /**
     * 通过推送单号查询推送信息
     * @param transactionId
     * @return
     */
    TbInsensatePointsPushRecord findByTransactionId(@Param("transactionId") String transactionId);
}