package com.kerryprops.kip.service.integral.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/06/2022 11:44
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantInfoVo implements Serializable {

    private String id;

    /**
     * 合同主体名称
     */
    private String name;

    private String note;

    private String contractNo;

    /**
     * 店铺所属的品牌ID
     */
    private String retailBrandId;

    /**
     * 店铺所属的品牌名称
     */
    private String retailBrandName;

    private String firstFormatCode;

    private String firstFormatName;

    private String secondFormatCode;

    private String secondFormatName;

    private String thirdFormatCode;

    private String thirdFormatName;

    /**
     * crm输入的店铺别名
     */
    private String shopName;

    private String shopType;

    /**
     * S端用户输入名
     */
    private String brandName;

    /**
     * 店铺状态: DISABLE, ENABLE, CANCELED
     */
    private String status;

    /**
     * 合同号
     */
    private List<String> doCoSet;

}
