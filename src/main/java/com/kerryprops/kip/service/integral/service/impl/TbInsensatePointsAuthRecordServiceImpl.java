package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import com.kerryprops.kip.pmw.client.resource.QueryPointsAuthInputResource;
import com.kerryprops.kip.pmw.client.resource.QueryPointsAuthOutputResource;
import com.kerryprops.kip.pmw.client.service.BizCircleClientService;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.*;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.TbInsensatePointsAuthRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.MemberAutoPointResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 无感积分 微信、支付宝授权记录表
 * @date 2022-09-08
 */
@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbInsensatePointsAuthRecordServiceImpl extends ServiceImpl<TbInsensatePointsAuthRecordMapper, TbInsensatePointsAuthRecord> implements TbInsensatePointsAuthRecordService {

    private final TbInsensatePointsAuthRecordMapper tbInsensatePointsAuthRecordMapper;
    private final TbAutoPointsConfigService tbAutoPointsConfigService;
    private final MemberRegisterService memberRegisterService;
    private final TbCardMemberRelationService tbCardMemberRelationService;
    private final BizCircleClientService bizCircleClientService;
    private final HeaderResource headerResource;
    private final RabbitMqService rabbitMqService;
    private final KerryStaffService kerryStaffService;
    private final HiveVasService hiveVasService;
    private final MallConfig mallConfig;
    private final Mapper mapper;

    @Async
    @Override
    public void insertJudgment(TbInsensatePointsAuthRecord authRecord) {
        TbInsensatePointsAuthRecord record = tbInsensatePointsAuthRecordMapper.checkExists(authRecord.getOpenId(), authRecord.getGroupId(),
                authRecord.getMallId(), authRecord.getOrigin());
        if (Objects.isNull(record)) {
            // 补全信息
            if (StringUtils.isBlank(authRecord.getMchid())) {
                Optional.ofNullable(mallConfig.getByMallId(authRecord.getMallId())).ifPresent(mall ->
                        authRecord.setMchid((InsensateOriginEnum.WECHAT.getValue() == authRecord.getOrigin()) ? mall.getWxMchId() : mall.getAliMallId()));
            }
            tbInsensatePointsAuthRecordMapper.insert(authRecord);
            // 无感积分绑定奖励
            this.sendMq(authRecord);
        } else {
            if (AuthorizeStatusEnum.AUTHORIZED.getVal() != record.getStatus() || !StringUtils.equals(authRecord.getKipUserId(), record.getKipUserId())) {
                record.setStatus(AuthorizeStatusEnum.AUTHORIZED.getVal());
                // 防止用户注销后，用户id对应不上
                if (!Objects.equals(authRecord.getKipUserId(), record.getKipUserId())) {
                    record.setKipUserId(authRecord.getKipUserId());
                }
                // 更新会员卡code
                if (StringUtils.isBlank(record.getCardNo()) || !StringUtils.equals(record.getCardNo(), authRecord.getCardNo())) {
                    record.setCardNo(authRecord.getCardNo());
                }
                // 更新创建时间
                record.setCreateDate(new Date());
                tbInsensatePointsAuthRecordMapper.updateById(record);
                // 无感积分绑定奖励
                this.sendMq(record);
            }
        }
    }

    @Override
    public void insertAuthRecord(TbInsensatePointsAuthRecord record) {
        tbInsensatePointsAuthRecordMapper.insert(record);
    }

    /**
     * 发送mq消息
     * @param authRecord
     */
    private void sendMq(TbInsensatePointsAuthRecord authRecord) {
        if (StringUtils.isAnyBlank(authRecord.getGroupId(), authRecord.getKipUserId())) {
            return;
        }
        TbMemberAsset member = memberRegisterService.findByGroupIdAndKipUserId(authRecord.getGroupId(), authRecord.getKipUserId());
        if (Objects.isNull(member)) {
            return;
        }
        WxAutoPointsMessageDto messageDto = WxAutoPointsMessageDto
                .builder()
                .type(authRecord.getOrigin() == InsensateOriginEnum.WECHAT.getValue() ? "2" : "1")
                .memberId(String.valueOf(member.getId())).mallId(authRecord.getMallId())
                .queryDate(DateUtil.formatDateTime(new Date())).build();
        rabbitMqService.sendMessage(RabbitMqConstant.MEMBER_SENSELESS_REWARD, JsonUtils.objToString(messageDto));
        // 商圈快速积分开通提醒-只有微信无感积分开通才有短信提醒，支付宝开通也有短信提醒
        SendMessageDto dto = SendMessageDto.builder().memberId(String.valueOf(member.getId())).mallId(authRecord.getMallId()).templateType(1)
                .messageType(authRecord.getOrigin() == InsensateOriginEnum.WECHAT.getValue() ? "19" : "25")
                .updateDate(DateUtil.formatDateTime(new Date())).build();
        rabbitMqService.sendLazyMessage(RabbitMqConstant.XCRM_MEMBER_INTEGRAL_GRADE_CHANGE, JsonUtils.objToString(dto), 3);
    }

    @Override
    public int update(TbInsensatePointsAuthRecord tbInsensatePointsAuthRecord) {
        return tbInsensatePointsAuthRecordMapper.updateById(tbInsensatePointsAuthRecord);
    }

    @Override
    public TbInsensatePointsAuthRecordDto getByWeChatOpenId(String openId, String mallId, String groupId) {
        if (StringUtils.isAnyBlank(openId, mallId)) {
            return null;
        }
        TbInsensatePointsAuthRecord tbInsensatePointsAuthRecord = tbInsensatePointsAuthRecordMapper.selectByOpenId(openId, mallId, groupId, InsensateOriginEnum.WECHAT.getValue(), null);
        return Objects.isNull(tbInsensatePointsAuthRecord) ? null : mapper.map(tbInsensatePointsAuthRecord, TbInsensatePointsAuthRecordDto.class);
    }

    @Override
    @Cacheable(value = RedisCacheKey.MEMBER_WX_OR_ALI_AUTO_POINTS_AUTH_RECORD, key = "#kipUserId + ':' + #mallId + ':' + #origin", unless = "#result == null")
    public TbInsensatePointsAuthRecord getByKipUserIdAndMallIdAndOrigin(String kipUserId, String mallId, int origin) {
        return tbInsensatePointsAuthRecordMapper.getByKipUserIdAndMallIdAndOrigin(kipUserId, mallId, origin);
    }

    @Override
    public TbInsensatePointsAuthRecord checkHkcMemberAuthStatus(String kipUserId, String mallId, int origin) {
        return tbInsensatePointsAuthRecordMapper.getByKipUserIdAndMallIdAndOrigin(kipUserId, mallId, origin);
    }

    @Override
    public TbInsensatePointsAuthRecord queryByOpenIdAndMallId(String openId, String mallId) {
        return tbInsensatePointsAuthRecordMapper.getByOpenIdAndMallId(openId, mallId);
    }

    @Override
    public TbInsensatePointsAuthRecordDto getByAliUserId(String openid, String mallId) {
        TbInsensatePointsAuthRecord record = tbInsensatePointsAuthRecordMapper.selectByOpenId(openid, mallId, "", InsensateOriginEnum.ALIPAY.getValue(), AuthorizeStatusEnum.AUTHORIZED.getVal());
        return Objects.isNull(record) ? null : mapper.map(record, TbInsensatePointsAuthRecordDto.class);
    }

    @Override
    public MemberAutoPointResource getAutoPointInfo(LoginUser loginUser, String provider, String lbsId) {
        // 获取对应LBS ID自助积分的配置
        TbAutoPointsConfig config = tbAutoPointsConfigService.findByLbsId(lbsId);
        if (Objects.isNull(config)) {
            return MemberAutoPointResource.builder().build();
        }
        MallItem mallItem = hiveVasService.getMallByLbsId(lbsId);
        if (Objects.isNull(mallItem)) {
            return MemberAutoPointResource.builder().build();
        }
        String code = config.getCode();
        // 如果请求是来源与支付宝端，需要排除自助积分配置的微信无感积分
        if (ProviderEnum.ALI.getVal().equals(provider) && AutoPointsEnum.containWxPay(code)) {
            code = CharSequenceUtil.split(code, CommonSeparators.COMMA_SEPARATOR).stream()
                    .filter(it -> !AutoPointsEnum.isWxPay(it)).collect(Collectors.joining(CommonSeparators.COMMA_SEPARATOR));
        }
        MemberAutoPointResource resource = MemberAutoPointResource.builder().codes(code).mchId(mallItem.getWxMchId())
                .wxAuth(false).aliMallId(mallItem.getAliMallId()).aliAuth(false).build();
        if (Objects.nonNull(loginUser)) {
            List<TbInsensatePointsAuthRecord> recordList = tbInsensatePointsAuthRecordMapper.selectByKipUserIdAndMallId(loginUser.getCId(), mallItem.getMallId());
            if (CollectionUtils.isNotEmpty(recordList)) {
                resource.setAliAuth(recordList.stream().anyMatch(it -> (it.getOrigin() == InsensateOriginEnum.ALIPAY.getValue() && it.getStatus() == AuthorizeStatusEnum.AUTHORIZED.getVal())));
                // 微信的授权记录-特殊处理杭州（Tick4.5 杭州迁移至KO小程序）
                if (mallConfig.isHkc(mallItem.getGroupId())) {
                    resource.setWxAuth(recordList.stream().filter(item -> item.getCreateDate().after(IntegralConstant.hkcMigrationToKoDate))
                            .anyMatch(it -> (it.getOrigin() == InsensateOriginEnum.WECHAT.getValue() && it.getStatus() == AuthorizeStatusEnum.AUTHORIZED.getVal())));
                } else {
                    resource.setWxAuth(recordList.stream().anyMatch(it -> (it.getOrigin() == InsensateOriginEnum.WECHAT.getValue() && it.getStatus() == AuthorizeStatusEnum.AUTHORIZED.getVal())));
                }
            }
            if (StringUtils.isBlank(resource.getOpenId())) {
                resource.setOpenId(loginUser.getOpenId());
            }
        }
        return resource;
    }

    @Override
    public List<TbInsensatePointsAuthRecord> getBatchData(Long id, int batchSize) {
        return tbInsensatePointsAuthRecordMapper.getBatchData(id, batchSize);
    }

    @Override
    public List<TbInsensatePointsAuthRecord> getMallBatchData(String mallId, Long id, int batchSize) {
        return tbInsensatePointsAuthRecordMapper.getMallBatchData(mallId, id, batchSize);
    }

    @Override
    public void checkAndSaveAliMemberOpenCard(TbInsensatePointsAuthRecord authRecord) {
        CustomerUserDto userDto = kerryStaffService.findByMobile(authRecord.getMobile());
        if (Objects.isNull(userDto)) {
            return;
        }
        TbInsensatePointsAuthRecord record = tbInsensatePointsAuthRecordMapper.checkExists(authRecord.getOpenId(), authRecord.getGroupId(), authRecord.getMallId(), authRecord.getOrigin());
        if (Objects.isNull(record)) {
            authRecord.setKipUserId(userDto.getId());
            if (StringUtils.isBlank(authRecord.getMchid())) {
                Optional.ofNullable(mallConfig.getByMallId(authRecord.getMallId())).ifPresent(it -> authRecord.setMchid(it.getAliMallId()));
            }
            tbInsensatePointsAuthRecordMapper.insert(authRecord);
            // 无感积分绑定奖励
            this.sendMq(authRecord);
        } else {
            if (AuthorizeStatusEnum.AUTHORIZED.getVal() != record.getStatus() || !Objects.equals(userDto.getId(), record.getKipUserId())) {
                record.setStatus(AuthorizeStatusEnum.AUTHORIZED.getVal());
                // 防止用户注销后，用户id对应不上
                record.setKipUserId(userDto.getId());
                // 更新会员卡code
                if (StringUtils.isBlank(authRecord.getCardNo())) {
                    record.setCardNo(authRecord.getCardNo());
                }
                tbInsensatePointsAuthRecordMapper.updateById(record);
                // 无感积分绑定奖励
                this.sendMq(record);
            }
        }
    }

    @Override
    public void saveAuthRecord(String mobile, String mallId) {
        log.info("saveAuthRecord: {}-{}", mobile, mallId);
        MallItem mallItem = mallConfig.getByMallId(mallId);
        Assert.notNull(mallItem, "商场信息不存在");
        TbCardMemberRelation relation = tbCardMemberRelationService.getByGroupIdAndMobileAndMallId(mallItem.getGroupId(), mobile, mallId);
        if (Objects.isNull(relation)) {
            log.info("支付宝无感积分授权记录不存在, {}-{}", mobile, mallId);
            return;
        }
        TbInsensatePointsAuthRecord record = tbInsensatePointsAuthRecordMapper.checkExists(relation.getUserId(), mallItem.getGroupId(), mallItem.getMallId(), InsensateOriginEnum.ALIPAY.getValue());
        if (Objects.nonNull(record)) {
            return;
        }
        TbMemberAsset tbMemberAsset = memberRegisterService.queryByGroupIdAndMobile(mallItem.getGroupId(), mobile);
        if (Objects.isNull(tbMemberAsset)) {
            log.info("会员信息不存在.");
            return;
        }
        record = TbInsensatePointsAuthRecord.builder()
                .origin(InsensateOriginEnum.ALIPAY.getValue())
                .cardNo(relation.getBizCardNo())
                .createDate(relation.getCreateTime())
                .status(0)
                .openId(relation.getUserId())
                .mallId(mallItem.getMallId())
                .groupId(mallItem.getGroupId())
                .kipUserId(tbMemberAsset.getKipUserId())
                .mchid(mallItem.getAliMallId())
                .build();
        // 补齐记录
        tbInsensatePointsAuthRecordMapper.insert(record);
        // 无感积分绑定奖励
        this.sendMq(record);
    }

    @Override
    public TbInsensatePointsAuthRecord getByOpenIdAndMallId(String openId, String mallId) {
        return tbInsensatePointsAuthRecordMapper.getByOpenIdAndMallId(openId, mallId);
    }

    @Override
    public void insertBatchFromCardMemberRelation() {
        tbInsensatePointsAuthRecordMapper.insertBatchFromCardMemberRelation();
    }

    @Override
    public TbInsensatePointsAuthRecord getByCardNoAndOrigin(String cardNo, Integer origin) {
        return tbInsensatePointsAuthRecordMapper.getByCardNoAndOrigin(cardNo, origin);
    }

    @Async
    @Override
    public void checkAuthStatus(TbInsensatePointsAuthRecord authRecord, MallItem mallItem) {
        QueryPointsAuthInputResource.QueryPointsAuthInputBodyResource body = new QueryPointsAuthInputResource.QueryPointsAuthInputBodyResource();
        body.setSubMchId(authRecord.getMchid());
        body.setOpenId(authRecord.getOpenId());
        // 对应小程序appId
        body.setAppId(mallItem.getAppId());
        body.setOrderSource(IntegralConstant.KIP_POINTS);
        QueryPointsAuthInputResource resource = new QueryPointsAuthInputResource(headerResource, body, null);
        QueryPointsAuthOutputResource outputResource = null;
        try {
            outputResource = bizCircleClientService.queryPointsAuth(resource);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("会员: [{}]查询微信无感积分授权状态异常: {}", authRecord.getOpenId(), e);
        }
        // 查询微信API报错后，更改用户授权状态为取消授权
        if (Objects.isNull(outputResource)) {
            if (AuthorizeStatusEnum.AUTHORIZED.getVal() != authRecord.getStatus()) {
                authRecord.setStatus(AuthorizeStatusEnum.DEAUTHORIZE.getVal());
                tbInsensatePointsAuthRecordMapper.updateById(authRecord);
            }
            return;
        }
        QueryPointsAuthOutputResource.QueryPointsAuthOutputBodyResource response = (QueryPointsAuthOutputResource.QueryPointsAuthOutputBodyResource) outputResource.getBody();
        if (Objects.isNull(response) || StringUtils.isBlank(response.getAuthorizeState())) {
            return;
        }
        // 获取授权状态
        AuthorizeStatusEnum anEnum = AuthorizeStatusEnum.getByName(response.getAuthorizeState());
        // 授权状态不一致，则更新
        if (anEnum.getVal() != authRecord.getStatus()) {
            authRecord.setStatus(anEnum.getVal());
            tbInsensatePointsAuthRecordMapper.updateById(authRecord);
        }
    }

    @Async
    @Override
    public void recheckWxAuthRecordByKipUserId(String kipUserId) {
        List<TbInsensatePointsAuthRecord> records = tbInsensatePointsAuthRecordMapper.findWxAuthRecordByKipUserId(kipUserId);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (TbInsensatePointsAuthRecord record : records) {
            MallItem mallItem = mallConfig.getByMallId(record.getMallId());
            if (Objects.isNull(mallItem)) {
                continue;
            }
            this.checkAuthStatus(record, mallItem);
        }
    }
}