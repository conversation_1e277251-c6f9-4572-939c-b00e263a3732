package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 12/06/2024 14:22
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "Kinetic调整积分API")
public class BasicIntegralUpdateResource implements Serializable {

    /**
     * 集团编号
     */
    @NotBlank(message = "groupId不能为空.")
    private String groupId;

    /**
     * 操作商场
     */
    @NotBlank(message = "商场id不能为空.")
    private String mallid;

    /**
     * 会员手机号
     */
    private String mobile;

    /**
     * 会员编号
     */
    private String vipcode;

    /**
     * 调整积分数量(或消费金额)
     */
    @NotNull(message = "调整积分数量不能为空.")
    private Double number;

    /**
     * 积分调整原因
     */
    @NotBlank(message = "积分调整原因不能为空.")
    private String remark;

    /**
     * 调整类型 A->普通调整， S->销售调整
     */
    private String type;

}
