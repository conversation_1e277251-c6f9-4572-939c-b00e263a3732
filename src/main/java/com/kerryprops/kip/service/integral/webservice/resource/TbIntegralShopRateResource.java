package com.kerryprops.kip.service.integral.webservice.resource;

import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/13/2023 13:39
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbIntegralShopRateResource implements Serializable {

    @NotBlank(message = "groupId不能为空")
    private String groupId;

    @NotBlank(message = "mallId不能为空")
    private String mallId;

    @NotEmpty(message = "shopIds不能为空")
    private List<String> shopIds;

    private String creator;

    /**
     * 业态是否统一
     */
    private int isConsistent;

    private List<GradeIntegralResponse> list;

}