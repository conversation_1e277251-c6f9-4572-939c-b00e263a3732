package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbActivityPromotion;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;

import java.util.List;

/***********************************************************************************************************************
 * Project - integral-service
 * <p>
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/17/2022 11:04
 **********************************************************************************************************************/
public interface TbActivityPromotionService {

    /**
     * 查询复合的营销积分活动
     * @param mallId    商场编号
     * @param saleDate  销售日期
     * @param shopNo    店铺id
     * @param kipUserId kipUserId
     * @return          根据商店编号查询积分促销活动
     */
    List<TbActivityPromotion> queryByMallId(String mallId, String saleDate, String shopNo, String kipUserId);

    /**
     * 处理该笔销售匹配的营销活动
     * @param dto
     * @param isAutoPoints: 是否是自动积分(true：需要记录该销售匹配的营销积分活动； false: 只计算营销积分活动获得的积分数)
     * @return
     */
    Integer handlePromotionActivity(SalesAutoPointsDto dto, boolean isAutoPoints);

}
