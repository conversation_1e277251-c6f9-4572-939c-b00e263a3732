package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbMemberCardOpenRecord;
import com.kerryprops.kip.service.integral.mapper.TbMemberCardOpenRecordMapper;
import com.kerryprops.kip.service.integral.service.TbMemberCardOpenRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/18/2022 12:14
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbMemberCardOpenRecordServiceImpl extends ServiceImpl<TbMemberCardOpenRecordMapper, TbMemberCardOpenRecord> implements TbMemberCardOpenRecordService {

    private final TbMemberCardOpenRecordMapper tbMemberCardOpenRecordMapper;

    @Override
    public void saveOrUpdateRecord(TbMemberCardOpenRecord record) {
        TbMemberCardOpenRecord openRecord = tbMemberCardOpenRecordMapper.selectByOpenIdAndMallId(record.getOpenId(), record.getMallId());
        if (Objects.isNull(openRecord)) {
            // 填充默认值
            record.fillDefaultVal();
            tbMemberCardOpenRecordMapper.insert(record);
        } else {
            if (!StringUtils.equals(record.getEventType(), openRecord.getEventType())
                    || !StringUtils.equals(record.getCardId(), openRecord.getCardId())
                    || !StringUtils.equals(record.getKipUserId(), openRecord.getKipUserId())) {
                openRecord.setEventType(record.getEventType());
                openRecord.setCardId(record.getCardId());
                openRecord.setCardNo(record.getCardNo());
                openRecord.setKipUserId(record.getKipUserId());
                // 填充默认值
                openRecord.fillDefaultVal();
                tbMemberCardOpenRecordMapper.updateById(openRecord);
            }
        }
    }
}
