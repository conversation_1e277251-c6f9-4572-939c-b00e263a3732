package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/07/2023 10:45
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "海鼎返回信息")
public class HaiDingPosResponse implements Serializable {

    private boolean success;

    private Integer amountOfPoints;

    private Integer points;

}
