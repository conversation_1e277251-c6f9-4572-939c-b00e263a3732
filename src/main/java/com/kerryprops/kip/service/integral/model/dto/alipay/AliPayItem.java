package com.kerryprops.kip.service.integral.model.dto.alipay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝配置类
 * @createDate 2022/10/21
 * @updateDate 2022/10/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AliPayItem implements Serializable {
    /**
     * 对应的区域名称
     */
    private String name;
    /**
     * 支付宝私钥
     */
    private String aliPayPrivateKey;

    /**
     * 支付宝公钥
     */
    private String alipayPublicKey;

    /**
     * 支付宝appid
     */
    private String aliPayAppId;

    /**
     * 网关URL
     */
    private String gatewayUrl;

    /**
     * 授权回调地址
     */
    private String callBackUrl;

}
