package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 05/09/2023 13:47
 **********************************************************************************************************************/

@Getter
public enum PointsInterceptEnum {
    // 时间类型 1:日 2:自然周 3：周期周(连续7天) 4:自然月

    DAY(1, "日"),
    NATURAL_WEEK(2, "自然周"),
    CYCLE_WEEK(3, "周期周(连续7天)"),
    NATURAL_MONTH(4, "自然月"),
    CYCLE_MONTH(5, "周期月");

    private final int type;
    private final String desc;

    PointsInterceptEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PointsInterceptEnum getByType(Integer type) {
        return Arrays.stream(values()).filter(it -> Objects.equals(it.getType(), type)).findFirst().orElse(null);
    }

    public static String getDescByType(Integer type) {
        return Arrays.stream(values()).filter(it -> Objects.equals(it.getType(), type)).findFirst().map(PointsInterceptEnum::getDesc).orElse(null);
    }

}
