package com.kerryprops.kip.service.integral.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/19/2022 21:54
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesAutoPointsDto implements Serializable {

    /**
     * 对应销售记录的crm_id
     */
    private String id;

    private String groupId;

    /**
     * 商场id
     */
    private String mallId;

    /**
     * 会员vipcode
     */
    private String vipcode;

    /**
     * 店铺号
     */
    private String shopId;

    /**
     * 店铺信息
     */
    @JsonIgnore
    private TbBaseShop baseShop;

    /**
     * 会员信息
     */
    @JsonIgnore
    private TbMemberAsset member;

    /**
     * 销售记录表信息，如果是支付推送记录时，则该字段用户记录与哪笔销售记录发生了重复
     */
    @JsonIgnore
    private TbSalesDetail memberSale;

    /**
     * 销售单号或支付记录单号
     */
    private String saleNo;

    /**
     * 销售记录发生的时间
     */
    private String saleDate;

    /**
     * 实付金额
     */
    private String amount;

    /**
     * 实退金额
     */
    private String refundAmount;

    /**
     * 销售记录来源
     */
    private String createUser;

    /**
     * 销售类型(1.CRM销售,2.POS销售,3.微信小票,4.微信扫码,5.口碑,6.线上商城,7.微信商圈,8.支付宝商圈,9.优惠买单)
     */
    private String saleType;

    /**
     * 退货状态
     */
    private String status;

    /**
     * 对应字典值
     */
    private String remark;

    /**
     * 字典名称
     */
    private String remarkName;

    /**
     * 该笔销售获取的积分数，正值是加积分，负值是减积分
     */
    private int exchangePoints;

    /**
     * 积分调整id
     */
    private String integralAdjustId;

    /**
     * 是否是退款流程
     */
    @Builder.Default
    private boolean refund = false;

    /**
     * 该字段是为了记录销售表内积分是怎么来的
     */
    private String salesRemark;

    /**
     * 收银机号(Admin/Ipad端销售录入会填写该字段)
     */
    private String posNo;

    /**
     * 暂存会员身份信息
     */
    @JsonIgnore
    private CustomerIdentityDto identityDto;

    /**
     * 支付宝推送记录对应的appId
     */
    public String appId;

    /**
     * 微信或支付宝暂存的用户唯一id
     */
    public String wxOrAliId;

    /**
     * 优惠金额
     */
    public String discountAmount;

    /**
     * 拍照积分图片地址
     */
    public String imageUrl;

    /**
     * 是否支持重复录入，1: 支持； null/0: 不支持
     */
    private String approved;

    /**
     * 校验步骤，记录走到了哪一步抛出异常，值为：1，2，3，4
     * 1: 第一步: 支付宝/微信，校验单号是否重复，其他销售类型，走默认的重复规则校验
     * 2: 第二步: 商场当天销售记录单号重复校验
     * 3: 第三步: 销售记录去重逻辑判断
     * 4: 进入异常积分拦截
     * 5: 异常积分拦截数据重复啦
     */
    private int validateStep;

    /**
     * 订单总金额
     */
    private String totalAmount;

    /**
     * 积分拦截状态，对应积分拦截状态的值：PointsInterceptStatusEnum
     */
    private Integer interceptStatus;

    /**
     * 销售表自增id，当为有单退货并且数据在销售表时，该值不能为空，但是积分拦截可以不传该参数
     */
    private Long refundSaleId;

    /**
     * 添加销售来源，处理IPAD/Admin（S）端录入的销售记录
     */
    private String fromType;


}
