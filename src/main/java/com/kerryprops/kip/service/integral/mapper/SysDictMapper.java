package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.SysDict;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/14/2022 09:17
 **********************************************************************************************************************/
public interface SysDictMapper extends BaseMapper<SysDict> {

    List<SysDict> findByDictTypes(@Param("list") List<String> list);

    SysDict findByDictType(@Param("dictType") String dictType);

    List<SysDict> getListByTypes(@Param("types") List<String> types);

}
