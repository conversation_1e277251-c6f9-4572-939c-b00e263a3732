package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbMemberGradeMapper extends BaseMapper<TbMemberGrade> {

    TbMemberGrade getByGradeAndGroupId(@Param("grade") String grade, @Param("groupId") String groupId);

    List<TbMemberGrade> getGradeSortAscByGroupId(@Param("groupId") String groupId);

    List<TbMemberGrade> getGradeSortDescByGroupId(@Param("groupId") String groupId);

    TbMemberGrade getMinGradeByGroupId(@Param("groupId") String groupId);

}
