package com.kerryprops.kip.service.integral.service.promotion.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.CustomerIdentityDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 * @DESC 办公楼用户
 * <AUTHOR> Bert
 * Created Date - 03/09/2023 11:18
 **********************************************************************************************************************/

@Slf4j
@Component
@DS("points")
@RequiredArgsConstructor
public class OfficePromotionRule implements PromotionRule {

    private final MallConfig mallConfig;
    private final ProfileServiceClient profileServiceClient;

    @Override
    public String getRuleType() {
        return "11";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        // 规则设置的内容
        String content = condition.getPromotionConditionContent();
        if (StringUtils.isBlank(content)) {
            return false;
        }
        TbMemberAsset member = dto.getMember();
        if (StringUtils.isBlank(member.getKipUserId())) {
            return false;
        }
        MallItem mallItem = mallConfig.getByMallId(dto.getMallId());
        if (Objects.isNull(mallItem)) {
            return false;
        }
        // 查询会员所在楼盘的身份信息
        List<CustomerIdentityDto> identityList = profileServiceClient.getIdentityResponse(member.getKipUserId(), mallItem.getProjectId());
        if (CollectionUtils.isEmpty(identityList)) {
            return false;
        }
        if (content.contains("1") && identityList.stream().anyMatch(CustomerIdentityDto::isOffice)) {
            return true;
        }
        if (content.contains("2") && identityList.stream().anyMatch(CustomerIdentityDto::isApartment)) {
            return true;
        }
        if (content.contains("3") && identityList.stream().anyMatch(CustomerIdentityDto::isResidence)) {
            return true;
        }
        return false;
    }
}
