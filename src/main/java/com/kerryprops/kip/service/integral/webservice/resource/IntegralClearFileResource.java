package com.kerryprops.kip.service.integral.webservice.resource;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/18/2023 09:01
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntegralClearFileResource implements Serializable {

    @ExcelProperty(value = "所属groupId", index = 0)
    private String groupId;

    @ExcelProperty(value = "会员号", index = 1)
    private String vipcode;

    @ExcelProperty(value = "2021年获得积分数", index = 2)
    private Integer getPoints;

    @ExcelProperty(value = "消耗积分数", index = 3)
    private Integer costPoints;

    @ExcelProperty(value = "清零积分数", index = 4)
    private Integer clearPoints;

    @ExcelProperty(value = "要扣件或增加积分数", index = 8)
    private Integer pointsNum;

}
