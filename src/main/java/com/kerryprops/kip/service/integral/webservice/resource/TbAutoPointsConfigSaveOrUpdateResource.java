package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Update;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/06/2023 16:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "自助积分配置新增或修改请求类")
public class TbAutoPointsConfigSaveOrUpdateResource implements Serializable {

    @NotNull(groups = {Update.class}, message = "主键id不能为空")
    @Schema( description = "主键id, 更新时必填")
    private Long id;

    @NotBlank(message = "业态不能为空")
    @Schema( description = "业态", required = true)
    private String format;

    @NotBlank(message = "groupId不能为空")
    @Schema( description = "groupId", required = true)
    private String groupId;

    @NotBlank(message = "lbsId不能为空")
    @Schema( description = "lbsId", required = true)
    private String lbsId;

    @NotBlank(message = "可使用的积分方式不能为空")
    @Schema( description = "可使用的积分方式", required = true)
    private String code;

}
