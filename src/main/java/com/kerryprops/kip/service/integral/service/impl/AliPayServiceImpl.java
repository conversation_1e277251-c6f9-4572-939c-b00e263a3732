package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayOpenAuthTokenAppModel;
import com.alipay.api.domain.AlipayOpenAuthTokenAppQueryModel;
import com.alipay.api.domain.AppTokenExchangeSubElement;
import com.alipay.api.request.AlipayOpenAuthTokenAppQueryRequest;
import com.alipay.api.request.AlipayOpenAuthTokenAppRequest;
import com.alipay.api.response.AlipayOpenAuthTokenAppQueryResponse;
import com.alipay.api.response.AlipayOpenAuthTokenAppResponse;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.*;
import com.kerryprops.kip.service.integral.config.AliPayConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.event.UserPointAuthorizationEvent;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.TbInsensatePointsAuthRecordDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.strategy.AliPayStrategy;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class AliPayServiceImpl implements AliPayService {

    private final TbBaseShopService baseShopService;
    private final TbMemberAssetService tbMemberAssetService;
    private final RedisService redisService;
    private final AliPayClientService aliPayClientService;
    private final TbInsensatePointsAuthRecordService insensatePointsAuthRecordService;
    private final TbInsensatePointsPushRecordService insensatePointsPushRecordService;
    private final MemberSalePointsProcessService memberSalePointsProcessService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final AliPayConfig aliPayConfig;

    @Override
    public String callback(AliPayBizContentDto callbackDto) {
        return StringUtils.isBlank(callbackDto.getOutRequestNo()) ? pay(callbackDto) : refund(callbackDto);
    }

    private String refund(AliPayBizContentDto callbackDto) {
        // 校验商圈与用户
        TbMemberAsset member = tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(callbackDto.getBuyerId(), callbackDto.getGroupId(), callbackDto.getMallId());
        if (Objects.isNull(member)) {
            log.error("payCallback - OpenId: {}对应的用户信息不存在", callbackDto.getBuyerId());
            return CodeMessageEnum.FAIL.getValue();
        }
        // 统一操作
        this.commonOperation(member, callbackDto, EventTypeEnum.REFUND_SUCC);
        // 查询店铺信息
        TbBaseShop shop = baseShopService.getByContractNoAndMallId(callbackDto.getMallStoreId(), callbackDto.getMallId());
        if (Objects.isNull(shop)) {
            log.error("payCallback - 该商铺: {}已失效或不属于该商圈", callbackDto.getMallStoreId());
            return CodeMessageEnum.FAIL.getValue();
        }
        log.info("AliPayRefundProcess {}, {}", callbackDto.getTradeNo(), callbackDto.getOutRequestNo());
        // 执行退款流程
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().groupId(callbackDto.getGroupId()).vipcode(member.getVipcode())
                .saleDate(StringUtils.isBlank(callbackDto.getGmtRefundPay()) ? DateUtil.formatDateTime(new Date()) : callbackDto.getGmtRefundPay())
                .mallId(callbackDto.getMallId()).member(member).refundAmount(callbackDto.getRefundAmount())
                .remark(IntegralConstant.ALIPAY_REFUND_POINTS).remarkName("支付宝无感积分退款").shopId(shop.getContractNo())
                .saleNo(callbackDto.getTradeNo()).createUser("ALIPAY_POINTS_REFUND").saleType(SaleTypeEnum.ALIPAY.getValue()).refund(true).appId(callbackDto.getAppId()).build();
        int refundPoints = 0;
        String refundKey = String.format(RedisCacheKey.REFUND_LOCK, callbackDto.getTradeNo());
        if (!redisService.setSaleNoIfAbsent(refundKey, IntegralConstant.KIP_POINTS)) {
            log.error("duplicated refundRecord {}", callbackDto.getTradeNo());
            return CodeMessageEnum.FAIL.getValue();
        }
        try {
            memberSalePointsProcessService.refundSalePointsProcess(pointsDto);
        } catch (Exception e) {
            log.error("AliPayRefund tradeNo{}, {}, {}", callbackDto.getTradeNo(), e.getMessage(), refundKey);
            log.error("AliPay - refund lock failed. tradeNo: {}", callbackDto.getTradeNo());
            return CodeMessageEnum.FAIL.getValue();
        } finally {
            log.info("finally - deleteKey {}, {}", refundKey, callbackDto.getTradeNo());
            redisService.delKeys(Collections.singletonList(refundKey));
        }
        // 更新会员卡信息
        if (refundPoints <= 0) {
            log.info("AliPay - No need to refund any point. tradeNo: {}", callbackDto.getTradeNo());
            return CodeMessageEnum.SUCCESS.getValue();
        }
        return CodeMessageEnum.SUCCESS.getValue();
    }

    private String pay(AliPayBizContentDto callbackDto) {
        // 校验商圈与用户
        TbMemberAsset member = tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(callbackDto.getBuyerId(), callbackDto.getGroupId(), callbackDto.getMallId());
        if (Objects.isNull(member)) {
            log.error("payCallback - BuyerId: {}对应的用户信息不存在", callbackDto.getBuyerId());
            return CodeMessageEnum.FAIL.getValue();
        }
        // 统一操作
        this.commonOperation(member, callbackDto, EventTypeEnum.TRANS_SUCC);
        // 查询店铺信息
        TbBaseShop shop = baseShopService.getByContractNoAndMallId(callbackDto.getMallStoreId(), callbackDto.getMallId());
        if (Objects.isNull(shop)) {
            log.error("payCallback - 该商铺: {}已失效或不属于该商圈", callbackDto.getMallStoreId());
            return CodeMessageEnum.FAIL.getValue();
        }
        // 执行支付记录自动积分流程
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId(callbackDto.getGroupId()).wxOrAliId(callbackDto.getBuyerId())
                .mallId(callbackDto.getMallId()).member(member).amount(callbackDto.getTotalAmount()).vipcode(member.getVipcode())
                .remark(IntegralConstant.ALIPAY_POINTS).remarkName("支付宝无感支付积分").shopId(callbackDto.getMallStoreId()).baseShop(shop)
                .saleNo(callbackDto.getTradeNo()).saleDate(StringUtils.isBlank(callbackDto.getGmtPayment()) ? DateUtil.formatDateTime(new Date()) : callbackDto.getGmtPayment())
                .createUser(ALIPAY).saleType(SaleTypeEnum.ALIPAY.getValue()).refund(false).appId(callbackDto.getAppId()).build();
        String saleKey = String.format(RedisCacheKey.SALES_NO_KEY, callbackDto.getTradeNo());
        if (!redisService.setSaleNoIfAbsent(saleKey, IntegralConstant.KIP_POINTS)) {
            log.error("duplicated payRecord {}", callbackDto.getTradeNo());
            return CodeMessageEnum.FAIL.getValue();
        }
        try {
            memberSalePointsProcessService.salePointsProcess(pointsDto);
        } catch (Exception e) {
            log.error("AliPay - payment lock failed. tradeNo: {}", callbackDto.getTradeNo());
            e.printStackTrace();
        } finally {
            redisService.delKeys(Collections.singletonList(saleKey));
        }
        if (TRADE_PAY_NOTIFY.equals(callbackDto.getMethod())) {
            aliPayClientService.clearingNotify(callbackDto, pointsDto);
        }
        return CodeMessageEnum.SUCCESS.getValue();
    }

    /**
     * 统一处理支付宝无感积分逻辑:
     * 1、发布授权无感积分事件
     * 2、保存推送记录
     * @param member
     * @param callbackDto
     * @param typeEnum
     */
    private void commonOperation(TbMemberAsset member, AliPayBizContentDto callbackDto, EventTypeEnum typeEnum) {
        // 发布用户授权支付宝无感积分事件
        applicationEventPublisher.publishEvent(UserPointAuthorizationEvent.builder().openId(callbackDto.getBuyerId())
                .kipUserId(member.getKipUserId()).groupId(callbackDto.getGroupId()).mallId(callbackDto.getMallId())
                .mchid(callbackDto.getMallItem().getAliMallId()).origin(InsensateOriginEnum.ALIPAY.getValue()).build());
        // 保存支付宝支付推送记录
        InsensatePointsPushRecordDto pushRecord = InsensatePointsPushRecordDto.builder().openId(callbackDto.getBuyerId()).eventType(typeEnum.getValue())
                .transactionId(callbackDto.getTradeNo()).transactionInfo(callbackDto.getTransactionInfo()).origin(InsensateOriginEnum.ALIPAY.getValue()).build();
        insensatePointsPushRecordService.pushTransaction(pushRecord);
    }

    @Override
    public String authCallback(AliPayAuthDto dto) {
        // 商圈授权
        if (mallAuth(dto)) {
            return null;
        }
        //会员开卡
        String url = memberAuth(dto);
        if (Objects.nonNull(url)) {
            return url;
        }
        // 智能积分授权回调
        return smartPointsCallback(dto);
    }

    @Override
    public boolean mallAuth(AliPayAuthDto authDto) {
        if (StringUtils.isBlank(authDto.getAppAuthCode())) {
            return false;
        }
        AlipayOpenAuthTokenAppRequest request = new AlipayOpenAuthTokenAppRequest();
        AlipayOpenAuthTokenAppModel model = new AlipayOpenAuthTokenAppModel();
        model.setGrantType(AUTH_CODE);
        model.setCode(authDto.getAppAuthCode());
        request.setBizModel(model);
        try {
            AppTokenExchangeSubElement tokenDto = getAliPayToken(request, authDto);
            if (StringUtils.isBlank(tokenDto.getAppAuthToken())) {
                return true;
            }
            logAuthToken(authDto.getMallItem(), tokenDto);
        } catch (AlipayApiException e) {
            log.error("mallAuth - appAuthCode:{}, groupId: {}, Enum: {}  AlipayApiException: {}", authDto.getAppAuthCode(), authDto.getGroupId(), authDto.getMallItem().getAbbreviation(), e.getMessage());
        }
        return true;
    }

    @Override
    public String memberAuth(AliPayAuthDto dto) {
        log.info("Entering memberAuth - {}", dto);
        if (StringUtils.isAnyBlank(dto.getAuthCode(), dto.getMallId())) {
            return ERROR_PAGE;
        }
        // 所属商圈
        MallItem mallItem = dto.getMallItem();
        AliPayMemberTokenDto tokenDto = aliPayClientService.getAccessToken(dto.getAuthCode(), mallItem.getAbbreviation());
        if (Objects.isNull(tokenDto)) {
            log.error("AliPay memberAuth - tokenDto empty");
            return ERROR_PAGE;
        }
        dto.setMemberToken(tokenDto);
        log.info("get member token: {}", tokenDto);
        try {
            // query active form from alipay
            ActiveFormDto activeForm = aliPayClientService.queryActiveForm(dto);
            dto.setActiveForm(activeForm);
            if (Objects.isNull(activeForm) || StringUtils.isBlank(activeForm.getMobile())) {
                log.error("AliPay memberAuth - active form empty: {}", activeForm);
                return ERROR_PAGE;
            }
            return aliPayClientService.openAlipayMarketingCard(dto);
        } catch (Exception e) {
            log.error("AliPay - error when get activeForm: {}", e.getMessage());
            return ERROR_PAGE;
        }
    }

    @Override
    public String smartPointsCallback(AliPayAuthDto dto) {
        if (StringUtils.isBlank(dto.getAuthCode()) || Objects.isNull(dto.getMallItem())) {
            return ERROR_PAGE;
        }
        AliPayMemberTokenDto accessToken = aliPayClientService.getAccessToken(dto.getAuthCode(), dto.getMallItem().getAbbreviation());
        if (Objects.isNull(accessToken)) {
            log.error("smartPointsCallback - can't get access token. auth_code:{}, enum:{}", dto.getAuthCode(), dto.getMallItem().getAbbreviation());
            return ERROR_PAGE;
        }
        TbInsensatePointsAuthRecordDto authRecordDto = insensatePointsAuthRecordService.getByAliUserId(accessToken.getUserId(), dto.getMallId());
        if (Objects.isNull(authRecordDto)) {
            return ERROR_PAGE;
        }
        return aliPayClientService.querySchemaUrl(authRecordDto.getCardNo(), dto.getMallItem());
    }

    private void logAuthToken(MallItem mallItem, AppTokenExchangeSubElement tokenDto) throws AlipayApiException {
        // 根据app_auth_token查询商户授权信息
        AlipayOpenAuthTokenAppQueryRequest requestQuery = new AlipayOpenAuthTokenAppQueryRequest();
        AlipayOpenAuthTokenAppQueryModel modelQuery = new AlipayOpenAuthTokenAppQueryModel();
        modelQuery.setAppAuthToken(tokenDto.getAppAuthToken());
        requestQuery.setBizModel(modelQuery);
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        AlipayOpenAuthTokenAppQueryResponse responseQuery = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(requestQuery);
        log.info("获取商户授权信息成功json_body_token: " + responseQuery.getBody());
    }

    private AppTokenExchangeSubElement getAliPayToken(AlipayOpenAuthTokenAppRequest request, AliPayAuthDto authDto) throws AlipayApiException {
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(authDto.getMallItem().getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        AlipayOpenAuthTokenAppResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
        if (!StringUtils.equals(response.getCode(), CODE_OK)) {
            log.error("mallAuth - failed to fetch mall auth info. appAuthCode:{}, groupId: {}, Enum: {}  message: {}", authDto.getAppAuthCode(),
                    authDto.getGroupId(), authDto.getMallItem().getAbbreviation(), response.getSubMsg());
            return new AppTokenExchangeSubElement();
        }
        List<AppTokenExchangeSubElement> tokenList = response.getTokens();
        AppTokenExchangeSubElement tokenDto = tokenList.stream().findFirst().orElse(new AppTokenExchangeSubElement());
        log.info("商圈授权token: " + tokenDto.getAppAuthToken());
        return tokenDto;
    }

}
