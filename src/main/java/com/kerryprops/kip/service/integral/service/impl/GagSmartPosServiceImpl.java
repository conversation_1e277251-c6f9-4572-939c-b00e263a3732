package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.client.SmartPosRpcClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.ResultVO;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.TradingAreaEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.config.SmartPosProperties;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbQrCodeRecord;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.model.dto.BillInfoRpcDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.GagScanCodeAchievePointsResource;
import com.kerryprops.kip.service.integral.webservice.response.IpadGagQrCodeResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName GagSmartPosServiceImpl
 * @Description GagSmartPosServiceImpl
 * @date 2022/10/25 13:50
 * @Version 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class GagSmartPosServiceImpl implements GagSmartPosService {

    private final SmartPosRpcClient smartPosRpcClient;
    private final MallConfig mallConfig;
    private final SmartPosProperties smartPosProperties;
    private final TbBaseShopService tbBaseShopService;
    private final TbMemberAssetService tbMemberAssetService;
    private final RedisService redisService;
    private final MemberSalePointsProcessService memberSalePointsProcessService;
    private final TbQrCodeRecordService tbQrCodeRecordService;
    private final TbSalesDetailService tbSalesDetailService;

    private static final String POS_SUCCESS = "OPEN_SUCCESS";

    @Override
    public Integer smartPosAutoPoints(LoginUser login, String billFileName) {
        String timestamp = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        // 获取Smart-POS销售单信息
        BillInfoRpcDto salesInfo = this.getBillInfoRpcDto(timestamp, billFileName);
        if (Objects.isNull(salesInfo)) {
            throw BizNotFoundException.error(PointsEnum.QR_CODE_SALES_DATA_NOT_FOUND);
        }
        //对GAG接口返回错误进行判断提示
        if (!StringUtils.equals(POS_SUCCESS, salesInfo.getRescode())) {
            throw BizException.error(PointsEnum.GAG_SALES_DATA_AMOUNT_ERROR.getCode(), StringUtils.isNotBlank(salesInfo.getResmsg()) ? salesInfo.getResmsg() : "账单未入库，无效二维码.");
        }
        //对GAG接口返回的金额做判断，小于0的金额不入库，直接报错
        if (Objects.isNull(salesInfo.getAmount()) || salesInfo.getAmount() <= 0) {
            throw BizException.error(PointsEnum.GAG_SALES_DATA_AMOUNT_ERROR);
        }
        // 不是当天二维码，直接报错
        String saleTime = salesInfo.getSaleTime();
        // 添加销售时间不能比系统当前时间大的判断
        Date currentDate = new Date();
        Date dateTime = DateUtil.parse(saleTime, DatePattern.PURE_DATETIME_PATTERN);
        if (dateTime.getTime() > currentDate.getTime()) {
            throw BizException.error(PointsEnum.GAG_QR_DATA_SALE_TIME_ERROR);
        }
        //第二天10点
        Date nextDay = this.getNextDay(dateTime);
        //杭州情况下，第二天10点之前有用
        MallItem mallItem = mallConfig.getByMallId(login.getLbsId());
        if (Objects.isNull(mallItem)) {
            throw BizNotFoundException.error(PointsEnum.MALL_NOT_EXISTS);
        }
        // 特殊处理，HKC smart pos二维码第二天早上10点过期
        if (StringUtils.equals(TradingAreaEnum.HANGZHOU.getAbbreviation(), mallItem.getAbbreviation())) {
            if (nextDay.getTime() < System.currentTimeMillis()) {
                throw BizException.error(PointsEnum.GAG_QR_CODE_OVERDUE_ERROR);
            }
        } else {
            if (!StringUtils.equals(DateUtil.formatDate(dateTime), DateUtil.formatDate(new Date()))) {
                throw BizException.error(PointsEnum.GAG_QR_CODE_OVERDUE_ERROR);
            }
        }
        // 当静安以及浦东商城时候，取MerchantName作为ContractNo
        TbBaseShop tbBaseShop = tbBaseShopService.getByContractNoAndMallId(TradingAreaEnum.isJakcOrKpc(mallItem.getAbbreviation()) ?
                salesInfo.getMerchantName() : salesInfo.getMerchantCode(), login.getLbsId());
        if (Objects.isNull(tbBaseShop)) {
            throw BizNotFoundException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        TbMemberAsset member = tbMemberAssetService.getMemberWithProfileInfo(login.getCId(), mallItem.getGroupId());
        if (Objects.isNull(member)) {
            throw BizNotFoundException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId(mallItem.getGroupId())
                .mallId(mallItem.getMallId()).member(member).amount(salesInfo.getAmount().toString()).vipcode(member.getVipcode())
                .remark("HF0053").remarkName("扫码积分").shopId(tbBaseShop.getContractNo()).baseShop(tbBaseShop)
                .saleNo(salesInfo.getUuid()).saleDate(StringUtils.isBlank(salesInfo.getSaleTime()) ? DateUtil.formatDateTime(new Date()) : salesInfo.getSaleTime())
                .createUser(login.getNickName()).saleType(SaleTypeEnum.SCAN.getValue()).refund(false).build();
        String smartPosKey = String.format(RedisCacheKey.SALES_NO_KEY, salesInfo.getUuid());
        Integer points = 0;
        // 重新积分校验
        if (Boolean.TRUE.equals(redisService.setSaleNoIfAbsent(smartPosKey, IntegralConstant.KIP_POINTS))) {
            try {
                points = memberSalePointsProcessService.salePointsProcess(pointsDto);
            } catch (Exception e) {
                e.printStackTrace();
                // 判断是否重复积分
                if (Objects.nonNull(pointsDto.getMemberSale())) {
                    this.repeatSaleHandler(pointsDto, member);
                } else {
                    throw e;
                }
            } finally {
                redisService.delKeys(Collections.singletonList(smartPosKey));
            }
        } else {
            throw BizException.error(PointsEnum.REPEAT_SCANNING_CODE_ERROR);
        }
        return points;
    }

    @Override
    public ResultVO<IpadGagQrCodeResponse> qrcodeDetail(String billFileName, String mallId) {
        String timestamp = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        BillInfoRpcDto salesInfo = this.getBillInfoRpcDto(timestamp, billFileName);
        if (Objects.isNull(salesInfo)) {
            return ResultVO.fail("未查询到该二维码的销售数据.");
        }
        //对GAG接口返回错误进行判断提示
        if (!StringUtils.equals(POS_SUCCESS, salesInfo.getRescode())) {
            return ResultVO.fail(StringUtils.isNotBlank(salesInfo.getResmsg()) ? salesInfo.getResmsg() : "账单未入库，无效二维码.");
        }
        //对GAG接口返回的金额做判断，小于0的金额不入库，直接报错
        if (Objects.isNull(salesInfo.getAmount()) || salesInfo.getAmount() <= 0) {
            return ResultVO.fail("查询销采金额错误.");
        }
        // 不是当天二维码，直接报错
        String saleTime = salesInfo.getSaleTime();
        // 添加销售时间不能比系统当前时间大的判断
        Date currentDate = new Date();
        Date dateTime = DateUtil.parse(saleTime, DatePattern.PURE_DATETIME_PATTERN);
        if (dateTime.getTime() > currentDate.getTime()) {
            return ResultVO.fail("您的小票扫描结果异常，请重新扫描或前往客服台进行人工积分.");
        }
        //杭州情况下，第二天10点之前有用
        MallItem byMallId = mallConfig.getByMallId(mallId);
        if (Objects.isNull(byMallId)) {
            return ResultVO.fail("商场信息不存在.");
        }
        //第二天10点
        Date nextDay = this.getNextDay(dateTime);
        // 特殊处理，HKC smart pos二维码第二天早上10点过期
        if (StringUtils.equals(TradingAreaEnum.HANGZHOU.getAbbreviation(), byMallId.getAbbreviation())) {
            if (nextDay.getTime() < System.currentTimeMillis()) {
                return ResultVO.fail("二维码已过期.");
            }
        } else {
            if (!StringUtils.equals(DateUtil.formatDate(dateTime), DateUtil.formatDate(new Date()))) {
                return ResultVO.fail("二维码已过期.");
            }
        }
        // 当静安以及浦东商城时候，取MerchantName作为ContractNo
        TbBaseShop tbBaseShop = tbBaseShopService.getByContractNoAndMallId(TradingAreaEnum.isJakcOrKpc(byMallId.getAbbreviation()) ?
                salesInfo.getMerchantName() : salesInfo.getMerchantCode(), byMallId.getMallId());
        if (Objects.isNull(tbBaseShop)) {
            return ResultVO.fail("该商场店铺不存在.");
        }
        // 查询二维码是否已积分
        TbSalesDetail detail = tbSalesDetailService.queryBySellNoAndSaleType(salesInfo.getUuid(), SaleTypeEnum.SCAN.getValue());
        IpadGagQrCodeResponse response = IpadGagQrCodeResponse.builder().traceno(billFileName)
                .contractNo(TradingAreaEnum.isJakcOrKpc(byMallId.getAbbreviation()) ? salesInfo.getMerchantCode() : tbBaseShop.getContractNo())
                .tradingDate(dateTime)
                .receivableAmount(BigDecimal.valueOf(salesInfo.getAmount()).setScale(2, RoundingMode.HALF_UP))
                .shopName(TradingAreaEnum.isJakcOrKpc(byMallId.getAbbreviation()) ? "渠道代码区分" : salesInfo.getMerchantName())
                .completedPoints("1").build();
        if (Objects.nonNull(detail)) {
            response.setCompletedPoints("0");
            response.setPointDate(detail.getCreateDate());
            Optional.ofNullable(tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(detail.getVipcode(), detail.getGroupId()))
                    .ifPresent(item -> {
                        response.setNameAndMobile((StringUtils.isBlank(item.getRealName()) ? "-" : item.getRealName()) + "/" + item.getMobile());
                        response.setCurrnentIntegral(item.getCurrentPoints());
                    });
        }
        return ResultVO.success(response);
    }

    @Override
    public ResultVO<Integer> scanCodeAchievePoints(GagScanCodeAchievePointsResource resource, String createUser) {
        String timestamp = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        BillInfoRpcDto salesInfo = this.getBillInfoRpcDto(timestamp, resource.getSerialnum());
        if (Objects.isNull(salesInfo)) {
            return ResultVO.fail("未查询到该二维码的销售数据.");
        }
        //对GAG接口返回错误进行判断提示
        if (!StringUtils.equals(POS_SUCCESS, salesInfo.getRescode())) {
            return ResultVO.fail(StringUtils.isNotBlank(salesInfo.getResmsg()) ? salesInfo.getResmsg() : "账单未入库，无效二维码.");
        }
        //对GAG接口返回的金额做判断，小于0的金额不入库，直接报错
        if (Objects.isNull(salesInfo.getAmount()) || salesInfo.getAmount() <= 0) {
            return ResultVO.fail("查询销采金额错误.");
        }
        // 不是当天二维码，直接报错
        String saleTime = salesInfo.getSaleTime();
        // 添加销售时间不能比系统当前时间大的判断
        Date currentDate = new Date();
        Date dateTime = DateUtil.parse(saleTime, DatePattern.PURE_DATETIME_PATTERN);
        if (dateTime.getTime() > currentDate.getTime()) {
            return ResultVO.fail("您的小票扫描结果异常，请重新扫描或前往客服台进行人工积分.");
        }
        //杭州情况下，第二天10点之前有用
        MallItem byMallId = mallConfig.getByMallId(resource.getMallId());
        if (Objects.isNull(byMallId)) {
            return ResultVO.fail("商场信息不存在.");
        }
        //第二天10点
        Date nextDay = this.getNextDay(dateTime);
        // 特殊处理，HKC smart pos二维码第二天早上10点过期
        if (StringUtils.equals(TradingAreaEnum.HANGZHOU.getAbbreviation(), byMallId.getAbbreviation())) {
            if (nextDay.getTime() < System.currentTimeMillis()) {
                return ResultVO.fail("二维码已过期.");
            }
        } else {
            if (!StringUtils.equals(DateUtil.formatDate(dateTime), DateUtil.formatDate(new Date()))) {
                return ResultVO.fail("二维码已过期.");
            }
        }
        // 当静安以及浦东商城时候，取MerchantName作为ContractNo
        TbBaseShop tbBaseShop = tbBaseShopService.getByContractNoAndMallId(TradingAreaEnum.isJakcOrKpc(byMallId.getAbbreviation()) ?
                salesInfo.getMerchantName() : salesInfo.getMerchantCode(), byMallId.getMallId());
        if (Objects.isNull(tbBaseShop)) {
            return ResultVO.fail("该商场店铺不存在.");
        }
        TbMemberAsset member = tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(resource.getVipcode(), resource.getGroupId());
        if (Objects.isNull(member)) {
            throw BizNotFoundException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId(resource.getGroupId())
                .mallId(resource.getMallId()).member(member).amount(salesInfo.getAmount().toString()).vipcode(member.getVipcode())
                .remark("HF0053").remarkName("扫码积分").shopId(tbBaseShop.getContractNo()).baseShop(tbBaseShop)
                .saleNo(salesInfo.getUuid()).saleDate(StringUtils.isBlank(salesInfo.getSaleTime()) ? DateUtil.formatDateTime(new Date()) : salesInfo.getSaleTime())
                .createUser(createUser).saleType(SaleTypeEnum.SCAN.getValue()).refund(false).build();
        String smartPosKey = String.format(RedisCacheKey.SALES_NO_KEY, salesInfo.getUuid());
        ResultVO resultVO = null;
        if (redisService.setSaleNoIfAbsent(smartPosKey, IntegralConstant.KIP_POINTS)) {
            try {
                resultVO = ResultVO.success(memberSalePointsProcessService.salePointsProcess(pointsDto));
            } catch (Exception e) {
                if (e instanceof BizException) {
                    BizException exp = (BizException) e;
                    resultVO = ResultVO.fail("根据商场设置的规则判定: " + exp.getMsg());
                } else {
                    resultVO = ResultVO.fail(e.getMessage());
                }
            } finally {
                redisService.delKeys(Collections.singletonList(smartPosKey));
            }
        } else {
            resultVO = ResultVO.fail("该销售单号正在积分，请勿重复提交.");
        }
        return resultVO;
    }

    /**
     * 调用gag api查询销售明细
     * @param timestamp
     * @param billFileName
     * @return
     */
    private BillInfoRpcDto getBillInfoRpcDto(String timestamp, String billFileName) {
        BillInfoRpcDto rpcDto = null;
        TbQrCodeRecord record = tbQrCodeRecordService.getByQrCode(billFileName);
        if (Objects.nonNull(record)) {
            rpcDto = JsonUtils.stringToObj(record.getContent(), BillInfoRpcDto.class);
        } else {
            Map<String, String> params = this.getParams(timestamp, billFileName);
            // 生成签名
            String sign = this.getSign(smartPosProperties.getAppSecret(), params, smartPosProperties.getSignMethod());
            MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
            map.add("method", smartPosProperties.getMethod());
            map.add("appKey", smartPosProperties.getAppKey());
            map.add("v", smartPosProperties.getVersion());
            map.add("signMethod", smartPosProperties.getSignMethod());
            map.add("messageFormat", smartPosProperties.getMessageFormat());
            map.add("qrCode", billFileName);
            map.add("timestamp", timestamp);
            map.add("data", MapUtils.getString(params, IntegralConstant.SMART_POS_DATA));
            map.add("sign", sign);
            rpcDto = smartPosRpcClient.getBillInfoRpc(map);
            // 异步保存gag查询日志
            if (Objects.nonNull(rpcDto) && Objects.equals(rpcDto.getRescode(), POS_SUCCESS)) {
                // 销采系统返回店铺号判断
                if (StringUtils.isAllBlank(rpcDto.getMerchantCode(), rpcDto.getMerchantName())) {
                    throw BizException.error(PointsEnum.GAG_SALE_INFO_EMPTY_ERROR);
                } else {
                    tbQrCodeRecordService.saveOrUpdate(billFileName, rpcDto);
                }
            }
        }
        return rpcDto;
    }

    /**
     * 处理重复积分异常
     * @param pointsDto
     * @param member
     */
    private void repeatSaleHandler(SalesAutoPointsDto pointsDto, TbMemberAsset member) {
        TbSalesDetail detail = pointsDto.getMemberSale();
        if (StringUtils.equals(member.getVipcode(), detail.getVipcode())) {
            throw BizException.error(PointsEnum.QR_CODE_REPEATED_POINTS_ERROR.getCode(), "该二维码您已积分.");
        } else {
            // 查询是被哪个用户积分了
            TbMemberAsset tbMemberAsset = tbMemberAssetService.findByVipcodeAndGroupId(detail.getVipcode(), detail.getGroupId());
            if (Objects.nonNull(tbMemberAsset) && StringUtils.isNotBlank(tbMemberAsset.getMobile())) {
                throw BizException.error(PointsEnum.QR_CODE_REPEATED_POINTS_ERROR.getCode(), String.format("该二维码已被: %s 会员积分.", DesensitizedUtil.mobilePhone(tbMemberAsset.getMobile())));
            } else {
                throw BizException.error(PointsEnum.QR_CODE_REPEATED_POINTS_ERROR.getCode(), "该二维码已被其他会员积分.");
            }
        }
    }

    private Map<String, String> getParams(String timestamp, String billFileName) {
        Map<String, String> param = new HashMap<>(16);
        param.put("method", smartPosProperties.getMethod());
        param.put("appKey", smartPosProperties.getAppKey());
        param.put("v", smartPosProperties.getVersion());
        param.put("signMethod", smartPosProperties.getSignMethod());
        param.put("messageFormat", smartPosProperties.getMessageFormat());
        param.put("qrCode", billFileName);
        param.put("timestamp", timestamp);
        Map<String, Object> data = new HashMap<>(2);
        if (StringUtils.isNotBlank(billFileName)) {
            data.put("billFileName", billFileName);
        }
        param.put(IntegralConstant.SMART_POS_DATA, JsonUtils.objToString(data));
        return param;
    }

    public Date getNextDay(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 10);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 签名算法
     *
     * @param appSecret  签名密钥
     * @param params     请求参数
     * @param signMethod 签名方法
     * @return 签名
     */
    private String getSign(String appSecret, Map<String, String> params, String signMethod) {
        // 1. 将所有参数排序
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        // 2. 把所有参数名和参数值串在一起
        StringJoiner joiner = new StringJoiner("");
        for (String key : keys) {
            String value = params.get(key);
            // 只取value不为空的
            if (StringUtils.isEmpty(value)) {
                continue;
            }
            joiner.add(String.format("%s=%s", key, value)).add("&");
        }
        // 3. 拼接签名密钥
        joiner.add("key=").add(appSecret);
        // 4. 签名
        if (Objects.equals("MD5", signMethod)) {
            return this.encrypt(joiner.toString(), "MD5");
        }
        return this.encrypt(joiner.toString(), "SHA");
    }

    /**
     * 加密方法
     *
     * @param str       待加密数据
     * @param algorithm 加密类型 MD5，SHA
     * @return 加密后数据
     */
    private String encrypt(String str, String algorithm) {
        try {
            MessageDigest md = MessageDigest.getInstance(algorithm);
            md.update(str.getBytes(StandardCharsets.UTF_8));
            byte[] digest = md.digest();
            StringBuilder hexstr = new StringBuilder();
            String shaHex;
            for (byte b : digest) {
                shaHex = Integer.toHexString(b & 0xFF);
                if (shaHex.length() < 2) {
                    hexstr.append(0);
                }
                hexstr.append(shaHex);
            }
            return hexstr.toString().toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
