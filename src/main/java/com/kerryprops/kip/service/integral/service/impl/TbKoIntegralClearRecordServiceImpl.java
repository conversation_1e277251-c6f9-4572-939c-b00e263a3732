package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.entity.TbKoIntegralClearRecord;
import com.kerryprops.kip.service.integral.mapper.TbKoIntegralClearRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.service.TbKoIntegralClearRecordService;
import com.kerryprops.kip.service.integral.service.TbMemberPointsChangeService;
import com.kerryprops.kip.service.integral.webservice.resource.IntegralClearFileResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/18/2023 09:42
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbKoIntegralClearRecordServiceImpl implements TbKoIntegralClearRecordService {

    private final TbKoIntegralClearRecordMapper tbKoIntegralClearRecordMapper;
    private final TbMemberPointsChangeService tbMemberPointsChangeService;
    private final Mapper mapper;

    private static final int BATCH_SIZE = 1000;

    @Override
    public void saveRecords(List<IntegralClearFileResource> resources) {
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }
        List<TbKoIntegralClearRecord> records = resources.stream().map(it -> mapper.map(it, TbKoIntegralClearRecord.class)).collect(Collectors.toList());
        tbKoIntegralClearRecordMapper.saveBatch(records);
    }

    /*@Async
    @Override
    public void clearMemberIntegral() {
        long time = System.currentTimeMillis();
        int total = this.total();
        log.info("clearMemberIntegral: total-{}", total);
        // 分页
        int page = (total % BATCH_SIZE == 0) ? total / BATCH_SIZE : (total / BATCH_SIZE) + 1;
        log.info("clearMemberIntegral: totalPage-{}", page);
        for (int i = 0; i < page; i++) {
            List<TbKoIntegralClearRecord> records = tbKoIntegralClearRecordMapper.findList(i * BATCH_SIZE, BATCH_SIZE);
            if (CollectionUtils.isEmpty(records)) {
                continue;
            }
            log.info("clearMemberIntegral: current-{}", i);
            for (TbKoIntegralClearRecord record: records) {
                MemberPointsChangeDto changeDto = MemberPointsChangeDto.builder().groupId(record.getGroupId()).vipcode(record.getVipcode()).saleType("0").type("A").build();
                if (record.getPointsNum() > 0) {
                    changeDto.setDictValue("XS0015");
                    changeDto.setDictName("积分清零");
                    changeDto.setRemark("2021年积分清零补扣积分");
                } else {
                    changeDto.setDictValue("XS0000");
                    changeDto.setDictName("其他（增加积分）");
                    changeDto.setRemark("2021年积分清零补积分");
                }
                changeDto.setChangePointsNum(-record.getPointsNum());
                tbMemberPointsChangeService.clearKoIntegral(changeDto);
            }
        }
        log.info("*****执行KO积分清零补扣积分操作结束*****{}", (System.currentTimeMillis() - time));
    }*/

    @Override
    public int total() {
        return tbKoIntegralClearRecordMapper.total();
    }
}
