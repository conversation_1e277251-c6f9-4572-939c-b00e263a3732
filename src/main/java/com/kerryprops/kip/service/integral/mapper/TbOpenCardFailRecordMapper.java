package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbOpenCardFailRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 01/16/2024 15:11
 **********************************************************************************************************************/
public interface TbOpenCardFailRecordMapper extends BaseMapper<TbOpenCardFailRecord> {

    int saveOrUpdate(TbOpenCardFailRecord record);

    /**
     * 查询授权记录
     * @param openId
     * @param mallId
     * @return
     */
    TbOpenCardFailRecord queryByOpenIdAndMallId(@Param("openId") String openId, @Param("mallId") String mallId);

    /**
     * 查询授权记录
     * @param openId
     * @return
     */
    List<TbOpenCardFailRecord> queryByOpenId(@Param("openId") String openId);

}
