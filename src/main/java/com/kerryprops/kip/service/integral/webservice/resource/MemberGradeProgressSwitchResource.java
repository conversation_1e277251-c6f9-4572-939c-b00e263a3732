package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/15/2022 15:15
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员成长进度条切换方式响应类")
public class MemberGradeProgressSwitchResource implements Serializable {

    @Schema( description = "规则ID")
    private String ruleId;

    @Schema( description = "规则文案")
    private String computeManner;

    @Schema( description = "排序字段")
    private Integer sort;

    @Schema( description = "规则业态描述")
    private String formatsDesc;

}
