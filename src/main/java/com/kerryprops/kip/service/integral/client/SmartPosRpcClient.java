package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.BillInfoRpcDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @ClassName BillInfoRpcClient
 * @Description smartpos远程调用接口
 * @date 2022/10/25 13:43
 * @Version 1.0
 */
@FeignClient(name = "bill-info-rpc", url = "${smart-pos.url}")
public interface SmartPosRpcClient {

    @PostMapping(value = "", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    BillInfoRpcDto getBillInfoRpc(@RequestBody MultiValueMap<String,Object> tokenVO);

}
