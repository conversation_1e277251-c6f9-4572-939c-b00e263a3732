package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.model.dto.TbMemberGradeCacheDto;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 14:44
 **********************************************************************************************************************/
public interface TbMemberGradeService extends IService<TbMemberGrade> {

    List<TbMemberGrade> queryGradeSortAscByGroupId(String groupId);

    CompletableFuture<Map<String, String>> getMapFutureByGroupId(String groupId);

    List<TbMemberGrade> queryGradeSortDescByGroupId(String groupId);

    List<TbMemberGrade> queryGradeSortDescWithoutCacheByGroupId(String groupId);

    List<TbMemberGrade> queryGradeSortAscWithoutCacheByGroupId(String groupId);

    List<TbMemberGrade> getGradeSortAscByGroupIdWithException(String groupId);

    TbMemberGrade queryByGroupIdAndGrade(String groupId, String grade);

    /**
     * groupId下最小卡等
     * @param groupId
     * @return
     */
    TbMemberGrade queryMinGroupGrade(String groupId);

    /**
     * groupId下最高卡等
     * @param groupId
     * @return
     */
    TbMemberGrade queryMaxGroupGrade(String groupId);

    String getGroupMiniGradeCode(String groupId);

    /**
     * 走本地缓存
     * @param groupId
     * @return
     */
    TbMemberGradeCacheDto getGradeList(String groupId);

}
