package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/08/2023 14:55
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "KIP同步会员注册信息")
public class KipSyncMemberResource implements Serializable {

    private String userId;

    private String phoneNumber;

    private String brandId;

    private String lbsId;

    /**
     * 是否是会员邀约
     */
    private Boolean isInvitation;

    /**
     * 来源渠道子业务
     */
    private String source;

    /**
     * 登录用户openId
     */
    private String openId;

}
