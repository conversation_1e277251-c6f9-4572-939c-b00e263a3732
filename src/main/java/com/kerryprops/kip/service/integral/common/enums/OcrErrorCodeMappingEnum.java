package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/10/2024 15:57
 **********************************************************************************************************************/

@Getter
public enum OcrErrorCodeMappingEnum {

    ERR_10(10, "小票重复","此小票重复上传，如有疑问，请前往客服台咨询。"),
    ERR_11(11, "已实时积分小票","此小票已通过其他方式完成积分，请勿重复上传。"),
    ERR_12(12, "五要素不全","照片较模糊，未识别到关键信息，建议重新拍照上传。"),
    ERR_13(13, "非消费小票","非消费小票，如有疑问，请前往客服台咨询。"),
    ERR_14(14, "无法积分租户","店铺不支持拍照积分，请前往客服台咨询积分。"),
    ERR_15(15, "小票过期","小票已过期，如有疑问，请前往客服台咨询。"),
    ERR_16(16, "交易金额超限","根据拍照积分规则，凡小票金额超过3万元，须请您携带小票至客服台，出示本人支付凭证完成人工积分。"),
    ERR_19(19, "图片模糊不清","照片较模糊，未识别到关键信息，建议重新拍照上传。"),
    ERR_20(20, "图片造假","小票不符，请前往客服台咨询积分。"),
    ERR_30(30, "系统内判定属于异常积分","积分异常正在审核中，如有疑问，请前往客服台咨询。"),
    ERR_40(40, "系统内判定销售重复","此小票已通过其他方式完成积分，请勿重复上传。"),
    ERR_50(50, "拍照小票上传失败","小票上传失败，请稍后重试。"),
    ERR_60(60, "小票识别超时","正在进行人工审核，通过后将会通知您积分成功，可在审核列表查看进度。"),
    ERR_70(70, "商铺信息不存在","店铺信息不存在，如有疑问，请前往客服台咨询。"),
    ERR_80(80, "商场信息不存在","商场信息不存在，如有疑问，请前往客服台咨询。"),
    ERR_90(90, "超出错误限制次数","会员在该商场OCR拍照识别错误次数超出限制。"),
    ERR_100(100, "API访问次数限制","当前上传申请过多，请您稍后再试。"),
    ERR_110(110, "虚拟号不允许OCR拍照积分","您的账号受限，请前往客服台咨询详情。"),
    ERR_500(500, "OCR服务器错误","系统异常，请稍后重试。");

    private Integer ocrErrorCode;
    private String ocrErrorMsg;
    private String errorMsg;

    OcrErrorCodeMappingEnum(Integer ocrErrorCode, String ocrErrorMsg, String errorMsg) {
        this.ocrErrorCode = ocrErrorCode;
        this.ocrErrorMsg = ocrErrorMsg;
        this.errorMsg = errorMsg;
    }

    /**
     * 通过OCR错误码获取错误信息
     * @param ocrErrorCode
     * @return
     */
    public static OcrErrorCodeMappingEnum getEnumByErrorCode(int ocrErrorCode) {
        OcrErrorCodeMappingEnum mappingEnum = OcrErrorCodeMappingEnum.ERR_11;
        for (OcrErrorCodeMappingEnum e: values()) {
            if (e.getOcrErrorCode() == ocrErrorCode) {
                mappingEnum = e;
                break;
            }
        }
        return mappingEnum;
    }

    /**
     * 是否是五元素缺失或重复积分
     * @param code
     * @return
     */
    public static boolean isMissFieldsOrDuplicated(Integer code) {
        return Stream.of(ERR_10, ERR_12).anyMatch(item -> Objects.equals(item.getOcrErrorCode(), code));
    }

}
