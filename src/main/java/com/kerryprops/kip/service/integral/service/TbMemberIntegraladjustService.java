//package com.kerryprops.kip.service.integral.service;
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
//import com.kerryprops.kip.service.integral.entity.TbMemberIntegraladjust;
//import com.kerryprops.kip.service.integral.model.dto.MemberIntegralAdjustRecordPageQueryDto;
//import com.kerryprops.kip.service.integral.model.dto.MemberPointSumQueryDto;
//import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
//import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
//import com.kerryprops.kip.service.integral.webservice.resource.MemberMonthlyIntegralResponse;
//
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.CompletableFuture;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 09/22/2022 16:50
// **********************************************************************************************************************/
//public interface TbMemberIntegraladjustService extends IService<TbMemberIntegraladjust> {
//
//    String saveIntegralAdjustRecord(SalesAutoPointsDto dto);
//
//    String savePointsAdjustRecord(MemberPointsChangeDto dto, TbMemberAsset tbMemberAsset);
//
//    void savePointsAdjustRecordNoMessage(MemberPointsChangeDto dto, TbMemberAsset tbMemberAsset);
//
//    List<TbMemberIntegraladjust> recordList(MemberIntegralAdjustRecordPageQueryDto pageQuery);
//
//    int getCount(MemberIntegralAdjustRecordPageQueryDto pageQuery);
//
//    CompletableFuture<List<MemberMonthlyIntegralResponse>> getMonthlyIntegral(String groupId, String mallId, String vipcode, List<String> moons);
//
//    /**
//     * 批量赠送积分过期积分数
//     * @param dto
//     * @return
//     */
//    double getMemberConsumePointsBetweenDate(MemberPointSumQueryDto dto);
//
//    CompletableFuture<Map<String, TbMemberIntegraladjust>> findByIds(List<String> adjustIds);
//
//    int integralRecordTotal(String vipCode, String mallId);
//
//    List<TbMemberIntegraladjust> integralRecordList(String vipCode, String mallId, int page, int size);
//
//    TbMemberIntegraladjust queryAdjustList(String groupId, String mallId, String saleNo, String shopId, String posCashOut);
//}
