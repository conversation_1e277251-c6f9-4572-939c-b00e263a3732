package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/28/2022 15:34
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "积分拦截记录")
@TableName("tb_points_intercept_approve")
public class TbPointsIntercept implements Serializable {

    @Schema( name = "主键ID")
    private String id;

    @Schema( name = "集团id")
    private String groupId;

    @Schema( name = "商场id")
    private String mallId;

    @Schema( name = "手机号")
    private String mobile;

    @Schema( name = "会员号")
    private String vipcode;

    @Schema( name = "会员信息id")
    private String kipUserId;

    @Schema( name = "销售id")
    private String saleId;

    @Schema( name = "销售单号流水号")
    private String orderNo;

    @Schema( name = "销售类型 1.CRM销售,2.POS销售,3.微信照片,4.微信扫码,5.口碑销售")
    private String saleType;

    @Schema( name = "店铺号")
    private String shopNo;

    @Schema( name = "销售金额")
    private BigDecimal payAmount;

    @Schema( name = "优惠金额")
    private BigDecimal discountAmount;

    @Schema( name = "总金额")
    private BigDecimal totalAmount;

    @Schema( name = "退货积分数")
    private Integer refundPointsNum;

    @Schema( name = "退货金额")
    private BigDecimal refundAmount;

    @Schema( name = "积分说明")
    private String remarkName;

    @Schema( name = "支付宝推送记录对应的appId")
    private String appId;

    @Schema( name = "微信或支付宝暂存的用户唯一id")
    private String wxOrAliId;

    @Schema( name = "销售时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date saleDate;

    /**
     * 详见枚举值 PointsInterceptStatusEnum
     */
    @Schema( name = "审核状态,详见枚举值PointsInterceptStatusEnum")
    private Integer status;

    @Schema( name = "异常原因")
    private String interceptReason;

    @Schema( name = "驳回原因")
    private String rejectReason;

    @Schema( name = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @Schema( name = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    @Schema( name = "创建者")
    private String createUser;

    @Schema( name = "更新人")
    private String updateUser;

    @Schema( name = "小票地址")
    private String imgUrl;

    @Schema( name = "正常的销售id")
    private String existSaleIds;
}
