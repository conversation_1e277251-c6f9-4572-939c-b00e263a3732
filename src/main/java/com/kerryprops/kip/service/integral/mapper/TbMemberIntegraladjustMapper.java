package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberIntegraladjust;

public interface TbMemberIntegraladjustMapper extends BaseMapper<TbMemberIntegraladjust> {

//    /**
//     * 获取时间内获得的积分数
//     * @param vipcode
//     * @param groupId
//     * @param remarks
//     * @param date
//     * @return
//     */
//    double getTotalPointsOfDay(@Param("groupId") String groupId, @Param("vipcode") String vipcode,
//                               @Param("remarks") List<String> remarks, @Param("date") String date);
//
//    List<TbMemberIntegraladjust> getList(MemberIntegralAdjustRecordPageQueryDto dto);
//
//    int getCount(MemberIntegralAdjustRecordPageQueryDto dto);
//
//    List<TbMemberIntegraladjust> getMonthlyIntegral(@Param("groupId") String groupId, @Param("mallId") String mallId,
//                                                    @Param("vipcode") String vipcode, @Param("list") List<String> moons);
//
//    double getMemberConsumePointsBetweenDate(MemberPointSumQueryDto dto);
//
//    List<DailyMemberStatisticsInfo> getDailyIntegralInfo(@Param("groupId") String groupId, @Param("mallId") String mallId,
//                                                         @Param("startTime") String startTime, @Param("endTime") String endTime);
//
//    List<TbMemberIntegraladjust> findByIds(@Param("list") List<String> list);
//
//    int integralRecordTotal(@Param("vipCode") String vipCode, @Param("mallId") String mallId);
//
//    List<TbMemberIntegraladjust> integralRecordList(@Param("vipCode") String vipCode, @Param("mallId") String mallId, @Param("offset") int offset, @Param("size") int size);
//
//    TbMemberIntegraladjust queryAdjustList(@Param("groupId") String groupId, @Param("mallId") String mallId, @Param("saleNo") String saleNo, @Param("shopId") String shopId, @Param("remark") String remark);
}