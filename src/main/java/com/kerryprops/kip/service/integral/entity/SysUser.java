/**
 * .
 *
 * 
 *
 * 版权所有，侵权必究！
 */

package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/14/2022 09:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_user")
public class SysUser implements Serializable {

	/**
	 * id
	 */
	@TableId
	private Long id;

	/**
	 * 用户名
	 */
	@TableField("username")
	private String userName;

	/**
	 * 密码
	 */
	@TableField("password")
	private String password;

	/**
	 * 姓名
	 */
	@TableField("real_name")
	private String realName;

	/**
	 * 头像
	 */
	@TableField("head_url")
	private String headUrl;

	/**
	 * 性别   0：男   1：女    2：保密
	 */
	@TableField("gender")
	private Integer gender;

	/**
	 * 邮箱
	 */
	@TableField("email")
	private String email;

	/**
	 * 手机号
	 */
	@TableField("mobile")
	private String mobile;

	/**
	 * 是否为ipad用户 0是1否
	 */
	@TableField("ipad_user")
	private Integer ipadUser;

	/**
	 * 商场id
	 */
	@TableField("mallid")
	private String mallId;

	/**
	 * 部门ID
	 */
	@TableField("dept_id")
	private String deptId;

	/**
	 * 超级管理员   0：否   1：是
	 */
	@TableField("super_admin")
	private Integer superAdmin;

	/**
	 * 状态  0：停用   1：正常
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 创建者
	 */
	@TableField(fill = FieldFill.INSERT)
	private Long creator;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT, value = "create_date")
	private Date createDate;

	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE, value = "update_date")
	private Date updateDate;

	/**
	 * 部门名称
	 */
	@TableField(exist=false)
	private String deptName;
	
	@TableField(fill = FieldFill.INSERT_UPDATE, value = "postid")
	private Long postId;
	
	@TableField(exist=false)
	private String postName;

	/**
	 * 是否是临时账号
	 */
	@TableField("is_temporary")
	private Integer isTemporary;

	/**
	 * 有效期开始时间
	 */
	@TableField("effective_start_date")
	private Date effectiveStartDate;

	/**
	 * 有效期-结束时间
	 */
	@TableField("effective_end_date")
	private Date effectiveEndDate;

	public Boolean isTimeValid() {
		if (Objects.nonNull(isTemporary) && 1 == isTemporary) {
			Date currentDate = new Date();
			return !(currentDate.after(effectiveStartDate) && currentDate.before(effectiveEndDate));
		}
		return Boolean.FALSE;
	}

}