package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.PerfectMemberActivityDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/20/2022 15:41
 **********************************************************************************************************************/

@FeignClient(name = "event-trigger", url = "${kerry.services.event-trigger:default}")
public interface EventTriggerClient {

    @GetMapping(value = "/activity/memberModify")
    PerfectMemberActivityDto getPerfectMemberInfo(@RequestParam("groupId") String groupId, @RequestParam("mallId") String mallId);

}
