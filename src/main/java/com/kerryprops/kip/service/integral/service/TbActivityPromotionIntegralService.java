package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotion;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionIntegral;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/23/2022 11:42
 **********************************************************************************************************************/
public interface TbActivityPromotionIntegralService extends IService<TbActivityPromotionIntegral> {

    /**
     * 保存活动调整的积分数
     * @param matchRules 匹配规则
     * @param dto 入参对象
     * @param type 1: 多倍积分 + 最高, 2: 固定积分 + 最高
     */
    void savePromotionIntegral(List<TbActivityPromotion> matchRules, SalesAutoPointsDto dto, int type, int basePoints);



    /**
     * 退款销售扣减匹配的营销积分活动
     * @param detail
     */
    void updatePromotionIntegral(TbSalesDetail detail);
}
