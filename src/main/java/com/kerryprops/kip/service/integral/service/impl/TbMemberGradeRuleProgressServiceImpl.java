package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.*;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.common.utils.KipDateUtils;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeRuleProgressMapper;
import com.kerryprops.kip.service.integral.model.dto.MemberGradeProgressSwitchDto;
import com.kerryprops.kip.service.integral.model.dto.MemberYearAmountCalDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.MemberUpgradeAmountResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbMemberGradeRuleProgressServiceImpl extends ServiceImpl<TbMemberGradeRuleProgressMapper, TbMemberGradeRuleProgress> implements TbMemberGradeRuleProgressService {

    private final MemberRegisterService memberRegisterService;
    private final TbBaseShopService tbBaseShopService;
    private final TbSalesDetailService tbSalesDetailService;
    private final TbMemberGradeService tbMemberGradeService;
    private final TbMemberGradeRuleProgressMapper tbMemberGradeRuleProgressMapper;
    private final TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    private final MemberGradeStrategyService memberGradeStrategyService;
    private final TbMemberGradeRuleService tbMemberGradeRuleService;

    /**
     * 等级有效期时间格式
     */
    private static final String DEFAULT_FORMAT = "yyyy年MM月dd日";
    private static final int ONE_HUNDRED = 100;

    @Override
    @Cacheable(value = RedisCacheKey.MEMBER_UPGRADE_PROGRESS_BAR_KEY, key = "#loginUser.brandId +':' + #loginUser.cId", unless = "#result == null")
    public Integer getProgressBar(LoginUser loginUser, String ruleId) {
        TbMemberAsset member = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        if (Objects.isNull(member)) {
            throw PointBusinessException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        // 查询卡等列表
        List<TbMemberGrade> gradeList = tbMemberGradeService.getGradeSortAscByGroupIdWithException(loginUser.getBrandId());
        if (this.checkMemberIsHonorable(gradeList, member)) {
            return ONE_HUNDRED;
        }
        // gradeList过滤掉尊享卡
        List<String> gradeCodeList = gradeList.stream().filter(it -> it.getUpGradationStatus()==1).map(TbMemberGrade::getCode).toList();
        // 会员等级数量
        int gradeSize = gradeCodeList.size();
        // 获取当前会员等级与下标
        int currentIndex = gradeCodeList.indexOf(member.getGrade());
        if (currentIndex < 0) {
            throw BizNotFoundException.error(PointsEnum.MEMBER_GRADE_NOT_EXISTS);
        }
        // 最大等级判断，如果是最高卡等则直接返回100
        if (StringUtils.equals(member.getGrade(), gradeCodeList.get(gradeSize - 1))) {
            return ONE_HUNDRED;
        }
        // 获取当前等级的下一个等级的下标
        int nextGradeIndex = currentIndex + 1;
        // 获取目标会员等级升级规则
        TbMemberGradeRule memberGradeRule = Objects.isNull(ruleId) ? tbMemberGradeRuleService.queryUpgradeRule(loginUser.getBrandId(), gradeCodeList.get(nextGradeIndex)) : tbMemberGradeRuleService.getById(ruleId);
        if (Objects.isNull(memberGradeRule)) {
            log.info("nextGradeIndex {}, gradeSize {}", nextGradeIndex, gradeSize);
            return (int)(NumberUtil.div(Double.valueOf(nextGradeIndex), Double.valueOf(gradeSize), 1) * 100);
        }
        // 到下一级所需的金额
        String leftMoney;
        // 年升级消费金额
        Double consumeAmount = tbSalesDetailService.getGradeRuleCacheAmount(memberGradeRule, member);
        if (Objects.isNull(consumeAmount)) {
            leftMoney = memberGradeStrategyService.modifyUpgradeLeftMoney(memberGradeRule, member);
        } else {
            leftMoney = NumberUtil.sub(memberGradeRule.getMoney(), new BigDecimal(consumeAmount + "")).toString();
        }
        return this.getProgressBar(leftMoney, gradeSize, nextGradeIndex, memberGradeRule);
    }

    /**
     * 计算进度条长度
     * @param leftMoney leftMoney
     * @param gradeSize gradeSize
     * @param nextGradeIndex nextGradeIndex
     * @param memberGradeRule memberGradeRule
     * @return int int
     */
    private int getProgressBar(String leftMoney, int gradeSize, int nextGradeIndex, TbMemberGradeRule memberGradeRule) {
        log.info("progress bar - leftMoney: {}", leftMoney);
        // 算出升级一个消费等级产生的占比
        BigDecimal gradeDimension = BigDecimal.ONE.divide(BigDecimal.valueOf(gradeSize - 1), 2, RoundingMode.HALF_UP);
        // 下一个级别占整体的进度条的占比
        BigDecimal nextDimension = gradeDimension.multiply(BigDecimal.valueOf(nextGradeIndex));
        int progress = BigDecimal.ZERO.compareTo(new BigDecimal(leftMoney)) == 0 ? ONE_HUNDRED
                : new BigDecimal(ONE_HUNDRED).multiply(nextDimension.subtract(NumberUtil.div(leftMoney, memberGradeRule.getMoney()+"").multiply(gradeDimension))).setScale(0, RoundingMode.DOWN).intValue();
        log.info("progress bar - progress: {}", progress);
        return progress;
    }

    /**
     * 检查会员卡等是否是不参与升降级的卡等
     * @param gradeList gradeList
     * @param member member
     * @return boolean
     */
    private boolean checkMemberIsHonorable(List<TbMemberGrade> gradeList, TbMemberAsset member) {
        return gradeList.stream().anyMatch(item -> (StringUtils.equals(item.getCode(), member.getGrade()) && item.getUpGradationStatus() == 0));
    }

    @Override
    @Cacheable(value = RedisCacheKey.MEMBER_RELEGATION_COPY_WRITER_KEY, key = "#loginUser.brandId +':' + #loginUser.cId", unless = "#result == null")
    public String getRelegationCopywriter(LoginUser loginUser) {
        // 获取会员信息
        TbMemberAsset member = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        // 查询卡等列表
        List<TbMemberGrade> gradeList = tbMemberGradeService.getGradeSortAscByGroupIdWithException(loginUser.getBrandId());
        TbMemberGrade minGrade = gradeList.stream().min(Comparator.comparing(TbMemberGrade::getSort)).orElse(TbMemberGrade.builder().build());
        // 如果会员为最低卡等，不存在保级文案或不参与升降级的卡等
        if (StringUtils.equals(minGrade.getCode(), member.getGrade()) || this.checkMemberIsHonorable(gradeList, member)) {
            return "";
        }
        // 查询会员卡等保级文案
        TbMemberGradeRuleProgress ruleProgress = tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(loginUser.getBrandId(), member.getGrade());
        if (Objects.isNull(ruleProgress) || StringUtils.isAllBlank(ruleProgress.getSustainProgressTextMax(), ruleProgress.getSustainProgressTextNotShow())) {
            return "";
        }
        // 查询卡等保级规则
        List<TbMemberGradeRule> gradeRules = tbMemberGradeRuleService.queryByGroupIdAndGradeAndType(loginUser.getBrandId(), member.getGrade(), 0);
        // 过滤调用会员身份认证的保级规则
        gradeRules = gradeRules.stream().filter(it -> !RuleTypeEnum.isIdentityRule(it.getRuleType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gradeRules)) {
            return "";
        }
        Date endDate = new Date();
        // 确定会员统计金额的开始时间
        MemberYearAmountCalDto calDto = memberGradeStrategyService.getCalculateYearAmountBeginDate(member.getGroupId(), member.getVipcode());
        // 年等级变更时间
        Date beginDate = calDto.getBeginDate();
        // 查询会员最终卡等变动记录
        TbMemberGradeChangeDetail changeDetail = tbMemberGradeChangeDetailService.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(loginUser.getBrandId(), member.getVipcode(), DateUtil.formatDateTime(beginDate));
        // 保级进行中文案
        String sustainText = StringUtils.isBlank(ruleProgress.getSustainProgressTextNotShow()) ? "" : ruleProgress.getSustainProgressTextNotShow();
        // 保级成功文案
        String sustainMaxText = StringUtils.isBlank(ruleProgress.getSustainProgressTextMax()) ? "" : ruleProgress.getSustainProgressTextMax();
        boolean isSuccess = false;
        Date startDate;
        for (TbMemberGradeRule rule : gradeRules) {
            // 会员等级规则设置的金额
            double ruleAmount = Objects.nonNull(rule.getMoney()) ? rule.getMoney().doubleValue() : 0;
            // 规则是按月、按日、还是按年
            String calculateType = RuleTypeEnum.getRuleType(rule.getRuleType());
            // 查询业态对应的店铺号
            List<String> shopIds = tbBaseShopService.getContractNoList(member.getGroupId(), rule.getFormats());
            // 是否是会员等级升级变更记录，如果是，则保级成功金额则从等级变更记录之后算起
            if (KipDateUtils.checkGradeChangeDetail(changeDetail) && MemberGradeChangeTypeEnum.isUpgradeType(changeDetail.getChangeType())) {
                beginDate = changeDetail.getCreateDate();
            }
            // 判断该规则下是否保级成功 - 每日/每月
            if (!RuleTimeEnum.YEAR.getValue().equals(calculateType) && tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(member, rule, beginDate, endDate, shopIds)) {
                isSuccess = true;
                break;
            }
            // 获取金额计算的开始时间
            startDate = memberGradeStrategyService.getRelegationRuleBeginDate(changeDetail, calDto.getCycleYear(), calculateType, beginDate);
            // 时间段内会员消费金额
            double amount = tbSalesDetailService.getMemberSaleAmountBetweenTime(member, rule, startDate, endDate, shopIds);
            // 说明保级成功，直接推出循环
            if (amount >= ruleAmount) {
                log.info("会员: {}，在{}保级规则下满足保级消费金额，保级成功.", member.getVipcode(), calculateType);
                isSuccess = true;
                break;
            }
            if (sustainText.contains("{" + rule.getId() + "}") || sustainMaxText.contains("{" + rule.getId() + "}")) {
                amount = NumberUtil.sub(ruleAmount + "", amount + "").setScale(0, RoundingMode.UP).doubleValue();
                if (amount < 0) {
                    amount = 0;
                }
                sustainMaxText = sustainMaxText.replaceAll("\\{" + rule.getId() + "}", ((int) amount) + "");
                sustainText = sustainText.replaceAll("\\{" + rule.getId() + "}", ((int) amount) + "");
            }
        }
        // 保级成功，且保级文案为空，直接返回
        if (isSuccess && StringUtils.isBlank(sustainMaxText)) {
            return sustainMaxText;
        }
        // 保级成功，金额参数替换成0
        if (isSuccess) {
            sustainMaxText = sustainMaxText.replaceAll(SUSTAIN_CYCLE_YEAR_AMOUNT, "0");
            sustainMaxText = sustainMaxText.replaceAll(SUSTAIN_NATURAL_YEAR_AMOUNT, "0");
            sustainMaxText = sustainMaxText.replaceAll(SUSTAIN_CYCLE_MONTH_AMOUNT, "0");
            sustainMaxText = sustainMaxText.replaceAll(SUSTAIN_CYCLE_DAY1_AMOUNT, "0");
            sustainMaxText = sustainMaxText.replaceAll(SUSTAIN_CYCLE_DAY2_AMOUNT, "0");
            sustainText = sustainText.replaceAll(SUSTAIN_CYCLE_YEAR_AMOUNT, "0");
            sustainText = sustainText.replaceAll(SUSTAIN_NATURAL_YEAR_AMOUNT, "0");
            sustainText = sustainText.replaceAll(SUSTAIN_CYCLE_MONTH_AMOUNT, "0");
            sustainText = sustainText.replaceAll(SUSTAIN_CYCLE_DAY1_AMOUNT, "0");
            sustainText = sustainText.replaceAll(SUSTAIN_CYCLE_DAY2_AMOUNT, "0");
        }
        // 会员对应的等级信息
        TbMemberGrade currentGrade = gradeList.stream().filter(it -> Objects.equals(it.getCode(), member.getGrade())).findFirst().orElse(null);
        // 替换等级名称
        if (Objects.nonNull(currentGrade)) {
            sustainMaxText = sustainMaxText.replaceAll(CURRENT_CARD_LEVEL, currentGrade.getName());
            sustainText = sustainText.replaceAll(CURRENT_CARD_LEVEL, currentGrade.getName());
        }
        // 等级变更时间
        Date changeDate = Objects.isNull(changeDetail) ? endDate : changeDetail.getCreateDate();
        // 1、自然年保级时间
        String strChangeYear = null;
        // 是否超过一个自然年
        if (KipDateUtils.startLessThanEndOverNatureYears(changeDate, endDate, 1)) {
            strChangeYear = DateUtil.formatDate(endDate);
        } else {
            strChangeYear = DateUtil.formatDate(DateUtil.endOfYear(DateUtil.offsetMonth(changeDate, (isSuccess ? 24 : 12))));
        }
        sustainMaxText = sustainMaxText.replaceAll(SUSTAIN_NATURAL_YEAR_END_DATE, strChangeYear);
        sustainText = sustainText.replaceAll(SUSTAIN_NATURAL_YEAR_END_DATE, strChangeYear);
        // 处理在等级变更时间内，超过一个周期年都没有发生过等级变更，则周期年时间显示为当前时间
        if (KipDateUtils.startLessThanEndOverCycleYears(changeDate, endDate, 1)) {
            strChangeYear = DateUtil.formatDate(endDate);
        } else {
            strChangeYear = DateUtil.formatDate(DateUtil.offsetMonth(changeDate, (isSuccess ? 24 : 12)));
        }
        // 2、周期年保级时间
        sustainMaxText = sustainMaxText.replaceAll(SUSTAIN_CYCLE_YEAR_END_DATE, strChangeYear);
        sustainText = sustainText.replaceAll(SUSTAIN_CYCLE_YEAR_END_DATE, strChangeYear);
        // 暂存当前时间
        strChangeYear = DateUtil.formatDate(DateUtil.endOfMonth(endDate));
        // 3、月保级日期
        sustainText = sustainText.replaceAll(SUSTAIN_BY_MONTH_END_DATE, strChangeYear);
        sustainMaxText = sustainMaxText.replaceAll(SUSTAIN_BY_MONTH_END_DATE, strChangeYear);
        // 暂存当前时间
        strChangeYear = DateUtil.formatDate(endDate);
        // 4、日保级日期
        sustainText = sustainText.replaceAll(SUSTAIN_BY_DAY_END_DATE, strChangeYear);
        sustainMaxText = sustainMaxText.replaceAll(SUSTAIN_BY_DAY_END_DATE, strChangeYear);
        return isSuccess ? sustainMaxText : sustainText;
    }

    @Override
    public TbMemberGradeRuleProgress getByGroupIdAndMemberGrade(String groupId, String grade) {
        return tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(groupId, grade);
    }

    @Override
    public List<MemberGradeProgressSwitchDto> memberGradeComputeMethod(LoginUser loginUser) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        if (Objects.isNull(tbMemberAsset)) {
            throw BizNotFoundException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        TbMemberGradeRuleProgress progress = tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(loginUser.getBrandId(), tbMemberAsset.getGrade());
        if (Objects.isNull(progress)) {
            return Collections.emptyList();
        }
        List<TbMemberGradeRule> rules = this.getConfigRules(progress);
        if (CollectionUtils.isEmpty(rules)) {
            return Collections.emptyList();
        }
        return rules.stream().map(this::parseItem).sorted(Comparator.comparing(MemberGradeProgressSwitchDto::getSort)).collect(Collectors.toList());
    }

    /**
     * 通过配置的升级文案展示id查询会员升级规则
     * @param progress progress
     * @return list
     */
    private List<TbMemberGradeRule> getConfigRules(TbMemberGradeRuleProgress progress) {
        List<String> upRuleIds = Stream.of(progress.getDayGradeRuleId(), progress.getMonthGradeRuleId(), progress.getYearGradeRuleId())
                .filter(StringUtils::isNotBlank).map(it -> it.split(CommonSeparators.WELL_SEPARATOR))
                .flatMap(Arrays::stream).filter(StringUtils::isNotBlank).distinct().toList();
        if (CollectionUtils.isEmpty(upRuleIds)) {
            return Collections.emptyList();
        }
        return tbMemberGradeRuleService.listByIds(upRuleIds);
    }

    @Override
    @Cacheable(value = RedisCacheKey.MEMBER_UPGRADE_COPY_WRITER_KEY, key = "#loginUser.brandId +':' + #loginUser.cId", unless = "#result == null")
    public String getUpgradeCopywriter(LoginUser loginUser, String grade) {
        // 获取会员信息
        TbMemberAsset member = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        // 查询卡等列表
        List<TbMemberGrade> gradeList = tbMemberGradeService.getGradeSortAscByGroupIdWithException(loginUser.getBrandId());
        // 不参与升降级的卡等
        if (this.checkMemberIsHonorable(gradeList, member)) {
            return StringUtils.EMPTY;
        }
        // 集团下参与升、降级的最高卡等
        TbMemberGrade maxGrade = gradeList.stream().filter(item -> item.getUpGradationStatus()==1).max(Comparator.comparing(TbMemberGrade::getSort)).orElse(null);
        // 判断会员等级是否是最高卡等
        if (Objects.nonNull(maxGrade) && StringUtils.equals(member.getGrade(), maxGrade.getCode())) {
            return StringUtils.EMPTY;
        }
        // 获取 用户 以及传入的 grade信息
        TbMemberGrade currentMemberGrade = gradeList.stream().filter(o -> StringUtils.equals(o.getCode(), member.getGrade())).findFirst().orElse(null);
        TbMemberGrade inputMemberGrade = gradeList.stream().filter(o -> StringUtils.equals(o.getCode(), grade)).findFirst().orElse(null);
        if (Objects.isNull(currentMemberGrade)) {
            throw BizNotFoundException.error(PointsEnum.MEMBER_GRADE_NOT_EXISTS);
        }

        // 如果无法取得传入的等级条件（如不传） 或 与传入等级相同时。则设置为下一等级。
        if (Objects.isNull(inputMemberGrade) || currentMemberGrade.getSort().equals(inputMemberGrade.getSort())) {
            inputMemberGrade = gradeList.stream().filter(o -> o.getSort() > currentMemberGrade.getSort()).findFirst().orElse(null);
        }

        // 当前卡等为最高卡等时 或 如果当前卡等高于传入卡等。则不展示文案。
        if (Objects.isNull(inputMemberGrade) || currentMemberGrade.getSort() > inputMemberGrade.getSort()) {
            log.info("upper writer empty");
            return StringUtils.EMPTY;
        }

        TbMemberGrade minGrade = gradeList.stream().min(Comparator.comparing(TbMemberGrade::getSort)).orElse(new TbMemberGrade());

        log.info("replaceUpgradeCopywriter {}, {}", "获取升级文案", member.getVipcode());
        // 查询current等级到input等级所展示的文案。
        return replaceUpgradeCopywriter(currentMemberGrade, inputMemberGrade, member, minGrade);
    }

    @Override
    @Cacheable(value = RedisCacheKey.MEMBER_UPGRADE_NEXT_GRADE_NEED_MONEY, key = "#loginUser.brandId +':' + #loginUser.cId", unless = "#result == null")
    public MemberUpgradeAmountResource getUpgradeMoney(LoginUser loginUser) {
        MemberUpgradeAmountResource result = MemberUpgradeAmountResource.builder().topGrade(false).amount(0).enableDownGrade(Boolean.FALSE).progressBar(0).build();
        // 获取当前用户信息
        TbMemberAsset member = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        // 查询卡等列表
        List<TbMemberGrade> gradeList = tbMemberGradeService.getGradeSortAscByGroupIdWithException(loginUser.getBrandId());
        // 不参与升降级的卡等
        if (this.checkMemberIsHonorable(gradeList, member)) {
            result.setTopGrade(Boolean.TRUE);
            result.setProgressBar(ONE_HUNDRED);
            return result;
        }
        // 过滤不参与升降级的卡等
        gradeList = gradeList.stream().filter(it -> it.getUpGradationStatus() == 1).toList();
        if (CollectionUtils.isEmpty(gradeList)) {
            throw BizNotFoundException.error(PointsEnum.BRAND_GRADE_NOT_EXISTS);
        }
        // 获取当前会员等级与下标
        int currentIndex = gradeList.stream().map(TbMemberGrade::getCode).toList().indexOf(member.getGrade());
        if (currentIndex < 0) {
            throw BizNotFoundException.error(PointsEnum.MEMBER_GRADE_NOT_EXISTS);
        }
        // 获取用户当前等级
        TbMemberGrade currentMemberGrade = gradeList.stream().filter(o -> StringUtils.equals(o.getCode(), member.getGrade())).findFirst().orElse(null);
        // 尊享卡或者会员卡等不存在
        if (Objects.isNull(currentMemberGrade)) {
            return result;
        }
        // 获取下一等级
        TbMemberGrade nextMemberGrade = gradeList.stream().filter(o -> o.getSort() > currentMemberGrade.getSort()).findFirst().orElse(null);
        // 如果是最高卡等，则不展示
        String grade = member.getGrade();
        if (Objects.isNull(nextMemberGrade)) {
            result.setTopGrade(true);
            result.setProgressBar(ONE_HUNDRED);
        } else {
            grade = nextMemberGrade.getCode();
        }
        log.info("getUpgradeMoney {}, {}", member.getVipcode(), grade);
        List<TbMemberGradeRule> rules = tbMemberGradeRuleService.queryByGroupIdAndGradeAndType(loginUser.getBrandId(), grade, Boolean.TRUE.equals(result.getTopGrade()) ? 0 : 1);
        // 过滤掉身份认证的规则信息
        rules = rules.stream().filter(it -> !RuleTypeEnum.isIdentityRule(it.getRuleType())).toList();
        if (CollectionUtils.isEmpty(rules)) {
            return result;
        }
        Date now = new Date();
        // 确定会员统计金额的开始时间
        MemberYearAmountCalDto calDto = memberGradeStrategyService.getCalculateYearAmountBeginDate(member.getGroupId(), member.getVipcode());
        log.info("MemberYearAmountCalDto {}", JsonUtils.objToString(calDto));
        Date beginDate = calDto.getBeginDate();
        TbMemberGradeChangeDetail changeDetail = tbMemberGradeChangeDetailService.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(loginUser.getBrandId(), member.getVipcode(), DateUtil.formatDateTime(beginDate));
        // 如果是最高卡等，则查询最高卡等变动明细，统计保级所需金额
        Date expireTime = null;
        if (KipDateUtils.checkGradeChangeDetail(changeDetail)) {
            if (Boolean.TRUE.equals(result.getTopGrade())) {
                beginDate = changeDetail.getCreateDate();
            }
            // 统计类型: 1:自然年，2:周期年
            expireTime = Boolean.TRUE.equals(calDto.getCycleYear()) ? DateUtil.offsetMonth(changeDetail.getCreateDate(), 12) : DateUtil.endOfYear(DateUtil.offsetMonth(changeDetail.getCreateDate(), 12));
        } else {
            // 统计类型: 1:自然年，2:周期年
            expireTime = Boolean.TRUE.equals(calDto.getCycleYear()) ? DateUtil.offsetMonth(beginDate, 12) : DateUtil.endOfYear(DateUtil.offsetMonth(beginDate, 12));
        }
        double amount;
        for (TbMemberGradeRule rule : rules) {
            // 规则设置的金额
            double ruleAmount = Objects.nonNull(rule.getMoney()) ? rule.getMoney().doubleValue() : 0;
            // 规则类型
            String calculateType = RuleTypeEnum.getRuleType(rule.getRuleType());
            // 查询业态对应的店铺号
            List<String> shopIds = tbBaseShopService.getContractNoList(member.getGroupId(), rule.getFormats());
            // 是最高卡等，则需要判断是否保级成功
            if (Boolean.TRUE.equals(result.getTopGrade()) && !RuleTimeEnum.YEAR.getValue().equals(calculateType)) {
                if (tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(member, rule, beginDate, now, shopIds)) {
                    result.setAmount(0);
                    // 统计类型: 1:自然年，2:周期年
                    expireTime = Boolean.TRUE.equals(calDto.getCycleYear()) ? DateUtil.offsetMonth(expireTime, 12) : DateUtil.endOfYear(DateUtil.offsetMonth(expireTime, 12));
                    break;
                }
            } else {
                if (!RuleTimeEnum.YEAR.getValue().equals(calculateType)) {
                    continue;
                }
                amount = tbSalesDetailService.getMemberSaleAmountBetweenTime(member, rule, beginDate, now, shopIds);
                // 进度条长度
                if (Boolean.FALSE.equals(result.getTopGrade())) {
                    result.setProgressBar(this.getProgressBar(NumberUtil.sub(rule.getMoney(), BigDecimal.valueOf(amount)).toString(), gradeList.size(), currentIndex + 1, rule));
                }
                // 统计类型: 1:自然年，2:周期年
                if (amount >= ruleAmount) {
                    expireTime = Boolean.TRUE.equals(calDto.getCycleYear()) ? DateUtil.offsetMonth(expireTime, 12) : DateUtil.endOfYear(DateUtil.offsetMonth(expireTime, 12));
                    break;
                }
                result.setAmount(NumberUtil.sub(ruleAmount + "", amount + "").setScale(0, RoundingMode.UP).intValue());
            }
        }
        // 等级过期时间
        result.setGradeExpire(DateUtil.format(expireTime, DEFAULT_FORMAT));
        return result;
    }

    private String replaceUpgradeCopywriter(TbMemberGrade currentMemberGrade, TbMemberGrade inputMemberGrade, TbMemberAsset member, TbMemberGrade minGrade) {
        // 获取目标升级等级的文案信息
        TbMemberGradeRuleProgress ruleProgress = tbMemberGradeRuleProgressMapper.queryByGroupIdAndGrade(member.getGroupId(), inputMemberGrade.getCode());
        if (Objects.isNull(ruleProgress) || StringUtils.isBlank(ruleProgress.getUpProgressTextNotShow())) {
            log.info("replaceUpgradeCopywriter - ruleProgress error: {}", ruleProgress);
            return StringUtils.EMPTY;
        }
        log.info("replaceUpgradeCopywriter {}, {}", "获取升级文案", member.getVipcode());
        // 当前时间
        Date now = new Date();
        // 根据策略工厂替换处理规则
        List<TbMemberGradeRule> tbMemberGradeRules = tbMemberGradeRuleService.queryGradeRulesByGroupIdAndGradeAndRuleTypes(member.getGroupId(),
                inputMemberGrade.getCode(), RuleTypeEnum.getUpgradeVal());
        if (CollectionUtils.isEmpty(tbMemberGradeRules)) {
            return StringUtils.EMPTY;
        }
        // 统计金额开始时间
        MemberYearAmountCalDto calDto = memberGradeStrategyService.getCalculateYearAmountBeginDate(member.getGroupId(), member.getVipcode());
        log.info("MemberYearAmountCalDto {}, {}", JsonUtils.objToString(calDto), member.getVipcode());
        Date startDate = calDto.getBeginDate();
        // 会员当前等级不是最低卡等
        boolean isMinGrade = StringUtils.equals(member.getGrade(), minGrade.getCode()) && calDto.getMinGrade();
        // 获取会员最近的等级变更记录
        TbMemberGradeChangeDetail changeDetail = tbMemberGradeChangeDetailService.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(member.getGroupId(), member.getVipcode(), DateUtil.formatDateTime(startDate));
        // 升级展示文案
        String text = ruleProgress.getUpProgressTextNotShow();
        // 替换当前卡等文案
        text = text.replaceAll(CURRENT_CARD_LEVEL, currentMemberGrade.getName());
        // 替换周期年文案
        text = text.replaceAll(UP_CYCLE_YEAR_END_DATE, this.getGradeValidEndTime(member, isMinGrade, now, changeDetail, Boolean.TRUE));
        // 替换自然年文案
        text = text.replaceAll(UP_NATURAL_YEAR_END_DATE, this.getGradeValidEndTime(member, isMinGrade, now, changeDetail, Boolean.FALSE));
        // 替换月文案
        text = text.replaceAll(UP_BY_MONTH_END_DATE, DateUtil.formatDate(DateUtil.endOfMonth(now)));
        // 替换日文案
        text = text.replaceAll(UP_BY_DAY_END_DATE, DateUtil.formatDate(now));
        for (TbMemberGradeRule rule : tbMemberGradeRules) {
            // 规则设置的金额
            double ruleAmount = Objects.nonNull(rule.getMoney()) ? rule.getMoney().doubleValue() : 0;
            // 用户时间段内消费金额
            Double amount = tbSalesDetailService.getGradeRuleCacheAmount(rule, member);
            if (Objects.isNull(amount)) {
                // 规则类型
                String calculateType = RuleTypeEnum.getRuleType(rule.getRuleType());
                // 业态对应的店铺
                List<String> shopIds = tbBaseShopService.getContractNoList(member.getGroupId(), rule.getFormats());
                // 升级金额统计开始时间
                Date beginDate = memberGradeStrategyService.getUpgradeRuleBeginDate(calculateType, startDate);
                // 用户时间段内消费金额
                amount = tbSalesDetailService.getMemberSaleAmountBetweenTime(member, rule, beginDate, now, shopIds);
            }
            // 替换文案内参数
            text = text.replace("{" + rule.getId() + "}", NumberUtil.sub(ruleAmount + "", amount + "").setScale(0, RoundingMode.UP).intValue() + "");
        }
        return text;
    }

    /**
     * 获取等级有效期的时间
     * @param member：会员信息
     * @param isMinGrade：是否是最低卡等
     * @param now：系统当前时间
     * @param changeDetail：等级变更记录
     * @param cycleYear：是否是周期年 true:周期年 false:自然年
     * @return
     */
    public String getGradeValidEndTime(TbMemberAsset member, boolean isMinGrade, Date now, TbMemberGradeChangeDetail changeDetail, boolean cycleYear) {
        Date changeDate = now;
        // 是最低卡等，则判断等级变更类型，非保级降级及人工调整逻辑，则去当前时间
        if (isMinGrade) {
            return DateUtil.formatDate(now);
        }
        if (KipDateUtils.checkGradeChangeDetail(changeDetail) && now.getTime() > changeDetail.getCreateDate().getTime()) {
            changeDate = changeDetail.getCreateDate();
        }
        changeDate = cycleYear ? DateUtil.offsetMonth(changeDate, 12) : DateUtil.endOfYear(DateUtil.offsetMonth(changeDate, 12));
        log.info("getGradeValidEndTime - member: {}-{}, changeDetailDate: {}", member.getGroupId(), member.getVipcode(), changeDate);
        return DateUtil.formatDate(changeDate);
    }

    /**
     * 对象转化
     * @param rule rule
     * @return MemberGradeProgressSwitchDto
     */
    private MemberGradeProgressSwitchDto parseItem(TbMemberGradeRule rule) {
        MemberGradeProgressSwitchDto dto = MemberGradeProgressSwitchDto.builder().ruleId(rule.getId() + "").formatsDesc(rule.getFormatsDesc()).build();
        if (Objects.equals(RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue(), rule.getRuleType())) {
            dto.setComputeManner("按当日累计");
            dto.setSort(3);
        } else if (Objects.equals(RuleTypeEnum.ACCUMULATIVE_CONSUME_UPGRADE.getValue(), rule.getRuleType())) {
            dto.setComputeManner(rule.getSumType() == 1 ? "按自然年累计" : "按周期年累计");
            dto.setSort(1);
        } else {
            dto.setComputeManner("按月累计");
            dto.setSort(2);
        }
        return dto;
    }
}
