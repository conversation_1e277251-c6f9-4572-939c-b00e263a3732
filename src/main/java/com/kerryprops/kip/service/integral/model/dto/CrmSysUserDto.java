package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - member-points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/08/2024 15:45
 *********************************************************************************************************************
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CrmSysUserDto implements Serializable {

    private Long id;

    private String name;

    private String email;

    /**
     * 状态, 0:删除; 1:启用 2:过期 3：禁用
     */
    private Integer status;

    /**
     * CRM中用户id
     */
    private Long crmUserId;

}
