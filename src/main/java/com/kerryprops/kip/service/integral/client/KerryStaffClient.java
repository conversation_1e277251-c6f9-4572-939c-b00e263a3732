package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.vo.KipMemberVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/19/2022 16:35
 **********************************************************************************************************************/

@FeignClient(name = "kerry-staff-service", url = "${kerry.services.kerry-staff:default}")
public interface KerryStaffClient {

    @GetMapping("/customer/user/simple")
    CustomerUserDto findByOpenId(@RequestParam("openId") String openId);

    @GetMapping("/customer/wx/byMobileAndAppId")
    CustomerThirdPartyDto getWxInfoByMobileAndAppId(@RequestParam("mobile") String mobile, @RequestParam("appId") String appId);

    /**
     * 调用profile-service修改会员信息
     * @param vo
     * @return
     */
    @PutMapping("/customer/crm/internal-modify")
    KerryResultDto<String> modifyProfileInfo(@RequestBody KipMemberVO vo);

    @PostMapping(value = "/customer/user/crm-internal")
    KerryResultDto<List<CustomerUserDto>> getProfileUsers(@RequestBody CrmProfileQueryDto dto);

    /**
     * 获取k+业务员信息
     * @param userIds userIds
     * @return list list
     */
    @PostMapping("/v2/kerry/staffs/userInfo")
    List<CrmSysUserDto> getUserList(@RequestBody List<Long> userIds);

}
