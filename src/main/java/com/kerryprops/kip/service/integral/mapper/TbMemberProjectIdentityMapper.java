package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberProjectIdentity;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/26/2023 11:16
 **********************************************************************************************************************/
public interface TbMemberProjectIdentityMapper extends BaseMapper<TbMemberProjectIdentity> {

//    void saveOrUpdateBatch(List<TbMemberProjectIdentity>list);
//
//    /**
//     * 查询用户在多个楼盘下的身份信息
//     * @param kipUserId
//     * @param projectIds
//     * @return
//     */
//    List<TbMemberProjectIdentity> findByKipUserIdAndProjectIds(@Param("kipUserId") String kipUserId, @Param("projectIds") List<String> projectIds);
//
//    void deleteByKipUserId(@Param("kipUserId") String kipUserId);
//
//    /**
//     * 通过kipUserId+projectIds删除会员身份认证信息
//     * @param kipUserId
//     * @param projectIds
//     */
//    void deleteByKipUserIdAndProjectIds(@Param("kipUserId") String kipUserId, @Param("projectIds") List<String> projectIds);
//
//    void removeByUserIdAndNotInProjectIds(@Param("kipUserId") String kipUserId, @Param("projectIds") List<String> projectIds);

}
