package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *  自动营销-会员标签表
 * <AUTHOR> Bert
 * Created Date - 12/20/2022 11:16
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_auto_sale_member_tag")
public class TbAutoSaleMemberTag implements Serializable {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 集团Id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 标签名称
     */
    @TableField("tag_name")
    private String tagName;

    /**
     * 1:统计中  2：统计成功 3：统计失败
     */
    @TableField("status")
    private int status;

    /**
     * 标签类型（1：逻辑标签；2：无逻辑标签）
     */
    @TableField("type")
    private Integer type;

    /**
     * 一级分类id
     */
    @TableField("firsort_id")
    private Long firSortId;

    /**
     * 二级分类id
     */
    @TableField("secsort_id")
    private Long secSortId;

    /**
     * 目标人数
     */
    @TableField("target_number")
    private Long targetNumber;
    /**
     * 规则条件
     */
    @TableField("condition_param")
    private String conditionParam;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 查询SQL条件
     */
    @TableField("query_conditions")
    private String queryConditions;

    /**
     * 标签描述
     */
    @TableField("describes")
    private String describes;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

}
