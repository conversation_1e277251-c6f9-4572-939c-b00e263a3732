package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbIntegralActivityRate;
import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import com.kerryprops.kip.service.integral.mapper.TbIntegralActivityRateMapper;
import com.kerryprops.kip.service.integral.mapper.TbIntegralShopRateMapper;
import com.kerryprops.kip.service.integral.service.TbIntegralActivityRateService;
import com.kerryprops.kip.service.integral.service.TbIntegralShopRateService;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralActivityRateResource;
import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbIntegralActivityRateResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbIntegralShopRateResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbIntegralActivityRateServiceImpl extends ServiceImpl<TbIntegralActivityRateMapper, TbIntegralActivityRate> implements TbIntegralActivityRateService {

    private final TbIntegralActivityRateMapper tbIntegralActivityRateMapper;



}
