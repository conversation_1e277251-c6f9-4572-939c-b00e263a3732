package com.kerryprops.kip.service.integral.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.PointsRedemptionEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.model.dto.PaymentRecordBodyDto;
import com.kerryprops.kip.service.integral.model.dto.PaymentRecordDetailDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 05/04/2023 16:03
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentMiddleWareSyncRecordListener {

    private final RedisService redisService;
    private final SysDictService sysDictService;
    private final TbBaseShopService tbBaseShopService;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbPointsDetailService tbPointsDetailService;
    private final TbPaymentPushRecordService tbPaymentPushRecordService;
    private final TbMemberPointsChangeService tbMemberPointsChangeService;
    private final MemberSalePointsProcessService memberSalePointsProcessService;

    /**
     * 对接payment自动积分的类型
     * 1、KERRY_PAY 优惠买单
     * 2、REWARDS_MALL 积分商场
     */
    private static final List<String> PAY_TYPES = Arrays.asList(IntegralConstant.KERRY_PAY, IntegralConstant.REWARDS_MALL);

    /**
     * 处理消息
     * @param message 消息主题
     * @param channel 信道
     * @throws Exception
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.PAYMENT_SYNC_PAY_RECORD_QUEUE, containerFactory = "paymentContainerFactory")
    public void receiveMsg(Channel channel, Message message) throws Exception {
        String msg =new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.warn("PaymentMiddleWareSyncRecordListener receiveMsg null");
            return;
        }
        log.info("PaymentMiddleWareSyncRecordListener receiveMsg: {}", msg);
        PaymentRecordBodyDto bodyDto = JsonUtils.stringToObj(msg, PaymentRecordBodyDto.class);
        if (Objects.isNull(bodyDto) || StringUtils.isBlank(bodyDto.getPayload())) {
            log.warn("PaymentMiddleWareSyncRecordListener参数转换失败: {}", msg);
            return;
        }
        // 交易记录明细
        PaymentRecordDetailDto detailDto = JsonUtils.stringToObj(bodyDto.getPayload(), PaymentRecordDetailDto.class);
        if (Objects.isNull(detailDto)) {
            log.warn("PaymentMiddleWareSyncRecordListener payload参数转换失败: {}", msg);
            return;
        }
        // 交易类型判断
        if (PAY_TYPES.stream().noneMatch(item -> item.equalsIgnoreCase(detailDto.getProductType()))) {
            log.warn("PaymentMiddleWareSyncRecordListener接受的支付记录非积分商城类型: {}", msg);
            return;
        }
        // 保存推送日志
        tbPaymentPushRecordService.savePushRecord(detailDto);
        // 获取会员冗余参数
        Map<String, String> params = detailDto.getExtraParams();
        if (MapUtils.isEmpty(params)) {
            log.warn("PaymentMiddleWareSyncRecordListener接受的支付记录会员信息参数为空: {}", msg);
            return;
        }
        String mallId = MapUtils.getString(params, "mallId");
        String groupId = MapUtils.getString(params, "groupId");
        String vipcode = MapUtils.getString(params, "vipcode");
        // 店铺id
        String tenantId = MapUtils.getString(params, "tenantId");
        if (StringUtils.isAnyBlank(mallId, groupId, vipcode)) {
            log.warn("PaymentMiddleWareSyncRecordListener积分商城支付会员信息参数缺失: {}", msg);
            return;
        }
        // 查询会员信息
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(vipcode, groupId);
        if (Objects.isNull(tbMemberAsset)) {
            log.warn("PaymentMiddleWareSyncRecordListener查询会员信息为空: {}-{}", vipcode, groupId);
            return;
        }
        SysDict sysDict = sysDictService.findByDictType(detailDto.getProductType());
        if (Objects.isNull(sysDict)) {
            log.warn("PaymentMiddleWareSyncRecordListener查询的字典值不存在: {}-{}", detailDto.getOrderNo(), detailDto.getProductType());
            return;
        }
        // 获取默认店铺信息
        TbBaseShop baseShop = this.getBaseShop(detailDto.getProductType(), groupId, mallId, tenantId);
        if (Objects.isNull(baseShop)) {
            return;
        }
        SaleTypeEnum saleTypeEnum = SaleTypeEnum.getSaleTypeByDictType(detailDto.getProductType());
        Assert.notNull(saleTypeEnum, "字典值不存在");
        // 组装自动积分参数
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId(groupId)
                .shopId(baseShop.getContractNo()).baseShop(baseShop).vipcode(vipcode)
                .mallId(mallId).member(tbMemberAsset).amount(detailDto.getOrderAmount()).remark(detailDto.getProductType()).remarkName(sysDict.getDictName())
                .saleNo(detailDto.getOrderNo()).saleDate(StringUtils.isBlank(detailDto.getFinishedDate()) ? DateUtil.formatDateTime(new Date()) : detailDto.getFinishedDate())
                .createUser(detailDto.getProductType()).saleType(saleTypeEnum.getValue()).refund(Boolean.FALSE).build();
        // 自动积分销售单号
        String mallSaleNoKey = String.format(RedisCacheKey.SALES_NO_KEY, detailDto.getOrderNo());
        if (Boolean.TRUE.equals(redisService.setSaleNoIfAbsent(mallSaleNoKey, IntegralConstant.KIP_POINTS))) {
            try {
                Integer points = memberSalePointsProcessService.salePointsProcess(pointsDto);
                log.info("积分商城支付自动积分获取积分: {}-{}-{}-{}", groupId, vipcode, detailDto.getOrderNo(), points);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                redisService.delKeys(Collections.singletonList(mallSaleNoKey));
            }
        } else {
            log.info("PaymentMiddleWareSyncRecordListener销售单号支付记录重复推送: {}", detailDto.getOrderNo());
        }
    }

    /**
     * 获取默认的线上商城店铺
     * @param groupId
     * @param mallId
     * @return
     */
    private TbBaseShop getDefaultShop(String groupId, String mallId) {
        return TbBaseShop.builder().id("**********").shopCode("**********").contractNo("**********").shopName("线上商城").brandName("线上商城").groupId(groupId).mallId(mallId).build();
    }

    /**
     * 处理推送过来的退款消息
     * @param message 消息主题
     * @param channel 信道
     * @throws Exception
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.REFUND_SYNC_MALL_RECORD_QUEUE, containerFactory = "paymentContainerFactory")
    public void receiveRefundMsg(Channel channel, Message message) throws Exception {
        String msg =new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.warn("RefundMiddleWareSyncRecordListener receiveMsg null");
            return;
        }
        log.info("RefundMiddleWareSyncRecordListener receiveMsg: {}", msg);
        PaymentRecordBodyDto bodyDto = JsonUtils.stringToObj(msg, PaymentRecordBodyDto.class);
        if (Objects.isNull(bodyDto) || StringUtils.isBlank(bodyDto.getPayload())) {
            log.warn("RefundMiddleWareSyncRecordListener参数转换失败: {}", msg);
            return;
        }
        // 交易记录明细
        PaymentRecordDetailDto detailDto = JsonUtils.stringToObj(bodyDto.getPayload(), PaymentRecordDetailDto.class);
        if (Objects.isNull(detailDto)) {
            log.warn("RefundMiddleWareSyncRecordListener交易明细为空: {}", msg);
            return;
        }
        // 交易类型判断
        if (PAY_TYPES.stream().noneMatch(item -> item.equalsIgnoreCase(detailDto.getProductType()))) {
            log.warn("RefundMiddleWareSyncRecordListener接受的退款记录非积分商城类型: {}", msg);
            return;
        }
        // 保存推送日志
        tbPaymentPushRecordService.savePushRecord(detailDto);
        // 获取会员冗余参数
        Map<String, String> params = detailDto.getExtraParams();
        if (params == null || params.size() <= 0) {
            log.warn("RefundMiddleWareSyncRecordListener接受的退款记录会员信息参数为空: {}", msg);
        }
        String mallId = MapUtils.getString(params, "mallId");
        String groupId = MapUtils.getString(params, "groupId");
        String vipcode = MapUtils.getString(params, "vipcode");
        // 店铺id
        String tenantId = MapUtils.getString(params, "tenantId");
        if (StringUtils.isAnyBlank(mallId, groupId, vipcode)) {
            log.warn("RefundMiddleWareSyncRecordListener积分商城退款会员信息参数缺失: {}", msg);
            return;
        }
        // 查询会员信息
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(vipcode, groupId);
        if (Objects.isNull(tbMemberAsset)) {
            log.warn("RefundMiddleWareSyncRecordListener查询会员信息为空: {}-{}", vipcode, groupId);
            return;
        }
        String refundType = this.getRefundType(detailDto.getProductType());
        SaleTypeEnum saleTypeEnum = SaleTypeEnum.getSaleTypeByDictType(refundType);
        Assert.notNull(saleTypeEnum, "销售类型不存在");
        SysDict sysDict = sysDictService.findByDictType(detailDto.getProductType());
        Assert.notNull(saleTypeEnum, "字典值不存在");
        // 获取默认店铺信息
        TbBaseShop baseShop = this.getBaseShop(detailDto.getProductType(), groupId, mallId, tenantId);
        if (Objects.isNull(baseShop)) {
            return;
        }
        // 组装退款参数
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder()
                .id(IdUtil.simpleUUID()).groupId(groupId).vipcode(vipcode).mallId(mallId)
                .shopId(baseShop.getContractNo()).baseShop(baseShop).member(tbMemberAsset)
                // amount是退款金额，orderAmount是销售金额
                .refundAmount(detailDto.getAmount()).remark(refundType)
                .remarkName(sysDict.getDictName()).saleNo(detailDto.getOrderNo())
                .saleDate(StringUtils.isBlank(detailDto.getFinishedDate()) ? DateUtil.formatDateTime(new Date()) : detailDto.getFinishedDate())
                .createUser(detailDto.getProductType()).saleType(saleTypeEnum.getValue()).refund(true).build();
        String refundKey = String.format(RedisCacheKey.REFUND_LOCK, detailDto.getOrderNo());
        if (Boolean.FALSE.equals(redisService.setSaleNoIfAbsent(refundKey, IntegralConstant.KIP_POINTS))) {
            log.info("RefundMiddleWareSyncRecordListener重复推送: {}", detailDto.getOrderNo());
            return;
        }
        try {
            // 退款后，该笔优惠买单自动积分需要退回
            memberSalePointsProcessService.refundSalePointsProcess(pointsDto);
            // 优惠买单积分抵现-退款退回积分抵现的积分
            if (saleTypeEnum.getValue().equals("9")) {
                this.handleKerryPayRedemption(pointsDto);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            redisService.delKeys(Collections.singletonList(refundKey));
        }
    }

    /**
     * 查询店铺信息
     * @param productType
     * @param groupId
     * @param mallId
     * @param tenantId
     * @return
     */
    private TbBaseShop getBaseShop(String productType, String groupId, String mallId, String tenantId) {
        TbBaseShop baseShop = null;
        // 积分商场取虚拟租户
        if (IntegralConstant.REWARDS_MALL.equals(productType)) {
            baseShop = this.getDefaultShop(groupId, mallId);
        } else {
            baseShop = tbBaseShopService.getByTenantId(tenantId, mallId);
            if (Objects.isNull(baseShop)) {
                log.warn("PaymentMiddleWareSyncRecordListener支付记录推送店铺ID对应租户不存在: {}-{}-{}", groupId, mallId, tenantId);
            }
        }
        return baseShop;
    }

    /**
     * 处理优惠买单积分抵现-退款退积分
     * @param pointsDto
     */
    private void handleKerryPayRedemption(SalesAutoPointsDto pointsDto) {
        TbPointsDetail detail = tbPointsDetailService.queryKerryPayPointsRedemptionAmountRecord(pointsDto.getGroupId(), pointsDto.getMallId(),
                pointsDto.getVipcode(), pointsDto.getSaleNo(), pointsDto.getShopId(), PointsRedemptionEnum.KERRY_PAY_CASH_OUT.getCode());
        if (Objects.isNull(detail)) {
            return;
        }
        PointsRedemptionEnum redemptionEnum = PointsRedemptionEnum.KERRY_PAY_CASH_OUT_REFUND;
        // 积分抵现扣除的积分，退款后需要退回扣除的积分数
        MemberPointsChangeDto changeDto = MemberPointsChangeDto.builder().adjustPointsId(IdUtil.simpleUUID())
                .groupId(pointsDto.getGroupId()).mallId(pointsDto.getMallId()).vipcode(pointsDto.getVipcode()).saleNo(pointsDto.getSaleNo())
                .saleType(SaleTypeEnum.KERRY_PAY.getValue()).saleAmount(BigDecimal.valueOf(detail.getAmount())).dictValue(redemptionEnum.getCode())
                .dictName(redemptionEnum.getDesc()).associatedBusinessId(pointsDto.getSaleNo()).shopNo(pointsDto.getShopId()).creator("KERRY_PAY")
                .changePointsNum(Math.abs(detail.getAmount()))
                .remark(String.format("%s - 退款金额: %s, 退回积分: %s" , redemptionEnum.getDesc(), pointsDto.getAmount(), Math.abs(detail.getAmount())))
                .type("S").build();
        tbMemberPointsChangeService.updateMemberPoints(changeDto);
    }

    /**
     * 获取对应的退款类型
     * @param productType
     * @return
     */
    private String getRefundType(String productType) {
        if (IntegralConstant.REWARDS_MALL.equals(productType)) {
            return IntegralConstant.REFUND_REWARDS_MALL;
        }
        if (IntegralConstant.KERRY_PAY.equals(productType)) {
            return IntegralConstant.REFUND_KERRY_PAY;
        }
        return productType;
    }

}
