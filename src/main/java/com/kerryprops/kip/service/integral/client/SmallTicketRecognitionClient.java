package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.SmallTicketUploadDto;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketCallbackResource;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 06/27/2024 14:32
 **********************************************************************************************************************/

@FeignClient(name = "ocr-service", url = "${kerry.services.ocr-service:default}")
public interface SmallTicketRecognitionClient {

    /**
     * 上传前端用户提交的小票给合合科技
     * @param env 调用方环境
     * @param priority 任务优先级，值为 1 到 9 的整数，数字越大则优先级越高
     * @return
     */
    @PostMapping(value = "/ihcr_recognize?service=mall_receipt", consumes = MediaType.IMAGE_JPEG_VALUE)
    SmallTicketUploadDto uploadFile(@RequestHeader("x-ti-app-id") String appId,
                                    @RequestHeader("x-ti-secret-code") String secret,
                                    @RequestParam("mall") String mall,
                                    @RequestParam("env") String env,
                                    @RequestParam("priority") Integer priority,
                                    @RequestParam("outer_id") String outerId,
                                    @RequestBody byte[] bytes);

    /**
     * 查询审核结果
     * @param appId
     * @param secret
     * @param taskId
     * @param verifyToken
     * @return
     */
    @GetMapping("/ihcr_query?service=mall_receipt")
    SmallTicketCallbackResource queryUploadResult(@RequestHeader("x-ti-app-id") String appId,
                                                  @RequestHeader("x-ti-secret-code") String secret,
                                                  @RequestParam("task_id") String taskId,
                                                  @RequestParam("verify_token") String verifyToken);

    /**
     * 查询审核结果
     * @param appId
     * @param secret
     * @param outerId: 嘉里id
     * @param verifyToken
     * @return
     */
    @GetMapping("/ihcr_query?service=mall_receipt")
    SmallTicketCallbackResource queryUploadResultByOuterId(@RequestHeader("x-ti-app-id") String appId,
                                                  @RequestHeader("x-ti-secret-code") String secret,
                                                  @RequestParam("outer_id") String outerId,
                                                  @RequestParam("verify_token") String verifyToken);

}
