package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbActivityPromotion;
import com.kerryprops.kip.service.integral.entity.TbSaleMatchedPromotion;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/17/2023 16:56
 **********************************************************************************************************************/
public interface TbSaleMatchedPromotionService {

    void saveRecord(List<TbActivityPromotion> list, SalesAutoPointsDto dto);

    List<TbSaleMatchedPromotion> findBySellNos(List<String> sellNos);

    List<TbSaleMatchedPromotion> findByAdjustIds(List<String> adjustIds);

}
