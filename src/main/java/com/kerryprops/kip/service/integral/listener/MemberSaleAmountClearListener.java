package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.ClearMemberSalesSumDto;
import com.kerryprops.kip.service.integral.model.dto.MemberSaleActivityDto;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import com.kerryprops.kip.service.integral.service.RedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC 清除会员消费金额缓存
 * <AUTHOR> Bert
 * Created Date - 03/22/2023 00:10
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class MemberSaleAmountClearListener {

    private final RedisService redisService;
    private final MallConfig mallConfig;

    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.XCRM_MEMBER_SALE_SUM}, containerFactory = "crmContainerFactory")
    public void receiveMsg(Message message) {
        String msg = new String(message.getBody());
        log.info("MemberSaleAmountClearListener: {}" , msg);
        if (StringUtils.isBlank(msg)) {
            return;
        }
        ClearMemberSalesSumDto sumDto = JsonUtils.stringToObj(msg, ClearMemberSalesSumDto.class);
        if (Objects.isNull(sumDto)) {
            return;
        }
        String groupId = sumDto.getGroupId();
        String vipcode = sumDto.getVipcode();
        String kipUserId = sumDto.getKipUserId();
        // 查询会员信息
        List<String> saleAmountKeys = new ArrayList<>();
        saleAmountKeys.add(String.format(RedisCacheKey.MEMBER_GRADE_RULE_SALE_MONEY, groupId, vipcode));
        saleAmountKeys.add(String.format(IntegralConstant.DEF_REDIS_KEY, RedisCacheKey.MEMBER_AMOUNT_CAL_START_DATE, groupId, vipcode));
        if (StringUtils.isNotBlank(kipUserId)) {
            for (String tmpGroupId: mallConfig.isKoBigGroup(groupId) ? mallConfig.getKoBigGroupIds() : Collections.singletonList(groupId)) {
                saleAmountKeys.add(String.format(IntegralConstant.DEF_REDIS_KEY, RedisCacheKey.INDEX_CRM_MEMBER_BY_KIP_USERID, tmpGroupId, kipUserId));
                saleAmountKeys.add(String.format(IntegralConstant.DEF_REDIS_KEY, RedisCacheKey.MEMBER_UPGRADE_NEXT_GRADE_NEED_MONEY, tmpGroupId, kipUserId));
                saleAmountKeys.add(String.format(IntegralConstant.DEF_REDIS_KEY, RedisCacheKey.MEMBER_UPGRADE_PROGRESS_BAR_KEY, tmpGroupId, kipUserId));
                // vipcode缓存key
                saleAmountKeys.add(String.format(IntegralConstant.DEF_REDIS_KEY, RedisCacheKey.INDEX_CRM_VIPCODE_CACHE_KEY, tmpGroupId, kipUserId));
                // Tick 4.7.0会员升级文案缓存key
                saleAmountKeys.add(String.format(IntegralConstant.DEF_REDIS_KEY, RedisCacheKey.MEMBER_UPGRADE_COPY_WRITER_KEY, tmpGroupId, kipUserId));
                // Tick 4.7.0会员保级文案缓存key
                saleAmountKeys.add(String.format(IntegralConstant.DEF_REDIS_KEY, RedisCacheKey.MEMBER_RELEGATION_COPY_WRITER_KEY, tmpGroupId, kipUserId));
            }
        }
        // 删除key
        redisService.delKeys(saleAmountKeys);
    }

}
