//package com.kerryprops.kip.service.integral.service;
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.kerryprops.kip.service.integral.entity.TbMemberProjectIdentity;
//import com.kerryprops.kip.service.integral.model.dto.CustomerIdentityDto;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 04/26/2023 11:44
// **********************************************************************************************************************/
//public interface TbMemberProjectIdentityService extends IService<TbMemberProjectIdentity> {
//
//    /**
//     * 新增身份信息
//     * @param kipUserId
//     */
//    void saveIdentity(String kipUserId);
//
//    /**
//     * 查询身份信息
//     * @param kipUserId
//     * @param projectId
//     * @return
//     */
//    CustomerIdentityDto findByKipUserIdAndProjectId(String kipUserId, String projectId);
//
//    /**
//     * 删除身份信息
//     * @param kipUserId
//     */
//    void removeIdentity(String kipUserId);
//
//    /**
//     * 根据kipUserId+groupId删除会员身份认证信息
//     * @param kipUserId
//     * @param groupId
//     */
//    void removeByKipUserIdAndGroupId(String kipUserId, String groupId);
//
//}
