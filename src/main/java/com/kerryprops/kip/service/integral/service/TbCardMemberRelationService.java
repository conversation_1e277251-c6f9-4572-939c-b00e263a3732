package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/25/2023 14:02
 **********************************************************************************************************************/
public interface TbCardMemberRelationService {

    /**
     * 查询用户商圈授权信息
     * @param mallId
     * @param aliUserId
     * @return
     */
    TbCardMemberRelation getByMallIdAndAliUserId(String mallId, String aliUserId);

    TbCardMemberRelation getByAliUserId(String aliUserId);

    /**
     * 通过商圈id+外部卡号查询开卡信息
     * @param mallId
     * @param externalCardNo
     * @return
     */
    TbCardMemberRelation getByMallIdAndExternalCardNo(String mallId, String externalCardNo);

    /**
     * 通过商圈id+手机号查询开卡信息
     * @param mallId
     * @param mobile
     * @return
     */
    TbCardMemberRelation getByMallIdAndMobile(String mallId, String mobile);

    /**
     * 通过vipcode查询授权信息
     * @param vipcode
     * @return
     */
    TbCardMemberRelation findByExternalCardNo(String vipcode);

    /**
     * 通过vipcode查询授权信息
     * @param groupId groupId
     * @param mobile mobile
     * @param mallId mallId
     * @return dto dto
     */
    TbCardMemberRelation getByGroupIdAndMobileAndMallId(String groupId, String mobile, String mallId);
}
