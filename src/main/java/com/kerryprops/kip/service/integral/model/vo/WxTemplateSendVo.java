package com.kerryprops.kip.service.integral.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/07/2023 10:03
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxTemplateSendVo implements Serializable {

    private String mallid;

    /**
     * 节点
     */
    private String templateTypeDesc;

    /**
     * 模板参数
     */
    private String keywordValues;

    /**
     * H5跳转链接中的变量数组
     */
    private List<String> url;

    /**
     * 小程序跳转链接
     */
    private List<String> miniPagePath;

    /**
     * 短信模板中的变量数组
     */
    private List<Map<String, String>> smsParam;

    /**
     * 会员手机号
     */
    private String mobile;

    /**
     * 类型，默认1  公众号1，小程序2
     */
    private String type;


    /**
     * 会员等级
     */
    private String grade;

    /**
     * 当前调用服务名称
     */
    private String invokeService;

    private String createUser;

}
