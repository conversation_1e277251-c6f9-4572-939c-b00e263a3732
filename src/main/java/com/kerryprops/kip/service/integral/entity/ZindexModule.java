package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 自定义页面模块表
 * <AUTHOR>
 * @date 2022-12-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_zindex_module")
public class ZindexModule implements Serializable {

    /**
    * 主键
    */
    private Long id;

    /**
    * 集团id collate utf8mb4_general_ci
    */
    private String groupId;

    /**
    * 商场编号 collate utf8mb4_general_ci
    */
    private String mallid;

    /**
    * 模块标识 collate utf8mb4_general_ci
    */
    private String moduleSign;

    /**
    * 列表id
    */
    private Long listId;

    /**
    * 排序
    */
    private Integer sort;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 创建者
    */
    private Long creator;

    /**
    * 更新时间
    */
    private Date updateDate;

    /**
    * 更新者
    */
    private Long updater;

    /**
    * 版本号
    */
    private Integer versions;

    /**
    * 品牌logo标题 collate utf8mb4_general_ci
    */
    private String brandLogoTitle;

    /**
    * 品牌logo背景图 collate utf8mb4_general_ci
    */
    private String brandLogoBg;

    /**
    * 品牌logo背景图英文 collate utf8mb4_general_ci
    */
    private String brandLogoBgEn;

    /**
     * 是否展示在支付宝小程序 1: 展示 0: 不展示
     */
    @TableField("show_ali_mini")
    private Integer showAliMini;

}