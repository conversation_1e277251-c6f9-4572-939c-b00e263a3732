package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/19/2023 09:22
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "海鼎积分抵现请求类")
public class HaiDingPosParamResource implements Serializable {

    @NotBlank(message = "groupId不能为空.")
    @Schema( description = "所属group id")
    private String groupId;

    @NotBlank(message = "vipcode不能为空.")
    @Schema( description = "会员号")
    private String vipcode;

    @NotBlank(message = "shopNo不能为空.")
    @Schema( description = "店铺号")
    private String shopNo;

    @NotBlank(message = "mallId不能为空.")
    @Schema( description = "商场")
    private String mallId;

    @NotBlank(message = "docNo不能为空.")
    @Schema( description = "流水号")
    private String docNo;

    @NotBlank(message = "amount不能为空.")
    @Schema( description = "订单总金额")
    private String amount;

    @Schema( description = "积分抵扣金额")
    private Integer amountOfPoints;

    @Schema( description = "销售类型，POS销售为2")
    private String saleType;

    @Schema( description = "amountOfPoints对应积分数")
    private Integer points;

}
