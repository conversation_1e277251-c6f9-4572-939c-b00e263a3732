package com.kerryprops.kip.service.integral.service;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.kerryprops.kip.service.integral.client.HiveServiceClient;
import com.kerryprops.kip.service.integral.client.HiveVasClient;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.vo.TenantAllVo;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/07/2022 11:06
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class HiveVasService {

    private final HiveVasClient hiveVasClient;
    private final HiveServiceClient hiveServiceClient;
    private final MallConfig mallConfig;
    private final RedisService redisService;

    /**
     * 通过店铺号查询店铺信息
     * @param shopNos
     * @return
     */
    public Map<String, TenantInfoVo> getTenantInfoByShopNos(List<String> shopNos) {
        if (CollectionUtils.isEmpty(shopNos)) {
            return MapUtil.empty();
        }
        List<TenantAllVo> allVos = hiveVasClient.getTenantInfo(CharSequenceUtil.join(CommonSeparators.COMMA_SEPARATOR, shopNos), 1, 1);
        if (CollectionUtils.isEmpty(allVos)) {
            return MapUtil.empty();
        }
        return allVos.stream().map(TenantAllVo::getTenant).filter(it -> StringUtils.isNotEmpty(it.getShopName()))
                .collect(Collectors.toMap(TenantInfoVo::getContractNo, Function.identity(), (k1, k2) -> k2));
    }

    @Async
    public CompletableFuture<Map<String, TenantInfoVo>> getTenantFuture(List<String> shopNos) {
        if (CollectionUtils.isEmpty(shopNos)) {
            return CompletableFuture.completedFuture(MapUtil.empty());
        }
        List<TenantAllVo> allVos = hiveVasClient.getTenantInfo(CharSequenceUtil.join(CommonSeparators.COMMA_SEPARATOR, shopNos), 1, 1);
        if (CollectionUtils.isEmpty(allVos)) {
            return CompletableFuture.completedFuture(MapUtil.empty());
        }
        return CompletableFuture.completedFuture(allVos.stream().map(TenantAllVo::getTenant).filter(it -> StringUtils.isNotEmpty(it.getRetailBrandName()))
                .collect(Collectors.toMap(TenantInfoVo::getContractNo, Function.identity(), (k1, k2) -> k2)));
    }

    @Async
    public CompletableFuture<Map<String, TenantInfoVo>> getTenantFutureFromHiveService(List<String> shopNos) {
        if (CollectionUtils.isEmpty(shopNos)) {
            return CompletableFuture.completedFuture(MapUtil.empty());
        }
        List<TenantInfoVo> tenantInfoVos = hiveServiceClient.findByShopNos(CharSequenceUtil.join(CommonSeparators.COMMA_SEPARATOR, shopNos));
        if (CollectionUtils.isEmpty(tenantInfoVos)) {
            return CompletableFuture.completedFuture(MapUtil.empty());
        }
        return CompletableFuture.completedFuture(tenantInfoVos.stream().collect(Collectors.toMap(TenantInfoVo::getContractNo, Function.identity(), (k1, k2) -> k2)));
    }

    public List<HiveLbsInfoDto> getLbsList(String brandIds, String format) {
        return hiveVasClient.getLbsList(CharSequenceUtil.split(brandIds, ","), format);
    }

    @Async
    public CompletableFuture<Map<String, String>> getMallName(List<String> mallIds) {
        if (CollectionUtils.isEmpty(mallIds)) {
            return CompletableFuture.completedFuture(MapUtil.empty());
        }
        List<HiveLbsInfoDto> lbsList = hiveVasClient.getLbsInfo(mallIds);
        if (CollectionUtils.isEmpty(lbsList)) {
            return CompletableFuture.completedFuture(MapUtil.empty());
        }
        return CompletableFuture.completedFuture(lbsList.stream().map(HiveLbsInfoDto::getLbs).filter(it -> StringUtils.isNotEmpty(it.getName()))
                .collect(Collectors.toMap(LbsItemDto::getId, LbsItemDto::getName, (k1, k2) -> k2)));
    }

    @Cacheable(value = RedisCacheKey.HIVE_LBS_CACHE_KEY, key = "#mallId", unless = "#result == null")
    public HiveLbsInfoDto getLbsName(String mallId) {
        List<HiveLbsInfoDto> lbsList = hiveVasClient.getLbsInfo(Collections.singletonList(mallId));
        if (CollectionUtils.isEmpty(lbsList)) {
            return null;
        }
        return lbsList.get(0);
    }

    public ProjectDto getLbsProjectInfo(String lbsId) {
        String lbsProjectKey = String.format(RedisCacheKey.LBS_PROJECT_KEY, lbsId);
        String projectId = redisService.getValue(lbsProjectKey);
        if (StringUtils.isNotBlank(projectId)) {
            return ProjectDto.builder().id(projectId).build();
        }
        List<HiveLbsInfoDto> lbsList = hiveVasClient.getLbsInfo(Collections.singletonList(lbsId));
        if (CollectionUtils.isEmpty(lbsList) || Objects.isNull(lbsList.get(0).getProject())) {
            return null;
        }
        ProjectDto project = lbsList.get(0).getProject();
        // 异步缓存lbs对应的楼盘id信息
        redisService.setVal(lbsProjectKey, project.getId(), 1L, TimeUnit.DAYS);
        return project;
    }

    /**
     * 查询所属商场
     * @param lbsId
     * @return
     */
    public MallItem getMallByLbsId(String lbsId) {
        if (StringUtils.isBlank(lbsId)) {
            return null;
        }
        MallItem item = mallConfig.getByMallId(lbsId);
        if (Objects.nonNull(item)) {
            return item;
        }
        log.info("非商场Lbs查询所属楼盘信息: {}", lbsId);
        ProjectDto project = this.getLbsProjectInfo(lbsId);
        if (Objects.isNull(project)) {
            return null;
        }
        return mallConfig.getByProjectId(project.getId());
    }

}
