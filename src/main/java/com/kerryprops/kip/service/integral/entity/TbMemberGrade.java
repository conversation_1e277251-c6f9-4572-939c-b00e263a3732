package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 会员等级表
 * <AUTHOR>
 * @date 2022-09-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_grade")
@ToString
public class TbMemberGrade implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 等级[编码]
     */
    @TableField("code")
    private String code;

    /**
     * 名称（金卡、普卡）
     */
    @TableField("name")
    private String name;

    /**
     * 英文名称（金卡、普卡）
     */
    @TableField("name_en")
    private String nameEn;

    /**
     * 等级顺序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 描述备注
     */
    @TableField("desc_remark")
    private String descRemark;

    /**
     * 英文描述备注
     */
    @TableField("desc_remark_en")
    private String descRemarkEn;

    /**
     * 会员卡封面图片地址
     */
    @TableField("card_cover_url")
    private String cardCoverUrl;

    /**
     * 英文会员卡封面图片地址
     */
    @TableField("card_cover_url_en")
    private String cardCoverUrlEn;

    /**
     * 会员卡封面图片地址（首页）
     */
    @TableField("card_cover_home_url")
    private String cardCoverHomeUrl;

    /**
     * 英文会员卡封面图片地址（首页）
     */
    @TableField("card_cover_home_url_en")
    private String cardCoverHomeUrlEn;

    /**
     * 会员等级说明
     */
    @TableField("grade_desc")
    private String gradeDesc;

    /**
     * 会员等级英文说明
     */
    @TableField("grade_desc_en")
    private String gradeDescEn;

    /**
     * 是否参与升级，保级降级  1 参与   0 不参与
     */
    @TableField("up_gradation_status")
    private Integer upGradationStatus;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    private String createUser;

    private String updateUser;

}