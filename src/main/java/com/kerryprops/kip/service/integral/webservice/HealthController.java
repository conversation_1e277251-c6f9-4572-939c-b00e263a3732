package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.service.integral.webservice.resource.HealthResource;
import io.swagger.v3.oas.annotations.Hidden;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;


/***********************************************************************************************************************
 * Project - gateway-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/29/2021 09:03
 **********************************************************************************************************************/

@Hidden
@RestController
public class HealthController {

    @GetMapping(value = "/health/shallow", produces = "application/json")
    public HealthResource health() {
        return HealthResource.SUCCESS_RESPONSE;
    }

}
