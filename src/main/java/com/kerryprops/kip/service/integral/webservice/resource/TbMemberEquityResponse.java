package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member_equity: 会员权益
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员权益响应类")
public class TbMemberEquityResponse implements Serializable {

    @Schema( description = "会员权限ID")
    private String id;

    @Schema( description = "商场编号")
    private String mallId;

    @Schema( description = "会员权益内容（代码）")
    private String content;

    @Schema( description = "集团ID")
    private String groupId;

    @Schema( description = "创建时间")
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @Schema( description = "权益类型: 1:会员权益2:优惠信息3:服务协议4:自助积分指南5:微信无感积分6:支付宝无感积分7:客服台积分8:拍照积分")
    private Integer type;

    @Schema( description = "权益类型名称")
    private String typeName;

    @Schema( description = "标题")
    private String title;

    @Schema( description = "协议名称")
    private String protocolName;

    @Schema( description = "自助积分指南(1:扫码积分; 2:微信无感积分; 3:支付宝无感积分; 4:客服台积分)")
    private Integer code;

}