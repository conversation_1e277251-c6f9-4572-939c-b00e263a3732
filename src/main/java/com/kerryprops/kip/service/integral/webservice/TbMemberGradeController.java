package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.service.CaffeineCacheService;
import com.kerryprops.kip.service.integral.service.MemberRegisterService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberGradeResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/28/2022 15:29
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/member/grades")
@RestController
@AllArgsConstructor
@Tag(name = "集团会员等级列表API")
public class TbMemberGradeController {

    private final TbMemberGradeService tbMemberGradeService;
    private final MemberRegisterService memberRegisterService;
    private final CaffeineCacheService caffeineCacheService;

    @GetMapping("/list")
    @Operation(summary="CRM获取品牌下设置的会员等级列表", method = "GET")
    public List<MemberGradeResponse> getMemberGradeList(@CurrentUser LoginUser loginUser, @RequestHeader("brandId") String brandId) {
        List<TbMemberGrade> grades = caffeineCacheService.getGradeList(brandId);
        if (Objects.isNull(loginUser)) {
            return grades.stream().filter(item -> (1 == item.getUpGradationStatus())).map(this::getGradeResponse).toList();
        }
        // 判断入参
        if (StringUtils.isAnyBlank(loginUser.getBrandId(), loginUser.getCId())) {
            return grades.stream().filter(item -> (1 == item.getUpGradationStatus())).map(this::getGradeResponse).toList();
        }
        TbMemberAsset memberAsset = memberRegisterService.findByGroupIdAndKipUserId(brandId, loginUser.getCId());
        if (Objects.isNull(memberAsset)) {
            throw BizException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        TbMemberGrade grade = grades.stream().filter(item -> StringUtils.equals(item.getCode(), memberAsset.getGrade())).findFirst().orElse(null);
        // 说明该会员等级是尊享卡，不参与会员升级
        if (Objects.isNull(grade) || 1 == grade.getUpGradationStatus()) {
            return grades.stream().filter(item -> (1 == item.getUpGradationStatus())).map(this::getGradeResponse).toList();
        }
        return grades.stream().map(this::getGradeResponse).toList();
    }

    @GetMapping("/member-current-grade")
    @Operation(summary="获取用户对应卡等信息-C端使用", method = "GET")
    public MemberGradeResponse getCurrentGrade(@CurrentUser LoginUser loginUser) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        if (Objects.isNull(tbMemberAsset)) {
            return null;
        }
        // 查询会员当前等级信息
        List<TbMemberGrade> grades = tbMemberGradeService.queryGradeSortAscByGroupId(loginUser.getBrandId());
        return this.getGradeResponse(grades.stream()
                .filter(item -> StringUtils.equals(item.getCode(), tbMemberAsset.getGrade()))
                .findFirst().orElse(null));
    }

    @GetMapping("/{groupId}")
    @Operation(summary="通过groupId获取设置的会员等级信息", method = "GET")
    public List<MemberGradeResponse> getGradeListByGroupId(@PathVariable("groupId") String groupId) {
        List<TbMemberGrade> grades = caffeineCacheService.getGradeList(groupId);
        if (CollectionUtils.isEmpty(grades)) {
            return Collections.emptyList();
        }
        return grades.stream().map(this::getGradeResponse).toList();
    }

    private MemberGradeResponse getGradeResponse(TbMemberGrade item) {
        if (Objects.isNull(item)) {
            return null;
        }
        return MemberGradeResponse.builder()
                .id(item.getId())
                .membergrade(item.getCode())
                .desc(item.getName())
                .remark(item.getDescRemark())
                .sort(item.getSort())
                .groupId(item.getGroupId())
                .cardCoverUrl(item.getCardCoverUrl())
                .cardCoverHomeUrl(item.getCardCoverHomeUrl())
                .upGradationStatus(item.getUpGradationStatus())
                .description(item.getGradeDesc())
                .build();
    }

}
