package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.pmw.client.resource.PointsPaymentConfirmResource;
import com.kerryprops.kip.pmw.client.resource.PointsRefundConfirmResource;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsPushRecord;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;

import java.util.List;

/**
 * @description 微信、支付宝推送记录表
 * <AUTHOR>
 * @date 2022-09-14
 */
public interface TbInsensatePointsPushRecordService extends IService<TbInsensatePointsPushRecord> {

    void pushTransaction(InsensatePointsPushRecordDto record);

    /**
     * 保存推送记录
     * @param dto
     */
    void saveWeChatPushPaymentRecord(PointsPaymentConfirmResource dto);

    /**
     * 保存微信推送的退款记录
     * @param resource
     */
    void saveWeChatPushRefundRecord(PointsRefundConfirmResource resource);

    InsensatePointsPushRecordDto findByTransactionIdAndEventTypeAndOrigin(String transactionId, Integer eventType, int origin);

    List<InsensatePointsPushRecordDto> selectByOpenId(String openId);

    TbInsensatePointsPushRecord findByTransactionId(String transactionId);

}