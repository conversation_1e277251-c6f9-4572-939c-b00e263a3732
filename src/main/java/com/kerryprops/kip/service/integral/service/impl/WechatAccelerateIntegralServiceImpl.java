package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.kerryprops.kip.pmw.client.resource.*;
import com.kerryprops.kip.pmw.client.service.BizCircleClientService;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.AutoPointsEnum;
import com.kerryprops.kip.service.integral.common.enums.EventTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.event.UserPointAuthorizationEvent;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.QueryPointsCommitStatusResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/***********************************************************************************************************************
 * Project - integral-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/16/2022 13:32
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class WechatAccelerateIntegralServiceImpl implements WechatAccelerateIntegralService {

    private final TbMemberAssetService tbMemberAssetService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final TbBaseShopService tbBaseShopService;
    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private final TbPointsDetailService tbPointsDetailService;
    private final MemberSalePointsProcessService memberSalePointsProcessService;
    private final BizCircleClientService bizCircleClientService;
    private final HeaderResource headerResource;
    private final TbInsensatePointsPushRecordService tbInsensatePointsPushRecordService;
    private final RedisService redisService;
    private final MallConfig mallConfig;
    private final TbSalesDetailService tbSalesDetailService;
    private final TbMemberCardOpenRecordService tbMemberCardOpenRecordService;
    private final TbAutoPointsConfigService tbAutoPointsConfigService;

    @Override
    public void payCallback(PointsPaymentConfirmResource dto) {
        if (Objects.isNull(dto.getBody())) {
            log.info("payCallback - empty body");
            return;
        }
        // 支付成功
        if (Objects.equals(dto.getBody().getEventType(), EventTypeEnum.TRANS_SUCC.getName())) {
            PointsPaymentConfirmResource.PointsPaymentDetailResource resource = dto.getBody().getResource();
            // 推送记录参数判断
            if (StringUtils.isAnyBlank(resource.getOpenId(), resource.getMchId(), resource.getShopNumber())) {
                log.info("payCallback - 微信支付推送记录参数缺失，推送单号: {}", resource.getTransactionId());
                return;
            }
            MallItem mallItem = mallConfig.getByMchId(resource.getMchId());
            if (Objects.isNull(mallItem)) {
                log.error("payCallback - 商圈id: {}对应的商场未开通微信无感积分权限", resource.getMchId());
                return;
            }
            // 查询用户信息
            TbMemberAsset member = tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(resource.getOpenId(), mallItem.getGroupId(), mallItem.getMallId());
            if (Objects.isNull(member)) {
                log.error("payCallback - OpenId: {}对应的用户信息不存在", resource.getOpenId());
                return;
            }
            // 发布用户授权微信无感积分事件
            applicationEventPublisher.publishEvent(UserPointAuthorizationEvent.builder().openId(resource.getOpenId())
                    .kipUserId(member.getKipUserId()).groupId(mallItem.getGroupId()).mallId(mallItem.getMallId())
                    .mchid(resource.getMchId()).origin(InsensateOriginEnum.WECHAT.getValue()).build());
            // 校验店铺信息是否有效
            TbBaseShop baseShop = tbBaseShopService.getByContractNoAndMallId(resource.getShopNumber(), mallItem.getMallId());
            if (Objects.isNull(baseShop)) {
                log.error("payCallback - 该商铺: {}已失效或不属于该商圈", resource.getShopNumber());
                return;
            }
            // 添加数据重复验证
            String saleNoKey = String.format(RedisCacheKey.SALES_NO_KEY, resource.getTransactionId());
            if (Boolean.TRUE.equals(redisService.setSaleNoIfAbsent(saleNoKey, IntegralConstant.KIP_POINTS))) {
                try {
                    // 通用销售记录去重及计算积分逻辑
                    SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId(mallItem.getGroupId()).wxOrAliId(resource.getOpenId())
                            .mallId(mallItem.getMallId()).member(member).amount(this.getAmount(resource.getAmount())).vipcode(member.getVipcode())
                            .remark(IntegralConstant.WECHAT_POINTS).remarkName("微信无感支付积分").shopId(resource.getShopNumber()).baseShop(baseShop)
                            .saleNo(resource.getTransactionId()).saleDate(StringUtils.isBlank(resource.getTimeEnd()) ? DateUtil.formatDateTime(new Date()) : resource.getTimeEnd())
                            .createUser("WX_POINTS").saleType(SaleTypeEnum.WECHAT.getValue()).refund(false).build();
                    memberSalePointsProcessService.salePointsProcess(pointsDto);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    redisService.delKeys(Collections.singletonList(saleNoKey));
                }
            } else {
                log.info("微信无感积分推送的单号: [{}]正在处理.", resource.getTransactionId());
            }
        } else {
            log.info("微信无感积分推送的EventType: {}不匹配", dto.getBody().getEventType());
        }
    }

    /**
     * 金额除100
     * @param amount
     * @return
     */
    private String getAmount(Integer amount) {
        return NumberUtil.div(amount.toString(), "100", 2, RoundingMode.HALF_UP).toString();
    }

    @Override
    public int refundCallback(PointsRefundConfirmResource dto) {
        if (Objects.isNull(dto.getBody())) {
            log.error("refundCallback - empty body");
            return 0;
        }
        PointsRefundConfirmResource.PointsRefundDetailResource resource = dto.getBody().getResource();
        // 退款成功
        if (!StringUtils.equals(dto.getBody().getEventType(), EventTypeEnum.REFUND_SUCC.getName())) {
            log.error("refundCallback - unknown refund event type: {}", dto.getBody().getEventType());
            return 0;
        }

        MallItem mallItem = mallConfig.getByMchId(resource.getMchId());
        if (Objects.isNull(mallItem)) {
            log.error("refundCallback - 商场 {} 未开通微信无感积分权限", resource.getMchId());
            return 0;
        }
        // 查询用户信息
        TbMemberAsset member = tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(resource.getOpenId(), mallItem.getGroupId(), mallItem.getMallId());
        if (Objects.isNull(member)) {
            log.error("refundCallback - fail to get member info. openId: {}, groupId: {}", resource.getOpenId(), mallItem.getGroupId());
            return 0;
        }
        // 通用销售记录去重及计算积分逻辑
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().groupId(mallItem.getGroupId()).vipcode(member.getVipcode())
                .mallId(mallItem.getMallId()).member(member).amount(this.getAmount(resource.getPayAmount())).refundAmount(this.getAmount(resource.getRefundAmount()))
                .remark(IntegralConstant.WECHAT_POINTS_REFUND).remarkName("微信无感积分退款").shopId(resource.getShopNumber())
                .saleNo(resource.getTransactionId()).saleDate(resource.getRefundTime()).createUser("WX_POINTS_REFUND").saleType(SaleTypeEnum.WECHAT.getValue()).refund(true).build();

        AtomicInteger points = new AtomicInteger();
        String refundKey = String.format(RedisCacheKey.REFUND_LOCK, resource.getTransactionId());
        if (!redisService.setSaleNoIfAbsent(refundKey, IntegralConstant.KIP_POINTS)) {
            log.error("duplicated refundCallback :{}", resource.getTransactionId());
            return 0;
        }
        try {
            points.set(memberSalePointsProcessService.refundSalePointsProcess(pointsDto));
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        } finally {
            redisService.delKeys(Collections.singletonList(refundKey));
        }
        return points.intValue();
    }

    @Override
    public void memberPointsAuth(PointsActivationConfirmResource resource) {
        PointsActivationConfirmResource.PointsActivationDetailResource authorizeResource = resource.getBody().getResource();
        if (Objects.isNull(resource.getBody()) || StringUtils.isAnyBlank(authorizeResource.getOpenId(), authorizeResource.getMchId())) {
            log.error("memberPointsAuth: empty param");
            return;
        }
        MallItem mallItem = mallConfig.getByMchId(authorizeResource.getMchId());
        if (Objects.isNull(mallItem)) {
            log.error("memberPointsAuth: 商场 {} 未开通微信无感积分权限", authorizeResource.getMchId());
            return;
        }
        TbMemberAsset member = tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(authorizeResource.getOpenId(), mallItem.getGroupId(), mallItem.getMallId());
        if (Objects.isNull(member)) {
            log.error("memberPointsAuth: 未找到用户 openId: {} , groupId: {}信息", authorizeResource.getOpenId(), mallItem.getGroupId());
            return;
        }
        // 新增微信无感积分授权记录
        TbInsensatePointsAuthRecord authRecord = TbInsensatePointsAuthRecord.builder().openId(authorizeResource.getOpenId()).kipUserId(member.getKipUserId()).cardNo(authorizeResource.getCode())
                .groupId(mallItem.getGroupId()).mallId(mallItem.getMallId()).status(0).origin(InsensateOriginEnum.WECHAT.getValue()).mchid(authorizeResource.getMchId()).build();
        tbInsensatePointsAuthRecordService.insertJudgment(authRecord);
        log.info("memberPointsAuth: openId: {}, kipUserId: {} auth success.", authorizeResource.getOpenId(), member.getKipUserId());
    }

    @Override
    public void memberCardOpenEvent(PointsActivationConfirmResource resource) {
        // 通过会员卡号查询授权信息
        TbInsensatePointsAuthRecord authRecord = tbInsensatePointsAuthRecordService.getByCardNoAndOrigin("", InsensateOriginEnum.WECHAT.getValue());
        if (Objects.isNull(authRecord)) {
            log.error("通过会员卡号: {}未查询到用户授权无感积分记录", "");
            return;
        }
        TbMemberCardOpenRecord openRecord = TbMemberCardOpenRecord.builder().cardNo(authRecord.getCardNo())
                .cardId("").openId(authRecord.getOpenId()).eventType("")
                .groupId(authRecord.getGroupId()).mallId(authRecord.getMallId()).createDate(new Date()).build();
        // 通过openId + groupId查询用户信息
        Optional.ofNullable(tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(authRecord.getOpenId(), authRecord.getGroupId(), authRecord.getMallId()))
                .ifPresent(it -> openRecord.setKipUserId(it.getKipUserId()));
        // 保存或更新
        tbMemberCardOpenRecordService.saveOrUpdateRecord(openRecord);
    }

    @Override
    @Async
    public void syncAliPay() {
        log.info("syncAliPay started");

        String lockKey = RedisCacheKey.ALIPAY_SYNC_TAG;
        if (!redisService.setSaleNoIfAbsent(lockKey, IntegralConstant.KIP_POINTS)) {
            log.error("duplicated syncAliPay：{}");
            return;
        }
        try {
            tbInsensatePointsAuthRecordService.insertBatchFromCardMemberRelation();
        } catch (Exception e) {
            log.error("syncAliPay failed：{}", e.getMessage());
        }
        log.info("syncAliPay ended");
    }

    @Override
    public void syncWechatPoints(SalesAutoPointsDto dto) {
        InsensatePointsPushRecordDto recordDto = tbInsensatePointsPushRecordService.findByTransactionIdAndEventTypeAndOrigin(dto.getSaleNo(), EventTypeEnum.TRANS_SUCC.getValue(), InsensateOriginEnum.WECHAT.getValue());
        if (Objects.isNull(recordDto)) {
            log.error("微信支付单号: {}未查询到推送记录", dto.getSaleNo());
            return;
        }
        log.info("***** 推送商圈会员积分同步至微信 *****");
        // 推送记录
        PointsPaymentConfirmResource.PointsPaymentDetailResource pushRecord = JsonUtils.stringToObj(recordDto.getTransactionInfo(), PointsPaymentConfirmResource.PointsPaymentDetailResource.class);
        if (Objects.isNull(pushRecord)) {
            return;
        }
        // 组装商圈会员积分同步
        EarnRewardPointsInputResource.EarnRewardPointsInputBodyResource body = new EarnRewardPointsInputResource.EarnRewardPointsInputBodyResource();
        body.setSubMchId(pushRecord.getMchId());
        body.setAppId(pushRecord.getAppId());
        body.setOpenId(pushRecord.getOpenId());
        body.setTransactionId(pushRecord.getTransactionId());
        if (dto.getExchangePoints() > 0) {
            body.setEarnPoints(Boolean.TRUE);
        } else {
            body.setEarnPoints(Boolean.FALSE);
            body.setNoPointsRemarks(dto.getSalesRemark());
        }
        Date date = new Date();
        body.setIncreasedPoints(dto.getExchangePoints());
        body.setPointsUpdateTime(DateUtil.format(date, DatePattern.UTC_WITH_XXX_OFFSET_PATTERN));
        body.setOrderSource(IntegralConstant.KIP_POINTS);
        // 当日获取微信无感积分总数
        TbSalesDetail detail = tbSalesDetailService.selectBySellNoAndMallId(pushRecord.getTransactionId(), dto.getMallId());
        if (Objects.nonNull(detail)) {
            int pointsOfDay = tbPointsDetailService.getTotalPointsOfDay(dto.getGroupId(), detail.getVipcode(),
                    Arrays.asList(IntegralConstant.WECHAT_POINTS, IntegralConstant.WECHAT_POINTS_REFUND), DateUtil.formatDate(date));
            body.setTotalPoints(pointsOfDay);
        }
        EarnRewardPointsOutputResource outputResource = null;
        try {
            EarnRewardPointsInputResource resource = new EarnRewardPointsInputResource(headerResource, body, null);
            outputResource = bizCircleClientService.earnRewardPoints(resource);
        } catch (Exception e) {
            e.printStackTrace();
            long count = redisService.setRetryCount(dto.getSaleNo());
            log.info("微信支付单号同步次数: [{}]", count);
            // 重试推送5次，如果都失败，则不在推送
            if (count > IntegralConstant.RETRY_TIMES) {
                log.info("同步微信商圈会员积分情况已达最大推送次数5次，推送失败: [{}]", dto.getSaleNo());
                redisService.delRetryCount(dto.getSaleNo());
            } else {
                throw e;
            }
        }
        log.info("同步微信支付记录获得积分返回信息: [{}-{}]", dto.getSaleNo(), outputResource);
        if (Objects.nonNull(outputResource) && Objects.nonNull(outputResource.getBody())) {
            EarnRewardPointsOutputResource.EarnRewardPointsResultBodyResource resultBody = (EarnRewardPointsOutputResource.EarnRewardPointsResultBodyResource) outputResource.getBody();
            if (resultBody.isResult()) {
                log.info("微信支付单号: {}推送商圈会员积分同步****成功****.", pushRecord.getTransactionId());
            } else {
                log.info("微信支付单号: {}推送商圈会员积分同步****失败****.", pushRecord.getTransactionId());
            }
        } else {
            log.info("推送商圈会员积分同步至微信返回信息为空，单号: [{}]", pushRecord.getTransactionId());
        }
    }

    @Override
    public QueryPointsCommitStatusResource queryMemberPointsCommitStatus(LoginUser loginUser) {
        MallItem mallItem = mallConfig.getByMallId(loginUser.getLbsId());
        QueryPointsCommitStatusResource commitStatusResource = QueryPointsCommitStatusResource.builder().pointsCommitStatus(IntegralConstant.WX_FINISHED).ocr(Boolean.FALSE).build();
        if (Objects.isNull(mallItem) || Objects.isNull(mallItem.getWxBrandId())) {
            return commitStatusResource;
        }
        // 判断是否开通OCR拍照积分
        commitStatusResource.setOcr(mallConfig.isKO(mallItem.getGroupId()));
        // 设置微信商圈id
        commitStatusResource.setMchId(mallItem.getWxMchId());
        // 未开通微信无感积分商场，直接返回已完成状态
        TbAutoPointsConfig config = tbAutoPointsConfigService.findByLbsId(loginUser.getLbsId());
        if (Objects.isNull(config) || !AutoPointsEnum.containWxPay(config.getCode())) {
            return commitStatusResource;
        }
        this.checkMemberOpenId(loginUser, mallItem.getMallId(), commitStatusResource);
        // 判断用户微信支付即积分授权是否开通
        if (Boolean.FALSE.equals(commitStatusResource.getWxAuth())) {
            return commitStatusResource;
        }
        QueryPointsCommitStatusInputResource.QueryPointsCommitStatusInputBodyResource body = new QueryPointsCommitStatusInputResource.QueryPointsCommitStatusInputBodyResource();
        body.setSubMchId(mallItem.getWxMchId());
        body.setAppId(mallItem.getAppId());
        body.setBrandId(mallItem.getWxBrandId().toString());
        body.setOrderSource(IntegralConstant.KIP_POINTS);
        body.setOpenId(commitStatusResource.getOpenId());
        QueryPointsCommitStatusInputResource resource = new QueryPointsCommitStatusInputResource(headerResource, body, null);
        QueryPointsCommitStatusOutputResource outputResource = null;
        try {
            // 调用微信api查询会员待积分状态
            outputResource = bizCircleClientService.queryPointsCommitStatus(resource);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("会员: [{}]商圈待积分状态查询异常: {}", body.getOpenId(), e);
        }
        if (Objects.isNull(outputResource) || Objects.isNull(outputResource.getBody())) {
            return commitStatusResource;
        }
        commitStatusResource.setPointsCommitStatus(((QueryPointsCommitStatusOutputResource.QueryPointsCommitStatusBodyResource) outputResource.getBody()).getPointsCommitStatus());
        return commitStatusResource;
    }

    /**
     * 填充用户对应的openId及微信授权状态
     * @param loginUser
     * @param mallId
     * @return
     */
    private void checkMemberOpenId(LoginUser loginUser, String mallId, QueryPointsCommitStatusResource commitStatusResource) {
        TbInsensatePointsAuthRecord authRecord = tbInsensatePointsAuthRecordService.getByKipUserIdAndMallIdAndOrigin(loginUser.getCId(), mallId, InsensateOriginEnum.WECHAT.getValue());
        if (Objects.nonNull(authRecord)) {
            // 是杭州，则通过授权开通日期判断（HKC迁移时间：2025-04-01 00:00:00）
            if (mallConfig.isHkc(loginUser.getBrandId())) {
                commitStatusResource.setWxAuth(authRecord.getCreateDate().after(IntegralConstant.hkcMigrationToKoDate));
                commitStatusResource.setOpenId(Boolean.TRUE.equals(commitStatusResource.getWxAuth()) ? authRecord.getOpenId() : loginUser.getOpenId());
            } else {
                commitStatusResource.setWxAuth(Boolean.TRUE);
                commitStatusResource.setOpenId(authRecord.getOpenId());
            }
        } else {
            commitStatusResource.setWxAuth(Boolean.FALSE);
            commitStatusResource.setOpenId(loginUser.getOpenId());
        }
    }

}
