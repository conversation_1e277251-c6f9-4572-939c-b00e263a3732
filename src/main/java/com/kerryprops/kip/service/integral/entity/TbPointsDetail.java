package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - loyalty-engine-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 10/18/2023 09:14
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_points_detail")
public class TbPointsDetail implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * crm迁移数据时的主键id
     */
    @TableField("crm_id")
    private String crmId;

    /**
     * 集团ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场编号
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 会员编号
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * 调整类型 1->普通调整， 2->销售调整
     */
    @TableField("type")
    private String type;

    /**
     * 积分变更后会员积分数
     */
    @TableField("current_points")
    private Integer currentPoints;

    /**
     * 调整积分数量
     */
    @TableField("amount")
    private Integer amount;

    /**
     * 剩余积分数
     */
    @TableField("left_points")
    private Integer leftPoints;

    /**
     * 积分调整原因
     */
    @TableField("reason_type")
    private String reasonType;

    /**
     * 冗余调整原因
     */
    @TableField("reason_desc")
    private String reasonDesc;

    /**
     * 销售单号(销售调整)
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 收银机号(销售调整)
     */
    @TableField("pos_no")
    private String posNo;

    /**
     * 商场编号
     */
    @TableField("shop_no")
    private String shopNo;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 过期时间，默认 2099-12-31 23:59:59
     */
    @TableField("expire_date")
    private String expireDate;

    /**
     * 扩展字段1, 是否办公楼会员
     */
    @TableField("extend1")
    private String extend1;

    /**
     * 扩展字段2， 是否公寓会员
     */
    @TableField("extend2")
    private String extend2;

    /**
     * 扩展字段3， 是否小区会员
     */
    @TableField("extend3")
    private String extend3;

    /**
     * 拍照积分上传图片地址
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 调整渠道（0:crm admin端积分 1:服务台积分, 2:用户自动积分 默认2）
     */
    @TableField("channel")
    private String channel;

    /**
     * 变更前积分数
     */
    @TableField("org_points")
    private Integer orgPoints;

    /**
     * 变更前会员等级
     */
    @TableField("org_grade")
    private String orgGrade;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 修改人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 调整记录月份
     */
    private transient String moon;

    /**
     * 设置默认值
     */
    public void setDefaultVal() {
        if (StringUtils.isBlank(this.extend1)) {
            this.extend1 = "";
        }
        if (StringUtils.isBlank(this.extend2)) {
            this.extend2 = "";
        }
        if (StringUtils.isBlank(this.extend3)) {
            this.extend3 = "";
        }
        if (StringUtils.isBlank(this.imageUrl)) {
            this.imageUrl = "";
        }
        // 收银机号
        if (StringUtils.isBlank(this.posNo)) {
            this.posNo = "";
        }
        // 积分过期时间，设置的都是默认值
        if (StringUtils.isBlank(this.expireDate)) {
            this.expireDate = "2099-12-31 23:59:59";
        }
        if (StringUtils.isBlank(this.reasonDesc)) {
            this.reasonDesc = "";
        }
    }

}