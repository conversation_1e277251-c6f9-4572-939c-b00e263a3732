package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/14/2022 09:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_sys_dict")
public class SysDict implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("pid")
    private Long pid;

    @TableField("dict_type")
    private String dictType;

    @TableField("dict_name")
    private String dictName;

    @TableField("dict_value")
    private String dictValue;

    @TableField("remark")
    private String remark;

    @TableField("sort")
    private Integer sort;

    @TableField("create_date")
    private Date createDate;

    @TableField("create_user")
    private String createUser;

    @TableField("update_date")
    private Date updateDate;

    @TableField("update_user")
    private String updateUser;

}
