package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * tb_member_refine_detail
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_refine_detail")
public class TbMemberRefineField implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置id
     */
    @TableField(value = "config_id")
    private Long configId;

    /**
     * 字段名称
     */
    @TableField(value = "field_name")
    private String fieldName;

    /**
     * 字段英文名称
     */
    @TableField(value = "field_ename")
    private String fieldEname;

    /**
     * 是否属于完善内容(0:否;1:是)
     */
    @TableField(value = "is_refine")
    private Integer isRefine;

    /**
     * 是否必填项(0:否;1:是)
     */
    @TableField(value = "is_required")
    private Integer isRequired;

    /**
     * C端展示顺序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 字段类型(0:个人信息字段;1:标签)
     */
    @TableField(value = "field_type")
    private Integer fieldType;

    /**
     * 组件类型
     */
    @TableField(value = "components_type")
    private String componentsType;

}