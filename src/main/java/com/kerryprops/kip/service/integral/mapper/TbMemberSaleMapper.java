package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberSale;

public interface TbMemberSaleMapper extends BaseMapper<TbMemberSale> {

//    Integer checkMemberSaleExists(@Param("saleDate") String saleDate,
//                                       @Param("shopId") String shopId,
//                                       @Param("saleNo") String saleNo,
//                                       @Param("mallId") String mallId);
//
//    /**
//     * 根据销售单号去重
//     * @param sellNo
//     * @return
//     */
//    TbMemberSale querySaleBySellNo(@Param("sellNo") String sellNo);
//
//    TbMemberSale queryBySellNoAndSaleType(@Param("sellNo") String sellNo, @Param("saleType") String saleType);
//
//    TbMemberSale checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto dto);
//
//    int queryMemberMonthSalesNumber(MemberSaleMonthNumberQueryDto query);
//
//    TbMemberSale selectBySellNoAndMallId(@Param("sellNo") String sellNo, @Param("mallId") String mallId);
//
//    /**
//     * 查询会员时间段内的销售记录
//     * @param groupId
//     * @param vipcode
//     * @param beginDate
//     * @param endDate
//     * @param shopIds 店铺号列表
//     * @return
//     */
//    List<TbMemberSale> getSaleListCreateAsc(@Param("groupId") String groupId, @Param("vipcode") String vipcode, @Param("beginDate") Date beginDate, @Param("endDate") Date endDate, @Param("shopIds") List<String> shopIds);
//
//    /**
//     * 根据条件查询会员的销售金额
//     * @param queryDto
//     * @return
//     */
//    TbMemberSale getMemberSaleAmountBetweenTime(MemberSaleAmountQueryDto queryDto);
//
//    /**
//     * 查询保级金额是否匹配
//     * @param queryDto
//     * @return
//     */
//    int checkMemberSaleAmountWhetherRelegationSucceeded(MemberGradeWhetherRelegationQueryDto queryDto);
//
//    /**
//     * 处理11/2, 11/3号两天由于bug导致会员未升级的数据
//     * @param mallId
//     * @param beginDate
//     * @param endDate
//     * @return
//     */
//    List<TbMemberSale> initMemberGradeData(@Param("mallId") String mallId, @Param("vipcode") String vipcode,
//                                           @Param("beginDate") String beginDate, @Param("endDate") String endDate);
//
//    List<String> getShopMemberSalesIds(@Param("mallId") String mallId, @Param("shopId") String shopId);
//
//    void updateMemberSalesShopId(@Param("salesIds") List<String> salesIds, @Param("shopId") String shopId);
//
//    List<TbMemberSale> findSalesInfo(@Param("groupId") String groupId, @Param("mallId") String mallId,
//                                     @Param("vipcode") String vipcode, @Param("sellNos") List<String> sellNos);
//
//    DailyMemberStatisticsInfo getDailyMemberStatisticsInfo(@Param("groupId") String groupId, @Param("mallId") String mallId,
//                                                           @Param("startTime") String startTime, @Param("endTime") String endTime);
//
//    TbMemberSale getMemberSaleById(@Param("id") String id);
//
//    List<String> checkSalesUpgradeGrade(@Param("groupId") String groupId, @Param("startTime") String startTime, @Param("endTime") String endTime);
//
//    /**
//     * 查询时间段内会员的一条销售记录
//     * @param groupId
//     * @param vipcode
//     * @param startTime
//     * @param endTime
//     * @return
//     */
//    String getOneSalesBetweenTime(@Param("groupId") String groupId, @Param("vipcode") String vipcode, @Param("startTime") String startTime, @Param("endTime") String endTime);

}