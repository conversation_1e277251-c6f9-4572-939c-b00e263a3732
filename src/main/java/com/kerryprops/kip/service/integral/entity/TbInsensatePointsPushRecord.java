package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 微信、支付宝、沈阳POS推送记录表
 * <AUTHOR>
 * @date 2022-09-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TbInsensatePointsPushRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 符合规范的唯一主键id
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 微信openid或支付宝user_id。根据origin判断
    */
    private String openId;

    /**
    * 支付订单号
    */
    private String transactionId;

    /**
    * 事件类型。1支付，2退款。对应integralconstant.wx_pay_success等枚举
    */
    private int eventType;

    /**
    * 支付具体信息
    */
    private String transactionInfo;

    /**
    * 来源，0微信，1支付宝，2沈阳海鼎POS
    */
    private int origin;

    /**
    * createDate
    */
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
    * update_date
    */
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

}