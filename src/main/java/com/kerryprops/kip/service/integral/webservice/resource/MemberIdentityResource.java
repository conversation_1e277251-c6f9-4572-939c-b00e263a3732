package com.kerryprops.kip.service.integral.webservice.resource;

import lombok.*;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - profile-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/28/2022 15:18
 **********************************************************************************************************************/

@Get<PERSON>
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberIdentityResource implements Serializable {

    private String userId;

    private String projectId;

    /**
     * 认证类型，即业态
     */
    private String auditType;

    /**
     * 身份认证所属lbsId
     */
    private String lbsId;

    /**
     * 身份认证状态
     */
    private Integer status;

}
