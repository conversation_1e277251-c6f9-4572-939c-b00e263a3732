package com.kerryprops.kip.service.integral.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/02/2022 11:19
 **********************************************************************************************************************/

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TenantRespVo implements Serializable {

    @Schema(name = "租户id")
    private String id;

    @Schema(name = "租户合同主体", example = "租户A")
    private String name;

    @Schema(name = "租户名称")
    private String brandName;

    @Schema(name = "租户状态: false-禁用，true-启用(DISABLE:禁用；ENABLE:启用；CANCELED:注销；)")
    private String status;

    @Schema(name = "楼层列表")
    private List<String> floorIdSet;

    @Schema(name = "楼栋列表")
    private List<String> buildingIdSet;

    @Schema(name = "公司合同")
    private List<String> doCoSet;

    @Schema(name = "CRM商铺编号")
    private String contractNo;

    @Schema(name = "CRM商铺别名")
    private String shopName;

    @Schema(name = "CRM商铺品牌Id")
    private String retailBrandId;

    @Schema(name = "CRM商铺品牌名")
    private String retailBrandName;

    @Schema(name = "CRM商铺一级业态编码")
    private String firstFormatCode;

    @Schema(name = "CRM商铺一级业态名称")
    private String firstFormatName;

    @Schema(name = "CRM商铺二级业态编码")
    private String secondFormatCode;

    @Schema(name = "CRM商铺二级业态名称")
    private String secondFormatName;

    @Schema(name = "CRM商铺三级业态编码")
    private String thirdFormatCode;

    @Schema(name = "CRM商铺三级业态名称")
    private String thirdFormatName;

    @Schema(name = "CRM商铺密码")
    private String password;

    @Schema(name = "CRM商铺类型")
    private String shopType;

    @Schema(name = "CRM商铺有效期开始时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime beginDate;

    @Schema(name = "CRM商铺有效期结束时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endDate;

}
