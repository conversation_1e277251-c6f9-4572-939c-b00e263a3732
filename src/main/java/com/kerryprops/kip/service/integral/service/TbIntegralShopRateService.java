package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralShopRateResource;

import java.util.List;

public interface TbIntegralShopRateService {
    List<TbIntegralShopRate> shopRatePage(String groupId, List<String> mallIds, String shopId, int page, int size);

    int getTotal(String groupId, List<String> mallIds, String shopId);

    List<TbIntegralShopRate> getShopRateList(TbIntegralShopRate rate);

    void deleteShopRateList(Long[] ids);

    void saveOrUpdateShopRate(TbIntegralShopRateResource resource);
}
