package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.MessageTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.mapper.TbPointsDetailMapper;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.service.SysDictService;
import com.kerryprops.kip.service.integral.service.TbPointsDetailService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberMonthlyIntegralResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> zhangxiliang
 * Created Date - 10/23/2023 17:32
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbPointsDetailServiceImpl extends ServiceImpl<TbPointsDetailMapper, TbPointsDetail> implements TbPointsDetailService {

    private final TbPointsDetailMapper tbPointsDetailMapper;
    private final SysDictService sysDictService;
    private final RabbitMqService rabbitMqService;
    private final MallConfig mallConfig;

    @Override
    public int getTotalPointsOfDay(String groupId, String vipcode, List<String> remarks, String date) {
        return tbPointsDetailMapper.getTotalPointsOfDay(groupId, vipcode, remarks, date);
    }

    @Override
    public String savePointsChangeRecord(SalesAutoPointsDto dto) {
        TbPointsDetail detail = new TbPointsDetail();
        detail.setCrmId(StringUtils.isNotBlank(dto.getIntegralAdjustId()) ? dto.getIntegralAdjustId() : IdUtil.simpleUUID());
        detail.setGroupId(dto.getGroupId());
        TbMemberAsset member = dto.getMember();
        detail.setVipcode(member.getVipcode());
        detail.setMobile(member.getMobile());
        detail.setMallId(StringUtils.isNotBlank(dto.getMallId()) ? dto.getMallId() : member.getMallId());
        // 判断是否是销售调整
        SaleTypeEnum saleTypeEnum = SaleTypeEnum.getSaleTypeByDictType(dto.getRemark());
        detail.setType(Objects.nonNull(saleTypeEnum) ? "S" : "A");
        detail.setReasonType(dto.getRemark());
        detail.setReasonDesc(StringUtils.isNotBlank(dto.getRemarkName()) ? dto.getRemarkName() : "");
        // 该笔销售剩余可使用积分数
        detail.setLeftPoints(Math.max(dto.getExchangePoints(), 0));
        detail.setOrgGrade(member.getGrade());
        detail.setOrgPoints(member.getCurrentPoints());
        detail.setOrderNo(StringUtils.isNotBlank(dto.getSaleNo()) ? dto.getSaleNo() : "");
        detail.setShopNo(StringUtils.isNotBlank(dto.getShopId()) ? dto.getShopId() : "");
        detail.setAmount(dto.getExchangePoints());
        detail.setCreateUser(StringUtils.isNotBlank(dto.getCreateUser()) ? dto.getCreateUser() : "");
        Date date = new Date();
        detail.setCreateDate(date);
        detail.setUpdateDate(date);
        detail.setUpdateUser(StringUtils.isNotBlank(dto.getCreateUser()) ? dto.getCreateUser() : "");
        detail.setChannel("0");
        detail.setImageUrl(StringUtils.isNotBlank(dto.getImageUrl()) ? dto.getImageUrl() : "");
        // 设置默认值
        detail.setDefaultVal();
        // 记录当前积分
        detail.setCurrentPoints(member.getCurrentPoints() + dto.getExchangePoints());
        // 记录销售金额
        detail.setRemark(StringUtils.isNotBlank(dto.getSalesRemark()) ? dto.getSalesRemark() : detail.getReasonDesc());
        // 补齐收银机号
        detail.setPosNo(StringUtils.isNotBlank(dto.getPosNo()) ? dto.getPosNo() : "");
        tbPointsDetailMapper.insert(detail);
        if (dto.getExchangePoints() == 0) {
            log.info("获取积分为零提前返回.");
            return detail.getId()+"";
        }
        // 非积分清零类型-发送模版消息及短信通知
        if (!IntegralConstant.INTEGRAL_CLEAR_REMARK.equals(dto.getRemark())) {
            this.sendMessage(dto.getRemark(), dto.getExchangePoints(), dto.getMallId(), member);
        }
        // 积分清零重新计算会员积分
        this.sendPointsConsumeMq(member, detail.getId());
        return detail.getId()+"";
    }

    @Override
    public String savePointsAdjustRecord(MemberPointsChangeDto dto, TbMemberAsset member) {
        Long adjustId = this.saveRecord(dto, member);
        // 非积分清零类型-发送积分变更模版消息
        if (!IntegralConstant.INTEGRAL_CLEAR_REMARK.equals(dto.getDictValue())) {
            this.sendMessage(dto.getDictValue(), dto.getChangePointsNum(), dto.getMallId(), member);
        }
        // 会员消耗积分MQ
        this.sendPointsConsumeMq(member, adjustId);
        return adjustId + "";
    }

    /**
     * 发送积分变更消息
     * @param dictValue
     * @param changePoints
     * @param mallId
     * @param tbMemberAsset
     */
    private void sendMessage(String dictValue, Integer changePoints, String mallId, TbMemberAsset tbMemberAsset) {
        SendMessageDto messageDto = SendMessageDto.builder().memberId(String.valueOf(tbMemberAsset.getId())).dictType(dictValue)
                .number(changePoints + "").mallId(mallId).messageType(MessageTypeEnum.POINTS_CHANGE.getType()).templateType(1).templateActivityid(IdUtil.simpleUUID())
                .updateDate(DateUtil.formatDateTime(new Date())).build();
        rabbitMqService.sendLazyMessage(RabbitMqConstant.XCRM_MEMBER_INTEGRAL_GRADE_CHANGE, JsonUtils.objToString(messageDto), 2);
    }

    /**
     * 会员消耗积分发送MQ通知
     * @param asset
     * @param detailId
     */
    private void sendPointsConsumeMq(TbMemberAsset asset, Long detailId) {
        // 积分>0，则不是消耗积分，直接返回
        if (asset.getAdjustIntegralNum() >= 0) {
            log.info("会员获得积分，无需发送积分消耗MQ.");
            return;
        }
        MemberConsumePointsDto consumeDto = MemberConsumePointsDto.builder().detailId(detailId + "").memberId(asset.getId()+"")
                .groupId(asset.getGroupId()).build();
        rabbitMqService.sendLazyMessage(RabbitMqConstant.CRM_MEMBER_CONSUME_POINTS_QUEUE, JsonUtils.objToString(consumeDto), 1);
    }

    @Override
    public void savePointsAdjustRecordNoMessage(MemberPointsChangeDto dto, TbMemberAsset member) {
        this.saveRecord(dto, member);
    }

    /**
     * 保存积分变更记录
     * @param dto
     * @param member
     * @return
     */
    private Long saveRecord(MemberPointsChangeDto dto, TbMemberAsset member) {
        TbPointsDetail detail = new TbPointsDetail();
        detail.setCrmId(StringUtils.isNotBlank(dto.getAdjustPointsId()) ? dto.getAdjustPointsId() : IdUtil.simpleUUID());
        detail.setGroupId(dto.getGroupId());
        detail.setMallId(StringUtils.isBlank(dto.getMallId()) ? member.getMallId() : dto.getMallId());
        detail.setVipcode(member.getVipcode());
        detail.setMobile(member.getMobile());
        // 销售调整
        detail.setType(Objects.nonNull(SaleTypeEnum.getSaleTypeByDictType(dto.getDictValue())) ? "S" : "A");
        detail.setReasonType(dto.getDictValue());
        if (StringUtils.isNotBlank(dto.getDictName())) {
            detail.setReasonDesc(dto.getDictName());
        } else {
            Optional.ofNullable(sysDictService.findByDictType(dto.getDictValue())).ifPresent(item -> detail.setReasonDesc(item.getDictName()));
        }
        // 该笔销售剩余可使用积分数
        detail.setLeftPoints(Math.max(dto.getChangePointsNum(), 0));
        // 产生积分变更时会员等级
        detail.setOrgGrade(member.getGrade());
        // 产生积分变更时会员当前积分数
        detail.setOrgPoints(member.getCurrentPoints());
        detail.setShopNo(StringUtils.isNotBlank(dto.getShopNo()) ? dto.getShopNo() : "");
        detail.setOrderNo(StringUtils.isNotBlank(dto.getSaleNo()) ? dto.getSaleNo() : "");
        detail.setAmount(dto.getChangePointsNum());
        Date date = new Date();
        detail.setCreateUser(StringUtils.isBlank(dto.getCreator()) ? "user_operation" : dto.getCreator());
        detail.setCreateDate(date);
        detail.setUpdateDate(date);
        detail.setUpdateUser(StringUtils.isBlank(dto.getCreator()) ? "user_operation" : dto.getCreator());
        detail.setChannel("0");
        // 积分清零，回填积分清零任务id
        if (StringUtils.isBlank(detail.getOrderNo()) && StringUtils.isNotBlank(dto.getAssociatedBusinessId())) {
            detail.setOrderNo(dto.getAssociatedBusinessId());
        }
        // 设置默认值
        detail.setDefaultVal();
        // 记录当前积分
        detail.setCurrentPoints(member.getCurrentPoints() + dto.getChangePointsNum());
        if (StringUtils.isNotBlank(dto.getContent())) {
            detail.setRemark(dto.getContent());
        } else {
            if (StringUtils.isNotBlank(dto.getRemark())) {
                detail.setRemark(dto.getRemark());
            } else {
                detail.setRemark(detail.getReasonDesc());
            }
        }
        // 补齐收银机号
        detail.setPosNo("");
        tbPointsDetailMapper.insert(detail);
        return detail.getId();
    }

    @Override
    public List<TbPointsDetail> recordList(MemberIntegralAdjustRecordPageQueryDto pageQuery) {
        pageQuery.setOffset(pageQuery.getPage() * pageQuery.getSize());
        List<TbPointsDetail> list = tbPointsDetailMapper.getList(pageQuery);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        list.forEach(item -> item.setMoon(DateUtil.format(item.getCreateDate(), "yyyy-MM")));
        return list;
    }

    @Override
    public List<TbPointsDetail> getPointsList(String groupId, String vipcode) {
        return tbPointsDetailMapper.getPointsList(groupId, vipcode);
    }

    @Override
    public int getCount(MemberIntegralAdjustRecordPageQueryDto pageQuery) {
        return tbPointsDetailMapper.getCount(pageQuery);
    }

    @Async
    @Override
    public CompletableFuture<List<MemberMonthlyIntegralResponse>> getMonthlyIntegral(String groupId, String mallId, String vipcode, List<String> moons) {
        List<TbPointsDetail> list = tbPointsDetailMapper.getMonthlyIntegral(groupId, mallId, vipcode, moons);
        // 按月分组
        Map<String, List<TbPointsDetail>> listMap = list.stream().collect(Collectors.groupingBy(TbPointsDetail::getMoon));
        List<TbPointsDetail> tmpList;
        List<MemberMonthlyIntegralResponse> months = new ArrayList<>(10);
        for (String moon: moons) {
            tmpList = MapUtils.getObject(listMap, moon);
            if (CollectionUtils.isEmpty(tmpList)) {
                continue;
            }
            int sum1 = tmpList.stream().filter(it -> it.getAmount() >= 0).mapToInt(TbPointsDetail::getAmount).sum();
            int sum2 = tmpList.stream().filter(it -> it.getAmount() < 0).mapToInt(TbPointsDetail::getAmount).sum();
            months.add(MemberMonthlyIntegralResponse.builder().month(moon).incomePoints(sum1).bonusPoints(sum2).build());
        }
        return CompletableFuture.completedFuture(months);
    }

    @Override
    public int getMemberConsumePointsBetweenDate(MemberPointSumQueryDto dto) {
        return tbPointsDetailMapper.getMemberConsumePointsBetweenDate(dto);
    }

    @Async
    @Override
    public CompletableFuture<Map<String, TbPointsDetail>> findByIds(List<String> adjustIds) {
        if (CollectionUtils.isEmpty(adjustIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        List<TbPointsDetail> list = tbPointsDetailMapper.findByCrmIds(adjustIds);
        if (CollectionUtils.isEmpty(list)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        return CompletableFuture.completedFuture(list.stream().collect(Collectors.toMap(TbPointsDetail::getCrmId, Function.identity(), (k1, k2) -> k2)));
    }

    @Override
    public int integralRecordTotal(String vipcode, String mallId) {
        return tbPointsDetailMapper.integralRecordTotal(vipcode, mallId);
    }

    @Override
    public List<TbPointsDetail> integralRecordList(String vipcode, String mallId, int page, int size) {
        return tbPointsDetailMapper.integralRecordList(vipcode, mallId, (page - 1) * size, size);
    }

    @Override
    public TbPointsDetail queryAdjustList(String groupId, String mallId, String orderNo, String shopNo, String remark) {
        return tbPointsDetailMapper.queryAdjustList(groupId, mallId, orderNo, shopNo, remark);
    }

    @Override
    public TbPointsDetail queryKerryPayPointsRedemptionAmountRecord(String groupId, String mallId, String vipcode, String orderNo, String shopNo, String reasonType) {
        return tbPointsDetailMapper.queryKerryPayPointsRedemptionAmountRecord(groupId, mallId, vipcode, orderNo, shopNo, reasonType);
    }

    @Override
    public List<TbPointsDetail> queryPointsRecordByConditions(String groupId, String mallId, String vipcode, String orderNo, String shopNo, List<String> reasonTypes) {
        return tbPointsDetailMapper.queryPointsRecordByConditions(groupId, mallId, vipcode, orderNo, shopNo, reasonTypes);
    }

    @Override
    public TbPointsDetail checkPointsAdjustExistRecord(String groupId, String mallId, String vipcode, String orderNo, Integer changePointsNum,
                                                       String reasonType, String startTime, String endTime) {
        return tbPointsDetailMapper.checkPointsAdjustExistRecord(groupId, mallId, vipcode, orderNo, changePointsNum, reasonType, startTime, endTime);
    }
}
