package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberBenefit;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/01/2022 11:33
 **********************************************************************************************************************/
public interface TbMemberBenefitMapper extends BaseMapper<TbMemberBenefit> {

    List<TbMemberBenefit> findByMallIdAndMemberGrade(@Param("mallId") String mallId, @Param("grade") String grade);

}
