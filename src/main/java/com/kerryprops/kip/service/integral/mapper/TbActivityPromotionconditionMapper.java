package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbActivityPromotionconditionMapper extends BaseMapper<TbActivityPromotionCondition> {

    List<TbActivityPromotionCondition> findByPromotionId(@Param("promotionId") String promotionId);

    List<TbActivityPromotionCondition> findByPromotionIds(@Param("promotionIds") List<String> promotionId);

}