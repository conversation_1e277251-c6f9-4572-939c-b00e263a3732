package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.enums.PointsInterceptStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.RefundStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsIntercept;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.exception.PointsException;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.response.TbInterceptResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/***********************************************************************************************************************
 * Project - points-service
 * <p>
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 13:38
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class MemberSalePointsProcessServiceImpl extends BaseMemberStatus implements MemberSalePointsProcessService {

    private final MallConfig mallConfig;
    private final TbSalesDetailService tbSalesDetailService;
    private final TbPointsRepeatRuleService tbPointsRepeatRuleService;
    private final TbSetshoprateService tbSetshoprateService;
    private final TbActivityPromotionService tbActivityPromotionService;
    private final TbMemberAssetService tbMemberAssetService;
    private final RabbitMqService rabbitMqService;
    private final TbBaseShopService tbBaseShopService;
    private final KerryStaffService kerryStaffService;
    private final TbMemberPointsChangeService tbMemberPointsChangeService;
    private final TbPointsDetailService tbPointsDetailService;
    private final TbInsensatePointsRepeatLogService tbInsensatePointsRepeatLogService;
    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private final TbPointsInterceptApproveService tbPointsInterceptApproveService;

    @Override
    public Integer salePointsProcess(SalesAutoPointsDto dto) {
        // 补充会员信息
        this.fillMemberData(dto);
        // 首先设置默认获取到积分为0
        dto.setExchangePoints(0);
        // 补齐店铺信息
        this.checkShopValid(dto);
        // 如果是北京的，新去重规则
        if (mallConfig.isBkc(dto.getGroupId())) {
            this.checkSaleIsDuplicateForBkc(dto);
        } else {
            // 非北京继续老去重规则
            this.checkSaleIsDuplicate(dto);
        }
        // 增加逻辑，判断用户支付宝是否开通无感积分授权
        this.checkSenselessPointsAuthorizeStatus(dto);
        // 销售记录判断拦截
        this.checkSaleIsIntercept(dto);
        // 补全积分变更记录ID
        if (StringUtils.isBlank(dto.getIntegralAdjustId())) {
            dto.setIntegralAdjustId(IdUtil.simpleUUID());
        }
        // 2. 计算该笔销售兑换成多少积分
        Integer integral = tbSetshoprateService.salesToIntegral(dto);
        // 销售额兑换成积分
        dto.setExchangePoints(integral);
        if (StringUtils.isNotBlank(dto.getSalesRemark())) {
            dto.setSalesRemark(dto.getSalesRemark() + integral + "分");
        } else {
            dto.setSalesRemark(dto.getRemarkName() + ", " + integral + "分");
        }
        if (integral > 0) {
            // 根据营销积分活动规则，计算出一个总积分数
            integral = tbActivityPromotionService.handlePromotionActivity(dto, Boolean.TRUE);
            dto.setExchangePoints(integral);
        }
        // 对应销售记录的crm_id
        dto.setId(StringUtils.isBlank(dto.getId()) ? IdUtil.simpleUUID() : dto.getId());
        // 保存销售记录
        tbSalesDetailService.saveSaleData(dto);
        // 调整用户积分数
        if (integral > 0) {
            tbMemberPointsChangeService.changeMemberPoints(dto);
        } else {
            // 生成一条积分为0的记录
            try {
                tbPointsDetailService.savePointsChangeRecord(dto);
            } catch (Exception e) {
                log.error("salePointsProcess: ", e);
            }
        }
        // 回传积分结果
        this.returnPointResult(dto, null);
        log.info("salePointsProcess - success, sellNo: {}, money: {}, points: {}", dto.getSaleNo(), dto.getAmount(), integral);
        return integral;
    }

    /**
     * 补充会员信息
     */
    private void fillMemberData(SalesAutoPointsDto dto) {
        // 从外部调用的接口可能会员为空，因为忽视注解
        if (Objects.isNull(dto.getMember())) {
            dto.setMember(tbMemberAssetService.findAllByDto(SingleMemberQueryDto.builder().vipcode(dto.getVipcode()).groupId(dto.getGroupId()).build()));
        }
        TbMemberAsset tbMemberAsset = dto.getMember();
        // 检验会员账号是否正常
        this.checkMemberStatus(tbMemberAsset);
        if (StringUtils.isBlank(tbMemberAsset.getKipUserId())) {
            Optional.ofNullable(kerryStaffService.findByMobile(tbMemberAsset.getMobile())).ifPresent(user -> tbMemberAsset.setKipUserId(user.getId()));
            dto.setMember(tbMemberAsset);
        }
    }

    /**
     * 判断该笔销售是否符合积分拦截规则
     */
    private void checkSaleIsIntercept(SalesAutoPointsDto dto) {
        log.info("checkSaleIsIntercept {}", JsonUtils.objToString(dto));
        // 积分拦截审核通过
        if (Objects.equals(dto.getInterceptStatus(), PointsInterceptStatusEnum.INTERCEPT_SUCCESS_STATUS.getType())) {
            return;
        }
        if (Objects.nonNull(dto.getBaseShop()) && StringUtils.equals(dto.getBaseShop().getId(), "KM00000001")) {
            log.info("线上商场不允许拦截");
            return;
        }
        TbInterceptResponse resp = tbPointsInterceptApproveService.checkSaleIsIntercept(dto);
        if (Boolean.FALSE.equals(resp.getIsIntercept())) {
            // 未达到积分拦截条件,所以该笔销售正常入库
            return;
        }
        // 积分拦截判断重复 小票20240613162404
        TbPointsIntercept points = tbPointsInterceptApproveService.getInterceptDto(TbPointsInterceptQueryDto
                .builder()
                .groupId(dto.getGroupId())
                .mallId(dto.getMallId())
                .orderNo(dto.getSaleNo())
                .mobile(dto.getMember().getMobile())
                .saleDate(DateUtil.formatDateTime(DateUtil.parse(dto.getSaleDate())))
                .build());
        if (Objects.nonNull(points)) {
            dto.setValidateStep(5);
            throw BizException.error(PointsEnum.POINTS_IS_INTERCEPT_REPEAT);
        }
        // 拦截记录进入积分拦截表
        TbPointsIntercept intercept = TbPointsIntercept
                .builder()
                .groupId(dto.getGroupId())
                .mallId(dto.getMallId())
                .mobile(dto.getMember().getMobile())
                .vipcode(dto.getMember().getVipcode())
                .kipUserId(StringUtils.isBlank(dto.getMember().getKipUserId()) ? "" : dto.getMember().getKipUserId())
                .saleId(StringUtils.isBlank(dto.getId()) ? "" : dto.getId())
                .orderNo(dto.getSaleNo())
                .shopNo(dto.getShopId())
                .saleType(dto.getSaleType())
                .totalAmount(StringUtils.isBlank(dto.getTotalAmount()) ? BigDecimal.ZERO : new BigDecimal(dto.getTotalAmount()))
                .discountAmount(StringUtils.isBlank(dto.getDiscountAmount()) ? BigDecimal.ZERO : new BigDecimal(dto.getDiscountAmount()))
                .payAmount(StringUtils.isBlank(dto.getAmount()) ? BigDecimal.ZERO : new BigDecimal(dto.getAmount()))
                .saleDate(DateUtil.parse(dto.getSaleDate()))
                .status(PointsInterceptStatusEnum.INTERCEPT_WAIT_STATUS.getType())
                .remarkName(StringUtils.isBlank(dto.getRemarkName()) ? "" : dto.getRemarkName())
                .appId(StringUtils.isBlank(dto.getAppId()) ? "" : dto.getAppId())
                .wxOrAliId(StringUtils.isBlank(dto.getWxOrAliId()) ? "" : dto.getWxOrAliId())
                .interceptReason(StringUtils.isBlank(resp.getInterceptReason()) ? "" : resp.getInterceptReason())
                .rejectReason("")
                .createDate(DateUtil.date())
                .createUser(dto.getCreateUser())
                .updateDate(DateUtil.date())
                .updateUser(dto.getCreateUser())
                .imgUrl(StringUtils.isBlank(dto.getImageUrl()) ? "" : dto.getImageUrl())
                .refundAmount(BigDecimal.ZERO)
                .refundPointsNum(0)
                .existSaleIds(resp.getInterceptSaleIds())
                .build();
        tbPointsInterceptApproveService.savePointsIntercept(intercept);
        dto.setValidateStep(4);
        throw new PointsException(PointsEnum.POINTS_IS_INTERCEPT.getCode(), PointsEnum.POINTS_IS_INTERCEPT.getMsg(), intercept.getId());
    }

    /**
     * 判断该笔销售是否符合去重规则
     */
    private void checkSaleIsDuplicate(SalesAutoPointsDto dto) {
        TbSalesDetail duplicatedSales;
        boolean isAutoPoints = IntegralConstant.UNIQUE_SALE_NO.contains(dto.getSaleType());
        // 第一步: 如果是微信无感,支付宝无感,扫码,POS,积分商场,优惠买单：不能单号重复
        if (isAutoPoints) {
            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto
                    .builder()
                    .groupId(dto.getGroupId())
                    .saleNo(dto.getSaleNo())
                    .saleTypes((IntegralConstant.UNIQUE_SALE_NO))
                    .build());
            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_1);
        }
        MemberSaleRepeatQueryDto queryDto = tbPointsRepeatRuleService.getSaleRepeatCondition(dto, isAutoPoints);
        if (Objects.isNull(queryDto)) {
            log.info("销售去重规则内配置了白名单: {}-{}-{}-{}", dto.getGroupId(), dto.getMallId(), dto.getVipcode(), dto.getShopId());
            return;
        }
        // 第二步:  跟普通渠道比，如果是微信无感,支付宝无感,扫码,POS,积分商场,优惠买单：用配置规则
        queryDto.setSaleTypes(SaleTypeEnum.getNormalSaleTypeValue());
        if (isAutoPoints) {
            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(queryDto);
            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_3);
        }

//        // 第二步: 跟普通渠道比，如果是非KO的销售录入或者拍照积分：用单号+配置规则
//        if (!isAutoPoints && !mallConfig.isKO(dto.getGroupId())) {
//            queryDto.setSaleNo(dto.getSaleNo());
//            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(queryDto);
//            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_3);
//        }

        // tick4.0.1优化, KO  OCR跟销售录入比:配置规则; OCR跟OCR比:单号+配置规则; 销售录入跟销售录入比:单号加配置规则
        if (!isAutoPoints) {
            List<MemberSaleRepeatQueryDto> queryList = new ArrayList<>(4);
            // 是KO，则需要跟OCR和销售录入比
            if (mallConfig.isKO(dto.getGroupId()) || mallConfig.isQats(dto.getGroupId())) {
                MemberSaleRepeatQueryDto queryDto1 = BeanUtil.copyProperties(queryDto, MemberSaleRepeatQueryDto.class);
                queryDto1.setSaleTypes(Collections.singletonList(SaleTypeEnum.TICKET.getValue()));
                MemberSaleRepeatQueryDto queryDto2 = BeanUtil.copyProperties(queryDto, MemberSaleRepeatQueryDto.class);
                queryDto2.setSaleTypes(Collections.singletonList(SaleTypeEnum.CRM.getValue()));
                if (StringUtils.equals(SaleTypeEnum.CRM.getValue(), dto.getSaleType())) {
                    queryDto1.setSaleNo(null);
                    queryDto2.setSaleNo(dto.getSaleNo());
                } else {
                    queryDto1.setSaleNo(dto.getSaleNo());
                    queryDto2.setSaleNo(null);
                }
                queryList.addAll(Arrays.asList(queryDto1, queryDto2));
                // 标记是否要排除全额退款的销售单做重复计算-Tick 4.8做的优化
                queryList.forEach(item -> item.setNotFullRefundFlag(1));
            } else {
                // 非KO，第二步: 跟普通渠道比，如果是非KO的销售录入或者拍照积分：用单号+配置规则
                MemberSaleRepeatQueryDto queryDto1 = BeanUtil.copyProperties(queryDto, MemberSaleRepeatQueryDto.class);
                queryDto1.setSaleNo(dto.getSaleNo());
                queryList.add(queryDto1);
            }
            // 第三步：跟单一渠道比，如果是单一渠道，参考第一步
            // 跟单一渠道比，如果是销售录入或者拍照积分：用规则配置(去掉上面set的单号)
            MemberSaleRepeatQueryDto queryDto0 = BeanUtil.copyProperties(queryDto, MemberSaleRepeatQueryDto.class);
            queryDto0.setSaleTypes(IntegralConstant.UNIQUE_SALE_NO);
            queryDto0.setSaleNo("");
            if (mallConfig.isKO(dto.getGroupId()) || mallConfig.isQats(dto.getGroupId())) {
                queryDto0.setNotFullRefundFlag(1);
            }
            queryList.add(queryDto0);
            List<TbSalesDetail> salesDetails = tbSalesDetailService.checkSaleRepeatedByConditions(queryList);
            this.fixedDuplicateProcess(CollectionUtils.isNotEmpty(salesDetails) ? salesDetails.get(0) : null, dto, IntegralConstant.CHECK_STEP_3);
        }

//        if (!isAutoPoints) {
//            queryDto.setSaleTypes(IntegralConstant.UNIQUE_SALE_NO);
//            queryDto.setSaleNo("");
//            if (mallConfig.isKO(dto.getGroupId())) {
//                queryDto.setNotFullRefundFlag(1);
//            }
//            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(queryDto);
//            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_3);
//        }
    }

    /**
     * 检查店铺信息
     * @param dto dto
     */
    private void checkSaleIsDuplicateForBkc(SalesAutoPointsDto dto) {
        TbSalesDetail duplicatedSales;
        boolean isAutoPoints = IntegralConstant.UNIQUE_SALE_NO_BKC.contains(dto.getSaleType());
        // 第一步: 如果是微信无感,支付宝无感,POS,积分商场,优惠买单：不能单号重复
        if (isAutoPoints) {
            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto
                    .builder()
                    .groupId(dto.getGroupId())
                    .saleNo(dto.getSaleNo())
                    .saleTypes((IntegralConstant.UNIQUE_SALE_NO_BKC))
                    .build());
            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_1);
        }
        MemberSaleRepeatQueryDto queryDto = tbPointsRepeatRuleService.getSaleRepeatCondition(dto, isAutoPoints);
        if (Objects.isNull(queryDto)) {
            log.info("checkSaleIsDuplicateForBkc: {}-{}-{}", dto.getMallId(), dto.getVipcode(), dto.getShopId());
            return;
        }
        // 第二步:  跟销售录入，拍照积分，扫码积分比，如果是微信无感,支付宝无感,POS,积分商场,优惠买单：用配置规则
        if (isAutoPoints) {
            queryDto.setSaleTypes(SaleTypeEnum.getNormalSaleTypeValueForBkc());
            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(queryDto);
            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_3);
        }

        // 第三步: 跟销售录入，拍照积分比，如果是销售录入，拍照积分：用单号+配置规则
        if (!isAutoPoints && !StringUtils.equals(SaleTypeEnum.SCAN.getValue(), dto.getSaleType())) {
            queryDto.setSaleNo(dto.getSaleNo());
            queryDto.setSaleTypes(SaleTypeEnum.getNormalSaleTypeValue());
            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(queryDto);
            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_3);
        }

        // 第四步: 跟销售录入，拍照积分比，如果是扫码积分：用配置规则
        if (!isAutoPoints && StringUtils.equals(SaleTypeEnum.SCAN.getValue(), dto.getSaleType())) {
            queryDto.setSaleNo("");
            queryDto.setSaleTypes(SaleTypeEnum.getNormalSaleTypeValue());
            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(queryDto);
            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_3);
        }

        // 第五步: 跟扫码积分比，如果是销售录入，拍照积分：用配置规则
        if (!isAutoPoints && !StringUtils.equals(SaleTypeEnum.SCAN.getValue(), dto.getSaleType())) {
            queryDto.setSaleNo("");
            queryDto.setSaleTypes(Collections.singletonList(SaleTypeEnum.SCAN.getValue()));
            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(queryDto);
            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_3);
        }

        // 第六步：跟单一渠道比，如果是单一渠道，参考第一步
        //       跟单一渠道比，如果是销售录入或者拍照积分或扫码积分：用规则配置
        if (!isAutoPoints) {
            queryDto.setSaleTypes(IntegralConstant.UNIQUE_SALE_NO_BKC);
            queryDto.setSaleNo("");
            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(queryDto);
            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_3);
        }
        // 第七步 扫码积分和扫码积分 增加一个使用小票单号校验的步骤
        if (StringUtils.equals(dto.getSaleType(), SaleTypeEnum.SCAN.getValue())) {
            duplicatedSales = tbSalesDetailService.checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto
                    .builder()
                    .groupId(dto.getGroupId())
                    .saleNo(dto.getSaleNo())
                    .saleTypes(Collections.singletonList(SaleTypeEnum.SCAN.getValue()))
                    .build());
            this.fixedDuplicateProcess(duplicatedSales, dto, IntegralConstant.CHECK_STEP_1);
        }
    }

    /**
     * 检查店铺信息
     * @param dto dto
     */
    private void checkShopValid(SalesAutoPointsDto dto) {
        if (Objects.nonNull(dto.getBaseShop())) {
            return;
        }
        TbBaseShop tbBaseShop = tbBaseShopService.getByContractNoAndMallId(dto.getShopId(), dto.getMallId());
        if (Objects.isNull(tbBaseShop)) {
            throw BizNotFoundException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        dto.setBaseShop(tbBaseShop);
    }

    /**
     * 检查无感积分授权状态
     * @param dto dto
     */
    private void checkSenselessPointsAuthorizeStatus(SalesAutoPointsDto dto) {
        if (!IntegralConstant.ALIPAY_POINTS.equals(dto.getRemark())) {
            return;
        }
        TbInsensatePointsAuthRecordDto recordDto = tbInsensatePointsAuthRecordService.getByAliUserId(dto.getWxOrAliId(), dto.getMallId());
        if (Objects.nonNull(recordDto)) {
            return;
        }
        throw BizException.error(PointsEnum.USER_UNAUTHORIZED_SENSELESS_POINTS);
    }

    /**
     * 处理销售记录重复时的逻辑
     * @param detail 销售记录
     * @param dto 自动积分对象
     * @param step 步骤
     */
    private void fixedDuplicateProcess(TbSalesDetail detail, SalesAutoPointsDto dto, int step) {
        if (Objects.isNull(detail)) {
            return;
        }
        log.info("salePointsProcess - 会员: [{}] 销售类型为: [{}] 单号: [{}] 已存在销售记录{}.", dto.getMember().getVipcode(), dto.getRemark(), dto.getSaleNo(), step);
        dto.setSalesRemark("根据积分去重规则判定为重复积分，获取积分数为0.");
        // 标记与哪笔销售记录发生了重复
        dto.setMemberSale(detail);
        // 标记那一个校验步骤发现了重复
        dto.setValidateStep(step);
        // 回传微信/支付宝积分结果
        this.returnPointResult(dto, detail);
        // 抛出异常
        throw BizException.error(PointsEnum.MEMBER_SALES_DUPLICATED);
    }

    /**
     * 回传微信/支付宝积分结果
     * @param dto 自动积分对象
     */
    private void returnPointResult(SalesAutoPointsDto dto, TbSalesDetail detail) {
        if (StringUtils.equals(dto.getRemark(), IntegralConstant.WECHAT_POINTS)) {
            // 微信支付，更新会员卡积分数
            rabbitMqService.sendLazyMessage(RabbitMqConstant.WECHAT_POINTS_SYNC_ROUTE_KEY, JsonUtils.objToString(dto), 5);
            // 传的TbMemberSale不为空，则说明重复
            if (Objects.nonNull(detail)) {
                tbInsensatePointsRepeatLogService.saveRepeatLog(dto, detail);
            }
        } else if (StringUtils.equals(dto.getRemark(), IntegralConstant.ALIPAY_POINTS)) {
            // 支付宝支付，更新会员卡积分数
            rabbitMqService.sendLazyMessage(RabbitMqConstant.ALIPAY_POINTS_SYNC_ROUTE_KEY, JsonUtils.objToString(dto), 3);
            // 传的TbMemberSale不为空，则说明重复
            if (Objects.nonNull(detail)) {
                tbInsensatePointsRepeatLogService.saveRepeatLog(dto, detail);
            }
        } else if (StringUtils.equals(dto.getRemark(), IntegralConstant.ALIPAY_REFUND_POINTS)) {
            // 支付宝退款，更新会员卡积分数
            rabbitMqService.sendLazyMessage(RabbitMqConstant.ALIPAY_POINTS_SYNC_ROUTE_KEY, JsonUtils.objToString(dto), 3);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int refundSalePointsProcess(SalesAutoPointsDto dto) {
        // 销售录入-无单退款
        if (StringUtils.equals(dto.getRemark(), IntegralConstant.NO_ORDER_REFUND)) {
            return this.handleNoOrderRefund(dto);
        }
        // 获取该笔交易支付获得积分
        TbSalesDetail detail = this.getSaleDetail(dto);
        if (Objects.isNull(detail)) {
            this.updatePointsIntercept(dto);
            return 0;
        }
        if (StringUtils.isBlank(dto.getAmount())) {
            dto.setAmount(detail.getPayAmount().toString());
        }
        int refundPoints;
        // 设置更新参数，同步更新integralAdjust、member表
        // 如果退款金额和销售金额相等，那么退还的积分等于此次销售的全部积分
        detail.setRefundAmount(NumberUtil.add(new BigDecimal(dto.getRefundAmount()), (Objects.isNull(detail.getRefundAmount()) ? BigDecimal.ZERO : detail.getRefundAmount())));
        // 记录销售单的实付金额
        BigDecimal payAmount = detail.getPayAmount();
        if (detail.getRefundAmount().compareTo(payAmount) >= 0) {
            refundPoints = detail.getPointsNum() - (Objects.isNull(detail.getRefundPointsNum()) ? 0 : detail.getRefundPointsNum());
        } else {
            // 不相等根据退款金额按比例退还积分
            BigDecimal refundAmount = new BigDecimal(dto.getRefundAmount());
            BigDecimal saleIntegral = BigDecimal.valueOf(detail.getPointsNum());
            // 获取退款积分 = (销售积分 * 退款金额) / 销售金额 防止精度溢出
            refundPoints = saleIntegral.multiply(refundAmount).divide(payAmount, 2, RoundingMode.FLOOR).intValue();
        }
        /*if (StringUtils.equals(IntegralConstant.ORDER_REFUND, dto.getRemark()) && refundPoints > dto.getMember().getCurrentPoints()) {
            throw BizException.error(PointsEnum.MEMBER_INTEGRAL_LESS_THEN_INTEGRAL);
        }*/
        dto.setExchangePoints(-refundPoints);
        // 标记销售单为已退款或部分退款
        detail.setStatus(detail.getRefundAmount().compareTo(payAmount) >= 0 ? RefundStatusEnum.REFUND.getValue(): RefundStatusEnum.PARTIAL_REFUND.getValue());
        detail.setRefundPointsNum((Objects.isNull(detail.getRefundPointsNum()) ? 0 : detail.getRefundPointsNum()) + refundPoints);
        detail.setRefundPointsId(refundPoints == 0 ? detail.getPointsId() : IdUtil.simpleUUID());
        detail.setUpdateDate(new Date());
        detail.setUpdateUser(dto.getCreateUser());
        // 保存销售记录
        log.info("refundSalePointsProcess - updating member_sale id:{}, sellNo: {}, backMoney: {}, status: {}, backIntegral: {}, backIntegralAdjustId: {}, refundAmount: {}"
                , detail.getId(), detail.getOrderNo(), detail.getRefundAmount(), detail.getStatus(), detail.getRefundPointsNum(), detail.getRefundPointsId(), dto.getRefundAmount());
        tbSalesDetailService.updateById(detail);
        // 回填退款金额
        detail.setPayAmount(NumberUtil.sub(BigDecimal.ZERO, detail.getRefundAmount()));
        // 线上商场不走这个逻辑
        if (!StringUtils.equals(dto.getRemark(), IntegralConstant.REFUND_REWARDS_MALL)) {
            rabbitMqService.sendMqMessage(detail, dto);
        }
        if (refundPoints == 0) {
            return 0;
        }
        // 保存积分调整记录
        dto.setIntegralAdjustId(detail.getRefundPointsId());
        dto.setSalesRemark(String.format("%s - 销售金额: %s, 退款金额: %s, 应扣积分: %s" ,dto.getRemarkName(), payAmount, detail.getRefundAmount(), refundPoints));
        tbMemberPointsChangeService.changeMemberPoints(dto);
        log.info("refundPointsProcess - success sellNo: {}, backMoney: {}, status: {}, backIntegral: {}, backIntegralAdjustId: {}"
            , detail.getOrderNo(), detail.getRefundAmount(), detail.getStatus(), detail.getRefundPointsNum(), detail.getRefundPointsId());
        // 回传积分结果
        this.returnPointResult(dto, null);
        return refundPoints;
    }

    private void updatePointsIntercept(SalesAutoPointsDto dto) {
        TbPointsIntercept intercept = tbPointsInterceptApproveService.getInterceptDto(TbPointsInterceptQueryDto
                .builder()
                .groupId(dto.getGroupId())
                .mallId(dto.getMallId())
                .mobile(Objects.isNull(dto.getMember()) ? "" : dto.getMember().getMobile())
                .orderNo(dto.getSaleNo())
                .shopNo(dto.getShopId())
                .build());
        // 说明sale表和异常记录拦截表都没有相关数据
        if (Objects.isNull(intercept)) {
            log.error("isErrorSalesDetail - fail to get memberSale info. sell: {}, mallId: {}", dto.getSaleNo(), dto.getMallId());
            throw BizException.error(PointsEnum.INTERCEPT_NOT_EXISTS_ERROR);
        }
        intercept.setRefundAmount(NumberUtil.add(new BigDecimal(dto.getRefundAmount()), (Objects.isNull(intercept.getRefundAmount()) ? BigDecimal.ZERO : intercept.getRefundAmount())));
        intercept.setUpdateUser(StringUtils.isBlank(dto.getCreateUser()) ? "REFUND" : dto.getCreateUser());
        intercept.setUpdateDate(DateUtil.date());
        if (intercept.getRefundAmount().compareTo(intercept.getPayAmount()) >= 0) {
            intercept.setStatus(PointsInterceptStatusEnum.INTERCEPT_REFUND_STATUS.getType());
        }
        tbPointsInterceptApproveService.updateById(intercept);
        log.info("updateInterceptRefundMoney sell: {}, mallId: {}", dto.getSaleNo(), dto.getVipcode());
    }

    private TbSalesDetail getSaleDetail(SalesAutoPointsDto dto) {
        if (Objects.nonNull(dto.getMemberSale())) {
            return dto.getMemberSale();
        }
        MemberSaleRepeatQueryDto queryDto = MemberSaleRepeatQueryDto.builder()
                .mallId(dto.getMallId())
                .saleNo(dto.getSaleNo())
                .shopId(dto.getShopId())
                .vipcode(dto.getVipcode())
                .build();
        // 销售id不为空时，带入查询条件中
        Optional.ofNullable(dto.getRefundSaleId()).ifPresent(queryDto::setSaleId);
        TbSalesDetail detail = tbSalesDetailService.checkSaleRecordRepeatedOrNot(queryDto);
        // 销售记录为空
        if (Objects.isNull(detail)) {
            return null;
        }
        if (NumberUtil.add(new BigDecimal(dto.getRefundAmount()), detail.getRefundAmount()).compareTo(detail.getPayAmount()) > 0){
            throw BizException.error(PointsEnum.REFUND_MONEY_OUT_OF_MONEY);
        }
        dto.setMemberSale(detail);
        return detail;
    }

    /**
     * 无单退货操作：
     * 1、扣积分
     * 2、记录退货销售记录
     *
     * @param dto dto
     * @return int int
     */
    private int handleNoOrderRefund(SalesAutoPointsDto dto) {
        if (StringUtils.isBlank(dto.getIntegralAdjustId())) {
            dto.setIntegralAdjustId(IdUtil.simpleUUID());
        }
        dto.setSalesRemark(String.format("%s - 退款金额: %s,积分: %s" ,dto.getRemarkName(), dto.getAmount(), dto.getExchangePoints()));
        // 修改会员积分并且在积分变更表新增记录，通过sql修改会员当前积分
        tbMemberPointsChangeService.changeMemberPoints(dto);
        // 保存无单退货记录
        tbSalesDetailService.saveNoOrderRefundRecord(dto);
        log.info("NoOrderRefundProcess - success NoSellNo");
        return 0;
    }
}
