package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/06/2023 15:56
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_cash_out_config")
public class TbCashOutConfig implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("group_id")
    private String groupId;

    @TableField("mall_id")
    private String mallId;

    @TableField("name")
    private String name;

    /**
     * 优惠买单积分抵现功能改造
     */
    @TableField("business_type")
    private String businessType;

    @TableField("start_time")
    private Date startTime;

    @TableField("end_time")
    private Date endTime;

    @TableField("shop_no")
    private String shopNo;

    @TableField("is_limit")
    private Integer isLimit;

    @TableField("money")
    private BigDecimal money;

    @TableField("create_date")
    private Date createDate;

    @TableField("creator")
    private String createUser;

    @TableField("update_date")
    private Date updateDate;

    @TableField("updater")
    private String updateUser;
}
