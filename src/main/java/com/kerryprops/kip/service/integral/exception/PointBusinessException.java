package com.kerryprops.kip.service.integral.exception;

import com.kerryprops.kip.service.integral.common.current.IError;
import lombok.Data;

import java.util.function.Supplier;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2022 17:03
 **********************************************************************************************************************/

@Data
public class PointBusinessException extends RuntimeException {

    protected final Integer code;
    protected final String msg;

    public PointBusinessException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public PointBusinessException(IError ie) {
        super(ie.getMsg());
        this.code = ie.getCode();
        this.msg = ie.getMsg();
    }

    public static PointBusinessException error(IError iError) {
        return new PointBusinessException(iError);
    }

    public static PointBusinessException error(Integer code, String msg) {
        return new PointBusinessException(code, msg);
    }

    public static Supplier<PointBusinessException> bizNotFoundException(IError iError) {
        return () -> new PointBusinessException(iError);
    }

}
