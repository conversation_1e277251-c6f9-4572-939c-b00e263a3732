package com.kerryprops.kip.service.integral.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.MemberGradeChangeTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.MessageTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.RuleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC: 销售发生退款后，重新计算会员销售额进行卡等级判断
 * <AUTHOR> Bert
 * Created Date - 04/24/2023 13:41
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class MemberSaleRefundProcessListener {

    private final MallConfig mallConfig;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbBaseShopService tbBaseShopService;
    private final TbSalesDetailService tbSalesDetailService;
    private final TbMemberGradeService tbMemberGradeService;
    private final ProfileServiceClient profileServiceClient;
    private final KerryStaffService kerryStaffService;
    private final TbMemberGradeRuleService tbMemberGradeRuleService;
    private final MemberGradeStrategyService memberGradeStrategyService;
    private final TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    private final TbMemberGradeEffectiveConfigService tbMemberGradeEffectiveConfigService;
    private final TbActivityPromotionIntegralService tbActivityPromotionIntegralService;
    private final TbGradeChangeBetweenSalesService tbGradeChangeBetweenSalesService;
    private final RabbitMqService rabbitMqService;
    private final RedisService redisService;

    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.MEMBER_SALE_REFUND_CHECK_GRADE}, containerFactory = "crmContainerFactory")
    public void refundCheckMemberGradeProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("接收到会员销售退款消息体为空.");
            return;
        }
        log.info("MemberSaleRefundProcessListener: {}" , msg);
        Map<String, Object> toMap = JsonUtils.stringToMap(msg);
        String saleId = MapUtils.getString(toMap, IntegralConstant.SALE_ID);
        // 销售来源
        String fromType = MapUtils.getString(toMap, IntegralConstant.FROM_TYPE);
        if (StringUtils.isBlank(saleId)) {
            log.error("MQ信息转对象失败-{}", msg);
            return;
        }
        // 退款的销售记录
        TbSalesDetail memberSale = tbSalesDetailService.getMemberSaleById(Long.parseLong(saleId));
        if (Objects.isNull(memberSale)) {
            log.error("未查询到会员销售信息-{}", saleId);
            return;
        }
        // 查询会员信息 针对积分订单转赠需要根据传递的会员号查询会员
        String vipcode = StringUtils.isNotBlank(MapUtils.getString(toMap, IntegralConstant.VIP_CODE)) ?
                MapUtils.getString(toMap, IntegralConstant.VIP_CODE) : memberSale.getVipcode();
        memberSale.setVipcode(vipcode);
        // 该销售匹配到的营销积分活动积分扣减
        tbActivityPromotionIntegralService.updatePromotionIntegral(memberSale);
        // 所属商场
        MallItem mallItem = mallConfig.getByMallId(memberSale.getMallId());
        if (Objects.isNull(mallItem)) {
            log.error("未查询到匹配的商场信息，{}-{}", saleId, memberSale.getMallId());
            return;
        }
        // 检查退货触发降级配置开关是否打开
        TbMemberGradeEffectiveConfig effectiveConfig = tbMemberGradeEffectiveConfigService.findByGroupId(memberSale.getGroupId());
        if (Objects.isNull(effectiveConfig) || Boolean.FALSE.equals(effectiveConfig.getEnableRefundDowngrade())) {
            log.info("集团: {}, 退货自动降级配置未配置或开关未打开.", memberSale.getGroupId());
            return;
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByVipcodeAndGroupId(vipcode, memberSale.getGroupId());
        if (Objects.isNull(tbMemberAsset)) {
            log.error("该销售记录对应的会员信息不存在-{}-{}", memberSale.getVipcode(), memberSale.getGroupId());
            return;
        }
        // 该group_id下对应的等级列表
        List<TbMemberGrade> grades = tbMemberGradeService.queryGradeSortDescByGroupId(memberSale.getGroupId());
        if (CollectionUtils.isEmpty(grades)) {
            log.error("该销售记录对应的group会员等级不存在-{}", memberSale.getGroupId());
            return;
        }
        // group下最低卡等
        TbMemberGrade minGrade = grades.stream().min(Comparator.comparing(TbMemberGrade::getSort)).orElse(grades.get(grades.size() - 1));
        // 会员当前卡等
        TbMemberGrade currentGrade = grades.stream().filter(it -> StringUtils.equals(it.getCode(), tbMemberAsset.getGrade())).findFirst().orElse(null);
        // 尊享卡会员不参与升降级
        if (Objects.nonNull(currentGrade) && currentGrade.getUpGradationStatus() == 0) {
            log.error("该会员是尊享卡，不参与升降级-{}", tbMemberAsset.getVipcode());
            return;
        }
        // 会员当前卡等是否是最低卡等
        if (Objects.nonNull(currentGrade) && StringUtils.equals(currentGrade.getCode(), minGrade.getCode())) {
            log.error("该会员已是最低卡等，无需调整会员等级-{}", tbMemberAsset.getVipcode());
            return;
        }
        // 销售时间
        String saleDate = DateUtil.formatDateTime(memberSale.getSaleDate());
        Date currentDate = new Date();
        // 开始时间-根据是否是周期年判断
        MemberYearAmountCalDto calDto = memberGradeStrategyService.getCalculateYearAmountBeginDate(tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode());
        Date startTime = calDto.getBeginDate();
        // 退款销售记录前的会员等级
        String memberGradeBeforeSales = calDto.getOrgGrade();
        // 保级记录对应的会员等级
        TbMemberGrade relegationGrade = grades.stream().filter(it -> StringUtils.equals(it.getCode(), memberGradeBeforeSales)).findFirst().orElse(null);
        if (Objects.nonNull(currentGrade) && Objects.nonNull(relegationGrade)) {
            grades = grades.stream()
                    .filter(it -> (currentGrade.getSort() >= it.getSort() && it.getSort() > relegationGrade.getSort()))
                    .filter(it -> Objects.equals(it.getUpGradationStatus(), 1))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(grades)) {
            log.info("年初保级会员等级与当前会员等级一致，销售退单不做会员等级变更处理: {}-{}", tbMemberAsset.getVipcode(), saleId);
            return;
        }
        // tick 3.15添加逻辑：发生退款的销售记录销售时间 > 会员等级变更时间，则不做处理
        TbMemberGradeChangeDetail changeDetail = tbMemberGradeChangeDetailService.getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode(), DateUtil.formatDateTime(startTime));
        // 补齐历史等级变更记录是否标记销售区间
        TbGradeChangeBetweenSales gradeBetweenSales = tbGradeChangeBetweenSalesService.checkChangeBetweenSales(changeDetail, calDto, minGrade);
        // 退款销售记录的销售时间发生在最新的等级变更记录之后，则不做处理
        if (Objects.nonNull(gradeBetweenSales) && memberSale.getSaleDate().after(gradeBetweenSales.getSalesEndTime())) {
            log.info("退款发生在会员等级变更记录之后，不做会员等级调整处理: 会员号: {} - 销售时间: {} - 等级变更时间: {}", tbMemberAsset.getVipcode(), saleDate, DateUtil.formatDateTime(changeDetail.getCreateDate()));
            return;
        }
        List<String> shopNos = null;
        String remark = null;
        // 匹配到的会员等级
        TbMemberGrade matchedGrade = null;
        // 保级身份认证配置
        List<MemberIdentityInfo> identityList = profileServiceClient.getProjectIdentityResponse(tbMemberAsset.getKipUserId(), mallItem.getProjectId());
        Date beginDate = null;
        Date endDate = null;
        for (TbMemberGrade grade: grades) {
            // 查询会员等级规则信息
            List<TbMemberGradeRule> gradeRules = tbMemberGradeRuleService.queryGradeRulesByGroupIdAndGradeAndRuleTypes(memberSale.getGroupId(), grade.getCode(), Arrays.asList(1, 2, 3, 7, 8));
            if (CollectionUtils.isEmpty(gradeRules)) {
                log.info("该会员等级: [{}]下未查询到会员升级规则信息", grade.getCode());
                continue;
            }
            // 解析入会当日的并行条件规则
            gradeRules = this.parseRules(gradeRules);
            // for循环判断等级消费是否满足
            for (TbMemberGradeRule rule: gradeRules) {
                // 保级身份认证判断
                if (Objects.equals(RuleTypeEnum.MEMBER_IDENTITY_RELEGATION.getValue(), rule.getRuleType())) {
                    log.info("身份认证规则信息，{}-{}", grade.getCode(), rule.getRuleType());
                    // 身份认证保级规则
                    for (MemberIdentityInfo identity: identityList) {
                        if (StringUtils.isAllBlank(identity.getOfficeMember(), identity.getApartmentMember())) {
                            continue;
                        }
                        MemberIdentityRuleDto identityRuleDto = JsonUtils.stringToObj(rule.getCertification(), MemberIdentityRuleDto.class);
                        if (Objects.isNull(identityRuleDto)) {
                            continue;
                        }
                        // 办公楼身份/公寓身份判断
                        if (this.checkMemberIdentities(identityRuleDto.getOffice(), identity.getOfficeMember()) ||
                                this.checkMemberIdentities(identityRuleDto.getApartment(), identity.getApartmentMember())) {
                            matchedGrade = grade;
                            break;
                        }
                    }
                    // 如果身份信息匹配，则结束循环
                    if (Objects.nonNull(matchedGrade)) {
                        break;
                    } else {
                        continue;
                    }
                } else {
                    // 消费金额等级判断
                    if (Objects.isNull(rule.getMoney()) || rule.getMoney().compareTo(BigDecimal.ZERO) < 0) {
                        log.info("该会员等级: [{}]下规则类型: [{}]设置的金额不正确", grade.getCode(), rule.getRuleType());
                        continue;
                    }
                    // 店铺业态对应的店铺编号
                    shopNos = this.getShopNosByGroupIdAndFormats(memberSale.getGroupId(), rule.getFormats());
                    if (RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                        log.info("每日消费消费金额判断");
                        beginDate = startTime;
                        // Tick 4.4.0添加每日消费金额发生变更后，从规则生效时间开始计算金额
                        if (Objects.nonNull(rule.getEffectiveDate()) && rule.getEffectiveDate().after(beginDate)) {
                            beginDate = DateUtil.beginOfDay(rule.getEffectiveDate());
                        }
                        // Tick 4.7.0等级升级统计销售的开始时间与当前时间是否是同一天
                        if (StringUtils.equals(DateUtil.formatDate(calDto.getBeginDate()), DateUtil.formatDate(beginDate))) {
                            beginDate = calDto.getBeginDate();
                        }
                        endDate = currentDate;
                    } else if (RuleTypeEnum.ACCUMULATIVE_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                        log.info("年累计消费金额判断");
                        beginDate = startTime;
                        endDate = currentDate;
                    } else if (RuleTypeEnum.MONTHLY_ACCUMULATIVE_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                        // 月累计升级规则
                        log.info("月累计消费金额判断");
                        beginDate = startTime;
                        endDate = currentDate;
                    } else if (RuleTypeEnum.JOIN_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())
                            && StringUtils.equals(saleDate, DateUtil.formatDate(tbMemberAsset.getJoinTime()))) {
                        // 入会当日消费规则
                        log.info("入会当日金额判断");
                        beginDate = DateUtil.beginOfDay(memberSale.getSaleDate());
                        endDate = DateUtil.endOfDay(memberSale.getSaleDate());
                    } else {
                        continue;
                    }
                }
                // 判断是否满足升级条件
                boolean succeeded = tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(tbMemberAsset, rule, beginDate, endDate, shopNos);
                if (succeeded) {
                    if (RuleTypeEnum.JOIN_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                        if (this.checkJoinDayParallelConditions(rule.getJoinDayParallelRules(), tbMemberAsset, beginDate, endDate)) {
                            matchedGrade = grade;
                            break;
                        } else {
                            log.info("会员入会当日规则发生退款，不满足入会当日并行条件: {}-{}-{}", memberSale.getGroupId(), memberSale.getVipcode(), rule.getFormatsDesc());
                        }
                    } else {
                        matchedGrade = grade;
                        break;
                    }
                } else {
                    // 每日消费升级历史记录检查
                    if (RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue().equals(rule.getRuleType()) && StringUtils.isNotBlank(rule.getCertification())) {
                        PreviousDailyConsumptionRuleDto ruleDto = JsonUtils.stringToObj(rule.getCertification(), PreviousDailyConsumptionRuleDto.class);
                        // 上一次每日消费升级规则判断，只有在上一次规则生效时间和当前规则设置的生效时间之内的，需要重新检查
                        if (Objects.nonNull(ruleDto) && Objects.nonNull(ruleDto.getEffectiveDate()) && ruleDto.getEffectiveDate().before(rule.getEffectiveDate()) &&
                                memberSale.getSaleDate().after(ruleDto.getEffectiveDate()) && memberSale.getSaleDate().before(rule.getEffectiveDate())) {
                            // 查询对应业态下的店铺编号
                            shopNos = this.getShopNosByGroupIdAndFormats(memberSale.getGroupId(), ruleDto.getFormats());
                            if (CollectionUtils.isNotEmpty(shopNos)) {
                                succeeded = tbSalesDetailService.checkPreviousDailyConsumptionRuleWhetherRelegationSucceeded(tbMemberAsset, ruleDto, DateUtil.beginOfDay(beginDate),
                                        DateUtil.endOfDay(DateUtil.offsetDay(rule.getEffectiveDate(), -1)), shopNos);
                                // 前一次设置的每日消费升级金额
                                if (succeeded) {
                                    matchedGrade = grade;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if (Objects.nonNull(matchedGrade)) {
                break;
            }
        }
        // 重置会员开卡商场id
        tbMemberAsset.setMallId(mallItem.getMallId());
        remark = "有单退货，消费金额不满足当前等级，做降级处理";
        if (Objects.isNull(matchedGrade)) {
            log.info("销售退款后，会员未匹配到满足条件的等级规则，删除该销售记录产生时间之后的等级变更记录");
            // TICK 4.4(SCRM-8022:添加IPAD/Admin端会员等级变更通知消息)
            this.setMemberGradeChangeNotifyMessage(tbMemberAsset.getGroupId(), tbMemberAsset.getMobile(),
                    Objects.isNull(relegationGrade) ? minGrade.getName() : relegationGrade.getName(), fromType);
            // 执行等级变更记录的新增
            this.saveMemberGradeChangeDetail(tbMemberAsset, MemberGradeChangeTypeEnum.TYPE_14.getCode(), tbMemberAsset.getGrade(),
                    memberGradeBeforeSales, remark, memberSale.getUpdateUser(), currentDate, null);
            return;
        }
        // 匹配到的等级规则与会员当前等级相同，说明退款不影响会员等级，判断逻辑结束
        if (StringUtils.equals(matchedGrade.getCode(), tbMemberAsset.getGrade())) {
            log.info("退款销售记录: {}-{}，不影响当前会员等级信息，匹配到的等级规则及会员当前等级信息如下: {}-{}", memberSale.getOrderNo(), saleDate, matchedGrade.getCode(), tbMemberAsset.getGrade());
            // 保存等级变更标记的销售区间
            Optional.ofNullable(changeDetail).ifPresent(item -> tbGradeChangeBetweenSalesService.saveDetail(item.getId(), calDto.getBeginDate(), currentDate));
            return;
        }
        // TICK 4.4(SCRM-8022:添加IPAD/Admin端会员等级变更通知消息)
        this.setMemberGradeChangeNotifyMessage(tbMemberAsset.getGroupId(), tbMemberAsset.getMobile(), matchedGrade.getName(), fromType);
        // 执行等级变更记录的新增
        this.saveMemberGradeChangeDetail(tbMemberAsset, MemberGradeChangeTypeEnum.TYPE_14.getCode(), tbMemberAsset.getGrade(),
                matchedGrade.getCode(), remark, memberSale.getUpdateUser(), currentDate, calDto);
    }

    /**
     * 添加会员等级变更通知消息
     * @param groupId
     * @param mobile
     * @param gradeName
     * @param fromType
     */
    private void setMemberGradeChangeNotifyMessage(String groupId, String mobile, String gradeName, String fromType) {
        if (StringUtils.isNotBlank(fromType) && IntegralConstant.IPAD_S_SALES.stream().anyMatch(item -> StringUtils.equalsIgnoreCase(item, fromType))) {
            String notifyKey = String.format(RedisCacheKey.MEMBER_GRADE_CHANGE_NOTIFY_KEY, groupId, mobile);
            redisService.setVal(notifyKey, gradeName, 5L, TimeUnit.MINUTES);
        }
    }

    /**
     * 检查入会当日并行条件
     * @param conditions
     * @param tbMemberAsset
     * @param beginDate
     * @param endDate
     * @return
     */
    private boolean checkJoinDayParallelConditions(List<TbMemberGradeRule> conditions, TbMemberAsset tbMemberAsset, Date beginDate, Date endDate) {
        if (CollectionUtils.isEmpty(conditions)) {
            return true;
        }
        boolean joinDayParallel = true;
        List<String> shopCodes = null;
        for (TbMemberGradeRule joinDayRule : conditions) {
            shopCodes = this.getShopNosByGroupIdAndFormats(tbMemberAsset.getGroupId(), joinDayRule.getFormats());
            if (CollectionUtils.isEmpty(shopCodes)) {
                joinDayParallel = false;
                break;
            }
            // 查询当日入会并行规则是否满足
            joinDayParallel = tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(tbMemberAsset, joinDayRule, beginDate, endDate, shopCodes);
            if (!joinDayParallel) {
                break;
            }
        }
        return joinDayParallel;
    }

    /**
     * 解析加入日并行条件
     * @param gradeRules
     * @return
     */
    private List<TbMemberGradeRule> parseRules(List<TbMemberGradeRule> gradeRules) {
        List<TbMemberGradeRule> rules = new ArrayList<>(10);
        Long pid = 0L;
        for (TbMemberGradeRule rule: gradeRules) {
            if (3 == rule.getRuleType()) {
                if (pid.equals(rule.getPid())) {
                    rule.setJoinDayParallelRules(gradeRules.stream().filter(it -> (3 == rule.getRuleType() && rule.getId().equals(it.getPid()))).collect(Collectors.toList()));
                    // 入会当日第一规则
                    rules.add(rule);
                }
            } else {
                rules.add(rule);
            }
        }
        return rules;
    }

    /**
     * 检查身份信息是否匹配
     * @param configIdentity: 配置的身份信息
     * @param realIdentity: 用户真实的身份信息
     * @return
     */
    private boolean checkMemberIdentities(String configIdentity, String realIdentity) {
        if (StringUtils.isAnyBlank(configIdentity, realIdentity)) {
            return false;
        }
        List<String> configList = this.strToList(configIdentity);
        List<String> realList = this.strToList(realIdentity);
        return realList.stream().anyMatch(configList::contains);
    }

    private List<String> strToList(String str) {
        if (StringUtils.isBlank(str)) {
            return Collections.emptyList();
        }
        return CharSequenceUtil.split(str, CommonSeparators.COMMA_SEPARATOR);
    }

    /**
     * 通过业态查询店铺号
     * @param groupId
     * @param formats
     * @return
     */
    private List<String> getShopNosByGroupIdAndFormats(String groupId, String formats) {
        // 查询对应业态下的店铺编号
        return tbBaseShopService.getContractNoList(groupId, formats);
    }

    /**
     * 保存等级变更记录
     * @param tbMemberAsset
     * @param changeType
     * @param oldGrade
     * @param newGrade
     * @param remark
     * @param userName
     * @param changeDate
     * @param calDto: 会员升级原始区间及卡等
     */
    private void saveMemberGradeChangeDetail(TbMemberAsset tbMemberAsset, Integer changeType, String oldGrade, String newGrade,
                                             String remark, String userName, Date changeDate, MemberYearAmountCalDto calDto) {
        log.info("MemberSaleRefundProcessListener，会员等级发生调整: {}-{}-{}-{}", tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode(), oldGrade, newGrade);
        // 执行等级变更
        tbMemberAsset.setGrade(newGrade);
        tbMemberAssetService.updateMemberGrade(tbMemberAsset);
        // 保存等级变更记录
        TbMemberGradeChangeDetail entity = new TbMemberGradeChangeDetail();
        entity.setId(IdUtil.simpleUUID());
        entity.setOldGrade(oldGrade);
        entity.setNewGrade(newGrade);
        entity.setVipcode(tbMemberAsset.getVipcode());
        entity.setRemark(remark);
        entity.setCreateDate(changeDate);
        entity.setGroupId(tbMemberAsset.getGroupId());
        entity.setChangeType(changeType);
        // 填充昵称
        Optional.ofNullable(kerryStaffService.findByKipUserId(tbMemberAsset.getKipUserId())).ifPresent(profile -> entity.setNickName(profile.getNickName()));
        if (StringUtils.isBlank(entity.getNickName())) {
            entity.setNickName("会员用户");
        }
        entity.setMobile(tbMemberAsset.getMobile());
        entity.setMallId(tbMemberAsset.getMallId());
        entity.setCreateUser(StringUtils.isNotBlank(userName) ? userName : "");
        entity.setMemberId(tbMemberAsset.getId()+"");
        entity.setKipUserId(tbMemberAsset.getKipUserId());
        tbMemberGradeChangeDetailService.saveDetail(entity);
        // 保存等级变更标记的销售区间
        if (Objects.nonNull(calDto) && !StringUtils.equals(calDto.getOrgGrade(), entity.getNewGrade())) {
            tbGradeChangeBetweenSalesService.saveDetail(entity.getId(), calDto.getBeginDate(), entity.getCreateDate());
        }
        // 发送降级短信
        if(Arrays.asList(MemberGradeChangeTypeEnum.TYPE_14.getCode(), MemberGradeChangeTypeEnum.TYPE_4.getCode(), MemberGradeChangeTypeEnum.TYPE_0.getCode()).contains(changeType)) {
            SendMessageDto dto = SendMessageDto.builder().memberId(entity.getMemberId()).mallId(tbMemberAsset.getMallId()).templateType(1)
                    .messageType(MessageTypeEnum.MEMBER_DOWNGRADE_NOTIFICATION.getType()).groupId(entity.getGroupId())
                    .updateDate(DateUtil.formatDateTime(new Date())).build();
            rabbitMqService.sendLazyMessage(RabbitMqConstant.XCRM_MEMBER_INTEGRAL_GRADE_CHANGE, JsonUtils.objToString(dto), 3);
        }
    }

    public static void main(String[] args) {
        Date saleTime = DateUtil.parseDateTime("2024-08-14 16:23:34");
        Date changeTime = DateUtil.parseDateTime("2024-02-12 16:23:34");
        System.out.println(saleTime.after(changeTime));
    }

}
