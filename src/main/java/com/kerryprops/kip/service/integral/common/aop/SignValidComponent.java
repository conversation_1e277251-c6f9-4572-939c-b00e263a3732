package com.kerryprops.kip.service.integral.common.aop;

import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - profile-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/22/2021 16:27
 **********************************************************************************************************************/

@Aspect
@Slf4j
@Component
public class SignValidComponent {

    private static final String DELIMITER = "&";

    @Autowired
    KipSignProperties kipSignProperties;

    @Pointcut("execution(public * *(..)) && @annotation(com.kerryprops.kip.service.integral.common.aop.SignValid)")
    private void checkSign() {}

    @Before("checkSign()")
    public void doBefore(JoinPoint jp) {
        MethodSignature signature = (MethodSignature) jp.getSignature();
        Method method = signature.getMethod();
        SignValid signValid = method.getAnnotation(SignValid.class);

        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String token = this.obtainHeader(request, "sign");
        String timestamp = this.obtainHeader(request, "timestamp");
        try {
            Boolean check = this.checkToken(token, timestamp, signValid.value());
            if (!check) {
                // 自定义异常抛出（开发者自行换成自己的即可）
                throw PointBusinessException.error(PointsEnum.METHOD_SIGN_VALID_ERROR);
            }
        } catch (Throwable throwable) {
            // 自定义异常抛出（开发者自行换成自己的即可）
            throw PointBusinessException.error(PointsEnum.METHOD_SIGN_VALID_ERROR);
        }
    }

    private Boolean checkToken(String token, String timestamp, String source) {
        if (StringUtils.isAnyBlank(token, timestamp)) {
            return false;
        }
        long now = System.currentTimeMillis();
        long time = Long.parseLong(timestamp);
        if (now - time > IntegralConstant.MAX_REQUEST) {
            log.error("时间戳已过期: [{}] - [{}] - [{}]", now, time, (now - time));
            throw PointBusinessException.error(PointsEnum.METHOD_SIGN_INVALID_ERROR);
        }
        String crypt = this.getSignature(timestamp, source);
        log.info("SignatureValid: [{} - {}]", crypt, token);
        return StringUtils.equals(crypt, token);
    }

    private String getSignature(String timestamp, String source) {
        KipSignProperties.SignConfig config = kipSignProperties.getByType(source);
        String[] signArr = new String[]{"systemId=" + config.getSystemId(), "systemSecret=" + config.getSystemSecret(), "timestamp=" + timestamp};
        Arrays.sort(signArr);
        String signStr = String.join(DELIMITER, signArr);
        return DigestUtils.md5DigestAsHex(signStr.getBytes());
    }

    private String obtainHeader(HttpServletRequest request, String key) {
        String result =  request.getHeader(key);
        return StringUtils.isBlank(result) ? null : result;
    }

}
