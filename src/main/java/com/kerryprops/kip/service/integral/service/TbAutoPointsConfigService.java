package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/06/2023 16:08
 **********************************************************************************************************************/
public interface TbAutoPointsConfigService extends IService<TbAutoPointsConfig> {

    TbAutoPointsConfig findByLbsId(String lbsId);

    TbAutoPointsConfig saveConfig(TbAutoPointsConfig config);

    TbAutoPointsConfig updateConfig(TbAutoPointsConfig config);

    void removeConfig(Long id);

    TbAutoPointsConfig findById(Long id);

    List<TbAutoPointsConfig> list(String groupId, List<String> projectIds, int page, int size);

    int total(String groupId, List<String> projectIds);

}
