package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbFieldSort;
import com.kerryprops.kip.service.integral.mapper.TbFieldSortMapper;
import com.kerryprops.kip.service.integral.service.TbFieldSortService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 12/04/2024 09:20
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbFieldSortServiceImpl extends ServiceImpl<TbFieldSortMapper, TbFieldSort> implements TbFieldSortService {

    private final TbFieldSortMapper tbFieldSortMapper;

    private static final Long DEFAULT_USER_ID = 0L;

    @Override
    public List<TbFieldSort> findByUserIdAndPageType(Long userId, String pageType) {
        List<TbFieldSort> list = tbFieldSortMapper.selectByUserIdsAndPageType(Arrays.asList(DEFAULT_USER_ID, userId), pageType);
        if (list.stream().anyMatch(item -> Objects.equals(item.getUserId(), userId))) {
            return list.stream().filter(item -> Objects.equals(item.getUserId(), userId)).toList();
        } else {
            return list.stream().filter(item -> Objects.equals(item.getUserId(), DEFAULT_USER_ID)).toList();
        }
    }
}
