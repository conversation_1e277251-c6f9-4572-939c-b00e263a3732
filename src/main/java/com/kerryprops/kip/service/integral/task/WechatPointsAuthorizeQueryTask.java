//package com.kerryprops.kip.service.integral.task;
//
//import com.kerryprops.kip.service.integral.config.MallConfig;
//import com.kerryprops.kip.service.integral.model.dto.MallItem;
//import com.kerryprops.kip.service.integral.service.WechatPaymentAutoPointsCheckService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// * 每日凌晨1点执行该定时job
// * <AUTHOR> Bert
// * Created Date - 09/20/2022 21:40
// **********************************************************************************************************************/
//
//@Slf4j
//@Component
//@RequiredArgsConstructor
//@ConditionalOnProperty(value = "distributed.jobs.enabled", havingValue = "true")
//public class WechatPointsAuthorizeQueryTask {
//
//    private final WechatPaymentAutoPointsCheckService wechatPaymentAutoPointsCheckService;
//    private final MallConfig mallConfig;
//
//    @Scheduled(cron = "0 0 1 * * ?")
//    @SchedulerLock(name = "wechatPointsAuthorizeQueryTask", lockAtMostFor = "PT30M")
//    public void dailyWechatPointsAuthorizeTask() {
//        log.info("WechatPointsAuthorizeQueryTask Start.");
//        for (MallItem mallItem: mallConfig.getList()) {
//            // 查询商场下的微信无感积分授权状态
//            wechatPaymentAutoPointsCheckService.checkMallWechatAutoPointsStatus(mallItem);
//        }
//        log.info("WechatPointsAuthorizeQueryTask End.");
//    }
//
//}
