package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/25/2022 15:18
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberGradeWhetherRelegationQueryDto implements Serializable {

    private String groupId;

    private String vipcode;

    private String startDate;

    private String endDate;

    private List<String> shopIds;

    private Double amount;

    /**
     * 1:按天，2：按月, 3:按年
     */
    private Integer groupBy;

}
