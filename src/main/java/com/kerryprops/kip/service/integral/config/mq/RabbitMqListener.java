package com.kerryprops.kip.service.integral.config.mq;

import com.kerryprops.kip.pmw.client.resource.PointsPaymentConfirmResource;
import com.kerryprops.kip.pmw.client.resource.PointsRefundConfirmResource;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.model.dto.KipUserInvalidDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.HaiDingPosParamResource;
import com.kerryprops.kip.service.integral.webservice.resource.MemberProfileCheckResource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/15/2022 18:08
 **********************************************************************************************************************/

@Slf4j
@Component
@AllArgsConstructor
public class RabbitMqListener {

    private final WechatAccelerateIntegralService wechatAccelerateIntegralService;
    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private final TbMemberRefineService tbMemberRefineService;
    private final TbAutoSaleMemberTagService tbAutoSaleMemberTagService;
    private final TbMemberAssetService tbMemberAssetService;
    private final MemberRegisterService memberRegisterService;
    private final RabbitMqService rabbitMqService;
    private final TbSalesDetailService tbSalesDetailService;
    private final AliPayClientService aliPayClientService;
    private final TbBrandGuideCollectionService tbBrandGuideCollectionService;

    /**
     * 性别，婚姻状态，是否有baby默认值是1
     */
    private static final String DEFAULT_FIELDS = "gender,maritalStatus,babyStatus";
    /**
     * 所属地区
     */
    private static final String USER_REGION = "region";

    /**
     * 微信支付记录实现自动积分功能
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.WECHAT_PAYMENT_SYNC_ROUTE_KEY, containerFactory = "crmContainerFactory")
    public void wechatAutoPointsProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("获取微信推送支付信息实现自动积分消息为空.");
            return;
        }
        log.info("wechatAutoPointsProcess: {}" , msg);
        PointsPaymentConfirmResource resource = JsonUtils.stringToObj(msg, PointsPaymentConfirmResource.class);
        if (Objects.isNull(resource)) {
            return;
        }
        wechatAccelerateIntegralService.payCallback(resource);
    }

    /**
     * 微信无感积分:
     *  退款，更新会员积分数
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.WECHAT_REFUND_SYNC_ROUTE_KEY, containerFactory = "crmContainerFactory")
    public void wechatAutoPointsRefundProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("获取微信推送退款信息为空.");
            return;
        }
        log.info("wechatAutoPointsRefundProcess: {}" , msg);
        PointsRefundConfirmResource resource = JsonUtils.stringToObj(msg, PointsRefundConfirmResource.class);
        if (Objects.isNull(resource)) {
            return;
        }
        try {
            wechatAccelerateIntegralService.refundCallback(resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 微信无感积分:
     *  同步微信支付记录获得多少积分
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.WECHAT_POINTS_SYNC_ROUTE_KEY, containerFactory = "crmContainerFactory")
    public void wechatPointsSyncProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("获取微信推送积分同步信息为空.");
            return;
        }
        log.info("wechatPointsSyncProcess: {}" , msg);
        SalesAutoPointsDto dto = JsonUtils.stringToObj(msg, SalesAutoPointsDto.class);
        if (Objects.isNull(dto)) {
            return;
        }
        // 商圈会员积分信息同步微信
        wechatAccelerateIntegralService.syncWechatPoints(dto);
    }

    /**
     * POS积分抵现补偿:
     *  pos积分抵现补偿
     * @param message 消息体
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.POS_CASH_OUT_ROUTE_KEY, containerFactory = "crmContainerFactory")
    public void posPointsCashOutProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("积分抵现信息为空.");
            return;
        }
        log.info("posPointsCashOutProcess: {}" , msg);
        HaiDingPosParamResource resource = JsonUtils.stringToObj(msg, HaiDingPosParamResource.class);
        if (Objects.isNull(resource)) {
            return;
        }
        TbSalesDetail detail = tbSalesDetailService.selectBySellNoAndMallId(resource.getDocNo(), resource.getMallId());
        if (Objects.isNull(detail)) {
            log.error("海鼎POS积分抵现销售单号: {}未查询到推送的销售记录.", resource.getDocNo());
        }
    }

    /**
     * 完善信息:
     *  必填项是否都已经完成检查
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.XCRM_MEMBER_REFINE_CHECK, containerFactory = "crmContainerFactory")
    public void memberRefineCheckProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("获取完善信息完整性判断信息为空.");
            return;
        }
        log.info("memberRefineCheckProcess: {}", msg);
        MemberProfileCheckResource resource = JsonUtils.stringToObj(msg, MemberProfileCheckResource.class);
        if (Objects.isNull(resource)) {
            return;
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.getAllByGroupIdAndKipUserId(resource.getBrandId(), resource.getKipUserId());
        if (Objects.isNull(tbMemberAsset)) {
            return;
        }
        // 更新会员无逻辑标签
        tbAutoSaleMemberTagService.updateMemberLabels(resource);
        if (StringUtils.equals(tbMemberAsset.getIsCompleted(), "1")) {
            log.info("groupId: {}, kipUserId: {} - Profile信息已完善.", resource.getBrandId(), resource.getKipUserId());
            // 移除缓存
            memberRegisterService.removeCacheMember(tbMemberAsset.getGroupId(), tbMemberAsset.getKipUserId());
            return;
        }
        // 商场完善信息配置项必填项是否已完善判断
        List<TbMemberRefineField> refineFields = tbMemberRefineService.memberRefineRequiredFields(resource.getLbsId());
        if (CollectionUtils.isEmpty(refineFields)) {
            return;
        }
        // TbMember对象转map
        Map<String, Object> fieldMap = JsonUtils.objToMap(tbMemberAsset);
        // 个人信息字段
        List<String> fields = refineFields.stream().filter(it -> 0 == it.getFieldType()).map(TbMemberRefineField::getFieldEname).collect(Collectors.toList());
        boolean isAllCompleted = true;
        for (String field : fields) {
            // 性别，婚姻状态，是否有baby数据库有设置默认值，排除默认值判断
            if (DEFAULT_FIELDS.contains(field)) {
                if (0 == MapUtils.getInteger(fieldMap, field, 0)) {
                    isAllCompleted = false;
                    break;
                }
            // 所属地区特殊判断
            } else if (USER_REGION.equals(field)) {
                if (StringUtils.isAnyBlank(tbMemberAsset.getProvinceAddress(), tbMemberAsset.getCityAddress(), tbMemberAsset.getDistrictAddress())) {
                    isAllCompleted = false;
                    break;
                }
            } else if (Objects.isNull(MapUtils.getObject(fieldMap, field))) {
                isAllCompleted = false;
                break;
            }
        }
        if (isAllCompleted) {
            // 必填标签字段
            fields = refineFields.stream().filter(it -> 1 == it.getFieldType()).map(TbMemberRefineField::getFieldEname).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(fields)) {
                if (StringUtils.isNotBlank(resource.getLabels())) {
                    List<Long> secSortIds = Arrays.stream(resource.getLabels().split(CommonSeparators.WELL_SEPARATOR)).filter(StringUtils::isNotBlank).map(Long::parseLong).distinct().collect(Collectors.toList());
                    // 通过选中的标签反查二级标签id
                    List<TbAutoSaleMemberTag> boundTags = tbAutoSaleMemberTagService.getByIds(secSortIds);
                    // 判断每个配置的必填二级分类标签是否都有值
                    isAllCompleted = fields.stream().allMatch(it -> boundTags.stream().anyMatch(item -> Objects.equals(it, item.getSecSortId()+"")));
                } else {
                    isAllCompleted = false;
                }
            }
        }
        if (!isAllCompleted) {
            log.info("groupId: {}, kipUserId: {}-必填信息未全部完善.", resource.getBrandId(), resource.getKipUserId());
            return;
        }
        tbMemberAsset.setIsCompleted("1");
        // 更新用户完善信息状态
        tbMemberAssetService.updateMemberCompletedStatus(tbMemberAsset);
        // 完善信息后触发场景奖励
        Map<String, Object> map = new HashMap<>(4);
        map.put("memberId", tbMemberAsset.getId());
        map.put("mallId", resource.getLbsId());
        rabbitMqService.sendMessage(RabbitMqConstant.MEMBER_PREFECT_INFO_REWARD, JsonUtils.objToString(map));
    }

    /**
     * 支付宝无感积分:
     *  授权记录
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.ALI_MEMBER_OPEN_CARD_KEY, containerFactory = "crmContainerFactory")
    public void aliMemberOpenCardProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("获取支付宝会员开卡信息为空.");
            return;
        }
        log.info("aliMemberOpenCardProcess: {}" , msg);
        TbInsensatePointsAuthRecord record = JsonUtils.stringToObj(msg, TbInsensatePointsAuthRecord.class);
        if (Objects.isNull(record)) {
            log.error("MQ信息转对象失败-{}", msg);
            return;
        }
        try {
            tbInsensatePointsAuthRecordService.checkAndSaveAliMemberOpenCard(record);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.XCRM_MEMBER_SALE_REFUND, containerFactory = "crmContainerFactory")
    public void memberSaleRefundProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("销售记录发生退款传递参数为空.");
            return;
        }
        log.info("memberSaleRefundProcess: {}" , msg);
        Map<String, Object> toMap = JsonUtils.stringToMap(msg);
        String saleId = MapUtils.getString(toMap, IntegralConstant.SALE_ID);
        if (StringUtils.isBlank(saleId)) {
            log.error("MQ信息转对象失败-{}", msg);
            return;
        }
        try {
            tbSalesDetailService.memberSaleRefundProcess(Long.parseLong(saleId));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 支付宝无感积分:
     *  同步支付宝更新会员卡积分数
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.ALIPAY_POINTS_SYNC_ROUTE_KEY, containerFactory = "crmContainerFactory")
    public void alipayPointsSyncProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("获取支付宝推送积分同步信息为空.");
            return;
        }
        log.info("alipayPointsSyncProcess: {}" , msg);
        SalesAutoPointsDto dto = JsonUtils.stringToObj(msg, SalesAutoPointsDto.class);
        if (Objects.isNull(dto)) {
            return;
        }
        // 商圈会员积分信息同步支付宝
        aliPayClientService.syncAlipayPoints(dto);
    }

    /**
     * KIP账号注销
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.KIP_USER_INVALID_CRM, containerFactory = "crmContainerFactory")
    public void kipUserInvalidProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("KIP用户账号注销消息体为空.");
            return;
        }
        log.info("kipUserInvalidProcess: {}" , msg);
        KipUserInvalidDto dto = JsonUtils.stringToObj(msg, KipUserInvalidDto.class);
        if (Objects.isNull(dto)) {
            return;
        }
        // KIP账号注销，同步CRM移除账号
        if (StringUtils.isNotBlank(dto.getMobile())) {
            tbMemberAssetService.removeMemberByMobile(dto.getMobile());
        } else {
            tbMemberAssetService.removeMemberByKipUserId(dto.getKipUserId());
        }
    }

    /**
     * CRM品牌导览收藏/取消收藏操作
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.CRM_BRAND_GUIDE_COLLECT, containerFactory = "crmContainerFactory")
    public void brandGuideCollectProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("KIP用户账号注销消息体为空.");
            return;
        }
        log.info("brandGuideCollectProcess: {}" , msg);
        TbBrandGuideCollection collection = JsonUtils.stringToObj(msg, TbBrandGuideCollection.class);
        if (Objects.isNull(collection)) {
            return;
        }
        // 取消/收藏品牌导览
        tbBrandGuideCollectionService.saveOrCancelBrandGuide(collection);
    }

}
