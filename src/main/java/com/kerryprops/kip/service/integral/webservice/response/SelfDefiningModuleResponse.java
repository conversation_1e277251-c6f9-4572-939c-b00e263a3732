package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * 自定义页面自定义功能列表
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "自定义页面自定义功能列表")
public class SelfDefiningModuleResponse implements Serializable {

	@Schema( name = "主键id")
	private String id;

	@Schema( name = "模块编号")
	private String moduleSign;

	@Schema( name = "排序")
	private Integer sort;

	@Schema(name = "品牌logo标题")
	private String brandLogoTitle;

	@Schema(name = "品牌logo背景")
	private String brandLogoBg;

	@Schema(name = "是否展示在支付宝小程序，1: 展示 0: 不展示")
	private Integer showAliMini;

	@Schema(name = "多行按钮内容资源列表")
	private List<SelfDefiningContextResource> multipleLineList;

	@Schema(name = "资源列表 功能球")
	private List<SelfDefiningContextResource> imgList;

	@Schema(name = "悬浮球图标资源列表")
	private List<SelfDefiningContextResource> suspensionList;

	@Schema(name = "瀑布流资源列表")
	private List<SelfDefiningWaterFallsResource> waterfallsFlowList;

	@Schema(name = "瀑布流Tab资源列表")
	private List<SelfDefiningWaterFallsResource> waterfallsFlowDTOList;

	private String myAdUrl;

	private Integer floatingBtnShow;

	private String floatingBtnUrl;

	private Integer myAdShow;

	private Integer showLineNum;

}