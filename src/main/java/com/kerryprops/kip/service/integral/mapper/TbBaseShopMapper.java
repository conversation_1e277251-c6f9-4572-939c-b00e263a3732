package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 15:32
 * 已废弃，shop信息从hive获取
 **********************************************************************************************************************/
public interface TbBaseShopMapper extends BaseMapper<TbBaseShop> {

    /**
     * 通过CRM自定义的店铺编号查询店铺信息
     * @param contractNo
     * @return
     */
    TbBaseShop selectByContractNo(String contractNo);

    TbBaseShop getByContractNoAndMallId(String contractNo, String mallId);

    /**
     * 品牌+业态查询店铺编号
     * @param groupId
     * @param formats
     * @return
     */
    List<String> selectByGroupIdAndFormats(String groupId, List<String> formats);

}
