package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 16:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "线上商城配置新增或修改请求类")
public class TbOnlineShopSaveOrUpdateResource implements Serializable {

    @Schema( description = "主键id, 更新时必填")
    private Long id;

    @NotBlank(message = "groupId不能为空")
    @Schema( description = "groupId", required = true)
    private String groupId;

    @NotBlank(message = "mallId不能为空")
    @Schema( description = "mallId", required = true)
    private String mallId;

    @NotBlank(message = "grade不能为空")
    @Schema( description = "grade", required = true)
    private String grade;

    @NotBlank(message = "businessType不能为空")
    @Schema( description = "businessType", required = true)
    private String businessType;

    @NotNull(message = "money不能为空")
    @Schema( description = "money", required = true)
    private BigDecimal money;

    @NotNull(message = "pointNum不能为空")
    @Schema( description = "pointNum", required = true)
    private BigDecimal pointNum;

    @Schema( description = "创建人")
    private String creator;

    @Schema( description = "更新人")
    private String updater;

    @Schema( description = "积分比例是否统一")
    private int isConsistent;

}
