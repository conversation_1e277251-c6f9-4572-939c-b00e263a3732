package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.SelfDefiningPage;
import org.apache.ibatis.annotations.Param;

/**
 * @description 自定义页面列表Mapper
 * <AUTHOR>
 * @date 2022-12-15
 */
public interface SelfDefiningPageMapper extends BaseMapper<SelfDefiningPage> {

    /**
     * 查询自定义配置页面
     * @param groupId
     * @param mallId
     * @param type
     * @return
     */
    SelfDefiningPage findHomePage(@Param("groupId") String groupId, @Param("mallId") String mallId, @Param("type") String type);

    /**
     * 查询自定义配置页面
     * @param groupId
     * @param mallId
     * @param type
     * @return
     */
    Long findPageId(@Param("groupId") String groupId, @Param("mallId") String mallId, @Param("type") String type);

    /**
     * 根据id查询页面
     * @param groupId groupId
     * @param mallId mallId
     * @param id id
     * @return dto
     */
    SelfDefiningPage getPageById(@Param("groupId") String groupId, @Param("mallId") String mallId, @Param("id") String id);
}