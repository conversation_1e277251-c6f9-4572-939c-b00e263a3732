package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description     退款状态枚举类
 * @createDate 2022/9/21
 * @updateDate 2022/9/21
 */
@Getter
public enum RefundStatusEnum {

    /**
     * 支付
     */
    PAY(0),

    /**
     * 退款
     */
    REFUND(1),

    /**
     * 部分退款
     */
    PARTIAL_REFUND(2)
    ;

    private final int value;

    RefundStatusEnum(int value) {
        this.value = value;
    }

}
