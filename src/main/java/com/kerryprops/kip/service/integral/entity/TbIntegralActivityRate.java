package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 12/14/2022 09:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_integral_activity_rate")
public class TbIntegralActivityRate implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("group_id")
    private String groupId;

    @TableField("mall_id")
    private String mallId;

    @TableField("grade_id")
    private String gradeId;

    @TableField("grade_name")
    private String gradeName;

    @TableField("reward_node")
    private String rewardNode;

    @TableField("money")
    private BigDecimal money;

    @TableField("point_num")
    private BigDecimal pointNum;

    @TableField("status")
    private int status;

    @TableField("create_date")
    private Date createDate;

    @TableField("creator")
    private String creator;

    @TableField("update_date")
    private Date updateDate;

    @TableField("updater")
    private String updater;

    private String order;



}
