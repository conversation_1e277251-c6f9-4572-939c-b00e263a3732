package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRuleProgress;
import com.kerryprops.kip.service.integral.model.dto.MemberGradeProgressSwitchDto;
import com.kerryprops.kip.service.integral.webservice.resource.MemberUpgradeAmountResource;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeRuleProgressService
 * @Description TbMemberGradeRuleProgressService
 * @date 2022/9/26 14:50
 * @Version 1.0
 */
public interface TbMemberGradeRuleProgressService extends IService<TbMemberGradeRuleProgress> {
    // 当前卡等
    String CURRENT_CARD_LEVEL = "\\{CurrentCardLevel}";
    // 周期年升级-结束日期
    String UP_CYCLE_YEAR_END_DATE = "\\{UpCycleYearEndDate}";
    // 周期年升级-升级金额
    String UP_CYCLE_YEAR_AMOUNT = "\\{UpCycleYearNeededAmount1}";
    // 自然年升级-结束日期
    String UP_NATURAL_YEAR_END_DATE = "\\{UpNaturalYearEndDate}";
    // 自然年升级-升级金额
    String UP_NATURAL_YEAR_AMOUNT = "\\{UpNaturalYearNeededAmount1}";
    // 自然月升级-结束日期
    String UP_BY_MONTH_END_DATE = "\\{UpByMonthEndDate}";
    // 自然月升级-升级金额
    String UP_BY_MONTH_AMOUNT = "\\{UpByMonthNeededAmount1}";
    // 自然日规则-结束日期
    String UP_BY_DAY_END_DATE = "\\{UpByDayEndDate}";
    // 自然日规则-升级金额
    String UP_BY_DAY_AMOUNT = "\\{UpByDayNeededAmount1}}";

    // 周期年保级-结束日期
    String SUSTAIN_CYCLE_YEAR_END_DATE = "\\{SustainCycleYearEndDate}";
    // 周期年保级-保级金额
    String SUSTAIN_CYCLE_YEAR_AMOUNT = "\\{SustainCycleYearNeededAmount1}";
    // 自然年保级-结束日期
    String SUSTAIN_NATURAL_YEAR_END_DATE = "\\{SustainNaturalYearEndDate}";
    // 自然年保级-保级金额
    String SUSTAIN_NATURAL_YEAR_AMOUNT = "\\{SustainNatureYearNeededAmount1}";
    // 自然月保级-结束日期
    String SUSTAIN_BY_MONTH_END_DATE = "\\{SustainByMonthEndDate}";
    // 自然月保级-保级金额
    String SUSTAIN_CYCLE_MONTH_AMOUNT = "\\{SustainByMonthNeededAmount1}";
    // 自然日保级-结束日期
    String SUSTAIN_BY_DAY_END_DATE = "\\{SustainByDayEndDate}";
    // 自然日保级-保级金额
    String SUSTAIN_CYCLE_DAY1_AMOUNT = "\\{SustainByDayNeededAmount1}";
    String SUSTAIN_CYCLE_DAY2_AMOUNT = "\\{SustainByDayNeededAmount2}";




    String getRelegationCopywriter(LoginUser loginUser);

    /**
     * 根据集团ID+会员等级，查询会员等级进度条展示
     *
     * @param groupId
     * @param grade
     * @return
     */
    TbMemberGradeRuleProgress getByGroupIdAndMemberGrade(String groupId, String grade);

    List<MemberGradeProgressSwitchDto> memberGradeComputeMethod(LoginUser loginUser);

    /**
     * @param loginUser 登陆用户信息，该接口需要当前客户端具有的brandId、phoneNumber等基础信息
     * @param grade     会员等级，对应membergrade表中的等级，与groupId绑定
     * @return 返回对应的升级文案
     */
    String getUpgradeCopywriter(LoginUser loginUser, String grade);

    /**
     * @param loginUser 当前用户
     * @return 用户到下一等级需要的金额
     */
    MemberUpgradeAmountResource getUpgradeMoney(LoginUser loginUser);

    /**
     * 获取会员等级进度条长度
     * @param loginUser
     * @param ruleId
     * @return
     */
    Integer getProgressBar(LoginUser loginUser, String ruleId);
}
