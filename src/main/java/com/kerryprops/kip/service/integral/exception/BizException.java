package com.kerryprops.kip.service.integral.exception;

import com.kerryprops.kip.service.integral.common.current.IError;
import lombok.Data;

import java.util.function.Supplier;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2022 16:57
 **********************************************************************************************************************/

@Data
public class BizException extends RuntimeException  {

    protected final Integer code;
    protected final String msg;

    public BizException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public BizException(IError ie) {
        super(ie.getMsg());
        this.code = ie.getCode();
        this.msg = ie.getMsg();
    }

    public static BizException error(IError iError) {
        return new BizException(iError);
    }

    public static BizException error(Integer code, String msg) {
        return new BizException(code, msg);
    }

    public static Supplier<BizException> bizException(IError iError) {
        return () -> new BizException(iError);
    }

}
