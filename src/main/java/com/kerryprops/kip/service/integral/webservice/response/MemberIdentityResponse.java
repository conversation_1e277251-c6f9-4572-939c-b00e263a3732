package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/19/2023 14:58
 **********************************************************************************************************************/

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "会员身份信息响应类")
public class MemberIdentityResponse implements Serializable {

    @Schema( name = "办公楼用户：true:是 false:否")
    private boolean office;

    @Schema( name = "公寓用户：true:是 false:否")
    private boolean apartment;

    @Schema( name = "小区用户：true:是 false:否")
    private boolean residence;

}
