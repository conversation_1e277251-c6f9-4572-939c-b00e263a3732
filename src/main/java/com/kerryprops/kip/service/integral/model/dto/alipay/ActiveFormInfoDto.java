package com.kerryprops.kip.service.integral.model.dto.alipay;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActiveFormInfoDto {

    /**
     * 性别
     */
    @JSO<PERSON>ield(name = "OPEN_FORM_FIELD_GENDER")
    private String gender;
    /**
     * 手机号
     */
    @JSONField(name = "OPEN_FORM_FIELD_MOBILE")
    private String mobile;

    /**
     * 名字
     */
    @J<PERSON><PERSON>ield(name = "OPEN_FORM_FIELD_NAME")
    private String name;

}
