package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/06/2023 16:01
 **********************************************************************************************************************/
public interface TbAutoPointsConfigMapper extends BaseMapper<TbAutoPointsConfig> {

    /**
     * 通过lbsId查找配置
     * @param lbsId
     * @return
     */
    TbAutoPointsConfig findByLbsId(String lbsId);

    List<TbAutoPointsConfig> listData(@Param("groupId") String groupId, @Param("projectIds") List<String> projectIds, @Param("offset") int offset, @Param("size") int size);

    int total(@Param("groupId") String groupId, @Param("projectIds") List<String> projectIds);

}
