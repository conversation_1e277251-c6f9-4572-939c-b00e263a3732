package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import com.kerryprops.kip.service.integral.client.KerryStaffClient;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.vo.KipMemberVO;
import com.kerryprops.kip.service.integral.service.KerryStaffService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/21/2022 16:40
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class KerryStaffServiceImpl implements KerryStaffService {

    private final KerryStaffClient kerryStaffClient;
    private final ProfileServiceClient profileServiceClient;

    @Override
    public CustomerUserDto findByKipUserId(String kipUserId) {
        if (StringUtils.isBlank(kipUserId)) {
            return null;
        }
        List<CustomerUserDto> list = this.getProfile(Arrays.asList(kipUserId), null);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public CustomerUserDto findByMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return null;
        }
        List<CustomerUserDto> list = this.getProfile(null, Arrays.asList(mobile));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    private List<CustomerUserDto> getProfile(List<String> userIds, List<String> mobiles) {
        if (CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(mobiles)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isNotEmpty(userIds)) {
            userIds = userIds.stream().distinct().collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(mobiles)) {
            mobiles = mobiles.stream().distinct().collect(Collectors.toList());
        }
        return kerryStaffClient.getProfileUsers(CrmProfileQueryDto.builder().userIds(userIds).phones(mobiles).build()).getData();
    }

    @Override
    public CustomerUserDto findByOpenId(String openId) {
        return kerryStaffClient.findByOpenId(openId);
    }

    @Override
    public String modifyProfileInfo(KipMemberVO memberVO) {
        KerryResultDto<String> result = kerryStaffClient.modifyProfileInfo(memberVO);
        if (Objects.equals(result.getCode(), IntegralConstant.DEF_SUCC)) {
            return result.getData();
        }
        return null;
    }

    @Override
    public List<CustomerUserDto> findByKipUserIds(List<String> kipUserIds) {
        return this.getProfile(kipUserIds, null);
    }

    @Override
    public Map<String, CustomerUserDto> getMapByKipUserIds(List<String> kipUserIds) {
        List<CustomerUserDto> dtos = this.getProfile(kipUserIds, null);
        if (CollectionUtils.isEmpty(dtos)) {
            return Maps.newHashMap();
        }
        return dtos.stream().collect(Collectors.toMap(CustomerUserDto::getId, Function.identity(), (k1, k2) -> k2));
    }

    @Override
    public Map<String, CustomerUserDto> getMapByMobiles(List<String> mobiles) {
        List<CustomerUserDto> list = this.getProfile(null, mobiles);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream().collect(Collectors.toMap(CustomerUserDto::getPhoneNumber, Function.identity(), (k1, k2) -> k2));
    }

    @Override
    @Async
    public CompletableFuture<Map<String, CustomerUserDto>> getMapFutureByMobile(List<String> mobiles) {
        List<CustomerUserDto> list = this.getProfile(null, mobiles);
        if (CollectionUtils.isEmpty(list)) {
            return CompletableFuture.completedFuture(MapUtil.empty());
        }
        return CompletableFuture.completedFuture(list.stream().collect(Collectors.toMap(CustomerUserDto::getPhoneNumber, Function.identity(), (k1, k2) -> k2)));
    }

    @Override
    public CustomerThirdPartyDto getWxInfoByMobileAndAppId(String mobile, String appId) {
        return kerryStaffClient.getWxInfoByMobileAndAppId(mobile, appId);
    }

    @Override
    @Cacheable(value = RedisCacheKey.KIP_MOBILE_AREA_CODE_KEY, key = "#areaCode", unless = "#result == null")
    public CountryCodeDto getByAreaCodeDetail(String areaCode) {
        if (StringUtils.isBlank(areaCode)) {
            return null;
        }
        return profileServiceClient.getByAreaCodeDetail(AreaCodeQueryDto.builder().areaCode(areaCode).build());
    }

    @Override
    public List<CrmSysUserDto> getUserList(List<Long> userIds) {
        return kerryStaffClient.getUserList(userIds);
    }

}
