package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MemberRegisterResourceDto;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayAuthDto;
import com.kerryprops.kip.service.integral.webservice.resource.BrandMemberDeleteResource;
import com.kerryprops.kip.service.integral.webservice.resource.KipSyncMemberResource;
import com.kerryprops.kip.service.integral.webservice.resource.MemberSignUpResource;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberResponse;

import java.util.List;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/22/2022 15:13
 **********************************************************************************************************************/
public interface TbMemberAssetService {

    /**
     * 查询用户信息-不走缓存
     * @param groupId
     * @param kipUserId
     * @return
     */
    TbMemberAsset getAllByGroupIdAndKipUserId(String groupId, String kipUserId);

    TbMemberAsset findByOpenIdAndGroupId(String openId, String groupId);

    TbMemberAsset getMemberWithProfileInfo(String kipUserId, String groupId);

    /**
     * 自动积分，通过openid+groupId查询会员信息
     * @param openId
     * @param groupId
     * @return
     */
    TbMemberAsset getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(String openId, String groupId, String mallId);

    /**
     * 获取登录用户信息
     * @param loginUser
     * @return
     */
    TbMemberResponse getLoginMemberInfo(LoginUser loginUser);

    /**
     * @param groupId
     * @param kipUserId
     * @return
     */
    TbMemberAsset findByKipUserIdAndGroupId(String kipUserId, String groupId);

    TbMemberAsset findByKipUserIdAndGroupIdWithException(String kipUserId, String groupId);

    /**
     * @param vipcode
     * @param groupId
     * @return
     */
    TbMemberAsset findByVipcodeAndGroupId(String vipcode, String groupId);

    /**
     * @param vipcode
     * @param groupId
     * @return
     */
    TbMemberAsset findMemberAllInfoByVipcodeAndGroupId(String vipcode, String groupId);

    /**
     * @param mobile
     * @param groupId
     * @return
     */
    TbMemberAsset findByMobileAndGroupId(String mobile, String groupId);

    List<TbMemberAsset> findByVipcodesAndGroupId(List<String> vipcodes, String groupId);

    /**
     * KIP修改手机号，更新tb_member会员信息
     * @param kipUserId
     * @param mobile
     */
    void profileMobileModify(String kipUserId, String mobile);

    /**
     * 通过groupId + vipcode或mobile，vipcode + mobile功能查询会员信息
     * @param queryDto
     * @return
     */
    TbMemberAsset findByDto(SingleMemberQueryDto queryDto);

    /**
     * 通过groupId + vipcode或mobile，vipcode + mobile功能查询会员信息
     * @param queryDto
     * @return
     */
    TbMemberAsset findAllByDto(SingleMemberQueryDto queryDto);

    /**
     * 查询会员，查不到抛出异常
     * @param mobile
     * @param groupId
     * @return
     */
    TbMemberAsset getByMobileAndGroupIdWithException(String mobile, String groupId);

    TbMemberAsset getOrCreateMember(AliPayAuthDto dto) throws Exception;

    /**
     * 第三方注册会员
     * @param resource
     * @return
     */
    TbMemberAsset memberSignUp(MemberSignUpResource resource);

    void updateMemberStatus(String groupId, List<String> vipcodes);

    /**
     * 更新用户是否已完善信息状态
     * @param tbMemberAsset
     */
    void updateMemberCompletedStatus(TbMemberAsset tbMemberAsset);

    TbMemberAsset getMemberById(String id);

    TbMemberAsset getMemberAllInfoById(String id);

    void updateMemberRegisterSource(MemberRegisterResourceDto dto);

    void updateMemberGrade(TbMemberAsset member);

    /**
     * KIP同步会员信息至CRM实现注册
     * @param resource
     */
    void kipSyncMemberCrmProcess(KipSyncMemberResource resource);

    /**
     * KIP注销账号，CRM账号删除
     * @param kipUserId
     */
    void removeMemberByKipUserId(String kipUserId);

    void removeGroupMemberAccount(BrandMemberDeleteResource resource);

    /**
     * KIP注销账号，CRM账号删除
     * @param mobile
     */
    void removeMemberByMobile(String mobile);

    void revertMemberById(String id);

    /**
     * 查询对象
     * @param mobile
     * @param groupId
     * @return dto
     */
    TbMemberAsset queryMemberByGroupIdAndMobile(String groupId, String mobile);

    /**
     * 根据groupId和kipUserId查询对象
     * @param groupId
     * @param kipUserId
     * @return dto
     */
    TbMemberAsset queryMemberByGroupIdAndKipUserId(String groupId, String kipUserId);

    /**
     * 多个kipUserId查询会员信息
     * @param groupId
     * @param kipUserIds
     * @return
     */
    List<TbMemberAsset> queryMemberByGroupIdAndKipUserIds(String groupId, List<String> kipUserIds);

    /**
     * 根据groupId和vipCode查询对象
     * @param groupId
     * @param vipCode
     * @return dto
     */
    TbMemberAsset queryMemberByGroupIdAndVipCode(String groupId, String vipCode);

    /**
     * 修改积分
     * @param tbMemberAsset
     * @return int
     */
    int updatePoints(TbMemberAsset tbMemberAsset);

    /**
     * 计算时间内对应数量
     * @param mallId
     * @param groupId
     * @param startTime
     * @param endTime
     * @param miniProgram
     * @return int
     */
    int getMemberCountBetweenDate(String groupId, String mallId, String startTime, String endTime, String miniProgram);

    /**
     * 更新会员信息，用于补齐缺失kipUserId的会员信息
     * @param tbMemberAsset
     * @return
     */
    int updateById(TbMemberAsset tbMemberAsset);
}
