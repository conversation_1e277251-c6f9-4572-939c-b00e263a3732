package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * tb_member
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_asset")
@ToString
public class TbMemberAsset implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * crm主键id
     */
    @TableField("crm_id")
    private String crmId;

    /**
     * 集团ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场编号---KO group叫homesite
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 授权商场id
     */
    @TableField("authorized_mall_id")
    private String authorizedMallId;

    /**
     * 授权时间
     */
    @TableField("authorized_date")
    private Date authorizedDate;

    /**
     * 手机区号
     */
    private transient String areaCode;

    /**
     * 手机号码
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 会员编号（规则生成）
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * 当前积分
     */
    @TableField("current_points")
    private int currentPoints;

    /**
     * 当前会员卡等[编码]
     */
    @TableField("grade")
    private String grade;

    /**
     * 会员状态（0:冻结；1:非冻结)
     */
    @TableField("status")
    private String status;

    /**
     * ,微信激活商场
     */
    @TableField("wx_open_market")
    private String wxOpenMarket;

    /**
     * 加入日期
     */
    @TableField("join_time")
    private Date joinTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否完善信息（0:未完善；1:完善)
     */
    @TableField("is_completed")
    private String isCompleted;

    /**
     * kip对应的user_id
     */
    @TableField("kip_user_id")
    private String kipUserId;

    /**
     * 是否黑名单(0:是 1:不是),供活动使用
     */
    @TableField("whether_blacklist")
    private Integer whetherBlacklist;

    /**
     * 注册来源
     */
    @TableField("register_source")
    private String registerSource;

    /**
     * 注册来源-附注信息(例如：活动，电子券)
     */
    @TableField("register_source_label")
    private String registerSourceLabel;

    /**
     * 来源名称
     */
    @TableField("register_source_remark")
    private String registerSourceRemark;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    private transient String nickName;

    private transient String realName;

    private transient String avatar;

    private transient String email;

    @TableField("sex")
    private transient Integer gender;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private transient Date birthDate;

    private transient Integer certificateType;

    private transient String certificateNum;

    private transient Integer maritalStatus;

    private transient Integer babyStatus;

    private transient String provinceAddress;

    private transient String cityAddress;

    private transient String districtAddress;

    private transient String address;

    private transient String companyName;

    private transient String job;

    private transient String nation;

    private transient String homePhone;

    private transient Integer education;

    private transient Integer profession;

    private transient Integer amountOfPoints;

    /**
     * 积分发生变动的积分值，更新时用户乐观锁判断
     */
    private transient Integer adjustIntegralNum;

    public void fillOtherInfo(CustomerUserDto userDto) {
        if (Objects.isNull(userDto)) {
            return;
        }
        // 手机所属区号
        this.areaCode = userDto.getAreaCode();
        this.mobile = userDto.getPhoneNumber();
        this.nickName = userDto.getNickName();
        this.realName = userDto.getRealName();
        this.kipUserId = userDto.getId();
        this.avatar = userDto.getAvatar();
        this.email = userDto.getEmail();
        this.gender = userDto.getGender();
        this.birthDate = userDto.getBirthDate();
        this.certificateType = userDto.getCertificateType();
        this.certificateNum = userDto.getCertificateNum();
        this.maritalStatus = userDto.getMaritalStatus();
        this.babyStatus = userDto.getBabyStatus();
        this.provinceAddress = userDto.getProvinceAddress();
        this.cityAddress = userDto.getCityAddress();
        this.districtAddress = userDto.getDistrictAddress();
        this.address = userDto.getAddress();
        this.companyName = userDto.getCompanyName();
        this.job = userDto.getJob();
        this.nation = userDto.getNation();
        this.homePhone = userDto.getHomePhone();
        this.education = userDto.getEducation();
        this.profession = userDto.getProfession();
    }

}