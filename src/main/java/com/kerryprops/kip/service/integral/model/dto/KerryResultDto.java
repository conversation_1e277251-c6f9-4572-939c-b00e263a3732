package com.kerryprops.kip.service.integral.model.dto;

import com.kerryprops.kip.service.integral.common.IntegralConstant;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description
 * @createDate 2022/10/26
 * @updateDate 2022/10/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema( name = "返回数据类")
public class KerryResultDto<T> implements Serializable {

    /** 错误码. */
    @Schema( name = "响应码")
    private String code;

    /** 提示信息. */
    @Schema( name = "响应信息")
    private String message;

    /** 具体的内容. */
    @Schema( name = "响应体")
    private T data;

    public static KerryResultDto ok() {
        KerryResultDto result = new KerryResultDto();
        result.setCode(IntegralConstant.DEF_SUCC);
        result.setMessage("send success.");
        return result;
    }

    public static KerryResultDto error(Integer code, String msg) {
        KerryResultDto result = new KerryResultDto();
        result.setCode(IntegralConstant.DEF_SUCC);
        result.setMessage("send success.");
        return result;
    }
}
