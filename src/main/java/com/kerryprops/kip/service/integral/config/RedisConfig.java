package com.kerryprops.kip.service.integral.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.google.common.collect.Maps;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.scripting.support.ResourceScriptSource;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Map;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 14:30
 **********************************************************************************************************************/

@EnableCaching
@Configuration
public class RedisConfig {

    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory, ObjectMapper objectMapper) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);

        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(objectMapper, Object.class);
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        //key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }

    /**
     * 从这往下是配置@cacheable失效时间
     */
    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        return new RedisCacheManager(
                RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory),
                // 默认策略，未配置的 key 会使用这个
                this.getRedisCacheConfigurationWithTtl(60 * 60),
                // 指定 key 策略
                this.getRedisCacheConfigurationMap()
        );
    }

    private Map<String, RedisCacheConfiguration> getRedisCacheConfigurationMap() {
        Map<String, RedisCacheConfiguration> redisCacheConfigurationMap = Maps.newHashMap();

        // 会员信息缓存key
        redisCacheConfigurationMap.put(RedisCacheKey.INDEX_CRM_MEMBER_BY_KIP_USERID, this.getRedisCacheConfigurationWithTtl(60 * 60 * 4));
        // 会员groupId+vipcode对应的kipUserId缓存key(8小时)
        redisCacheConfigurationMap.put(RedisCacheKey.INDEX_GROUP_VIPCODE_TO_KIP_USERID, this.getRedisCacheConfigurationWithTtl(60 * 60 * 8));
        // 会员vipcode缓存key
        redisCacheConfigurationMap.put(RedisCacheKey.INDEX_CRM_VIPCODE_CACHE_KEY, this.getRedisCacheConfigurationWithTtl(60 * 60 * 24));
        // 完善信息配置缓存key
        redisCacheConfigurationMap.put(RedisCacheKey.INDEX_CRM_REFINE_CONFIG_CACHE, this.getRedisCacheConfigurationWithTtl(5 * 60));
        // 首页-自定义UI缓存key
        redisCacheConfigurationMap.put(RedisCacheKey.POINTS_HOME_PAGE_CUSTOMIZE_UI, this.getRedisCacheConfigurationWithTtl(30 * 60));
        // 我的-自定义UI缓存key
        redisCacheConfigurationMap.put(RedisCacheKey.POINTS_MY_PAGE_CUSTOMIZE_UI, this.getRedisCacheConfigurationWithTtl(60 * 60));
        // 首页展示在我的页面的模块-缓存key
        redisCacheConfigurationMap.put(RedisCacheKey.POINTS_HOME_PAGE_DISPLAY_MY_PAGE, this.getRedisCacheConfigurationWithTtl(60 * 60));
        // 会员等级保级文案缓存key
        redisCacheConfigurationMap.put(RedisCacheKey.MEMBER_UPGRADE_NEXT_GRADE_NEED_MONEY, this.getRedisCacheConfigurationWithTtl(60 * 60));
        // 会员成长进度条key
        redisCacheConfigurationMap.put(RedisCacheKey.MEMBER_UPGRADE_PROGRESS_BAR_KEY, this.getRedisCacheConfigurationWithTtl(60 * 60));
        // 自助积分页面配置缓存key
        redisCacheConfigurationMap.put(RedisCacheKey.CRM_AUTO_POINT_ENTRY_KEY, this.getRedisCacheConfigurationWithTtl(60 * 60 * 8));
        // 集团会员等级列表缓存KEY
        redisCacheConfigurationMap.put(RedisCacheKey.CRM_MEMBER_GRADE_KEY, this.getRedisCacheConfigurationWithTtl(60 * 60));
        // 用户无感积分授权状态缓存key
        redisCacheConfigurationMap.put(RedisCacheKey.MEMBER_WX_OR_ALI_AUTO_POINTS_AUTH_RECORD, this.getRedisCacheConfigurationWithTtl(60 * 60 * 8));
        // 会员消费金额统计开始时间
        redisCacheConfigurationMap.put(RedisCacheKey.MEMBER_AMOUNT_CAL_START_DATE, this.getRedisCacheConfigurationWithTtl(60 * 60));
        // Hive查询LBS信息缓存key（解决调用hive-vas太频繁的问题）
        redisCacheConfigurationMap.put(RedisCacheKey.HIVE_LBS_CACHE_KEY, this.getRedisCacheConfigurationWithTtl(60 * 60));
        // 会员升降级有效配置
        redisCacheConfigurationMap.put(RedisCacheKey.KIP_MEMBER_GRADE_EFFECTIVE_CONFIG_KEY, this.getRedisCacheConfigurationWithTtl(60 * 60));
        // 手机区号信息缓存
        redisCacheConfigurationMap.put(RedisCacheKey.KIP_MOBILE_AREA_CODE_KEY, this.getRedisCacheConfigurationWithTtl(60 * 60 * 24));

        // Tick 4.7.0会员升级文案缓存key- 20秒
        redisCacheConfigurationMap.put(RedisCacheKey.MEMBER_UPGRADE_COPY_WRITER_KEY, this.getRedisCacheConfigurationWithTtl(20));
        // Tick 4.7.0会员保级文案缓存key- 20秒
        redisCacheConfigurationMap.put(RedisCacheKey.MEMBER_RELEGATION_COPY_WRITER_KEY, this.getRedisCacheConfigurationWithTtl(20));
        return redisCacheConfigurationMap;
    }

    private RedisCacheConfiguration getRedisCacheConfigurationWithTtl(Integer seconds) {
        ObjectMapper objectMapper = new Jackson2ObjectMapperBuilder()
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .serializerByType(LocalDateTime.class, new LocalDateTimeSerializer(DefaultMvcConfiguration.CUSTOM_ISO_LOCAL_DATE_TIME))
                .deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(DefaultMvcConfiguration.CUSTOM_ISO_LOCAL_DATE_TIME))
                .serializerByType(LocalDate.class, new LocalDateSerializer(ISO_LOCAL_DATE))
                .deserializerByType(LocalDate.class, new LocalDateDeserializer(ISO_LOCAL_DATE))
                .serializerByType(LocalTime.class, new LocalTimeSerializer(DefaultMvcConfiguration.CUSTOM_ISO_LOCAL_TIME))
                .deserializerByType(LocalTime.class, new LocalTimeDeserializer(DefaultMvcConfiguration.CUSTOM_ISO_LOCAL_TIME))
                .build();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(objectMapper, Object.class);
        RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig();
        redisCacheConfiguration = redisCacheConfiguration.serializeValuesWith(
                RedisSerializationContext.SerializationPair
                        .fromSerializer(jackson2JsonRedisSerializer)
        ).entryTtl(Duration.ofSeconds(seconds));

        return redisCacheConfiguration;
    }

    @Bean
    public DefaultRedisScript<Long> checkAndDelScript() {
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("lua/check_and_del.lua")));
        redisScript.setResultType(Long.class);
        return redisScript;
    }

    @Bean
    public DefaultRedisScript<Long> accessLimitScript() {
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("lua/access_limit.lua")));
        redisScript.setResultType(Long.class);
        return redisScript;
    }

}
