package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_grade_change_between_sales")
public class TbGradeChangeBetweenSales implements Serializable {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("change_id")
    private String changeId;

    @TableField("sales_start_time")
    private Date salesStartTime;

    @TableField("sales_end_time")
    private Date salesEndTime;

    @TableField("create_date")
    private Date createDate;

}
