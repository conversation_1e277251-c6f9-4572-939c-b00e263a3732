package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsAuthRecordService;
import com.kerryprops.kip.service.integral.service.WechatAccelerateIntegralService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberAutoPointResource;
import com.kerryprops.kip.service.integral.webservice.resource.QueryPointsCommitStatusResource;
import com.kerryprops.kip.service.integral.webservice.response.CheckMemberWxAuthorizeOldStatusResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Objects;


/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/14/2022 16:16
 **********************************************************************************************************************/

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/auto/points")
@Tag(name = "自助积分API")
public class AutoPointsController {

    private final TbInsensatePointsAuthRecordService insensatePointsAuthRecordService;
    private final WechatAccelerateIntegralService wechatAccelerateIntegralService;
    private final RedisService redisService;
    private final MallConfig mallConfig;

    @GetMapping("/config")
    @Operation(summary="查询用户自助积分页面的配置信息", method = "GET")
    public MemberAutoPointResource getMemberPointsAuth(@CurrentUser LoginUser loginUser,
                                                       @RequestHeader("lbsId") String lbsId,
                                                       @RequestHeader(value = "provider", required = false) String provider) {
        return insensatePointsAuthRecordService.getAutoPointInfo(loginUser, provider, lbsId);
    }

    @GetMapping("/check_hkc_old_authorize_status")
    @Operation(summary="检查HKC老用户微信无感积分授权状态", method = "GET")
    public CheckMemberWxAuthorizeOldStatusResponse checkMemberWxAuthorizeOldStatus(@CurrentUser LoginUser loginUser,
                                                                                   @RequestHeader("lbsId") String lbsId,
                                                                                   @RequestHeader("brandId") String groupId) {
        // 非HKC商圈不检查
        if (!mallConfig.isHkc(groupId)) {
            return CheckMemberWxAuthorizeOldStatusResponse.builder().redirectWxAuthPage(false).build();
        }
        MallItem mallItem = mallConfig.getByMallId(lbsId);
        String value = redisService.getValue(RedisCacheKey.HKC_OLD_MEMBER_WX_PAY_AUDIT_SWITCH);
        TbInsensatePointsAuthRecord authRecord = insensatePointsAuthRecordService.checkHkcMemberAuthStatus(loginUser.getCId(),
                lbsId, InsensateOriginEnum.WECHAT.getValue());
        if (Objects.isNull(authRecord)) {
            return CheckMemberWxAuthorizeOldStatusResponse.builder().redirectWxAuthPage(StringUtils.equals("1", value))
                    .mchId(mallItem.getWxMchId()).openId(loginUser.getOpenId()).build();
        }
        if (authRecord.getCreateDate().after(IntegralConstant.hkcMigrationToKoDate) || !(StringUtils.equals("1", value))) {
            return CheckMemberWxAuthorizeOldStatusResponse.builder().redirectWxAuthPage(false).build();
        } else {
            return CheckMemberWxAuthorizeOldStatusResponse.builder().redirectWxAuthPage(true)
                    .mchId(mallItem.getWxMchId()).openId(loginUser.getOpenId()).build();
        }
    }

    @GetMapping("/commit-status")
    @Operation(summary="查询商圈会员待积分状态", method = "GET")
    public QueryPointsCommitStatusResource getWxPointsCommitStatus(@CurrentUser LoginUser loginUser) {
        return wechatAccelerateIntegralService.queryMemberPointsCommitStatus(loginUser);
    }

    @Hidden
    @GetMapping("/complete-alipay-auth-info")
    public void checkAliAuthPoints(@RequestParam("mobile") String mobile, @RequestParam("mallId") String mallId) {
        insensatePointsAuthRecordService.saveAuthRecord(mobile, mallId);
    }
}
