package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/19/2022 21:38
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaleDetailQueryDto implements Serializable {

    private String groupId;

    private String mallId;

    private String saleNo;

    private String shopNo;

    private String startTime;

    private String endTime;

    private String vipcode;

    private int count;

    private Boolean saleTimeIsBetweenQueryTime;

}
