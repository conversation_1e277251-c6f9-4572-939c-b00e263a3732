package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbFieldSort;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 12/04/2024 09:12
 **********************************************************************************************************************/
public interface TbFieldSortMapper extends BaseMapper<TbFieldSort> {

    List<TbFieldSort> selectByUserIdsAndPageType(@Param("userIds") List<Long> userIds, @Param("pageType") String pageType);

}
