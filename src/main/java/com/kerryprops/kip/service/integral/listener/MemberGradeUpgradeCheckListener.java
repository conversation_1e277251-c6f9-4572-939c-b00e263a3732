package com.kerryprops.kip.service.integral.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.RuleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.common.utils.KipDateUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.model.dto.MemberSaleActivityDto;
import com.kerryprops.kip.service.integral.model.dto.MemberYearAmountCalDto;
import com.kerryprops.kip.service.integral.model.dto.PreviousDailyConsumptionRuleDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * @DESC: 会员产生销售后，MQ监听判断会员是否需要升级
 * Created Date - 01/16/2023 17:32
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class MemberGradeUpgradeCheckListener {

    private final TbMemberAssetService tbMemberAssetService;
    private final TbMemberGradeService tbMemberGradeService;
    private final TbSalesDetailService tbSalesDetailService;
    private final TbMemberGradeRuleService tbMemberGradeRuleService;
    private final TbBaseShopService tbBaseShopService;
    private final TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    private final RabbitMqService rabbitMqService;
    private final RedisService redisService;
    private final MemberGradeStrategyService memberGradeStrategyService;
    private final TbGradeChangeBetweenSalesService tbGradeChangeBetweenSalesService;


    /**
     * 通过销售id查询不到销售记录，默认重试5次
     */
    private static final int DEFAULT_COUNT = 5;

    /**
     * 根据会员销售判断会员是否满足升级逻辑判断
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.MEMBER_SALES_TRIGGER_UPGRADE_CHECK}, containerFactory = "crmContainerFactory")
    public void memberGradeUpgradeCheckAfterSales(Message message) {
        String msg = new String(message.getBody());
        log.info("MemberGradeUpgradeCheckListener: {}" , msg);
        MemberSaleActivityDto activityDto = JsonUtils.stringToObj(msg, MemberSaleActivityDto.class);
        if (Objects.isNull(activityDto) || StringUtils.isBlank(activityDto.getSaleId())) {
            log.info("销售记录数据转化为对象失败: {}", msg);
            return;
        }
        // 是否是退款反向流程
        if (activityDto.isRefund()) {
            log.info("该销售是退款流程，会员升级逻辑则不判断: [{}]", msg);
            return;
        }
        // 销售记录id
        String saleId = activityDto.getSaleId();
        // 销售来源
        String fromType = activityDto.getFromType();
        // 查询会员销售记录
        TbSalesDetail saleEntity = tbSalesDetailService.getMemberSaleById(Long.parseLong(saleId));
        int retryCount = 0;
        while (Objects.isNull(saleEntity) && retryCount < DEFAULT_COUNT) {
            saleEntity = tbSalesDetailService.getMemberSaleById(Long.parseLong(saleId));
            retryCount += 1;
            log.info("RetryCount-{}-{}", saleId, retryCount);
        }
        if (Objects.isNull(saleEntity)) {
            log.info("销售记录ID对应的记录不存在: {}", saleId);
            return;
        }
        // 销售日期结束时间
        Date currentDate = DateUtil.endOfDay(new Date());
        // 销售时间
        Date saleDate = saleEntity.getSaleDate();
        // 销售时间转yyyy-MM-dd
        String saleDateStr = DateUtil.formatDate(saleDate);
        // groupId添加的会员等级列表
        List<TbMemberGrade> gradeList = tbMemberGradeService.queryGradeSortDescWithoutCacheByGroupId(saleEntity.getGroupId());
        if (CollectionUtils.isEmpty(gradeList)) {
            log.info("该groupId: {}下未查询到会员等级信息", saleEntity.getGroupId());
            return;
        }
        // 查询会员信息
        TbMemberAsset member = tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(saleEntity.getVipcode(), saleEntity.getGroupId());
        if (Objects.isNull(member)) {
            log.info("会员: {}不存在！", saleEntity.getVipcode());
            return;
        }
        // 当前会员对应的等级信息
        TbMemberGrade currentGrade = gradeList.stream().filter(it -> StringUtils.equals(it.getCode(), member.getGrade())).findFirst().orElse(null);
        // 尊享卡会员不参与升降级
        if (Objects.nonNull(currentGrade) && currentGrade.getUpGradationStatus() == 0) {
            log.error("该会员是尊享卡，不参与升降级", member.getVipcode());
            return;
        }
        // 非尊享卡，会员只能升级到参与升降的会员等级
        if (Objects.nonNull(currentGrade)) {
            gradeList = gradeList.stream()
                    .filter(it -> (it.getSort() > currentGrade.getSort()))
                    .filter(it -> Objects.equals(it.getUpGradationStatus(), 1)).toList();
        }
        if (CollectionUtils.isEmpty(gradeList)) {
            log.info("当前会员等级: {}-{}，已是最高等级，不需要升级", member.getVipcode(), member.getGrade());
            return;
        }
        log.info("比当前会员等级高的等级数量: {}-{}", member.getGrade(), gradeList.size());
        // 开始时间-根据是否是周期年判断
        MemberYearAmountCalDto calDto = memberGradeStrategyService.getCalculateYearAmountBeginDate(saleEntity.getGroupId(), saleEntity.getVipcode());
        Date startDate = calDto.getBeginDate();
        if (startDate.getTime() > saleDate.getTime()) {
            log.info("会员订单消费时间超出会员金额统计开始时间，会员消费时间【{}】消费金额统计开始时间【{}】", DateUtil.formatDateTime(saleDate), DateUtil.formatDateTime(startDate));
            return;
        }
        TbBaseShop baseShop = tbBaseShopService.getByContractNoAndMallId(saleEntity.getShopNo(), saleEntity.getMallId());
        if (Objects.isNull(baseShop)) {
            log.info("该商场: [{}-{}]下未查询店铺信息", saleEntity.getMallId(), saleEntity.getShopNo());
            return;
        }
        TbMemberGrade matchedMemberGrade = null;
        String remark = null;
        Integer changeType = null;
        Date beginDate = null, endDate = null;
        for (TbMemberGrade grade: gradeList) {
            // 查询会员等级规则信息
            List<TbMemberGradeRule> gradeRules = tbMemberGradeRuleService.queryByGroupIdAndGradeAndType(saleEntity.getGroupId(), grade.getCode(), 1);
            if (CollectionUtils.isEmpty(gradeRules)) {
                log.info("该会员等级: [{}]下未查询到会员升级规则信息", grade.getCode());
                continue;
            }
            gradeRules = this.parseRules(gradeRules);
            List<String> shopCodes = null;
            for (TbMemberGradeRule rule: gradeRules) {
                // 设置的身份认证升级规则
                if (RuleTypeEnum.MEMBER_IDENTITY_UPGRADE.getValue().equals(rule.getRuleType())) {
                    log.info("升级内设置的身份认证规则, {}-{}", grade.getCode(), rule.getRuleType());
                    continue;
                }
                if (Objects.isNull(rule.getMoney()) || rule.getMoney().compareTo(BigDecimal.ZERO) < 0) {
                    log.info("该会员等级: [{}]下规则类型: [{}]设置的金额不正确", grade.getCode(), rule.getRuleType());
                    continue;
                }
                // 查询对应业态下的店铺编号
                shopCodes = this.getShopNosByGroupIdAndFormats(saleEntity.getGroupId(), rule.getFormats());
                // 保存升级备注信息
                String formatsDesc = StringUtils.isNotBlank(rule.getFormatsDesc()) ? "在" + rule.getFormatsDesc() + "中" : "";
                // 每日消费
                if (RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                    beginDate = startDate;
                    // Tick 4.4.0添加每日消费金额发生变更后，从变更时间开始计算金额
                    if (Objects.nonNull(rule.getEffectiveDate()) && rule.getEffectiveDate().after(beginDate)) {
                        beginDate = DateUtil.beginOfDay(rule.getEffectiveDate());
                    }
                    // Tick 4.7.0等级升级统计销售的开始时间与当前时间是否是同一天
                    if (StringUtils.equals(DateUtil.formatDate(calDto.getBeginDate()), DateUtil.formatDate(beginDate))) {
                        beginDate = calDto.getBeginDate();
                    }
                    endDate = currentDate;
                    changeType = 2;
                    remark = "每日消费金额满足升级标准：" + (StringUtils.isNotBlank(formatsDesc) ? formatsDesc : "") + "消费" + rule.getMoney() + "元";
                // 年消费升级规则
                } else if (RuleTypeEnum.ACCUMULATIVE_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                    beginDate = startDate;
                    endDate = currentDate;
                    changeType = 3;
                    remark = "累计消费金额满足升级标准：" + ((2 == rule.getSumType()) ? "周期年内" : "自然年内") + (StringUtils.isNotBlank(formatsDesc) ? formatsDesc : "") + "消费" + rule.getMoney() + "元";
                // 月累计升级规则
                } else if (RuleTypeEnum.MONTHLY_ACCUMULATIVE_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                    beginDate = startDate;
                    endDate = currentDate;
                    changeType = 10;
                    remark = "月累计消费金额满足升级标准：" + (StringUtils.isNotBlank(formatsDesc) ? formatsDesc : "") + "消费" + rule.getMoney() + "元";
                // 入会当日升级规则
                } else if (RuleTypeEnum.JOIN_CONSUME_UPGRADE.getValue().equals(rule.getRuleType()) && StringUtils.equals(saleDateStr, DateUtil.formatDate(member.getJoinTime()))) {
                    beginDate = DateUtil.beginOfDay(saleDate);
                    endDate = DateUtil.endOfDay(saleDate);
                    changeType = 5;
                    remark = "入会当日消费满足升级标准：" + (StringUtils.isNotBlank(formatsDesc) ? formatsDesc : "") + "消费" + rule.getMoney() + "元";
                } else {
                    continue;
                }
                // 计算销售金额是否能升级
                boolean succeeded = tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(member, rule, beginDate, endDate, shopCodes);
                if (succeeded) {
                    // 是入会当天的升级规则
                    if (3 == rule.getRuleType()) {
                        if (this.checkJoinDayParallelConditions(rule.getJoinDayParallelRules(), member, beginDate, endDate)) {
                            matchedMemberGrade = grade;
                            break;
                        } else {
                            log.info("会员销售满足入会当日规则，不满足入会当日并行条件: {}-{}-{}", saleEntity.getGroupId(), saleEntity.getVipcode(), rule.getFormatsDesc());
                        }
                    } else {
                        matchedMemberGrade = grade;
                        break;
                    }
                } else {
                    // 每日消费升级历史记录检查
                    if (RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue().equals(rule.getRuleType()) && StringUtils.isNotBlank(rule.getCertification())) {
                        PreviousDailyConsumptionRuleDto ruleDto = JsonUtils.stringToObj(rule.getCertification(), PreviousDailyConsumptionRuleDto.class);
                        // 上一次每日消费升级规则判断，只有在上一次规则生效时间和当前规则设置的生效时间之内的，需要重新检查
                        if (Objects.nonNull(ruleDto) && Objects.nonNull(ruleDto.getEffectiveDate()) && ruleDto.getEffectiveDate().before(rule.getEffectiveDate()) &&
                                saleDate.after(ruleDto.getEffectiveDate()) && saleDate.before(rule.getEffectiveDate())) {
                            // 查询对应业态下的店铺编号
                            shopCodes = this.getShopNosByGroupIdAndFormats(saleEntity.getGroupId(), ruleDto.getFormats());
                            if (CollectionUtils.isNotEmpty(shopCodes)) {
                                succeeded = tbSalesDetailService.checkPreviousDailyConsumptionRuleWhetherRelegationSucceeded(member, ruleDto, DateUtil.beginOfDay(ruleDto.getEffectiveDate()),
                                        DateUtil.endOfDay(DateUtil.offsetDay(rule.getEffectiveDate(), -1)), shopCodes);
                                // 前一次设置的每日消费升级金额
                                if (succeeded) {
                                    matchedMemberGrade = grade;
                                    remark = "每日消费升级-上一次设置规则满足升级标准：" + "消费" + ruleDto.getMoney() + "元，规则生效日期为：" + DateUtil.formatDateTime(ruleDto.getEffectiveDate());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if (Objects.nonNull(matchedMemberGrade)) {
                break;
            }
        }
        if (Objects.isNull(matchedMemberGrade)) {
            log.info("会员消费金额未满足等级升级规则，不做升级处理，{}-{}-{}", saleEntity.getGroupId(), saleEntity.getVipcode(), Objects.nonNull(saleEntity.getPayAmount()) ? saleEntity.getPayAmount() : "0");
            return;
        }
        log.info("会员满足: {}-{}，执行升级逻辑: {}-{}", member.getVipcode(), remark, member.getGrade(), matchedMemberGrade.getCode());
        if (StringUtils.isNotBlank(saleEntity.getMallId())) {
            member.setMallId(saleEntity.getMallId());
        }
        // TICK 4.4(SCRM-8022:添加IPAD/Admin端会员等级变更通知消息)
        if (StringUtils.isNotBlank(fromType) && IntegralConstant.IPAD_S_SALES.stream().anyMatch(item -> StringUtils.equalsIgnoreCase(item, fromType))) {
            String notifyKey = String.format(RedisCacheKey.MEMBER_GRADE_CHANGE_NOTIFY_KEY, member.getGroupId(), member.getMobile());
            redisService.setVal(notifyKey, matchedMemberGrade.getName(), 5L, TimeUnit.MINUTES);
        }
        this.executeMemberUpgrade(member, matchedMemberGrade, remark, changeType, saleEntity.getCreateDate(), calDto);
    }

    /**
     * 集团卡等变更触发会员升级判断
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.GROUP_MEMBER_GRADE_CHANGE_TRIGGER_UPGRADE}, containerFactory = "crmContainerFactory")
    public void groupGradeChangeTriggerUpgrade(Message message) {
        String msg = new String(message.getBody());
        log.info("groupGradeChangeTriggerUpgrade: {}" , msg);
        MemberSaleActivityDto activityDto = JsonUtils.stringToObj(msg, MemberSaleActivityDto.class);
        if (Objects.isNull(activityDto) || StringUtils.isBlank(activityDto.getSaleId())) {
            log.info("销售记录数据转化为对象失败: {}", msg);
            return;
        }
        // 是否是退款反向流程
        if (activityDto.isRefund()) {
            log.info("该销售是退款流程，会员升级逻辑则不判断: [{}]", msg);
            return;
        }
        // 销售记录id
        String saleId = activityDto.getSaleId();
        // 查询会员销售记录
        TbSalesDetail saleEntity = tbSalesDetailService.getMemberSaleById(Long.parseLong(saleId));
        int retryCount = 0;
        while (Objects.isNull(saleEntity) && retryCount < DEFAULT_COUNT) {
            saleEntity = tbSalesDetailService.getMemberSaleById(Long.parseLong(saleId));
            retryCount += 1;
            log.info("RetryCount-{}-{}", saleId, retryCount);
        }
        if (Objects.isNull(saleEntity)) {
            log.info("销售记录ID对应的记录不存在: {}", saleId);
            return;
        }
        // 销售日期结束时间
        Date currentDate = DateUtil.endOfDay(new Date());
        // 销售时间
        Date saleDate = saleEntity.getSaleDate();
        // 销售时间转yyyy-MM-dd
        String saleDateStr = DateUtil.formatDate(saleDate);
        // groupId添加的会员等级列表
        List<TbMemberGrade> gradeList = tbMemberGradeService.queryGradeSortDescWithoutCacheByGroupId(saleEntity.getGroupId());
        if (CollectionUtils.isEmpty(gradeList)) {
            log.info("该groupId: {}下未查询到会员等级信息", saleEntity.getGroupId());
            return;
        }
        // 查询会员信息
        TbMemberAsset member = tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(saleEntity.getVipcode(), saleEntity.getGroupId());
        if (Objects.isNull(member)) {
            log.info("会员: {}不存在！", saleEntity.getVipcode());
            return;
        }
        // 当前会员对应的等级信息
        TbMemberGrade currentGrade = gradeList.stream().filter(it -> StringUtils.equals(it.getCode(), member.getGrade())).findFirst().orElse(null);
        // 尊享卡会员不参与升降级
        if (Objects.nonNull(currentGrade) && currentGrade.getUpGradationStatus() == 0) {
            log.error("该会员是尊享卡，不参与升降级", member.getVipcode());
            return;
        }
        // 非尊享卡，会员只能升级到参与升降的会员等级
        if (Objects.nonNull(currentGrade)) {
            gradeList = gradeList.stream()
                    .filter(it -> (it.getSort() > currentGrade.getSort()))
                    .filter(it -> Objects.equals(it.getUpGradationStatus(), 1)).toList();
        }
        if (CollectionUtils.isEmpty(gradeList)) {
            log.info("当前会员等级: {}-{}，已是最高等级，不需要升级", member.getVipcode(), member.getGrade());
            return;
        }
        log.info("比当前会员等级高的等级数量: {}-{}", member.getGrade(), gradeList.size());
        // 开始时间-根据是否是周期年判断
        MemberYearAmountCalDto calDto = memberGradeStrategyService.getNoCacheCalculateYearAmountBeginDate(saleEntity.getGroupId(), saleEntity.getVipcode());
        Date startDate = calDto.getBeginDate();
        if (startDate.getTime() > saleDate.getTime()) {
            log.info("会员订单消费时间超出会员金额统计开始时间，会员消费时间【{}】消费金额统计开始时间【{}】", DateUtil.formatDateTime(saleDate), DateUtil.formatDateTime(startDate));
            return;
        }
        TbBaseShop baseShop = tbBaseShopService.getByContractNoAndMallId(saleEntity.getShopNo(), saleEntity.getMallId());
        if (Objects.isNull(baseShop)) {
            log.info("该商场: [{}-{}]下未查询店铺信息", saleEntity.getMallId(), saleEntity.getShopNo());
            return;
        }
        TbMemberGrade matchedMemberGrade = null;
        String remark = null;
        Integer changeType = null;
        Date beginDate = null, endDate = null;
        for (TbMemberGrade grade: gradeList) {
            // 查询会员等级规则信息
            List<TbMemberGradeRule> gradeRules = tbMemberGradeRuleService.queryByGroupIdAndGradeAndType(saleEntity.getGroupId(), grade.getCode(), 1);
            if (CollectionUtils.isEmpty(gradeRules)) {
                log.info("该会员等级: [{}]下未查询到会员升级规则信息", grade.getCode());
                continue;
            }
            gradeRules = this.parseRules(gradeRules);
            List<String> shopCodes = null;
            for (TbMemberGradeRule rule: gradeRules) {
                // 设置的身份认证升级规则
                if (RuleTypeEnum.MEMBER_IDENTITY_UPGRADE.getValue().equals(rule.getRuleType())) {
                    log.info("升级内设置的身份认证规则, {}-{}", grade.getCode(), rule.getRuleType());
                    continue;
                }
                if (Objects.isNull(rule.getMoney()) || rule.getMoney().compareTo(BigDecimal.ZERO) < 0) {
                    log.info("该会员等级: [{}]下规则类型: [{}]设置的金额不正确", grade.getCode(), rule.getRuleType());
                    continue;
                }
                // 查询对应业态下的店铺编号
                shopCodes = this.getShopNosByGroupIdAndFormats(saleEntity.getGroupId(), rule.getFormats());
                // 保存升级备注信息
                String formatsDesc = StringUtils.isNotBlank(rule.getFormatsDesc()) ? "在" + rule.getFormatsDesc() + "中" : "";
                // 每日消费
                if (RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                    beginDate = startDate;
                    // Tick 4.4.0添加每日消费金额发生变更后，从变更时间开始计算金额
                    if (Objects.nonNull(rule.getEffectiveDate()) && rule.getEffectiveDate().after(beginDate)) {
                        beginDate = DateUtil.beginOfDay(rule.getEffectiveDate());
                    }
                    // Tick 4.7.0等级升级统计销售的开始时间与当前时间是否是同一天
                    if (DateUtil.isSameDay(calDto.getBeginDate(), beginDate)) {
                        beginDate = calDto.getBeginDate();
                    }
                    endDate = currentDate;
                    changeType = 2;
                    remark = "每日消费金额满足升级标准：" + (StringUtils.isNotBlank(formatsDesc) ? formatsDesc : "") + "消费" + rule.getMoney() + "元";
                    // 年消费升级规则
                } else if (RuleTypeEnum.ACCUMULATIVE_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                    beginDate = startDate;
                    endDate = currentDate;
                    changeType = 3;
                    remark = "累计消费金额满足升级标准：" + ((2 == rule.getSumType()) ? "周期年内" : "自然年内") + (StringUtils.isNotBlank(formatsDesc) ? formatsDesc : "") + "消费" + rule.getMoney() + "元";
                    // 月累计升级规则
                } else if (RuleTypeEnum.MONTHLY_ACCUMULATIVE_CONSUME_UPGRADE.getValue().equals(rule.getRuleType())) {
                    beginDate = startDate;
                    endDate = currentDate;
                    changeType = 10;
                    remark = "月累计消费金额满足升级标准：" + (StringUtils.isNotBlank(formatsDesc) ? formatsDesc : "") + "消费" + rule.getMoney() + "元";
                    // 入会当日升级规则
                } else if (RuleTypeEnum.JOIN_CONSUME_UPGRADE.getValue().equals(rule.getRuleType()) && StringUtils.equals(saleDateStr, DateUtil.formatDate(member.getJoinTime()))) {
                    beginDate = DateUtil.beginOfDay(saleDate);
                    endDate = DateUtil.endOfDay(saleDate);
                    changeType = 5;
                    remark = "入会当日消费满足升级标准：" + (StringUtils.isNotBlank(formatsDesc) ? formatsDesc : "") + "消费" + rule.getMoney() + "元";
                } else {
                    continue;
                }
                // 计算销售金额是否能升级
                boolean succeeded = tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(member, rule, beginDate, endDate, shopCodes);
                if (succeeded) {
                    // 是入会当天的升级规则
                    if (3 == rule.getRuleType()) {
                        if (this.checkJoinDayParallelConditions(rule.getJoinDayParallelRules(), member, beginDate, endDate)) {
                            matchedMemberGrade = grade;
                            break;
                        } else {
                            log.info("会员销售满足入会当日规则，不满足入会当日并行条件: {}-{}-{}", saleEntity.getGroupId(), saleEntity.getVipcode(), rule.getFormatsDesc());
                        }
                    } else {
                        matchedMemberGrade = grade;
                        break;
                    }
                } else {
                    // 每日消费升级历史记录检查
                    if (RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue().equals(rule.getRuleType()) && StringUtils.isNotBlank(rule.getCertification())) {
                        PreviousDailyConsumptionRuleDto ruleDto = JsonUtils.stringToObj(rule.getCertification(), PreviousDailyConsumptionRuleDto.class);
                        // 上一次每日消费升级规则判断，只有在上一次规则生效时间和当前规则设置的生效时间之内的，需要重新检查
                        if (Objects.nonNull(ruleDto) && Objects.nonNull(ruleDto.getEffectiveDate()) && ruleDto.getEffectiveDate().before(rule.getEffectiveDate()) &&
                                saleDate.after(ruleDto.getEffectiveDate()) && saleDate.before(rule.getEffectiveDate())) {
                            // 查询对应业态下的店铺编号
                            shopCodes = this.getShopNosByGroupIdAndFormats(saleEntity.getGroupId(), ruleDto.getFormats());
                            if (CollectionUtils.isNotEmpty(shopCodes)) {
                                succeeded = tbSalesDetailService.checkPreviousDailyConsumptionRuleWhetherRelegationSucceeded(member, ruleDto, DateUtil.beginOfDay(ruleDto.getEffectiveDate()),
                                        DateUtil.endOfDay(DateUtil.offsetDay(rule.getEffectiveDate(), -1)), shopCodes);
                                // 前一次设置的每日消费升级金额
                                if (succeeded) {
                                    matchedMemberGrade = grade;
                                    remark = "每日消费升级-上一次设置规则满足升级标准：" + "消费" + ruleDto.getMoney() + "元，规则生效日期为：" + DateUtil.formatDateTime(ruleDto.getEffectiveDate());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if (Objects.nonNull(matchedMemberGrade)) {
                break;
            }
        }
        if (Objects.isNull(matchedMemberGrade)) {
            log.info("会员消费金额未满足等级升级规则，不做升级处理，{}-{}-{}", saleEntity.getGroupId(), saleEntity.getVipcode(), Objects.nonNull(saleEntity.getPayAmount()) ? saleEntity.getPayAmount() : "0");
            return;
        }
        if (StringUtils.isNotBlank(saleEntity.getMallId())) {
            member.setMallId(saleEntity.getMallId());
        }
        log.info("集团卡等级变更触发会员升级: {}-{}-{}-{}", member.getVipcode(), remark, member.getGrade(), matchedMemberGrade.getCode());
        this.executeMemberUpgrade(member, matchedMemberGrade, remark, changeType, new Date(), calDto);
    }

    /**
     * 检查入会当日并行条件
     * @param conditions
     * @param tbMemberAsset
     * @param beginDate
     * @param endDate
     * @return
     */
    private boolean checkJoinDayParallelConditions(List<TbMemberGradeRule> conditions, TbMemberAsset tbMemberAsset, Date beginDate, Date endDate) {
        if (CollectionUtils.isEmpty(conditions)) {
            return true;
        }
        boolean joinDayParallel = true;
        List<String> shopCodes = null;
        for (TbMemberGradeRule joinDayRule : conditions) {
            shopCodes = this.getShopNosByGroupIdAndFormats(tbMemberAsset.getGroupId(), joinDayRule.getFormats());
            if (CollectionUtils.isEmpty(shopCodes)) {
                joinDayParallel = false;
                break;
            }
            // 查询当日入会并行规则是否满足
            joinDayParallel = tbSalesDetailService.checkMemberSaleAmountWhetherRelegationSucceeded(tbMemberAsset, joinDayRule, beginDate, endDate, shopCodes);
            if (!joinDayParallel) {
                break;
            }
        }
        return joinDayParallel;
    }

    /**
     * 通过业态查询店铺号
     * @param groupId
     * @param formats
     * @return
     */
    private List<String> getShopNosByGroupIdAndFormats(String groupId, String formats) {
        // 查询对应业态下的店铺编号
        return tbBaseShopService.getContractNoList(groupId, formats);
    }

    /**
     * 解析加入日并行条件
     * @param gradeRules
     * @return
     */
    private List<TbMemberGradeRule> parseRules(List<TbMemberGradeRule> gradeRules) {
        List<TbMemberGradeRule> rules = new ArrayList<>(10);
        Long pid = 0L;
        for (TbMemberGradeRule rule: gradeRules) {
            if (3 == rule.getRuleType()) {
                if (pid.equals(rule.getPid())) {
                    rule.setJoinDayParallelRules(gradeRules.stream().filter(it -> (3 == rule.getRuleType() && rule.getId().equals(it.getPid()))).collect(Collectors.toList()));
                    // 入会当日第一规则
                    rules.add(rule);
                }
            } else {
                rules.add(rule);
            }
        }
        return rules;
    }

    /**
     * 执行会员等级升级操作
     * @param member
     * @param matchedGrade
     * @param remark
     * @param changeType
     * @param saleCreateDate: 销售落入数据库的时间
     * @param calDto: 等级变更关联的销售开始时间
     */
    private void executeMemberUpgrade(TbMemberAsset member, TbMemberGrade matchedGrade, String remark, Integer changeType, Date saleCreateDate, MemberYearAmountCalDto calDto) {
        TbMemberGradeChangeDetail changeDetail = tbMemberGradeChangeDetailService.queryChangeMaxByGroupIdAndVipcode(member.getGroupId(), member.getVipcode());
        if (KipDateUtils.checkGradeChangeDetail(changeDetail) && changeDetail.getCreateDate().getTime() >= saleCreateDate.getTime()) {
            saleCreateDate = DateUtil.offsetSecond(changeDetail.getCreateDate(), 1);
        }
        // 会员旧等级
        String oldGrade = member.getGrade();
        // 会员新等级
        String newGrade = matchedGrade.getCode();
        TbMemberGradeChangeDetail detail = TbMemberGradeChangeDetail.builder().id(IdUtil.simpleUUID())
                .nickName(member.getNickName()).mobile(member.getMobile()).changeType(changeType).oldGrade(oldGrade)
                .vipcode(member.getVipcode()).createDate(saleCreateDate).groupId(member.getGroupId()).mallId(member.getMallId())
                .createUser("system").newGrade(newGrade).remark(remark).memberId(String.valueOf(member.getId()))
                .kipUserId(member.getKipUserId()).authorizedMallId(member.getAuthorizedMallId()).build();
        //设置升级后的等级.
        member.setGrade(newGrade);
        // 执行会员等级变更
        tbMemberAssetService.updateMemberGrade(member);
        // 保存等级变更明细
        tbMemberGradeChangeDetailService.saveDetail(detail);
        // 保存等级变更标记的销售区间
        tbGradeChangeBetweenSalesService.saveDetail(detail.getId(), calDto.getBeginDate(), detail.getCreateDate());
        // 会员升级场景触发奖励
        this.sendSceneReward(member, newGrade);
    }

    /**
     * 场景触发奖励-会员升级奖励发放
     * @param member 会员
     * @param newGrade 会员升级后等级
     */
    private void sendSceneReward(TbMemberAsset member, String newGrade) {
        JSONObject sendJson = new JSONObject();
        sendJson.put("skipSubEventIds", newGrade);
        sendJson.put("vipCode", member.getVipcode());
        sendJson.put("groupId", member.getGroupId());
        sendJson.put("mallId", member.getMallId());
        sendJson.put("mobile", member.getMobile());
        log.info("会员升级场景触发奖励------>会员号【{}】---------->生产者数据信息【{}】", member.getVipcode(), sendJson.toJSONString());
        rabbitMqService.sendTopicMessage(RabbitMqConstant.MEMBER_SCENE_UPGRADE_QUEUE, sendJson.toJSONString());
    }

}
