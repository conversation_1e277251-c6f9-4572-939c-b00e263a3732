package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbPaymentPushRecord;
import com.kerryprops.kip.service.integral.model.dto.PaymentRecordDetailDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 05/05/2023 15:08
 **********************************************************************************************************************/
public interface TbPaymentPushRecordService extends IService<TbPaymentPushRecord> {

    void savePushRecord(PaymentRecordDetailDto detailDto);

    void refundPosPoints(SalesAutoPointsDto pointsDto, Integer amountOfPoints);
}
