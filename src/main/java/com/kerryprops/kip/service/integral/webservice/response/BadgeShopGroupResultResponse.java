package com.kerryprops.kip.service.integral.webservice.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BadgeShopGroupResultResponse implements Serializable {

    private BigDecimal amount;

    private Integer shopCount;

    private Integer frequency;

    private Integer days;

    private Long shopGroupId;

}
