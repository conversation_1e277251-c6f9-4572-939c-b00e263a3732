package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member_benefitsconfig
 * <AUTHOR>

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_benefit_config")
public class TbMemberBenefitConfig implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 权益ID
     */
    @TableField("benefits_id")
    private String benefitsId;

    /**
     * 商场编号
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 会员等级
     */
    @TableField("member_grade")
    private String memberGrade;

    /**
     * 权益名称
     */
    @TableField("name")
    private String name;

    /**
     * 副标题
     */
    @TableField("subtitle")
    private String subTitle;

    /**
     * 图标
     */
    @TableField("logo")
    private String logo;

    /**
     * 链接类型：0站内 1站外 2无类型 3富文本
     */
    @TableField("link_type")
    private String linkType;

    /**
     * 内部链接类型：0自定义1商品2券3页面4自定义页面
     */
    @TableField("inside_type")
    private String insideType;

    /**
     * 内部链接url
     */
    @TableField("inside_url")
    private String insideUrl;

    /**
     * 内部链接选中id
     */
    @TableField("inside_id")
    private String insideId;

    /**
     * 二级联动回显
     */
    @TableField("two_level_linkage")
    private String twoLevelLinkage;

    /**
     * 外部链接类型：0h5、1小程序
     */
    @TableField("outside_type")
    private String outsideType;

    /**
     * 外部appid
     */
    @TableField("outside_app_id")
    private String outsideAppid;

    /**
     * 外部链接名称
     */
    @TableField("outside_url_name")
    private String outsideUrlName;

    /**
     * 外部链接url
     */
    @TableField("outside_url")
    private String outsideUrl;

    /**
     * 展现形式：0弹框 1新页面
     */
    @TableField("show_type")
    private String showType;

    /**
     * 权益内容
     */
    @TableField("content")
    private String content;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 集团ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 创建者
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新者
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 是否删除;1删除/0正常
     */
    @TableField("is_del")
    private Integer isDel;

}