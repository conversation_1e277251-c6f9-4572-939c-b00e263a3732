package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.enums.PointsRedemptionEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbPaymentPushRecord;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.mapper.TbPaymentPushRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.PaymentRecordDetailDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbMemberPointsChangeService;
import com.kerryprops.kip.service.integral.service.TbPaymentPushRecordService;
import com.kerryprops.kip.service.integral.service.TbPointsDetailService;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 05/05/2023 15:09
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbPaymentPushRecordServiceImpl extends ServiceImpl<TbPaymentPushRecordMapper, TbPaymentPushRecord> implements TbPaymentPushRecordService {

    private final TbPaymentPushRecordMapper tbPaymentPushRecordMapper;
    private final TbMemberPointsChangeService tbMemberPointsChangeService;
    private final TbPointsDetailService tbPointsDetailService;
    private final TbSalesDetailService tbSalesDetailService;

    @Async
    @Override
    public void savePushRecord(PaymentRecordDetailDto detailDto) {
        if (Objects.isNull(detailDto)) {
            return;
        }
        // 组装数据，保存推送记录
        TbPaymentPushRecord record = TbPaymentPushRecord.builder()
                .orderNo(detailDto.getOrderNo())
                .tradeType(detailDto.getProductType())
                .payMethod(StringUtils.isBlank(detailDto.getRefundBatchCode()) ? detailDto.getPayOption() : detailDto.getPspName())
                .refundNo(StringUtils.isBlank(detailDto.getRefundBatchCode()) ? "0" : detailDto.getRefundBatchCode())
                .content(JsonUtils.objToString(detailDto))
                .build();
        tbPaymentPushRecordMapper.insert(record);
    }

    @Async
    @Override
    public void refundPosPoints(SalesAutoPointsDto pointsDto, Integer amountOfPoints) {
        TbPointsDetail detail = tbPointsDetailService.queryAdjustList(pointsDto.getGroupId(), pointsDto.getMallId(), pointsDto.getSaleNo(), pointsDto.getShopId(), PointsRedemptionEnum.POS_CASH_OUT.getCode());
        if (Objects.isNull(detail) || detail.getAmount() == 0) {
            log.info("refundPosPoints adjust is null, saleNo :{}", pointsDto.getSaleNo());
            return;
        }
        // 判断该笔订单是否退款成功，0代表未退货表示失败
        TbSalesDetail salesDetail = tbSalesDetailService.querySaleBySellNo(pointsDto.getSaleNo());
        if (Objects.isNull(salesDetail) || 0 == salesDetail.getStatus()) {
            log.info("refundPosPoints memberSale is null or is not refund, saleNo{}", pointsDto.getSaleNo());
            return;
        }
        SalesAutoPointsDto dto = SalesAutoPointsDto.builder().groupId(pointsDto.getGroupId()).mallId(pointsDto.getMallId()).shopId(pointsDto.getShopId()).member(pointsDto.getMember()).build();
        dto.setSaleNo(pointsDto.getSaleNo());
        // 将金额转换成积分
        dto.setRemark(PointsRedemptionEnum.POS_CASH_OUT_REFUND.getCode());
        dto.setRemarkName("POS积分抵现退款退积分");
        dto.setAmount(amountOfPoints + "");
        pointsDto.setCreateUser("pos");
        dto.setExchangePoints(Math.abs(detail.getAmount()));
        dto.setSalesRemark(String.format("%s - 退还积分: %s" ,dto.getRemarkName(), dto.getExchangePoints()));
        // 查询积分变更记录
        tbMemberPointsChangeService.changeMemberPoints(dto);
    }

}
