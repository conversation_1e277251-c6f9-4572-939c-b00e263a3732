package com.kerryprops.kip.service.integral.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PointsInterceptStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsIntercept;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.model.dto.TbPointsInterceptQueryDto;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.response.PagePointsInterceptDetailResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbPointsInterceptDetailResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/26/2023 16:38
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/points/intercept")
@RestController
@RequiredArgsConstructor
@Tag(name = "积分拦截")
public class TbPointsInterceptApproveController extends BaseMemberStatus {

    private final MemberSalePointsProcessService memberSalePointsProcessService;
    private final HiveVasService hiveVasService;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbPointsInterceptApproveService interceptApproveService;
    private final TbCardMemberRelationService tbCardMemberRelationService;
    private final Mapper mapper;

    @PostMapping("/salePointsProcess")
    @Operation(summary="销售录入", method = "POST")
    public Integer salePointsProcess(@RequestBody SalesAutoPointsDto pointsDto) {
        log.info("interceptParams :{}", JsonUtils.objToString(pointsDto));
        return memberSalePointsProcessService.salePointsProcess(pointsDto);
    }

    @GetMapping("/record")
    @Operation(summary="会员异常拦截", method = "GET")
    public PagePointsInterceptDetailResponse getInterceptRecord(@CurrentUser LoginUser loginUser,
                                                                           @RequestParam(required = false) String cardNo,
                                                                           @RequestParam Integer num,
                                                                           @RequestParam Integer size
                                                                    ) {
        // 查询当前会员信息
        TbMemberAsset tbMemberAsset;
        // 取审核驳回及待审核的
        // 根据LBS查询其所属的楼盘信息
        if (StringUtils.isNotBlank(cardNo)) {
            TbCardMemberRelation relation = tbCardMemberRelationService.findByExternalCardNo(cardNo);
            if (Objects.isNull(relation)) {
                return PagePointsInterceptDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).build();
            }
            tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(relation.getGroupId()).mobile(relation.getMobile()).build());
        } else {
            // 根据LBS查询其所属的楼盘信息
            MallItem mallItem = hiveVasService.getMallByLbsId(loginUser.getLbsId());
            if (Objects.isNull(mallItem)) {
                return PagePointsInterceptDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).build();
            }
            tbMemberAsset = tbMemberAssetService.findByKipUserIdAndGroupId(loginUser.getCId(), loginUser.getBrandId());
        }
        if (Objects.isNull(tbMemberAsset)) {
            return PagePointsInterceptDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).build();
        }
        TbPointsInterceptQueryDto queryDto = TbPointsInterceptQueryDto
                .builder()
                .groupId(tbMemberAsset.getGroupId())
                .mobile(tbMemberAsset.getMobile())
                .statusList(Arrays.asList(PointsInterceptStatusEnum.INTERCEPT_WAIT_STATUS.getType(), PointsInterceptStatusEnum.INTERCEPT_REJECT_STATUS.getType(), PointsInterceptStatusEnum.INTERCEPT_REFUND_STATUS.getType()))
                .num(num)
                .size(size)
                .build();
        int count = interceptApproveService.getTotal(queryDto);
        if (count <= 0) {
            return PagePointsInterceptDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).build();
        }
        List<TbPointsIntercept> list = interceptApproveService.getPageData(queryDto);
        List<TbPointsInterceptDetailResponse> respList = list.stream().map(it -> mapper.map(it, TbPointsInterceptDetailResponse.class)).collect(Collectors.toList());
        this.fillData(respList);
        int totalPage = (count % queryDto.getSize() == 0) ? (count / queryDto.getSize()) : (count / queryDto.getSize()) + 1;
        return PagePointsInterceptDetailResponse.builder().totalPages(totalPage).totalElements(count).content(respList).build();
    }

    private void fillData(List<TbPointsInterceptDetailResponse> list) {
        List<String> shopNos = list.stream().map(TbPointsInterceptDetailResponse::getShopNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        CompletableFuture<Map<String, TenantInfoVo>> shopFuture = hiveVasService.getTenantFuture(shopNos);
        List<String> mallIds = list.stream().map(TbPointsInterceptDetailResponse::getMallId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        CompletableFuture<Map<String, String>> mallNameFuture = hiveVasService.getMallName(mallIds);
        try {
            Map<String, TenantInfoVo> shopMap = shopFuture.get();
            Map<String, String> mallNameMap = mallNameFuture.get();
            for (TbPointsInterceptDetailResponse sale : list) {
                sale.setSaleTypeName(StringUtils.equals(sale.getSaleType(), SaleTypeEnum.CRM.getValue()) ? "销售积分" : SaleTypeEnum.getDescByVale(sale.getSaleType()));
                sale.setRemark(SaleTypeEnum.getDictTypeByValue(sale.getSaleType()));
                Optional.ofNullable(MapUtils.getObject(shopMap, sale.getShopNo())).ifPresent(tenant -> {
                    // 店铺别名 -> hive shopName
                    sale.setShopAliasName(tenant.getShopName());
                    // 店铺名 -> hive brandName
                    sale.setShopName(tenant.getBrandName());
                    // 店铺品牌名 -> hive retailBrandName
                    sale.setRetailBrandName(tenant.getRetailBrandName());
                });
                Optional.ofNullable(MapUtils.getString(mallNameMap, sale.getMallId())).ifPresent(sale::setMallName);
            }
        } catch (Exception e) {
            log.error("积分拦截补充信息失败 {}", e.getMessage());
            e.printStackTrace();
        }
    }

}
