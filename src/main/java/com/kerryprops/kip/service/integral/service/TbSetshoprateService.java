package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbCashOutConfig;
import com.kerryprops.kip.service.integral.model.dto.PointsRedemptionDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;

import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 14:02
 **********************************************************************************************************************/
public interface TbSetshoprateService {

    /**
     * 根据销售金额计算销售金额能获取的积分数
     *
     * @param points points
     * @return Integer int
     */
    Integer salesToIntegral(SalesAutoPointsDto points);

    /**
     * 获取店铺最大积分抵现金额 --- 用户积分抵现计算最大可用金额
     * @param points points
     * @return Integer int
     */
    PointsRedemptionDto getMaxAmountOfStoreCashOut(SalesAutoPointsDto points);

    PointsRedemptionDto getMaxAmountOfShopCashOut(SalesAutoPointsDto points, TbCashOutConfig config);

    /**
     * 积分转金额
     * @param currentIntegral
     * @param money
     * @param points
     * @return
     */
    int getAmount(Integer currentIntegral, BigDecimal money, BigDecimal points);

    /**
     * 金额转积分
     * @param amount
     * @param money
     * @param points
     * @return
     */
    int getPoints(Integer amount, BigDecimal money, BigDecimal points);

    /**
     * 金额转积分
     * @param amount
     * @param money
     * @param points
     * @return
     */
    int getBigdecimalPoints(BigDecimal amount, BigDecimal money, BigDecimal points);
}
