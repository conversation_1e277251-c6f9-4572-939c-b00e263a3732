package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbPointsRepeatRule;
import org.apache.ibatis.annotations.Param;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/16/2022 14:09
 **********************************************************************************************************************/
public interface TbPointsRepeatRuleMapper extends BaseMapper<TbPointsRepeatRule> {

    /**
     * 品牌下的商场只能设置一条有效的规则
     * @param mallId
     * @param groupId
     * @return
     */
    TbPointsRepeatRule queryRulesByMallIdAndGroupId(@Param(value = "mallId") String mallId,
                                                    @Param(value = "groupId") String groupId);


}
