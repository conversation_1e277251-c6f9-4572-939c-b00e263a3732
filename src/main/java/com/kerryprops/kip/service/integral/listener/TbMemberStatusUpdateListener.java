package com.kerryprops.kip.service.integral.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.model.dto.TbMemberStatusUpdateDto;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/13/2022 17:12
 **********************************************************************************************************************/

@Slf4j
public class TbMemberStatusUpdateListener extends AnalysisEventListener<TbMemberStatusUpdateDto> {

    private final TbMemberAssetService tbMemberAssetService;
    private final String groupId;

    // 分批
    private static final int BATCH_COUNT = 100;
    private final AtomicInteger atomic = new AtomicInteger();
    List<String> list = new ArrayList<>();

    public TbMemberStatusUpdateListener(TbMemberAssetService tbMemberAssetService, String groupId) {
        this.tbMemberAssetService = tbMemberAssetService;
        this.groupId = groupId;
    }

    @Override
    public void invoke(TbMemberStatusUpdateDto updateDto, AnalysisContext analysisContext) {
        log.info("解析到一条数据:{}", JsonUtils.objToString(updateDto));
        list.add(updateDto.getVipcode());
        atomic.incrementAndGet();
        if (list.size() >= BATCH_COUNT) {
            updateStatus(groupId, list);
            // 存储完成清理 list
            list.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        this.updateStatus(groupId, list);
        log.info("Total Parse Data Size: {}", atomic.get());
    }

    private void updateStatus(String groupId, List<String> list) {
        log.info("{}条数据，开始执行会员状态更新.", list.size());
        tbMemberAssetService.updateMemberStatus(groupId, list);
        log.info("更新会员状态成功.");
    }

}
