package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource;
import com.kerryprops.kip.service.integral.webservice.response.BadgeShopGroupResultResponse;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 03/12/2024 12:00
 **********************************************************************************************************************/
public interface TbSalesDetailService extends IService<TbSalesDetail> {

    /**
     * 保存会员销售
     *
     * @param salesDetail
     */
    void saveMemberSale(TbSalesDetail salesDetail);

    /**
     * 无单退货保存销售记录
     * @param dto
     */
    void saveNoOrderRefundRecord(SalesAutoPointsDto dto);

    /**
     * 检查销售记录是否重重
     *
     * @param queryDto 查询参数
     * @return sale 销售对象
     */
    TbSalesDetail checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto queryDto);

    /**
     * 检查销售记录是否重复-通过list查询
     *
     * @param list 查询参数
     * @return sale 销售对象
     */
    List<TbSalesDetail> checkSaleRepeatedByConditions(List<MemberSaleRepeatQueryDto> list);

    /**
     * 根据销售单号查询销售记录
     * @param orderNo 销售单号
     * @return sale 销售对象
     */
    TbSalesDetail querySaleBySellNo(String orderNo);

    /**
     * 根据销售单号查询销售记录
     * @param orderNo 销售单号
     * @param saleType 销售类型
     * @return sale 销售对象
     */
    TbSalesDetail queryBySellNoAndSaleType(String orderNo, String saleType);

    /**
     * 查询会员时间段内排除退款的销售笔数
     * @param query 查询参数
     * @return int 数量
     */
    int queryMemberMonthSalesNumber(MemberSaleMonthNumberQueryDto query);

    /**
     * 查询会员时间段内不排除退款的销售笔数
     * @param query 查询参数
     * @return int 数量
     */
    int queryMemberSaleCountBetweenTime(MemberSaleMonthNumberQueryDto query);

    /**
     * 查询销售对象
     * @param orderNo 销售单
     * @param mallId 商城id
     * @return sale 对象
     */
    TbSalesDetail selectBySellNoAndMallId(String orderNo, String mallId);

    /**
     * 保存销售记录
     * @param dto 销售单
     */
    void saveSaleData(SalesAutoPointsDto dto);

    /**
     * 查询用户时间段内的销售金额-先查缓存再查数据库
     * @param member
     * @param rule
     * @param beginDate
     * @param endDate
     * @param shopNos
     * @return double
     */
    Double getMemberSaleAmountBetweenTime(TbMemberAsset member, TbMemberGradeRule rule, Date beginDate, Date endDate, List<String> shopNos);

    /**
     * 查询用户时间段内的销售金额-实时查数据库
     * @param member
     * @param ruleId
     * @param beginDate
     * @param endDate
     * @param shopNos
     * @return double
     */
    Double getMemberSaleAmountBetweenDate(TbMemberAsset member, List<String> shopNos, Date beginDate, Date endDate, Long ruleId);

    /**
     * 校验用户在保级规则下是否保级成功
     * @param member
     * @param rule
     * @param beginDate
     * @param endDate
     * @param shopNos
     * @return
     */
    boolean checkMemberSaleAmountWhetherRelegationSucceeded(TbMemberAsset member, TbMemberGradeRule rule, Date beginDate, Date endDate, List<String> shopNos);

    /**
     * 每日消费升级规则，上一次记录升级成功判断
     * @param member
     * @param ruleDto: 该规则对应的上一条每日消费升级规则配置
     * @param beginDate
     * @param endDate
     * @param shopNos
     * @return
     */
    boolean checkPreviousDailyConsumptionRuleWhetherRelegationSucceeded(TbMemberAsset member, PreviousDailyConsumptionRuleDto ruleDto, Date beginDate, Date endDate, List<String> shopNos);

    /**
     * 处理11/2，11/3号这两天由于bug导致用户未升级的等级
     */
    void initMemberGradeData(String vipcode);

    /**
     * 修改商场错误的店铺id
     * @param shopNos 店铺id
     * @param mallId 商城id
     * @param newIds newIds
     */
    void modifyMemberSaleShopId(String shopNos, String mallId, String newIds);

    /**
     * 修改商场错误的店铺id
     * @param groupId 店铺id
     * @param mallId 商城id
     * @param vipcode 会员号
     * @param orderNos 销售单
     */
    CompletableFuture<Map<String, TbSalesDetail>> findSalesMap(String groupId, String mallId, String vipcode, List<String> orderNos);

    Map<String, TbSalesDetail> getSalesList(String groupId, String mallId, String vipcode, List<String> orderNos);

    /**
     * 发生了退款，处理满赠活动满赠金额MQ推送
     * @param saleId
     */
    void memberSaleRefundProcess(Long saleId);

    /**
     * 通过id查询销售数据
     * @param id
     * @return
     */
    TbSalesDetail getMemberSaleById(Long id);

    /**
     * 查询时间范围内的销售记录
     * @param groupId
     * @param vipcode
     * @param beginDate
     * @param endDate
     * @param shopNos
     * @return
     */
    List<TbSalesDetail> getSaleListCreateAsc(String groupId, String vipcode, Date beginDate, Date endDate, List<String> shopNos);

    void checkSalesUpgradeGrade(String groupId, String startDate, String endDate);

    /**
     * 获取会员等级规则缓存的金额信息
     * @param rule
     * @param tbMemberAsset
     * @return
     */
    Double getGradeRuleCacheAmount(TbMemberGradeRule rule, TbMemberAsset tbMemberAsset);

    /**
     * 校验积分拦截
     * @param queryDto queryDto
     * @return int int
     */
    List<String> checkSaleIsIntercept(SaleDetailQueryDto queryDto);

    /**
     * 查询会员销售明细
     * @param groupId
     * @param vipcode
     * @return
     */
    List<TbSalesDetail> getMemberSaleList(String groupId, String vipcode);

    /**
     * 修复数据的sql
     * @param sql
     */
    void executeSql(String sql);

    /**
     * 查询sql返回的结果信息
     * @param sql
     * @return
     */
    List<Map<String, Object>> querySqlResult(String sql);

    /**
     * 徽章累计金额计算
     * @param resource
     * @return
     */
    BigDecimal accumulatedSalesAmount(BadgeSalesResource resource);

    /**
     * 徽章查询消费店铺数量
     * @param resource
     * @return
     */
    List<String> consumeShopCount(BadgeSalesResource resource);

    /**
     * 徽章查询会员消费频次
     * @param resource
     * @return
     */
    int consumeFrequency(BadgeSalesResource resource);

    /**
     * 徽章查询会员消费天数
     * @param resource
     * @return
     */
    List<String> consumeDays(BadgeSalesResource resource);

    /**
     * 徽章关联的店铺组销售明细
     * @param list
     * @return
     */
    List<BadgeShopGroupResultResponse> badgeShopGroupSalesDetails(List<BadgeSalesResource> list);

    /**
     * KO合并，会员卡等变更触发会员升级
     * @param groupId
     * @param date
     */
    void gradeChangeTriggerMemberUpgrade(String groupId, String date);

}
