package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/19/2023 09:22
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "海鼎查询shopNo入参")
public class ShopCodeResource implements Serializable {

    @NotBlank(message = "合同号不能为空.")
    @Schema(description = "合同号")
    private String contractNo;

    @NotBlank(message = "商场id不能为空.")
    @Schema(description = "商场id")
    private String mallid;

}
