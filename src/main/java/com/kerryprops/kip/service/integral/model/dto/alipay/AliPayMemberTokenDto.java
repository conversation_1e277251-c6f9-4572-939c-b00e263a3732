package com.kerryprops.kip.service.integral.model.dto.alipay;

import lombok.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝授权token接受类
 * @createDate 2022/10/20
 * @updateDate 2022/10/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class AliPayMemberTokenDto {
    /**
     * 支付宝唯一用户标识，以2088开头的16位数字
     */
    private String userId;

    /**
     * 授权访问令牌。通过该令牌调用需要授权类接口
     */
    private String accessToken;
}
