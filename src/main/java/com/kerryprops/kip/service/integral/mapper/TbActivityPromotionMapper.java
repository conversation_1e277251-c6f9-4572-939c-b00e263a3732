package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbActivityPromotionMapper extends BaseMapper<TbActivityPromotion> {

    List<TbActivityPromotion> queryByMallIdAndDate(@Param("mallId") String mallId, @Param("saleDate") String saleDate);

}