package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.collect.Lists;
import com.kerryprops.kip.service.integral.common.enums.EmailTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.MessageTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.WxTemplatePlaceholderEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbEmailTemplateConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbTemplateMessage;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.vo.WxTemplateSendVo;
import com.kerryprops.kip.service.integral.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/22/2023 15:53
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateMessageSendServiceImpl implements TemplateMessageSendService {

    private final SysDictService sysDictService;
    private final HiveVasService hiveVasService;
    private final TbMemberAssetService tbMemberAssetService;
    private final KerryStaffService kerryStaffService;
    private final TbMemberGradeService tbMemberGradeService;
    private final MemberRegisterService memberRegisterService;
    private final MessageService messageService;
    private final MallConfig mallConfig;

    @Override
    public void sendMessage(SendMessageDto dto) {
        TbMemberAsset tbMemberAsset = tbMemberAssetService.getMemberById(dto.getMemberId());
        if (Objects.isNull(tbMemberAsset)) {
            return;
        }
        // 清除redis缓存信息
        memberRegisterService.removeCacheMember(tbMemberAsset.getGroupId(), tbMemberAsset.getKipUserId());
        if (StringUtils.isBlank(dto.getGroupId())) {
            dto.setGroupId(tbMemberAsset.getGroupId());
        }
        if (StringUtils.isBlank(dto.getMallId())) {
            dto.setMallId(tbMemberAsset.getMallId());
        }
        if (Objects.equals(dto.getMessageType(), MessageTypeEnum.NEXT_DAY.getType())) {
            Date updated = Objects.nonNull(tbMemberAsset.getJoinTime()) ? tbMemberAsset.getJoinTime() : new Date();
            dto.setUpdateDate(DateUtil.formatDateTime(updated));
        }
        // 通过lbs id查询lbs信息
        Optional.ofNullable(hiveVasService.getLbsName(dto.getMallId())).ifPresent(it -> {
            dto.setMallName(Objects.nonNull(it.getLbs()) ? it.getLbs().getName() : null);
            dto.setProjectId(Objects.nonNull(it.getProject()) ? it.getProject().getId() : null);
        });
        // 发送模版消息(失败会自动发送短息，成功是否发送短息要根据配置来)
        this.doSendMsg(tbMemberAsset, dto);
        // 发邮件
        this.doSendEmail(tbMemberAsset, dto);
    }

    /**
     * 发送模版消息
     *
     * @param member     member
     * @param messageDto messageDto
     */
    private void doSendMsg(TbMemberAsset member, SendMessageDto messageDto) {
        String dictType = messageDto.getDictType();
        //积分商城和领券中心兑换电子券导致的积分变更不发送短信
        List<String> excludeList = Arrays.asList("XS0025", "XS0016", "REWARDS_MALL", "REFUND_REWARDS_MALL");
        if (StringUtils.isNotBlank(dictType) && excludeList.contains(dictType)) {
            return;
        }
        MessageQueryDto query = MessageQueryDto.builder()
                .mallid(messageDto.getMallId())
                .templateType(messageDto.getMessageType())
                .membergrade(member.getGrade())
                .state("0")
                .build();
        // 查找设置的对应模版消息
        List<TbTemplateMessage> messages = messageService.getModelTemplate(query);
        // 查到了配置的模版消息
        String msgContent = "";
        if (CollectionUtils.isNotEmpty(messages)) {
            msgContent = messages.get(0).getContent();
            log.info("wxTemplate {}", msgContent);
        }
        // 替换昵称，补充字典名，替换会员等级
        this.fillMessage(member, messageDto);
        String keywordValues = this.getKeyWordValues(msgContent, messageDto);
        // 发送短信的参数替换
        List<Map<String, String>> smsParam = this.getSmsParam(member.getGrade(), messageDto);
        // 组装发送模版消息参数
        WxTemplateSendVo sendVo = WxTemplateSendVo.builder()
                .mallid(messageDto.getMallId())
                .templateTypeDesc(MessageTypeEnum.getByType(messageDto.getMessageType()).getMsgType())
                .keywordValues(keywordValues)
                .mobile(messageDto.getMobile())
                .grade(member.getGrade())
                .miniPagePath(Collections.emptyList())
                .url(Collections.emptyList())
                .smsParam(smsParam)
                .type(String.valueOf(messageDto.getTemplateType()))
                .invokeService("points-service")
                .build();
        messageService.sendSmsTemplate(sendVo);
    }

    private List<Map<String, String>> getSmsParam(String grade, SendMessageDto messageDto) {
        MallItem mallItem = mallConfig.getByMallId(messageDto.getMallId());
        // 替换成前海的签名
        boolean isQHPrefix = !Objects.isNull(mallItem) && (StringUtils.equals(mallItem.getAbbreviation(), ABB_QHKC));
        List<Map<String, String>> smsParam = new ArrayList<>();
        MessageQueryDto query = MessageQueryDto.builder()
                .groupId(messageDto.getGroupId())
                .mallid(messageDto.getMallId())
                .sendNodeDesc(MessageTypeEnum.getByType(messageDto.getMessageType()).getMsgType())
                .membergrade(grade)
                .state("0")
                .build();
        List<TbSmsTemplateConfig> smsConfigList = messageService.getSmsTemplate(query);
        if (CollectionUtils.isEmpty(smsConfigList)) {
            log.info("smsTemplateIsEmpty {}, {}", query.getSendNodeDesc(), query.getMembergrade());
            return Collections.emptyList();
        }
        // 查找对应商场id对应的短信模版信息
        TbSmsTemplateConfig config = smsConfigList.stream().filter(it -> StringUtils.equals(messageDto.getMallId(), it.getMallid())).findFirst().orElse(null);
        if (Objects.isNull(config)) {
            log.info("mallSmsTemplateIsEmpty {}", query.getMallid());
            return Collections.emptyList();
        }
        String content = config.getSmsPreview();
        if (StringUtils.isBlank(content)) {
            return Collections.emptyList();
        }
        // 对content解析
        // 【嘉里建设】您的会员卡号「{MEMBER_CARD_NO}」发生积分变更：「{POINTS_CHANGE_NUMBER}」，
        // 当前积分余额为「{MEMBER_POINTS}」。
        for (WxTemplatePlaceholderEnum wxEnum : WxTemplatePlaceholderEnum.values()) {
            if (content.contains(wxEnum.getName())) {
                Map<String, String> map = new HashMap<>(1);
                String mapKey = wxEnum.getName().replace("{", "").replace("}", "");
                String desc = wxEnum.getDesc();
                if (isQHPrefix && StringUtils.equals(mapKey, "SMS_PREFIX")) {
                    desc = "【前海嘉里中心嘉湾汇】";
                }
                Object value = ReflectUtil.getFieldValue(messageDto, wxEnum.getField());
                if (Objects.nonNull(value)) {
                    map.put(mapKey, value.toString());
                } else {
                    map.put(mapKey, StringUtils.equals(desc, "2099-01-01") ? DateUtil.formatDateTime(new Date()) : desc);
                }
                smsParam.add(map);
            }
        }
        log.info("smsParam {}", JSON.toJSON(smsParam));
        return smsParam;
    }

    private String getKeyWordValues(String msgContent, SendMessageDto messageDto) {
        if (StringUtils.isBlank(msgContent)) {
            log.info("wxTemplate is null {}", messageDto.getMessageType());
            return "";
        }
        // 解析content，将占位符提取出来
        List<TemplateParamDto> list = Lists.newArrayList();
        JSONObject jsonObject = JSON.parseObject(msgContent, Feature.OrderedField);
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            Object valueJson = entry.getValue();
            if (!Objects.isNull(valueJson) && valueJson instanceof JSONObject) {
                String templateValue = ((JSONObject) valueJson).getString("value");
                templateValue = Objects.isNull(templateValue) ? "" : templateValue;
                List<WxTemplatePlaceholderEnum> enumList = WxTemplatePlaceholderEnum.match(templateValue);
                Map<WxTemplatePlaceholderEnum, String> map = enumList.stream().collect(Collectors.toMap(valueEnum -> valueEnum, v -> "",
                        (o1, o2) -> o1, LinkedHashMap::new));
                list.add(new TemplateParamDto(entry.getKey(), map));
            }
        }
        // 通过反射获取DTO中的值
        for (TemplateParamDto templateParam : list) {
            for (Map.Entry<WxTemplatePlaceholderEnum, String> entry : templateParam.getEnumMap().entrySet()) {
                Object value = ReflectUtil.getFieldValue(messageDto, entry.getKey().getField());
                String desc = entry.getKey().getDesc();
                if (Objects.nonNull(value)) {
                    entry.setValue(value.toString());
                } else {
                    entry.setValue(StringUtils.equals(desc, "2099-01-01") ? DateUtil.formatDateTime(new Date()) : desc);
                }
            }
        }
        // 序列化为JSON
        JSONObject resultObject = new JSONObject(true);
        for (int i = 0; i < list.size(); i++) {
            TemplateParamDto templateParam = list.get(i);
            String finalValue = templateParam.buildFinalValue();
            Map<String, Object> valueMap = Map.of("value", finalValue);
            // resultObject.put(templateParam.key, new JSONObject(valueMap));
            resultObject.put("keyword" + i, new JSONObject(valueMap));
        }
        log.info("KeyWordValues {}", resultObject);
        return resultObject.toString();
    }

    private void fillMessage(TbMemberAsset member, SendMessageDto messageDto) {
        if (StringUtils.isBlank(member.getNickName()) && StringUtils.isNotBlank(member.getKipUserId())) {
            Optional.ofNullable(kerryStaffService.findByKipUserId(member.getKipUserId())).ifPresent(member::fillOtherInfo);
        }
        // 如果昵称为空，则设置为默认用户昵称
        if (StringUtils.isBlank(member.getNickName())) {
            member.setNickName("微信用户");
        }
        messageDto.setNickName(member.getNickName());
        // 设置字典名称
        if (StringUtils.isNotBlank(messageDto.getDictType()) && StringUtils.isBlank(messageDto.getDictName())) {
            Optional.ofNullable(sysDictService.findByDictType(messageDto.getDictType())).ifPresent(it -> messageDto.setDictName(it.getDictName()));
        }
        // 设置积分名称
        if (StringUtils.isNotBlank(messageDto.getNumber()) && !messageDto.getNumber().contains(messageDto.getDictName())) {
            messageDto.setNumber((StringUtils.isNotBlank(messageDto.getDictName()) ? messageDto.getDictName() : "") + messageDto.getNumber() + "积分");
        }
        Optional.ofNullable(tbMemberGradeService.queryByGroupIdAndGrade(member.getGroupId(), member.getGrade())).ifPresent(it -> messageDto.setMemberGradeDesc(it.getName()));
        messageDto.setMobile(member.getMobile());
        messageDto.setCurrentIntegral(member.getCurrentPoints());
        messageDto.setVipcode(member.getVipcode());
    }

    /**
     * 发邮件
     *
     * @param member     member
     * @param messageDto messageDto
     */
    private void doSendEmail(TbMemberAsset member, SendMessageDto messageDto) {
        // 除了商圈快速积分开通提醒类型外，其他消息类型需要发送邮件
        if (!Objects.equals(messageDto.getMessageType(), MessageTypeEnum.WX_AUTO_POINTS.getType())) {
            return;
        }
        if (StringUtils.isBlank(member.getNickName()) && StringUtils.isNotBlank(member.getKipUserId())) {
            Optional.ofNullable(kerryStaffService.findByKipUserId(member.getKipUserId())).ifPresent(member::fillOtherInfo);
        }
        if (StringUtils.isBlank(member.getEmail())) {
            return;
        }
        EmailTypeEnum typeEnum = EmailTypeEnum.getByType(messageDto.getMessageType());
        if (Objects.isNull(typeEnum)) {
            return;
        }
        // 查询配置的邮箱信息
        TbEmailTemplateConfig emailConfig = TbEmailTemplateConfig.builder().groupId(member.getGroupId())
                .mallId(StringUtils.isNotBlank(messageDto.getMallId()) ? messageDto.getMallId() : member.getMallId())
                .memberGrade(member.getGrade()).emailSendNode(typeEnum.getSendNode()).status(0).build();
        List<TbEmailTemplateConfig> emails = Collections.emptyList();
        if (CollectionUtils.isEmpty(emails)) {
            log.info("email: {}-{}未查询到短信模版配置.", member.getGroupId(), member.getGrade());
            return;
        }
        emailConfig = emails.get(0);
        // 获取短信发送内容
        String emailContent = emailConfig.getEmailContent();
        if (StringUtils.isBlank(emailContent)) {
            log.info("发送邮件内容为空-{}", emailConfig.getId());
            return;
        }
        // 替换参数
        /*emailContent = emailContent.replaceAll(this.getPrefix(MEMBER_CARD_NO), member.getVipcode());
        emailContent = emailContent.replaceAll(this.getPrefix(POINTS_CHANGE_NUMBER), StringUtils.isNotBlank(messageDto.getNumber()) ? messageDto.getNumber() : "0积分");
        if ((emailContent.contains(MEMBER_LEVEL) || emailContent.contains(MEMBER_GRADE)) && StringUtils.isBlank(messageDto.getMemberGradeDesc())) {
            Optional.ofNullable(tbMemberGradeService.queryByGroupIdAndGrade(member.getGroupId(), member.getGrade())).ifPresent(it -> messageDto.setMemberGradeDesc(it.getName()));
        }
        emailContent = emailContent.replaceAll(this.getPrefix(MEMBER_GRADE), StringUtils.isBlank(messageDto.getMemberGradeDesc()) ? "" : messageDto.getMemberGradeDesc());
        emailContent = emailContent.replaceAll(this.getPrefix(MEMBER_LEVEL), StringUtils.isBlank(messageDto.getMemberGradeDesc()) ? "" : messageDto.getMemberGradeDesc());
        emailContent = emailContent.replaceAll(this.getPrefix(MEMBER_NICKNAME), StringUtils.isBlank(member.getNickName()) ? "微信用户" : member.getNickName());
        emailContent = emailContent.replaceAll(this.getPrefix(MEMBER_POINTS), Objects.nonNull(member.getCurrentPoints()) ? member.getCurrentPoints() + "积分" : "0积分");
        emailContent = emailContent.replaceAll(this.getPrefix(EVENT_TIME), StringUtils.isBlank(messageDto.getUpdateDate()) ? DateUtil.formatDateTime(new Date()) : messageDto.getUpdateDate());
        // 发邮件
        EmailSendVo sendVo = EmailSendVo.builder().sendTos(Arrays.asList(member.getEmail()))
                .businessType("COMMON").html(true).subject(emailConfig.getEmailSubject())
                .text(emailContent).source("CRM").build();
        smsSendService.sendEmail(sendVo);*/
    }


}

