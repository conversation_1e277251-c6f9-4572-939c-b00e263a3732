package com.kerryprops.kip.service.integral.webservice.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/27/2024 16:39
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialQrcodeResponse implements Serializable {

    private String redirect;

    private String tpName;

    @JsonProperty("mallId")
    private String mallId;

    @JsonProperty("mallid")
    private String mallid;

    private String regSource;

    private String lbsId;
}
