package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.pmw.client.resource.PointsActivationConfirmResource;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbOpenCardFailRecord;
import com.kerryprops.kip.service.integral.mapper.TbOpenCardFailRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsAuthRecordService;
import com.kerryprops.kip.service.integral.service.TbOpenCardFailRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 01/16/2024 15:10
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbOpenCardFailRecordServiceImpl extends ServiceImpl<TbOpenCardFailRecordMapper, TbOpenCardFailRecord> implements TbOpenCardFailRecordService {

    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private final TbOpenCardFailRecordMapper tbOpenCardFailRecordMapper;
    private final MallConfig mallConfig;

    @Async
    @Override
    public void saveOrUpdateRecord(PointsActivationConfirmResource resource) {
        // 取body信息
        PointsActivationConfirmResource.PointsActivationDetailResource authorizeResource = resource.getBody().getResource();
        if (Objects.isNull(authorizeResource)) {
            return;
        }
        MallItem mallItem = mallConfig.getByMchId(authorizeResource.getMchId());
        if (Objects.isNull(mallItem)) {
            return;
        }
        TbOpenCardFailRecord record = TbOpenCardFailRecord.builder()
                .openId(authorizeResource.getOpenId()).mallId(mallItem.getMallId())
                .content(JsonUtils.objToString(authorizeResource)).build();
        tbOpenCardFailRecordMapper.saveOrUpdate(record);
    }

    @Override
    public TbOpenCardFailRecord queryByOpenIdAndMallId(String openId, String mallId) {
        return tbOpenCardFailRecordMapper.queryByOpenIdAndMallId(openId, mallId);
    }

    @Async
    @Override
    public void checkWeChatAndAlipayAuthStatus(String openId, String userId) {
        List<TbOpenCardFailRecord> records = tbOpenCardFailRecordMapper.queryByOpenId(openId);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        TbInsensatePointsAuthRecord authRecord;
        MallItem mallItem;
        for (TbOpenCardFailRecord record: records) {
            authRecord = tbInsensatePointsAuthRecordService.queryByOpenIdAndMallId(record.getOpenId(), record.getMallId());
            if (Objects.nonNull(authRecord)) {
                continue;
            }
            mallItem = mallConfig.getByMallId(record.getMallId());
            if (Objects.isNull(mallItem)) {
                continue;
            }
            PointsActivationConfirmResource.PointsActivationDetailResource authorizeResource = JsonUtils.stringToObj(record.getContent(), PointsActivationConfirmResource.PointsActivationDetailResource.class);
            if (Objects.isNull(authorizeResource)) {
                continue;
            }
            // 补充授权记录
            authRecord = TbInsensatePointsAuthRecord.builder().origin(InsensateOriginEnum.WECHAT.getValue()).openId(record.getOpenId()).groupId(mallItem.getGroupId())
                    .mallId(mallItem.getMallId()).mchid(mallItem.getWxMchId()).kipUserId(userId).cardNo(authorizeResource.getCode()).status(0).build();
            tbInsensatePointsAuthRecordService.insertAuthRecord(authRecord);
        }
    }

    @Override
    public void queryWeChatOpenCardInfo(String openId, String userId) {
        for (String appId: mallConfig.getAppIdList()) {
            log.info("AppId: {}", appId);
        }
    }


}
