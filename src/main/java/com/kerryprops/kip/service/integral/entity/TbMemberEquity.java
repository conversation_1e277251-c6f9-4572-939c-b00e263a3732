package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member_equity: 会员权益
 * <AUTHOR>
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_equity")
public class TbMemberEquity implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商场编号
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 会员权益内容（代码）
     */
    @TableField("content")
    private String content;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 集团ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField("create_date")
    private Date updateDate;

    /**
     * 类型  1:会员权益2:优惠信息3:服务协议4:自助积分指南5:微信无感积分6:支付宝无感积分7:客服台积分8:拍照积分
     */
    @TableField("type")
    private Integer type;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 协议名称
     */
    @TableField("protocol_name")
    private String protocolName;

    /**
     * 自助积分指南(1:扫码积分2:微信无感积分3:支付宝无感积分4:客服台积分)
     */
    @TableField("code")
    private Integer code;

}