package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member_benefitsconfig
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员权益配置响应类")
public class TbMemberBenefitsConfigResponse implements Serializable {

    @Schema( description = "权益配置ID")
    private String id;

    @Schema( description = "权益ID")
    private String benefitsId;

    @Schema( description = "商场编号")
    private String mallId;

    @Schema( description = "会员等级")
    private String memberGrade;

    @Schema( description = "权益名称")
    private String name;

    @Schema( description = "副标题")
    private String subTitle;

    @Schema( description = "图标")
    private String logo;

    @Schema( description = "链接类型：0站内 1站外 2无类型 3富文本")
    private String linkType;

    @Schema( description = "内部链接类型：0自定义1商品2券3页面4自定义页面")
    private String insideType;

    @Schema( description = "内部链接url")
    private String insideUrl;

    @Schema( description = "内部链接选中id")
    private String insideId;

    @Schema( description = "二级联动回显")
    private String twoLevelLinkage;

    @Schema( description = "外部链接类型：0h5、1小程序")
    private String outsideType;

    @Schema( description = "外部appid")
    private String outsideAppid;

    @Schema( description = "外部链接名称")
    private String outsideUrlName;

    @Schema( description = "外部链接url")
    private String outsideUrl;

    @Schema( description = "展现形式：0弹框 1新页面")
    private String showType;

    @Schema( description = "权益内容")
    private String content;

    @Schema( description = "集团ID")
    private String groupId;

    @Schema( description = "创建时间")
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

}