package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/20/2022 11:43
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员二级分类标签类")
public class TbAutoSaleMemberTagResponse implements Serializable {

    @Schema( description = "主键ID")
    private String id;

    @Schema( description = "标签名称")
    private String tagName;

    @Schema( description = "标签类型（1：逻辑标签；2：无逻辑标签）")
    private Integer type;
}
