package com.kerryprops.kip.service.integral.webservice.resource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2022 16:32
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionResource implements Serializable {

    @Builder.Default
    private String serviceName = "points-service";

    /**
     * 错误code
     */
    private Integer errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    private String correlationId;

    private String dataMsg;

    @Builder.Default
    private ZonedDateTime timestamp = ZonedDateTime.now();

}
