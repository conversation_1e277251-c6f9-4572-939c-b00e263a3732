package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - loyalty-engine-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/08/2023 16:52
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_bkc_sale_member")
public class TbBkcSaleMember implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 会员号
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * BKC迁移会员类型: 1: 全部BKC,开卡BKC，2: 部分BKC部分KO,开卡BKC，3: 全部BKC,开卡非BKC，4: 部分BKC部分KO,开卡非BKC
     */
    @TableField("sale_case")
    private Integer saleCase;

    /**
     * BKC迁移状态确认, 0:未确认, 1:已确认
     */
    @TableField("confirm_status")
    private Integer confirmStatus;

}
