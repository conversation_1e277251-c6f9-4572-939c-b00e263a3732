package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/13/2023 15:44
 **********************************************************************************************************************/

@Getter
public enum ZIndexModuleSignEnum {

    // 瀑布流
    WATERFALL("waterfall"),
    // 瀑布流Tab
    WATERFALLTAB("waterfallTab"),
    // 自定义UI配置的瀑布流
    SUSPEN("suspen"),
    // 轮播图
    LOOP("loop"),
    // 弹屏广告
    SCREEN("screen"),
    // 标题
    TITLE("title"),
    // 我的页面
    MY("my"),
    BALL("ball"),
    BRANDLOGO("brandLogo"),
    NAVIGATION("navigation"),
    ADVERTISEMENT("advertisement");

    private final String val;

    ZIndexModuleSignEnum(String val) {
        this.val = val;
    }

    /**
     * 我的页面包含首页配置的模块
     * @return
     */
    public static List<String> getIndexConfig() {
        return Stream.of(NAVIGATION, SUSPEN, WATERFALL, ADVERTISEMENT).map(ZIndexModuleSignEnum::getVal).collect(Collectors.toList());
    }

    public static List<String> getMyPageConfig() {
        return Stream.of(NAVIGATION).map(ZIndexModuleSignEnum::getVal).collect(Collectors.toList());
    }

    public static boolean isBallOrWaterFall(String moduleSign) {
        return Stream.of(BALL, WATERFALL).anyMatch(item -> item.getVal().equalsIgnoreCase(moduleSign));
    }

    /**
     * 哪些模块配置了展示时间
     * @param moduleSign
     * @return
     */
    public static boolean hasShowTimeModule(String moduleSign) {
        return Stream.of(TITLE, LOOP, SCREEN, ADVERTISEMENT).anyMatch(item -> item.getVal().equalsIgnoreCase(moduleSign));
    }

}
