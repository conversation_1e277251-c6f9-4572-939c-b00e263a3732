package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/26/2023 11:02
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_project_identity")
public class TbMemberProjectIdentity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("kip_user_id")
    private String kipUserId;

    @TableField("project_id")
    private String projectId;

    @TableField("office")
    private boolean office;

    @TableField("apartment")
    private boolean apartment;

    @TableField("residence")
    private boolean residence;

    @TableField("create_date")
    private Date createDate;

    @TableField("update_date")
    private Date updateDate;

}
