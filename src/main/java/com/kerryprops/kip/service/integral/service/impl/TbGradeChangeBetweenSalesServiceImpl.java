package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.enums.MemberGradeChangeTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbGradeChangeBetweenSales;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.mapper.TbGradeChangeBetweenSalesMapper;
import com.kerryprops.kip.service.integral.mapper.TbMemberGradeChangeDetailMapper;
import com.kerryprops.kip.service.integral.model.dto.MemberYearAmountCalDto;
import com.kerryprops.kip.service.integral.service.TbGradeChangeBetweenSalesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class TbGradeChangeBetweenSalesServiceImpl extends ServiceImpl<TbGradeChangeBetweenSalesMapper, TbGradeChangeBetweenSales> implements TbGradeChangeBetweenSalesService {

    private final TbGradeChangeBetweenSalesMapper tbGradeChangeBetweenSalesMapper;
    private final TbMemberGradeChangeDetailMapper tbMemberGradeChangeDetailMapper;

    @Override
    public TbGradeChangeBetweenSales findByChangeId(String changeId) {
        return tbGradeChangeBetweenSalesMapper.findByChangeId(changeId);
    }

    @Override
    public TbGradeChangeBetweenSales saveDetail(String changeId, Date saleStartTime, Date saleEndTime) {
        TbGradeChangeBetweenSales betweenSales = tbGradeChangeBetweenSalesMapper.findByChangeId(changeId);
        if (Objects.isNull(betweenSales)) {
            betweenSales = TbGradeChangeBetweenSales.builder().changeId(changeId).salesStartTime(saleStartTime).salesEndTime(saleEndTime).build();
            tbGradeChangeBetweenSalesMapper.insert(betweenSales);
        } else {
            betweenSales.setSalesStartTime(saleStartTime);
            betweenSales.setSalesEndTime(saleEndTime);
            tbGradeChangeBetweenSalesMapper.updateById(betweenSales);
        }
        return betweenSales;
    }

    /**
     * 补齐等级变更记录关联的销售信息
     * @param detail: 会员卡等级变更记录
     * @param calDto: 是否是周期年，true:是， false:否，即自然年
     * @param minGrade: group下设置的最低卡等
     */
    @Override
    public TbGradeChangeBetweenSales checkChangeBetweenSales(TbMemberGradeChangeDetail detail, MemberYearAmountCalDto calDto, TbMemberGrade minGrade) {
        // 无等级变更记录或保级、降级执行后降到最低卡等，则无需标记销售区间
        if (Objects.isNull(detail) || StringUtils.equals(detail.getNewGrade(), minGrade.getCode())) {
            return null;
        }
        Date startTime = Boolean.TRUE.equals(calDto.getCycleYear()) ?
                DateUtil.beginOfDay(DateUtil.offsetMonth(detail.getCreateDate(), -12)) :
                DateUtil.beginOfYear(DateUtil.offsetMonth(detail.getCreateDate(), -12));
        TbGradeChangeBetweenSales betweenSales = tbGradeChangeBetweenSalesMapper.findByChangeId(detail.getId());
        if (Objects.isNull(betweenSales)) {
            // 查询上一年是否存在保级、降级执行后的等级变更记录
            List<TbMemberGradeChangeDetail> list = tbMemberGradeChangeDetailMapper.getChangeDetailBetweenTime(detail.getGroupId(),
                    detail.getVipcode(), DateUtil.formatDateTime(startTime), DateUtil.formatDateTime(detail.getCreateDate()),
                    Boolean.TRUE.equals(calDto.getCycleYear()) ? MemberGradeChangeTypeEnum.getRelegationType() : MemberGradeChangeTypeEnum.getNaturalRelegationType());
            if (CollectionUtils.isEmpty(list)) {
                return this.saveDetail(detail.getId(), startTime, detail.getCreateDate());
            } else {
                // 是否存在人工调整卡等的记录
                if (list.stream().anyMatch(item -> MemberGradeChangeTypeEnum.isManualDowngrade(item.getChangeType()))) {
                    TbMemberGradeChangeDetail manualDetail = list.stream().filter(item -> MemberGradeChangeTypeEnum.isManualDowngrade(item.getChangeType()))
                            .findFirst().orElse(null);
                    return Objects.nonNull(manualDetail) ? this.saveDetail(detail.getId(), manualDetail.getCreateDate(), detail.getCreateDate()) : null;
                } else {
                    // 卡等与年初卡等一致且为非最低卡等，则销售区间为上一年的销售区间
                    if (StringUtils.equals(detail.getNewGrade(), calDto.getOrgGrade()) && !StringUtils.equals(calDto.getOrgGrade(), minGrade.getCode())) {
                        TbGradeChangeBetweenSales betweenSales1 = tbGradeChangeBetweenSalesMapper.findByChangeId(list.get(0).getId());
                        if (Objects.nonNull(betweenSales1)) {
                            return this.saveDetail(detail.getId(), betweenSales1.getSalesStartTime(), betweenSales1.getSalesEndTime());
                        } else {
                            return this.saveDetail(detail.getId(), Boolean.TRUE.equals(calDto.getCycleYear()) ?
                                    DateUtil.beginOfDay(DateUtil.offsetMonth(list.get(0).getCreateDate(), -12)) :
                                    DateUtil.beginOfYear(DateUtil.offsetMonth(list.get(0).getCreateDate(), -12)), list.get(0).getCreateDate());
                        }
                    } else {
                        return this.saveDetail(detail.getId(), StringUtils.equals(list.get(0).getNewGrade(), minGrade.getCode()) ?
                                startTime : list.get(0).getCreateDate(), detail.getCreateDate());
                    }
                }
            }
        }
        return betweenSales;
    }
}
