package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.service.TbMemberIdentityService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberIdentityResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/23/2023 11:40
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class KipSyncMemberIdentityListener {

    private final TbMemberIdentityService tbMemberIdentityService;

    /**
     * KIP会员身份信息同步
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.KIP_SYNC_MEMBER_IDENTITY, containerFactory = "crmContainerFactory")
    public void kipSyncMemberIdentityProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("KIP会员身份信息同步消息体为空.");
            return;
        }
        log.info("KipSyncMemberIdentityListener: {}" , msg);
        MemberIdentityResource resource = JsonUtils.stringToObj(msg, MemberIdentityResource.class);
        if (Objects.isNull(resource)) {
            return;
        }
        // KIP会员身份信息同步
        tbMemberIdentityService.kipSyncMemberIdentityProcess(resource);
    }

}
