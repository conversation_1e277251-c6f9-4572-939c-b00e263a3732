package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeRuleMapper
 * @Description 会员等级规则
 * @date 2022/9/27 14:54
 * @Version 1.0
 */
public interface TbMemberGradeRuleMapper extends BaseMapper<TbMemberGradeRule> {

    List<TbMemberGradeRule> queryGradeRulesByGroupIdAndGradeAndRuleTypes(@Param("groupId") String groupId,
                                                                          @Param("grade") String grade,
                                                                          @Param("ruleTypes") List<Integer> ruleTypes);

    List<TbMemberGradeRule> queryGradeRulesByGroupIdAndGradeAndType(@Param("groupId") String groupId,
                                                                         @Param("grade") String grade,
                                                                         @Param("type") Integer type);

    List<TbMemberGradeRule> findGradeRulesByGroupIdAndType(@Param("groupId") String groupId,
                                                                         @Param("type") Integer type);

    List<TbMemberGradeRule> findGradeRulesByGroupIdAndRuleType(@Param("groupId") String groupId,
                                                               @Param("ruleType") Integer ruleType);

    /**
     * 年累计消费金额-周期年
     * @param groupId
     * @param type
     * @return
     */
    boolean isCycleYear(@Param("groupId") String groupId, @Param("type") Integer type);

}
