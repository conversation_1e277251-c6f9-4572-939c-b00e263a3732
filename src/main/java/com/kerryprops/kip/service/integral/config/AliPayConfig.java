package com.kerryprops.kip.service.integral.config;

import cn.hutool.core.util.StrUtil;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayItem;
import com.kerryprops.kip.service.integral.strategy.AliPayStrategy;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝客户端、网关等配置类
 * @createDate 2022/10/21
 * @updateDate 2022/10/21
 */
@Data
@Component
@ConfigurationProperties(prefix = "alipay")
@Slf4j
public class AliPayConfig implements InitializingBean {

    private List<AliPayItem> list;

    public AliPayItem getByName(String name) {
        return list.stream().filter(it -> StringUtils.isNotBlank(it.getName())).filter(it -> StrUtil.split(it.getName(), CommonSeparators.COMMA_SEPARATOR).contains(name)).findFirst().orElse(null);
    }

    public AliPayItem getByAlipayAppId(String alipayAppId) {
        return list.stream().filter(it -> StringUtils.equals(it.getAliPayAppId(), alipayAppId)).findFirst().orElse(null);
    }

    @Override
    public void afterPropertiesSet() {
        for (AliPayItem item : list) {
            if (StringUtils.isBlank(item.getAliPayAppId())) {
                continue;
            }
            AlipayClient alipayClient = new DefaultAlipayClient(item.getGatewayUrl(), item.getAliPayAppId(), item.getAliPayPrivateKey(), "json", StandardCharsets.UTF_8.name(), item.getAlipayPublicKey(), "RSA2");
            AliPayStrategy.registerClient(item.getAliPayAppId(), alipayClient);
        }
    }
}
