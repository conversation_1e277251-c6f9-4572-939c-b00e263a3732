package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.CustomerIdentityDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.BaseMemberStatus;
import com.kerryprops.kip.service.integral.service.HiveVasService;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberIdentityQueryResource;
import com.kerryprops.kip.service.integral.webservice.resource.MemberIdentityResource;
import com.kerryprops.kip.service.integral.webservice.response.MemberIdentityResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/22/2023 09:34
 **********************************************************************************************************************/

@Slf4j
@RestController
@RequestMapping("/member/identity")
@RequiredArgsConstructor
@Tag(name = "会员身份信息同步api")
public class TbMemberIdentityController extends BaseMemberStatus {

    private final ProfileServiceClient profileServiceClient;
    private final RabbitMqService rabbitMqService;
    private final HiveVasService hiveVasService;
    private final TbMemberAssetService tbMemberAssetService;

    @Hidden
    @PutMapping("/kip_sync")
    public void kipSyncMemberIdentity(@RequestBody MemberIdentityResource resource) {
        rabbitMqService.sendMessage(RabbitMqConstant.KIP_SYNC_MEMBER_IDENTITY, JsonUtils.objToString(resource));
    }

    @PostMapping("/current_lbs")
    @Operation(summary="查询会员在查询lbs下的身份信息", method = "POST")
    public MemberIdentityResponse getMemberIdentityInfo(@RequestBody @Valid MemberIdentityQueryResource resource) {
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByVipcodeAndGroupId(resource.getVipcode(), resource.getGroupId());
        // 校验会员状态
        this.checkMemberStatus(tbMemberAsset);
        // 查询对应商场信息
        MallItem mallItem = hiveVasService.getMallByLbsId(resource.getLbsId());
        if (Objects.isNull(mallItem)) {
            return MemberIdentityResponse.builder().office(Boolean.FALSE).apartment(Boolean.FALSE).residence(Boolean.FALSE).build();
        }
        List<CustomerIdentityDto> identityList = profileServiceClient.getIdentityResponse(tbMemberAsset.getKipUserId(), mallItem.getProjectId());
        if (CollectionUtils.isEmpty(identityList)) {
            return MemberIdentityResponse.builder().office(Boolean.FALSE).apartment(Boolean.FALSE).residence(Boolean.FALSE).build();
        }
        return MemberIdentityResponse.builder()
                .office(identityList.stream().anyMatch(CustomerIdentityDto::isOffice))
                .apartment(identityList.stream().anyMatch(CustomerIdentityDto::isApartment))
                .residence(identityList.stream().anyMatch(CustomerIdentityDto::isResidence)).build();
    }

}