package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.service.integral.entity.TbSaleMatchedPromotion;
import com.kerryprops.kip.service.integral.service.TbSaleMatchedPromotionService;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/03/2023 14:32
 **********************************************************************************************************************/

@Slf4j
@Hidden
@RestController
@RequiredArgsConstructor
@RequestMapping("/promotion/integral")
public class TbActivityPromotionIntegralController {

    private final TbSaleMatchedPromotionService tbSaleMatchedPromotionService;

    @GetMapping("/activityNames")
    public Map<String, String> getMatchedPromotions(@RequestParam("adjustIds") List<String> adjustIds) {
        List<TbSaleMatchedPromotion> promotions = tbSaleMatchedPromotionService.findByAdjustIds(adjustIds);
        Map<String, String> names = new HashMap<>(adjustIds.size());
        promotions.stream().collect(Collectors.groupingBy(TbSaleMatchedPromotion::getIntegralAdjustId))
                .forEach((key, val) -> names.put(key, val.stream().map(TbSaleMatchedPromotion::getPromotionName)
                        .collect(Collectors.joining(","))));
        return names;
    }

}
