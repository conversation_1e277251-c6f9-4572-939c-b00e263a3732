package com.kerryprops.kip.service.integral.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 06/27/2024 15:42
 **********************************************************************************************************************/

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "textin")
public class SmallTicketProperties {

    private String appId;

    private String secret;

    private String service;

    private String env;

    /**
     * OCR识别超时时间设置
     */
    private Long timeout;

    /**
     * API访问限制
     */
    private Long accessLimit;

    /**
     * OCR图片外网访问地址
     */
    private String ocrFileExternalDomain;

    /**
     * OCR图片内网访问地址
     */
    private String ocrFileInternalDomain;

    /**
     * 是否是mock
     */
    private Boolean mock;

}
