package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeChangeDetail
 * @Description 用户会员等级变更
 * @date 2022/9/27 15:44
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_grade_change_detail")
public class TbMemberGradeChangeDetail implements Serializable {

    private String id;

    @TableField("group_id")
    private String groupId;

    @TableField("mall_id")
    private String mallId;

    @TableField("authorized_mall_id")
    private String authorizedMallId;

    // 会员昵称
    @TableField("nick_name")
    private String nickName;

    // 手机号
    @TableField("mobile")
    private String mobile;

    //会员号
    @TableField("vipcode")
    private String vipcode;

    //会员旧等级
    @TableField("old_grade")
    private String oldGrade;

    //会员新等级
    @TableField("new_grade")
    private String newGrade;

    // 卡变动类型（0、人工调整-降级；1、人工调整-升级；2、每日消费金额-升级；3、累计消费金额-升级；4、累计消费金额降级；5、入会当日消费-升级；6、会员身份认证-升级；7、累计消费金额-保级；8、每日消费金额-保级；9、会员身份认证-保级；10、会员月累计消费升级；11、会员月累计消费保级；12、系统调整；13、系统自动保级）
    @TableField("change_type")
    private Integer changeType;

    //备注
    @TableField("remark")
    private String remark;

    @TableField("create_date")
    private Date createDate;

    @TableField("create_user")
    private String createUser;

    private transient String memberId;

    private transient String kipUserId;

}
