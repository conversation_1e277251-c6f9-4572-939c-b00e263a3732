package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member_benefits
 * <AUTHOR>

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_benefit")
public class TbMemberBenefit implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商场编号
     */
    @TableField("mallid")
    private String mallId;

    /**
     * 会员等级
     */
    @TableField("member_grade")
    private String memberGrade;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 集团ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 创建者
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新者
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 是否删除;1删除/0正常
     */
    @TableField("is_del")
    private Integer isDel;

}