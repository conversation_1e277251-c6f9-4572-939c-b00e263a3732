package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.webservice.response.BadgeListResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "badge-service", url = "${kerry.services.badge-service:default}")
public interface BadgeServiceClient {

    @RequestMapping("/badge/my_page_list")
    BadgeListResponse myPageBadgeList(@RequestParam("brandId") String groupId,
                                      @RequestParam("lbsId") String mallId,
                                      @RequestParam(value = "vipcode", required = false) String vipcode);

}
