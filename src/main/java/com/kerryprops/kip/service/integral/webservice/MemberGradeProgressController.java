package com.kerryprops.kip.service.integral.webservice;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeEffectiveConfig;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.model.dto.MemberGradeProgressSwitchDto;
import com.kerryprops.kip.service.integral.service.TbMemberGradeEffectiveConfigService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeRuleProgressService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberGradeProgressSwitchResource;
import com.kerryprops.kip.service.integral.webservice.resource.MemberUpgradeAmountResource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/member/grade/progress")
@Tag(name = "会员等级进度条API")
public class MemberGradeProgressController {

    private final TbMemberGradeRuleProgressService tbMemberGradeRuleProgressService;
    private final TbMemberGradeEffectiveConfigService tbMemberGradeEffectiveConfigService;
    private final Mapper mapper;

    @GetMapping("/get_progress_bar")
    @Operation(summary="获取会员升级进度条", method = "GET")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    })
    public int getProgressBar(@CurrentUser LoginUser loginUser,
                              @RequestParam(value = "ruleId", required = false) String ruleId) {
        // 判断入参
        if (StringUtils.isAnyBlank(loginUser.getBrandId(), loginUser.getCId())) {
            throw BizException.error(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR);
        }
        return tbMemberGradeRuleProgressService.getProgressBar(loginUser, ruleId);
    }

    @GetMapping("/get_relegation_copywriter")
    @Operation(summary="获取会员保级文案", method = "GET")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    })
    public String getRelegationCopywriter(@CurrentUser LoginUser loginUser) {
        if (StringUtils.isBlank(loginUser.getBrandId()) || StringUtils.isBlank(loginUser.getPhoneNumber())) {
            log.error("获取会员保级文案参数缺失");
            return null;
        }
        return tbMemberGradeRuleProgressService.getRelegationCopywriter(loginUser);
    }

    @GetMapping("/get_upgrade_copywriter")
    @Operation(summary="获取会员升级文案", method = "GET")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    })
    public String getUpgradeCopywriter(@CurrentUser LoginUser loginUser,
                                       @RequestParam(value = "grade", required = false) String grade) {
        // 判断入参
        if (StringUtils.isBlank(loginUser.getBrandId()) || StringUtils.isBlank(loginUser.getPhoneNumber())) {
            log.error("获取会员升级文案参数缺失");
            return StringUtils.EMPTY;
        }
        return tbMemberGradeRuleProgressService.getUpgradeCopywriter(loginUser, grade);
    }

    @GetMapping("/get_upgrade_money")
    @Operation(summary="获取会员升级剩余金额", method = "GET")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    })
    public MemberUpgradeAmountResource getUpgradeMoney(@CurrentUser LoginUser loginUser, @RequestParam(value = "ruleId", required = false) String ruleId) {
        // 查询升级还剩余金额
        MemberUpgradeAmountResource resource = tbMemberGradeRuleProgressService.getUpgradeMoney(loginUser);
        if (Objects.nonNull(resource)) {
            TbMemberGradeEffectiveConfig config = tbMemberGradeEffectiveConfigService.findByGroupId(loginUser.getBrandId());
            // 判断groupId是否开启保级降级定时任务
            resource.setEnableDownGrade(Objects.isNull(config) ? Boolean.FALSE : config.getEnableDowngradeJob());
        }
        return resource;
    }

    @GetMapping("/switch_list")
    @Operation(summary="会员成长进度条计算方式查询", method = "GET")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    })
    public List<MemberGradeProgressSwitchResource> getMemberGrowthProgressSwitchMethod(@CurrentUser LoginUser loginUser) {
        List<MemberGradeProgressSwitchDto> dtoList = tbMemberGradeRuleProgressService.memberGradeComputeMethod(loginUser);
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        return dtoList.stream().map(it -> mapper.map(it, MemberGradeProgressSwitchResource.class)).toList();
    }



}
