package com.kerryprops.kip.service.integral.service.impl;

import com.kerryprops.kip.service.integral.entity.TbCfgAuthorizerWx;
import com.kerryprops.kip.service.integral.mapper.TbCfgAuthorizerWxMapper;
import com.kerryprops.kip.service.integral.service.TbCfgAuthorizerWxService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/06/2023 16:28
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class TbCfgAuthorizerWxServiceImpl implements TbCfgAuthorizerWxService {

    private final TbCfgAuthorizerWxMapper tbCfgAuthorizerWxMapper;

    @Override
    public List<TbCfgAuthorizerWx> findByEntity(TbCfgAuthorizerWx wx) {
        return tbCfgAuthorizerWxMapper.findByEntity(wx);
    }
}
