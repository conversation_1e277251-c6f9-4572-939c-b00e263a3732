package com.kerryprops.kip.service.integral.service.defered;

import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.OcrErrorCodeMappingEnum;
import com.kerryprops.kip.service.integral.service.RedisService;
import com.kerryprops.kip.service.integral.webservice.response.OcrPhotoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.async.DeferredResult;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/24/2024 09:12
 **********************************************************************************************************************/

@Slf4j
public class DeferredResultTimeOut implements Runnable {

    private final DeferredResult<OcrPhotoResponse> result;
    private final RedisService redisService;
    private final String key;

    public DeferredResultTimeOut(DeferredResult<OcrPhotoResponse> deferredResult, String cacheKey, RedisService redisService) {
        this.result = deferredResult;
        this.key = cacheKey;
        this.redisService = redisService;
    }

    @Override
    public void run() {
        log.info("OCR小票识别请求超时: {}", key);
        // 缓存内设置key用于标识识别超时(设置为7*24小时)，OCR回传后自动提交积分
        redisService.setValue(String.format(RedisCacheKey.OCR_PHOTO_IDENTITY_TIMEOUT, key), key, 168L);
        // 前端错误信息提示
        OcrPhotoResponse response = OcrPhotoResponse.builder().code(OcrErrorCodeMappingEnum.ERR_60.getOcrErrorCode())
                .msg(OcrErrorCodeMappingEnum.ERR_60.getErrorMsg()).taskId(key).build();
        result.setErrorResult(response);
    }
}
