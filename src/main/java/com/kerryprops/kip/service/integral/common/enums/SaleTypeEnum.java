package com.kerryprops.kip.service.integral.common.enums;

import cn.hutool.core.text.CharSequenceUtil;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description     销售类型枚举
 * @createDate 2022/9/21
 * @updateDate 2022/9/21
 */
@Getter
public enum SaleTypeEnum {

    /**
     * CRM消费
     */
    CRM("1", "销售积分", "XS0002", "XS00030,XS00031"),

    /**
     * POS销售
     */
    POS("2", "POS销售", "XS0002", "XS00030,XS0022"),

    /**
     * 拍照积分
     */
    TICKET("3", "拍照积分", "HF0054", ""),

    /**
     * 扫码积分
     */
    SCAN("4", "扫码积分", "HF0053", ""),

    /**
     * 口碑
     */
    WOM("5", "口碑销售", "", ""),

    /**
     * 线上商城
     */
    MALL("6", "线上商城", "REWARDS_MALL", "REFUND_REWARDS_MALL"),

    /**
     * 微信无感支付积分
     */
    WECHAT("7", "微信无感支付积分", "XS0068", "XS0069"),

    /**
     * 支付宝无感积分
     */
    ALIPAY("8", "支付宝商圈", "XS0066", "XS0067"),

    /**
     * 优惠买单
     */
    KERRY_PAY("9", "优惠买单", "KERRY_PAY,YM0160", "REFUND_KERRY_PAY,YM0161"),
    ;

    private final String value;
    private final String desc;
    private final String dictType;
    private final String refundDictType;

    SaleTypeEnum(String value, String desc, String dictType, String refundDictType) {
        this.value = value;
        this.desc = desc;
        this.dictType = dictType;
        this.refundDictType = refundDictType;
    }

    /**
     * 是否是OCR拍照积分
     * @param saleType
     * @return
     */
    public boolean isTicket(String saleType) {
        if (StringUtils.isBlank(saleType)) {
            return false;
        }
        return StringUtils.equals(TICKET.getValue(), saleType);
    }

    public static String getDescByVale(String value) {
        String desc = "";
        if (StringUtils.isBlank(value)) {
            return desc;
        }
        for (SaleTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                desc = typeEnum.getDesc();
                break;
            }
        }
        return desc;
    }

    public static String getDictTypeByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return "";
        }
        String dictType = "";
        for (SaleTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                dictType = typeEnum.getDictType();
                break;
            }
        }
        return dictType;
    }

    /**
     * 查找该笔积分变更是否属于销售调整
     * @param value
     * @return
     */
    public static SaleTypeEnum getByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        SaleTypeEnum saleTypeEnum = null;
        for (SaleTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                saleTypeEnum = typeEnum;
                break;
            }
        }
        return saleTypeEnum;
    }

    /**
     * 通过字典值获取销售类型
     * @param dictType
     * @return
     */
    public static SaleTypeEnum getSaleTypeByDictType(String dictType) {
        if (StringUtils.isBlank(dictType)) {
            return null;
        }
        SaleTypeEnum val = null;
        for (SaleTypeEnum typeEnum : values()) {
            if (StringUtils.isNotBlank(typeEnum.getDictType()) && CharSequenceUtil.split(typeEnum.getDictType(), CommonSeparators.COMMA_SEPARATOR).contains(dictType)) {
                val = typeEnum;
                break;
            }
            if (StringUtils.isNotBlank(typeEnum.getRefundDictType()) && CharSequenceUtil.split(typeEnum.getRefundDictType(), CommonSeparators.COMMA_SEPARATOR).contains(dictType)) {
                val = typeEnum;
                break;
            }
        }
        return val;
    }

    public static List<String> getNormalSaleTypeValue() {
        List<String> valueList = new ArrayList<>();
        for (SaleTypeEnum typeEnum : values()) {
            if (IntegralConstant.NORMAL_SALE_TYPE.contains(typeEnum.getValue())) {
                valueList.add(typeEnum.getValue());
            }
        }
        return valueList;
    }

    public static List<String> getNormalSaleTypeValueForBkc() {
        List<String> valueList = new ArrayList<>();
        for (SaleTypeEnum typeEnum : values()) {
            if (Arrays.asList(SaleTypeEnum.CRM.getValue(), SaleTypeEnum.TICKET.getValue(), SaleTypeEnum.SCAN.getValue()).contains(typeEnum.getValue())) {
                valueList.add(typeEnum.getValue());
            }
        }
        return valueList;
    }

}
