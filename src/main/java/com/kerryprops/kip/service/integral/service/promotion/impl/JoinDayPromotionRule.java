package com.kerryprops.kip.service.integral.service.promotion.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MemberSaleMonthNumberQueryDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbMemberGradeChangeDetailService;
import com.kerryprops.kip.service.integral.service.TbMemberGradeService;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC 首次入会
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:34
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class JoinDayPromotionRule implements PromotionRule {

    private final TbSalesDetailService tbSalesDetailService;
    private final TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    private final TbMemberGradeService tbMemberGradeService;

    @Override
    public String getRuleType() {
        return "8";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        TbMemberAsset member = dto.getMember();
        if (Objects.isNull(member.getJoinTime())) {
            return false;
        }
        // 过滤出该条件的首次入会配置信息
        TbActivityPromotionJoinvip joinvip = vips.stream().filter(it -> Objects.equals(it.getPromotionConditionId(), condition.getId())).findFirst().orElse(null);
        if (Objects.isNull(joinvip)) {
            return false;
        }
        // 次日规则直接返回false
        if (StringUtils.isBlank(joinvip.getMemberGrade())) {
            return false;
        }
        // 会员等级是否满足
        List<String> grades = new ArrayList<>();
        if (StringUtils.isNotBlank(joinvip.getMemberGrade())) {
            if (joinvip.getMemberGrade().contains(CommonSeparators.WELL_SEPARATOR)) {
                grades = StrUtil.split(joinvip.getMemberGrade(), CommonSeparators.WELL_SEPARATOR);
            } else {
                grades = StrUtil.split(joinvip.getMemberGrade(), CommonSeparators.COMMA_SEPARATOR);
            }
        }
        String maxGrade = tbMemberGradeChangeDetailService.getJoinDayMaxGrade(member.getVipcode(), dto.getGroupId(), DateUtil.formatDate(member.getJoinTime()));
        // 为空就获取当前集团下最小等级
        if (StringUtils.isBlank(maxGrade)) {
            maxGrade = tbMemberGradeService.getGroupMiniGradeCode(dto.getGroupId());
        }
        // 当日
        if (0 == joinvip.getJoinTimeType()) {
            return grades.contains(maxGrade) && StringUtils.equals(DateUtil.formatDate(member.getJoinTime()), DateUtil.formatDate(new Date()));
        } else {
            if (!grades.contains(maxGrade)) {
                return false;
            }
            // 次日 - 开始时间是第二天开始时间
            DateTime startDate = DateUtil.beginOfDay(DateUtil.offsetDay(member.getJoinTime(), 1));
            // 次日 - 结束时间是第二天开始时间+配置的多少天内
            DateTime endDate = DateUtil.beginOfDay(DateUtil.offsetDay(startDate, joinvip.getJoinTime()));
            // 销售日期大于统计销售笔数的结束时间，则直接返回
            if (saleDate.getTime() > endDate.getTime()) {
                return false;
            }
            MemberSaleMonthNumberQueryDto queryDto = MemberSaleMonthNumberQueryDto.builder().startDate(DateUtil.formatDateTime(startDate))
                    .endDate(DateUtil.formatDateTime(endDate)).vipcode(member.getVipcode()).groupId(dto.getGroupId()).build();
            if (!IntegralConstant.DEFAULT_STORE.equalsIgnoreCase(condition.getShopId())) {
                queryDto.setShopNos(CharSequenceUtil.split(condition.getShopId(), CommonSeparators.WELL_SEPARATOR));
            }
            // 此时是第五笔销售记录，但是数据库里面只存在4笔，所以要加上现在的这一笔
            int salesNumber = 1 + tbSalesDetailService.queryMemberMonthSalesNumber(queryDto);
            // 5次消费后前10次 (5,6,7,8,9,10实时给，1,2,3,4补发积分)
            return salesNumber > joinvip.getSaleNum() - 1 && salesNumber <= joinvip.getFormerNum();
        }
    }
}
