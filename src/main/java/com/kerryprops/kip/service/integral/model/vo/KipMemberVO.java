package com.kerryprops.kip.service.integral.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户回写kip会员信息对象
 * @author: ZongXin
 * @create: 2021-10-13 14:38
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KipMemberVO implements Serializable {
    /**
     * 详细地址
     */
    private String address;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 是否有宝宝：0：未知，1：有，2：无
     */
    private int babyStatus;
    /**
     * 出生年月
     */
    private Date birthDate;
    /**
     * 证件号码
     */
    private String certificateNum;
    /**
     * 证件类型（1：身份证、2：驾驶证、3：护照、4：其他证件
     */
    private int certificateType;
    /**
     * 市地址
     */
    private String cityAddress;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 区地址
     */
    private String districtAddress;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 性别，0：未知，1：男，2：女
     */
    private int gender;
    /**
     * 婚姻状态：0：未知，1：已婚，2：未婚
     */
    private int maritalStatus;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 手机号码
     */
    private String phoneNumber;
    /**
     * 手机号区号
     */
    private String areaCode;
    /**
     * 省地址
     */
    private String provinceAddress;
    /**
     * 用户姓名
     */
    private String realName;
    /**
     * 微信unionId
     */
    private String unionId;
    /**
     * 用户id
     */
    private String userId;
    private String lbsId;
    private String brandId;

    /**
     * 家庭电话
     */
    private String homePhone;
}
