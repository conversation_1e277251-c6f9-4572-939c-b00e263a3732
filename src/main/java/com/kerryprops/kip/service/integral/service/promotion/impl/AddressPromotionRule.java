package com.kerryprops.kip.service.integral.service.promotion.impl;

import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:32
 **********************************************************************************************************************/

@Slf4j
@Component
public class AddressPromotionRule implements PromotionRule {

    @Override
    public String getRuleType() {
        return "7";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        TbMemberAsset member = dto.getMember();
        if (StringUtils.isBlank(condition.getPromotionConditionContent()) || StringUtils.isAllBlank(member.getProvinceAddress(), member.getCityAddress(), member.getDistrictAddress())) {
            return false;
        }
        String ruleAddress = condition.getPromotionConditionContent().replaceAll("/", " ").replaceAll(" ", "");
        String address = member.getProvinceAddress() + member.getCityAddress() + member.getDistrictAddress();
        // 实际设置值为省市区三级
        return ruleAddress.contains(address) || address.contains(ruleAddress);
    }
}
