package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopResource;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 16:08
 **********************************************************************************************************************/

public interface TbOnlineShopConfigService extends IService<TbOnlineShopConfig> {

    /**
     * 计算总数
     * @param resource: resource
     * @return int int
     */
    int onlineShopTotal(TbOnlineShopResource resource);

    /**
     * 分页查询
     * @param resource: resource
     * @return list list
     */
    List<TbOnlineShopConfig> onlineShopPage(TbOnlineShopResource resource);

    /**
     * 保存和修改
     * @param list: list
     */
    void saveOrUpdateOnlineShopConfig(List<TbOnlineShopConfig> list);

    /**
     * 根据id删除
     * @param ids: ids
     */
    void removeOnlineShopConfig(Long[] ids);

    /**
     * 查询信息
     * @param configParam: configParam
     * @return list list
     */
    List<TbOnlineShopConfig> getOnlineShop(TbOnlineShopConfig configParam);

    /**
     * 查询线上商城积分比例设置信息
     * @param mallId: 商城id
     * @param grade: 会员等级code
     * @param businessType: 线上商城业务类型
     * @return config config
     */
    TbOnlineShopConfig findByMallIdAndGradeAndBusinessType(String mallId, String grade, String businessType);

}
