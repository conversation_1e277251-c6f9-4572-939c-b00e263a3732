package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.text.CharSequenceUtil;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.entity.TbAutoSaleMemberTag;
import com.kerryprops.kip.service.integral.entity.TbTagMemberNologic;
import com.kerryprops.kip.service.integral.service.CrmVipcodeService;
import com.kerryprops.kip.service.integral.service.TbAutoSaleMemberTagService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberBoundTagResponse;
import com.kerryprops.kip.service.integral.webservice.resource.TbAutoSaleMemberTagResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/20/2022 11:06
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/member/tags")
@RestController
@RequiredArgsConstructor
@Tag(name = "会员标签相关api")
public class TbMemberTagController {

    private final TbAutoSaleMemberTagService tbAutoSaleMemberTagService;
    private final CrmVipcodeService crmVipcodeService;
    private final Mapper mapper;

    @GetMapping("/list")
    @Operation(summary="完善信息-二级分类标签列表", method = "GET")
    @Parameters({
            @Parameter(name = "secSortId", description = "二级分类标签id", required = true)
    })
    public List<TbAutoSaleMemberTagResponse> getSecondMemberTagList(@RequestParam("secSortId") String secSortId) {
        List<TbAutoSaleMemberTag> tagList = tbAutoSaleMemberTagService.getBySecSortIdAndType(secSortId, 2);
        return tagList.stream().map(this::parseItem).toList();
    }

    /**
     * 数据类型转化
     * @param tag
     * @return
     */
    private TbAutoSaleMemberTagResponse parseItem(TbAutoSaleMemberTag tag) {
        TbAutoSaleMemberTagResponse response = mapper.map(tag, TbAutoSaleMemberTagResponse.class);
        response.setId(tag.getId() + "");
        return response;
    }

    @GetMapping("/bound")
    @Operation(summary="完善信息-会员已绑定的标签信息", method = "GET")
    public List<MemberBoundTagResponse> getMemberTagBound(@CurrentUser LoginUser loginUser) {
        TbTagMemberNologic logic = tbAutoSaleMemberTagService.getByGroupIdAndVipcode(loginUser.getBrandId(),
                crmVipcodeService.getVipcode(loginUser.getBrandId(), loginUser.getCId()));
        if (Objects.isNull(logic) || StringUtils.isBlank(logic.getTagIds())) {
            return Collections.emptyList();
        }
        List<Long> tagIds = CharSequenceUtil.split(logic.getTagIds(), CommonSeparators.WELL_SEPARATOR).stream().filter(StringUtils::isNotBlank).map(Long::parseLong).distinct().toList();
        List<TbAutoSaleMemberTag> tagList = tbAutoSaleMemberTagService.getByIds(tagIds);
        if (CollectionUtils.isEmpty(tagList)) {
            return Collections.emptyList();
        }
        List<MemberBoundTagResponse> list = new ArrayList<>(8);
        tagList.stream().collect(Collectors.groupingBy(TbAutoSaleMemberTag::getSecSortId)).forEach((key, val) -> {
            MemberBoundTagResponse response = MemberBoundTagResponse.builder().secSortId(key+"")
                    .tagList(val.stream().map(this::parseItem).toList()).build();
            list.add(response);
        });
        return list;
    }

}
