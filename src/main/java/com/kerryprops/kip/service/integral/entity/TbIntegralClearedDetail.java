package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_integral_cleared_detail
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_integral_cleared_detail")
public class TbIntegralClearedDetail implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 积分清除规则id
     */
    @TableField("integral_clear_id")
    private String integralClearId;

    /**
     * 会员号
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 当前积分
     */
    @TableField("current_integral")
    private Double currentIntegral;

    /**
     * 所需清除积分
     */
    @TableField("clear_integral")
    private Double clearIntegral;

    /**
     * 清除时间
     */
    @TableField("clear_date")
    private Date clearDate;

    /**
     * 商场id
     */
    @TableField("mallid")
    private String mallid;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 会员等级
     */
    @TableField("grade")
    private String grade;

}