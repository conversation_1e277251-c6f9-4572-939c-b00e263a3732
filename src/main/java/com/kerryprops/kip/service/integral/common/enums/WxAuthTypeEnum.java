package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 微信授权类型枚举类
 * @createDate 2022/9/20
 * @updateDate 2022/9/20
 */
@Getter
public enum WxAuthTypeEnum {


    /**
     * 会员开卡(进卡包) + 未授权会员积分服务
     */
    REGISTERED_MODE ("REGISTERED_MODE"),

    /**
     * 会员开卡(进卡包）+授权会员积分服务
     */
    REGISTERED_AND_AUTHORIZATION_MODE("REGISTERED_AND_AUTHORIZATION_MODE"),
    ;

    private final String value;

    WxAuthTypeEnum(String value) {
        this.value = value;
    }
}
