package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbQrCodeRecord;
import com.kerryprops.kip.service.integral.model.dto.BillInfoRpcDto;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/11/2022 14:07
 **********************************************************************************************************************/
public interface TbQrCodeRecordService extends IService<TbQrCodeRecord> {

    TbQrCodeRecord getByQrCode(String qrCode);

    void saveOrUpdate(String qrCode, BillInfoRpcDto dto);

}
