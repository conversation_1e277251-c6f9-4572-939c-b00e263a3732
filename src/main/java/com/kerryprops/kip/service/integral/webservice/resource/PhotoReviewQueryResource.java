package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/10/2023 14:16
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "积分审核查询")
public class PhotoReviewQueryResource implements Serializable {

    @Schema( description = "商场id")
    private String mallId;

    @Schema( description = "集团id")
    private String groupId;

    @Schema( description = "会员号")
    private String vipcode;

    @Schema( description = "手机号")
    private String mobile;

    @Schema( description = "审核状态 1待审核 2审核通过 3审核未通过")
    private List<String> state;

    @Schema( description = "店铺号")
    private String shopNo;

    @Schema( description = "审核查询号")
    private String qrcode;

    @Schema( description = "小票号")
    private String serialNum;

    @Schema( description = "审核开始时间")
    private String examineStartTime;

    @Schema( description = "审核结束时间")
    private String examineEndTime;

    @Schema( description = "上传开始时间")
    private String uploadStartTime;

    @Schema( description = "上传结束时间")
    private String uploadEndTime;

    @Schema( description = "消费金额-开始")
    private Double moneyStart;

    @Schema( description = "消费金额-结束")
    private Double moneyEnd;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 偏移量
     */
    private Integer offset;

    /**
     * 分批查询出来，每批数据量的大小
     */
    private Integer size;

    @Schema( description = "降序排序字段，对应给到的数据库字段")
    private List<String> desc;

    @Schema( description = "升序排序字段，对应给到的数据库字段")
    private List<String> asc;

}
