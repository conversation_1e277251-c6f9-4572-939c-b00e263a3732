package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/21/2022 16:29
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberSaleMonthNumberQueryDto implements Serializable {

    private String groupId;

    private String vipcode;

    private String startDate;

    private String endDate;

    /**
     * 店铺号
     */
    private List<String> shopNos;

}
