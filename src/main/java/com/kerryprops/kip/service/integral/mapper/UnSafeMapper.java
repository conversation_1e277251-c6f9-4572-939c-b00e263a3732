package com.kerryprops.kip.service.integral.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.context.annotation.Profile;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * @description 不安全的mapper（请勿使用）
 * <AUTHOR>
 * @date 2022-09-16
 */
@Mapper
@Deprecated
public interface UnSafeMapper {

    /**
     * @param sql 要执行的sql
     * @return     sql执行结果（请勿使用）
     */
    @Select("${sql}")
    @Deprecated
    List<LinkedHashMap<String,Object>> unsafeRunAnySql(@Param("sql") String sql);

}