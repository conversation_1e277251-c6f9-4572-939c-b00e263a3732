package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbPointsIntercept;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.TbPointsInterceptQueryDto;
import com.kerryprops.kip.service.integral.webservice.response.TbInterceptResponse;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 14:02
 **********************************************************************************************************************/
public interface TbPointsInterceptApproveService extends IService<TbPointsIntercept> {

    /**
     * 判断销售数据是否会被积分拦截
     *
     * @param points points
     * @return Integer int
     */
    TbInterceptResponse checkSaleIsIntercept(SalesAutoPointsDto points);

    /**
     * 保存积分拦截记录
     *
     * @param intercept intercept
     */
    void savePointsIntercept(TbPointsIntercept intercept);

    /**
     * 获取总数
     * @param queryDto queryDto
     * @return int int
     */
    int getTotal(TbPointsInterceptQueryDto queryDto);

    /**
     * 获取记录
     * @param queryDto queryDto
     * @return list list
     */
    List<TbPointsIntercept> getPageData(TbPointsInterceptQueryDto queryDto);

    /**
     * 获取记录
     * @param queryDto queryDto
     * @return list list
     */
    TbPointsIntercept getInterceptDto(TbPointsInterceptQueryDto queryDto);
}
