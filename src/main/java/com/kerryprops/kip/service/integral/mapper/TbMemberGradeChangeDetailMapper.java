package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeChangeDetailMapper
 * @Description 会员等级变更细节
 * @date 2022/9/27 15:49
 * @Version 1.0
 */
public interface TbMemberGradeChangeDetailMapper extends BaseMapper<TbMemberGradeChangeDetail> {

    TbMemberGradeChangeDetail queryChangeMaxByGroupIdAndVipcode(@Param("groupId") String groupId, @Param("vipcode") String vipcode);

    /**
     * 保级降级变更记录内生成的最大等级变更记录
     * @param groupId
     * @param vipcode
     * @param overDate
     * @return
     */
    TbMemberGradeChangeDetail getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(@Param("groupId") String groupId, @Param("vipcode") String vipcode, @Param("overDate") String overDate);

    List<TbMemberGradeChangeDetail> getChangeDetailList(@Param("groupId") String groupId, @Param("vipcode") String vipcode);

    TbMemberGradeChangeDetail queryMemberJoinTimeMaxGradeChangeItem(@Param("groupId") String groupId, @Param("vipcode") String vipcode,
                                                 @Param("joinTime") String joinTime);

    List<TbMemberGradeChangeDetail> queryDetailByGroupIdAndDate(@Param("groupId") String groupId,
                                                                @Param("createDate") String createDate);

    int updateChangeDate(TbMemberGradeChangeDetail detail);

    /**
     * 会员销售发生退款时，查询是否存在等级变更记录
     * @param groupId
     * @param vipcode
     * @param saleDate
     * @return
     */
    List<TbMemberGradeChangeDetail> queryChangeGradeListBySaleDate(@Param("groupId") String groupId, @Param("vipcode") String vipcode, @Param("saleDate") String saleDate);

    /**
     * 查询会员年初的保级记录
     * @param groupId
     * @param vipcode
     * @param createDate
     * @param changeTypes
     * @return
     */
    TbMemberGradeChangeDetail getMaxRelegationRecordByDate(@Param("groupId") String groupId,
                                                           @Param("vipcode") String vipcode,
                                                           @Param("createDate") String createDate,
                                                           @Param("changeTypes") List<Integer> changeTypes);

    /**
     * 查询大于指定日期的等级变更记录列表
     * @param groupId
     * @param vipcode
     * @param createDate
     * @param changeTypes
     * @return
     */
    List<TbMemberGradeChangeDetail> getChangeDetailGtCreateDate(@Param("groupId") String groupId,
                                                                @Param("vipcode") String vipcode,
                                                                @Param("createDate") String createDate,
                                                                @Param("changeTypes") List<Integer> changeTypes);

    /**
     * 查询大于指定日期的等级变更记录列表
     * @param groupId
     * @param vipcode
     * @param startTime
     * @param endTime
     * @param changeTypes
     * @return
     */
    List<TbMemberGradeChangeDetail> getChangeDetailBetweenTime(@Param("groupId") String groupId,
                                                                @Param("vipcode") String vipcode,
                                                                @Param("startTime") String startTime,
                                                               @Param("endTime") String endTime,
                                                                @Param("changeTypes") List<Integer> changeTypes);

    /**
     * 会员销售发生退款时，查询该销售前的最新的等级变更记录
     * @param groupId
     * @param vipcode
     * @param saleDate
     * @return
     */
    TbMemberGradeChangeDetail queryNearChangeGradeBySaleDate(@Param("groupId") String groupId, @Param("vipcode") String vipcode, @Param("saleDate") String saleDate);

    /**
     * 查询入会日的会员最终等级
     * @param groupId groupId
     * @param vipcode vipcode
     * @param joinDay joinDay
     * @return grade
     */
    String getJoinDayMaxGrade(@Param("vipcode") String vipcode, @Param("groupId") String groupId, @Param("joinDay") String joinDay);
}
