package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/21/2024 10:46
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "积分抵现前端请求金额类")
public class PointsRedemptionResource implements Serializable {

    @NotBlank(message = "租户id不能为空")
    @Schema(description = "租户id")
    private String tenantId;

    @NotNull(message = "抵现金额不能为空")
    @Schema(description = "扣除优惠后的抵现金额或自定义金额")
    private BigDecimal amount;

}
