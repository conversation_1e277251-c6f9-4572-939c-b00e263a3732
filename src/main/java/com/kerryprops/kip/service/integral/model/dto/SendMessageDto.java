package com.kerryprops.kip.service.integral.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - saas
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/09/2022 09:10
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendMessageDto implements Serializable {

    /**
     * 会员id，不能为空
     */
    @Schema(name = "会员id")
    private String memberId;

    /**
     * 品牌id
     */
    private String groupId;

    /**
     *  商场id，不能为空
     */
    @Schema(name = "商场id")
    private String mallId;

    /**
     * 模板类型，不能为空
     */
    @Schema(name = "模板类型(1、公众号；2、小程序)")
    private Integer templateType;

    /**
     * 消息类型，不能为空
     */
    @Schema(name = "消息类型(4:会员等级变更; 5:积分调整; 14: 积分清零提醒短信; 16:新注册用户; 17: 注册用户次日提醒; 18: 拍照积分驳回; 19:商圈快速积分开通提醒;")
    private String messageType;

    @Schema(name = "模板活动ID")
    private String templateActivityid;

    @Schema(name = "会员openID")
    private String openId;

    @Schema(name = "积分调整类型")
    private String dictType;

    @Schema(name = "积分调整名称，非必填，代码内会重新查询补充信息")
    private String dictName;

    /**
     * messageType = 5时，需传该字段，积分调整数量，如不传默认为0
     */
    @Schema(name = "积分调整数量")
    private String number;

    /**
     * 如果提前能知道appid，就传，否则不需要传该参数
     */
    @Schema(name = "小程序或公众号appid")
    private String appId;

    /**
     * 积分变更/等级变更发生的时间
     */
    private String updateDate;

    @Schema(name = "商场名称")
    private String mallName;

    /**
     * 会员等级描述
     */
    private String memberGradeDesc;

    /**
     * 短信内容
     */
    private String smsContent;

    /**
     * 楼盘id
     */
    private String projectId;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 会员昵称
     */
    private String nickName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 当前积分
     */
    private int currentIntegral;

    /**
     * 会员号
     */
    private String vipcode;

}
