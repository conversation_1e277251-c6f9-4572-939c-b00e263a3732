package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbPointClearedDetail;
import org.apache.ibatis.annotations.Param;

/***********************************************************************************************************************
 * Project - loyalty-engine-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/06/2023 17:23
 **********************************************************************************************************************/
public interface TbPointClearedDetailMapper extends BaseMapper<TbPointClearedDetail> {

    TbPointClearedDetail getDetailByIntegralClearId(String integralClearId);

    TbPointClearedDetail getLatestMemberClearIntegral(@Param("groupId") String groupId, @Param("vipcode") String vipcode);

}
