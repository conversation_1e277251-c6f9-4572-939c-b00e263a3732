package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 06/27/2024 10:43
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmallTicketCallbackResource implements Serializable {

    /**
     * 报错时，错误码
     */
    private Integer code = 0;

    /**
     * 错误信息
     */
    private String msg;

    /**
     * 自动识别任务id
     */
    @NotBlank(message = "任务id不能为空.")
    @JsonProperty(value = "task_id")
    private String taskId;

    /**
     * 嘉里系统生成的唯一单号
     */
    @JsonProperty(value = "outer_id")
    private String outerId;

    /**
     * 是否处理正常，true 成功  false 失败
     */
    private Boolean isPass;

    /**
     * 自动审核标识，true 表示自动审核，其他情况表示人工审核，boolean
     */
    private Boolean isRobot;

    /**
     * 商场 code，对应嘉里商场列表接口里的 mallCode，string
     */
    private String mall;

    /**
     * 商户 code，对应嘉里商户列表接口里的 shopCode，string
     */
    private String shop;

    /**
     * 金额，形如 19.99，string
     */
    private String amount;

    /**
     * 小票单号，string
     */
    private String ticketNo;

    /**
     * 交易时间，形如 2023-11-23 13:14:15，string
     */
    private String transTime;

    /**
     * 异常原因
     */
    private SmallTicketRejectResource rejectReason;

    /**
     * OCR识别成功
     * @return
     */
    public boolean success() {
        return Objects.nonNull(this.isPass) && Boolean.TRUE.equals(this.isPass);
    }

    /**
     * OCR识别失败
     * @return
     */
    public boolean fail() {
        return Objects.isNull(this.isPass) || Boolean.FALSE.equals(this.isPass);
    }

}
