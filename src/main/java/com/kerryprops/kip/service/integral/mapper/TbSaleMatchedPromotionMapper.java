package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbSaleMatchedPromotion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/17/2023 16:47
 **********************************************************************************************************************/
public interface TbSaleMatchedPromotionMapper extends BaseMapper<TbSaleMatchedPromotion> {

    List<TbSaleMatchedPromotion> findBySellNos(@Param("sellNos") List<String> sellNos);

    List<TbSaleMatchedPromotion> findByAdjustIds(@Param("adjustIds") List<String> adjustIds);

    void saveBatch(@Param("list") List<TbSaleMatchedPromotion> list);

}
