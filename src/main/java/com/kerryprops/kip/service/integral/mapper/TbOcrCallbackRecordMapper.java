package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbOcrCallbackRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/01/2024 17:06
 **********************************************************************************************************************/
public interface TbOcrCallbackRecordMapper extends BaseMapper<TbOcrCallbackRecord> {

    List<TbOcrCallbackRecord> findByPhotoIds(@Param("photoIds") List<Long> photoIds);

}
