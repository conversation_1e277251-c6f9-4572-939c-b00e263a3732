package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/27/2023 11:20
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "拍照积分明细类")
public class TbBonusSelfResponse implements Serializable {

    @Schema( description = "记录ID")
    private String id;

    @Schema( description = "标题")
    private String title;

    @Schema( description = "描述")
    private String desc;

    @Schema( description = "积分数")
    private int integralNumber;

    @Schema( description = "审核状态, (1: 待审核 2: 审核通过 3: 审核未通过)")
    private String state;

    @Schema( description = "审核状态名称")
    private String statusName;

    @Schema( description = "上传图片")
    private String imgUrl;

    @Schema( description = "集团ID")
    private String groupId;

    @Schema( description = "商场ID")
    private String mallId;

    @Schema( description = "商场名称")
    private String mallName;

    @Schema( description = "店铺编号")
    private String storeId;

    @Schema( description = "店铺名称")
    private String storeName;

    @Schema( description = "小票金额")
    private Double money;

    @Schema( description = "审核未通过原因")
    private String reason;

    @Schema( description = "审核查询号")
    private String qrcode;

}
