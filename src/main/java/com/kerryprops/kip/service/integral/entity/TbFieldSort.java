package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_field_sort: 页面表单自定义导出字段
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_field_sort")
public class TbFieldSort implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 字段列名称
     */
    @TableField("field_name")
    private String fieldName;

    /**
     * 字段列顺序 0：代表不展示 ；其他大于0的数字依次代表顺序
     */
    @TableField("field_sort")
    private Integer fieldSort;

    /**
     * 页面类型： 1 : 表示拍照积分审核页面
     */
    @TableField("page_type")
    private String pageType;

    /**
     * 是否默认数据 0：否  1：是
     */
    @TableField("is_initial")
    private Integer isInitial;

    /**
     * 字段列(中文)名称
     */
    @TableField("field_cn_name")
    private String fieldCnName;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 该字段开发了哪些group，默认ALL
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 修改时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 修改人
     */
    @TableField("update_user")
    private String updateUser;

}