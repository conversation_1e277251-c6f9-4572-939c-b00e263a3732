package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/14/2022 15:30
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "自助积分页相应类")
public class MemberAutoPointResource implements Serializable {

    @Schema( description = "开通了哪些业务功能，多个code英文逗号分隔，枚举如下：qr(扫码积分), photo(拍照积分), alipay(支付宝无感积分), wxpay(微信无感积分)")
    private String codes;

    @Schema( description = "是否授权微信无感积分， true: 已授权，false：未授权")
    private Boolean wxAuth;

    @Schema( description = "会员微信openId")
    private String openId;

    @Schema( description = "微信商圈id")
    private String mchId;

    @Schema( description = "支付宝商圈id")
    private String aliMallId;

    @Schema( description = "是否授权支付宝无感积分，true: 已授权，false：未授权")
    private Boolean aliAuth;

}
