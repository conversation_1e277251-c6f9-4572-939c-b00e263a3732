package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - user-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/26/2022 13:55
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerVehicleVo implements Serializable {

    private String id;

    private String kipUserId;

    /**
     * 车牌号
     */
    private String carno;

    /**
     * 车牌类型 （0:普通车牌,1:新能源,2:特殊车牌)
     */
    private Integer carType;

}
