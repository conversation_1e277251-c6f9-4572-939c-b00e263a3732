package com.kerryprops.kip.service.integral.common.aop;

import com.kerryprops.kip.service.integral.common.enums.SignEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/***********************************************************************************************************************
 * Project - profile-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2021 09:14
 **********************************************************************************************************************/

@Data
@Component
@ConfigurationProperties(prefix = "kip.signature")
public class KipSignProperties {

    private SignConfig crm;

    private SignConfig pos;

    private SignConfig ocr;

    @Getter
    @Setter
    public static class SignConfig {
        private String systemId;
        private String systemSecret;
    }

    public SignConfig getByType(String type) {
        if (StringUtils.equals(type, SignEnum.POS.getValue())) {
            return this.pos;
        }
        if (StringUtils.equals(type, SignEnum.OCR.getValue())) {
            return this.ocr;
        }
        return this.crm;
    }

}
