package com.kerryprops.kip.service.integral.service;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kerryprops.kip.service.integral.client.BadgeServiceClient;
import com.kerryprops.kip.service.integral.common.enums.CustomizePageTypeEnum;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.TbMemberGradeCacheDto;
import com.kerryprops.kip.service.integral.webservice.response.BadgeListResponse;
import com.kerryprops.kip.service.integral.webservice.response.ZIndexListResponse;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 * @DESC: 服务内使用本地缓存-caffeine
 *          1、集团设置的会员等级   缓存时长：5s
 *          2、自定义首页          缓存时长：5s
 * <AUTHOR> Bert
 * Created Date - 08/15/2023 15:08
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class CaffeineCacheService {

    private final ZIndexService zindexService;
    private final TbMemberGradeService tbMemberGradeService;
    private final MallConfig mallConfig;
    private final BadgeServiceClient badgeServiceClient;
    // 会员卡等缓存，当前设置5秒
    private LoadingCache<String, TbMemberGradeCacheDto> MEMBER_GRADE_CACHE;
    // 自定义首页缓存，当前设置10秒
    private AsyncLoadingCache<String, ZIndexListResponse> HOME_PAGE_CACHE;
    // 自定义首页缓存，当前设置10秒
    private AsyncLoadingCache<String, ZIndexListResponse> MY_PAGE_CACHE;

    @PostConstruct
    public void init() {
        // 会员等级
        MEMBER_GRADE_CACHE = Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofSeconds(5))
                .initialCapacity(20)
                .maximumSize(50).build(tbMemberGradeService::getGradeList);
        // 自定义首页
        HOME_PAGE_CACHE = Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofSeconds(10))
                .initialCapacity(20)
                .maximumSize(50).buildAsync(key -> {
                    MallItem mallItem = mallConfig.getByMallId(key);
                    log.info("GetHomePageFromRedis: {}", key);
                    return Objects.isNull(mallItem) ? null : zindexService.getHomePageByGroupIdAndMallId(mallItem.getGroupId(), mallItem.getMallId());
                });
        // 自定义-我的页面
        MY_PAGE_CACHE = Caffeine.newBuilder()
               .expireAfterWrite(Duration.ofSeconds(10))
               .initialCapacity(20)
               .maximumSize(50).buildAsync(key -> {
                    MallItem mallItem = mallConfig.getByMallId(key);
                    log.info("GetMyPageFromRedis: {}", key);
                    return Objects.isNull(mallItem)? null : zindexService.getMyPageByGroupIdAndMallId(mallItem.getGroupId(), mallItem.getMallId());
               });
    }

    public ZIndexListResponse getCacheVal(String groupId, String mallId, String type) {
        if (CustomizePageTypeEnum.HOME_PAGE.getValue().equals(type)) {
            return zindexService.getHomePageByGroupIdAndMallId(groupId, mallId);
        } else {
            return zindexService.getMyPageByGroupIdAndMallId(groupId, mallId);
        }
    }

    /**
     * 自定义首页，添加本地缓存
     * @param groupId
     * @param mallId
     * @param type
     * @return
     */
    @Async
    public CompletableFuture<ZIndexListResponse> asyncGetHomePage(String groupId, String mallId, String type) {
        return HOME_PAGE_CACHE.get(mallId);
    }

    /**
     * 自定义-我的页面
     * @param groupId
     * @param mallId
     * @param type
     * @return
     */
    @Async
    public CompletableFuture<ZIndexListResponse> asyncGetMyPage(String groupId, String mallId, String type) {
        return MY_PAGE_CACHE.get(mallId);
    }

    /**
     * 自定义-我的页面-我的徽章
     * @param groupId
     * @param mallId
     * @param vipcode
     * @return
     */
    @Async
    public CompletableFuture<BadgeListResponse> asyncGetMyBadge(String groupId, String mallId, String vipcode) {
        return CompletableFuture.completedFuture(badgeServiceClient.myPageBadgeList(groupId, mallId, vipcode));
    }

    /**
     * 会员卡等，添加本地缓存
     * @param groupId
     * @return
     */
    public List<TbMemberGrade> getGradeList(String groupId) {
        TbMemberGradeCacheDto cacheDto = MEMBER_GRADE_CACHE.get(groupId);
        if (Objects.isNull(cacheDto)) {
            log.info("CaffeineCacheService查询Mysql数据: [{}]", groupId);
            return tbMemberGradeService.queryGradeSortAscWithoutCacheByGroupId(groupId);
        } else {
            log.info("CaffeineCacheService查询Local缓存: [{}]", groupId);
            return cacheDto.getList();
        }
    }

}
