package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.dto.SmsSendResDto;
import com.kerryprops.kip.service.integral.model.dto.WxTemplateSendResDto;
import com.kerryprops.kip.service.integral.model.vo.EmailSendVo;
import com.kerryprops.kip.service.integral.model.vo.SmsSendVo;
import com.kerryprops.kip.service.integral.model.vo.WxTemplateSendVo;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/***********************************************************************************************************************
 * Project - tenant-admin-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - Bert
 * Created Date - 07/06/2021 10:49
 **********************************************************************************************************************/

@FeignClient(name = "unified-messaging-service", url = "${kerry.services.unified-messaging:default}")
public interface UnifiedMessageClient {

    /**
     * 发送短信
     * @param sendVo
     * @return
     */
    @PostMapping(value = "/v2/sms/send", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    SmsSendResDto smsSend(@RequestBody @Valid SmsSendVo sendVo);

    /**
     * 发送邮件
     * @param sendVo
     * @return
     */
    @PostMapping(value = "/email/v2/send", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    KerryResultDto emailSend(@RequestBody @Valid EmailSendVo sendVo);

    /**
     * 内网邮件发送，主要是发送给业务人员，如果是发送给用户不可以用这个接口
     * @param sendVo
     * @return
     */
    @PostMapping(value = "/email/alicloud/send", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    KerryResultDto emailAliCloudSend(@RequestBody @Valid EmailSendVo sendVo);

    /**
     * 模版消息发送
     * @param sendVo
     */
    @PostMapping(value = "/wx-template/direct_send", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    WxTemplateSendResDto wxTemplateSend(@RequestBody WxTemplateSendVo sendVo);

}
