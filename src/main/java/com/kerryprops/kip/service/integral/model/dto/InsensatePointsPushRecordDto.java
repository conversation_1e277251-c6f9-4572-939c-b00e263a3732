package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 微信、支付宝推送记录表
 * <AUTHOR>
 * @date 2022-09-23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsensatePointsPushRecordDto implements Serializable {


    /**
    * 符合规范的唯一主键id
    */
    private Long id;

    /**
    * 微信openid或支付宝user_id。根据origin判断
    */
    private String openId;

    /**
    * 支付订单号
    */
    private String transactionId;

    /**
    * 事件类型。1支付，2退款。对应integralconstant.wx_pay_success等枚举
    */
    private int eventType;

    /**
    * 支付具体信息
    */
    private String transactionInfo;

    /**
    * 来源，0微信，1支付宝
    */
    private int origin;

    /**
    * create_date
    */
    private Date createDate;

    /**
    * update_date
    */
    private Date updateDate;
}