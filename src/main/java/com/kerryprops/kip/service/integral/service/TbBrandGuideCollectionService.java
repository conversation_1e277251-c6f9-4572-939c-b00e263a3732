package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbBrandGuideCollection;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 07/18/2024 14:07
 **********************************************************************************************************************/
public interface TbBrandGuideCollectionService extends IService<TbBrandGuideCollection> {

    /**
     * 添加收藏
     * @param loginUser
     * @param tbMemberAsset
     * @param brandGuideId
     */
    void saveBrandGuide(LoginUser loginUser, TbMemberAsset tbMemberAsset, String brandGuideId);

    /**
     * 取消收藏
     * @param loginUser
     * @param tbMemberAsset
     * @param brandGuideId
     */
    void cancelBrandGuide(LoginUser loginUser, TbMemberAsset tbMemberAsset, String brandGuideId);

    /**
     * 取消或收藏品牌导览
     * @param collection
     */
    void saveOrCancelBrandGuide(TbBrandGuideCollection collection);

    /**
     * 获取收藏列表
     * @param groupId
     * @param mallId
     * @param vipcode
     * @return
     */
    List<String> getBrandGuideList(String groupId, String mallId, String vipcode);

    /**
     * 从数据库查询我的收藏
     * @param groupId
     * @param mallId
     * @param vipcode
     * @return
     */
    List<String> getBrandGuideFromDb(String groupId, String mallId, String vipcode);

    /**
     * 获取品牌导览redis缓存key
     * @param groupId
     * @param vipcode
     * @param mallId
     * @return
     */
    String getBrandGuideKey(String groupId, String vipcode, String mallId);

}
