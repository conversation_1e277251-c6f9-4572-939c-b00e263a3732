package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberRefine;
import com.kerryprops.kip.service.integral.entity.TbMemberRefineField;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.mapper.TbMemberRefineMapper;
import com.kerryprops.kip.service.integral.mapper.TbMemberRefineDetailMapper;
import com.kerryprops.kip.service.integral.model.dto.PerfectMemberActivityDto;
import com.kerryprops.kip.service.integral.service.EventTriggerService;
import com.kerryprops.kip.service.integral.service.TbMemberRefineService;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberRefineResource;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberRefineDetailResource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/17/2022 13:48
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbMemberRefineServiceImpl extends ServiceImpl<TbMemberRefineMapper, TbMemberRefine> implements TbMemberRefineService {

    private final TbMemberRefineMapper tbMemberRefineMapper;
    private final TbMemberRefineDetailMapper tbMemberRefineDetailMapper;
    private final EventTriggerService eventTriggerService;
    private final Mapper mapper;

    @Override
    @Cacheable(value = RedisCacheKey.INDEX_CRM_REFINE_CONFIG_CACHE, key = "#mallId", unless = "#result == null")
    public TbMemberRefineResource getByMallId(String mallId) {
        if (StringUtils.isBlank(mallId)) {
            throw BizException.error(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR);
        }
        TbMemberRefine config = tbMemberRefineMapper.findByMallId(mallId);
        if (Objects.isNull(config)) {
            return null;
        }
        // 查找完善信息送积分活动
        CompletableFuture<PerfectMemberActivityDto> future = eventTriggerService.obtainRewardForInfo(config.getGroupId(), config.getMallId());
        // 响应参数组装
        TbMemberRefineResource resource = mapper.map(config, TbMemberRefineResource.class);
        List<TbMemberRefineField> fields = tbMemberRefineDetailMapper.findByConfigId(config.getId(), 1);
        List<TbMemberRefineDetailResource> list = fields.stream().map(it -> mapper.map(it, TbMemberRefineDetailResource.class)).collect(Collectors.toList());
        resource.setFieldDTOS(list.stream().filter(it -> (0 == it.getFieldType())).collect(Collectors.toList()));
        resource.setLabelDTOS(list.stream().filter(it -> (1 == it.getFieldType())).collect(Collectors.toList()));
        CompletableFuture.allOf(future).join();
        try {
            PerfectMemberActivityDto activityDto = future.get();
            // 设置积分数
            resource.setIntegral((Objects.nonNull(activityDto) && StringUtils.isNotBlank(activityDto.getIssuePoints())) ? Integer.parseInt(activityDto.getIssuePoints()) : 0);
            // 完善信息内容参数替换
            if (StringUtils.isNotBlank(resource.getSynopsis())) {
                resource.setSynopsis(resource.getSynopsis().replace(IntegralConstant.REFINE_CONFIG_PARAMS, resource.getIntegral().toString()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resource;
    }

    @Override
    public List<TbMemberRefineField> memberRefineRequiredFields(String mallId) {
        List<TbMemberRefineField> configs = tbMemberRefineDetailMapper.findMallRefineFields(mallId);
        if (CollectionUtils.isEmpty(configs)) {
            return Collections.emptyList();
        }
        // 找出必填字段
        return configs.stream().filter(it -> 1 == it.getIsRequired()).collect(Collectors.toList());
    }

}
