package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 05/09/2023 13:47
 **********************************************************************************************************************/

@Getter
public enum MemberEquityEnum {

    SCAN_QR(1, "扫码积分指南"),
    DISCOUNT(2, "优惠信息指南"),
    SERV_AGR(3, "服务协议指南"),
    AUTO_POINTS(4, "自助积分指南"),
    WXPAY(5, "微信无感积分指南"),
    ALIPAY(6, "支付宝无感积分指南"),
    SERV_DESK(7, "客服台积分指南"),
    PHOTO(8, "拍照积分指南");

    private final Integer type;
    private final String typeName;

    MemberEquityEnum(Integer type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public static MemberEquityEnum getByType(Integer type) {
        return Arrays.stream(values()).filter(it -> Objects.equals(it.getType(), type)).findFirst().orElse(null);
    }

}
