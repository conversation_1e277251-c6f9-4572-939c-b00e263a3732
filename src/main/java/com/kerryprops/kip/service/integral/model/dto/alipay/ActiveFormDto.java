package com.kerryprops.kip.service.integral.model.dto.alipay;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝会员卡信息提交表单接受类
 * @createDate 2022/10/24
 * @updateDate 2022/10/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ActiveFormDto implements Serializable {

    /**
     * 性别
     */
    @JSONF<PERSON>(name = "OPEN_FORM_FIELD_GENDER")
    private String gender;
    /**
     * 手机号
     */
    @JSONField(name = "OPEN_FORM_FIELD_MOBILE")
    private String mobile;

    /**
     * 名字
     */
    @JSONField(name = "OPEN_FORM_FIELD_NAME")
    private String name;

    @JSONField(name = "OPEN_FORM_FIELD_BIRTHDAY")
    private String birthday;

    private String mallId;

    private String groupId;
}
