package com.kerryprops.kip.service.integral.common.utils;

import com.kerryprops.kip.service.integral.model.dto.TbMemberGradeFormat;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 01/17/2023 17:40
 **********************************************************************************************************************/

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RuleFormatsUtils {

    /**
     * 获取当天group下对应规则的业态并集
     * @param ruleFormats ruleFormats
     * @return string string
     */
    public static List<String> getGroupRuleFormats(String ruleFormats) {
        if (StringUtils.isBlank(ruleFormats)) {
            return Collections.emptyList();
        }
        List<TbMemberGradeFormat> gradeFormats = JsonUtils.stringToList(ruleFormats, TbMemberGradeFormat.class);
        if (CollectionUtils.isEmpty(gradeFormats)) {
            return Collections.emptyList();
        }
        return gradeFormats.stream().flatMap(o->o.getFormatList().stream()).distinct().collect(Collectors.toList());
    }

}
