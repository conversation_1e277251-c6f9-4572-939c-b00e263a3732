package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 无感积分 微信、支付宝授权记录表
 * <AUTHOR>
 * @date 2022-09-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbInsensatePointsAuthRecordDto implements Serializable {

    /**
    * 符合规范的唯一主键id
    */
    private Long id;

    /**
    * openid
    */
    private String openid;

    /**
    * kip系统中用户唯一id
    */
    private String kipUserId;

    /**
    * 集团id
    */
    private String groupId;

    /**
    * 商场编号
    */
    private String mallId;

    /**
     * 支付宝卡号
     */
    private String cardNo;

    /**
    * 授权状态，0已授权，1未授权，默认0
    */
    private int status;

    /**
    * 来源，0微信，1支付宝
    */
    private int origin;

    private Date createDate;
}