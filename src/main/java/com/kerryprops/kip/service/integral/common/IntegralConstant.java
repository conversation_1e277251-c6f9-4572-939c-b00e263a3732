package com.kerryprops.kip.service.integral.common;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/***********************************************************************************************************************
 * Project - integral-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/15/2022 10:49
 **********************************************************************************************************************/

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class IntegralConstant {

    public static final String X_USER = "X-User";
    public static final String BRAND_ID = "brandId";
    public static final String LBS_ID = "lbsId";
    public static final String APP_ID = "appId";

    /**
     * 商业lbs所属的业态
     */
    public static final String LBS_RETAIL = "RETAIL";

    /**
     * 请求来源
     */
    public static final String PROVIDER = "provider";

    public static final String OLD_KO_SITES = "JAKC,KP,BKC";
    /**
     * 营销积分活动限制店铺-全部店铺
     */
    public static final String DEFAULT_STORE = "allstore";

    public static final String DEFAULT_ENC = "UTF-8";

    public static final String INTEGRAL_REPEAT_1 = "1";
    public static final String INTEGRAL_REPEAT_2 = "2";
    public static final String INTEGRAL_REPEAT_3 = "3";

    // 大陆手机号格式
    public static final String PHONE_NUMBER_REGEX = "^1\\d{10}$";

    /**
     * 是否展示在支付宝小程序
     */
    public static final Integer SHOW_ALI_MINI = 1;

    /**
     * 支付宝无感积分-支付
     */
    public static final String ALIPAY_POINTS = "XS0066";

    /**
     * 支付宝无感积分-退款
     */
    public static final String ALIPAY_REFUND_POINTS = "XS0067";

    /**
     * 微信无感支付积分
     */
    public static final String WECHAT_POINTS = "XS0068";
    /**
     * 微信无感支付退款扣积分
     */
    public static final String WECHAT_POINTS_REFUND = "XS0069";

    /**
     * 拍照积分(销售录入拍照积分,拍照积分审核,OCR拍照)
     */
    public static final String TAKE_PHOTO = "HF0054";

    /**
     * 销售录入的销售积分,pos机的销售积分
     */
    public static final String SALE_INTEGRAL = "XS0002";

    /**
     * 有单退货
     */
    public static final String ORDER_REFUND = "XS00030";

    /**
     * 无单退货
     */
    public static final String NO_ORDER_REFUND = "XS00031";

    /**
     * 积分清零字典值
     */
    public static final String INTEGRAL_CLEAR_REMARK = "XS0015";

    /**
     * 积分商城
     */
    public static final String REWARDS_MALL = "REWARDS_MALL";
    /**
     * 积分商城退款退积分
     */
    public static final String REFUND_REWARDS_MALL = "REFUND_REWARDS_MALL";
    /**
     * 优惠买单
     */
    public static final String KERRY_PAY = "KERRY_PAY";
    /**
     * 优惠买单退款退积分
     */
    public static final String REFUND_KERRY_PAY = "REFUND_KERRY_PAY";

    /**
     * 对接pmw传的订单来源
     */
    public static final String KIP_POINTS = "KIP_POINTS";

    public static final long RETRY_TIMES = 5;

    public static final String WX_FINISHED = "FINISHED";

    public static final String SMART_POS_DATA = "data";

    public static final String REFINE_CONFIG_PARAMS = "{INTEGRAL}";

    public static final String INTEGRAL_CLEAR_DATE = "{DATE}";

    public static final String MEMBER_FREEZE_STATUS = "0";

    public static final String DEF_SUCC = "000000";

    /**
     * KO下存在的两个商场
     */
    public static final String DEFAULT_KO = "JAKC,KP";

    /**
     * 人工调整会员卡等，升级金额统计区间不受影响的group
     */
    public static final String NO_EFFECT_GROUPS = "JAKC,SYKC";

    public static final String TEST_GROUP = "QATS";

    public static final int CHECK_STEP_1 = 1;
    public static final int CHECK_STEP_3 = 3;

    /**
     * pos传过来的时间戳60分钟内有效
     */
    public static final long MAX_REQUEST = 60 * 60 * 1000L;

    /**
     * POS积分抵现
     */
    public static final String POS_CASH_OUT = "XS0110";

    /**
     * POS积分抵现退款退积分
     */
    public static final String POS_CASH_OUT_REFUND = "XS0022";

    /**
     * 海鼎POS发起退款退款
     */
    public static final String REFUND = "REFUND";

    /**
     * 海鼎POS发起退款销售
     */
    public static final String PAYMENT = "PAYMENT";

    /**
     * 查询支付宝会员卡类型
     */
    public static final String ALI_BIZ_CARD = "BIZ_CARD";

    /**
     * 会员冻结状态 0：冻结，1：非冻结
     */
    public static final String MEMBER_STATUS_FREEZE = "0";

    /**
     * 分页数量
     */
    public static final int size = 1000;
    /**
     * 产生销售记录计算会员是否升级MQ参数id
     */
    public static final String SALE_ID = "saleId";
    // 销售来源
    public static final String FROM_TYPE = "fromType";
    // 销售来源消息通知值(ipad/S/CRM原始admin端)
    public static final List<String> IPAD_S_SALES = Arrays.asList("IPAD", "S", "CRM");
    public static final String SEND_FULL_REWARD = "fullReward";

    /**
     * Redis Key结构
     */
    public static final String DEF_REDIS_KEY = "%s::%s:%s";

    /**
     * 店铺状态
     */
    public static final String SHOP_ENABLE_STATUS = "ENABLE";

    /**
     * 微信无感,支付宝无感,扫码,积分商场,优惠买单(有自动成功的唯一单号) POS(特殊处理，虽然有唯一单号，但是remark值和销售录入一样啦，所以要根据saleType来)
     */
    public static final List<String> UNIQUE_SALE_NO = Arrays.asList(SaleTypeEnum.WECHAT.getValue(), SaleTypeEnum.ALIPAY.getValue(), SaleTypeEnum.MALL.getValue(), SaleTypeEnum.KERRY_PAY.getValue(), SaleTypeEnum.SCAN.getValue(), SaleTypeEnum.POS.getValue());

    public static final List<String> UNIQUE_SALE_NO_BKC = Arrays.asList(SaleTypeEnum.WECHAT.getValue(), SaleTypeEnum.ALIPAY.getValue(), SaleTypeEnum.MALL.getValue(), SaleTypeEnum.KERRY_PAY.getValue(), SaleTypeEnum.POS.getValue());

    /**
     * HKC迁移日期
     */
    public static final Date hkcMigrationToKoDate = DateUtil.parseDateTime("2025-04-01 00:00:00");

    /**
     * 销售录入,拍照积分
     */
    public static final List<String> NORMAL_SALE_TYPE = Arrays.asList(SaleTypeEnum.CRM.getValue(), SaleTypeEnum.TICKET.getValue());

    public static final Map<String, String> sceneMap = new HashMap<>();
    static {
        sceneMap.put("AAA9B28387CA25D4F01C0A18961061C0", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a88a9fd7f73ffcd017f968739870006\"}");
        sceneMap.put("C8D19315F508EB31411A2266B34D603A", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a8883557cca9463017ccb002b360001\",\"tpName\":\"tenant_hkc_om_hkc_crm_points\"}");
        sceneMap.put("44F65FC195D7C820ACD493AB16370E99", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a88a9fd7f73ffcd017f968739870006\",\"tpName\":\"tenant_qhkc_om_qhkc_crm_points\",\"redirect\":\"1\"}");
        sceneMap.put("D7DEA807846C5A5D5AE901DA69857ED1", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a888aed7d0295e5017d029ff1f40000\"}");
        sceneMap.put("4716BD5B341498A2D3106CA142FA6884", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a8884e77cc9e70a017cca4003590008\"}");
        sceneMap.put("85C3370B9E96299090CE902060B1D655", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a88835c7cd96d31017cda2fd9910000\",\"tpName\":\"tenant_jakc_om_ko_crm_points\",\"redirect\":\"1\"}");
        sceneMap.put("CCBAF09EFB79CD0D63A2F36FCF90E77C", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a8883557cca9463017ccb002b360001\"}");
        sceneMap.put("61BF031EC1115EF71B537923F39A4882", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a88835c7cd96d31017cda3f77e80003\"}");
        sceneMap.put("77E7106D6815BDB9C0CAFDDFD75C2916", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a8883557cca9463017ccb0ce0a00002\"}");
        sceneMap.put("B67EA26B18316084A77C65BA0A0CA13B", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a84853b7c91ac5b017c961a9b2a030d\",\"tpName\":\"tenant_jakc_om_ko_crm_points\",\"redirect\":\"1\"}");
        sceneMap.put("DF6B4B57A5AC3E31AFBBADED7CFE073C", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a88835c7cd96d31017cda2fd9910000\",\"tpName\":\"tenant_jakc_om_ko_crm_points\",\"redirect\":\"1\"}");
        sceneMap.put("08F564A6279ED748372BF687351277FC", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a88835c7cd96d31017cda2fd9910000\",\"tpName\":\"tenant_jakc_om_ko_crm_points\",\"redirect\":\"1\"}");
        sceneMap.put("98C0C258C07D27790BD35341A8BB950A", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a8883557cca9463017ccb002b360001\",\"tpName\":\"tenant_hkc_om_hkc_crm_points\",\"redirect\":\"1\"}");
        sceneMap.put("0BF4027C3C2BE667681D2628790425E5", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a8883557cca9463017ccb002b360001\",\"tpName\":\"tenant_jakc_om_ko_crm_points\"}");
        sceneMap.put("2AE2B1249861D24E72C941580D6E7D90", "{\"regSource\":\"WX_EASY_POINTS\",\"mallid\":\"8a88835c7cd96d31017cda2fd9910000\",\"tpName\":\"tenant_jakc_om_ko_crm_points\"}");
    }

    /**
     * 参与退款的的会员号
     */
    public static final String VIP_CODE = "vipcode";

    /**
     * 导出数据最大值10w
     */
    public static final int MAX_EXPORT_SIZE = 100000;

    /**
     * 支持积分扣成负数的字典值列表
     * RDB002: 徽章收回扣积分
     */
    public static final List<String> SUPPORT_NEGATIVE_POINTS_DICT_LIST = Arrays.asList("RDB002");

}
