package com.kerryprops.kip.service.integral.webservice.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bouncycastle.asn1.cms.PasswordRecipientInfo;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/01/2022 10:45
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbInterceptResponse implements Serializable {

    @Schema( name = "是否被拦截")
    private Boolean isIntercept;

    @Schema( name = "拦截id")
    private String interceptId;

    @Schema( name = "拦截内容")
    private String interceptReason;

    @Schema( name = "因为哪些销售id被拦截，逗号分隔")
    private String interceptSaleIds;

}
