package com.kerryprops.kip.service.integral.webservice.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierMemberResponse implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 开卡商城
     */
    private String mallid;
    /**
     * 开卡商城
     */
    private String openmarket;
    /**
     * 积分调整商场
     */
    private String marketid;
    /**
     * 当前积分
     */
    private Double currnentintegral;
    /**
     * 当前储值余额
     */
    private Integer currentCharge;
    /**
     * 资料来源 （默认）见维度值选择
     */
    private String sourceorigin;
    /**
     * 会员状态（0:冻结；1:非冻结)
     */
    private String status;
    /**
     * 微信激活状态（"1" 激活、"0或者其他" 非激活）
     */
    private String wxstatus;
    /**
     * 微信激活商场
     */
    private String wxopenmarket;
    /**
     * 证件类型（身份证、驾驶证、护照、其他证件）
     */
    private String certificatetype;
    /**
     * 证件号码
     */
    private String certificatenum;
    /**
     * 会员姓名
     */
    private String vipname;
    /**
     * 会员性别(男：M     女：F  未知：U)
     */
    private String sex;
    /**
     * 会员昵称
     */
    private String nickname;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 省级地址
     */
    private String provinceAddress;
    /**
     * 市级地址
     */
    private String cityAddress;
    /**
     * 区县地址
     */
    private String countryAddress;
    /**
     * 会员地址
     */
    private String address;
    /**
     * 生日
     */
    private String birthday;
    /**
     * 婚姻状态 未婚：0       ; 已婚 ：1
     */
    private String marriagestatus;
    /**
     * 是否有宝宝的状态 0->没有 1->有
     */
    private String babystatus;
    /**
     * 兴趣标签（维度维护）多选
     */
    private String hobys;
    /**
     * 兴趣标签（维度维护）多选
     */
    private String hobysName;
    /**
     * 微信头像
     */
    private String headimgurl;

    /**
     * 加入日期
     */
    private Date jointime;
    /**
     * 微信激活日期
     */
    private Date wxactivetime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 等级[编码]
     */
    private String grade;
    /**
     * 会员编号.(自动生成.)
     */
    private String vipcode;
    // 会员编号(爱琴海)
    private String vipNo;
    /**
     * 绑定实体卡卡号
     */
    private String xfEntityCard;
    /**
     * 会员调整积分时存储会员原始积分
     */
    private Double oldIntegral;
    /**
     * 会员等级允许透支的积分
     */
    private Integer maxoweintegral;
    /**
     * 积分调整原因code
     */
    private String integralAdjustReasonCode;
    /**
     * 积分调整商场
     */
    private String integralAdjustMallid;

    /**
     * 邮箱
     */
    private String mailBox;
    /**
     * 邮箱 （0:跳过；1:不跳过)
     */
    private String isSkip;
    /**
     * 是否完善信息（0:未完善；1:完善)
     */
    private String isCompleted;
    /**
     * 集团编号
     */
    private String groupId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新人
     */
    private Long updater;
    /**
     * 创建者ID
     */
    private Long creator;
    /**
     * 创建者ID
     */
    private String deptId;
    /**
     * 验证码
     */
    private String msgcode;
    /**
     * 是否使用第三方会员(0:否；1：是）
     */
    private Integer isThirdMember;
    /**
     * 第三方会员号
     */
    private String thirdVipcode;
    //商户拉新店铺号
    private String shareShopNo;
    //商户拉新店员号
    private String shareShopEmployeesNo;
    //邀请人会员号
    private String shareVipcode;
    //注册openid
    private String openid;
    private String unionId;

    /**
     * 数据来源: “HF” 哈峰 “HX”红星
     */
    private String dataSource;


	
	/**
	 * 红星调整积分数量
	 */
	private Double hxintegral;

    /**
     * 会员卡面url(我的)
     */
	private String cardCoverUrl;

    /**
     * 会员卡面url(首页)
     */
	private String cardCoverHomeUrl;
    /**
     * 车牌号
     */
    private String plate;

    /**
     * 历史消费总额
     */
    private BigDecimal totalConsumption;
    /**
     * 消费总金额
     */
    private BigDecimal totalAmount;
    /***
     * 真实名称
     */
    private String realName;

    /**
     * 公司名称
     */
    private String companyName;
    /**
     *  年龄
     */
    private Integer age;
    /**
     * 星座
     */
    private String starSign;
    /**
     * 民族
     */
    private String nation;
    /**
     * 职位
     */
    private String position;
    /**
     * 家庭电话
     */
    private String telephone;

    /**
     * kip的user_id也叫profile_id
     */
    private String kipUserId;

    /**
     * 供活动使用 是否为黑名单 0:是 1:不是
     */
    private Integer whetherBlacklist;

    /**
     * 会员身份认证 多个身份使用#分隔
     * apartment 公寓
     * office 办公楼
     */
    private String certification;
    /**
     * 会员修改信息的旧手机号
     */
    private String oldMobile;

    /**
     * 学历
     */
    private Integer education;

    /**
     * 职业
     */
    private Integer profession;

    /**
     * 是否是会员邀约的标识
     */
    private transient Boolean isInvitation;

    /**
     * 等级名称
     */
    private transient String gradeName;

    /**
     * 车牌list
     */
    private List<CarNoResponse> plateList;

}
