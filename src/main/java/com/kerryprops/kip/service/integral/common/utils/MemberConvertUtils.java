package com.kerryprops.kip.service.integral.common.utils;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.webservice.response.SupplierMemberResponse;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - saas
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/29/2024 16:12
 **********************************************************************************************************************/
public class MemberConvertUtils {

    public static SupplierMemberResponse convertMemberEntity(TbMemberAsset allInfo) {
        if (Objects.isNull(allInfo)) {
            return null;
        }
        return SupplierMemberResponse.builder()
                .id(allInfo.getId()+"")
                .mallid(allInfo.getMallId())
                .openmarket(allInfo.getMallId())
                .marketid(allInfo.getMallId())
                .currnentintegral(Double.valueOf(allInfo.getCurrentPoints()))
                .currentCharge(0)
                .sourceorigin(allInfo.getRegisterSource())
                .status(allInfo.getStatus())
                .certificatetype(allInfo.getCertificateType()+"")
                .certificatenum(allInfo.getCertificateNum())
                .vipname(allInfo.getRealName())
                .sex(Objects.isNull(allInfo.getGender()) ? "U" : ((0 == allInfo.getGender()) ? "U" : (1 == allInfo.getGender() ? "M" : "F")))
                .nickname(allInfo.getNickName())
                .mobile(allInfo.getMobile())
                .provinceAddress(allInfo.getProvinceAddress())
                .cityAddress(allInfo.getCityAddress())
                .countryAddress(allInfo.getDistrictAddress())
                .address(allInfo.getAddress())
                .birthday(Objects.nonNull(allInfo.getBirthDate()) ? DateUtil.formatDate(allInfo.getBirthDate()) : null)
                .marriagestatus(allInfo.getMaritalStatus()+"")
                .babystatus(allInfo.getBabyStatus()+"")
                .headimgurl(allInfo.getAvatar())
                .jointime(allInfo.getJoinTime())
                .remark(allInfo.getRemark())
                .isCompleted(allInfo.getIsCompleted())
                .grade(allInfo.getGrade())
                .vipcode(allInfo.getVipcode())
                .vipNo(allInfo.getVipcode())
                .mailBox(allInfo.getEmail())
                .groupId(allInfo.getGroupId())
                .realName(allInfo.getRealName())
                .companyName(allInfo.getCompanyName())
                .nation(allInfo.getNation())
                .position(allInfo.getJob())
                .kipUserId(allInfo.getKipUserId())
                .telephone(allInfo.getHomePhone())
                .whetherBlacklist(allInfo.getWhetherBlacklist())
                .education(allInfo.getEducation())
                .createDate(allInfo.getJoinTime())
                .build();
    }

}
