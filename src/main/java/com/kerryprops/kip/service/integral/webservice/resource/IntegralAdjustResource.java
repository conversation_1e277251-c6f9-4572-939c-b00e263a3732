package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/26/2023 13:43
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntegralAdjustResource implements Serializable {

    /**
     * 会员id(销售录入传来的id是会员id，tb_member表id)
     */
    @NotBlank(message = "会员id不能为空")
    @Schema( description = "会员id")
    private String id;

    /**
     * 调整类型 A->普通调整， S->销售调整
     */
    @Schema( description = "调整类型 A->普通调整， S->销售调整，如果是销售录入，传S")
    private String type;

    /**
     * 商场编号
     */
    @NotBlank(message = "mallId不能为空")
    @Schema( description = "商场id")
    private String mallId;

    /**
     * 积分调整原因
     */
    @NotBlank(message = "remark不能为空")
    @Schema( description = "调整原因")
    private String remark;

    @Schema( description = "销售表自增id，当为有单退货并且数据在销售表时，该值不能为空，但是积分拦截可以不传该参数")
    private Long saleId;

    /**
     * 销售单号(销售调整)
     */
    @Schema( description = "销售单号")
    private String sellNo;

    /**
     * 店铺编号(给积分记录使用)
     */
    @NotBlank(message = "shopNo不能为空")
    @Schema( description = "店铺编号")
    private String shopNo;

    /**
     * 店铺名称
     */
    @Schema( description = "店铺名称")
    private String shopName;

    /**
     * 消费金额
     */
    @Schema( description = "消费金额")
    private BigDecimal useMoney;

    /**
     * 优惠金额
     */
    @Schema( description = "优惠金额")
    private BigDecimal discounts;

    /**
     * 销售日期(销售调整)
     */
    @Schema( description = "销售日期")
    private String sellDate;

    /**
     * 备注
     */
    @Schema( description = "备注")
    private String content;

    /**
     * 收银机号
     */
    @Schema( description = "收银机号")
    private String cashNo;

    /**
     * 小票地址
     */
    @Schema( description = "小票地址")
    private String imageUrl;

    /**
     * 创建者
     */
    @Schema( description = "销售录入操作人员名称")
    private String creator;

    /**
     * 审核通过 1是通过，null或者0表示不通过
     */
    @Schema( description = "审核通过 1是通过，null或者0表示不通过，当出现重复录入时，C端给予业务人员提示，点击确定重复录入后，该值传1")
    private String approved;

    @Schema( description = "退款金额")
    private String refundMoney;

}
