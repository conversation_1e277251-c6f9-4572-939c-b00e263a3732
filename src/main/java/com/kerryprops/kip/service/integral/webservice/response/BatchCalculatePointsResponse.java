package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> nancy
 * Created Date - 08/01/2024 14:49
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "批量计算积分返回的对象")
public class BatchCalculatePointsResponse implements Serializable {

    @Schema( name = "订单号")
    private String orderNo;

    @Schema( name = "积分数")
    private String pointsNum;

    @Schema( name = "记录转赠记录新的积分来源")
    private String salesRemark;

    @Schema( name = "积分明细id")
    private String pointsId;

}
