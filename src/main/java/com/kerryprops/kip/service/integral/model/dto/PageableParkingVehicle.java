package com.kerryprops.kip.service.integral.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

/***********************************************************************************************************************
 * Project - temporary-parking-service
 * PageableParkingVehicle
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Sam.Zhang
 * Created Date - 2023/8/3 14:46
 **********************************************************************************************************************/

@Getter
@Setter
public class PageableParkingVehicle {
    private List<VehicleData> content;


    @Setter
    @Getter
    public static class VehicleData {
        private Integer id;
        private String userId;
        private String vehicleNo;
        private LicensePlateType licensePlateType;

        @Override
        public String toString() {
            return new StringJoiner(", ", VehicleData.class.getSimpleName() + "[", "]")
                    .add("id=" + id)
                    .add("userId='" + userId + "'")
                    .add("vehicleNo='" + vehicleNo + "'")
                    .add("licensePlateType=" + licensePlateType)
                    .toString();
        }

        @Getter
        public enum LicensePlateType {
            ORDINARY_CAR_NO(0), NEW_ENERGY_CAR_NO(1), SPECIAL_CAR_NO(2);
            private final Integer code;

            LicensePlateType(Integer code) {
                this.code = code;
            }
        }

        public CustomerVehicleVo toVo() {
            return CustomerVehicleVo.builder()
                    .id(Objects.toString(id))
                    .carno(vehicleNo)
                    .kipUserId(userId)
                    .carType(licensePlateType.getCode())
                    .build();
        }
    }
}
