package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopResource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 16:01
 **********************************************************************************************************************/
public interface TbOnlineShopConfigMapper extends BaseMapper<TbOnlineShopConfig> {

    /**
     * 计算总数
     * @param resource: resource
     * @return int int
     */
    int onlineShopTotal(TbOnlineShopResource resource);

    /**
     * 分页查询
     * @param resource: resource
     * @return list list
     */
    List<TbOnlineShopConfig> onlineShopPage(TbOnlineShopResource resource);

    /**
     * 保存和修改
     * @param configParam: configParam
     */
    List<TbOnlineShopConfig> getOnlineShop(TbOnlineShopConfig configParam);

    /**
     * 根据条件删除删除
     * @param param: param
     */
    void deleteByParam(TbOnlineShopConfig param);

    /**
     * 保存和修改
     * @param list: list
     */
    void insertOnlineShopBatch(List<TbOnlineShopConfig> list);

    /**
     * 查询线上商城积分比例
     * @param mallId 商场id
     * @param grade 等级
     * @param businessType 业务类型
     * @return dto dto
     */
    TbOnlineShopConfig findByMallIdAndGradeAndBusinessType(@Param("mallId") String mallId, @Param("grade") String grade, @Param("businessType") String businessType);

}
