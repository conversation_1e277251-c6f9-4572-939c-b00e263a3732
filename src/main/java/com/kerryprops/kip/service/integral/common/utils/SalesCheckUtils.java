package com.kerryprops.kip.service.integral.common.utils;

import cn.hutool.core.util.NumberUtil;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 03/13/2024 09:53
 **********************************************************************************************************************/

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SalesCheckUtils {

    /**
     * 错误的销售记录
     * @param detail
     * @return
     */
    public static boolean isErrorSalesDetail(TbSalesDetail detail) {
        return Objects.isNull(detail.getPayAmount()) || detail.getPayAmount().compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 退款金额是否大于实付金额
     * @param detail
     * @return
     */
    public static boolean refundMoreThanPay(TbSalesDetail detail, String refundMoney) {
        return NumberUtil.add(StringUtils.isBlank(refundMoney) ? BigDecimal.ZERO : new BigDecimal(refundMoney),
                (Objects.isNull(detail.getRefundAmount()) ? BigDecimal.ZERO : detail.getRefundAmount()))
                .compareTo(detail.getPayAmount()) > 0;
    }

}
