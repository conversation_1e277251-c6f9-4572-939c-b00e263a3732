package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 05/31/2023 13:43
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchCalculatePointsResource implements Serializable {

    @Schema( description = "会员号")
    private String vipcode;

    @Schema( description = "手机号")
    private String mobile;

    @NotBlank(message = "调整原因不能为空")
    @Schema( description = "调整原因")
    private String remark;

    @NotBlank(message = "groupId不能为空")
    @Schema( description = "集团id")
    private String groupId;

    @NotBlank(message = "mallId不能为空")
    @Schema( description = "商场id")
    private String mallId;

    @Schema( description = "订单号")
    private List<String> orderNos;

    @NotEmpty(message = "订单id不能为空")
    private List<String> saleIds;

}
