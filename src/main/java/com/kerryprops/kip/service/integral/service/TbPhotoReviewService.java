package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.model.dto.BonusSelfQueryDto;
import com.kerryprops.kip.service.integral.model.dto.BonusSelfUploadDto;
import com.kerryprops.kip.service.integral.model.dto.PhotoReviewCheckDto;
import com.kerryprops.kip.service.integral.model.dto.TakePhotoAuditDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/27/2023 10:41
 **********************************************************************************************************************/
public interface TbPhotoReviewService extends IService<TbPhotoReview> {

    /**
     * 获取数量
     * @param dto dto
     * @return list list
     */
    List<TbPhotoReview> geTbBonusSelfList(BonusSelfQueryDto dto);

    /**
     * 覆盖更新的方法
     *
     * @param review
     * @return
     */
    boolean updateById(TbPhotoReview review);

    /**
     * 获取数量
     * @param dto dto
     * @return int int
     */
    int getBonusTotal(BonusSelfQueryDto dto);

    /**
     * 获取数量
     * @param dto dto
     */
    Long saveRecord(BonusSelfUploadDto dto);

    /**
     * 拍照上传审核
     * @param dto dto
     * @return int
     */
    Integer auditRecord(TakePhotoAuditDto dto);

    /**
     * S端分页查询
     * @param dto dto
     * @return list list
     */
    List<TbPhotoReview> getReviewPage(BonusSelfQueryDto dto);

    /**
     * 查询用户待积分的拍照积分记录
     * @param id
     * @param vipcode
     * @return
     */
    TbPhotoReview findByIdAndVipcode(Long id, String vipcode);

    /**
     * 查询前一页
     * @param mallId mallId
     * @param endTime endTime
     * @param startTime startTime
     * @param timestamp timestamp
     * @return dto dto
     */
    TbPhotoReview getForPrevPage(String timestamp, String mallId, String startTime, String endTime);

    /**
     * 查询下一页未审核的信息
     * @param mallId mallId
     * @param endTime endTime
     * @param startTime startTime
     * @param timestamp timestamp
     * @return dto dto
     */
    TbPhotoReview getForNextPage(String timestamp, String mallId, String startTime, String endTime);

    /**
     * 查询下一页图片
     * @param mallId mallId
     * @param endTime endTime
     * @param startTime startTime
     * @param timestamp timestamp
     * @return dto dto
     */
    String getNextImage(String timestamp, String mallId, String startTime, String endTime);

    /**
     * C端用户积分后，补充拍照积分相关参数
     * @param tbMemberAsset
     * @param review
     * @param baseShop
     * @param points
     * @param pointsDetailId
     */
    void fillPhotoViewInfo(TbMemberAsset tbMemberAsset, TbPhotoReview review, TbBaseShop baseShop, Integer points, String pointsDetailId);

    /**
     * 重复销售，记录与那笔记录发生重复
     * @param review
     * @param memberSale
     * @param baseShop
     */
    void updatePhotoViewRejectReason(TbPhotoReview review, TbSalesDetail memberSale, TbBaseShop baseShop);

    /**
     * 更新拍照积分的状态
     * @param review
     * @param statusEnum
     */
    void updatePhotoViewStatus(TbPhotoReview review, PhotoReviewStatusEnum statusEnum);

    /**
     * 通过OCR上传任务taskId查询拍照积分记录
     * @param taskId
     * @return
     */
    TbPhotoReview findByOcrTaskId(String taskId);

    /**
     * 通过OCR上传记录二维码查询拍照积分记录
     * @param qrCode
     * @return
     */
    TbPhotoReview findByQrCode(String qrCode);

    /**
     * 判断OCR拍照积分是否存在
     * checkDto: 检查记录是否存在
     * @return
     */
    TbPhotoReview checkPhotoReviewRecord(PhotoReviewCheckDto checkDto);

    /**
     * 校验生成的单号是否已存在
     * @param qrCode
     * @return
     */
    boolean checkQrCodeExists(String qrCode);

}
