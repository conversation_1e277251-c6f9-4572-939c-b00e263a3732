package com.kerryprops.kip.service.integral.model.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ProgressBarDto
 * @Description 给前端需要的进度条实体类
 * <AUTHOR>
 * @Date 2021/9/29 18:52
 **/
@Data
@Builder
public class ProgressBarDto implements Serializable {

    private static final long serialVersionUID = 8534303162945376823L;

    private int progress;
    private boolean isLowGrade;
    private boolean isTopGrade;
    private boolean keepSuccess;

}
