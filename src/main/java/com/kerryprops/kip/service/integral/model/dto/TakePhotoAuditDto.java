package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/20/2023 13:44
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TakePhotoAuditDto implements Serializable {

    private Long id;

    private String mallId;

    private String state;

    private String sellNo;

    private String shopNo;

    private String sellDate;

    private String reason;

    private BigDecimal money;

    private String auditor;

    /**
     * 是否确认录入
     */
    private String approved;

}
