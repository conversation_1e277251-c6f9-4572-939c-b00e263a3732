package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/23/2022 11:18
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberRegisterResourceDto implements Serializable {

    private String groupId;

    private String mallId;

    private String kipUserId;

    private String registerSource;

    private String registerSourceLabel;

    private String registerSourceRemark;

    /**
     * 会员来源-UTM参数 JSON格式字符串
     */
    private String regMemberSourceOriginalParams;

}
