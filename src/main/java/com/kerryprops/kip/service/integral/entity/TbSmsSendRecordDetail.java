package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * tb_sms_send_record_detail
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_sms_send_record_detail")
public class TbSmsSendRecordDetail implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 记录表(主)id
     */
    @TableField("record_id")
    private Long recordId;

    /**
     * 发送手机号
     */
    @TableField("send_mobile")
    private String sendMobile;

}