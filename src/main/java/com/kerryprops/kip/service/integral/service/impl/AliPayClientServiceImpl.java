package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.FileItem;
import com.alipay.api.domain.*;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.common.enums.TradingAreaEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.AliPayConfig;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.TbInsensatePointsAuthRecordDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.strategy.AliPayStrategy;
import com.kerryprops.kip.service.integral.webservice.resource.AlipayCardTemplateUpdateResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class AliPayClientServiceImpl implements AliPayClientService {
    private final TbMemberAssetService tbMemberAssetService;
    private final RabbitMqService rabbitMqService;
    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private final TbCardMemberRelationService tbCardMemberRelationService;
    private final AliPayConfig aliPayConfig;
    private final MallConfig mallConfig;

    /**
     * 支付宝小程序appId
     */
    private static final String ALI_MINI_PROGRAM_APP_ID = "2021003130628504";
    private static final String POINTS_DETAIL_MINI_PAGE = "pages/accumulatePoints/selfServicePoints";
    private static final String CRM_MINI_HOME_PAGE = "pages/automatic/automaticIndex";

    @Override
    public String openAlipayMarketingCard(AliPayAuthDto dto) throws Exception {
        if (StringUtils.isAnyBlank(dto.getActiveForm().getMobile(), dto.getGroupId())) {
            log.error("AliPay auth - openAlipayMarketingCard empty info.");
            return ERROR_PAGE;
        }
        // 首先判断用户是否存在，如不存在则创建
        TbMemberAsset tbMemberAsset = tbMemberAssetService.getOrCreateMember(dto);
        Date createDate = new Date();
        log.info("AliPay - openAlipayMarketingCard member: {}-{}", tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode());

        // 发卡用户信息
        // set会员卡信息
        CardUserInfo cardUserInfo = new CardUserInfo();
        cardUserInfo.setUserUniId(dto.getMemberToken().getUserId());
        cardUserInfo.setUserUniIdType("UID");

        // 商户会员信息
        MerchantMenber memberExtInfo = new MerchantMenber();
        memberExtInfo.setCell(dto.getActiveForm().getMobile());
        memberExtInfo.setName(dto.getActiveForm().getName());

        // 查询会员卡号对应的后缀
        TradingAreaEnum areaEnum = TradingAreaEnum.getByAbbr(dto.getMallItem().getAbbreviation());
        // 会员卡额外信息
        MerchantCard cardExtInfo = new MerchantCard();
        cardExtInfo.setOpenDate(createDate);
        cardExtInfo.setValidDate(VALID_DATE);
        cardExtInfo.setPoint(tbMemberAsset.getCurrentPoints()+"");
        cardExtInfo.setExternalCardNo(tbMemberAsset.getVipcode() + (Objects.isNull(areaEnum) ? "Z" : areaEnum.getCode()));

        AlipayMarketingCardOpenModel model = new AlipayMarketingCardOpenModel();
        model.setCardUserInfo(cardUserInfo);
        model.setOutSerialNo(System.currentTimeMillis()+"");
        if (StringUtils.isBlank(dto.getTemplateId())) {
            Optional.ofNullable(dto.getMallItem()).ifPresent(mall -> model.setCardTemplateId(mall.getAliCardTemplateId()));
        } else {
            model.setCardTemplateId(dto.getTemplateId());
        }
        model.setMemberExtInfo(memberExtInfo);
        model.setCardExtInfo(cardExtInfo);

        AlipayMarketingCardOpenRequest request = new AlipayMarketingCardOpenRequest();
        request.setBizModel(model);
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(dto.getMallItem().getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        AlipayMarketingCardOpenResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request, dto.getMemberToken().getAccessToken());
        log.info("AlipayMarketingCardOpenResponse: {}", response);
        if (!response.isSuccess()) {
            return ERROR_PAGE;
        }
        // 开卡信息
        MerchantCard cardInfo = response.getCardInfo();
        // 添加授权记录表
        TbInsensatePointsAuthRecord authRecord = TbInsensatePointsAuthRecord.builder().origin(InsensateOriginEnum.ALIPAY.getValue()).cardNo(cardInfo.getBizCardNo())
                .createDate(createDate).openId(dto.getMemberToken().getUserId()).mallId(dto.getMallId()).groupId(dto.getGroupId())
                .status(0).mchid(dto.getMallItem().getAliMallId()).kipUserId(tbMemberAsset.getKipUserId()).build();
        tbInsensatePointsAuthRecordService.insertJudgment(authRecord);
        // 场景触发奖励-支付宝无感积分授权开通(insertJudgment里面已经发送啦)
        //WxAutoPointsMessageDto messageDto = WxAutoPointsMessageDto.builder().type(InsensateOriginEnum.ALIPAY.getValue().toString()).memberId(String.valueOf(tbMemberAsset.getId())).mallId(authRecord.getMallId()).queryDate(DateUtil.formatDateTime(createDate)).build();
        //rabbitMqService.sendMessage(RabbitMqConstant.MEMBER_SENSELESS_REWARD, JsonUtils.objToString(messageDto));
        // 查询开卡地址并返回
        return this.querySchemaUrl(cardInfo.getBizCardNo(), dto.getMallItem());
    }

    @Override
    public String querySchemaUrl(String cardNo, MallItem mallItem) {
        AlipayMarketingCardQueryRequest request = new AlipayMarketingCardQueryRequest();
        AlipayMarketingCardQueryModel model = new AlipayMarketingCardQueryModel();
        model.setTargetCardNo(cardNo);
        model.setTargetCardNoType(BIZ_CARD);
        request.setBizModel(model);
        // 查找对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
            AlipayMarketingCardQueryResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            return response.getSchemaUrl();
        } catch (Exception e) {
            log.error("querySchemaUrl error");
            e.printStackTrace();
        }
        return ERROR_PAGE;
    }

    @Override
    public String create(String orderId, String orderName, String payPrice) {
        //创建支付宝支付连接客户端（沙箱环境）
        AlipayClient client = AliPayStrategy.getClient(TradingAreaEnum.SANDBOX.getAbbreviation());
        AlipayTradePagePayRequest payRequest = new AlipayTradePagePayRequest();
        AlipayTradePagePayModel  model = new AlipayTradePagePayModel();
        model.setOutTradeNo(orderId);
        model.setSubject(orderName);
        model.setTotalAmount(payPrice);
        model.setProductCode("FAST_INSTANT_TRADE_PAY");
        payRequest.setBizModel(model);
        payRequest.setReturnUrl("https://dev-gateway-kip.kerryonvip.com/api/points/v1/alipay/notify/callback");
        payRequest.setNotifyUrl("https://dev-gateway-kip.kerryonvip.com/api/points/v1/alipay/notify/callback");
        try {
            AlipayTradePagePayResponse response = client.pageExecute(payRequest);
            if (response.isSuccess()) {
                log.info("success: {}", response.getBody());
            } else {
                log.error("error: {}", response.getBody());
            }
            return response.getBody();
        } catch (AlipayApiException e) {
            log.error("[支付宝] 支付失败", e);
        }
        return null;
    }

    @Override
    public String createAliPayCardUrl(MallItem mallItem) {
        AlipayMarketingCardActivateurlApplyRequest request = new AlipayMarketingCardActivateurlApplyRequest();
        AlipayMarketingCardActivateurlApplyModel model = new AlipayMarketingCardActivateurlApplyModel();
        model.setTemplateId(mallItem.getAliCardTemplateId());
        // 开卡授权回调地址
        model.setCallback("https://crm.kerryplus.com/xcrm-api/zhsq/alipay/alipayMarketCardTemplateCallBack");
        // 扩展信息，会员领卡完成后将此参数原样带回商户页面,集团下商场的编号，每个商场不同的领卡链接，目前是杭州正式的商场的id
        model.setOutString(mallItem.getMallId());
        request.setBizModel(model);

        AlipayMarketingCardActivateurlApplyResponse response;
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
            response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            // 生成的领卡链接
            String url = response.getApplyCardUrl();
            log.info("领卡链接：" + url);
            // URLDecode解码
            url = URLDecoder.decode(url, StandardCharsets.UTF_8);
            return url;
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String cardMarketingTradeSubscribe(MallItem mallItem) {
        AlipayOpenAppMessageTopicSubscribeRequest request = new AlipayOpenAppMessageTopicSubscribeRequest();
        AlipayOpenAppMessageTopicSubscribeModel model = new AlipayOpenAppMessageTopicSubscribeModel();
        model.setAuthType("app_auth");
        model.setTopic("alipay.business.mall.trade.success");
        model.setCommType("HTTP");
        model.setTag("BIZ_TAG");
        request.setBizModel(model);

        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
            AlipayOpenAppMessageTopicSubscribeResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            if (response.isSuccess()) {
                System.out.println("调用订阅交易信息接口成功");
                return response.getBody();
            } else {
                System.out.println("调用订阅交易信息接口失败" + response.getSubMsg());
            }
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String queryCardTemplate(MallItem mallItem) {
        AlipayMarketingCardTemplateQueryRequest request = new AlipayMarketingCardTemplateQueryRequest();
        AlipayMarketingCardTemplateQueryModel model = new AlipayMarketingCardTemplateQueryModel();
        model.setTemplateId(mallItem.getAliCardTemplateId());
        request.setBizModel(model);
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
//            String marketToken = "";
            AlipayMarketingCardTemplateQueryResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            return response.isSuccess() ? response.getBody() : response.getSubMsg();
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getAlipayMarketingCardActivateurl(MallItem mallItem) {
        AlipayMarketingCardActivateurlApplyRequest request = new AlipayMarketingCardActivateurlApplyRequest();
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
            Map<String, String> params = new HashMap<>(8);
            params.put("template_id", mallItem.getAliCardTemplateId());
            params.put("out_string", mallItem.getMallId());
            params.put("callback", "https://crm.kerryplus.com/xcrm-api/zhsq/alipay/alipayMarketCardTemplateCallBack");
            params.put("follow_app_id", payItem.getAliPayAppId());
            request.setBizContent(JsonUtils.objToString(params));
            AlipayMarketingCardActivateurlApplyResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            return response.isSuccess() ? response.getBody() : response.getSubMsg();
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getAlipayMarketingCardFormtemplateSetRequest(MallItem mallItem) {
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        // 构造请求参数以调用接口
        AlipayMarketingCardFormtemplateSetRequest request = new AlipayMarketingCardFormtemplateSetRequest();
        AlipayMarketingCardFormtemplateSetModel model = new AlipayMarketingCardFormtemplateSetModel();

        // 设置表单字段
        OpenFormFieldDO fields = new OpenFormFieldDO();
        fields.setRequired("{'common_fields':['OPEN_FORM_FIELD_NAME','OPEN_FORM_FIELD_GENDER','OPEN_FORM_FIELD_MOBILE','OPEN_FORM_FIELD_BIRTHDAY']}");
        // 可选字段配置中不能含有必须字段配置的有效值
        fields.setOptional("{'common_fields':['OPEN_FORM_FIELD_IDCARD']}");
        model.setFields(fields);
        // 设置会员卡模板id
        model.setTemplateId(mallItem.getAliCardTemplateId());

        request.setBizModel(model);
        try {
            AlipayMarketingCardFormtemplateSetResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            return response.isSuccess() ? response.getBody() : response.getSubMsg();
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String configTemplate(MallItem mallItem) {
        AlipayMarketingCardFormtemplateSetRequest request = new AlipayMarketingCardFormtemplateSetRequest();
        AlipayMarketingCardFormtemplateSetModel model = new AlipayMarketingCardFormtemplateSetModel();
        model.setTemplateId(mallItem.getAliCardTemplateId());
        OpenFormFieldDO fields = new OpenFormFieldDO();
        fields.setRequired("{'common_fields':['OPEN_FORM_FIELD_NAME','OPEN_FORM_FIELD_GENDER','OPEN_FORM_FIELD_MOBILE','OPEN_FORM_FIELD_BIRTHDAY']}");
        model.setFields(fields);
        request.setBizModel(model);
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
            AlipayMarketingCardFormtemplateSetResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            return response.getBody();
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String createCardTemplate(MallItem mallItem) {

        AlipayMarketingCardTemplateCreateRequest request = new AlipayMarketingCardTemplateCreateRequest();
        AlipayMarketingCardTemplateCreateModel model = new AlipayMarketingCardTemplateCreateModel();
        model.setRequestId(System.currentTimeMillis() + "");
        model.setCardType("OUT_MEMBER_CARD");
        // 卡包详情页面中展现出的卡码
        model.setWriteOffType("qrcode");
        // 业务卡号后缀的长度
        model.setBizNoSuffixLen("20");

        // 模板样式
        TemplateStyleInfoDTO style = new TemplateStyleInfoDTO();
        style.setCardShowName("榕城·江上图会员卡");
        //调用支付宝接口上传自己所需的logo和背景图
        style.setLogoId("A*A0iSTrEhMNgAAAAAAAAAAAAADmV1AQ");
        //背景图
        style.setBackgroundId("A*pcogRIejU9MAAAAAAAAAAAAADmV1AQ");
        //字体颜色（非背景色），只影响卡详情中部信息区域字体颜色
        style.setBgColor("rgb(55,112,179)");
        model.setTemplateStyleInfo(style);

        TemplateColumnInfoDTO columnInfoPoint = new TemplateColumnInfoDTO();
        columnInfoPoint.setCode("POINT_INFO");
        columnInfoPoint.setTitle("会员积分");
        columnInfoPoint.setOperateType("openWeb");
        MoreInfoDTO pointMoreInfo = new MoreInfoDTO();
        pointMoreInfo.setUrl("https://crm.kerryplus.com/h5-alipay/#/pages/integral/myIntegral?cardno=$ExternalCardNo$");
        Map<String, String> params = new HashMap<>(2);
        params.put("groupId", mallItem.getGroupId());
        pointMoreInfo.setParams(JsonUtils.objToString(params));

        columnInfoPoint.setMoreInfo(pointMoreInfo);

        //智能积分授权链接记得修改
        TemplateColumnInfoDTO columnInfoAuth = new TemplateColumnInfoDTO();
        columnInfoAuth.setCode("AUTH_INFO");
        columnInfoAuth.setTitle("开通智能积分授权");
        columnInfoAuth.setOperateType("openWeb");
        MoreInfoDTO authMoreInfo = new MoreInfoDTO();
        authMoreInfo.setUrl("https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=2021003157640328&scope=mall_smart_points&redirect_uri=https%3A%2F%2Fcrm.kerryplus.com%2Fxcrm-api%2Fzhsq%2Falipay%2FalipayMarketCardTemplateCallBack");
        params = new HashMap<>(2);
        params.put("mallId", mallItem.getMallId());
        authMoreInfo.setParams(JsonUtils.objToString(params));
        columnInfoAuth.setMoreInfo(authMoreInfo);
        model.setColumnInfoList(Lists.newArrayList(columnInfoPoint, columnInfoAuth));

        // 字段规则列表，会员卡开卡过程中，会员卡信息的生成规则
        TemplateFieldRuleDTO ruleBalance = new TemplateFieldRuleDTO();
        ruleBalance.setFieldName("Balance");
        ruleBalance.setRuleName("ASSIGN_FROM_REQUEST");
        ruleBalance.setRuleValue("Balance");

        TemplateFieldRuleDTO rulePoint = new TemplateFieldRuleDTO();
        rulePoint.setFieldName("Point");
        rulePoint.setRuleName("ASSIGN_FROM_REQUEST");
        rulePoint.setRuleValue("Point");

        TemplateFieldRuleDTO ruleOpenDate = new TemplateFieldRuleDTO();
        ruleOpenDate.setFieldName("OpenDate");
        ruleOpenDate.setRuleName("ASSIGN_FROM_REQUEST");
        ruleOpenDate.setRuleValue("OpenDate");

        model.setFieldRuleList(Lists.newArrayList(ruleBalance, rulePoint, ruleOpenDate));

        request.setBizModel(model);

        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
            AlipayMarketingCardTemplateCreateResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);

            String code = response.getCode();
            if ("10000".equals(code)) {
                String responseTemplateId = response.getTemplateId();
                System.out.println("商户会员卡模板创建成功!");
                System.out.println("模板id: " + responseTemplateId);
            } else if ("40004".equals(code)) {
                System.out.println("创建模板失败,失败原因: " + response.getSubMsg());
                System.out.println("创建模板失败,详情请查看方法返回值:" + response.getBody());
            } else {
                System.out.println("创建模板失败,详情请查看方法返回值:" + response.getBody());
            }
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Boolean validateSignature(Map<String, String> params, String abbreviation) {
        // 商圈交易通知
        try {
            return AlipaySignature.rsaCheckV1(params, aliPayConfig.getByName(abbreviation).getAlipayPublicKey(), IntegralConstant.DEFAULT_ENC, "RSA2");
        } catch (AlipayApiException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public void syncAlipayPoints(SalesAutoPointsDto dto) {
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByVipcodeAndGroupId(dto.getVipcode(), dto.getGroupId());
        if (StringUtils.isBlank(tbMemberAsset.getKipUserId())) {
            return;
        }
        MallItem mallItem = mallConfig.getByMallId(dto.getMallId());
        if (Objects.isNull(mallItem)) {
            return;
        }
        TbInsensatePointsAuthRecordDto authRecord = tbInsensatePointsAuthRecordService.getByAliUserId(dto.getWxOrAliId(), mallItem.getMallId());
        if (Objects.isNull(authRecord)) {
            return;
        }
        // 会员开卡信息
        TbCardMemberRelation relation = tbCardMemberRelationService.getByMallIdAndAliUserId(dto.getMallId(), dto.getWxOrAliId());
        // 同步支付宝获得积分数
        AlipayMarketingCardUpdateRequest updateRequest = new AlipayMarketingCardUpdateRequest();
        AlipayMarketingCardUpdateModel updateModel = new AlipayMarketingCardUpdateModel();
        updateModel.setTargetCardNo(authRecord.getCardNo());
        updateModel.setTargetCardNoType(BIZ_CARD);
        updateModel.setOccurTime(new Date());
        MerchantCard merchantCard = new MerchantCard();
        merchantCard.setOpenDate(Objects.nonNull(relation) ? relation.getOpenDate() : new Date());
        merchantCard.setValidDate(VALID_DATE);
        merchantCard.setTemplateId(mallItem.getAliCardTemplateId());
        merchantCard.setPoint(NumberUtil.round(tbMemberAsset.getCurrentPoints(), 0).toString());
        updateModel.setCardInfo(merchantCard);
        //支付宝会员卡积分变动提醒
        log.info("支付宝商圈积分变动提醒: {}", dto.getExchangePoints());
        MerchantCardMsgInfo merchantCardMsgInfo = new MerchantCardMsgInfo();
        merchantCardMsgInfo.setChangedPoint(dto.getExchangePoints() + "");
        updateModel.setMerchantCardMsgInfo(merchantCardMsgInfo);
        updateRequest.setBizModel(updateModel);
        try {
            AlipayMarketingCardUpdateResponse updateResponse = AliPayStrategy.getClient(dto.getAppId()).execute(updateRequest);
            if (updateResponse.isSuccess()) {
                log.info("AliPay callback success - cardNO: {}", updateModel.getTargetCardNo());
            } else {
                log.error("AliPay callback failed：" + updateResponse.getCode() + "msg:" + updateResponse.getMsg());
                // 再次同步，通过外部卡号重新查询支付宝业务卡号再次同步
                if (Objects.nonNull(relation)) {
                    this.queryAndReSyncAlipay(dto, tbMemberAsset, mallItem, relation);
                }
            }
        } catch (AlipayApiException e) {
            e.printStackTrace();
            // 再次同步，通过外部卡号重新查询支付宝业务卡号再次同步
            if (Objects.nonNull(relation)) {
                this.queryAndReSyncAlipay(dto, tbMemberAsset, mallItem, relation);
            }
        }
    }

    /**
     * 查询会员卡信息并再次同步
     * @param dto
     * @param mallItem
     * @param tbMemberAsset
     * @param relation
     */
    private void queryAndReSyncAlipay(SalesAutoPointsDto dto, TbMemberAsset tbMemberAsset, MallItem mallItem, TbCardMemberRelation relation) {
        if (Objects.nonNull(relation) && StringUtils.isBlank(relation.getExternalCardNo())) {
            TradingAreaEnum areaEnum = TradingAreaEnum.getByAbbr(mallItem.getAbbreviation());
            if (Objects.nonNull(areaEnum)) {
                relation.setExternalCardNo(String.format("%s%s", tbMemberAsset.getVipcode(), areaEnum.getCode()));
            } else {
                log.info("支付宝会员卡外部卡号缺失: {}-{}", tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode());
                return;
            }
        }
        // 通过支付宝外部卡号查询会员卡信息
        MerchantCard cardDetail = this.getCardDetail(relation, mallItem.getMallId(), EXTERNAL_CARD);
        if (Objects.isNull(cardDetail)) {
            log.info("通过支付宝外部会员卡号查询会员卡信息为空, {}-{}", tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode());
            return;
        }
        AlipayMarketingCardUpdateRequest updateRequest = new AlipayMarketingCardUpdateRequest();
        AlipayMarketingCardUpdateModel updateModel = new AlipayMarketingCardUpdateModel();
        updateModel.setTargetCardNo(cardDetail.getBizCardNo());
        updateModel.setTargetCardNoType(BIZ_CARD);
        updateModel.setOccurTime(new Date());
        MerchantCard merchantCard = new MerchantCard();
        merchantCard.setBizCardNo(cardDetail.getBizCardNo());
        merchantCard.setOpenDate(Objects.nonNull(relation.getCreateTime()) ? relation.getCreateTime() : new Date());
        merchantCard.setValidDate(VALID_DATE);
        merchantCard.setTemplateId(mallItem.getAliCardTemplateId());
        merchantCard.setPoint(NumberUtil.round(tbMemberAsset.getCurrentPoints(), 0).toString());
        updateModel.setCardInfo(merchantCard);
        //支付宝会员卡积分变动提醒
        log.info("支付宝商圈积分变动提醒2: {}", dto.getExchangePoints());
        MerchantCardMsgInfo merchantCardMsgInfo = new MerchantCardMsgInfo();
        merchantCardMsgInfo.setChangedPoint(dto.getExchangePoints() + "");
        updateModel.setMerchantCardMsgInfo(merchantCardMsgInfo);
        updateRequest.setBizModel(updateModel);
        try {
            AlipayMarketingCardUpdateResponse updateResponse = AliPayStrategy.getClient(dto.getAppId()).execute(updateRequest);
            if (updateResponse.isSuccess()) {
                log.info("AliPay callback success - cardNO2: {}", updateModel.getTargetCardNo());
            } else {
                log.error("AliPay callback failed2：" + updateResponse.getCode() + "msg:" + updateResponse.getMsg());
            }
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void clearingNotify(AliPayBizContentDto callbackDto, SalesAutoPointsDto autoPointsDto) {
        AlipayBusinessMallPointsNotifyRequest pointsNotifyRequest = new AlipayBusinessMallPointsNotifyRequest();
        String bizContent = JsonUtils.objToString(AliPayTradeDto.builder().buyerId(callbackDto.getBuyerId()).tradeNo(callbackDto.getTradeNo())
                .earnPoints(true).obtainPoints(autoPointsDto.getExchangePoints()).pointsUpdateTime(new Date()).build());
        pointsNotifyRequest.setBizContent(bizContent);
        try {
            AlipayBusinessMallPointsNotifyResponse pointsNotifyResponse = AliPayStrategy.getClient(callbackDto.getAppId()).execute(pointsNotifyRequest);
            if (pointsNotifyResponse.isSuccess()) {
                log.info("AliPay - Clearing succeed");
            } else {
                log.error("AliPay - Clearing failed. Error info: {}", pointsNotifyResponse.getMsg());
            }
        } catch (AlipayApiException e) {
            log.error("AliPay - Clearing failed. tradeNo: {}", callbackDto.getTradeNo());
            e.printStackTrace();
        }
    }

    @Override
    public MerchantCard memberCardDetailByCardNo(String cardNo, String mallId) {
        TbCardMemberRelation relation = tbCardMemberRelationService.getByMallIdAndExternalCardNo(mallId, cardNo);
        if (Objects.isNull(relation)) {
            log.info("未查询到支付宝开卡信息, {}-{}", mallId, cardNo);
            return null;
        }
        return this.getCardDetail(relation, mallId, IntegralConstant.ALI_BIZ_CARD);
    }

    @Override
    public MerchantCard memberCardDetailByAliUserId(String aliUserId, String mallId) {
        TbCardMemberRelation relation = tbCardMemberRelationService.getByMallIdAndAliUserId(mallId, aliUserId);
        if (Objects.isNull(relation)) {
            log.info("未查询到支付宝开卡信息, {}-{}", mallId, aliUserId);
            return null;
        }
        return this.getCardDetail(relation, mallId, IntegralConstant.ALI_BIZ_CARD);
    }

    @Override
    public MerchantCard memberCardDetailByMobile(String mobile, String mallId) {
        TbCardMemberRelation relation = tbCardMemberRelationService.getByMallIdAndMobile(mallId, mobile);
        if (Objects.isNull(relation)) {
            log.info("未查询到支付宝开卡信息, {}-{}", mallId, mobile);
            return null;
        }
        return this.getCardDetail(relation, mallId, IntegralConstant.ALI_BIZ_CARD);
    }

    /**
     * 调用支付宝API查询会员开卡信息
     * @param relation
     * @param mallId
     * @param targetCardNoType
     * @return
     */
    private MerchantCard getCardDetail(TbCardMemberRelation relation, String mallId, String targetCardNoType) {
        AlipayMarketingCardQueryRequest request = new AlipayMarketingCardQueryRequest();
        AlipayMarketingCardQueryModel model = new AlipayMarketingCardQueryModel();
        if (IntegralConstant.ALI_BIZ_CARD.equals(targetCardNoType)) {
            model.setTargetCardNo(relation.getBizCardNo());
            model.setTargetCardNoType(BIZ_CARD);
        } else {
            model.setTargetCardNo(relation.getExternalCardNo());
            model.setTargetCardNoType(EXTERNAL_CARD);
        }
        request.setBizModel(model);
        // 用户uid
        CardUserInfo userInfo = new CardUserInfo();
        userInfo.setUserUniIdType("UID");
        userInfo.setUserUniId(relation.getUserId());
        model.setCardUserInfo(userInfo);
        // 查找对应商圈
        MallItem mallItem = mallConfig.getByMallId(mallId);
        Assert.notNull(mallItem);
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
            AlipayMarketingCardQueryResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            if (response.isSuccess()) {
                return response.getCardInfo();
            } else {
                log.error("AliPay - failed to query card info. CardNo: {},  error info: {}", relation.getBizCardNo(), response.getMsg());
            }
        } catch (Exception e) {
            log.error("AliPay - failed to query card info. carNo: {}", relation.getBizCardNo());
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public MerchantCard getAliMemberCardDetail(String userUniId, String bizCardNo, String mallId) {
        AlipayMarketingCardQueryRequest request = new AlipayMarketingCardQueryRequest();
        AlipayMarketingCardQueryModel model = new AlipayMarketingCardQueryModel();
        model.setTargetCardNo(bizCardNo);
        model.setTargetCardNoType(BIZ_CARD);
        request.setBizModel(model);
        // 用户uid
        CardUserInfo userInfo = new CardUserInfo();
        userInfo.setUserUniIdType("UID");
        userInfo.setUserUniId(userUniId);
        model.setCardUserInfo(userInfo);
        // 查找对应商圈
        MallItem mallItem = mallConfig.getByMallId(mallId);
        Assert.notNull(mallItem);
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
            AlipayMarketingCardQueryResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            if (response.isSuccess()) {
                return response.getCardInfo();
            } else {
                log.error("AliPay - failed to query card info. CardNo: {},  error info: {}", bizCardNo, response.getMsg());
            }
        } catch (Exception e) {
            log.error("AliPay - failed to query card info. carNo: {}", bizCardNo);
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public ActiveFormDto queryActiveForm(AliPayAuthDto dto) throws JsonProcessingException {
        // 构造request
        AlipayMarketingCardActivateformQueryRequest request = new AlipayMarketingCardActivateformQueryRequest();
        AlipayMarketingCardActivateformQueryModel model = new AlipayMarketingCardActivateformQueryModel();
        model.setBizType(MEMBER_CARD);
        model.setTemplateId(dto.getTemplateId());
        model.setRequestId(dto.getRequestId());
        request.setBizModel(model);

        // 获取response
        AlipayMarketingCardActivateformQueryResponse response;
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(dto.getMallItem().getAbbreviation());
        // 不为空判断
        Assert.notNull(payItem);
        try {
            response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request, dto.getMemberToken().getAccessToken());
        } catch (AlipayApiException e) {
            log.error("AliPay memberAuth - failed to get activate form: {}", e.getMessage());
            return null;
        }
        // 解析response
        log.info("AliPay getActiveForm infos: {}", response.getInfos());
        // 返回的是一个数组字符串: "[{\"OPEN_FORM_FIELD_BIRTHDAY\":\"03-27\"},{\"OPEN_FORM_FIELD_GENDER\":\"女\"},{\"OPEN_FORM_FIELD_MOBILE\":\"15221024085\"},{\"OPEN_FORM_FIELD_NAME\":\"熊慧\"}]"
        List<Map> attrMap = JsonUtils.stringToList(response.getInfos(), Map.class);
        ActiveFormDto formDto = ActiveFormDto.builder().groupId(dto.getGroupId()).mallId(dto.getMallId()).build();
        for (Map map : attrMap) {
            String birthday = MapUtils.getString(map, OPEN_FORM_FIELD_BIRTHDAY);
            if (StringUtils.isNotBlank(birthday)) {
                formDto.setBirthday(birthday);
            }
            String gender = MapUtils.getString(map, OPEN_FORM_FIELD_GENDER);
            if (StringUtils.isNotBlank(gender)) {
                formDto.setGender(gender);
            }
            String mobile = MapUtils.getString(map, OPEN_FORM_FIELD_MOBILE);
            if (StringUtils.isNotBlank(mobile)) {
                formDto.setMobile(mobile);
            }
            String name = MapUtils.getString(map, OPEN_FORM_FIELD_NAME);
            if (StringUtils.isNotBlank(name)) {
                formDto.setName(name);
            }
        }
        return formDto;
    }

    @Override
    public AliPayMemberTokenDto getAccessToken(String authCode, String abbreviation) {
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
        request.setCode(authCode);
        request.setGrantType(AUTH_CODE);
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(abbreviation);
        // 不为空判断
        Assert.notNull(payItem);
        try {
            AlipaySystemOauthTokenResponse authTokenResponse = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            log.info("Get Alipay AccessToken Response: {}", JsonUtils.objToString(authTokenResponse));
            if (authTokenResponse.isSuccess()) {
                return AliPayMemberTokenDto.builder().accessToken(authTokenResponse.getAccessToken()).userId(authTokenResponse.getUserId()).build();
            } else {
                return null;
            }
        } catch (AlipayApiException e) {
            // 处理异常
            log.error("AliPay getAccessToken - AlipayApiException: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public void uploadAlipayCardBackgroundImage(String path, String mallId) {
        String type = path.substring(path.lastIndexOf(".") + 1);
        // 素材请求类
        AlipayOfflineMaterialImageUploadRequest alipayRequest = new AlipayOfflineMaterialImageUploadRequest();
        // 图片类型，当前支持bmp,png,jpeg,jpg,gif 5个类型
        alipayRequest.setImageType(type);
        alipayRequest.setImageName("SZKP_CRM_MEMBER_CARD_BG.jpg");
        FileItem fileItem = new FileItem(new File(path));
        alipayRequest.setImageContent(fileItem);
        MallItem mallItem = mallConfig.getByMallId(mallId);
        Assert.notNull(mallItem);
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        try {
            AlipayOfflineMaterialImageUploadResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(alipayRequest);
            if (response.isSuccess()) {
                log.info("Alipay Upload Image Id: {} - {}", response.getImageId(), response.getImageUrl());
            } else {
                log.error("{} - {}", response.getSubCode(), response.getSubMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用会员卡上传图片接口异常！");
        }
    }

    @Override
    public void updateAlipayCardTemplateRequest(AlipayCardTemplateUpdateResource resource) throws Exception {
        AlipayMarketingCardTemplateModifyRequest request = new AlipayMarketingCardTemplateModifyRequest();
        // 确定商场信息
        MallItem mallItem = mallConfig.getByMallId(resource.getMallId());
        Assert.notNull(mallItem);
        // 通过abb查询对应的alipay配置
        AliPayItem payItem = aliPayConfig.getByName(mallItem.getAbbreviation());
        Assert.notNull(payItem);

        AlipayMarketingCardTemplateModifyModel model = new AlipayMarketingCardTemplateModifyModel();
        model.setRequestId(System.currentTimeMillis() + "");
        // 卡包详情页面中展现出的卡码
        model.setWriteOffType("qrcode");
        // 杭州会员卡模版id
        model.setTemplateId(mallItem.getAliCardTemplateId());
        // 业务卡号后缀长度
        model.setBizNoSuffixLen("20");
        // 模板样式
        TemplateStyleInfoDTO styleInfoDTO = new TemplateStyleInfoDTO();
        styleInfoDTO.setCardShowName(resource.getCardShowName());
        //调用支付宝接口上传自己所需的logo和背景图
        styleInfoDTO.setLogoId(resource.getLogoId());
        //背景图
        styleInfoDTO.setBackgroundId(resource.getBackgroundId());
        //字体颜色（非背景色），只影响卡详情中部信息区域字体颜色
        styleInfoDTO.setBgColor("rgb(55,112,179)");
        model.setTemplateStyleInfo(styleInfoDTO);

        // alipay会员卡页面自定义配置栏目
        // 1、会员积分 - 直接打开小程序积分明细页面
        TemplateColumnInfoDTO columnInfoPoint = new TemplateColumnInfoDTO();
        columnInfoPoint.setCode("POINT_INFO");
        columnInfoPoint.setTitle("会员积分");
        columnInfoPoint.setOperateType("openWeb");
        MoreInfoDTO pointMoreInfo = new MoreInfoDTO();
        // 跳转小程序积分明细页面
//        String query = String.format("groupId=%s&mallId=%s", mallItem.getGroupId(), mallItem.getMallId());
//        String schemeUrl = String.format("alipays://platformapi/startapp?appId=%s&page=%s&query=%s", ALI_MINI_PROGRAM_APP_ID, POINTS_DETAIL_MINI_PAGE, URLEncoder.encode(query, IntegralConstant.DEFAULT_ENC));
//        pointMoreInfo.setUrl(schemeUrl);
        pointMoreInfo.setUrl("https://crm.kerryplus.com/h5-alipay/#/pages/integral/myIntegral?groupId=" + mallItem.getGroupId() + "&mallId=" + mallItem.getMallId() + "&cardno=$ExternalCardNo$");
        pointMoreInfo.setParams("{}");
        columnInfoPoint.setMoreInfo(pointMoreInfo);

        // alipay会员卡页面自定义配置栏目
        // 2、会员积分 - 直接打开小程序积分明细页面
        TemplateColumnInfoDTO blColumnInfo = new TemplateColumnInfoDTO();
        blColumnInfo.setCode("BULU_INFO");
        blColumnInfo.setTitle("积分补录");
        blColumnInfo.setOperateType("openWeb");
        MoreInfoDTO blMoreInfo = new MoreInfoDTO();
        // 跳转小程序积分明细页面
        String query = String.format("mallId=%s", mallItem.getAliMallId());
        String schemeUrl = String.format("alipays://platformapi/startapp?appId=%s&page=%s&query=%s", "2021002120625430", "pages/index/index", URLEncoder.encode(query, IntegralConstant.DEFAULT_ENC));
        blMoreInfo.setUrl(schemeUrl);
        blMoreInfo.setParams("{}");
        blColumnInfo.setMoreInfo(blMoreInfo);

        // 3、智能积分授权链接记得修改
        TemplateColumnInfoDTO columnInfoAuth = new TemplateColumnInfoDTO();
        columnInfoAuth.setCode("AUTH_INFO");
        columnInfoAuth.setTitle("开通智能积分授权");
        columnInfoAuth.setOperateType("openWeb");
        MoreInfoDTO authMoreInfo = new MoreInfoDTO();
        // 智能积分授权添加参数
        String redirectUri = payItem.getCallBackUrl() + "?mallId=" + mallItem.getMallId();
        // Url encode
        redirectUri = URLEncoder.encode(redirectUri, IntegralConstant.DEFAULT_ENC);
        authMoreInfo.setUrl("https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=" + payItem.getAliPayAppId() + "&scope=mall_smart_points&redirect_uri=" + redirectUri);
        authMoreInfo.setParams("{}");
        columnInfoAuth.setMoreInfo(authMoreInfo);
        // 设置栏目
        model.setColumnInfoList(Lists.newArrayList(columnInfoPoint, blColumnInfo, columnInfoAuth));
        // 设置适用门店-BKC
        if ("BKC".equals(mallItem.getAbbreviation())) {
            model.setShopIds(Collections.singletonList("2017101600077010000045987776"));
        }

        // 字段规则列表，会员卡开卡过程中，会员卡信息的生成规则
        // 1、金额
        TemplateFieldRuleDTO ruleBalance = new TemplateFieldRuleDTO();
        ruleBalance.setFieldName("Balance");
        ruleBalance.setRuleName("ASSIGN_FROM_REQUEST");
        ruleBalance.setRuleValue("Balance");
        // 2、整数
        TemplateFieldRuleDTO rulePoint = new TemplateFieldRuleDTO();
        rulePoint.setFieldName("Point");
        rulePoint.setRuleName("ASSIGN_FROM_REQUEST");
        rulePoint.setRuleValue("Point");
        // 3、开卡日期
        TemplateFieldRuleDTO ruleOpenDate = new TemplateFieldRuleDTO();
        ruleOpenDate.setFieldName("OpenDate");
        ruleOpenDate.setRuleName("ASSIGN_FROM_REQUEST");
        ruleOpenDate.setRuleValue("OpenDate");
        model.setFieldRuleList(Lists.newArrayList(ruleBalance, rulePoint, ruleOpenDate));

        // 卡行动点配置
        // 1、打开小程序
        TemplateActionInfoDTO openAliMiniProgram = new TemplateActionInfoDTO();
        openAliMiniProgram.setCode("TO_CLOCK_IN");
        openAliMiniProgram.setText("会员小程序");
        openAliMiniProgram.setUrlType("miniAppUrl");
        // 小程序跳转参数配置
        TemplateActionMiniAppUrlDTO appUrlDTO = new TemplateActionMiniAppUrlDTO();
        appUrlDTO.setMiniAppId(ALI_MINI_PROGRAM_APP_ID);
        appUrlDTO.setMiniPageParam(CRM_MINI_HOME_PAGE);
        appUrlDTO.setMiniQueryParam(String.format("groupId=%s&mallid=%s", mallItem.getGroupId(), mallItem.getMallId()));
        appUrlDTO.setDisplayOnList("false");
        openAliMiniProgram.setMiniAppUrl(appUrlDTO);
        model.setCardActionList(Arrays.asList(openAliMiniProgram));
        // 配置开卡信息
        TemplateOpenCardConfDTO openCardConfDTO = new TemplateOpenCardConfDTO();
        openCardConfDTO.setOpenCardSourceType("MER");
        openCardConfDTO.setOpenCardUrl(OPEN_CARD_URL + "app_id=" + payItem.getAliPayAppId() + "&template_id=" + mallItem.getAliCardTemplateId()
                + "&__webview_options__=canPullDown%3dNO%26transparentTitle%3dauto&out_string=" + mallItem.getMallId()
                + "&callback=" + redirectUri);
        openCardConfDTO.setSourceAppId(payItem.getAliPayAppId());
        model.setOpenCardConf(openCardConfDTO);

        // 填充请求类参数
        request.setBizModel(model);

        try {
            AlipayMarketingCardTemplateModifyResponse response = AliPayStrategy.getClient(payItem.getAliPayAppId()).execute(request);
            if (response.isSuccess()) {
                log.info("Alipay Upload Image Id: {} - {}", response.getCode(), response.getTemplateId());
            } else {
                log.error("{} - {}", response.getSubCode(), response.getSubMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用会员卡更新API异常！");
        }
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String uri = "https://crm.kerryplus.com/xcrm-api/zhsq/alipay/alipayMarketCardTemplateCallBack?mallId=111111111";
        uri = URLEncoder.encode(uri, IntegralConstant.DEFAULT_ENC);
        System.out.println(uri);
    }

}
