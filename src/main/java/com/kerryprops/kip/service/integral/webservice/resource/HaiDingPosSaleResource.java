package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/19/2023 09:22
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "海鼎销售查询类")
public class HaiDingPosSaleResource implements Serializable {

    @NotBlank(message = "groupId不能为空.")
    @Schema( description = "所属group id")
    private String groupId;

    @NotBlank(message = "vipcode不能为空.")
    @Schema( description = "会员号")
    private String vipcode;

    @NotBlank(message = "shopNo不能为空")
    @Schema( description = "店铺号")
    private String shopNo;

    @NotBlank(message = "mallId不能为空.")
    @Schema( description = "商场")
    private String mallId;

    @NotBlank(message = "docNo不能为空.")
    @Schema( description = "流水号")
    private String docNo;

    @NotBlank(message = "saleDate不能为空")
    @Schema( description = "销售日期, yyyy-MM-dd hh:mm:ss")
    private String saleDate;

    @NotBlank(message = "payAmount不能为空.")
    @Schema( description = "实付金额")
    private String payAmount;

    @Schema( description = "订单金额")
    private String totalAmount;

    @Schema( description = "积分抵扣金额")
    private String amountOfPoints;

    @NotBlank(message = "discounts不能为空.")
    @Schema( description = "优惠金额")
    private String discounts;

    @Schema( description = "pos机id")
    private String posId;

    @Schema( description = "创建者，默认pos")
    private String createUser;

    @Schema( description = "销售类型，POS销售为2")
    private String saleType;

    @NotBlank(message = "optType不能为空")
    @Schema( description = "操作类型: PAYMENT/REFUND")
    private String optType;

}
