package com.kerryprops.kip.service.integral.exception;

import com.kerryprops.kip.service.integral.common.current.IError;
import lombok.Data;

import java.util.function.Supplier;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2022 17:03
 **********************************************************************************************************************/

@Data
public class BizNotFoundException extends RuntimeException {

    protected final Integer code;
    protected final String msg;

    public BizNotFoundException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public BizNotFoundException(IError ie) {
        super(ie.getMsg());
        this.code = ie.getCode();
        this.msg = ie.getMsg();
    }

    public static BizNotFoundException error(IError iError) {
        return new BizNotFoundException(iError);
    }

    public static BizNotFoundException error(Integer code, String msg) {
        return new BizNotFoundException(code, msg);
    }

    public static Supplier<BizNotFoundException> bizNotFoundException(IError iError) {
        return () -> new BizNotFoundException(iError);
    }

}
