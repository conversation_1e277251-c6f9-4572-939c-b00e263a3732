package com.kerryprops.kip.service.integral.webservice.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/13/2023 13:39
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "店铺积分比例响应类")
public class TbIntegralShopRateResponse implements Serializable {

    @Schema( name = "主键id")
    private Long id;

    @Schema( name = "集团id")
    private String groupId;

    @Schema( name = "商场id")
    private String mallId;

    @Schema( name = "集团会员等级id")
    private String gradeId;

    @Schema( name = "等级名称")
    private String gradeName;

    @Schema( name = "店铺编号")
    private String shopId;

    @Schema( name = "店铺别名")
    private String shopAliasName;

    @Schema( name = "店铺名")
    private String shopName;

    @Schema( name = "金额")
    private BigDecimal money;

    @Schema( name = "积分数")
    private BigDecimal pointNum;

    @Schema( name = "状态")
    private int status;

    @Schema( name = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    private String creator;

    @Schema( name = "修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    private String updater;

    @Schema( name = "店铺品牌名称")
    private String retailBrandName;

    private List<GradeIntegralResponse> list;

}