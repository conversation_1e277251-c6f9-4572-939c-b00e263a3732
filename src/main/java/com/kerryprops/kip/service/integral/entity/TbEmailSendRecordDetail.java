//package com.kerryprops.kip.service.integral.entity;
//
//import com.baomidou.mybatisplus.annotation.IdType;
//import com.baomidou.mybatisplus.annotation.TableField;
//import com.baomidou.mybatisplus.annotation.TableId;
//import com.baomidou.mybatisplus.annotation.TableName;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.io.Serializable;
//
///**
// * tb_email_send_record_detail
// * <AUTHOR> */
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//@TableName("tb_email_send_record_detail")
//public class TbEmailSendRecordDetail implements Serializable {
//    /**
//     * 邮箱发送记录id
//     */
//    @TableId(value = "id", type = IdType.INPUT)
//    private Long id;
//
//    /**
//     * 记录表(主)id
//     */
//    @TableField("record_id")
//    private Long recordId;
//
//    /**
//     * 发送邮箱
//     */
//    @TableField("send_email")
//    private String sendEmail;
//
//    /**
//     * 备注(暂用于电子劵,批次id)
//     */
//    @TableField("remark")
//    private String remark;
//
//}