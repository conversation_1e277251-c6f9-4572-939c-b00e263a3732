package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/19/2022 16:35
 **********************************************************************************************************************/

@FeignClient(name = "cip-middleware-service", url = "${kerry.services.cip-service:default}")
public interface CipMiddleWareClient {


    /**
     * 判断会员是否在人群包中
     * @param userId userId
     * @param crowdId crowdId
     * @return
     */
    @GetMapping(value = "/cip/api/tagging/internal/hasCrowdbyUserV3")
    CipResultDto<List<CrowdResultDto>> hasCrowdByUser(@RequestParam("user_id") String userId, @RequestParam("crowd_id") String crowdId);

}
