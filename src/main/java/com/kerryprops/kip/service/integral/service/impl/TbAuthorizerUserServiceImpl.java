package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.entity.TbAuthorizerUser;
import com.kerryprops.kip.service.integral.mapper.TbAuthorizerUserMapper;
import com.kerryprops.kip.service.integral.service.TbAuthorizerUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/14/2024 14:07
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbAuthorizerUserServiceImpl implements TbAuthorizerUserService {

    private final TbAuthorizerUserMapper tbAuthorizerUserMapper;

    @Override
    public TbAuthorizerUser getAuthorizerUserByUser(String user) {
        if (StringUtils.isBlank(user)) {
            return null;
        }
        return tbAuthorizerUserMapper.getAuthorizerUserByUser(user);
    }
}
