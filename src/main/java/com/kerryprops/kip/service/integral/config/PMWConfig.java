package com.kerryprops.kip.service.integral.config;

import com.kerryprops.kip.pmw.client.config.PaymentConfigBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@ComponentScan(basePackages = "com.kerryprops.kip.pmw.client")
public class PMWConfig {

    @Value("${kerry.services.payment:default}")
    private String payment;

    @Bean
    public PaymentConfigBean paymentConfigBean() {
        PaymentConfigBean configBean = new PaymentConfigBean();
        configBean.setConnectionTimeout("3000");
        configBean.setPmwHost(payment);

        configBean.setKeystorePath("classpath:/rsa/store.jks");
        configBean.setKeystorePassword("Kerry+2022");
        configBean.setAliasName("point_service");
        configBean.setKeyPassword("Kerry+2022");

        configBean.setClientPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsUoakaogSefZWK5lH70SrFV3xtyGsUGGyMuUj3XkWbWtziSN3QYBkDeo+FHGANNLWbV7i0hVsHC7cHjVZ/OOe4YdCF6KlpNTWfEjSJkfrxFtyoJhpW7PPXYu9HvCRstZ65L5j0EhSK2bg5KlkY4oBv9XEzOiDNe3wnxvqQCd6Q+pQybQpTIJLFNQppoyqgSzNTCtxceZbR+OY6xwA3b1OesvcksutZtp6XkVsvUiClYIaVWnyE14bmoROQPNskeURVFOSu/ITquWHANINNHF3bQ8HPsdBvtyPt4Rug2zuSKKhnpcDpbnqjZY4a7F0wQkGLPf0LdkvgqnG2T+g/GuyQIDAQAB");

        return configBean;
    }

}
