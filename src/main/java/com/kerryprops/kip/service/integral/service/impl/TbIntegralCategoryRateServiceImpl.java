package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate;
import com.kerryprops.kip.service.integral.mapper.TbIntegralCategoryRateMapper;
import com.kerryprops.kip.service.integral.service.TbIntegralCategoryRateService;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralCategoryRateResource;
import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbIntegralCategoryRateServiceImpl extends ServiceImpl<TbIntegralCategoryRateMapper, TbIntegralCategoryRate> implements TbIntegralCategoryRateService {
    private final TbIntegralCategoryRateMapper tbIntegralCategoryRateMapper;

    @Override
public List<TbIntegralCategoryRate> categoryRatePage(String groupId, String categoryId, List<String> mallId, int page, int size) {
        return tbIntegralCategoryRateMapper.categoryRatePage(groupId, categoryId, mallId,page * size, size);
    }

    @Override
    public int getTotal(String groupId, String categoryId, List<String> mallId) {
        return tbIntegralCategoryRateMapper.getTotal(groupId, categoryId, mallId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateCategoryRate(TbIntegralCategoryRateResource resource) {
        // 新增或修改前先根据groupId,业态批量删除 传mallId，其它site不传
        if (StringUtils.isNotBlank(resource.getCategoryId())) {
            tbIntegralCategoryRateMapper.deleteByParams(resource.getGroupId(), resource.getMallId(), resource.getCategoryId());
        }
        List<TbIntegralCategoryRate> rateList = this.covertCategoryRate(resource);
        tbIntegralCategoryRateMapper.saveCategoryRateList(rateList);
    }

    private List<TbIntegralCategoryRate> covertCategoryRate(TbIntegralCategoryRateResource resource) {
        List<TbIntegralCategoryRate> rateList = new ArrayList<>();
        List<GradeIntegralResponse> gradeList = resource.getList();
        if (CollectionUtils.isEmpty(gradeList)) {
            return Collections.emptyList();
        }
        // 根据categoryId反查categoryName
        for (GradeIntegralResponse res: gradeList) {
            TbIntegralCategoryRate rate = TbIntegralCategoryRate.builder()
                    .groupId(resource.getGroupId())
                    .mallId(Objects.isNull(resource.getMallId()) ? "" : resource.getMallId())
                    .categoryId(resource.getCategoryId())
                    .categoryName(resource.getCategoryName())
                    .creator(resource.getCreator())
                    .updater(resource.getCreator())
                    .gradeId(res.getGradeId())
                    .gradeName(res.getGradeName())
                    .money(res.getMoney())
                    .pointNum(res.getPointNum())
                    .status(1)
                    .isConsistent(resource.getIsConsistent())
                    .build();
            rateList.add(rate);
        }
        return rateList;
    }

    @Override
    public List<TbIntegralCategoryRate> getCategoryRateList(TbIntegralCategoryRate rate) {
        return tbIntegralCategoryRateMapper.getCategoryRateList(rate);
    }

    @Override
    public void deleteCategoryRateList(Long[] ids) {
        tbIntegralCategoryRateMapper.deleteBatchIds(Arrays.asList(ids));
    }
}
