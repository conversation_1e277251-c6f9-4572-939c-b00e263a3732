package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 14:28
 **********************************************************************************************************************/

@Slf4j
@Component
@AllArgsConstructor
public class RedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    /**  不设置过期时长 */
    public final static long NOT_EXPIRE = -1L;

    public long setRetryCount(String orderNo) {
        String orderKey = String.format(RedisCacheKey.WX_SYNC_COUNT_KEY, orderNo);
        return Optional.ofNullable(redisTemplate.opsForValue().increment(orderKey, 1)).orElse(0L);
    }

    /**
     * 自增+1
     * @param key
     * @return
     */
    public Long incrementOne(String key) {
        return redisTemplate.opsForValue().increment(key);
    }

    /**
     * 自增指定长度
     * @param key
     * @param step
     * @return
     */
    public Long incrementLimitedSteps(String key, Long step) {
        return redisTemplate.opsForValue().increment(key, step);
    }

    public void delRetryCount(String orderNo) {
        String orderKey = String.format(RedisCacheKey.WX_SYNC_COUNT_KEY, orderNo);
        redisTemplate.delete(orderKey);
    }

    public void hSet(String key, String field, Object value, long expire) {
        redisTemplate.opsForHash().put(key, field, value);
        if(expire != NOT_EXPIRE){
            expire(key, expire);
        }
    }

    public Object hGet(String key, String field) {
        return redisTemplate.opsForHash().get(key, field);
    }

    public void expire(String key, long expire){
        redisTemplate.expire(key, expire, TimeUnit.SECONDS);
    }

    public String getValue(String key) {
        Object val = redisTemplate.opsForValue().get(key);
        return Objects.isNull(val) ? null : val.toString();
    }

    public Set<String> getKeys(String namePatter) {
        Set<String> keys = redisTemplate.keys(namePatter);
        return keys;
    }

    /**
     * 设置微信无感积分补录标识
     * @param key
     * @param value
     * @param expire
     */
    public void setValue(String key, Object value, Long expire) {
        redisTemplate.opsForValue().set(key, value, expire, TimeUnit.HOURS);
    }

    @Async
    public void setVal(String key, Object value, Long expire, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, expire, unit);
    }

    /**
     * 获取key的过期时间
     * @param key
     * @return
     */
    public Long getKeyExpireTime(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 删除redis key缓存
     * @param keys
     */
    @Async
    public void delKeys(List<String> keys) {
        redisTemplate.delete(keys);
    }

    /**
     * 删除redis key缓存
     * @param key
     */
    @Async
    public void flush(String key) {
        Set<String> keys = redisTemplate.keys(key + "*");
        log.info("del key: {}", keys);
        redisTemplate.delete(keys);
    }

    /**
     * 类似锁
     * @param saleNoKey
     * @param value
     * @return
     */
    public Boolean setSaleNoIfAbsent(String saleNoKey, Object value) {
        Boolean absent = redisTemplate.opsForValue().setIfAbsent(saleNoKey, value, 5L, TimeUnit.SECONDS);
        return Objects.nonNull(absent) && Boolean.TRUE.equals(absent);
    }

    /**
     * 争抢锁
     * @param key
     * @param value
     * @return
     */
    public Boolean setIfAbsentWithExpire(String key, Object value, long timeout, TimeUnit unit) {
        return redisTemplate.opsForValue().setIfAbsent(key, value, timeout, unit);
    }

    /**
     * 判断redis key是否存在
     * @param key
     * @return
     */
    public boolean hasKey(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    /**
     * 添加收藏
     * @param key
     * @param brandId
     */
    public void addFavoriteBrand(String key, String brandId) {
        redisTemplate.opsForSet().add(key, brandId);
    }

    /**
     * 移除收藏
     * @param key
     * @param brandId
     */
    public void removeFavoriteBrand(String key, String brandId) {
        redisTemplate.opsForSet().remove(key, brandId);
    }

    /**
     * 获取用户品牌导览收藏列表
     * @param key
     * @return
     */
    public List<String> getFavoriteBrands(String key) {
        Set<Object> brandIds = redisTemplate.opsForSet().members(key);
        return CollectionUtils.isEmpty(brandIds) ? Collections.emptyList() : brandIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
    }

    /**
     * 判断该品牌导览是否已收藏
     * @param key
     * @param brandId
     * @return
     */
    public boolean inSet(String key, String brandId) {
        Boolean setMember = redisTemplate.opsForSet().isMember(key, brandId);
        return Objects.nonNull(setMember) && Boolean.TRUE.equals(setMember);
    }

}
