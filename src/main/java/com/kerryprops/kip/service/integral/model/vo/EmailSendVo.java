package com.kerryprops.kip.service.integral.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - saas
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/15/2022 09:16
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailSendVo implements Serializable {

    private List<AttachmentItem> attachments;

    private String businessType;

    private String callbackUrl;

    private boolean html;

    private List<String> sendTos;

    @Builder.Default
    private String source = "CRM";

    private String subject;

    private String text;

}
