package com.kerryprops.kip.service.integral.model.dto.alipay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝通知相关dto
 * @createDate 2022/11/7
 * @updateDate 2022/11/7
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class AliPayNotifyDto {

    /**
     * 商场id
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private MallItem mallItem;

    @JsonProperty("out_string")
    private String mallId;

    private String groupId;
}
