package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("tb_sequence_vipcode")
public class TbSequenceVipcode implements Serializable {

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	@TableField("last_update_time")
	private Date lastUpdateTime;

}