package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.pmw.client.resource.PointsActivationConfirmResource;
import com.kerryprops.kip.pmw.client.resource.PointsPaymentConfirmResource;
import com.kerryprops.kip.pmw.client.resource.PointsRefundConfirmResource;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.webservice.resource.QueryPointsCommitStatusResource;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/16/2022 13:32
 **********************************************************************************************************************/
public interface WechatAccelerateIntegralService {

    void payCallback(PointsPaymentConfirmResource dto);

    int refundCallback(PointsRefundConfirmResource resource);

    void memberPointsAuth(PointsActivationConfirmResource resource);

    void memberCardOpenEvent(PointsActivationConfirmResource resource);

    void syncAliPay();

    void syncWechatPoints(SalesAutoPointsDto dto);

    QueryPointsCommitStatusResource queryMemberPointsCommitStatus(LoginUser loginUser);

}