package com.kerryprops.kip.service.integral.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/06/2022 12:06
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantAllVo implements Serializable {

    private TenantInfoVo tenant;

    private List<TenantBuildingVo> buildings;

    private List<TenantFloorVo> floors;

}

