package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.BatchCalculatePointsResource;
import com.kerryprops.kip.service.integral.webservice.resource.MemberPointsChangeResource;
import com.kerryprops.kip.service.integral.webservice.response.BatchCalculatePointsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/26/2023 16:38
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/sale/transfer")
@RestController
@RequiredArgsConstructor
@Tag(name = "销售转赠")
public class TbSaleTransferController extends BaseMemberStatus {

    private final TbMemberAssetService tbMemberAssetService;
    private final TbSalesDetailService salesDetailService;
    private final TbSetshoprateService tbSetshoprateService;
    private final SysDictService sysDictService;
    private final TbActivityPromotionService tbActivityPromotionService;
    private final TbBaseShopService tbBaseShopService;
    private final TbMemberPointsChangeService tbMemberPointsChangeService;

    @PostMapping("/batchCalculatePoints")
    @Operation(summary="计算每笔订单的最新对应积分", method = "POST")
    public List<BatchCalculatePointsResponse> batchCalculatePoints(@RequestBody @Validated BatchCalculatePointsResource resource) {
        log.info("batchCalculatePoints {}", JsonUtils.objToString(resource));
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findAllByDto(SingleMemberQueryDto.builder().groupId(resource.getGroupId()).vipcode(resource.getVipcode()).mobile(resource.getMobile()).build());
        this.checkMemberStatus(tbMemberAsset);
        List<TbSalesDetail>  salesDetails = salesDetailService.listByIds(resource.getSaleIds());
        if (CollectionUtils.isEmpty(salesDetails)) {
            return Collections.emptyList();
        }
        List<BatchCalculatePointsResponse> list = new ArrayList<>();
        for (TbSalesDetail detail : salesDetails) {
            SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder()
                    .groupId(tbMemberAsset.getGroupId())
                    .shopId(detail.getShopNo())
                    .mallId(resource.getMallId())
                    .saleDate(DateUtil.formatDateTime(detail.getSaleDate()))
                    .remark(SaleTypeEnum.getDictTypeByValue(detail.getSaleType()))
                    .vipcode(tbMemberAsset.getVipcode())
                    .discountAmount(detail.getDiscountAmount().toString())
                    .amount(detail.getPayAmount().toString())
                    .member(tbMemberAsset)
                    .integralAdjustId(IdUtil.simpleUUID())
                    .saleNo(detail.getOrderNo())
                    .build();
            if (StringUtils.isNotBlank(pointsDto.getRemark())) {
                Optional.ofNullable(sysDictService.findByDictType(pointsDto.getRemark())).ifPresent(dict -> pointsDto.setRemarkName(dict.getDictName()));
            }
            TbBaseShop tbBaseShop = tbBaseShopService.getByContractNoAndMallId(pointsDto.getShopId(), pointsDto.getMallId());
            if (Objects.isNull(tbBaseShop)) {
                continue;
            }
            pointsDto.setBaseShop(tbBaseShop);
            int points = tbSetshoprateService.salesToIntegral(pointsDto);
            if (StringUtils.isNotBlank(pointsDto.getSalesRemark())) {
                pointsDto.setSalesRemark(pointsDto.getSalesRemark() + points + "分");
            } else {
                pointsDto.setSalesRemark(pointsDto.getRemarkName() + ", " + points + "分");
            }
            if (points > 0) {
                log.info("handlePromotionActivity {}, {}", resource.getVipcode(), detail.getOrderNo());
                pointsDto.setExchangePoints(points);
                points = tbActivityPromotionService.handlePromotionActivity(pointsDto, Boolean.TRUE);
            }
            BatchCalculatePointsResponse response = BatchCalculatePointsResponse.builder()
                    .orderNo(String.valueOf(detail.getId()))
                    .pointsNum(String.valueOf(points))
                    .salesRemark(pointsDto.getSalesRemark())
                    .pointsId(pointsDto.getIntegralAdjustId())
                    .build();
            list.add(response);
        }
        return list;
    }
    @PostMapping("/batchAdjustPoints")
    @Operation(summary="批量调整积分", method = "POST")
    public void batchAdjustPoints(@RequestBody List<MemberPointsChangeResource> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        tbMemberPointsChangeService.batchAdjustPoints(list);
    }

}
