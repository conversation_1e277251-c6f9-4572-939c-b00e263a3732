package com.kerryprops.kip.service.integral.service.impl;

import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.service.CrmVipcodeService;
import com.kerryprops.kip.service.integral.service.MemberRegisterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/06/2022 16:37
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class CrmVipcodeServiceImpl implements CrmVipcodeService {

    private final MemberRegisterService memberRegisterService;

    @Override
    @Cacheable(value = RedisCacheKey.INDEX_CRM_VIPCODE_CACHE_KEY, key = "#groupId +':' + #kipUserId", unless = "#result == null")
    public String getVipcode(String groupId, String kipUserId) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(groupId, kipUserId);
        if (Objects.isNull(tbMemberAsset)) {
            throw BizNotFoundException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        return tbMemberAsset.getVipcode();
    }
}
