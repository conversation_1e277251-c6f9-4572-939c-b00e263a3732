package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/06/2022 16:28
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberIntegralAdjustRecordPageQueryDto implements Serializable {

    private String groupId;

    private String mallId;

    private String vipcode;

    private Integer type;

    private int page;

    private int size;

    private int offset;

    /**
     * Hotfix 3.20.2，C端积分明细只展示最近一年的记录
     */
    private String startTime;

    /**
     * 排除C端不展示补差额的积分记录
     */
    private List<String> excludeList;

}
