package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.model.dto.MemberIntegralAdjustRecordPageQueryDto;
import com.kerryprops.kip.service.integral.model.dto.MemberPointSumQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 10/23/2023 17:32
 **********************************************************************************************************************/
public interface TbPointsDetailMapper extends BaseMapper<TbPointsDetail> {

    /**
     * 获取时间内获得的积分数
     * @param vipcode
     * @param groupId
     * @param remarks
     * @param date
     * @return
     */
    int getTotalPointsOfDay(@Param("groupId") String groupId, @Param("vipcode") String vipcode,
                               @Param("remarks") List<String> remarks, @Param("date") String date);

    List<TbPointsDetail> getList(MemberIntegralAdjustRecordPageQueryDto dto);

    List<TbPointsDetail> getPointsList(@Param("groupId") String groupId, @Param("vipcode") String vipcode);

    int getCount(MemberIntegralAdjustRecordPageQueryDto dto);

    List<TbPointsDetail> getMonthlyIntegral(@Param("groupId") String groupId, @Param("mallId") String mallId,
                                                    @Param("vipcode") String vipcode, @Param("list") List<String> moons);

    int getMemberConsumePointsBetweenDate(MemberPointSumQueryDto dto);

    List<TbPointsDetail> findByCrmIds(@Param("list") List<String> list);

    int integralRecordTotal(@Param("vipcode") String vipcode, @Param("mallId") String mallId);

    List<TbPointsDetail> integralRecordList(@Param("vipcode") String vipcode, @Param("mallId") String mallId, @Param("offset") int offset, @Param("size") int size);

    TbPointsDetail queryAdjustList(@Param("groupId") String groupId, @Param("mallId") String mallId, @Param("orderNo") String orderNo, @Param("shopNo") String shopNo, @Param("remark") String remark);

    /**
     * 查询优惠买单积分变更记录
     * @param groupId
     * @param mallId
     * @param vipcode
     * @param orderNo
     * @param shopNo
     * @param reasonType
     * @return
     */
    TbPointsDetail queryKerryPayPointsRedemptionAmountRecord(@Param("groupId") String groupId,
                                                             @Param("mallId") String mallId,
                                                             @Param("vipcode") String vipcode,
                                                             @Param("orderNo") String orderNo,
                                                             @Param("shopNo") String shopNo,
                                                             @Param("reasonType") String reasonType);

    /**
     * 查询优惠买单积分变更记录
     * @param groupId
     * @param mallId
     * @param vipcode
     * @param orderNo
     * @param shopNo
     * @param reasonTypes
     * @return
     */
    List<TbPointsDetail> queryPointsRecordByConditions(@Param("groupId") String groupId,
                                                             @Param("mallId") String mallId,
                                                             @Param("vipcode") String vipcode,
                                                             @Param("orderNo") String orderNo,
                                                             @Param("shopNo") String shopNo,
                                                             @Param("reasonTypes") List<String> reasonTypes);

    /**
     * 重复操作积分校验
     * @param groupId
     * @param mallId
     * @param vipcode
     * @param orderNo
     * @param changePointsNum
     * @param reasonType
     * @param startTime
     * @param endTime
     * @return
     */
    TbPointsDetail checkPointsAdjustExistRecord(@Param("groupId") String groupId,
                                                @Param("mallId") String mallId,
                                                @Param("vipcode") String vipcode,
                                                @Param("orderNo") String orderNo,
                                                @Param("changePointsNum") Integer changePointsNum,
                                                @Param("reasonType") String reasonType,
                                                @Param("startTime") String startTime,
                                                @Param("endTime") String endTime);

}
