package com.kerryprops.kip.service.integral.event;

import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import lombok.Builder;
import lombok.Data;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 09:19
 **********************************************************************************************************************/

@Data
@Builder
public class UserPointAuthorizationEvent extends PointEvent {

    private String kipUserId;

    private String openId;

    private String groupId;

    private String mallId;

    private String mchid;

    private String cardNo;

    @Builder.Default
    private int origin = InsensateOriginEnum.WECHAT.getValue();

    public UserPointAuthorizationEvent(String kipUserId, String openId, String groupId, String mallId, String mchid, String cardNo) {
        super(kipUserId);
        this.kipUserId = kipUserId;
        this.openId = openId;
        this.groupId = groupId;
        this.mallId = mallId;
        this.origin = InsensateOriginEnum.WECHAT.getValue();
        this.mchid = mchid;
        this.cardNo = cardNo;
    }

    public UserPointAuthorizationEvent(String kipUserId, String openId, String groupId, String mallId, String mchid, String cardNo, int origin) {
        super(kipUserId);
        this.kipUserId = kipUserId;
        this.openId = openId;
        this.groupId = groupId;
        this.mallId = mallId;
        this.mchid = mchid;
        this.origin = origin;
        this.cardNo = cardNo;
    }

}
