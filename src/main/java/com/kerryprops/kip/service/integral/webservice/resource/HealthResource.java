package com.kerryprops.kip.service.integral.webservice.resource;

import lombok.Data;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - gateway-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/29/2021 09:04
 **********************************************************************************************************************/

@Data
public class HealthResource {

    private int health = Integer.MIN_VALUE;
    public static final int SUCCESS = 0;

    public static boolean isSuccess(HealthResource healthResponse) {
        return Objects.nonNull(healthResponse) && SUCCESS == healthResponse.health;
    }

    public static final HealthResource SUCCESS_RESPONSE = new HealthResource() {
        @Override
        public int getHealth() {
            return SUCCESS;
        }
    };

    public static final HealthResource FAIL_RESPONSE = new HealthResource();

}
