package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.BatchCalculatePointsResource;
import com.kerryprops.kip.service.integral.webservice.resource.IpadIntegralResource;
import com.kerryprops.kip.service.integral.webservice.resource.MemberPointsChangeResource;
import com.kerryprops.kip.service.integral.webservice.response.BatchCalculatePointsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/26/2023 16:38
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/ipad/integral")
@RestController
@RequiredArgsConstructor
@Tag(name = "ipad端会员积分信息")
public class IpadIntegralController extends BaseMemberStatus {

    private final TbSetshoprateService tbSetshoprateService;
    private final TbMemberAssetService tbMemberAssetService;
    private final SysDictService sysDictService;
    private final TbActivityPromotionService tbActivityPromotionService;
    private final TbBaseShopService tbBaseShopService;
    private static final List<String> CRM_REFUND_REMARKS = Arrays.asList(IntegralConstant.ORDER_REFUND, IntegralConstant.NO_ORDER_REFUND);


    @PostMapping("/calculateIntegral")
    @Operation(summary="ipad端销售录入-计算积分数", method = "POST")
    public int integralRecordList(@RequestBody @Validated IpadIntegralResource resource) {
        log.info("calculateIntegral {}", JsonUtils.objToString(resource));
        // 退货情况直接返回积分0
        if (CRM_REFUND_REMARKS.contains(resource.getRemark())) {
            return 0;
        }
        TbMemberAsset tbMemberAsset;
        if (StringUtils.isNotBlank(resource.getId())) {
            tbMemberAsset = tbMemberAssetService.getMemberAllInfoById(resource.getId());
        } else {
            tbMemberAsset = tbMemberAssetService.findAllByDto(SingleMemberQueryDto.builder().groupId(resource.getGroupId()).vipcode(resource.getVipcode()).build());
        }
        this.checkMemberStatus(tbMemberAsset);
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder()
                .groupId(tbMemberAsset.getGroupId())
                .shopId(resource.getShopNo())
                .mallId(resource.getMallId())
                .saleDate(resource.getSellDate())
                .remark(resource.getRemark())
                .vipcode(tbMemberAsset.getVipcode())
                .discountAmount(resource.getDiscounts().toString())
                .amount(resource.getUseMoney().toString())
                .member(tbMemberAsset)
                .build();
        if (StringUtils.isNotBlank(pointsDto.getRemark())) {
            Optional.ofNullable(sysDictService.findByDictType(pointsDto.getRemark())).ifPresent(dict -> pointsDto.setRemarkName(dict.getDictName()));
        }
        TbBaseShop tbBaseShop = tbBaseShopService.getByContractNoAndMallId(pointsDto.getShopId(), pointsDto.getMallId());
        if (Objects.isNull(tbBaseShop)) {
            throw BizNotFoundException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        pointsDto.setBaseShop(tbBaseShop);
        int integral = tbSetshoprateService.salesToIntegral(pointsDto);
        if (integral > 0) {
            pointsDto.setExchangePoints(integral);
            integral = tbActivityPromotionService.handlePromotionActivity(pointsDto, Boolean.FALSE);
        }
        return integral;
    }

}
