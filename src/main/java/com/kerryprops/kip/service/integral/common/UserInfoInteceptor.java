package com.kerryprops.kip.service.integral.common;

import com.kerryprops.kip.service.integral.common.utils.UserInfoUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/***********************************************************************************************************************
 * Project - decoration-review-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - Bert
 * Created Date - 06/28/2021 16:04
 **********************************************************************************************************************/

@Slf4j
@Component
public class UserInfoInteceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String userInfo = this.obtainHeader(request, IntegralConstant.X_USER);
        String brandId = this.obtainHeader(request, IntegralConstant.BRAND_ID);
        String lbsId = this.obtainHeader(request, IntegralConstant.LBS_ID);
        String appId = this.obtainHeader(request, IntegralConstant.APP_ID);
        if (StringUtils.isNotBlank(userInfo)) {
            UserInfoUtils.setUser(userInfo, brandId, lbsId, appId);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserInfoUtils.removeUser();
    }

    private String obtainHeader(HttpServletRequest request, String parameter) {
        String result =  request.getHeader(parameter);
        return StringUtils.isBlank(result) ? null : result;
    }

}
