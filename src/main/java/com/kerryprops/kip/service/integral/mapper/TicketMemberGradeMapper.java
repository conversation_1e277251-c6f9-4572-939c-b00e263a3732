package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TicketMemberGrade;
import com.kerryprops.kip.service.integral.model.dto.TicketMemberGradeDto;

import java.util.List;

/**
 * @description 用户信息Mapper
 * <AUTHOR>
 * @date 2022-12-27
 */
public interface TicketMemberGradeMapper extends BaseMapper<TicketMemberGrade> {

    List<TicketMemberGradeDto> findList(List<String> ticketIds, String grade);

}