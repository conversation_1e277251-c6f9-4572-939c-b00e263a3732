package com.kerryprops.kip.service.integral.service.promotion.impl;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:29
 **********************************************************************************************************************/

@Slf4j
@Component
public class EachMonthPromotionRule implements PromotionRule {

    /**
     * 月中的某一天
     */
    private static final String DAY_OF_MONTH = "dd";

    @Override
    public String getRuleType() {
        return "5";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        if (StringUtils.isBlank(condition.getPromotionConditionContent())) {
            return false;
        }
        String month = DateUtil.format(saleDate, DAY_OF_MONTH);
        return condition.getPromotionConditionContent().contains(month);
    }
}
