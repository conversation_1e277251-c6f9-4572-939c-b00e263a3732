package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbPointsRepeatRule;
import com.kerryprops.kip.service.integral.model.dto.MemberSaleRepeatQueryDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/19/2022 20:33
 **********************************************************************************************************************/
public interface TbPointsRepeatRuleService extends IService<TbPointsRepeatRule> {

    TbPointsRepeatRule getRuleByMallIdAndGroupId(String mallId, String groupId);

    /**
     * 获取积分去重条件
     * @param dto
     * @return
     */
    MemberSaleRepeatQueryDto getSaleRepeatCondition(SalesAutoPointsDto dto, boolean isAutoPoints);

}
