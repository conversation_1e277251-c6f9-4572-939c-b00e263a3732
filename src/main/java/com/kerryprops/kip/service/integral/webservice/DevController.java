package com.kerryprops.kip.service.integral.webservice;

import com.alibaba.excel.EasyExcelFactory;
import com.kerryprops.kip.service.integral.client.KerryStaffClient;
import com.kerryprops.kip.service.integral.client.SmartPosRpcClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.listener.TbMemberStatusUpdateListener;
import com.kerryprops.kip.service.integral.model.dto.CustomerThirdPartyDto;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.TbMemberStatusUpdateDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.ExecuteSqlResource;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/dev")
@RestController
@RequiredArgsConstructor
@Hidden
public class DevController {

    private final TbInsensatePointsPushRecordService insensatePointsPushRecordService;
    private final RedisService redisService;
    private final SmsSendService smsSendService;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbSalesDetailService tbSalesDetailService;
    private final TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    private final KerryStaffClient kerryStaffClient;
    private final WechatPaymentAutoPointsCheckService wechatPaymentAutoPointsCheckService;
    private final MallConfig mallConfig;
    private final SmartPosRpcClient smartPosRpcClient;

    @GetMapping("/get_push_record_by_mobile_appid")
    @Operation(summary="通过手机号与appid获取用户", method = "GET")
    public List<InsensatePointsPushRecordDto> getPushRecordByMobileAppid(@RequestParam String mobile, @RequestParam String appId) {
        CustomerThirdPartyDto userDto = kerryStaffClient.getWxInfoByMobileAndAppId(mobile, appId);
        if (Objects.isNull(userDto)) {
            return Collections.emptyList();
        }
        return insensatePointsPushRecordService.selectByOpenId(userDto.getOpenId());
    }

    @GetMapping("/wechat-points-authorize-task")
    public String updateMallPointsAuthorizeStatus(@RequestParam("mallId") String mallId) {
        MallItem mallItem = mallConfig.getByMallId(mallId);
        if (Objects.isNull(mallItem)) {
            return "商场不存在";
        }
        wechatPaymentAutoPointsCheckService.checkMallWechatAutoPointsStatus(mallItem);
        return "正在更新商场微信无感积分授权状态";
    }

    @GetMapping("/mock/additional/points")
    public void setMockAdditionalKey(@RequestParam("openId") String openId) {
        String mockKey = String.format(RedisCacheKey.MOCK_ADDITIONAL_POINTS, openId);
        redisService.setValue(mockKey, IntegralConstant.INTEGRAL_REPEAT_1, 1L);
    }

    @GetMapping("/init_member_grade")
    public void handleMemberGradeData(@RequestParam(value = "vipcode", required = false) String vipcode) {
        tbSalesDetailService.initMemberGradeData(vipcode);
    }

    @GetMapping("/modify_sales_shopId")
    public void modifyMemberSaleShopId(@RequestParam("shopIds") String shopIds, @RequestParam("mallId") String mallId, @RequestParam("newIds") String newIds) {
        tbSalesDetailService.modifyMemberSaleShopId(shopIds, mallId, newIds);
    }

    @GetMapping("/fill_member_grade_mallId")
    public void fillMemberGradeChangeDetailMallId(@RequestParam("groupId") String groupId,
                                                  @RequestParam("vipcode") String vipcode,
                                                  @RequestParam("mallId") String mallId) {
        tbMemberGradeChangeDetailService.fillMemberGradeChangeDetailMallId(groupId, vipcode, mallId);
    }

    @PostMapping("/update/tb_member_status")
    @ResponseBody
    public String syncCrmUser(MultipartFile file, @RequestParam("groupId") String groupId) throws IOException {
        EasyExcelFactory.read(file.getInputStream(), TbMemberStatusUpdateDto.class,
                new TbMemberStatusUpdateListener(tbMemberAssetService, groupId)).sheet().doRead();
        return "上传成功";
    }

    @GetMapping("/change_member_grade_detail_date")
    public String changeDate(@RequestParam("groupId") String groupId, @RequestParam("date") String date) {
        tbMemberGradeChangeDetailService.reduceMemberGradeChangeDate(groupId, date);
        return "请求成功";
    }

    @GetMapping("/check_sales_upgrade_grade")
    public String checkSalesUpgradeGrade(@RequestParam("groupId") String groupId, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        tbSalesDetailService.checkSalesUpgradeGrade(groupId, startDate, endDate);
        return "正在处理中";
    }

    @Hidden
    @GetMapping("/remove_redis_key")
    public String removeCacheKey(@RequestParam("redisKey") String redisKey) {
        redisService.flush(redisKey);
        return "缓存删除成功";
    }

    @Hidden
    @GetMapping("/get_redis_key")
    public String getCacheKey(@RequestParam("redisKey") String redisKey) {
        Object o = redisService.getValue(redisKey);
        return Objects.isNull(o) ? "" : o.toString();
    }

    @Hidden
    @GetMapping("/set_ops_for_value_key_and_value")
    public String setOpsForValueKeyAndValue(@RequestParam("redisKey") String redisKey, @RequestParam("redisValue") String redisValue) {
        redisService.setVal(redisKey, redisValue, 365L, TimeUnit.DAYS);
        return "设置Redis key & value成功";
    }

    @Hidden
    @GetMapping("/getKeyName")
    public Set<String> getKeys(String namePatter) {
        return redisService.getKeys(namePatter);
    }

    @Hidden
    @GetMapping("/test_send_email")
    public void testSendEmail(@RequestParam("mallId") String mallId,
                              @RequestParam("subscriptionType") Integer subscriptionType) {
        smsSendService.sendPointsEmail(mallId, subscriptionType);
    }

    /**
     * ####################### 谨慎使用 ###########################
     * 用于修复数据的原生sql执行
     * @param resource
     */
    @Hidden
    @PostMapping("/execute_sql_script")
    public void runSql(@RequestBody ExecuteSqlResource resource) {
        tbSalesDetailService.executeSql(resource.getSql());
    }

    /**
     * ####################### 谨慎使用，执行sql前确认返回的数据不会因为数据量太大造成服务OOM ###########################
     * 用于原生sql查询结果的返回
     * @param resource
     * @return
     */
    @Hidden
    @PostMapping("/query_sql_result")
    public List<Map<String, Object>> querySqlResult(@RequestBody ExecuteSqlResource resource) {
        return tbSalesDetailService.querySqlResult(resource.getSql());
    }

    @Hidden
    @GetMapping("/grade_change_trigger_member_upgrade")
    public void gradeChangeTriggerMemberUpgrade(@RequestParam("groupId") String groupId, @RequestParam("date") String date) {
        tbSalesDetailService.gradeChangeTriggerMemberUpgrade(groupId, String.format("%s 00:00:00", date));
    }

}
