package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.CheckJsCodeDto;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "auth-service", url = "${kerry.services.auth-service:default}")
public interface AuthServiceClient {

    @PostMapping("/check/wechat_mini/js_code")
    KerryResultDto<String> checkMiniProgramJsCode(@RequestBody CheckJsCodeDto resource);

}
