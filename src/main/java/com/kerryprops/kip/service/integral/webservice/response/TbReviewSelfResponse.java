package com.kerryprops.kip.service.integral.webservice.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/27/2023 11:20
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "拍照积分明细类")
public class TbReviewSelfResponse implements Serializable {

    @ExcelIgnore
    private Integer sort;

    @ExcelIgnore
    @Schema( name = "记录ID")
    private String id;

    @ExcelIgnore
    @Schema( name = "商场ID")
    private String mallId;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "商场名称", index = 0)
    @Schema( name = "商场")
    private String mallName;

    @ExcelIgnore
    @Schema( name = "手机号")
    private String mobile;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "会员号", index = 1)
    @Schema( name = "vipcode")
    private String vipcode;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "会员昵称", index = 2)
    @Schema( name = "昵称")
    private String nickName;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "上传时间", index = 3)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema( name = "上传日期")
    private String uploadDate;

    @ExcelIgnore
    @Schema( name = "审核状态:PhotoReviewStatusEnum")
    private String status;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "审核状态", index = 4)
    private String statusStr;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "店铺别名", index = 5)
    @Schema( name = "店铺别名")
    private String shopAliasName;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "店铺名称", index = 6)
    @Schema( name = "店铺名称")
    private String shopName;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "店铺编号", index = 7)
    @Schema( name = "店铺编号")
    private String shopNo;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "消费时间", index = 8)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema( name = "消费时间")
    private String tradingDate;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "消费金额", index = 9)
    @Schema( name = "金额")
    private Double money;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "小票号", index = 10)
    @Schema( name = "小票号")
    private String serialNum;

    @ExcelIgnore
    @Schema( name = "获得积分")
    private Integer points;

    @ExcelIgnore
    @Schema( name = "调整前等级")
    private String orgGrade;

    @ExcelIgnore
    @Schema( name = "调整前等级名称")
    private String orgGradeName;

    @ExcelIgnore
    @Schema( name = "调整前积分")
    private String orgPoints;

    @ExcelIgnore
    @Schema( name = "上传图片")
    private String imageUrl;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "获得积分", index = 11)
    @Schema( name = "积分")
    private String bonus;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "驳回原因", index = 12)
    @Schema( name = "审核未通过原因")
    private String reason;

    @ExcelIgnore
    @Schema( name = "下一张图片")
    private String nextImage;

    @ExcelIgnore
    @Schema( name = "积分方式（1.小票；2.二维码; 3.CRM后台录入）")
    private String type;

    @ExcelIgnore
    private String qrcode;

    @ExcelIgnore
    private String description;

    @ExcelIgnore
    @Schema( name = "时间戳")
    private String timestamp;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "审核日期", index = 13)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema( name = "审核日期")
    private String checkTime;

    @ColumnWidth(value=15)
    @ExcelProperty(value = "审核人", index = 14)
    @Schema( name = "审核人")
    private String checkName;

    @ExcelIgnore
    @Schema( name = "集团id")
    private String groupId;

    @ExcelIgnore
    @Schema( name = "创建日期")
    private Date createDate;

    @ExcelIgnore
    @Schema( name = "业态id")
    private String formatId;

    @ExcelIgnore
    @Schema( name = "业态名称")
    private String formatName;

    @ExcelIgnore
    @Schema( name = "OCR任务ID")
    private String ocrTaskId;

}
