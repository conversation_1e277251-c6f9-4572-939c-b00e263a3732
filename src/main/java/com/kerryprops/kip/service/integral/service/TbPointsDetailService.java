package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.model.dto.MemberIntegralAdjustRecordPageQueryDto;
import com.kerryprops.kip.service.integral.model.dto.MemberPointSumQueryDto;
import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.webservice.resource.MemberMonthlyIntegralResponse;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 10/23/2023 17:30
 **********************************************************************************************************************/
public interface TbPointsDetailService extends IService<TbPointsDetail> {

    /**
     * 获取时间内获得的积分数
     * @param groupId
     * @param vipcode
     * @param remarks
     * @param date
     * @return
     */
    int getTotalPointsOfDay(String groupId, String vipcode, List<String> remarks, String date);

    String savePointsChangeRecord(SalesAutoPointsDto dto);

    /**
     * 统一调整积分方法
     * @param dto
     * @param tbMemberAsset
     * @return
     */
    String savePointsAdjustRecord(MemberPointsChangeDto dto, TbMemberAsset tbMemberAsset);

    void savePointsAdjustRecordNoMessage(MemberPointsChangeDto dto, TbMemberAsset tbMemberAsset);

    List<TbPointsDetail> recordList(MemberIntegralAdjustRecordPageQueryDto pageQuery);

    List<TbPointsDetail> getPointsList(String groupId, String vipcode);

    int getCount(MemberIntegralAdjustRecordPageQueryDto pageQuery);

    CompletableFuture<List<MemberMonthlyIntegralResponse>> getMonthlyIntegral(String groupId, String mallId, String vipcode, List<String> moons);

    /**
     * 批量赠送积分过期积分数
     * @param dto
     * @return
     */
    int getMemberConsumePointsBetweenDate(MemberPointSumQueryDto dto);

    CompletableFuture<Map<String, TbPointsDetail>> findByIds(List<String> adjustIds);

    int integralRecordTotal(String vipcode, String mallId);

    List<TbPointsDetail> integralRecordList(String vipcode, String mallId, int page, int size);

    TbPointsDetail queryAdjustList(String groupId, String mallId, String orderNo, String shopNo, String posCashOut);

    /**
     * 查询优惠买单-积分抵现记录
     * @param groupId
     * @param mallId
     * @param vipcode
     * @param orderNo
     * @param shopNo
     * @param reasonType
     * @return
     */
    TbPointsDetail queryKerryPayPointsRedemptionAmountRecord(String groupId, String mallId, String vipcode, String orderNo, String shopNo, String reasonType);

    /**
     * 同时查询多个调整原因的积分调整记录
     * @param groupId
     * @param mallId
     * @param vipcode
     * @param orderNo
     * @param shopNo
     * @param reasonTypes
     * @return
     */
    List<TbPointsDetail> queryPointsRecordByConditions(String groupId, String mallId, String vipcode, String orderNo, String shopNo, List<String> reasonTypes);


    /**
     * 检查积分调整记录是否存在
     * @param groupId
     * @param mallId
     * @param vipcode
     * @param orderNo
     * @param changePointsNum
     * @param reasonType
     * @param startTime
     * @param endTime
     * @return
     */
    TbPointsDetail checkPointsAdjustExistRecord(String groupId, String mallId, String vipcode, String orderNo,
                                                Integer changePointsNum, String reasonType,
                                                String startTime, String endTime);
}
