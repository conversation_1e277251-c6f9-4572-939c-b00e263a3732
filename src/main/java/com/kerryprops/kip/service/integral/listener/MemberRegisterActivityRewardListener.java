package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.MemberRegisterRewardDto;
import com.kerryprops.kip.service.integral.model.dto.MemberSceneRegisterDto;
import com.kerryprops.kip.service.integral.service.HiveVasService;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberIdentityResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC 1、会员注册场景触发奖励发放MQ
 *       2、发送会员身份认证升级检查MQ
 * <AUTHOR> Bert
 * Created Date - 03/27/2023 10:39
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class MemberRegisterActivityRewardListener {

    private final RabbitMqService rabbitMqService;
    private final TbMemberAssetService tbMemberAssetService;
    private final HiveVasService hiveVasService;

    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.XCRM_MEMBER_REGISTER_ACTIVITY_REWARD}, containerFactory = "crmContainerFactory")
    public void registerRewardProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("KIP会员注册奖励发放信息为空.");
            return;
        }
        log.info("MemberRegisterActivityRewardListener: {}" , msg);
        MemberRegisterRewardDto dto = JsonUtils.stringToObj(msg, MemberRegisterRewardDto.class);
        if (Objects.isNull(dto)) {
            log.error("MQ信息转对象失败-{}", msg);
            return;
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.getMemberById(dto.getMemberId());
        if (Objects.isNull(tbMemberAsset)) {
            log.error("会员ID对应的会员信息不存在-{}", dto.getMemberId());
            return;
        }
        // 会员注册奖励发放MQ
        MemberSceneRegisterDto registerDto = MemberSceneRegisterDto.builder().groupId(tbMemberAsset.getGroupId()).mallId(dto.getMallId())
                .vipCode(tbMemberAsset.getVipcode()).mobile(tbMemberAsset.getMobile())
                .isInvitation(Objects.isNull(dto.getInvitation()) ? Boolean.FALSE : dto.getInvitation()).build();
        log.info("会员注册奖励事件发送------>会员ID[{}]", dto.getMemberId());
        rabbitMqService.sendTopicMessage(RabbitMqConstant.MEMBER_SCENE_TRIGGER_QUEUE, JsonUtils.objToString(registerDto));
        // 查询会员身份信息
        MallItem mallItem = hiveVasService.getMallByLbsId(dto.getMallId());
        if (Objects.isNull(mallItem)) {
            return;
        }
        // 会员身份认证确定会员等级
        MemberIdentityResource resource = MemberIdentityResource.builder().userId(tbMemberAsset.getKipUserId())
                .projectId(mallItem.getProjectId()).lbsId(mallItem.getMallId()).build();
        rabbitMqService.sendMessage(RabbitMqConstant.KIP_SYNC_MEMBER_IDENTITY, JsonUtils.objToString(resource));
    }

}
