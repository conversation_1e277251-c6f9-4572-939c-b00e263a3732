package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 09/21/2023 09:01
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendIdentityRewardDto implements Serializable {

    /**
     * 会员号，存在为空的情况
     */
    private String vipCode;

    /**
     * kip user id
     */
    private String kipUserId;

    /**
     * 身份认证所属的lbs id
     */
    private String lbsId;

    /**
     * 身份认证所属的业态，当前有四种:
     * OFFICE("办公楼"),
     * RETAIL("商场"),
     * APARTMENT("公寓"),
     * RESIDENCE("小区")
     */
    private String format;

    /**
     * 楼盘id
     */
    private String projectId;

}
