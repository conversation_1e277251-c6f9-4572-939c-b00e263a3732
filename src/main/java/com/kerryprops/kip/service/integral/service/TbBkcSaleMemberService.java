package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbBkcSaleMember;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/09/2023 15:06
 **********************************************************************************************************************/
public interface TbBkcSaleMemberService extends IService<TbBkcSaleMember> {

    TbBkcSaleMember findByVipcode(String vipcode, String groupId);

    /**
     * 弹窗确认
     * @param loginUser
     * @param confirmStatus
     */
    void bkcMigrationConfirmStatus(LoginUser loginUser, Integer confirmStatus);

    void insertBkcCaseMember(TbBkcSaleMember member);

}
