package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/22/2023 15:52
 **********************************************************************************************************************/
public interface TemplateMessageSendService {

    String POINTS_CHANGE_NUMBER = "{POINTS_CHANGE_NUMBER}";
    String MEMBER_LEVEL = "{MEMBER_LEVEL}";
    String EVENT_TIME = "{EVENT_TIME}";
    String MEMBER_POINTS = "{MEMBER_POINTS}";
    String MEMBER_CARD_NO = "{MEMBER_CARD_NO}";
    String MEMBER_GRADE = "{member_grade}";
    String MEMBER_NICKNAME = "{MEMBER_NICKNAME}";
    String ABB_QHKC = "QHKC";

    void sendMessage(SendMessageDto dto);

}
