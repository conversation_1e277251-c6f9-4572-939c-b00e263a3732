package com.kerryprops.kip.service.integral;

import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.retry.annotation.EnableRetry;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/15/2022 10:21
 **********************************************************************************************************************/

@EnableRetry
@EnableFeignClients
@SpringBootApplication(scanBasePackages = {"com.kerryprops.kip"})
@MapperScan(basePackages = "com.kerryprops.kip.service.integral.mapper")
public class IntegralApplication {

    public static void main(String[] args) {
        SpringApplication.run(IntegralApplication.class, args);
    }

    @Bean
    public HeaderResource headerResource() {
        HeaderResource.Builder builder = new HeaderResource.Builder();
        builder.setSub("point_service");
        return builder.build();
    }


}
