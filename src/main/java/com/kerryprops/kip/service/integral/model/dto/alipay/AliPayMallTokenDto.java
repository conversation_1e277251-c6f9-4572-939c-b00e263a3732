package com.kerryprops.kip.service.integral.model.dto.alipay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝授权token接受类
 * @createDate 2022/10/20
 * @updateDate 2022/10/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AliPayMallTokenDto {
    /**
     * 商场对应的token
     */
    private String appAuthToken;
    /**
     * 商场token刷新令牌
     */
    private String appRefreshToken;
    /**
     * 刷新令牌过期时间
     */
    private Integer reExpiresIn;
}
