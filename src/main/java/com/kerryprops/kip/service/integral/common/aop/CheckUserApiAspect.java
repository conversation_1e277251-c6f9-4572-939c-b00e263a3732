package com.kerryprops.kip.service.integral.common.aop;

import com.kerryprops.kip.service.integral.common.ResultVO;
import com.kerryprops.kip.service.integral.common.utils.AesEncodeUtil;
import com.kerryprops.kip.service.integral.common.utils.MD5;
import com.kerryprops.kip.service.integral.entity.TbAuthorizerUser;
import com.kerryprops.kip.service.integral.service.TbAuthorizerUserService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020年5月25日 下午7:30:00
 * @Description 验证签名和记录日志，切面处理类
 */
@Slf4j
@Aspect
@Component
@Order(2)
@RequiredArgsConstructor
public class CheckUserApiAspect {

    private final TbAuthorizerUserService tbAuthorizerUserService;

    @Pointcut("@annotation(com.kerryprops.kip.service.integral.common.aop.CheckUserApi)")
    public void checkUserCut () {}

    @Around("checkUserCut()")
    public Object around (ProceedingJoinPoint point) throws Throwable {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        HttpServletRequest request = sra.getRequest();
        String ip = request.getRemoteAddr();
        // 请求URL
        String requestURI = request.getRequestURI();
        log.info("Request_URL: {}-{}", requestURI, ip);
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Map<String, String> allParam = BeanUtils.describe(point.getArgs()[0]);
        List<String> keyList = new ArrayList<>();
        for (String key : allParam.keySet()) {
            Object obj = allParam.get(key);
            String o1 = "";
            if (Objects.nonNull(obj)) {
                o1 = obj.toString();
            }
            if ("class".equals(key) || StringUtils.isBlank(o1)) {
                continue;
            }
            if (o1.contains("com.hafeng")) {
                continue;
            }
            if (requestURI.contains("apply4Return") || requestURI.contains("apply4Change")) {
                // 特殊字符导致验签失败
                if ("remark".equals(key)) {
                    continue;
                }
            }
            keyList.add(key);
        }
        CheckUserApi annotation = method.getAnnotation(CheckUserApi.class);
        if (annotation.isCheck()) {
            // 进行验证签名
            String user = request.getHeader("user");
            log.info("request_user_keyList {}--{}", user, String.join(",", keyList));
            TbAuthorizerUser checkUser = tbAuthorizerUserService.getAuthorizerUserByUser(user);
            if (Objects.nonNull(checkUser)) {
                if (0 == checkUser.getIsUse()) {
                    return ResultVO.fail(4002, "请求用户不可用!");
                }
                // 是否需要验证签名
                if (1 == checkUser.getIsCheck()) {
                    // 根据参数生成签名
                    Boolean verifySign = verifySignNew(request, allParam, keyList, AesEncodeUtil.decrypt(checkUser.getPassword()));
                    log.info("供应商：{}，请求地址: {}，验证签名结果: {}", user, requestURI, verifySign);
                    if (Boolean.FALSE.equals(verifySign)) {
                        // 验签失败
                        return ResultVO.fail(4004, "验证签名失败!");
                    }
                }
            } else {
                log.info("不存在的供应商：{}，请求地址: {}，异常查询签名API", user, requestURI);
            }
        }
        try {
            // 执行方法
            return point.proceed(point.getArgs());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 验证签名(new)
     * @throws Exception
     */
    public Boolean verifySignNew (HttpServletRequest request, Map<String, String> allParam, List<String> needParam,
                                  String password) throws Exception {
        // header获取签名 和时间戳
        String sign = request.getHeader("sign");
        // 当前时间戳
        String timeStampStr = request.getHeader("timeStamp");
        log.info("verifySignNewStep1 sign: {}, timeStamp: {}", sign, timeStampStr);
        if (StringUtils.isBlank(sign) || StringUtils.isBlank(timeStampStr)) {
            return false;
        }
        Long timeStamp = Long.parseLong(timeStampStr);
        StringBuilder stringBuilder = new StringBuilder();
        Collections.sort(needParam);
        for (String key: needParam) {
            stringBuilder.append(allParam.get(key));
            stringBuilder.append("&");
        }
        stringBuilder.append(password);
        stringBuilder.append("&");
        stringBuilder.append(timeStamp);
        // MD5加密
        String signCalculate = MD5.getMD5(stringBuilder.toString());
        log.info("verifySignNewStep2 sign: {}, signCalculate: {}", sign, signCalculate);
        if (!signCalculate.equalsIgnoreCase(sign)) {
            //校验失败打印输出 签名
            log.info("Request URI: " + request.getRequestURI()
                    + ", Original string: " + stringBuilder
                    + ", sign from system : " + signCalculate
                    + ", sign from request : " + sign);
            return false;
        }
        return true;
    }

}