package com.kerryprops.kip.service.integral.webservice.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "HKC用户微信授权状态检查返回类")
public class CheckMemberWxAuthorizeOldStatusResponse implements Serializable {

    @Schema(description = "是否需要重新授权, true: 需要重新授权, false: 不需要重新授权")
    private boolean redirectWxAuthPage;

    @Schema(description = "微信商圈id")
    private String mchId;

    @Schema(description = "登录用户openId")
    private String openId;

}
