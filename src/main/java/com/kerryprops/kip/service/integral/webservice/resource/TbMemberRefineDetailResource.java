package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * tb_member_refine_detail
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "完善信息页面配置项类")
public class TbMemberRefineDetailResource implements Serializable {

    @Schema( description = "主键")
    private String id;

    @Schema( description = "配置id")
    private String configId;

    @Schema( description = "字段名称")
    private String fieldName;

    @Schema( description = "字段英文名称")
    private String fieldEname;

    @Schema( description = "是否属于完善内容(0:否;1:是)")
    private Integer isRefine;

    @Schema( description = "是否必填项(0:否;1:是)")
    private Integer isRequired;

    @Schema( description = "C端展示顺序")
    private Integer sort;

    @Schema( description = "字段类型(0:个人信息字段;1:标签)")
    private Integer fieldType;

    @Schema( description = "组件类型")
    private String componentsType;

}