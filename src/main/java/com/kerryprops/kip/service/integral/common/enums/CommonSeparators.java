package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description
 * @createDate 2022/9/6
 * @updateDate 2022/9/6
 */

@Getter
public class CommonSeparators {
    /**
     * 冒号分隔符“:”
     */
    public static final String COLON_SEPARATOR = ":";
    /**
     * 减号分隔符“-”
     */
    public static final String SUB_SEPARATOR = "-";
    /**
     * 逗号分隔符“,”
     */
    public static final String COMMA_SEPARATOR = ",";

    /**
     * 点号分隔符“.”
     */
    public static final String POINT_SEPARATOR = ".";

    /**
     * 空格分隔符“ ”
     */
    public static final String BLANK_SEPARATOR = " ";

    /**
     * 井号分隔符“#”
     */
    public static final String WELL_SEPARATOR = "#";


    /**
     * 加号分隔符“+”
     */
    public static final String ADD_SEPARATOR = "+";

    /**
     * 分号分隔符“;”
     */
    public static final String SEMICOLON = ";";
}
