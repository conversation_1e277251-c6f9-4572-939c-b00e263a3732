package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.ResultVO;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.webservice.resource.MaterialQrcodeResource;
import com.kerryprops.kip.service.integral.webservice.response.MaterialQrcodeResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/27/2024 16:03
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/material")
@RestController
@RequiredArgsConstructor
@Tag(name = "CRM老的二维码获取参数的API")
public class TbQrcodeSourceController {

    @PostMapping(value = "/getMiniCodeParam")
    public ResultVO<MaterialQrcodeResponse> getMiniCodeParam(@RequestBody @Valid MaterialQrcodeResource resource) {
        String json = IntegralConstant.sceneMap.get(resource.getScene());
        if (StringUtils.isBlank(json)) {
            return ResultVO.success();
        }
        return ResultVO.success(JsonUtils.stringToObj(json, MaterialQrcodeResponse.class));
    }

}
