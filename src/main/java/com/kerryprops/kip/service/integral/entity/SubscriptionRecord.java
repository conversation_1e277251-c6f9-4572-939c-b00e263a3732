package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_subscription_record")
@ToString
public class SubscriptionRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集团ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场编号
     */
    @TableField("mall_id")
    private String mallId;

    private String userId;

    @Schema( name = "订阅类型(0:积分拦截 1:拍照积分 2:其它)")
    private Integer subscriptionType;

    @Schema( name = "订阅状态(0: 取消订阅 1:订阅)")
    private Integer status;

    private String createUser;

    private Date createDate;

}