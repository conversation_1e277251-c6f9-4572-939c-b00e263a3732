package com.kerryprops.kip.service.integral.webservice.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema( description = "徽章列表-徽章明细展示类")
public class TbBadgeWithCategoryResponse implements Serializable {

    @Schema( description = "徽章id")
    private Long id;

    @Schema( description = "集团id")
    private String groupId;

    @Schema( description = "徽章名称")
    private String badgeName;

    @Schema( description = "徽章排序")
    private Integer badgeSort;

    @Schema( description = "徽章子标题")
    private String badgeSubtitle;

    @Schema( description = "点亮图片")
    private String brightImg;

    @Schema( description = "未点亮图片")
    private String darkImg;

    @Schema( description = "背景图片")
    private String backgroundImg;

    @Schema( description = "分类id")
    private Long categoryId;

    @Schema( description = "分类名称")
    private String categoryName;

    @Schema( description = "分类排序")
    private Integer categorySort;

    @Builder.Default
    @Schema( description = "是否已领取，true:已获得，false:未获得")
    private boolean received = false;

}
