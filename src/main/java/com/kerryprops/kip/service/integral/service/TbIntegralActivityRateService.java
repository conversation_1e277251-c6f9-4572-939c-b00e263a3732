package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbIntegralActivityRate;
import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import com.kerryprops.kip.service.integral.webservice.resource.TbIntegralActivityRateResource;
import com.kerryprops.kip.service.integral.webservice.response.TbIntegralActivityRateResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbIntegralShopRateResponse;

import java.util.List;

public interface TbIntegralActivityRateService {
/*    int getTotal(String mallId, String rewardNode);

    List<TbIntegralActivityRate> activityRatePage(String mallId, String rewardNode, String order, int page, int size);

    void saveOrUpdateActivityRate(TbIntegralActivityRateResource resource);

    List<TbIntegralActivityRate> getActivityRateList(TbIntegralActivityRate rate);

    void deleteActivityRateList(Long[] ids);*/
}
