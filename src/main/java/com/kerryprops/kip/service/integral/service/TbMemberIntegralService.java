//package com.kerryprops.kip.service.integral.service;
//
//import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
//import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 10/28/2022 09:45
// **********************************************************************************************************************/
//public interface TbMemberIntegralService {
//
//    int updateMemberIntegral(SalesAutoPointsDto dto);
//
//    int updateMemberPoints(MemberPointsChangeDto dto);
//
//    void clearKoIntegral(MemberPointsChangeDto changeDto);
//
//}
