package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import org.apache.ibatis.annotations.Param;

/**
 * @description 会员商场关系表
 * <AUTHOR>
 * @date 2022-09-16
 */

public interface TbCardMemberRelationMapper extends BaseMapper<TbCardMemberRelation> {

    TbCardMemberRelation getByGroupIdAndAliUserId(@Param("groupId") String groupId,
                                                 @Param("userId") String aliUserId);

    TbCardMemberRelation getByMallIdAndAliUserId(@Param("mallId") String mallId,
                                                 @Param("userId") String aliUserId);

    TbCardMemberRelation getByMallIdAndMobile(@Param("mallId") String mallId,
                                                 @Param("mobile") String mobile);

    TbCardMemberRelation getByMallIdAndExternalCardNo(@Param("mallId") String mallId,
                                                 @Param("externalCardNo") String externalCardNo);

    TbCardMemberRelation getByAliUserId(@Param("userId") String aliUserId);

    TbCardMemberRelation getByGroupIdAndMobileAndMallId(@Param("groupId") String groupId,
                                                        @Param("mobile") String mobile,
                                                        @Param("mallId") String mallId);

    TbCardMemberRelation findByExternalCardNo(@Param("externalCardNo") String externalCardNo);
    
}