package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/23/2022 09:52
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "会员注册来源类")
public class MemberRegisterSourceResource implements Serializable {

    @NotBlank(message = "注册来源不能为空.")
    @Schema( description = "注册来源")
    private String registerSource;

    @Schema( description = "注册来源-附注信息(例如：活动，电子券)")
    private String registerSourceLabel;

    @Schema( description = "来源名称")
    private String registerSourceRemark;

    /**
     * 会员来源-UTM参数 JSON格式字符串
     */
    @Schema( description = "会员来源-UTM参数 JSON格式字符串")
    private String regMemberSourceOriginalParams;

}
