package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "徽章查询销售请求类")
public class BadgeSalesResource implements Serializable {

    @NotBlank(message = "集团id不能为空")
    @Schema(description = "集团id")
    private String groupId;

    @NotBlank(message = "会员号不能为空")
    @Schema(description = "会员号")
    private String vipcode;

    @Schema(description = "店铺号")
    private List<String> shopNos;

    @Schema(description = "商场多个id")
    private List<String> mallIds;

    @NotBlank(message = "销售开始时间不能为空")
    @Schema(description = "销售开始时间")
    private String startDate;

    @NotBlank(message = "销售结束时间不能为空")
    @Schema(description = "销售结束时间")
    private String endDate;

    @NotBlank(message = "活动开始时间不能为空")
    @Schema(description = "活动开始时间")
    private String activityStartDate;

    @NotBlank(message = "活动结束时间不能为空")
    @Schema(description = "活动结束时间")
    private String activityEndDate;

    @NotBlank(message = "店铺组id不能为空")
    @Schema(description = "店铺组id")
    private Long shopGroupId;

    @Schema(description = "限制的会员卡等")
    private List<String> grades;

}
