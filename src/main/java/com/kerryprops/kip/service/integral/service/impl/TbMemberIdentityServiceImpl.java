package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.RuleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.MemberIdentityInfo;
import com.kerryprops.kip.service.integral.model.dto.MemberIdentityRuleDto;
import com.kerryprops.kip.service.integral.model.dto.SendIdentityRewardDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.MemberIdentityResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @Desc 会员身份认证触发会员等级变更MQ监听
 * <AUTHOR> Bert
 * Created Date - 02/22/2023 10:08
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class TbMemberIdentityServiceImpl implements TbMemberIdentityService {

    private final HiveVasService hiveVasService;
    private final MallConfig mallConfig;
    private final TbMemberAssetService tbMemberAssetService;
    private final RabbitMqService rabbitMqService;
    private final TbMemberGradeService tbMemberGradeService;
    private final ProfileServiceClient profileServiceClient;
    private final TbMemberGradeRuleService tbMemberGradeRuleService;
    private final TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;

    public static final int DEFAULT_ZERO = 0;

    @Override
    public void kipSyncMemberIdentityProcess(MemberIdentityResource detail) {
        MallItem mallItem = null;
        if (StringUtils.isNotBlank(detail.getLbsId())) {
            mallItem = hiveVasService.getMallByLbsId(detail.getLbsId());
        } else {
            // 根据KIP通过过来时的楼盘id确定属于哪个商场
            String projectId = StringUtils.isBlank(detail.getProjectId()) ? "192" : detail.getProjectId();
            mallItem = mallConfig.getByProjectId(projectId);
        }
        if (Objects.isNull(mallItem)) {
            this.sendMemberAddIdentityReward(detail, null);
            return;
        }
        final String mallId = mallItem.getMallId();
        // 查询会员信息
        TbMemberAsset tbMemberAsset = tbMemberAssetService.getMemberWithProfileInfo(detail.getUserId(), mallItem.getGroupId());
        // 会员身份认证场景触发奖励
        this.sendMemberAddIdentityReward(detail, tbMemberAsset);
        if (Objects.isNull(tbMemberAsset)) {
            log.info("QHKC-会员信息不存在-{}", detail.getUserId());
            return;
        }
        // 用户在所属楼盘下的身份信息
        List<MemberIdentityInfo> identityList = profileServiceClient.getProjectIdentityResponse(detail.getUserId(), mallItem.getProjectId());
        if (CollectionUtils.isEmpty(identityList)) {
            log.info("用户暂无身份信息: {}-{}", detail.getUserId(), mallItem.getGroupId());
            return;
        }
        List<TbMemberGradeRule> rules = tbMemberGradeRuleService.queryByGroupIdAndRuleType(mallItem.getGroupId(), RuleTypeEnum.MEMBER_IDENTITY_UPGRADE.getValue());
        if (CollectionUtils.isEmpty(rules)) {
            log.info("品牌: [{}]未设置会员身份认证信息", mallItem.getGroupId());
            return;
        }
        // 过滤商场设置的身份认证信息配置
        rules = rules.stream().filter(item -> StringUtils.equals(mallId, item.getMallId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rules)) {
            log.info("品牌-商场: [{}-{}]未设置会员身份认证信息", mallItem.getGroupId(), mallItem.getMallId());
            return;
        }
        // 查询groupId下的会员等级信息
        List<TbMemberGrade> grades = tbMemberGradeService.queryGradeSortAscByGroupId(mallItem.getGroupId());
        if (CollectionUtils.isEmpty(grades)) {
            log.info("品牌: [{}]未设置会员等级信息", mallItem.getGroupId());
            return;
        }
        TbMemberGrade memberCurrentGrade = grades.stream().filter(it -> StringUtils.equals(it.getCode(), tbMemberAsset.getGrade())).findFirst().orElse(null);
        if (Objects.isNull(memberCurrentGrade) || Objects.equals(memberCurrentGrade.getUpGradationStatus(), 0)) {
            log.info("memberCurrentGrade 为空或者尊享卡{}", tbMemberAsset.getGrade());
            return;
        }
        List<String> gradeCodes = grades.stream().map(TbMemberGrade::getCode).collect(Collectors.toList());
        // 过滤出尊享卡的rule
        rules = rules.stream().filter(it -> gradeCodes.contains(it.getGradeCode())).collect(Collectors.toList());
        Map<String, Integer> gradeMap = grades.stream().collect(Collectors.toMap(TbMemberGrade::getCode, TbMemberGrade::getSort, (o, n) -> n));
        String newGrade = tbMemberAsset.getGrade();
        String gradeChangeDesc = null;
        String oldGrade = tbMemberAsset.getGrade();
        MemberIdentityRuleDto identityRuleDto = null;
        for (TbMemberGradeRule rule: rules) {
            if (StringUtils.isBlank(rule.getCertification())) {
                continue;
            }
            // 身份规则转化
            identityRuleDto = JsonUtils.stringToObj(rule.getCertification(), MemberIdentityRuleDto.class);
            if (Objects.isNull(identityRuleDto)) {
                continue;
            }
            for ( MemberIdentityInfo identity: identityList) {
                // 办公楼身份判断
                if (this.checkIdentityMatchRelation(identityRuleDto.getOffice(), identity.getOfficeMember())) {
                    if (MapUtils.getIntValue(gradeMap, rule.getGradeCode(), DEFAULT_ZERO) > MapUtils.getIntValue(gradeMap, newGrade, DEFAULT_ZERO)) {
                        newGrade = rule.getGradeCode();
                        gradeChangeDesc = "会员通过办公楼认证进行升级操作";
                    }
                }
                // 公寓身份判断
                if (this.checkIdentityMatchRelation(identityRuleDto.getApartment(), identity.getApartmentMember())) {
                    if (MapUtils.getIntValue(gradeMap, rule.getGradeCode(), DEFAULT_ZERO) > MapUtils.getIntValue(gradeMap, newGrade, DEFAULT_ZERO)) {
                        newGrade = rule.getGradeCode();
                        gradeChangeDesc = "会员通过公寓认证进行升级操作";
                    }
                }
            }
        }
        int newGradeSort = MapUtils.getIntValue(gradeMap, newGrade, DEFAULT_ZERO);
        int oldGradeSort = MapUtils.getIntValue(gradeMap, oldGrade, DEFAULT_ZERO);
        log.info("oldGradeSort -> newGradeSort: [{} - {}]", oldGradeSort, newGradeSort);
        if (newGradeSort > oldGradeSort) {
            log.info("KipUserId: [{}] 满足前海设置的身份认证规则，升级等级: [{} -> {}]", detail.getUserId(), oldGrade, newGrade);
            // 说明会员等级满足身份认证规则，记录等级变更日志及更新会员等级
            tbMemberAsset.setGrade(newGrade);
            // 更新会员等级
            tbMemberAssetService.updateMemberGrade(tbMemberAsset);
            //异步保存会员等级变更记录
            tbMemberAsset.setMallId(mallId);
            tbMemberGradeChangeDetailService.saveMemberGradeChangeRecord(tbMemberAsset, 6, oldGrade, newGrade, gradeChangeDesc, "admin");
        } else {
            log.info("KipUserId: [{}] 前海会员等级未发生变更", detail.getUserId());
        }
    }

    /**
     * 检查配置的身份信息与用户真实身份信息是否匹配
     * @param configIdentity
     * @param realIdentity
     * @return
     */
    private boolean checkIdentityMatchRelation(String configIdentity, String realIdentity) {
        if (StringUtils.isAnyBlank(configIdentity, realIdentity)) {
            return Boolean.FALSE;
        }
        List<String> configList = this.strToList(configIdentity);
        List<String> realList = this.strToList(realIdentity);
        return realList.stream().anyMatch(configList::contains);
    }

    private List<String> strToList(String str) {
        if (StringUtils.isBlank(str)) {
            return Collections.emptyList();
        }
        return CharSequenceUtil.split(str, CommonSeparators.COMMA_SEPARATOR);
    }

    /**
     * 新增身份认证，触发身份认证奖励
     * @param detail
     * @param tbMemberAsset
     */
    private void sendMemberAddIdentityReward(MemberIdentityResource detail, TbMemberAsset tbMemberAsset) {
        if (StringUtils.isAnyBlank(detail.getLbsId(), detail.getAuditType())) {
            return;
        }
        // 不是审核通过的身份认证，不发场景MQ消息
        if (2 != detail.getStatus()) {
            return;
        }
        // 身份认证场景触发奖励
        SendIdentityRewardDto rewardDto = SendIdentityRewardDto.builder().kipUserId(detail.getUserId()).format(detail.getAuditType()).projectId(detail.getProjectId()).lbsId(detail.getLbsId()).build();
        Optional.ofNullable(tbMemberAsset).ifPresent(item -> rewardDto.setVipCode(item.getVipcode()));
        rabbitMqService.sendTopicMessage(RabbitMqConstant.MEMBER_IDENTITY_REWARD, JsonUtils.objToString(rewardDto));
    }

}
