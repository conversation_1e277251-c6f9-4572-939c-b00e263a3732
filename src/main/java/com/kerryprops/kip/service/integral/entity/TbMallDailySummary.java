package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;

import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/13/2022 12:31
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
//@TableName("tb_integral_repeat_rule")
public class TbMallDailySummary {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 集团ID
     */
    private String groupId;

    /**
     * 商场ID
     */
    private String mallId;

    /**
     * 当日小程序注册会员数
     */
    private int miniMemberCount;

    /**
     * 当日新增会员数
     */
    private int memberCount;

    /**
     * 当日销售积分数
     */
    private int saleIntegral;

    /**
     * 当日销售积分会员数
     */
    private int saleIntegralMemberCount;

    /**
     * 当日新增积分数
     */
    private int addIntegral;

    /**
     * 当日新增积分会员数
     */
    private int addIntegralMemberCount;

    /**
     * 当日消耗积分数
     */
    private int subIntegral;

    /**
     * 当日消耗积分会员数
     */
    private int subIntegralMemberCount;

    /**
     * 每日消费金额总数
     */
    private BigDecimal saleAmount;

    /**
     * 消费笔数
     */
    private int saleCount;

    /**
     * 会员消费笔单价
     */
    private BigDecimal eachSaleAmount;

    /**
     * 会员消费客单价
     */
    private BigDecimal eachMemberAmount;

}
