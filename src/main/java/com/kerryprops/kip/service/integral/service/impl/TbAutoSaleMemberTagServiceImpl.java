package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbAutoSaleMemberTag;
import com.kerryprops.kip.service.integral.entity.TbTagMemberNologic;
import com.kerryprops.kip.service.integral.mapper.TbAutoSaleMemberTagMapper;
import com.kerryprops.kip.service.integral.mapper.TbTagMemberNologicMapper;
import com.kerryprops.kip.service.integral.service.CrmVipcodeService;
import com.kerryprops.kip.service.integral.service.TbAutoSaleMemberTagService;
import com.kerryprops.kip.service.integral.webservice.resource.MemberProfileCheckResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/20/2022 11:39
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbAutoSaleMemberTagServiceImpl extends ServiceImpl<TbAutoSaleMemberTagMapper, TbAutoSaleMemberTag> implements TbAutoSaleMemberTagService {

    private final TbAutoSaleMemberTagMapper tbAutoSaleMemberTagMapper;
    private final TbTagMemberNologicMapper tbTagMemberNologicMapper;
    private final CrmVipcodeService crmVipcodeService;

    @Override
    public List<TbAutoSaleMemberTag> getBySecSortIdAndType(String secSortId, Integer type) {
        return tbAutoSaleMemberTagMapper.getBySecSortIdAndType(Long.parseLong(secSortId), type);
    }

    @Override
    public List<TbAutoSaleMemberTag> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return tbAutoSaleMemberTagMapper.selectBatchIds(ids);
    }

    @Override
    public TbTagMemberNologic getByGroupIdAndVipcode(String groupId, String vipcode) {
        return tbTagMemberNologicMapper.getByGroupIdAndVipcode(groupId, vipcode);
    }

    @Override
    public void updateMemberLabels(MemberProfileCheckResource resource) {
        String vipcode = crmVipcodeService.getVipcode(resource.getBrandId(), resource.getKipUserId());
        TbTagMemberNologic nologic = tbTagMemberNologicMapper.getByGroupIdAndVipcode(resource.getBrandId(), vipcode);
        if (Objects.nonNull(nologic)) {
            if (StringUtils.isBlank(resource.getLabels())) {
                tbTagMemberNologicMapper.deleteById(nologic.getId());
            } else {
                nologic.setTagIds(resource.getLabels());
                tbTagMemberNologicMapper.updateById(nologic);
            }
        } else {
            if (StringUtils.isBlank(resource.getLabels())) {
                return;
            }
            nologic = TbTagMemberNologic.builder().groupId(resource.getBrandId()).mallId(resource.getLbsId())
                    .tagIds(resource.getLabels()).vipcode(vipcode).id(IdWorker.getId()).createDate(new Date())
                    .updateDate(new Date()).build();
            tbTagMemberNologicMapper.insert(nologic);
        }
    }
}
