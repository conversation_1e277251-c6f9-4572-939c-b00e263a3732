package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbActivityPromotionJoinvipMapper extends BaseMapper<TbActivityPromotionJoinvip> {

    List<TbActivityPromotionJoinvip> findByPromotionId(@Param("promotionId") String promotionId);

    List<TbActivityPromotionJoinvip> findByPromotionIds(@Param("promotionIds") List<String> promotionIds);

}