package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.CustomizePageTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.ZIndexModuleSignEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.SelfDefiningPage;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.response.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class ZIndexServiceImpl implements ZIndexService {

    private final Mapper mapper;
    private final SelfDefiningPageService selfDefiningPageService;

    @Override
    public void setModulesForCustomize(ZIndexListResponse listResponse, String moduleText) {
        if (StringUtils.isBlank(moduleText)) {
            return;
        }
        List<SelfDefiningModuleResponse> modulesList = JsonUtils.stringToList(moduleText, SelfDefiningModuleResponse.class);
        for (SelfDefiningModuleResponse resp : modulesList) {
            if (Objects.isNull(resp.getMultipleLineList())){
                resp.setMultipleLineList(Collections.emptyList());
            }
            if (Objects.isNull(resp.getImgList())){
                resp.setImgList(Collections.emptyList());
            }
            if (Objects.isNull(resp.getSuspensionList())){
                resp.setSuspensionList(Collections.emptyList());
            }
            if (Objects.isNull(resp.getWaterfallsFlowList())){
                resp.setWaterfallsFlowList(Collections.emptyList());
            }
            if (Objects.isNull(resp.getWaterfallsFlowDTOList())){
                resp.setWaterfallsFlowDTOList(Collections.emptyList());
            }
        }
        this.setWaterFallTabData(modulesList);
        listResponse.setModuleList(modulesList);
    }

    private void setWaterFallTabData(List<SelfDefiningModuleResponse> modulesList) {
        for (SelfDefiningModuleResponse resp : modulesList) {
            if (!Arrays.asList(ZIndexModuleSignEnum.WATERFALLTAB.getVal(), ZIndexModuleSignEnum.WATERFALL.getVal()).contains(resp.getModuleSign())) {
                continue;
            }
            resp.setWaterfallsFlowDTOList(resp.getWaterfallsFlowList());
        }
    }

    @Override
    @Cacheable(value = RedisCacheKey.POINTS_HOME_PAGE_CUSTOMIZE_UI, key = "#groupId +':' + #mallId", unless = "#result == null")
    public ZIndexListResponse getHomePageByGroupIdAndMallId(String groupId, String mallId) {
        SelfDefiningPage zIndex = selfDefiningPageService.getZIndex(groupId, mallId, CustomizePageTypeEnum.HOME_PAGE.getValue());
        if (Objects.isNull(zIndex)) {
            return null;
        }
        ZIndexListResponse indexListResponse = mapper.map(zIndex, ZIndexListResponse.class);
        indexListResponse.setId(zIndex.getId()+"");
        indexListResponse.setMallid(zIndex.getMallId());
        // 设置对应的模块及资源列表
        this.setModulesForCustomize(indexListResponse, zIndex.getModuleContext());
        return indexListResponse;
    }

    @Override
    @Cacheable(value = RedisCacheKey.POINTS_MY_PAGE_CUSTOMIZE_UI, key = "#groupId +':' + #mallId", unless = "#result == null")
    public ZIndexListResponse getMyPageByGroupIdAndMallId(String groupId, String mallId) {
        SelfDefiningPage zIndex = selfDefiningPageService.getZIndex(groupId, mallId, CustomizePageTypeEnum.MY_PAGE.getValue());
        if (Objects.isNull(zIndex)) {
            return null;
        }
        ZIndexListResponse indexListResponse = mapper.map(zIndex, ZIndexListResponse.class);
        indexListResponse.setMallid(zIndex.getMallId());
        // 设置对应的模块及资源列表
        this.setModulesForCustomize(indexListResponse, zIndex.getModuleContext());
        if (CollectionUtils.isEmpty(indexListResponse.getModuleList())) {
            return null;
        }
        SelfDefiningModuleResponse myModule = indexListResponse.getModuleList().stream().filter(it -> StringUtils.equals(ZIndexModuleSignEnum.MY.getVal(), it.getModuleSign())).findFirst().orElse(null);
        // 设置首页需要展示在我的页面的modules(底部导航栏,悬浮框)
        HomePageDisplayMyPageResponse homeResponse = this.getHomePageDisplayInMyPageModules(groupId, mallId, myModule);
        if (Objects.nonNull(homeResponse)) {
            indexListResponse.setCompleteFlag(homeResponse.getCompleteFlag());
            if (CollectionUtils.isNotEmpty(homeResponse.getModuleList())) {
                indexListResponse.getModuleList().addAll(homeResponse.getModuleList());
            }
        }
        return indexListResponse;
    }

    @Override
    @Cacheable(value = RedisCacheKey.POINTS_HOME_PAGE_DISPLAY_MY_PAGE, key = "#groupId +':' + #mallId", unless = "#result == null")
    public HomePageDisplayMyPageResponse getHomePageDisplayInMyPageModules(String groupId, String mallId, SelfDefiningModuleResponse myModule) {
        SelfDefiningPage zIndex = selfDefiningPageService.getZIndex(groupId, mallId, CustomizePageTypeEnum.HOME_PAGE.getValue());
        if (Objects.isNull(zIndex) || Objects.isNull(zIndex.getModuleContext())) {
            return null;
        }
        List<SelfDefiningModuleResponse> moduleList = JsonUtils.stringToList(zIndex.getModuleContext(), SelfDefiningModuleResponse.class);
        if (CollectionUtils.isEmpty(moduleList)) {
            return null;
        }
        // NAVIGATION, SUSPEN, WATERFALL, ADVERTISEMENT(后三种应该不需要,)
        moduleList = moduleList.stream().filter(it -> ZIndexModuleSignEnum.getMyPageConfig().contains(it.getModuleSign())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(moduleList)) {
            return null;
        }
        // 我的页面种suspen相关信息是放在zIndex里面(floating_btn_show,floating_btn_url,my_show_grade)
        // 保存的时候会把floatingBtnUrl存在imageUrl的SuspensionList里面
        if (1 == zIndex.getFloatingBtnShow()) {
            SelfDefiningModuleResponse susModule = SelfDefiningModuleResponse
                    .builder()
                    .moduleSign(ZIndexModuleSignEnum.SUSPEN.getVal())
                    .suspensionList(myModule.getSuspensionList())
                    .build();
            moduleList.add(susModule);
        }
        HomePageDisplayMyPageResponse response = HomePageDisplayMyPageResponse.builder().completeFlag(zIndex.getCompleteFlag()).build();
        response.setModuleList(moduleList);
        return response;
    }

    @Override
    public ZIndexListResponse getPageById(String groupId, String mallId, String id) {
        SelfDefiningPage page = selfDefiningPageService.getPageById(groupId, mallId, id);
        if (Objects.isNull(page)) {
            return null;
        }
        ZIndexListResponse response = mapper.map(page, ZIndexListResponse.class);
        response.setId(page.getId()+"");
        response.setMallid(page.getMallId());
        // 设置对应的模块及资源列表
        this.setModulesForCustomize(response, page.getModuleContext());
        return response;
    }

}
