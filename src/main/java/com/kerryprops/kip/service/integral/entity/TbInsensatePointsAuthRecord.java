package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 无感积分 微信、支付宝授权记录表
 * <AUTHOR>
 * @date 2022-09-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("tb_insensate_points_auth_record")
public class TbInsensatePointsAuthRecord implements Serializable {

    /**
    * 符合规范的唯一主键id
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * openid(origin为0【微信】时为微信的open id，origin为1【支付宝】时为支付宝的user id)
    */
    private String openId;

    /**
    * kip系统中用户唯一id
    */
    private String kipUserId;

    /**
    * 集团id
    */
    private String groupId;

    /**
    * 商场编号
    */
    private String mallId;

    /**
     * 会员卡号
     */
    private String cardNo;

    /**
    * 授权状态，0已授权，1未授权，2: 已取消授权
    */
    private int status;

    /**
    * 来源，0微信，1支付宝 参考InsensateOriginEnum
    */
    private int origin;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 更新时间
    */
    private Date updateDate;

    /**
     * 商圈id
     */
    private String mchid;

    private transient int count;

    /**
     * 支付宝开卡授权状态判断
     */
    private transient String mobile;
}