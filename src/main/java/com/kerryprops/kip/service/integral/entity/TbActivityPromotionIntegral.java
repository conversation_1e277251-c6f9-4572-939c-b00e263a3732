package com.kerryprops.kip.service.integral.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * tb_activity_promotionintegral
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_activity_promotion_points")
public class TbActivityPromotionIntegral implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 营销规则id
     */
    @TableField("promotion_id")
    private String promotionId;

    /**
     * 营销活动名称
     */
    @TableField("promotion_name")
    private String promotionName;

    /**
     * 积分调整记录id
     */
    @TableField("points_id")
    private String pointsId;

    /**
     * 会员编号
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * 营销积分 倍数*基础积分
     */
    private Integer integral;

    /**
     * 可用营销积分
     */
    @TableField("enable_integral")
    private Integer enableIntegral;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 初始积分
     */
    @TableField("prime_integral")
    private Integer primeIntegral;

    /**
     * 消费金额
     */
    @TableField("sale_money")
    private BigDecimal saleMoney;

    /**
     * 商场号
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 销售单号
     */
    @TableField("sell_no")
    private String sellNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 营销积分过期时间
     */
    @TableField("expire_time")
    private Date expireTime;

    @TableField("create_date")
    private Date createDate;

    @TableField("update_date")
    private Date updateDate;

    public void fillDefaultVal() {
        if (StringUtils.isBlank(this.promotionId)) {
            this.promotionId = "";
        }
        if (StringUtils.isBlank(this.promotionName)) {
            this.promotionName = "";
        }
        if (StringUtils.isBlank(this.pointsId)) {
            this.pointsId = "";
        }
        if (StringUtils.isBlank(this.vipcode)) {
            this.vipcode = "";
        }
        if (Objects.isNull(this.integral)) {
            this.integral = 0;
        }
        if (Objects.isNull(this.enableIntegral)) {
            this.enableIntegral = 0;
        }
        if (Objects.isNull(this.expireTime)) {
            this.expireTime = DateUtil.parseTime("9999-12-31 23:59:59");
        }
        if (StringUtils.isBlank(this.mobile)) {
            this.mobile = "";
        }
        if (Objects.isNull(this.primeIntegral)) {
            this.primeIntegral = 0;
        }
        if (Objects.isNull(this.saleMoney)) {
            this.saleMoney = BigDecimal.ZERO;
        }
        if (StringUtils.isBlank(this.mallId)) {
            this.mallId = "";
        }
        if (StringUtils.isBlank(this.sellNo)) {
            this.sellNo = "";
        }
        if (StringUtils.isBlank(this.remark)) {
            this.remark = "";
        }
    }

}