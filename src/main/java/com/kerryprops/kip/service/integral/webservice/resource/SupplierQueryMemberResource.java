package com.kerryprops.kip.service.integral.webservice.resource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 10/30/2024 11:54
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierQueryMemberResource implements Serializable {

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 会员编号.(自动生成.)
     */
    private String vipcode;

    /**
     * 会员车牌号
     */
    private String plate;

    /**
     * 集团编号
     */
    private String groupId;

    /**
     * 开卡商城
     */
    private String mallid;

    /**
     * 调用方
     */
    private String createuser;

}
