package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
import com.kerryprops.kip.service.integral.service.TemplateMessageSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.*;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/06/2023 14:03
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class MemberGradeOrIntegralChangeListener {

    private final TemplateMessageSendService templateMessageSendService;

    /**
     * 自定义线程池去消费会员等级、积分变更关于消息的发送逻辑
     */
    private static final ExecutorService executorService =  new ThreadPoolExecutor(3, 10,5,
            TimeUnit.MINUTES, new ArrayBlockingQueue<>(5, true), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.XCRM_MEMBER_INTEGRAL_GRADE_CHANGE}, containerFactory = "crmContainerFactory")
    public void sendMessage(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("发生了会员等级或积分变更消息内容为空.");
            return;
        }
        log.info("MemberGradeOrIntegralChangeListener: {}", msg);
        SendMessageDto messageDto = JsonUtils.stringToObj(msg, SendMessageDto.class);
        if (Objects.isNull(messageDto)) {
            log.info("会员等级或积分变更信息内容为空: {}", msg);
            return;
        }
        // 发送短信，模版消息及邮件
        executorService.execute(() -> templateMessageSendService.sendMessage(messageDto));
    }

}
