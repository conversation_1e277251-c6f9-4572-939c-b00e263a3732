package com.kerryprops.kip.service.integral.model.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 微信支付回调通知数据dto
 * @createDate 2022/9/7
 * @updateDate 2022/9/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeChatPayResourceDto {

    /**
     * 微信支付分配的商户号
     * 示例值：1230000109
     */
    @Schema(name = "商户号")
    private String mchid;

    /**
     * 商圈商户名称
     * 示例值：微信支付
     */
    @Schema(name = "商圈商户名称")
    @JsonProperty("merchant_name")
    private String merchantName;

    /**
     * 门店名称，商圈在商圈小程序上圈店时填写的门店名称
     * 示例值：微信支付
     */
    @Schema(name = "门店名称")
    @JsonProperty(value = "shop_name")
    private String shopName;

    /**
     * 门店编号，商圈在商圈小程序上圈店时填写的门店编号，用于跟商圈自身已有的商户识别码对齐
     * 示例值：123456
     */
    @Schema(name = "门店编号")
    @JsonProperty(value = "shop_number")
    private String shopNumber;

    /**
     * 顾客授权积分时使用的小程序的appid
     * 示例值：wxd678efh567hg6787
     */
    @Schema(name = "小程序APPID")
    private String appid;

    /**
     * 顾客授权时使用的小程序上的openid
     * 示例值：oUpF8uMuAJ2pxb1Q9zNjWeS6o
     */
    @Schema(name = "用户标识")
    @JsonProperty(value = "openid")
    private String openId;

    /**
     * 交易完成时间，遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，HH:mm:ss表示时分秒毫秒，TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。例如：2015-05-20T13:29:35+08:00表示北京时间2015年05月20日13点29分35秒（需要增加所有跟时间有关的参数的描述）
     * 示例值：2015-05-20T13:29:35+08:00
     */
    @Schema(name = "交易完成时间")
    @JsonProperty(value = "time_end")
    private String timeEnd;

    /**
     * 用户实际消费金额，单位（分）
     * 示例值：200
     */
    @Schema(name = "金额")
    private int amount;

    /**
     * 微信支付订单号
     * 示例值：1234567890
     */
    @Schema(name = "门店编号")
    @JsonProperty(value = "transaction_id")
    private String transactionId;

    /**
     * 手动提交积分标记，自动提交时无该字段，用于区分用户手动申请后推送的积分数据
     * 示例值：oUpF8uMuAJ2pxb1Q9zNjWUHsd
     */
    @Schema(name = "手动提交积分标记")
    @JsonProperty(value = "commit_tag")
    private String commitTag;


}
