package com.kerryprops.kip.service.integral.service.promotion;

import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;

import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:15
 **********************************************************************************************************************/
public interface PromotionRule {

    String getRuleType();

    /**
     * 检查活动是否匹配规则
     * @param condition
     * @param dto
     * @param saleDate
     * @param vips
     * @return
     */
    boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips);

}
