package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 12/25/2024 16:14
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "优惠买单-订单退款请求类")
public class KerryPayRefundResource implements Serializable {

    @NotBlank(message = "集团id不能为空")
    @Schema(description = "集团id")
    private String groupId;

    @NotBlank(message = "商场id不能为空")
    @Schema(description = "商场id")
    private String mallId;

    @NotBlank(message = "kip用户id不能为空")
    @Schema(description = "kip用户id")
    private String kipUserId;

    @NotBlank(message = "租户id不能为空")
    @Schema(description = "租户id")
    private String tenantId;

    @NotBlank(message = "订单号不能为空")
    @Schema(description = "订单号")
    private String orderNo;

}
