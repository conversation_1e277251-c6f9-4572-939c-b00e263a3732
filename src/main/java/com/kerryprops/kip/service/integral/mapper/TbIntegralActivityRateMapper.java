package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbIntegralActivityRate;
import com.kerryprops.kip.service.integral.entity.TbIntegralShopRate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/13/2023 09:17
 * 已废弃，现在走的studioZ自己的接口
 **********************************************************************************************************************/
public interface TbIntegralActivityRateMapper extends BaseMapper<TbIntegralActivityRate> {
    /*int getTotal(@Param("mallId") String mallId, @Param("rewardNode") String rewardNode);

    List<TbIntegralActivityRate> activityRatePage(@Param("mallId") String mallId,@Param("rewardNode")  String rewardNode,@Param("order") String order,@Param("offset") int offset,@Param("size") int size);

    void deleteByParams(@Param("mallId") String mallId, @Param("rewardNode") String rewardNode);

    List<TbIntegralActivityRate> getActivityRateList(TbIntegralActivityRate rate);

    void saveActivityRateList(@Param("rateList") List<TbIntegralActivityRate> rateList);*/
}
