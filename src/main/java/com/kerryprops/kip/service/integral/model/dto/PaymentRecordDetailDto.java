package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 05/04/2023 21:20
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRecordDetailDto implements Serializable {

    /**
     * 订单来源：CRM
     */
    private String orderSource;

    /**
     * 电子券购买订单号
     */
    private String orderNo;

    private String payUnitId;

    /**
     * 订单金额
     */
    private String orderAmount;

    /**
     * 交易状态: SUCCESS
     */
    private String state;

    /**
     * 订单创建时间
     */
    private String createdDate;

    /**
     * 支付完成时间
     */
    private String finishedDate;

    private String pspName;

    /**
     * 支付方式: WECHATPAY/ALIPAY
     */
    private String payOption;

    /**
     * 优惠金额
     */
    private String reducedAmount;

    /**
     * 会员信息：mallId，groupId，vipcode
     */
    private Map<String, String> extraParams;

    /**
     * 购买产品类型:
     * REWARDS_MALL: 积分商场
     * KERRY_PAY: 优惠买单
     */
    private String productType;

    /**
     * 退款给的退款单号
     */
    private String refundBatchCode;


    /**
     * 订单金额
     */
    private String amount;

}
