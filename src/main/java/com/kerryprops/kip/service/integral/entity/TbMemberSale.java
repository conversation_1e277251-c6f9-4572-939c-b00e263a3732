package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member_sale
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("tb_member_sale")
public class TbMemberSale implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 销售单号(销售调整)流水号
     */
    @TableField(value = "sellNo")
    private String sellno;

    /**
     * 会员编号
     */
    @TableField(value = "vipcode")
    private String vipcode;

    /**
     * 积分记录id
     */
    @TableField(value = "integraladjustId")
    private String integraladjustid;

    /**
     * 有单退货扣积分 积分记录id
     */
    @TableField(value = "backIntegraladjustid")
    private String backIntegraladjustid;

    /**
     * 销售金额
     */
    @TableField(value = "saleMoney")
    private Double salemoney;

    /**
     * 优惠金额
     */
    @TableField(value = "preferentialMoney")
    private Double preferentialmoney;

    /**
     * 消费金额
     */
    @TableField(value = "useMoney")
    private Double usemoney;

    /**
     * 店铺名称
     */
    @TableField(value = "shopName")
    private String shopName;

    /**
     * 消费金额积分
     */
    @TableField(value = "integral")
    private Double integral;

    /**
     * 商场编号
     */
    @TableField(value = "shopId")
    private String shopid;

    /**
     * 商场编号
     */
    @TableField(value = "mallid")
    private String mallid;

    /**
     * 退货状态（0->未退货 1->退货）
     */
    private String status;

    /**
     * 销售类型1.CRM销售,2.POS销售,3.微信照片,4.微信扫码,5.口碑销售,7.微信无感积分,8.支付宝无感积分
     */
    @TableField(value = "saleType")
    private String saleType;

    /**
     * 退货金额
     */
    @TableField("backMoney")
    private Double backmoney;

    /**
     * 退货金额积分
     */
    @TableField("backIntegral")
    private Double backintegral;

    @TableField("parentOrderNo")
    private String parentOrderNo;

    /**
     * 扩展字段1
     */
    @TableField("extend1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @TableField("extend2")
    private String extend2;

    /**
     * 扩展字段3
     */
    @TableField("extend3")
    private String extend3;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_date")
    private Date updateDate;

    /**
     * 更新人
     */
    @TableField(value = "updateUser")
    private String updateUser;

    /**
     * 创建人
     */
    @TableField(value = "createUser")
    private String createUser;

    /**
     * 集团ID
     */
    @TableField(value = "group_id")
    private String groupId;

    /**
     * 部门ID
     */
    @TableField(value = "dept_id")
    private String deptId;

    /**
     * 销售小票地址
     */
    @TableField(value = "image_url")
    private String imageUrl;

    /**
     * 变更前积分
     */
    @TableField(value = "beforeIntegral")
    private Integer beforeIntegral;

    /**
     * 变更前等级
     */
    @TableField(value = "beforeGrade")
    private String beforeGrade;

}