package com.kerryprops.kip.service.integral.strategy;

import com.alipay.api.AlipayClient;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.exception.BizException;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝无感积分策略类
 * @createDate 2022/10/13
 * @updateDate 2022/10/13
 */
public class AliPayStrategy {

    private static final Map<String, AlipayClient> CLIENTS = new ConcurrentHashMap<>();

    public static AlipayClient getClient(String appId){
        AlipayClient alipayClient = CLIENTS.get(appId);
        if (Objects.isNull(alipayClient)) {
            throw BizException.error(PointsEnum.MALL_NOT_EXISTS);
        }
        return alipayClient;
    }

    public static void registerClient(String appId, AlipayClient client){
        CLIENTS.put(appId, client);
    }
}
