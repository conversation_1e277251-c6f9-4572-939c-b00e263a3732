package com.kerryprops.kip.service.integral.webservice.resource;

import com.kerryprops.kip.service.integral.webservice.response.GradeIntegralResponse;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/23/2023 13:39
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbIntegralActivityRateResource implements Serializable {

    @NotBlank(message = "groupId不能为空")
    private String groupId;

    @NotBlank(message = "mallId不能为空")
    private String mallId;

    @NotBlank(message = "rewardNode不能为空")
    private String rewardNode;

    private String creator;

    private List<GradeIntegralResponse> list;

}