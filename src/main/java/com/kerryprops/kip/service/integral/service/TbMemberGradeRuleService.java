package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeRuleService
 * @Description TbMemberGradeRuleService
 * @date 2022/9/27 14:48
 * @Version 1.0
 */
public interface TbMemberGradeRuleService extends IService<TbMemberGradeRule> {

    List<TbMemberGradeRule> queryGradeRulesByGroupIdAndGradeAndRuleTypes(String groupId, String grade, List<Integer> ruleTypes);

    TbMemberGradeRule queryUpgradeRule(String groupId, String grade);

    List<TbMemberGradeRule> queryByGroupIdAndType(String groupId, Integer type);

    List<TbMemberGradeRule> queryByGroupIdAndRuleType(String groupId, Integer ruleType);

    /**
     *
     * @param groupId
     * @param grade
     * @param type: 类型（0、保级；1、升级）
     * @return
     */
    List<TbMemberGradeRule> queryByGroupIdAndGradeAndType(String groupId, String grade, Integer type);

    /**
     * 查询年统计是否是周期年
     * @param groupId
     * @param type: 类型（0、保级；1、升级）
     * @return
     */
    boolean isCycleYear(String groupId, Integer type);

}
