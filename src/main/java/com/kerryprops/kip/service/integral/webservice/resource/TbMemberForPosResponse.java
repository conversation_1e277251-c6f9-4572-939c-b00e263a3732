package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/01/2022 10:45
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "为海鼎准备的CRM会员信息")
public class TbMemberForPosResponse implements Serializable {

    @Schema( description = "集团ID")
    private String groupId;

    @Schema( description = "商场Id")
    private String mallId;

    @Schema( description = "手机号码")
    private String mobile;

    @Schema( description = "会员编号")
    private String vipcode;

    @Schema( description = "加入日期")
    private Date joinTime;

    @Schema( description = "性别，0:未知, 1:男, 2:女")
    private Integer gender;

    @Schema( description = "积分抵扣金额数")
    private String amountOfPoints;

    @Schema( description = "会员当前积分")
    private Integer currentIntegral;

    private String nickName;

    private String realName;

}
