package com.kerryprops.kip.service.integral.webservice.resource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 06/27/2024 10:49
 **********************************************************************************************************************/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmallTicketRejectResource implements Serializable {

    /**
     * 错误id
     */
    private Integer id;

    /**
     * 错误名称
     */
    private String name;

    /**
     * 缺少的五要素字段列表
     */
    private List<String> missingFields;

    /**
     * 不能识别小票类型，即黑白名小票
     */
    private String type;

}
