package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 16:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "线上商场积分类")
public class TbOnlineShopConfigResponse implements Serializable {

    @Schema( description = "主键id, 更新时必填")
    private Long id;

    @Schema( description = "groupId")
    private String groupId;

    @Schema( description = "mall_id")
    private String mallId;

    @Schema( description = "会员等级")
    private String grade;

    @Schema( description = "业务类型")
    private String businessType;

    @Schema( description = "业务名称")
    private String businessName;

    @Schema( description = "消费金额")
    private BigDecimal money;

    @Schema( description = "积分数")
    private BigDecimal pointNum;

    @Schema( description = "创建时间")
    @JsonFormat(timezone="GMT+8", pattern="yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date createDate;

    @Schema( description = "创建人")
    private String creator;

    @Schema( description = "修改时间")
    @JsonFormat(timezone="GMT+8", pattern="yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date updateDate;

    @Schema( description = "更新人")
    private String updater;

    @Schema( description = "积分比例是否统一")
    private int isConsistent;

}
