package com.kerryprops.kip.service.integral.model.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 微信支付回调通知数据加密dto
 * @createDate 2022/9/7
 * @updateDate 2022/9/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeChatPayResourceEncryptedDto {

    /**
     * 对支付结果数据进行加密的加密算法，目前只支持AEAD_AES_256_GCM。
     * 示例值：AEAD_AES_256_GCM
     */
    @Schema(name = "加密算法类型")
    private String algorithm;

    /**
     * Base64编码后的支付结果数据密文
     * 示例值：EV-2018022511223320873
     */
    @Schema(name = "数据密文")
    private String ciphertext;

    /**
     * 原始回调类型为：discount_card
     * 示例值：discount_card
     */
    @Schema(name = "原始回调类型")
    @JsonProperty(value = "original_type")
    private String originalType;

    /**
     * 附加数据
     * 示例值：EV-2018022511223320873
     */
    @Schema(name = "附加数据")
    @JsonProperty(value = "associated_data")
    private String associatedData;

    /**
     * 加密使用的随机串
     * 示例值：fdasflkja484w
     */
    @Schema(name = "随机串")
    private String nonce;

}
