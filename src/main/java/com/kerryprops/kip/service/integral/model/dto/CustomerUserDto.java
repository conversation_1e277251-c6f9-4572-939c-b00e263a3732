package com.kerryprops.kip.service.integral.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - user-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/08/2021 16:22
 **********************************************************************************************************************/

@Get<PERSON>
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerUserDto implements Serializable {

    private String id;

    /**
     * 手机区号
     */
    private String areaCode;

    private String phoneNumber;

    private String email;

    private String nickName;

    private String realName;

    private String avatar;

    /**
     * 性别，0：未知，1：男，2：女
     */
    private Integer gender;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthDate;

    private Integer certificateType;

    private String certificateNum;

    private Integer maritalStatus;

    private Integer babyStatus;

    private String provinceAddress;

    private String cityAddress;

    private String districtAddress;

    private String address;

    private String companyName;

    private String job;

    private String nation;

    private String homePhone;

    private Integer education;

    private Integer profession;

}
