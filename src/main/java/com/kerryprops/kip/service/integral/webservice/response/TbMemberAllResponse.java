package com.kerryprops.kip.service.integral.webservice.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/01/2022 10:45
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "CRM会员信息+Profile信息")
public class TbMemberAllResponse implements Serializable {

    @Schema( name = "集团ID")
    private String groupId;

    @Schema( name = "商场Id")
    private String mallId;

    @Schema( name = "手机区号")
    private String areaCode;

    @Schema( name = "手机号码")
    private String mobile;

    @Schema( name = "会员编号")
    private String vipcode;

    @Schema( name = "当前积分")
    private Integer currentPoints;

    @Schema( name = "当前会员卡等[编码]")
    private String grade;

    @Schema( name = "会员状态（0:冻结；1:非冻结)")
    private String status;

    @Schema( name = "会员邮箱")
    private String email;

    @Schema( name = "备注信息")
    private String remark;

    @Schema( name = "加入日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    @Schema( name = "是否完善信息（0:未完善；1:完善)")
    private String isCompleted;

    @Schema( name = "kip对应的user_id")
    private String kipUserId;

    @Schema( name = "昵称")
    private String nickName;

    @Schema( name = "真实姓名")
    private String realName;

    @Schema( name = "头像")
    private String avatar;

    @Schema( name = "性别，0:未知, 1:男, 2:女")
    private Integer gender;

    @Schema( name = "生日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date birthDate;

}
