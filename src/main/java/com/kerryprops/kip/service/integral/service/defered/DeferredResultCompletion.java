package com.kerryprops.kip.service.integral.service.defered;

import com.kerryprops.kip.service.integral.common.utils.DeferredResultCacheUtils;
import lombok.extern.slf4j.Slf4j;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 * @DESC: deferredResult完成时(正常完成或超时被取消)，移除map内的缓存
 * Author - zhangxiliang
 * Created Date - 08/06/2024 09:07
 **********************************************************************************************************************/

@Slf4j
public class DeferredResultCompletion implements Runnable {

    private final String key;

    public DeferredResultCompletion(String key) {
        this.key = key;
    }

    @Override
    public void run() {
        // 移除map内的缓存
        DeferredResultCacheUtils.removeResultMap(key);
        log.info("OCR小票识别请求任务完成: {}-{}", key, DeferredResultCacheUtils.getSize());
    }
}
