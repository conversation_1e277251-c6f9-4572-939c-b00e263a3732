package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.Arrays;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/24/2023 16:39
 **********************************************************************************************************************/

@Getter
public enum EmailTypeEnum {

    POINTS_CHANGE("5", 8),
    GRADE_CHANGE("4", 7),
    MEMBER_REGISTER("17", 0);

    private String type;
    private int sendNode;

    EmailTypeEnum(String type, int sendNode) {
        this.type = type;
        this.sendNode = sendNode;
    }

    public static EmailTypeEnum getByType(String type) {
        return Arrays.stream(EmailTypeEnum.values()).filter(p -> p.getType().equals(type)).findFirst().orElse(null);
    }

}
