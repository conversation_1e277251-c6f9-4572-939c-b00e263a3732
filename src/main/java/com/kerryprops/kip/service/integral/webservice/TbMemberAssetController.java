package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.ResultVO;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.*;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.*;
import com.kerryprops.kip.service.integral.webservice.response.CrmMemberResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbMemberAllResponse;
import com.kerryprops.kip.service.integral.webservice.response.TbMemberDetailResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/21/2022 16:25
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/member")
@RestController
@RequiredArgsConstructor
@Tag(name = "会员及积分api")
public class TbMemberAssetController extends BaseMemberStatus {

    private final TbMemberAssetService tbMemberAssetService;
    private final MemberRegisterService memberRegisterService;
    private final TbMemberPointsChangeService tbMemberPointsChangeService;
    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private final TbMemberGradeChangeDetailService tbMemberGradeChangeDetailService;
    private final KerryStaffService kerryStaffService;
    private final TbPointsDetailService tbPointsDetailService;
    private final TbSalesDetailService tbSalesDetailService;
    private final CrmVipcodeService crmVipcodeService;
    private final TbPointsExpiredReminderService tbPointsExpiredReminderService;
    private final TbCardMemberRelationService tbCardMemberRelationService;
    private final SysDictService sysDictService;
    private final RabbitMqService rabbitMqService;
    private final HiveVasService hiveVasService;
    private final RedisService redisService;
    private final Mapper mapper;
    private final MallConfig mallConfig;

    @GetMapping("/current")
    @Operation(summary="CRM获取登录会员卡等积分信息", method = "GET")
    public TbMemberResponse getMember(@CurrentUser LoginUser loginUser) {
        return tbMemberAssetService.getLoginMemberInfo(loginUser);
    }

    @Hidden
    @GetMapping("/crm_membership")
    public CrmMemberResponse getCrmMemberInfo(@RequestParam("brandId") String brandId,
                                              @RequestParam("kipUserId") String kipUserId) {
        TbMemberAsset asset = memberRegisterService.findByGroupIdAndKipUserId(brandId, kipUserId);
        return Objects.nonNull(asset) ? mapper.map(asset, CrmMemberResponse.class) : null;
    }

    /**
     * CRM删除账号时，该API用户profile-service检查该kipUserId在crm系统内其他brand下是否还存在账号信息
     * @param kipUserId
     * @return
     */
    @Hidden
    @GetMapping("/check_members_by_kipUserId")
    public List<CrmMemberResponse> checkMembersByKipUserId(@RequestParam("kipUserId") String kipUserId) {
        List<TbMemberAsset> memberList = memberRegisterService.findByKipUserId(kipUserId);
        return CollectionUtils.isEmpty(memberList) ? Collections.emptyList() :
                memberList.stream().map(item -> mapper.map(item, CrmMemberResponse.class)).toList();
    }

    @Hidden
    @GetMapping("/check-member-status")
    public TbMemberDetailResponse getMemberStatus(@RequestParam("brandId") String brandId,
                                                  @RequestParam("kipUserId") String kipUserId) {
        return this.getMemberDetailResponse(tbMemberAssetService.findByKipUserIdAndGroupId(kipUserId, brandId));
    }

    /**
     * 对象转换
     * @param member
     * @return
     */
    private TbMemberDetailResponse getMemberDetailResponse(TbMemberAsset member) {
        if (Objects.isNull(member)) {
            return null;
        }
        TbMemberDetailResponse response = mapper.map(member, TbMemberDetailResponse.class);
        response.setCurrentIntegral(member.getCurrentPoints());
        response.setWxopenmarket(member.getWxOpenMarket());
        return response;
    }

    private static final String REPEATED_OPT_POINTS_KEY = "crm:repeated:opt:points:%s:%s:%s";

    @PutMapping("/points/adjust")
    @Operation(summary="CRM会员卡积分调整，返回积分变更记录ID", method = "PUT")
    public PointsAdjustResponse updateMemberPoints(@RequestBody @Valid MemberPointsChangeResource resource) {
        MemberPointsChangeDto changeDto = mapper.map(resource, MemberPointsChangeDto.class);
        // 返回积分调整记录id
        String adjustId = null;
        String repeatedOptPointsKey = null;
        try {
            String orderNo = StringUtils.isNotBlank(resource.getSaleNo()) ? resource.getSaleNo() : resource.getAssociatedBusinessId();
            if (StringUtils.isNotBlank(orderNo)) {
                // 重复操作积分校验
                repeatedOptPointsKey = String.format(REPEATED_OPT_POINTS_KEY, resource.getGroupId(), resource.getVipcode(), orderNo);
                // Redis Key设置，如果成功，则继续积分操作，不成功，则存在并发调用，返回报错信息
                Boolean absent = redisService.setIfAbsentWithExpire(repeatedOptPointsKey, resource.getVipcode(), 5L, TimeUnit.SECONDS);
                if (Objects.nonNull(absent) && Boolean.FALSE.equals(absent)) {
                    throw BizException.error(PointsEnum.REPEATED_OPT_POINTS_ERROR);
                } else {
                    Date currentTime = new Date();
                    // 判断该操作是否已完成
                    TbPointsDetail detail = tbPointsDetailService.checkPointsAdjustExistRecord(resource.getGroupId(), resource.getMallId(), resource.getVipcode(), orderNo,
                            resource.getChangePointsNum(), resource.getDictValue(), DateUtil.formatDateTime(DateUtil.offsetHour(currentTime, -1)),
                            DateUtil.formatDateTime(currentTime));
                    if (Objects.nonNull(detail)) {
                        return PointsAdjustResponse.builder().pointsAdjustId(detail.getCrmId()).changePointsNum(detail.getAmount()).build();
                    }
                }
            }
            adjustId = tbMemberPointsChangeService.updateMemberPoints(changeDto);
        } finally {
            Optional.ofNullable(repeatedOptPointsKey).ifPresent(key -> redisService.delKeys(Collections.singletonList(key)));
        }
        return PointsAdjustResponse.builder().pointsAdjustId(adjustId).changePointsNum(resource.getChangePointsNum()).build();
    }

    @GetMapping("/integral/record")
    @Operation(summary = "查询会员积分变更明细", method = "GET")
    @Parameters({
            @Parameter(name = "type", description = "积分类型，0: 全部，1：收入，2：支出", required = false)
    })
    public PageIntegralDetailResponse getMemberIntegralAdjustRecord(@CurrentUser LoginUser loginUser, @RequestParam(value = "type", required = false) Integer type,
                                                                    @PageableDefault(page = 0, size = 10) Pageable pageable) {
        // 根据LBS查询其所属的楼盘信息
        MallItem mallItem = hiveVasService.getMallByLbsId(loginUser.getLbsId());
        if (Objects.isNull(mallItem)) {
            return PageIntegralDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).currentIntegral(0).build();
        }
        // 查询当前会员信息
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByKipUserIdAndGroupId(loginUser.getCId(), loginUser.getBrandId());
        if (Objects.isNull(tbMemberAsset)) {
            return PageIntegralDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).currentIntegral(0).build();
        }
        // Hotfix 3.20.2修改，C端积分明细，只展示周期年(包括末尾整个月)内的记录
        String startTime = DateUtil.formatDateTime(DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -12)));
        // 查询积分变更记录
        MemberIntegralAdjustRecordPageQueryDto queryDto = MemberIntegralAdjustRecordPageQueryDto.builder().groupId(mallItem.getGroupId())
                .vipcode(tbMemberAsset.getVipcode()).type(type).excludeList(Collections.singletonList("SMR001")).startTime(startTime)
                .page(pageable.getPageNumber()).size((pageable.getPageSize() >= 100) ? 50 : pageable.getPageSize()).build();
        int count = tbPointsDetailService.getCount(queryDto);
        if (count <= 0) {
            return PageIntegralDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0)
                    .currentIntegral(tbMemberAsset.getCurrentPoints()).build();
        }
        List<TbPointsDetail> list = tbPointsDetailService.recordList(queryDto);
        return this.fillData(list, queryDto, count, tbMemberAsset);
    }

    @GetMapping("/integral/alipayRecord")
    @Operation(summary="查询会员积分变更明细-支付宝H5页面", method = "GET")
    @Parameters({
            @Parameter(name = "type", description = "积分类型，0: 全部，1：收入，2：支出", required = false),
            @Parameter(name = "cardNo", description = "支付宝给的会员号", required = true)
    })
    public PageIntegralDetailResponse getPointsDetail(@RequestParam("cardNo") String cardNo, @RequestParam(value = "type", required = false) Integer type,
                                                      @PageableDefault(page = 0, size = 10) Pageable pageable) {
        // 根据LBS查询其所属的楼盘信息
        TbCardMemberRelation relation = tbCardMemberRelationService.findByExternalCardNo(cardNo);
        if (Objects.isNull(relation)) {
            return PageIntegralDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).currentIntegral(0).build();
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(relation.getGroupId()).mobile(relation.getMobile()).build());
        if (Objects.isNull(tbMemberAsset)) {
            return PageIntegralDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0).currentIntegral(0).build();
        }
        // Hotfix 3.20.2修改，C端积分明细，只展示周期年(包括末尾整个月)内的记录
        String startTime = DateUtil.formatDateTime(DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -12)));
        // 查询积分变更记录
        MemberIntegralAdjustRecordPageQueryDto queryDto = MemberIntegralAdjustRecordPageQueryDto.builder().groupId(tbMemberAsset.getGroupId()).startTime(startTime)
                .vipcode(tbMemberAsset.getVipcode()).type(type).page(pageable.getPageNumber()).size((pageable.getPageSize() >= 100) ? 50 : pageable.getPageSize()).build();
        int count = tbPointsDetailService.getCount(queryDto);
        if (count <= 0) {
            return PageIntegralDetailResponse.builder().content(Collections.emptyList()).totalElements(0).totalPages(0)
                    .currentIntegral(tbMemberAsset.getCurrentPoints()).build();
        }
        List<TbPointsDetail> list = tbPointsDetailService.recordList(queryDto);
        return this.fillData(list, queryDto, count, tbMemberAsset);
    }

    @GetMapping("/integral_clear/quantity")
    @Operation(summary="会员积分明细页面，查询积分清零数", method = "GET")
    public Integer getMemberClearIntegralNum(@CurrentUser LoginUser loginUser) {
        // 根据LBS查询其所属的楼盘信息
        MallItem mallItem = hiveVasService.getMallByLbsId(loginUser.getLbsId());
        if (Objects.isNull(mallItem)) {
            return 0;
        }
        IntegralClearShowDto showDto = tbPointsExpiredReminderService.getPointsClearMessage(mallItem.getGroupId(), mallItem.getMallId(),
                crmVipcodeService.getVipcode(mallItem.getGroupId(), loginUser.getCId()));
        if (Objects.nonNull(showDto) && showDto.getIsShow() == 0) {
            return showDto.getClearNum();
        }
        return 0;
    }

    @GetMapping("/integral_clear/notice")
    @Operation(summary="查询积分清零文案信息", method = "GET")
    public IntegralClearShowResponse getPointsClearMessage(@CurrentUser LoginUser loginUser) {
        IntegralClearShowDto showDto = tbPointsExpiredReminderService.getPointsClearMessage(loginUser.getBrandId(), loginUser.getLbsId(),
                crmVipcodeService.getVipcode(loginUser.getBrandId(), loginUser.getCId()));
        return mapper.map(showDto, IntegralClearShowResponse.class);
    }

    /**
     * 填充其他参数
     *
     * @param list
     * @param queryDto
     * @return
     */
    private PageIntegralDetailResponse fillData(List<TbPointsDetail> list, MemberIntegralAdjustRecordPageQueryDto queryDto, int count, TbMemberAsset tbMemberAsset) {
        List<String> orderNos = list.stream().map(TbPointsDetail::getOrderNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 取出月份
        List<String> months = list.stream().map(TbPointsDetail::getMoon).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 取出字典值
        List<String> dictTypes = list.stream().map(TbPointsDetail::getReasonType).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 商场ID
        List<String> mallIds = list.stream().map(TbPointsDetail::getMallId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 异步查询销售数据
        CompletableFuture<Map<String, TbSalesDetail>> saleMapFuture = tbSalesDetailService.findSalesMap(queryDto.getGroupId(), queryDto.getMallId(), queryDto.getVipcode(), orderNos);
        // 查询积分月汇总信息
        CompletableFuture<List<MemberMonthlyIntegralResponse>> monthIntegralFuture = tbPointsDetailService.getMonthlyIntegral(queryDto.getGroupId(),
                queryDto.getMallId(), queryDto.getVipcode(), months);
        // 查询字典对应的字典名
        CompletableFuture<Map<String, String>> dictNameFuture = sysDictService.findDictNames(dictTypes);
        // 商场名称
        CompletableFuture<Map<String, String>> mallNameFuture = hiveVasService.getMallName(mallIds);
        // 等待多个任务完成
        CompletableFuture.allOf(saleMapFuture, monthIntegralFuture, dictNameFuture, mallNameFuture).join();
        int totalPage = (count % queryDto.getSize() == 0) ? (count / queryDto.getSize()) : (count / queryDto.getSize()) + 1;
        PageIntegralDetailResponse page = PageIntegralDetailResponse.builder().totalPages(totalPage).totalElements(count)
                .currentIntegral(tbMemberAsset.getCurrentPoints()).build();
        try {
            Map<String, TbSalesDetail> saleMap = saleMapFuture.get();
            Map<String, String> dictNameMap = dictNameFuture.get();
            Map<String, String> mallNameMap = mallNameFuture.get();
            Map<String, List<MemberIntegralAdjustRecordResponse>> detailMap = list.stream().map(it -> {
                MemberIntegralAdjustRecordResponse response = this.convertToResponse(it);
                Optional.ofNullable(MapUtils.getObject(saleMap, it.getOrderNo())).ifPresent(sale -> {
                    response.setUseMoney(sale.getPayAmount());
                    response.setShopId(sale.getShopNo());
                    response.setShopName(sale.getShopName());
                });
                if (Objects.isNull(response.getUseMoney())) {
                    response.setUseMoney(BigDecimal.ZERO);
                }
                // 设置字典名称
                Optional.ofNullable(MapUtils.getString(dictNameMap, it.getRemark())).ifPresent(response::setRemarkName);
                // 设置商场名称
                Optional.ofNullable(MapUtils.getString(mallNameMap, it.getMallId())).ifPresent(response::setMallName);
                return response;
            }).collect(Collectors.groupingBy(MemberIntegralAdjustRecordResponse::getMoon));
            List<MemberMonthlyIntegralResponse> responses = monthIntegralFuture.get();
            responses.forEach(it -> Optional.ofNullable(MapUtils.getObject(detailMap, it.getMonth())).ifPresent(it::setDetails));
            page.setContent(responses);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return page;
    }

    /**
     * TbPointsDetail convert to TbMemberIntegralAdjustResponse 对象转换
     * @param detail
     * @return
     */
    private MemberIntegralAdjustRecordResponse convertToResponse(TbPointsDetail detail) {
        return MemberIntegralAdjustRecordResponse.builder().id(detail.getCrmId()).groupId(detail.getGroupId()).mallId(detail.getMallId()).type(detail.getType())
                .vipcode(detail.getVipcode()).currentIntegral(detail.getCurrentPoints()).number(detail.getAmount()).remark(detail.getReasonType())
                .numberToIntegral(detail.getAmount()).integralDead(detail.getExpireDate())
                .remarkName(detail.getReasonDesc()).sellNo(detail.getOrderNo()).sellDate(detail.getCreateDate()).shopId(detail.getShopNo()).createDate(detail.getCreateDate())
                .moon(detail.getMoon()).build();
    }

    @GetMapping("/inquiry/current")
    @Operation(summary="登录用户获取用户信息", method = "GET")
    public TbMemberAllResponse getMemberCurrent(@CurrentUser LoginUser loginUser, @RequestHeader("brandId") String brandId) {
        return this.getAllResponse(brandId, loginUser.getCId());
    }

    @PostMapping(value = "/inquiry/sign_up")
    @Operation(summary="会员注册，用于第三方会员注册", method = "POST", hidden = true)
    public TbMemberAllResponse memberSignUp(@RequestBody MemberSignUpResource resource) {
        TbMemberAsset tbMember = tbMemberAssetService.memberSignUp(resource);
        if (Objects.isNull(tbMember)) {
            throw BizException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        TbMemberAllResponse response = mapper.map(tbMember, TbMemberAllResponse.class);
        response.setAreaCode(resource.getAreaCode());
        response.setNickName(StringUtils.isNotBlank(resource.getNickName()) ? resource.getNickName() : "会员用户");
        return response;
    }

    @GetMapping("/inquiry/{brandId}/{kipUserId}")
    @Operation(summary="查询会员明细，供第三方查询会员信息，存在敏感信息处理", method = "GET", hidden = true)
    @Parameters({
            @Parameter(name = "brandId", description = "集团ID", required = true),
            @Parameter(name = "kipUserId", description = "Profile用户ID", required = true)
    })
    public TbMemberAllResponse getMemberDetail(@PathVariable("brandId") String brandId, @PathVariable("kipUserId") String kipUserId) {
        return this.getAllResponse(brandId, kipUserId);
    }

    @GetMapping("/inquiry_mobile/{brandId}/{mobile}")
    @Operation(summary="查询会员明细，供第三方查询会员信息，存在敏感信息处理", method = "GET", hidden = true)
    @Parameters({
            @Parameter(name = "brandId", description = "集团ID", required = true),
            @Parameter(name = "mobile", description = "用户手机号", required = true)
    })
    public TbMemberAllResponse getMemberDetailByMobile(@PathVariable("brandId") String brandId, @PathVariable("mobile") String mobile) {
        TbMemberAsset tbMember = tbMemberAssetService.findByMobileAndGroupId(mobile, brandId);
        // 填充会员信息
        return this.fillMemberInfo(tbMember);
    }

    /**
     * 会员信息转化
     * @param groupId
     * @param kipUserId
     * @return
     */
    private TbMemberAllResponse getAllResponse(String groupId, String kipUserId) {
        if (StringUtils.isAnyBlank(groupId, kipUserId)) {
            throw PointBusinessException.error(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR);
        }
        TbMemberAsset tbMember = memberRegisterService.findByGroupIdAndKipUserId(groupId, kipUserId);
        // 填充会员信息
        return this.fillMemberInfo(tbMember);
    }

    private TbMemberAllResponse fillMemberInfo(TbMemberAsset tbMember) {
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMember);
        if (StringUtils.isNotBlank(tbMember.getKipUserId())) {
            Optional.ofNullable(kerryStaffService.findByKipUserId(tbMember.getKipUserId())).ifPresent(tbMember::fillOtherInfo);
        } else {
            // 填充用户信息
            Optional.ofNullable(kerryStaffService.findByMobile(tbMember.getMobile())).ifPresent(tbMember::fillOtherInfo);
        }
        TbMemberAllResponse allResponse = mapper.map(tbMember, TbMemberAllResponse.class);
        if (!mallConfig.isAllowNegativePointsGroup(tbMember.getGroupId()) && tbMember.getCurrentPoints() < 0) {
            allResponse.setCurrentPoints(0);
        } else {
            allResponse.setCurrentPoints(tbMember.getCurrentPoints());
        }
        // 加密手机号
        allResponse.setMobile(DesensitizedUtil.mobilePhone(allResponse.getMobile()));
        return allResponse;
    }

    @GetMapping("/kip_user_id/{brandId}/{kipUserId}")
    @Operation(summary="查询会员信息，内部服务调用", method = "GET")
    @Parameters({
            @Parameter(name = "brandId", description = "集团ID", required = true),
            @Parameter(name = "kipUserId", description = "Profile用户ID", required = true),
            @Parameter(name = "needProfile", description = "是否需要profile信息，0:不需要，null或其他值：需要", required = true)
    })
    public TbMemberWithProfileResponse getMemberByGroupIdAndKipUserId(@PathVariable("brandId") String brandId,
                                                                      @PathVariable("kipUserId") String kipUserId,
                                                                      @RequestParam(value = "needProfile", required = false) Integer needProfile) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(brandId, kipUserId);
        if (Objects.nonNull(needProfile) && needProfile == 0) {
            return this.convertMember(tbMemberAsset);
        } else {
            return this.getResponse(tbMemberAsset);
        }
    }

    @GetMapping("/vip_code/{brandId}/{vipcode}")
    @Operation(summary="查询会员信息，内部服务调用", method = "GET")
    @Parameters({
            @Parameter(name = "brandId", description = "集团ID", required = true),
            @Parameter(name = "vipcode", description = "CRM vipcdoe", required = true),
            @Parameter(name = "needProfile", description = "是否需要profile信息，0:不需要，null或其他值：需要", required = true)
    })
    public TbMemberWithProfileResponse getMemberByGroupIdAndVipcode(@PathVariable("brandId") String brandId,
                                                                    @PathVariable("vipcode") String vipcode,
                                                                    @RequestParam(value = "needProfile", required = false) Integer needProfile) {
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByVipcodeAndGroupId(vipcode, brandId);
        if (Objects.nonNull(needProfile) && needProfile == 0) {
            return this.convertMember(tbMemberAsset);
        } else {
            return this.getResponse(tbMemberAsset);
        }
    }

    @GetMapping("/phone_number/{brandId}/{mobile}")
    @Operation(summary="查询会员信息，内部服务调用", method = "GET")
    @Parameters({
            @Parameter(name = "brandId", description = "集团ID", required = true),
            @Parameter(name = "mobile", description = "用户手机号", required = true),
            @Parameter(name = "needProfile", description = "是否需要profile信息，0:不需要，null或其他值：需要", required = true)
    })
    public TbMemberWithProfileResponse getMemberByGroupIdAndMobile(@PathVariable("brandId") String brandId,
                                                                   @PathVariable("mobile") String mobile,
                                                                   @RequestParam(value = "needProfile", required = false) Integer needProfile) {
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByMobileAndGroupId(mobile, brandId);
        if (Objects.nonNull(needProfile) && needProfile == 0) {
            return this.convertMember(tbMemberAsset);
        } else {
            return this.getResponse(tbMemberAsset);
        }
    }

    @GetMapping("/detail")
    @Operation(summary="通过会员ID查询会员全部信息，内部服务调用", method = "GET")
    public TbMemberWithProfileResponse getMemberById(@RequestParam("memberId") String memberId) {
        TbMemberAsset tbMemberAsset = tbMemberAssetService.getMemberById(memberId);
        TbMemberWithProfileResponse response = this.getResponse(tbMemberAsset);
        if (StringUtils.isNotBlank(response.getAuthorizedMallId())) {
            Optional.ofNullable(hiveVasService.getLbsName(response.getAuthorizedMallId())).ifPresent(item -> response.setAuthorizedMallName(item.getLbs().getName()));
        }
        return response;
    }

    /**
     * 公共处理逻辑
     *
     * @param tbMemberAsset
     * @return
     */
    private TbMemberWithProfileResponse getResponse(TbMemberAsset tbMemberAsset) {
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        // 填充用户信息
        if (StringUtils.isNotBlank(tbMemberAsset.getKipUserId())) {
            Optional.ofNullable(kerryStaffService.findByKipUserId(tbMemberAsset.getKipUserId())).ifPresent(tbMemberAsset::fillOtherInfo);
        } else {
            Optional.ofNullable(kerryStaffService.findByMobile(tbMemberAsset.getMobile())).ifPresent(tbMemberAsset::fillOtherInfo);
        }
        return this.convertMember(tbMemberAsset);
    }

    /**
     * 对象转换
     * @param tbMemberAsset
     * @return
     */
    private TbMemberWithProfileResponse convertMember(TbMemberAsset tbMemberAsset) {
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        // 确定哪些group支持负积分逻辑
        if (!mallConfig.isAllowNegativePointsGroup(tbMemberAsset.getGroupId()) && tbMemberAsset.getCurrentPoints() < 0) {
            tbMemberAsset.setCurrentPoints(0);
        }
        TbMemberWithProfileResponse res = mapper.map(tbMemberAsset, TbMemberWithProfileResponse.class);
        res.setCurrentIntegral(tbMemberAsset.getCurrentPoints());
        res.setWxopenmarket(tbMemberAsset.getWxOpenMarket());
        return res;
    }

    @PutMapping("/update_register")
    @Operation(summary="修改会员注册来源接口", method = "PUT")
    public ResultVO updateRegister(@CurrentUser LoginUser loginUser, @RequestBody MemberRegisterSourceResource resource) {
        MemberRegisterResourceDto dto = mapper.map(resource, MemberRegisterResourceDto.class);
        dto.setGroupId(loginUser.getBrandId());
        dto.setMallId(loginUser.getLbsId());
        dto.setKipUserId(loginUser.getCId());
        rabbitMqService.sendLazyMessage(RabbitMqConstant.XCRM_REGISTER_SOURCE, JsonUtils.objToString(dto), 5);
        return ResultVO.success(Boolean.TRUE);
    }

    /**
     * KIP同步会员信息
     *
     * @param resource
     */
    @Hidden
    @PostMapping("/kip_sync_register")
    public void kipSyncRegister(@RequestBody KipSyncMemberResource resource) {
        rabbitMqService.sendMessage(RabbitMqConstant.KIP_SYNC_MEMBER_CRM, JsonUtils.objToString(resource));
    }

    /**
     * KIP修改用户手机号，同步修改tb_member表
     *
     * @param kipUserId
     * @param mobile
     */
    @Hidden
    @GetMapping("/user_mobile/update")
    public void profileMobileModify(@RequestParam("kipUserId") String kipUserId, @RequestParam("mobile") String mobile) {
        tbMemberAssetService.profileMobileModify(kipUserId, mobile);
    }

    /**
     * KIP账号注销
     *
     * @param kipUserId
     */
    @Hidden
    @DeleteMapping("/kip_user_invalid")
    public void kipUserInvalid(@RequestParam("kipUserId") String kipUserId, @RequestParam(value = "mobile", required = false) String mobile) {
        rabbitMqService.sendMessage(RabbitMqConstant.KIP_USER_INVALID_CRM, JsonUtils.objToString(KipUserInvalidDto.builder().kipUserId(kipUserId).mobile(mobile).build()));
    }

    /**
     * CRM账号注销
     *
     * @param resource
     */
    @Hidden
    @DeleteMapping(value = "/brand_member_invalid")
    public void brandMemberDelete(@RequestBody BrandMemberDeleteResource resource) {
        tbMemberAssetService.removeGroupMemberAccount(resource);
    }

    @GetMapping("/profile/kip_user_list")
    @Operation(summary="通过多个kipUserId查询会员profile信息，仅供内部调用", method = "GET")
    public List<CustomerUserResponse> getMemberProfileInfos(@RequestParam("kipUserIds") List<String> kipUserIds) {
        List<CustomerUserDto> dtoList = kerryStaffService.findByKipUserIds(kipUserIds);
        return CollectionUtils.isEmpty(dtoList) ? Collections.emptyList() : dtoList.stream().map(it -> mapper.map(it, CustomerUserResponse.class)).collect(Collectors.toList());
    }

    @GetMapping("/profile/vipcode_list")
    @Operation(summary="通过多个vipcode查询会员profile及crm会员信息，仅供内部调用", method = "GET")
    public List<TbMemberWithProfileResponse> getMemberProfileInfos(@RequestParam("vipcodes") List<String> vipcodes, @RequestParam("groupId") String groupId) {
        List<TbMemberAsset> members = tbMemberAssetService.findByVipcodesAndGroupId(vipcodes, groupId);
        return this.fillProfileInfo(members);
    }

    /**
     * 通过vipcode批量查询用户信息
     *
     * @param members
     * @return
     */
    private List<TbMemberWithProfileResponse> fillProfileInfo(List<TbMemberAsset> members) {
        if (CollectionUtils.isEmpty(members)) {
            return Collections.emptyList();
        }
        List<String> kipUserIds = members.stream().map(TbMemberAsset::getKipUserId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, CustomerUserDto> userMap = kerryStaffService.getMapByKipUserIds(kipUserIds);
        members.forEach(it -> Optional.ofNullable(MapUtils.getObject(userMap, it.getKipUserId())).ifPresent(it::fillOtherInfo));
        return members.stream().map(it -> {
            TbMemberWithProfileResponse response = this.getResponse(it);
            if (!mallConfig.isAllowNegativePointsGroup(response.getGroupId()) && Objects.nonNull(response.getCurrentIntegral()) && response.getCurrentIntegral() < 0) {
                response.setCurrentIntegral(0);
            }
            return response;
        }).collect(Collectors.toList());
    }

    @PostMapping("/getMemberInfo")
    @Operation(summary="查询会员信息", method = "POST")
    public TbMemberWithProfileResponse findAllMemberInfo(@RequestBody @Valid SingleMemberQueryResource resource) {
        if (StringUtils.isBlank(resource.getGroupId()) || StringUtils.isAllBlank(resource.getVipcode(), resource.getMobile(), resource.getKipUserId())) {
            throw BizException.error(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR);
        }
        SingleMemberQueryDto queryDto = mapper.map(resource, SingleMemberQueryDto.class);
        TbMemberAsset tbMember = tbMemberAssetService.findByDto(queryDto);
        if (Objects.isNull(tbMember)) {
            throw PointBusinessException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        TbMemberWithProfileResponse response = this.getResponse(tbMember);
        Optional.ofNullable(kerryStaffService.getByAreaCodeDetail(response.getAreaCode()))
                .ifPresent(item -> response.setAreaCodeName(item.getZhName()));
        // 区分KO Big Group
        if (mallConfig.isKoBigGroup(resource.getGroupId())) {
            List<HiveLbsInfoDto> lbsList = hiveVasService.getLbsList(mallConfig.getKoBigGroup(), IntegralConstant.LBS_RETAIL);
            if (CollectionUtils.isNotEmpty(lbsList)) {
                Map<String, String> lbsNameMap = lbsList.stream().map(HiveLbsInfoDto::getLbs).collect(Collectors.toMap(LbsItemDto::getId, LbsItemDto::getName, (v1, v2) -> v1));
                Optional.ofNullable(MapUtils.getString(lbsNameMap, tbMember.getMallId())).ifPresent(response::setMallName);
                Optional.ofNullable(MapUtils.getString(lbsNameMap, tbMember.getAuthorizedMallId())).ifPresent(response::setAuthorizedMallName);
            }
        } else {
            // 补齐商场名称
            Optional.ofNullable(hiveVasService.getLbsName(tbMember.getMallId())).ifPresent(mall -> response.setMallName(mall.getLbs().getName()));
        }
        return response;
    }



    @Hidden
    @GetMapping("/revert-member")
    public String revertMember(@RequestParam("id") String id) {
        tbMemberAssetService.revertMemberById(id);
        return "执行成功.";
    }

    @Hidden
    @GetMapping("/transfer-member-data")
    public void transferMemberData(@RequestParam("groupId") String groupId, @RequestParam("fromMobile") String fromMobile, @RequestParam("toMobile") String toMobile) {
        TbMemberAsset fromMember = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(groupId).mobile(fromMobile).build());
        if (Objects.isNull(fromMember)) {
            return;
        }
        TbMemberAsset toMember = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(groupId).mobile(toMobile).build());
        if (Objects.isNull(toMember)) {
            return;
        }
        // 变更销售
        List<TbSalesDetail> saleList = tbSalesDetailService.getMemberSaleList(fromMember.getGroupId(), fromMember.getVipcode());
        if (CollectionUtils.isNotEmpty(saleList)) {
            for (TbSalesDetail detail: saleList) {
                detail.setVipcode(toMember.getVipcode());
                detail.setKipUserId(toMember.getKipUserId());
                tbSalesDetailService.updateById(detail);
            }
        }
        // 变更积分
        List<TbPointsDetail> pointsList = tbPointsDetailService.getPointsList(fromMember.getGroupId(), fromMember.getVipcode());
        if (CollectionUtils.isNotEmpty(pointsList)) {
            for (TbPointsDetail detail: pointsList) {
                detail.setMobile(toMember.getMobile());
                detail.setVipcode(toMember.getVipcode());
                tbPointsDetailService.updateById(detail);
            }
        }
        // 变更授权记录
        TbInsensatePointsAuthRecord authRecord = tbInsensatePointsAuthRecordService.getByKipUserIdAndMallIdAndOrigin(fromMember.getKipUserId(), fromMember.getMallId(), InsensateOriginEnum.ALIPAY.getValue());
        if (Objects.nonNull(authRecord)) {
            authRecord.setKipUserId(toMember.getKipUserId());
            tbInsensatePointsAuthRecordService.updateById(authRecord);
        }
        // 会员等级变更记录
        List<TbMemberGradeChangeDetail> detailList = tbMemberGradeChangeDetailService.getChangeDetailList(fromMember.getGroupId(), fromMember.getVipcode());
        if (CollectionUtils.isNotEmpty(detailList)) {
            for (TbMemberGradeChangeDetail detail: detailList) {
                detail.setMobile(toMember.getMobile());
                detail.setVipcode(toMember.getVipcode());
                tbMemberGradeChangeDetailService.updateById(detail);
            }
        }
    }

}
