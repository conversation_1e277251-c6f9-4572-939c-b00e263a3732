package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbPointsInterceptConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/09/2022 15:32
 **********************************************************************************************************************/
public interface TbPointsInterceptConfigMapper extends BaseMapper<TbPointsInterceptConfig> {

    /**
     * fetch data by rule id
     *
     * @param mallId mallId
     * @return TbPointsInterceptConfig list
     */
    List<TbPointsInterceptConfig> findByMallId(@Param("mallId") String mallId);

}
