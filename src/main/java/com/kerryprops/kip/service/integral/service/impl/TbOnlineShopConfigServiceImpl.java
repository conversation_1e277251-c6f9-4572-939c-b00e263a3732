package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig;
import com.kerryprops.kip.service.integral.mapper.TbOnlineShopConfigMapper;
import com.kerryprops.kip.service.integral.service.TbOnlineShopConfigService;
import com.kerryprops.kip.service.integral.webservice.resource.TbOnlineShopResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 16:08
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbOnlineShopConfigServiceImpl extends ServiceImpl<TbOnlineShopConfigMapper, TbOnlineShopConfig> implements TbOnlineShopConfigService {

    private final TbOnlineShopConfigMapper tbOnlineShopConfigMapper;

    @Override
    public int onlineShopTotal(TbOnlineShopResource resource) {
        return tbOnlineShopConfigMapper.onlineShopTotal(resource);
    }

    @Override
    public List<TbOnlineShopConfig> onlineShopPage(TbOnlineShopResource resource) {
        resource.setOffset(resource.getPage() * resource.getSize());
        return tbOnlineShopConfigMapper.onlineShopPage(resource);
    }

    @Override
    public void saveOrUpdateOnlineShopConfig(List<TbOnlineShopConfig> list) {
        // 无论是新增还是修改，同意先跟据商场和业态删除掉相关数据，然后再新增
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        TbOnlineShopConfig config = list.get(0);
        TbOnlineShopConfig param = TbOnlineShopConfig.builder().mallId(config.getMallId()).businessType(config.getBusinessType()).build();
        // 根据商场名称和业态名称删除相关数据
        tbOnlineShopConfigMapper.deleteByParam(param);
        tbOnlineShopConfigMapper.insertOnlineShopBatch(list);
    }

    @Override
    public void removeOnlineShopConfig(Long[] ids) {
        List<Long> idList = new ArrayList<>();
        for (Long id : ids) {
            idList.add(id);
        }
        tbOnlineShopConfigMapper.deleteBatchIds(idList);
    }

    @Override
    public List<TbOnlineShopConfig> getOnlineShop(TbOnlineShopConfig configParam) {
        return tbOnlineShopConfigMapper.getOnlineShop(configParam);
    }

    @Override
    public TbOnlineShopConfig findByMallIdAndGradeAndBusinessType(String mallId, String grade, String businessType) {
        return tbOnlineShopConfigMapper.findByMallIdAndGradeAndBusinessType(mallId, grade, businessType);
    }

}
