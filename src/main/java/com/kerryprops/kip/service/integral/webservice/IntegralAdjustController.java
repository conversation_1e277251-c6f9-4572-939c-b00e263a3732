package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.CommonResponse;
import com.kerryprops.kip.service.integral.webservice.resource.IntegralAdjustResource;
import com.kerryprops.kip.service.integral.webservice.response.IntegralAdjustResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/26/2023 16:38
 **********************************************************************************************************************/

@Slf4j
@RequestMapping("/activity/integralAdjust")
@RestController
@RequiredArgsConstructor
@Tag(name = "CRM Admin会员信息")
public class IntegralAdjustController extends BaseMemberStatus {

    private final TbPointsDetailService tbPointsDetailService;
    private final MemberSalePointsProcessService memberSalePointsProcessService;
    private final TbSalesDetailService tbSalesDetailService;
    private final TbSetshoprateService tbSetshoprateService;
    private final TbMemberAssetService tbMemberAssetService;
    private final SysDictService sysDictService;
    private final RedisService redisService;

    /**
     * 小票积分,销售积分
     */
    private static final List<String> CRM_POINTS_REMARKS = Arrays.asList(IntegralConstant.TAKE_PHOTO, IntegralConstant.SALE_INTEGRAL);

    @GetMapping("/integralRecordList")
    @Operation(summary="会员管理-会员信息-积分记录页", method = "GET")
    public Page<IntegralAdjustResponse> integralRecordList(@RequestParam("vipCode") String vipCode, @RequestParam("mallId") String mallId, @RequestParam("page") int page, @RequestParam("size") int size) {
        int total = tbPointsDetailService.integralRecordTotal(vipCode,mallId);
        if (total <= 0) {
            return Page.of(page, size, total);
        }
        List<TbPointsDetail> list = tbPointsDetailService.integralRecordList(vipCode, mallId, page, size);
        // 需要从tb_member_sale查询到销售记录，将ShopName，shopId，useMoney补齐
        Page<IntegralAdjustResponse> pageData = Page.of(page, size, total);
        pageData.setRecords(this.getDataList(list, vipCode, mallId));
        return pageData;
    }


    /**
     * 类型转化
     * @param list 积分记录
     * @param vipCode 会员编号
     * @param mallId 商场id
     */
    private List<IntegralAdjustResponse> getDataList(List<TbPointsDetail> list, String vipCode, String mallId) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<IntegralAdjustResponse> result = new ArrayList<>();
        List<String> orderNos = list.stream().map(TbPointsDetail::getOrderNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, TbSalesDetail> map = tbSalesDetailService.getSalesList("", mallId, vipCode, orderNos);
        TbSalesDetail salesDetail;
        for (TbPointsDetail detail : list) {
            IntegralAdjustResponse res = this.convertToResponse(detail);
            salesDetail = MapUtils.getObject(map, detail.getOrderNo());
            if (Objects.nonNull(salesDetail)) {
                res.setShopName(salesDetail.getShopName());
                res.setShopNo(salesDetail.getShopNo());
                res.setUseMoney(salesDetail.getPayAmount());
            }
            result.add(res);
        }
        return result;
    }

    /**
     * 对象转化
     * @param detail
     * @return
     */
    private IntegralAdjustResponse convertToResponse(TbPointsDetail detail) {
        return IntegralAdjustResponse.builder().id(detail.getCrmId()).groupId(detail.getGroupId()).mallId(detail.getMallId()).type(detail.getType()).remark(detail.getReasonType())
                .sellNo(detail.getOrderNo()).shopNo(detail.getShopNo()).cashNo(detail.getPosNo()).imageUrl(detail.getImageUrl()).creator(detail.getCreateUser()).build();
    }

    @PostMapping("/integralAdjustSave")
    @Operation(summary="会员管理-会员信息-销售录入", method = "POST")
    public CommonResponse integralAdjustSave(@CurrentUser LoginUser loginUser, @RequestBody @Validated IntegralAdjustResource resource) {
        // 查询会员信息
        TbMemberAsset tbMemberAsset = tbMemberAssetService.getMemberAllInfoById(resource.getId());
        if (Objects.isNull(tbMemberAsset)) {
            log.warn("销售录入查询会员信息为空: id={}", resource.getId());
            throw BizNotFoundException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder()
                .groupId(tbMemberAsset.getGroupId())
                // 代表crm，这是固定的
                .saleType(SaleTypeEnum.CRM.getValue())
                .shopId(resource.getShopNo())
                .mallId(resource.getMallId())
                .saleNo(StringUtils.trim(resource.getSellNo()))
                .saleDate(resource.getSellDate())
                .remark(resource.getRemark())
                .imageUrl(resource.getImageUrl())
                .vipcode(tbMemberAsset.getVipcode())
                .discountAmount(Objects.isNull(resource.getDiscounts()) ? "0" : resource.getDiscounts().toString())
                .amount(Objects.isNull(resource.getUseMoney()) ? "0" : resource.getUseMoney().toString())
                .totalAmount(NumberUtil.add(resource.getDiscounts(), resource.getUseMoney()).toString())
                .createUser(resource.getCreator())
                .approved(resource.getApproved())
                .member(tbMemberAsset)
                // 添加收银机号
                .posNo(resource.getCashNo())
                .refundSaleId(resource.getSaleId())
                // IPAD/S
                .fromType(loginUser.getFromType())
                .build();
        // 补充remarkName
        if (StringUtils.isNotBlank(pointsDto.getRemark()) && StringUtils.isBlank(pointsDto.getRemarkName())) {
            Optional.ofNullable(sysDictService.findByDictType(pointsDto.getRemark())).ifPresent(dict -> pointsDto.setRemarkName(dict.getDictName()));
        }
        String lockKey = String.format(RedisCacheKey.INTEGRAL_ADJUST_LOCK, resource.getRemark(), resource.getMallId(), resource.getSellNo());
        // 销售积分 小票积分
        if (CRM_POINTS_REMARKS.contains(resource.getRemark())) {
            pointsDto.setSaleType(StringUtils.equals(IntegralConstant.TAKE_PHOTO, resource.getRemark()) ? SaleTypeEnum.TICKET.getValue() : SaleTypeEnum.CRM.getValue());
            if (!Boolean.TRUE.equals(redisService.setSaleNoIfAbsent(lockKey, IntegralConstant.KIP_POINTS))) {
                throw BizException.error(PointsEnum.REPEAT_SALES_DATA_ERROR);
            }
            try {
                memberSalePointsProcessService.salePointsProcess(pointsDto);
            } catch (Exception e) {
                e.printStackTrace();
                // 判断是否重复积分
                if (Objects.nonNull(pointsDto.getMemberSale())) {
                    this.repeatIntegralHandler(pointsDto, tbMemberAsset);
                } else {
                    throw e;
                }
            } finally {
                redisService.delKeys(Collections.singletonList(lockKey));
            }
        } else if (StringUtils.equals(IntegralConstant.ORDER_REFUND, resource.getRemark())) {
            if (!Boolean.TRUE.equals(redisService.setSaleNoIfAbsent(lockKey, IntegralConstant.KIP_POINTS))) {
                throw BizException.error(PointsEnum.REPEAT_SALES_DATA_ERROR);
            }
            if (StringUtils.isBlank(resource.getRefundMoney())) {
                throw BizException.error(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR);
            }
            pointsDto.setRefund(true);
            pointsDto.setRefundAmount(resource.getRefundMoney());
            try {
                memberSalePointsProcessService.refundSalePointsProcess(pointsDto);
            } catch (Exception e) {
                throw e;
            } finally {
                redisService.delKeys(Collections.singletonList(lockKey));
            }
        } else if (StringUtils.equals(IntegralConstant.NO_ORDER_REFUND, resource.getRemark())){
            // 无单退货
            // 根据会员信息，店铺信息，业态信息，及销售金额获取基础积分
            Integer integral = tbSetshoprateService.salesToIntegral(pointsDto);
            pointsDto.setRefund(true);
            pointsDto.setRefundAmount(resource.getUseMoney().toString());
            pointsDto.setExchangePoints(-integral);
            memberSalePointsProcessService.refundSalePointsProcess(pointsDto);
        } else {
            throw BizException.error(PointsEnum.SALE_REMARK_NOT_FOUND);
        }
        return CommonResponse.builder().success(Boolean.TRUE).build();
    }

    /**
     * 处理销售录入重复积分异常
     * @param pointsDto 积分对象
     * @param member 会员对象
     */
    private void repeatIntegralHandler(SalesAutoPointsDto pointsDto, TbMemberAsset member) {
        TbSalesDetail detail = pointsDto.getMemberSale();
        TbMemberAsset saleMember = tbMemberAssetService.findByVipcodeAndGroupId(detail.getVipcode(), detail.getGroupId());
        // 针对无法根据销售单获取到会员信息或者手机号的情况，统一处理
        if (Objects.isNull(saleMember) || StringUtils.isEmpty(saleMember.getMobile())) {
            throw BizException.error(PointsEnum.INTEGRAL_BY_OTHERS_ERROR.getCode(), "已被其他会员积分,请核对后重试");
        }
        // 根据step1，2，3判断
        if (pointsDto.getValidateStep() == IntegralConstant.CHECK_STEP_1) {
            throw BizException.error(PointsEnum.INTEGRAL_BY_OTHERS_ERROR.getCode(), String.format("已被会员%s积分,如有疑问您可咨询服务台.",
                    DesensitizedUtil.mobilePhone(saleMember.getMobile())));
        } else if (pointsDto.getValidateStep() == IntegralConstant.CHECK_STEP_3){
            String saleType = detail.getSaleType();
            if (StringUtils.equals(member.getMobile(), saleMember.getMobile())) {
                throw BizException.error(PointsEnum.INTEGRAL_BY_SELF_ERROR.getCode(), String.format("您已在%s快速积分，请勿重复积分", SaleTypeEnum.getDescByVale(saleType)));
            }
            throw BizException.error(PointsEnum.INTEGRAL_BY_OTHERS_ERROR.getCode(), String.format("该单号已被会员%s在%s快速积分,请核对后重试",
                    DesensitizedUtil.mobilePhone(saleMember.getMobile()), SaleTypeEnum.getDescByVale(saleType)));
        }
    }

}
