package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbBkcSaleMember;
import org.apache.ibatis.annotations.Param;

/***********************************************************************************************************************
 * Project - loyalty-engine-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/08/2023 16:54
 **********************************************************************************************************************/
public interface TbBkcSaleMemberMapper extends BaseMapper<TbBkcSaleMember> {

    TbBkcSaleMember findByVipcode(@Param("vipcode") String vipcode, @Param("groupId") String groupId);

    int saveOrUpdate(TbBkcSaleMember member);

}
