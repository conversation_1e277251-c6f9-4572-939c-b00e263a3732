package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.service.integral.common.ResultVO;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.service.GagSmartPosService;
import com.kerryprops.kip.service.integral.webservice.resource.GagQrCodeQueryResource;
import com.kerryprops.kip.service.integral.webservice.resource.GagScanCodeAchievePointsResource;
import com.kerryprops.kip.service.integral.webservice.response.IpadGagQrCodeResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @ClassName BonusselfController
 * @Description BonusselfController
 * @date 2022/10/25 10:20
 * @Version 1.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/qr-code")
@Tag(name = "C端会员及IPAD端扫码积分API")
public class GagSmartPosController {

    private final GagSmartPosService gagSmartPosService;

    @GetMapping("/auto-points")
    @Operation(summary="销采二维码扫码自动积分接口", method = "GET")
    public Integer businessAndSaleIntegral(@CurrentUser LoginUser loginUser, @RequestParam("billFileName") String billFileName) {
        if (StringUtils.isAnyBlank(loginUser.getBrandId(), loginUser.getLbsId(), billFileName)) {
            log.error("参数缺失，传入的参数: billFileName【{}】", billFileName);
        }
        // 二维码自助积分
        return gagSmartPosService.smartPosAutoPoints(loginUser, billFileName);
    }

    @PostMapping("/sales_detail")
    @Operation(summary="IPAD扫码展示销售明细", method = "POST")
    public ResultVO<IpadGagQrCodeResponse> ipadScanQrCode(@RequestBody @Valid GagQrCodeQueryResource resource) {
        return gagSmartPosService.qrcodeDetail(resource.getBillFileName(), resource.getMallId());
    }

    @PostMapping("/achieve_points")
    @Operation(summary="IPAD二维码销售明细实现积分", method = "POST")
    public ResultVO<Integer> scanCodeAchievePoints(@RequestBody @Valid GagScanCodeAchievePointsResource resource, @CurrentUser LoginUser loginUser) {
        return gagSmartPosService.scanCodeAchievePoints(resource, loginUser.getNickName());
    }

}
