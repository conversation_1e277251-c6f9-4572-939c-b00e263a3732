package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_activity_promotion_join_vip
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_activity_promotion_join_vip")
public class TbActivityPromotionJoinvip implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 规则id
     */
    private String promotionId;

    /**
     * 活动组id
     */
    private String promotionConditionGroupId;

    /**
     * 活动组实体id
     */
    private String promotionConditionId;

    /**
     * 会员等级
     */
    private String memberGrade;

    /**
     * 入会时间类型（0、当日；1、次日起）
     */
    private Integer joinTimeType;

    /**
     * 入会多少天内
     */
    private Integer joinTime;

    /**
     * 消费规则条件（0、所有消费；1、完成）
     */
    private Integer saleRuleType;

    /**
     * 消费多少次后
     */
    private Integer saleNum;

    /**
     * 前多少次消费
     */
    private Integer formerNum;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人
     */
    private Long updater;

    /**
     * 更新时间
     */
    private Date updateDate;

}