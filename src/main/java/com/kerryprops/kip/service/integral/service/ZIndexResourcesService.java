package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.ZindexResources;
import com.kerryprops.kip.service.integral.webservice.response.SelfDefiningContextResource;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @description 自定义页面资源表服务层
 * <AUTHOR>
 * @date 2022-12-15
 */
public interface ZIndexResourcesService extends IService<ZindexResources> {

    /**
     * 查询品牌导览列表
     * @param mallId
     * @return
     */
    CompletableFuture<List<SelfDefiningContextResource>> getBrandLogoByMallId(String mallId);
}