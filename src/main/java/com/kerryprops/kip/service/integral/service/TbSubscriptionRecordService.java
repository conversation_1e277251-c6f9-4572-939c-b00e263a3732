package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.SubscriptionRecord;
import com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto;

import java.util.List;

/***********************************************************************************************************************
 * Project - member-points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 05/11/2023 13:58
 **********************************************************************************************************************/
public interface TbSubscriptionRecordService extends IService<SubscriptionRecord> {

    /**
     * querySubscriptionDto
     * @param build build
     * @return SubscriptionRecord dto
     */
    SubscriptionRecord querySubscriptionDto(SubscriptionRecordQueryDto build);

    /**
     * querySubscriptionDto
     * @param build build
     * @return SubscriptionRecord dto
     */
    List<SubscriptionRecord> querySubscriptionList(SubscriptionRecordQueryDto build);

    /**
     * querySubscriptionUserIdList
     * @param build build
     * @return SubscriptionRecord dto
     */
    List<String> querySubscriptionUserIdList(SubscriptionRecordQueryDto build);
}
