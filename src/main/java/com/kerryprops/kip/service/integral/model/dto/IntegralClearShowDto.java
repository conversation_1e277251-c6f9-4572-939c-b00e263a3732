package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/09/2022 10:54
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntegralClearShowDto implements Serializable {

    /**
     * 是否展示(0是 1否)
     */
    private Integer isShow;

    /**
     * 展示文案
     */
    private String content;

    /**
     * 积分清零数
     */
    private Integer clearNum;

}
