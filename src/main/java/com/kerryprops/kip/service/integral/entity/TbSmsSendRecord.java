package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_sms_send_record
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_sms_send_record")
public class TbSmsSendRecord implements Serializable {
    /**
     * 短信发送记录id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 商场编号
     */
    @TableField("mallid")
    private String mallId;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 短信模板编码
     */
    @TableField("template_id")
    private Long templateId;

    /**
     * 短信内容
     */
    @TableField("template_content")
    private String templateContent;

    /**
     * 发送日期
     */
    @TableField("send_time")
    private Date sendTime;

    /**
     * 发送状态(0:发送成功,1:发送失败)
     */
    @TableField("status")
    private Integer status;

    /**
     * 操作人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 创建者id
     */
    @TableField("creator")
    private Long creator;

    /**
     * 发送失败原因
     */
    @TableField("fail_cause")
    private String failCause;

}