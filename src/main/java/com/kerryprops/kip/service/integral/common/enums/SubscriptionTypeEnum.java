package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum SubscriptionTypeEnum {

    /**
     * 积分拦截
     */
    POINTS_INTERCEPT(0, "积分拦截", "您有待处理的异常积分审核业务！", "/emails/notify.html"),

    /**
     * 拍照积分
     */
    POINTS_PHOTO(1, "拍照积分", "您有待处理的拍照积分审核业务！", "/emails/photo_points.html")
    ;

    private final Integer type;
    private final String desc;
    private final String subject;
    private final String path;

    SubscriptionTypeEnum(Integer type, String desc, String subject, String path) {
        this.type = type;
        this.desc = desc;
        this.subject = subject;
        this.path = path;
    }

    /**
     * 通过字典值获取销售类型
     * @param type type
     * @return subject subject
     */
    public static String getSubject(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        String val = "";
        for (SubscriptionTypeEnum typeEnum : values()) {
            if (Objects.equals(type, typeEnum.getType())) {
                val = typeEnum.getSubject();
                break;
            }
        }
        return val;
    }

    /**
     * 通过字典值获取路径名
     * @param type type
     * @return subject subject
     */
    public static String getPath(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        String val = "";
        for (SubscriptionTypeEnum typeEnum : values()) {
            if (Objects.equals(type, typeEnum.getType())) {
                val = typeEnum.getPath();
                break;
            }
        }
        return val;
    }


}
