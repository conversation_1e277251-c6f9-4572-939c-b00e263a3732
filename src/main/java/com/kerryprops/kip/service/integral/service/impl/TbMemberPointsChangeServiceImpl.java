package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import com.kerryprops.kip.service.integral.model.dto.MemberIdDto;
import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.MemberPointsChangeResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 * (c) Kerry Properties Limited. All rights reserved.
 * <AUTHOR> zhangxiliang
 * Created Date - 03/12/2024 08:52
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbMemberPointsChangeServiceImpl extends BaseMemberStatus implements TbMemberPointsChangeService {

    private final TbMemberAssetService tbMemberAssetService;
    private final TbPointsDetailService tbPointsDetailService;
    private final Mapper mapper;
    private final MallConfig mallConfig;
    private final RabbitMqService rabbitMqService;

    @Override
    @Retryable(retryFor = BizException.class, maxAttempts = 5, backoff = @Backoff(delay=1000L, multiplier=2))
    public int changeMemberPoints(SalesAutoPointsDto dto) {
        TbMemberAsset tbMemberAsset = null;
        if (Objects.nonNull(dto.getMember())) {
            if (StringUtils.isNotBlank(dto.getMember().getKipUserId()) && !"false".equalsIgnoreCase(dto.getMember().getKipUserId())) {
                tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndKipUserId(dto.getGroupId(), dto.getMember().getKipUserId());
            } else {
                tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndMobile(dto.getGroupId(), dto.getMember().getMobile());
            }
        }
        // kipUserId为空的会员，再次通过vipcode查询会员信息
        if (Objects.isNull(tbMemberAsset) && StringUtils.isNotBlank(dto.getVipcode())) {
            tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndVipCode(dto.getGroupId(), dto.getVipcode());
        }
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        log.info("正在更新会员: [{}-{}]，调整积分数: [{}]", tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode(), dto.getExchangePoints());
        // 重新设置TbMember信息
        dto.setMember(tbMemberAsset);
        // 如果计算积分结果小于0
        if (dto.getExchangePoints() < 0 && (tbMemberAsset.getCurrentPoints() + dto.getExchangePoints() < 0)) {
            // 如果是杭州，福州，深圳group下，并且saleType是支付宝或者微信的 正常更新积分即可（允许负分数）
            if (mallConfig.isAllowNegativePointsGroup(dto.getGroupId()) &&
                    Arrays.asList(SaleTypeEnum.ALIPAY.getValue(), SaleTypeEnum.WECHAT.getValue()).contains(dto.getSaleType())) {
                log.info("negativePoints {}-{}-{}", tbMemberAsset.getVipcode(), dto.getExchangePoints(), dto.getSaleNo());
            } else {
                // 不允许负积分，如果计算积分结果小于零则取反
                dto.setExchangePoints(-tbMemberAsset.getCurrentPoints());
                dto.setSalesRemark(dto.getSalesRemark() + ", 实际扣除积分数: " + tbMemberAsset.getCurrentPoints());
            }
        }
        tbMemberAsset.setAdjustIntegralNum(dto.getExchangePoints());
        int count = tbMemberAssetService.updatePoints(tbMemberAsset);
        if (count > 0) {
            // 记录积分变更明细
            tbPointsDetailService.savePointsChangeRecord(dto);
        } else {
            throw BizException.error(PointsEnum.MEMBER_INTEGRAL_UPDATE_ERROR);
        }
        return count;
    }

    private void sendInitPointRedeemMq(String memberId) {
        MemberIdDto dto = MemberIdDto.builder().id(memberId).build();
        rabbitMqService.sendMessage(RabbitMqConstant.INIT_POINTS_REDEEM_DETAIL, JsonUtils.objToString(dto));
    }

    @Override
    @Retryable(retryFor = BizException.class, maxAttempts = 5, backoff = @Backoff(delay=1000L, multiplier=2))
    public String updateMemberPoints(MemberPointsChangeDto dto) {
        if (StringUtils.isBlank(dto.getAdjustPointsId())) {
            dto.setAdjustPointsId(IdUtil.simpleUUID());
        }
        // 积分变更必须是不等于0的数
        if (Objects.nonNull(dto.getChangePointsNum()) && 0 == dto.getChangePointsNum()) {
            log.error("积分变更数为0，请求参数: [{}]", JsonUtils.objToString(dto));
            throw PointBusinessException.error(PointsEnum.ERROR_WITH_ZERO_CHANGE_IN_POINTS);
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndVipCode(dto.getGroupId(), dto.getVipcode());
        // 检查会员是否存在及是否冻结（添加是否判断会员状态）
        if (Objects.nonNull(dto.getCheckMemberStatus()) && 1 == dto.getCheckMemberStatus()) {
            if (Objects.isNull(tbMemberAsset)) {
                throw PointBusinessException.error(PointsEnum.MEMBER_NOT_EXISTS);
            }
        } else {
            this.checkMemberStatus(tbMemberAsset);
        }
        // 是否支持积分扣成负数的判断
        if (IntegralConstant.SUPPORT_NEGATIVE_POINTS_DICT_LIST.stream().anyMatch(item -> StringUtils.equalsIgnoreCase(item, dto.getDictValue()))) {
            log.info("支持扣成负积分的字典值: {}-{}-{}-{}", dto.getGroupId(), dto.getVipcode(), dto.getDictValue(), dto.getChangePointsNum());
        } else {
            // 不作修改，即使是负数积分，加积分肯定不受影响，扣积分根据之前的情况有多少扣多少
            if (dto.getChangePointsNum() < 0 && tbMemberAsset.getCurrentPoints() <= 0) {
                throw PointBusinessException.error(PointsEnum.MEMBER_INTEGRAL_INSUFFICIENT_ERROR);
            }
            // 判断扣减积分数是否充足
            if (dto.getChangePointsNum() < 0 && ((tbMemberAsset.getCurrentPoints() + dto.getChangePointsNum()) < 0)) {
                throw PointBusinessException.error(PointsEnum.MEMBER_INTEGRAL_INSUFFICIENT_ERROR);
            }
        }
        tbMemberAsset.setAdjustIntegralNum(dto.getChangePointsNum());
        int count = tbMemberAssetService.updatePoints(tbMemberAsset);
        if (count > 0) {
            // 记录积分变更明细
            tbPointsDetailService.savePointsAdjustRecord(dto, tbMemberAsset);
        } else {
            throw BizException.error(PointsEnum.MEMBER_INTEGRAL_UPDATE_ERROR);
        }
        return dto.getAdjustPointsId();
    }

    /*@Override
    public void clearKoIntegral(MemberPointsChangeDto changeDto) {
        TbMemberAsset tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndVipCode(changeDto.getGroupId(), changeDto.getVipcode());
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        if (changeDto.getChangePointsNum() < 0 && tbMemberAsset.getCurrentPoints() <= 0) {
            log.info("会员积分为0，不做积分扣减操作: {}", changeDto.getVipcode());
            return;
        }
        // 判断扣减积分数是否充足
        if (changeDto.getChangePointsNum() < 0 && ((tbMemberAsset.getCurrentPoints() + changeDto.getChangePointsNum()) < 0)) {
            tbMemberAsset.setAdjustIntegralNum(-tbMemberAsset.getCurrentPoints());
        } else {
            tbMemberAsset.setAdjustIntegralNum(changeDto.getChangePointsNum());
        }
        int count = tbMemberAssetService.updatePoints(tbMemberAsset);
        if (count > 0) {
            // 记录积分变更明细
            tbPointsDetailService.savePointsAdjustRecordNoMessage(changeDto, tbMemberAsset);
        }
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdjustPoints(List<MemberPointsChangeResource> list) {
        for (MemberPointsChangeResource resource : list) {
            if (StringUtils.isAnyBlank(resource.getGroupId(), resource.getMallId(), resource.getVipcode(),
                    resource.getSaleType(), resource.getDictValue()) || Objects.isNull(resource.getChangePointsNum())) {
                throw PointBusinessException.error(PointsEnum.VALIDATION_FAILED);
            }
            // 0积分直接加一条积分记录
            if (0 == Math.abs(resource.getChangePointsNum())) {
                TbMemberAsset tbMemberAsset = tbMemberAssetService.queryMemberByGroupIdAndVipCode(resource.getGroupId(), resource.getVipcode());
                if (Objects.isNull(tbMemberAsset)) {
                    throw PointBusinessException.error(PointsEnum.MEMBER_NOT_EXISTS);
                }
                SalesAutoPointsDto dto = SalesAutoPointsDto
                        .builder()
                        .groupId(resource.getGroupId())
                        .mallId(resource.getMallId())
                        .member(tbMemberAsset)
                        .integralAdjustId(resource.getAdjustPointsId())
                        .remark(resource.getDictValue())
                        .remarkName(resource.getContent())
                        .exchangePoints(resource.getChangePointsNum())
                        .saleNo(resource.getSaleNo())
                        .shopId(resource.getShopNo())
                        .createUser(resource.getCreator())
                        .build();
                tbPointsDetailService.savePointsChangeRecord(dto);
                continue;
            }
            MemberPointsChangeDto changeDto = mapper.map(resource, MemberPointsChangeDto.class);
            this.updateMemberPoints(changeDto);
        }
    }
}
