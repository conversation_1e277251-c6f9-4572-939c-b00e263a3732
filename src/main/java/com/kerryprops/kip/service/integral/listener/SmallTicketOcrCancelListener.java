package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.model.dto.SmallTicketUploadDto;
import com.kerryprops.kip.service.integral.service.TbPhotoReviewService;
import com.kerryprops.kip.service.integral.webservice.SmallTicketRecognitionController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 08/13/2024 13:42
 **********************************************************************************************************************/

/**
 * OCR上传小票识别成功，C端用户取消自动积分消息监听
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmallTicketOcrCancelListener {

    private final SmallTicketRecognitionController smallTicketRecognitionController;

    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.SMALL_TICKET_OCR_MANUAL_CANCEL}, containerFactory = "crmContainerFactory")
    public void receiveMsg(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("OCR上传小票识别成功，C端用户取消自动积分消息监听消息体为空.");
            return;
        }
        log.info("SmallTicketOcrCancelListener: {}" , msg);
        SmallTicketUploadDto dto = JsonUtils.stringToObj(msg, SmallTicketUploadDto.class);
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getTaskId())) {
            return;
        }
        smallTicketRecognitionController.getTaskDetail(dto.getTaskId());
    }

}
