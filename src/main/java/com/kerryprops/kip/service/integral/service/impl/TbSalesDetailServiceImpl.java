package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.RefundStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.RuleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper;
import com.kerryprops.kip.service.integral.mapper.TbSalesDetailMapper;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.vo.TenantInfoVo;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource;
import com.kerryprops.kip.service.integral.webservice.response.BadgeShopGroupResultResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 03/12/2024 12:01
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbSalesDetailServiceImpl extends ServiceImpl<TbSalesDetailMapper, TbSalesDetail> implements TbSalesDetailService {

    private final TbSalesDetailMapper tbSalesDetailMapper;
    private final TbMemberAssetMapper tbMemberAssetMapper;
    private final ProfileServiceClient profileServiceClient;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbBaseShopService tbBaseShopService;
    private final RabbitMqService rabbitMqService;
    private final HiveVasService hiveVasService;
    private final RedisService redisService;
    private final MallConfig mallConfig;

    @Override
    public void saveMemberSale(TbSalesDetail salesDetail) {
        salesDetail.setDefault();
        tbSalesDetailMapper.insert(salesDetail);
    }

    @Override
    public void saveNoOrderRefundRecord(SalesAutoPointsDto dto) {
        TbMemberAsset member = dto.getMember();
        int oldIntegral = member.getCurrentPoints();
        Date now = new Date();
        // 无单退货退款金额
        BigDecimal refundAmount = new BigDecimal(dto.getRefundAmount());
        //销售表中新增这个订单信息.
        TbSalesDetail detail = TbSalesDetail.builder()
                .crmId(IdUtil.simpleUUID())
                .imageUrl(dto.getImageUrl())
                .groupId(dto.getGroupId())
                .mallId(dto.getMallId())
                .vipcode(dto.getVipcode())
                .kipUserId(member.getKipUserId())
                .shopNo(dto.getShopId())
                // 代表已退款
                .status(1)
                .createUser(dto.getCreateUser())
                .createDate(now)
                .saleDate(DateUtil.parseDateTime(dto.getSaleDate()))
                .updateDate(now)
                .updateUser(dto.getCreateUser())
                .saleType(dto.getSaleType())
                .refundPointsId(dto.getIntegralAdjustId())
                .remark(StringUtils.isBlank(dto.getSalesRemark()) ? "无单退货" : dto.getSalesRemark())
                // 退还积分
                .refundPointsNum(Math.abs(dto.getExchangePoints()))
                // 退款金额
                .refundAmount(refundAmount)
                // 无单退货的金额和有单退货金额记一样的
                .totalAmount(refundAmount)
                .payAmount(refundAmount)
                .pointsId(dto.getIntegralAdjustId())
                .pointsNum(0)
                .orgGrade(member.getGrade())
                .orgPoints(oldIntegral)
                .build();
        if (StringUtils.isBlank(detail.getShopName())) {
            Optional.ofNullable(tbBaseShopService.getByContractNoAndMallId(dto.getShopId(), dto.getMallId())).ifPresent(item -> detail.setShopName(item.getShopName()));
        }
        detail.setDefault();
        // 不能用saveSaleData方法，因为无单退货只保存数据，没有其它业务
        tbSalesDetailMapper.insert(detail);
        log.info("NoOrderRefundProcess - tbMemberSale insert success");
    }

    @Override
    public TbSalesDetail checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto queryDto) {
        return tbSalesDetailMapper.checkSaleRecordRepeatedOrNot(queryDto);
    }

    @Override
    public List<TbSalesDetail> checkSaleRepeatedByConditions(List<MemberSaleRepeatQueryDto> list) {
        return tbSalesDetailMapper.checkSaleRepeatedByConditions(list);
    }

    @Override
    public TbSalesDetail querySaleBySellNo(String orderNo) {
        return tbSalesDetailMapper.queryByOrderNo(orderNo);
    }

    @Override
    public TbSalesDetail queryBySellNoAndSaleType(String orderNo, String saleType) {
        return tbSalesDetailMapper.queryByOrderNoAndSaleType(orderNo, saleType);
    }

    @Override
    public int queryMemberMonthSalesNumber(MemberSaleMonthNumberQueryDto query) {
        return tbSalesDetailMapper.queryMemberMonthSalesNumber(query);
    }

    @Override
    public int queryMemberSaleCountBetweenTime(MemberSaleMonthNumberQueryDto query) {
        return tbSalesDetailMapper.queryMemberSaleCountBetweenTime(query);
    }

    @Override
    public TbSalesDetail selectBySellNoAndMallId(String orderNo, String mallId) {
        return tbSalesDetailMapper.queryByOrderNoAndMallId(orderNo, mallId);
    }

    @Override
    public void saveSaleData(SalesAutoPointsDto dto) {
        if (StringUtils.isBlank(dto.getIntegralAdjustId())) {
            dto.setIntegralAdjustId(IdUtil.simpleUUID());
        }
        Date date = new Date();
        TbSalesDetail sale = new TbSalesDetail();
        sale.setCrmId(dto.getId());
        sale.setGroupId(dto.getGroupId());
        sale.setMallId(dto.getMallId());
        sale.setSaleDate(DateUtil.parse(dto.getSaleDate()));
        sale.setCreateDate(date);
        sale.setUpdateDate(date);
        sale.setCreateUser(dto.getCreateUser());
        sale.setUpdateUser(dto.getCreateUser());
        sale.setVipcode(dto.getMember().getVipcode());
        sale.setKipUserId(dto.getMember().getKipUserId());
        // 销售单状态默认未退款
        sale.setStatus(RefundStatusEnum.PAY.getValue());
        sale.setTotalAmount(StringUtils.isBlank(dto.getTotalAmount()) ? NumberUtil.toBigDecimal(dto.getAmount()) : NumberUtil.toBigDecimal(dto.getTotalAmount()));
        sale.setSaleType(dto.getSaleType());
        sale.setOrderNo(dto.getSaleNo());
        sale.setShopNo(dto.getShopId());
        // 填充店铺业态品牌名称
        Optional.ofNullable(dto.getBaseShop()).ifPresent(shop -> {
            sale.setShopName(StringUtils.isNotBlank(shop.getRetailBrandName()) ? shop.getRetailBrandName() : shop.getShopName());
        });
        sale.setPointsNum(dto.getExchangePoints());
        sale.setPayAmount(NumberUtil.toBigDecimal(dto.getAmount()));
        // 设置积分调整id
        sale.setPointsId(dto.getIntegralAdjustId());
        sale.setRemark(dto.getSalesRemark());
        sale.setOrgGrade(dto.getMember().getGrade());
        sale.setOrgPoints(dto.getMember().getCurrentPoints());
        try {
            // 优惠金额
            sale.setDiscountAmount(NumberUtil.toBigDecimal(dto.getDiscountAmount()));
            // 填充退款金额值
            sale.setRefundAmount(NumberUtil.toBigDecimal(dto.getRefundAmount()));
            // 未退货（退款金额为空或小于等于0）
            if (NumberUtil.toBigDecimal(dto.getRefundAmount()).compareTo(BigDecimal.ZERO) <= 0) {
                sale.setStatus(RefundStatusEnum.PAY.getValue());
            } else {
                // 判断是部分退款还是全额退款
                sale.setStatus(new BigDecimal(dto.getAmount()).compareTo(new BigDecimal(dto.getRefundAmount())) > 0 ? RefundStatusEnum.PARTIAL_REFUND.getValue() : RefundStatusEnum.REFUND.getValue());
            }
        } catch (Exception e) {
            log.error("saveSaleData Error: {}", e);
        }
        // 拍照积分上传图片地址
        sale.setImageUrl(StringUtils.isNotBlank(dto.getImageUrl()) ? dto.getImageUrl() : "");
        // 填充身份信息
        this.fillIdentityParams(sale, dto);
        // 填充默认值
        sale.setDefault();
        // 新增销售记录
        tbSalesDetailMapper.insert(sale);
        // 排除线上商城-积分商城
        if (StringUtils.equals(IntegralConstant.REWARDS_MALL, dto.getRemark())) {
            return;
        }
        // 发MQ消息
        rabbitMqService.sendMqMessage(sale, dto);
    }

    /**
     * 填充身份信息参数
     * @param detail
     * @param dto
     */
    private void fillIdentityParams(TbSalesDetail detail, SalesAutoPointsDto dto) {
        MallItem mallItem = mallConfig.getByMallId(dto.getMallId());
        if (Objects.isNull(mallItem)) {
            return;
        }
        List<CustomerIdentityDto> identityList = null;
        if (Objects.nonNull(dto.getMember()) && StringUtils.isNotBlank(dto.getMember().getKipUserId())) {
            identityList = profileServiceClient.getIdentityResponse(dto.getMember().getKipUserId(), mallItem.getProjectId());
        }
        detail.setExtend1(CollectionUtils.isEmpty(identityList) ? "0" : (identityList.stream().anyMatch(CustomerIdentityDto::isOffice) ? "1" : "0"));
        detail.setExtend2(CollectionUtils.isEmpty(identityList) ? "0" : (identityList.stream().anyMatch(CustomerIdentityDto::isApartment) ? "1" : "0"));
        detail.setExtend3(CollectionUtils.isEmpty(identityList) ? "0" : (identityList.stream().anyMatch(CustomerIdentityDto::isResidence) ? "1" : "0"));
    }

    @Override
    public Double getMemberSaleAmountBetweenTime(TbMemberAsset member, TbMemberGradeRule rule, Date beginDate, Date endDate, List<String> shopNos) {
        Double amount = this.getGradeRuleCacheAmount(rule, member);
        return Objects.nonNull(amount) ? amount : this.getMemberSaleAmountBetweenDate(member, shopNos, beginDate, endDate, rule.getId());
    }

    @Override
    public Double getMemberSaleAmountBetweenDate(TbMemberAsset member, List<String> shopNos, Date beginDate, Date endDate, Long ruleId) {
        MemberSaleAmountQueryDto queryDto = this.getSaleAmountQueryDto(member, shopNos, beginDate, endDate);
        TbSalesDetail salesDetail = tbSalesDetailMapper.getSaleAmountBetweenTime(queryDto);
        BigDecimal moneyConsumed = BigDecimal.ZERO;
        if (Objects.nonNull(salesDetail)) {
            moneyConsumed = salesDetail.getPayAmount().subtract(salesDetail.getRefundAmount()).setScale(2, RoundingMode.HALF_UP);
        }
        String key = String.format(RedisCacheKey.MEMBER_GRADE_RULE_SALE_MONEY, member.getGroupId(), member.getVipcode());
        redisService.hSet(key, ruleId.toString(), moneyConsumed.toString(), 60 * 60 * 4);
        return moneyConsumed.doubleValue();
    }

    /**
     * 获取会员时间段内消费金额查询条件
     * @param member
     * @param shopNos
     * @param beginDate
     * @param endDate
     * @return
     */
    private MemberSaleAmountQueryDto getSaleAmountQueryDto(TbMemberAsset member, List<String> shopNos, Date beginDate, Date endDate) {
        MemberSaleAmountQueryDto queryDto = MemberSaleAmountQueryDto.builder().groupId(member.getGroupId()).vipcode(member.getVipcode()).shopIds(shopNos).build();
        if (Objects.nonNull(beginDate)) {
            queryDto.setStartDate(DateUtil.formatDateTime(beginDate));
        }
        if (Objects.nonNull(endDate)) {
            queryDto.setEndDate(DateUtil.formatDateTime(endDate));
        }
        return queryDto;
    }

    @Override
    public boolean checkMemberSaleAmountWhetherRelegationSucceeded(TbMemberAsset member, TbMemberGradeRule rule, Date beginDate, Date endDate, List<String> shopNos) {
        MemberGradeWhetherRelegationQueryDto queryDto = MemberGradeWhetherRelegationQueryDto.builder().groupBy(rule.getRuleType())
                .groupId(member.getGroupId()).vipcode(member.getVipcode()).build();
        if (Objects.nonNull(beginDate)) {
            queryDto.setStartDate(DateUtil.formatDateTime(beginDate));
        }
        if (Objects.nonNull(endDate)) {
            queryDto.setEndDate(DateUtil.formatDateTime(endDate));
        }
        queryDto.setAmount(Objects.nonNull(rule.getMoney()) ? rule.getMoney().doubleValue() : 0);
        queryDto.setShopIds(shopNos);
        return tbSalesDetailMapper.checkMemberSaleAmountWhetherRelegationSucceeded(queryDto) > 0;
    }

    @Override
    public boolean checkPreviousDailyConsumptionRuleWhetherRelegationSucceeded(TbMemberAsset member, PreviousDailyConsumptionRuleDto ruleDto, Date beginDate, Date endDate, List<String> shopNos) {
        MemberGradeWhetherRelegationQueryDto queryDto = MemberGradeWhetherRelegationQueryDto.builder().groupBy(RuleTypeEnum.DAILY_CONSUME_UPGRADE.getValue())
                .groupId(member.getGroupId()).vipcode(member.getVipcode()).build();
        if (Objects.nonNull(beginDate)) {
            queryDto.setStartDate(DateUtil.formatDateTime(beginDate));
        }
        if (Objects.nonNull(endDate)) {
            queryDto.setEndDate(DateUtil.formatDateTime(endDate));
        }
        queryDto.setAmount(Objects.nonNull(ruleDto.getMoney()) ? ruleDto.getMoney().doubleValue() : 0);
        queryDto.setShopIds(shopNos);
        return tbSalesDetailMapper.checkMemberSaleAmountWhetherRelegationSucceeded(queryDto) > 0;
    }

    @Override
    public void initMemberGradeData(String vipcode) {
        log.info("*************** initMemberGradeData Start *****************");
        List<String> dates = Arrays.asList("2022-11-02", "2022-11-03");
        Map<String, Object> saleMap = null;
        for (String date: dates) {
            for (MallItem item: mallConfig.getList()) {
                List<TbSalesDetail> sales = tbSalesDetailMapper.initMemberGradeData(item.getMallId(), vipcode, String.format("%s 00:00:00", date), String.format("%s 23:59:59", date));
                if (CollectionUtils.isEmpty(sales)) {
                    continue;
                }
                log.info("商场: {}, 日期: {}, 销售数据总数: {}", item.getAbbreviation(), date, sales.size());
                for (TbSalesDetail detail: sales) {
                    saleMap = new HashMap<>(4);
                    saleMap.put(IntegralConstant.SALE_ID, detail.getId());
                    // 用于标记是否发放满赠奖励
                    saleMap.put(IntegralConstant.SEND_FULL_REWARD, vipcode);
                    rabbitMqService.sendFanoutMessage(RabbitMqConstant.SALES_EXCHANGE_FANOUT, JsonUtils.objToString(saleMap), RabbitMqConstant.MEMBER_SALES_TRIGGER_UPGRADE_CHECK);
                }
            }
        }
        log.info("*************** initMemberGradeData End *****************");
    }

    @Override
    public void modifyMemberSaleShopId(String shopNos, String mallId, String newIds) {
        if (StringUtils.isAnyBlank(shopNos, newIds, mallId)) {
            return;
        }
        List<String> oldIds = StrUtil.split(shopNos, CommonSeparators.COMMA_SEPARATOR);
        List<String> replaceIds = StrUtil.split(newIds, CommonSeparators.COMMA_SEPARATOR);
        if (oldIds.size() != replaceIds.size()) {
            log.info("shopIds, newIds size not match");
            return;
        }
        for (int i = 0, j = oldIds.size(); i < j; i++) {
            List<Long> salesIds = tbSalesDetailMapper.getShopMemberSalesIds(mallId, oldIds.get(i));
            if (CollectionUtils.isEmpty(salesIds)) {
                continue;
            }
            log.info("old shop id: {} query sales data: {}", oldIds.get(i), salesIds.size());
            tbSalesDetailMapper.updateMemberSalesShopId(salesIds, replaceIds.get(i));
        }
        log.info("修改销售表内的店铺ID已完成.");
    }

    @Override
    public CompletableFuture<Map<String, TbSalesDetail>> findSalesMap(String groupId, String mallId, String vipcode, List<String> orderNos) {
        return CompletableFuture.completedFuture(getSalesList(groupId, mallId, vipcode, orderNos));
    }

    @Override
    public Map<String, TbSalesDetail> getSalesList(String groupId, String mallId, String vipcode, List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return MapUtil.empty();
        }
        List<TbSalesDetail> sales = tbSalesDetailMapper.findSalesList(groupId, mallId, vipcode, orderNos);
        if (CollectionUtils.isEmpty(sales)) {
            return MapUtil.empty();
        }
        List<String> shopNos = sales.stream().map(TbSalesDetail::getShopNo).filter(StringUtils::isNotBlank).distinct().toList();
        if (CollectionUtils.isEmpty(shopNos)) {
            return sales.stream().collect(Collectors.toMap(TbSalesDetail::getOrderNo, Function.identity(), (k1, k2) -> k2));
        }
        // 通过店铺号查询店铺关联品牌名称
        Map<String, TenantInfoVo> nameMap = hiveVasService.getTenantInfoByShopNos(shopNos);
        sales.forEach(it -> Optional.ofNullable(MapUtils.getObject(nameMap, it.getShopNo())).ifPresent(item -> it.setShopName(item.getRetailBrandName())));
        return sales.stream().collect(Collectors.toMap(TbSalesDetail::getOrderNo, Function.identity(), (k1, k2) -> k2));
    }

    @Override
    public void memberSaleRefundProcess(Long saleId) {
        TbSalesDetail detail = tbSalesDetailMapper.getMemberSaleById(saleId);
        if (Objects.isNull(detail)) {
            log.error("销售记录ID: [{}]对应的记录不存在.", saleId);
            return;
        }
        TbMemberAsset member = tbMemberAssetService.queryMemberByGroupIdAndVipCode(detail.getGroupId(), detail.getVipcode());
        if (Objects.isNull(member)) {
            log.error("销售记录对应的用户不存在: [{}-{}]", detail.getGroupId(), detail.getVipcode());
            return;
        }
        log.info("销售单号: [{}]，退款发送满赠消息通知", detail.getOrderNo());
        rabbitMqService.sendFullActivityReward(detail, SalesAutoPointsDto.builder().member(member).build(), Boolean.TRUE);
    }

    @Override
    public TbSalesDetail getMemberSaleById(Long id) {
        return tbSalesDetailMapper.getMemberSaleById(id);
    }

    @Override
    public List<TbSalesDetail> getSaleListCreateAsc(String groupId, String vipcode, Date beginDate, Date endDate, List<String> shopNos) {
        return tbSalesDetailMapper.getSaleListCreateAsc(groupId, vipcode, beginDate, endDate, shopNos);
    }

    @Override
    public void checkSalesUpgradeGrade(String groupId, String startDate, String endDate) {
        log.info("CheckSalesUpgradeGrade Send MQ Begin: {}", groupId);
        String startTime = String.format("%s 00:00:00", startDate);
        String endTime = String.format("%s 23:59:59", endDate);
        List<String> vipcodes = tbSalesDetailMapper.checkSalesUpgradeGrade(groupId, startTime, endTime);
        if (CollectionUtils.isEmpty(vipcodes)) {
            log.info("CheckSalesUpgradeGrade Send MQ End: {}", groupId);
            return;
        }
        Map<String, Object> saleMap = null;
        for (String vipcode: vipcodes) {
            String saleId = tbSalesDetailMapper.getOneSalesBetweenTime(groupId, vipcode, startTime, endTime);
            if (StringUtils.isBlank(saleId)) {
                continue;
            }
            saleMap = new HashMap<>(4);
            saleMap.put(IntegralConstant.SALE_ID, saleId);
            // 用于标记是否发放满赠奖励
            saleMap.put(IntegralConstant.SEND_FULL_REWARD, vipcode);
            rabbitMqService.sendFanoutMessage(RabbitMqConstant.SALES_EXCHANGE_FANOUT, JsonUtils.objToString(saleMap), RabbitMqConstant.MEMBER_SALES_TRIGGER_UPGRADE_CHECK);
        }
        log.info("CheckSalesUpgradeGrade Send MQ End: {}", groupId);
    }

    @Override
    public Double getGradeRuleCacheAmount(TbMemberGradeRule rule, TbMemberAsset tbMemberAsset) {
        String key = String.format(RedisCacheKey.MEMBER_GRADE_RULE_SALE_MONEY, tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode());
        Object amount = null;
        try {
            amount = redisService.hGet(key, rule.getId() + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Objects.nonNull(amount) ? Double.parseDouble(amount + "") : null;
    }

    @Override
    public List<String> checkSaleIsIntercept(SaleDetailQueryDto queryDto) {
        return tbSalesDetailMapper.querySaleIsIntercept(queryDto);
    }

    @Override
    public List<TbSalesDetail> getMemberSaleList(String groupId, String vipcode) {
        return tbSalesDetailMapper.getMemberSaleList(groupId, vipcode);
    }

    @Override
    public void executeSql(String sql) {
        tbSalesDetailMapper.executeSql(sql);
    }

    @Override
    public List<Map<String, Object>> querySqlResult(String sql) {
        return tbSalesDetailMapper.querySqlResult(sql);
    }

    @Override
    public BigDecimal accumulatedSalesAmount(BadgeSalesResource resource) {
        return tbSalesDetailMapper.accumulatedSalesAmount(resource);
    }

    @Override
    public List<String> consumeShopCount(BadgeSalesResource resource) {
        return tbSalesDetailMapper.consumeShopCount(resource);
    }

    @Override
    public int consumeFrequency(BadgeSalesResource resource) {
        return tbSalesDetailMapper.consumeFrequency(resource);
    }

    @Override
    public List<String> consumeDays(BadgeSalesResource resource) {
        return tbSalesDetailMapper.consumeDays(resource);
    }

    @Override
    public List<BadgeShopGroupResultResponse> badgeShopGroupSalesDetails(List<BadgeSalesResource> list) {
        try {
            return tbSalesDetailMapper.queryBadgeShopGroupDetails(list);
        } catch (Exception e) {
            log.error("徽章查询店铺分组销售信息异常", e);
            List<List<BadgeSalesResource>> groupedList = Lists.partition(list, 3);
            return groupedList.stream().map(item -> tbSalesDetailMapper.queryBadgeShopGroupDetails(item))
                    .flatMap(List::stream).toList();
        }
    }

    @Async
    @Override
    public void gradeChangeTriggerMemberUpgrade(String groupId, String date) {
        List<String> vipcodes = tbSalesDetailMapper.gradeChangeTriggerMemberUpgrade(groupId, date);
        if (CollectionUtils.isEmpty(vipcodes)) {
            return;
        }
        TbMemberAsset tbMemberAsset = null;
        TbSalesDetail detail = null;
        for (String vipcode: vipcodes) {
            tbMemberAsset = tbMemberAssetMapper.queryMemberByGroupIdAndVipcode(groupId, vipcode);
            if (Objects.isNull(tbMemberAsset)) {
                continue;
            }
            detail = tbSalesDetailMapper.getLatestSalesRecord(groupId, vipcode);
            if (Objects.isNull(detail)) {
                continue;
            }
            MemberSaleActivityDto activityDto = MemberSaleActivityDto.builder().memberId(String.valueOf(tbMemberAsset.getId())).mallId(detail.getMallId()).shopId(detail.getShopNo())
                    .sellNo(detail.getOrderNo()).createDate(DateUtil.formatDateTime(detail.getSaleDate())).createUser(detail.getCreateUser()).saleType(detail.getSaleType())
                    .money(detail.getPayAmount()).saleId(String.valueOf(detail.getId())).isRefund(Boolean.FALSE)
                    .fromType("SYS").orgGrade(tbMemberAsset.getGrade()).build();
            // 发送会员升级判断MQ消息
            rabbitMqService.sendMessage(RabbitMqConstant.GROUP_MEMBER_GRADE_CHANGE_TRIGGER_UPGRADE, JsonUtils.objToString(activityDto));
        }
    }
}
