package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeChangeDetailService
 * @Description TbMemberGradeChangeDetailService
 * @date 2022/9/27 15:44
 * @Version 1.0
 */
public interface TbMemberGradeChangeDetailService extends IService<TbMemberGradeChangeDetail> {

    TbMemberGradeChangeDetail queryChangeMaxByGroupIdAndVipcode(String groupId, String vipcode);

    /**
     * 获取会员在给定的overDate之后，最新的一条等级变更记录
     * @param groupId
     * @param vipcode
     * @param overDate
     * @return
     */
    TbMemberGradeChangeDetail getMaxChangeDetailByGroupIdAndVipcodeAndOverDate(String groupId, String vipcode, String overDate);

    List<TbMemberGradeChangeDetail> getChangeDetailList(String groupId, String vipcode);

    /**
     * 补全用户等级变更记录缺少mallId
     * @param groupId
     * @param vipcode
     * @param mallId
     */
    void fillMemberGradeChangeDetailMallId(String groupId, String vipcode, String mallId);

    /**
     * 处理等级变更时间为2023年的数据
     * @param groupId
     * @param date
     */
    void reduceMemberGradeChangeDate(String groupId, String date);

    /**
     * 保存等级变更明细
     * @param changeDetail
     */
    void saveDetail(TbMemberGradeChangeDetail changeDetail);

    /**
     * 会员身份认证
     * @param member
     * @param changeType
     * @param oldGrade
     * @param newGrade
     * @param gradeDesc
     * @param userName
     */
    void saveMemberGradeChangeRecord(TbMemberAsset member, Integer changeType, String oldGrade,
                                     String newGrade, String gradeDesc, String userName);

    /**
     * 查询会员产生销售记录时是否存在等级变更记录
     * @param groupId
     * @param vipcode
     * @param saleDate
     * @return
     */
    List<TbMemberGradeChangeDetail> queryChangeGradeListBySaleDate(String groupId, String vipcode, String saleDate);

    /**
     * 查询最大的保级记录
     * @param groupId
     * @param vipcode
     * @param createDate
     * @param changeTypes
     * @return
     */
    TbMemberGradeChangeDetail getMaxRelegationRecordByDate(String groupId, String vipcode, String createDate, List<Integer> changeTypes);

    /**
     * 查询大于指定日期的等级变更记录列表
     * @param groupId
     * @param vipcode
     * @param createDate
     * @param changeTypes
     * @return
     */
    List<TbMemberGradeChangeDetail> getChangeDetailGtCreateDate(String groupId, String vipcode, String createDate, List<Integer> changeTypes);

    /**
     * 退款时，变更会员等级记录创建时间
     * @param details
     * @param createDate
     */
    void changeMemberGradeChangeDetailDate(List<TbMemberGradeChangeDetail> details, Date createDate);

    /**
     * 查询销售记录前的一条等级变更记录
     * @param groupId
     * @param vipcode
     * @param saleDate
     * @return
     */
    TbMemberGradeChangeDetail queryNearChangeGradeBySaleDate(String groupId, String vipcode, String saleDate);

    /**
     * 查询入会日的会员最终等级
     * @param groupId groupId
     * @param vipcode vipcode
     * @param joinTime joinTime
     * @return grade
     */
    String getJoinDayMaxGrade(String vipcode, String groupId, String joinTime);
}
