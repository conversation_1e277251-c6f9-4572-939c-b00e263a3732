package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/15/2022 15:05
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeChatAuthorizationNoticeResource implements Serializable {

    /**
     * 通知的唯一ID。
     * 示例值：EV-2018022511223320873
     */
    @Schema( description = "通知ID")
    private String id;

    /**
     * 遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，HH:mm:ss.表示时分秒，
     * TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。
     * 例如：2015-05-20T13:29:35+08:00表示北京时间2015年05月20日13点29分35秒。
     * 示例值：2015-05-20T13:29:35+08:00
     */
    @Schema( description = "通知创建时间")
    @JsonProperty(value = "create_time")
    private String createTime;

    /**
     * 通知的类型， 支付成功通知的类型为：MALL_TRANSACTION.SUCCESS
     * 示例值：MALL_TRANSACTION.SUCCESS
     */
    @Schema( description = "通知类型")
    @JsonProperty(value = "event_type")
    private String eventType;

    /**
     * 通知的资源数据类型，支付成功通知为encrypt-resource。
     * 示例值：encrypt-resource
     */
    @Schema( description = "通知数据类型")
    @JsonProperty(value = "resource_type")
    private String resourceType;

    /**
     * 通知资源数据
     */
    @Schema( description = "用户开通微信无感积分授权结果通知")
    private WechatAuthorizeResource resource;

    /**
     * 回调摘要
     * 示例值：支付成功
     */
    @Schema( description = "回调摘要")
    private String summary;

}
