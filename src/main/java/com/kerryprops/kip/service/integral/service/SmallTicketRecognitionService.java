package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketCallbackResource;
import com.kerryprops.kip.service.integral.webservice.response.OcrPhotoResponse;
import org.springframework.web.context.request.async.DeferredResult;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 06/27/2024 14:25
 **********************************************************************************************************************/
public interface SmallTicketRecognitionService {

    /**
     * 异步上传图片至合合科技
     * @param result
     * @param tbMemberAsset
     * @param mallItem
     * @param imgUrl
     */
    void asyncUploadImgToHeHe(DeferredResult<OcrPhotoResponse> result,
                              TbMemberAsset tbMemberAsset,
                              MallItem mallItem,
                              String imgUrl);

    /**
     * 兼容前端通过非OCR拍照积分上传KO图片
     * @param tbMemberAsset
     * @param mallItem
     * @param imgUrl
     */
    void matchUploadImgToHeHe(TbMemberAsset tbMemberAsset,
                              MallItem mallItem,
                              String imgUrl);

    /**
     * 查询上传识别结果
     * @param taskId
     * @return
     */
    SmallTicketCallbackResource getUploadResult(String taskId);

    /**
     * 查询上传识别结果-通过嘉里生成的id查询
     * @param outerId
     * @return
     */
    SmallTicketCallbackResource getUploadResultByOuterId(String outerId);

    /**
     * OCR识别超时，回调结果后自动积分
     * @param resource OCR回传内容
     * @param photoReview 拍照记录
     */
    void ocrRecognitionTimeoutAutomaticPoints(SmallTicketCallbackResource resource, TbPhotoReview photoReview);

    /**
     * OCR未超时回调处理逻辑
     * @param resource
     * @param msg
     */
    void ocrCallbackHandler(SmallTicketCallbackResource resource, String msg);

    /**
     * 填充拍照记录OCR回调信息
     * @param review
     * @param resource
     */
    void fillPhotoInfo(TbPhotoReview review, SmallTicketCallbackResource resource);

    /**
     * 填充拍照记录店铺信息
     * @param review
     * @param baseShop
     */
    void fillShopInfo(TbPhotoReview review, TbBaseShop baseShop);

}
