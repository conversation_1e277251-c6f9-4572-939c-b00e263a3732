package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/***********************************************************************************************************************
 * Project - member-points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 01/14/2025 09:04
 **********************************************************************************************************************/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreviousDailyConsumptionRuleDto implements Serializable {

    /**
     * 每日消费升级金额
     */
    private BigDecimal money;

    /**
     * 设置的对应业态
     */
    private String formats;

    /**
     * 生效日期
     */
    private Date effectiveDate;

}
