package com.kerryprops.kip.service.integral.common;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 14:27
 **********************************************************************************************************************/
public class RedisCacheKey {

    /**
     * 会员等级缓存key
     */
    public static final String CRM_MEMBER_GRADE_KEY = "crm:member:grades:list";

    public static final String WX_SYNC_COUNT_KEY = "crm:sync:wechat:%s";

    /**
     * 会员消费总金额缓存key
     * 第一个%s: groupId
     * 第二个%s: vipcode
     */
    public static final String MEMBER_GRADE_RULE_SALE_MONEY = "grade:rule:sales:money:%s:%s";

    /**
     * 销售锁标签
     */
    public static final String SALES_NO_KEY = "crm:sales:%s";

    /**
     * 退款锁标签
     */
    public static final String REFUND_LOCK = "crm:refund:%s";

    public static final String INTEGRAL_ADJUST_LOCK = "crm:type:%s:mallId:%s:sale:%s";

    public static final String ALIPAY_SYNC_TAG = "crm:alipay:sync";

    public static final String MOCK_ADDITIONAL_POINTS = "crm:mock:additional:points:%s";

    /**
     * 缓存会员信息key
     */
    public static final String INDEX_CRM_MEMBER_BY_KIP_USERID = "crm:cache_new_member:info";

    /**
     * 缓存会员信息key
     */
    public static final String INDEX_GROUP_VIPCODE_TO_KIP_USERID = "crm:group_vipcode:kipUserId";

    /**
     * 缓存会员vipcode key
     */
    public static final String INDEX_CRM_VIPCODE_CACHE_KEY = "crm:cache_vipcode";

    /**
     * 完善信息配置redis缓存
     */
    public static final String INDEX_CRM_REFINE_CONFIG_CACHE = "crm:refine:config:key";

    /**
     * LBS对应的楼盘id
     */
    public static final String LBS_PROJECT_KEY = "crm:lbs:project:%s";

    /**
     * 自助积分入口缓存key
     */
    public static final String CRM_AUTO_POINT_ENTRY_KEY = "crm:auto:points:entry";

    /**
     * 首页自定义UI缓存
     */
    public static final String POINTS_HOME_PAGE_CUSTOMIZE_UI = "points:homepage:customize:ui";

    /**
     * 我的自定义UI缓存
     */
    public static final String POINTS_MY_PAGE_CUSTOMIZE_UI = "points:mypage:customize:ui";

    /**
     * 首页需要展示在我的自定义页面的模块
     */
    public static final String POINTS_HOME_PAGE_DISPLAY_MY_PAGE = "points:homepage:display:mypage";

    /**
     * 会员升级所需金额缓存
     */
    public static final String MEMBER_UPGRADE_NEXT_GRADE_NEED_MONEY = "crm:upgrade:next:grade:need_new:money";

    /**
     * 会员升级所需金额缓存
     */
    public static final String MEMBER_UPGRADE_PROGRESS_BAR_KEY = "crm:upgrade:progress:bar";

    /**
     * 会员升级文案缓存
     */
    public static final String MEMBER_UPGRADE_COPY_WRITER_KEY = "crm:upgrade:copy:writer";

    /**
     * 会员保级文案缓存
     */
    public static final String MEMBER_RELEGATION_COPY_WRITER_KEY = "crm:relegation:copy:writer";

    /**
     * 会员消费金额年统计开始时间
     */
    public static final String MEMBER_AMOUNT_CAL_START_DATE = "member:amount:cal:start";

    /**
     * 用户无感积分授权状态缓存key
     */
    public static final String MEMBER_WX_OR_ALI_AUTO_POINTS_AUTH_RECORD = "crm:member:wxorali:autopoints:auth";

    /**
     * 系统内会员vipcode redis缓存值
     */
    public static final String KIP_MEMBER_VIPCODE_KEY = "kip:vipcode:sequence:auto";

    /**
     * OCR识别任务超时缓存key
     */
    public static final String OCR_PHOTO_IDENTITY_TIMEOUT = "ocr:photo:identity:timeout:%s";

    /**
     * OCR识别任务用户操作失败次数记录
     */
    public static final String OCR_PHOTO_MEMBER_FAIL_NUMBER_KEY = "ocr:photo:member:fail:number:%s:%s";

    /**
     * 品牌导览-用户收藏缓存key
     */
    public static final String BRAND_GUIDE_CACHE_KEY = "crm:brand:guide:%s:%s:%s";

    /**
     * Hive lbs明细缓存key
     */
    public static final String HIVE_LBS_CACHE_KEY = "crm:hive:lbs:detail";

    /**
     * OCR拍照积分API访问次数限制key
     */
    public static final String CRM_OCR_ACCESS_LIMIT_KEY = "crm:ocr:access:limit:count";

    /**
     * 拍照积分上传图片访问限制
     */
    public static final String PHOTO_POINTS_ACCESS_LIMIT_KEY = "crm:photo:access:limit:%s:%s";

    /**
     * 缓存group设置的升降级有效配置
     */
    public static final String KIP_MEMBER_GRADE_EFFECTIVE_CONFIG_KEY = "kip:member_grade:effective_config";

    /**
     * 会员等级变更通知Redis Key
     * 第一个%s: groupId
     * 第二个%s: mobile
     */
    public static final String MEMBER_GRADE_CHANGE_NOTIFY_KEY = "member_grade:change_notify:%s:%s";

    /**
     * 手机区号信息缓存
     */
    public static final String KIP_MOBILE_AREA_CODE_KEY = "kip:mobile:area_code";

    /**
     * 旧会员微信支付及积分授权跳转开关
     */
    public static final String HKC_OLD_MEMBER_WX_PAY_AUDIT_SWITCH = "hkc:old:member:wx:pay:audit:switch";

    /**
     * KO OCR上传图片缓存redis key
     */
    public static final String KO_OCR_UPLOAD_IMAGE = "ko:ocr:img_url:%s";

}
