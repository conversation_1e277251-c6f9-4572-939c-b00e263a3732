package com.kerryprops.kip.service.integral.webservice.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 12/11/2024 10:40
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberSignUpResource implements Serializable {

    @NotBlank(message = "groupId不能为空.")
    @Schema( description = "groupId")
    private String groupId;

    @NotBlank(message = "mallId不能为空.")
    @Schema( description = "mallId")
    private String mallId;

    @Schema( description = "昵称")
    private String nickName;

    @NotBlank(message = "手机区号不能为空.")
    @Schema( description = "手机区号")
    private String areaCode;

    @NotBlank(message = "手机号不能为空.")
    @Schema( description = "mobile")
    private String mobile;

}
