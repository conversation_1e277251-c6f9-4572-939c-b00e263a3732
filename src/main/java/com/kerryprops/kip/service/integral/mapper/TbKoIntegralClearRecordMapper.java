package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbKoIntegralClearRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/18/2023 09:24
 **********************************************************************************************************************/
public interface TbKoIntegralClearRecordMapper extends BaseMapper<TbKoIntegralClearRecord> {

    void saveBatch(@Param("list") List<TbKoIntegralClearRecord> list);

    int total();

    List<TbKoIntegralClearRecord> findList(int offset, int size);

}
