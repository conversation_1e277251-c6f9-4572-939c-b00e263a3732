package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - saas
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 06/30/2022 15:09
 **********************************************************************************************************************/

@Getter
public enum MemberGradeChangeTypeEnum {

    TYPE_0(0, "降级", "人工调整-降级"),
    TYPE_1(1, "升级", "人工调整-升级"),
    TYPE_2(2, "升级", "每日消费金额-升级"),
    TYPE_3(3, "升级", "累计消费金额-升级"),
    TYPE_4(4, "降级", "累计消费金额-降级"),
    TYPE_5(5, "升级", "入会当日消费-升级"),
    TYPE_6(6, "升级", "会员身份认证-升级"),
    TYPE_7(7, "保级", "累计消费金额-保级"),
    TYPE_8(8, "保级", "每日消费金额-保级"),
    TYPE_9(9, "保级", "会员身份认证-保级"),
    TYPE_10(10, "升级", "每月消费金额-升级"),
    TYPE_11(11, "保级", "每月消费金额-保级"),
    TYPE_12(12, "保级", "人工调整"),
    TYPE_13(13, "保级", "系统自动调整"),
    TYPE_14(14, "降级", "有单退货-降级");

    private final Integer code;
    private final String name;
    private final String desc;

    MemberGradeChangeTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static MemberGradeChangeTypeEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        MemberGradeChangeTypeEnum changeTypeEnum = null;
        for (MemberGradeChangeTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                changeTypeEnum = typeEnum;
                break;
            }
        }
        return changeTypeEnum;
    }

    public static List<Integer> getRelegationType() {
        return Arrays.asList(TYPE_0.getCode(), TYPE_4.getCode(), TYPE_7.getCode(), TYPE_8.getCode(), TYPE_9.getCode(), TYPE_11.getCode(), TYPE_12.getCode());
    }

    /**
     * 自然年不受手动调整等级的影响
     * @return
     */
    public static List<Integer> getNaturalRelegationType() {
        return Arrays.asList(TYPE_4.getCode(), TYPE_7.getCode(), TYPE_8.getCode(), TYPE_9.getCode(), TYPE_11.getCode(), TYPE_12.getCode());
    }

    /**
     * 判断是否是人工调整降级会员等级
     * @param changeType
     * @return
     */
    public static boolean isManualOperation(Integer changeType) {
        return TYPE_0.getCode().equals(changeType);
    }

    /**
     * 是否是升级类型
     * @param changeType
     * @return
     */
    public static boolean isUpgradeType(Integer changeType) {
        if (Objects.isNull(changeType)) {
            return false;
        }
        return Arrays.asList(TYPE_1.getCode(), TYPE_2.getCode(), TYPE_3.getCode(), TYPE_5.getCode(), TYPE_6.getCode(), TYPE_10.getCode()).contains(changeType);
    }

    public static List<Integer> getTypesWithNot4() {
        return Arrays.asList(TYPE_0.getCode(), TYPE_1.getCode(), TYPE_7.getCode(), TYPE_8.getCode(), TYPE_9.getCode(), TYPE_11.getCode(), TYPE_12.getCode());
    }

    /**
     *
     * @param changeType
     * @return
     */
    public static boolean isManualDowngrade(Integer changeType) {
        return TYPE_0.getCode().equals(changeType);
    }

    /**
     * 是否是通过销售自动升级的等级变更类型
     * 2：每日消费金额-升级
     * 3：累计消费金额-升级
     * 5：入会当日消费-升级
     * 10：每月消费金额-升级
     * @param changeType
     * @return
     */
    public static boolean isAutoUpgrade(Integer changeType) {
        return Stream.of(TYPE_2, TYPE_3, TYPE_5, TYPE_10).anyMatch(type -> type.getCode().equals(changeType));
    }

}
