package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 自定义页面列表
 * <AUTHOR>
 * @date 2022-12-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_self_defining_page")
public class SelfDefiningPage implements Serializable {

    /**
    * 主键
    */
    private Long id;

    /**
    * 页面名称 collate utf8mb4_general_ci
    */
    private String pageName;

    /**
    * 集团id collate utf8mb4_general_ci
    */
    private String groupId;

    /**
    * 商场编号 collate utf8mb4_general_ci
    */
    private String mallId;

    /**
    * 是否启用：0关闭 1启用
    */
    private Integer openFlag;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 创建者
    */
    private String createUser;

    /**
    * 更新时间
    */
    private Date updateDate;

    /**
    * 更新者
    */
    private String updateUser;

    /**
    * 页面类型：0首页 1自定义页面 2我的
    */
    private String type;

    /**
    * 版本号
    */
    private Integer versions;

    /**
    * 头部导航是否显示(1开0关)
    */
    private Integer headNavigationShow;

    /**
    * 背景图链接 collate utf8mb4_general_ci
    */
    private String backgroundImageUrl;

    /**
    * 背景图开关 0关闭1开启
    */
    private Integer backgroundImageFlag;

    /**
    * 导航条字体颜色 collate utf8mb4_general_ci
    */
    private String headNavigationColor;

    /**
    * 悬浮胶囊颜色（0黑1白）
    */
    private Integer capsuleColor;

    /**
    * 下划线开关（0关1开）
    */
    private Integer underline;

    /**
    * 背景图全图开关（0关1开）
    */
    private Integer completeFlag;

    /**
    * 0关1开
    */
    private Integer myWaterfallShow;

    /**
    * 广告图开关
    */
    private Integer myAdShow;

    /**
     * 广告图片地址 collate utf8mb4_general_ci
     */
    private String myAdUrl;

    /**
    * 资源列表每行显示个数
    */
    private Integer showLineNum;

    /**
     * 资源列表每行显示个数
     */
    private Integer showBadge;

    /**
    * 显示悬浮按钮开关 0关1开
    */
    private Integer floatingBtnShow;

    /**
    * 悬浮按钮图片地址 collate utf8mb4_general_ci
    */
    private String floatingBtnUrl;

    /**
    * 显示卡等（针对页面类型为我的），可多个，逗号拼接 collate utf8mb4_general_ci
    */
    private String myShowGrade;

    /**
    * 广告图片地址英文 collate utf8mb4_general_ci
    */
    private String myAdUrlEn;

    private String moduleContext;
}