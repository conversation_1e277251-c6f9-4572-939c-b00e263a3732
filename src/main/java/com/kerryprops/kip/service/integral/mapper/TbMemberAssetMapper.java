package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbMemberAssetMapper extends BaseMapper<TbMemberAsset> {

    /**
     * 查询crm用户信息
     * @param groupId
     * @param kipUserId
     * @return
     */
    TbMemberAsset queryMemberByGroupIdAndKipUserId(@Param("groupId") String groupId, @Param("kipUserId") String kipUserId);

    List<TbMemberAsset> queryMemberByGroupIdAndKipUserIds(@Param("groupId") String groupId, @Param("kipUserIds") List<String> kipUserIds);

    /**
     * 根据groupId+手机号查询会员信息
     * @param groupId
     * @param mobile
     * @return
     */
    TbMemberAsset queryMemberByGroupIdAndMobile(@Param("groupId") String groupId, @Param("mobile") String mobile);

    /**
     * 根据groupId+vipcode查询会员信息
     * @param groupId
     * @param vipcode
     * @return
     */
    TbMemberAsset queryMemberByGroupIdAndVipcode(@Param("groupId") String groupId, @Param("vipcode") String vipcode);

    /**
     * 通过groupId+vipcode反查对应的kipUserId
     * @param groupId
     * @param vipcode
     * @return
     */
    String findKipUserIdByGroupIdAndVipcode(@Param("groupId") String groupId, @Param("vipcode") String vipcode);

    List<TbMemberAsset> queryMemberByGroupIdAndVipcodes(@Param("groupId") String groupId, @Param("vipcodes") List<String> vipcodes);

    void updateMemberStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 调整积分数量
     * @param member
     * @return
     */
    int updateMemberIntegral(TbMemberAsset member);

    /**
     * 填充tb_member表缺失的kipUserId字段值
     * @param member
     */
    void fillMemberKipUserId(TbMemberAsset member);

    int getMemberCountBetweenDate(@Param("groupId") String groupId, @Param("mallId") String mallId, @Param("startTime") String startTime,
                                  @Param("endTime") String endTime, @Param("registerSource") String registerSource);

    void updateMemberCompletedStatus(TbMemberAsset member);

    /**
     * 更新会员注册来源
     * @param tbMemberAsset
     */
    void updateRegisterSource(TbMemberAsset tbMemberAsset);

    /**
     * 更新会员等级
     * @param tbMemberAsset
     * @return
     */
    int updateMemberGrade(TbMemberAsset tbMemberAsset);

    void updateMobileByKipUserId(@Param("mobile") String mobile,
                                 @Param("kipUserId") String kipUserId);

    /**
     * 通过kipUserId查找会员信息
     * @param kipUserId
     * @return
     */
    List<TbMemberAsset> findByKipUserId(@Param("kipUserId") String kipUserId);

    /**
     * 通过mobile查找会员信息
     * @param mobile
     * @return
     */
    List<TbMemberAsset> findByMobile(@Param("mobile") String mobile);

    /**
     * 查询单个会员信息
     * @param dto
     * @return
     */
    TbMemberAsset findByDto(SingleMemberQueryDto dto);

    /**
     * 复制账号信息至会员禁用表
     * @param kipUserId
     */
    void copyToMemberInvalidByKipUserId(@Param("kipUserId") String kipUserId, @Param("groupIds") List<String> groupIds);

    /**
     * 复制账号信息至会员禁用表
     * @param mobile
     */
    void copyToMemberInvalidByMobile(@Param("mobile") String mobile, @Param("groupIds") List<String> groupIds);

    /**
     * 删除会员账号
     * @param kipUserId kipUserId
     * @param groupIds groupIds
     */
    void removeMemberByKipUserId(@Param("kipUserId") String kipUserId, @Param("groupIds") List<String> groupIds);

    /**
     * 删除会员账号
     * @param mobile mobile
     * @param groupIds groupIds
     */
    void removeMemberByMobile(@Param("mobile") String mobile, @Param("groupIds") List<String> groupIds);

    TbMemberAsset getLatestMemberAsset();

    /**
     * KO Big Group Member Query By KipUserId
     * @param groupIds
     * @param kipUserId
     * @return
     */
    List<TbMemberAsset> findByGroupIdsAndKipUserId(@Param("groupIds") List<String> groupIds, @Param("kipUserId") String kipUserId);

    /**
     * KO Big Group Member Query By Mobile
     * @param groupIds
     * @param mobile
     * @return
     */
    List<TbMemberAsset> findByGroupIdsAndMobile(@Param("groupIds") List<String> groupIds, @Param("mobile") String mobile);
}
