package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.io.IoUtil;
import com.kerryprops.kip.service.integral.client.UnifiedMessageClient;
import com.kerryprops.kip.service.integral.common.enums.SubscriptionTypeEnum;
import com.kerryprops.kip.service.integral.config.NotifyEmailProperties;
import com.kerryprops.kip.service.integral.model.dto.CrmSysUserDto;
import com.kerryprops.kip.service.integral.model.dto.KerryResultDto;
import com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto;
import com.kerryprops.kip.service.integral.model.vo.EmailSendVo;
import com.kerryprops.kip.service.integral.service.KerryStaffService;
import com.kerryprops.kip.service.integral.service.SmsSendService;
import com.kerryprops.kip.service.integral.service.TbSubscriptionRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/23/2023 09:57
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class SmsSendServiceImpl implements SmsSendService {

    private final UnifiedMessageClient unifiedMessageClient;
    private final NotifyEmailProperties notifyEmailProperties;
    private final TbSubscriptionRecordService tbSubscriptionRecordService;
    private final KerryStaffService kerryStaffService;

    @Override
    public KerryResultDto sendEmail(EmailSendVo sendVo) {
        return unifiedMessageClient.emailSend(sendVo);
    }

    @Override
    public KerryResultDto emailAliCloudSend(EmailSendVo sendVo) {
        return unifiedMessageClient.emailAliCloudSend(sendVo);
    }

    @Async
    @Override
    public void sendPointsEmail(String mallId, Integer subscriptionType) {
        List<String> userIds = tbSubscriptionRecordService.querySubscriptionUserIdList(SubscriptionRecordQueryDto
                .builder()
                .mallId(mallId)
                .subscriptionType(subscriptionType)
                .status(1)
                .build());
        if (CollectionUtils.isEmpty(userIds)) {
            log.info("userIds is empty {}", mallId);
            return;
        }
        List<CrmSysUserDto> userList = kerryStaffService.getUserList(userIds.stream().map(Long::parseLong).toList());
        if (CollectionUtils.isEmpty(userList)) {
            log.info("userList is empty {}", mallId);
            return;
        }
        List<String> emailList = userList.stream().map(CrmSysUserDto::getEmail).toList();
        if (CollectionUtils.isEmpty(emailList)) {
            log.info("emailList is empty {}", mallId);
            return;
        }
        // 根据type获取不同的模板内容
        String content = this.getEmailTemplateContent(SubscriptionTypeEnum.getPath(subscriptionType));
        if (StringUtils.isBlank(content)) {
            log.info("邮箱模版内容为空: 【{}】", mallId);
            return;
        }
        // 订阅类型(0:积分拦截 1:拍照积分 )
        if (Objects.equals(SubscriptionTypeEnum.POINTS_INTERCEPT.getType(), subscriptionType)) {
            content = content.replace(CRM_ADMIN_URL, notifyEmailProperties.getCrmAdminUrl());
        } else if (Objects.equals(SubscriptionTypeEnum.POINTS_PHOTO.getType(), subscriptionType)) {
            content = content.replace(CRM_PHOTO_POINTS_URL, notifyEmailProperties.getCrmPhotoPointsUrl());
        } else {
            log.info("该订阅类型不支持");
            return;
        }
        EmailSendVo sendVo = EmailSendVo
                .builder()
                .subject(SubscriptionTypeEnum.getSubject(subscriptionType))
                .sendTos(emailList)
                .businessType("COMMON")
                .html(true)
                .source("CRM")
                .text(content)
                .build();
        // 发送邮件
        unifiedMessageClient.emailAliCloudSend(sendVo);
    }

    private String getEmailTemplateContent(String templatePath) {
        if (StringUtils.isBlank(templatePath)) {
            log.info("template path is blank");
            return null;
        }
        try (InputStream resourceAsStream = this.getClass().getResourceAsStream(templatePath)) {
            String read = IoUtil.read(resourceAsStream, Charset.defaultCharset());
            log.info(read);
            return read;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        try {
            File file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "emails/photo_points.html");
            String s = Files.readString(Paths.get(file.toURI()));
            log.info(s);
        } catch (IOException e) {
            e.printStackTrace();
        }

        try (InputStream resourceAsStream = SmsSendService.class.getResourceAsStream("/emails/photo_points.html")) {
            String read = IoUtil.read(resourceAsStream, Charset.defaultCharset());
            log.info(read);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
