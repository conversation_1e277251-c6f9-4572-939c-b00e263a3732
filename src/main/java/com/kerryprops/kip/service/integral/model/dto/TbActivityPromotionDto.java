package com.kerryprops.kip.service.integral.model.dto;

import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * tb_activity_promotion
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbActivityPromotionDto implements Serializable {

    private String id;

    /**
     * 规则名字
     */
    private String name;

    /**
     *  积分模式(1:多倍积分   2：固定积分) 
     */
    private String module;

    /**
     * 开始时间
     */
    private Date begintime;

    /**
     * 结束时间
     */
    private Date endtime;

    /**
     * 营销积分过期时间
     */
    private Date promotionbonusexpirationdate;

    /**
     * 商场编号
     */
    private String mallid;

    /**
     * 店铺id
     */
    private String shopid;

    /**
     * 关系类型（叠加或者取最高 必填项二选一）叠加  0    最高   1
     */
    private String type;

    /**
     * 倍数
     */
    private Double times;

    /**
     * 积分
     */
    private Integer bonus;

    /**
     * 集团id
     */
    private String groupId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 满足的条件
     */
    private List<List<TbActivityPromotionCondition>> conditions;

    /**
     * 首次入会
     */
    private List<TbActivityPromotionJoinvip> vips;

}