package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_brand_guide_collection
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_brand_guide_collection")
public class TbBrandGuideCollection implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场id
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 会员号
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * 品牌导览id
     */
    @TableField("brand_guide_id")
    private String brandGuideId;

    /**
     * 是否有效，1: 有效，0: 无效
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    private Date createDate;

}