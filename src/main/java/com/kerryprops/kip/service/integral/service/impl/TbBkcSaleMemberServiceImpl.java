package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.entity.TbBkcSaleMember;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.mapper.TbBkcSaleMemberMapper;
import com.kerryprops.kip.service.integral.service.MemberRegisterService;
import com.kerryprops.kip.service.integral.service.TbBkcSaleMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/09/2023 15:08
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbBkcSaleMemberServiceImpl extends ServiceImpl<TbBkcSaleMemberMapper, TbBkcSaleMember> implements TbBkcSaleMemberService {

    private final TbBkcSaleMemberMapper tbBkcSaleMemberMapper;
    private final MemberRegisterService memberRegisterService;

    @Override
    public TbBkcSaleMember findByVipcode(String vipcode, String groupId) {
        return tbBkcSaleMemberMapper.findByVipcode(vipcode, groupId);
    }

    @Override
    public void bkcMigrationConfirmStatus(LoginUser loginUser, Integer confirmStatus) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        if (Objects.isNull(tbMemberAsset)) {
            return;
        }
        TbBkcSaleMember saleMember = tbBkcSaleMemberMapper.findByVipcode(tbMemberAsset.getVipcode(), tbMemberAsset.getGroupId());
        // 不存在，则新增
        if (Objects.isNull(saleMember)) {
            saleMember = TbBkcSaleMember.builder().groupId(loginUser.getBrandId()).vipcode(tbMemberAsset.getVipcode()).saleCase(0).confirmStatus(confirmStatus).build();
            tbBkcSaleMemberMapper.insert(saleMember);
            return;
        }
        if (1 == saleMember.getConfirmStatus()) {
            return;
        }
        saleMember.setConfirmStatus(confirmStatus);
        tbBkcSaleMemberMapper.updateById(saleMember);
    }

    @Override
    public void insertBkcCaseMember(TbBkcSaleMember member) {
        tbBkcSaleMemberMapper.saveOrUpdate(member);
    }
}
