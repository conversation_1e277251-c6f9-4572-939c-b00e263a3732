package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/06/2023 15:56
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_auto_points_config")
public class TbAutoPointsConfig implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("format")
    private String format;

    @TableField("group_id")
    private String groupId;

    @TableField("lbs_id")
    private String lbsId;

    @TableField("project_id")
    private String projectId;

    @TableField("code")
    private String code;

    @TableField("status")
    private Integer status;

    @TableField("create_date")
    private Date createDate;

    @TableField("creator")
    private String creator;

    @TableField("update_date")
    private Date updateDate;

    @TableField("updater")
    private String updater;
}
