package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.pmw.client.resource.PointsActivationConfirmResource;
import com.kerryprops.kip.service.integral.entity.TbOpenCardFailRecord;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 01/16/2024 15:10
 **********************************************************************************************************************/
public interface TbOpenCardFailRecordService extends IService<TbOpenCardFailRecord> {

    /**
     * 保存授权信息失败记录
     * @pam record
     */
    void saveOrUpdateRecord(PointsActivationConfirmResource resource);

    /**
     * 查询授权信息失败记录
     * @param openId
     * @param mallId
     * @return
     */
    TbOpenCardFailRecord queryByOpenIdAndMallId(String openId, String mallId);

    /**
     * 检查商圈的授权情况
     * @param openId
     * @param userId
     */
    void checkWeChatAndAlipayAuthStatus(String openId, String userId);

    /**
     * 查询用户在微信端的开卡信息
     * @param openId
     * @param userId
     */
    void queryWeChatOpenCardInfo(String openId, String userId);

}
