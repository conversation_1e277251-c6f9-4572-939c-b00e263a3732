package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_card_market
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_card_market")
public class TbCardMarket implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 集团id
     */
    private String groupId;

    /**
     * 商场编号
     */
    private String mallId;

    /**
     * 商场名称
     */
    private String name;

    /**
     * 支付宝商圈名称
     */
    private String zfbName;

    /**
     * 商场位置坐标纬度
     */
    private String latitude;

    /**
     * 商场位置坐标经度
     */
    private String longitude;

    /**
     * 支付宝会员卡领卡链接
     */
    private String url;

    /**
     * 会员卡模板id
     */
    private String templateId;

    /**
     * 状态,0默认，1已开通
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 支付宝商场id
     */
    private String mallAli;

    /**
     * 是否需要跳转到第三方支付宝小程序(0:不需要;1:需要)
     */
    private Integer isToMiniProgramAli;

    /**
     * 支付宝小程序跳转的页面路径与参数
     */
    private String pagesAli;

    /**
     * 支付宝小程序的appid
     */
    private String appidAli;

}