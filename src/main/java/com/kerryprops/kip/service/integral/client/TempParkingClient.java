package com.kerryprops.kip.service.integral.client;

import com.kerryprops.kip.service.integral.model.dto.PageableParkingVehicle;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/***********************************************************************************************************************
 * Project - temporary-parking-service
 * TempParkingClient
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Sam.Zhang
 * Created Date - 2023/8/3 15:02
 **********************************************************************************************************************/

@FeignClient(name = "temporary-parking-service", url = "${kerry.services.temporary-parking:default}")
public interface TempParkingClient {

    /**
     * 根据车牌号查询绑定过的车列表
     *
     * @param carNo 车牌号
     * @return 返回结果
     */
    @GetMapping(value = "vehicles/by-vehicle-no/{carNo}")
    PageableParkingVehicle vehiclesByCarNo(@PathVariable("carNo") String carNo);

}
