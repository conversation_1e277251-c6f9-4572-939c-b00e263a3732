package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2022 12:00
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MallItem implements Serializable {

    /**
     * 商场id
     */
    private String mallId;

    /**
     * 商场简称
     */
    private String abbreviation;

    /**
     * 品牌id
     */
    private String groupId;

    /**
     * 自助积分页面icon
     */
    private String code;

    /**
     * 对应的小程序id
     */
    private String appId;

    /**
     * 微信商户号
     */
    private String wxMchId;

    /**
     * 微信商圈id
     */
    private Integer wxBrandId;

    /**
     * 微信无感积分会员卡模版ID
     */
    private String wxCardTemplateId;

    /**
     * 支付宝的mall id
     */
    private String aliMallId;

    /**
     * 支付宝无感积分会员卡模版id
     */
    private String aliCardTemplateId;

    /**
     * 商场所属楼盘id
     */
    private String projectId;

}
