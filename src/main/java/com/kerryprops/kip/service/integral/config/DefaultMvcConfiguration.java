package com.kerryprops.kip.service.integral.config;

import com.kerryprops.kip.service.integral.common.UserInfoInteceptor;
import com.kerryprops.kip.service.integral.common.current.TokenArgumentResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.List;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;
import static java.time.temporal.ChronoField.*;
import static java.time.temporal.ChronoField.SECOND_OF_MINUTE;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/14/2022 16:28
 **********************************************************************************************************************/

@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public class DefaultMvcConfiguration implements WebMvcConfigurer {

    public static final DateTimeFormatter CUSTOM_ISO_LOCAL_DATE_TIME;
    public static final DateTimeFormatter CUSTOM_ISO_LOCAL_TIME;

    static {
        CUSTOM_ISO_LOCAL_DATE_TIME = new DateTimeFormatterBuilder().parseCaseInsensitive()
                .append(ISO_LOCAL_DATE).appendLiteral(' ').appendValue(HOUR_OF_DAY, 2)
                .appendLiteral(':').appendValue(MINUTE_OF_HOUR, 2).optionalStart()
                .appendLiteral(':').appendValue(SECOND_OF_MINUTE, 2).toFormatter();

        CUSTOM_ISO_LOCAL_TIME = new DateTimeFormatterBuilder().parseCaseInsensitive()
                .appendValue(HOUR_OF_DAY, 2)
                .appendLiteral(":").appendValue(MINUTE_OF_HOUR, 2).optionalStart()
                .appendLiteral(":").appendValue(SECOND_OF_MINUTE, 2).toFormatter();
    }

    @Autowired
    private UserInfoInteceptor userInfoInteceptor;
    @Autowired
    private TokenArgumentResolver tokenArgumentResolver;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(userInfoInteceptor);
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(tokenArgumentResolver);
    }

}
