package com.kerryprops.kip.service.integral.service.promotion.impl;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @DESC 每周
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:27
 **********************************************************************************************************************/

@Slf4j
@Component
public class EachWeekPromotionRule implements PromotionRule {
    @Override
    public String getRuleType() {
        return "4";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        if (StringUtils.isBlank(condition.getPromotionConditionContent())) {
            return false;
        }
        return condition.getPromotionConditionContent().contains(this.dayOfWeek(saleDate));
    }

    private String dayOfWeek(Date date){
        int day= DateUtil.dayOfWeek(date);
        switch (day){
            case 1:return "星期日";
            case 2:return "星期一";
            case 3:return "星期二";
            case 4:return "星期三";
            case 5:return "星期四";
            case 6:return "星期五";
            case 7:return "星期六";
            default:return "";
        }
    }

}
