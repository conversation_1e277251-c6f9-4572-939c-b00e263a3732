package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/14/2022 09:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbPointsInterceptQueryDto implements Serializable {

    private String groupId;

    private String mallId;

    private String shopNo;

    private String mobile;

    private String orderNo;

    private Integer status;

    private String startDate;

    private String endDate;

    private Integer num;

    private Integer size;

    private Integer offset;

    private String saleDate;

    private String vipcode;

    private List<Integer> statusList;

}
