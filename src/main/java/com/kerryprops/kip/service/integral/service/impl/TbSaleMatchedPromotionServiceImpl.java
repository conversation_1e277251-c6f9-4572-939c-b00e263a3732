package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotion;
import com.kerryprops.kip.service.integral.entity.TbSaleMatchedPromotion;
import com.kerryprops.kip.service.integral.mapper.TbSaleMatchedPromotionMapper;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbSaleMatchedPromotionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/17/2023 16:56
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbSaleMatchedPromotionServiceImpl implements TbSaleMatchedPromotionService {

    private final TbSaleMatchedPromotionMapper tbSaleMatchedPromotionMapper;

    @Override
    public void saveRecord(List<TbActivityPromotion> list, SalesAutoPointsDto dto) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<TbSaleMatchedPromotion> matchedList = new ArrayList<>(list.size());
        for (TbActivityPromotion promotionDto: list) {
            matchedList.add(TbSaleMatchedPromotion.builder().sellNo(dto.getSaleNo()).integralAdjustId(dto.getIntegralAdjustId()).promotionId(promotionDto.getId()).promotionName(promotionDto.getName()).build());
        }
        tbSaleMatchedPromotionMapper.saveBatch(matchedList);
    }

    @Override
    public List<TbSaleMatchedPromotion> findBySellNos(List<String> sellNos) {
        if (CollectionUtils.isEmpty(sellNos)) {
            return Collections.emptyList();
        }
        return tbSaleMatchedPromotionMapper.findBySellNos(sellNos);
    }

    @Override
    public List<TbSaleMatchedPromotion> findByAdjustIds(List<String> adjustIds) {
        if (CollectionUtils.isEmpty(adjustIds)) {
            return Collections.emptyList();
        }
        return tbSaleMatchedPromotionMapper.findByAdjustIds(adjustIds);
    }
}
