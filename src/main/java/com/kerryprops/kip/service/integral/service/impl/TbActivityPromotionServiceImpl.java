package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.client.CipMiddleWareClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotion;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionJoinvipMapper;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionMapper;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionconditionMapper;
import com.kerryprops.kip.service.integral.model.dto.CrowdResultDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbActivityPromotionIntegralService;
import com.kerryprops.kip.service.integral.service.TbActivityPromotionService;
import com.kerryprops.kip.service.integral.service.promotion.impl.PromotionRuleStrategyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/17/2022 11:04
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbActivityPromotionServiceImpl implements TbActivityPromotionService {

    private final TbActivityPromotionMapper tbActivityPromotionMapper;
    private final TbActivityPromotionconditionMapper tbActivityPromotionconditionMapper;
    private final TbActivityPromotionJoinvipMapper tbActivityPromotionJoinvipMapper;
    private final TbActivityPromotionIntegralService tbActivityPromotionIntegralService;
    private final CipMiddleWareClient cipMiddleWareClient;
    /**
     * 多倍营销积分规则策略组件
     */
    private final PromotionRuleStrategyService promotionRuleStrategyService;

    @Override
    public List<TbActivityPromotion> queryByMallId(String mallId, String saleDate, String shopNo, String kipUserId) {
        List<TbActivityPromotion> promotions = tbActivityPromotionMapper.queryByMallIdAndDate(mallId, saleDate);
        if (CollectionUtils.isEmpty(promotions)) {
            return Collections.emptyList();
        }
        if (StringUtils.isNotBlank(shopNo)) {
            promotions = promotions.stream().filter(it -> (IntegralConstant.DEFAULT_STORE.equalsIgnoreCase(it.getShopId()) ||
                    StrUtil.split(it.getShopId(), CommonSeparators.WELL_SEPARATOR).contains(shopNo))).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(promotions)) {
            return Collections.emptyList();
        }
        // 判断人群包
        promotions = this.checkCrowd(promotions, kipUserId, mallId);
        if (CollectionUtils.isEmpty(promotions)) {
            return Collections.emptyList();
        }
        List<String> ids = promotions.stream().map(TbActivityPromotion::getId).collect(Collectors.toList());
        List<TbActivityPromotionCondition> conditions = Collections.emptyList();
        List<TbActivityPromotionJoinvip> vips = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(ids)) {
            conditions = tbActivityPromotionconditionMapper.findByPromotionIds(ids);
            vips = tbActivityPromotionJoinvipMapper.findByPromotionIds(ids);
        }
        Map<String, List<TbActivityPromotionCondition>> conditionMap = new HashMap<>(2);
        Map<String, List<TbActivityPromotionJoinvip>> vipMap = new HashMap<>(2);
        if (CollectionUtils.isNotEmpty(conditions)) {
            conditionMap = conditions.stream().collect(Collectors.groupingBy(TbActivityPromotionCondition::getPromotionId));
        }
        if (CollectionUtils.isNotEmpty(vips)) {
            vipMap = vips.stream().collect(Collectors.groupingBy(TbActivityPromotionJoinvip::getPromotionId));
        }
        for (TbActivityPromotion promotion: promotions) {
            // 当日入会
            promotion.setVips(MapUtils.getObject(vipMap, promotion.getId(), Collections.emptyList()));
            // 分组条件
            List<TbActivityPromotionCondition> list = MapUtils.getObject(conditionMap, promotion.getId());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            // 回填活动下设置的店铺信息
            list.forEach(item -> item.setShopId(promotion.getShopId()));
            Map<String, List<TbActivityPromotionCondition>> listMap = list.stream().collect(Collectors.groupingBy(TbActivityPromotionCondition::getPromotionConditionGroupId));
            promotion.setConditions(new ArrayList<>(listMap.values()));
        }
        return promotions;
    }

    private List<TbActivityPromotion> checkCrowd(List<TbActivityPromotion> promotions, String kipUserId, String mallId) {
        List<String> crowdIdList = promotions.stream().map(TbActivityPromotion::getCrowdId)
                .filter(StringUtils::isNotBlank).flatMap(s -> Arrays.stream(s.split(","))).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crowdIdList)) {
            return promotions;
        }
        // 10个人群包id为一组
        List<List<String>> partitions = ListUtil.partition(crowdIdList, 10);
        List<CrowdResultDto> allCrowdList = new ArrayList<>(crowdIdList.size());
        List<CrowdResultDto> crowdList = null;
        for (List<String> itemIds: partitions) {
            try {
                crowdList = cipMiddleWareClient.hasCrowdByUser(kipUserId, String.join(",", itemIds)).getData();
            } catch (Exception e) {
                log.info("checkCrowdId error {}", e.getMessage());
            }
            if (CollectionUtils.isEmpty(crowdList)) {
                continue;
            }
            allCrowdList.addAll(crowdList);
        }
        if (CollectionUtils.isEmpty(allCrowdList)) {
            // 说明该会员不属于任何一个人群包，所以要返回人群包为空的营销活动
            return promotions.stream().filter(it -> (StringUtils.isBlank(it.getCrowdId()))).collect(Collectors.toList());
        }
        // 过滤出mallId符合条件的人群包数据，并且hasCrowd为true的
        List<String> crowds = allCrowdList.stream().filter(it-> (StringUtils.equals(it.getMall_id(), mallId) && Boolean.TRUE.equals(it.getHasCrowd())))
                .map(CrowdResultDto::getCrowd_id).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crowds)) {
            // 没有符合条件的人群包
            return promotions.stream().filter(it -> (StringUtils.isBlank(it.getCrowdId()))).collect(Collectors.toList());
        }
        List<TbActivityPromotion> list = new ArrayList<>();
        for (TbActivityPromotion promotion : promotions) {
            if (StringUtils.isBlank(promotion.getCrowdId())) {
                list.add(promotion);
                continue;
            }
            // 营销积分活动内配置的人群包id
            List<String> promotionCrowdIds = Arrays.asList(promotion.getCrowdId().split(","));
            // 两个集合有相同元素，说明该营销活动符合规则
            if(promotionCrowdIds.stream().anyMatch(crowds::contains)){
                list.add(promotion);
            }
        }
        return list;
    }

    @Override
    public Integer handlePromotionActivity(SalesAutoPointsDto dto, boolean isAutoPoints) {
        Date saleDate = DateUtil.parse(dto.getSaleDate());
        String strSaleDate = DateUtil.formatDateTime(saleDate);
        // 人群包判断
        List<TbActivityPromotion> promotionList = this.queryByMallId(dto.getMallId(), strSaleDate, dto.getShopId(), dto.getMember().getKipUserId());
        if (CollectionUtils.isEmpty(promotionList)) {
            return dto.getExchangePoints();
        }
        List<TbActivityPromotion> matchRules = promotionList.stream().filter(promotion -> (CollectionUtils.isEmpty(promotion.getConditions()) ||
                promotion.getConditions().stream().anyMatch(conditionList -> conditionList.stream().allMatch(condition ->
                        promotionRuleStrategyService.getRuleProcess(condition.getPromotionConditionType()).checkRule(condition, dto, saleDate, promotion.getVips())))))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchRules)) {
            return dto.getExchangePoints();
        }
        log.info("MatchedPromotionActivity: {}-{}", dto.getSaleNo(), matchRules.stream().map(TbActivityPromotion::getId).collect(Collectors.joining(",")));
        // 固定 + 叠加的活动，都满足
        List<TbActivityPromotion> list = matchRules.stream().filter(it -> (Objects.equals(it.getModule(), "2") && Objects.equals(it.getType(), "0"))).collect(Collectors.toList());
        // 不是单纯的计算积分，计算完需要保存
        /*if (isAutoPoints) {
            tbActivityPromotionIntegralService.batchSavePromotionIntegralRecord(list, dto);
        }*/
        // 1:多倍积分 + 最高
        double dbWithMax = matchRules.stream().filter(it -> Objects.equals(it.getModule(), "1")).filter(it -> Objects.equals(it.getType(), "1")).map(TbActivityPromotion::getTimes)
                .max(BigDecimal::compareTo).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).doubleValue();
        // 1:多倍积分 + 叠加 --- 倍数
        double dbWithDj = matchRules.stream().filter(it -> Objects.equals(it.getModule(), "1")).filter(it -> Objects.equals(it.getType(), "0")).map(TbActivityPromotion::getTimes)
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP).doubleValue();
        // 2:固定积分 + 最高
        double gdWithMax = matchRules.stream().filter(it -> Objects.equals(it.getModule(), "2")).filter(it -> Objects.equals(it.getType(), "1")).mapToDouble(TbActivityPromotion::getBonus).max().orElse(0D);
        // 2:固定积分 + 叠加 -- 积分数
        double gdWithDj = list.stream().mapToDouble(TbActivityPromotion::getBonus).sum();
        log.info("获得积分情况如下: {} - {} - {} - {}", dbWithMax, gdWithMax, gdWithDj, dbWithDj);
        // 销售额兑换到的基础积分数
        int basePoints = dto.getExchangePoints();
        // 营销积分获得积分数
        int promotionIntegral = 0;
        // 总积分数
        int totalPoints = 0;
        // 积分统计来源
        int maxPointsFromType = 0;
        // 多倍 + 最高
        if ((dbWithMax * basePoints) > (gdWithMax + basePoints)) {
            dbWithMax = dbWithMax == 0 ? 1 : dbWithMax;
            dbWithDj = dbWithDj == 0 ? 1 : dbWithDj;
            promotionIntegral = (int) Math.floor((dbWithMax - 1) * basePoints + (dbWithDj - 1) * basePoints) + (int) gdWithDj;
            totalPoints = promotionIntegral + basePoints;
            maxPointsFromType = 1;
        } else {
            // 固定 + 最高
            dbWithDj = dbWithDj == 0 ? 1 : dbWithDj;
            totalPoints = (int) Math.floor(gdWithMax + gdWithDj + dbWithDj * basePoints);
            promotionIntegral = (int) Math.floor(gdWithMax + gdWithDj + (dbWithDj - 1) * basePoints);
            maxPointsFromType = 2;
        }
        // 记录积分来源: 多倍 + 最高 / 固定 + 最高
        // 不是单纯的计算积分，计算完需要保存
        if (isAutoPoints) {
            tbActivityPromotionIntegralService.savePromotionIntegral(matchRules, dto, maxPointsFromType, basePoints);
        }
        // 保存积分通过营销积分活动调整的记录
        dto.setSalesRemark(dto.getSalesRemark() + "，营销积分活动获得积分:" + promotionIntegral + "，总积分:" + totalPoints + "分");
        // 营销积分 + 基础积分 = 本次销售记录获得到的总积分数
        return totalPoints;
    }
}
