package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/19/2022 21:38
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberSaleRepeatQueryDto implements Serializable {

    private String groupId;

    private String mallId;

    private String saleNo;

    private String shopId;

    private String useMoney;

    private String saleDateBefore;

    private String saleDateAfter;

    private String vipcode;

    /**
     * 销售单id
     */
    private Long saleId;

    private List<String> saleTypes;

    /**
     * 是否排除全额退款的销售单，有值且为1，则排除全额退款的销售单 - TICK 4.8(SCRM-8468)
     */
    private Integer notFullRefundFlag;

}
