package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRuleProgress;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeRuleProgressMapper
 * @Description 会员等级变更流程表
 * @date 2022/9/26 15:00
 * @Version 1.0
 */
public interface TbMemberGradeRuleProgressMapper extends BaseMapper<TbMemberGradeRuleProgress> {

    TbMemberGradeRuleProgress queryByGroupIdAndGrade(@Param("groupId") String groupId, @Param("grade") String grade);

}
