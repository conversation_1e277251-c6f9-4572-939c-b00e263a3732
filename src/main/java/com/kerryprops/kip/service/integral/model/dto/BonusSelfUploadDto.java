package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/27/2023 13:39
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BonusSelfUploadDto implements Serializable {

    private String vipcode;

    private String mobile;

    private String mallId;

    private String groupId;

    /**
     * 4 小程序扫码  3 小票审核  1CRM后台录入
     */
    private String type;

    private String status;

    /**
     * 积分审核图片地址
     */
    private String imageUrl;

    private Integer orgPoints;

    /**
     * 变更前等级
     */
    private String orgGrade;

    /**
     * 记录OCR上传/回调的异常原因
     */
    private String description;

    /**
     * OCR拍照积分/拍照积分-嘉里生成code
     */
    private String qrcode;

    /**
     * 记录合合上传拍照图片异常的原因，非必传参数
     */
    private String reason;

}
