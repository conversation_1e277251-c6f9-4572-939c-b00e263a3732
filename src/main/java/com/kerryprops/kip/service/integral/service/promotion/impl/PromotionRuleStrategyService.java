package com.kerryprops.kip.service.integral.service.promotion.impl;

import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:36
 **********************************************************************************************************************/

@Slf4j
@Service
public class PromotionRuleStrategyService implements ApplicationContextAware, InitializingBean {

    private final Map<String, PromotionRule> ruleMap = new HashMap<>(16);

    private ApplicationContext applicationContext;

    public PromotionRule getRuleProcess(String ruleType) {
        PromotionRule rule = MapUtils.getObject(ruleMap, ruleType);
        if (Objects.isNull(rule)) {
            throw BizException.error(PointsEnum.PROMOTION_RULE_PARAMS_ERROR);
        }
        return rule;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        applicationContext.getBeansOfType(PromotionRule.class).forEach((k, v) -> {
            if (StringUtils.isBlank(v.getRuleType())) {
                return;
            }
            ruleMap.put(v.getRuleType(), v);
        });
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
