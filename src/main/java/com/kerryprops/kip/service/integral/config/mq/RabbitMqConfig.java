package com.kerryprops.kip.service.integral.config.mq;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/***********************************************************************************************************************
 * Project - integral-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/15/2022 14:02
 **********************************************************************************************************************/

@Slf4j
@Configuration
public class RabbitMqConfig {

    /*
    定义连接，rabbitMQ
     */
    @Bean(name = "crmConnectionFactory")
    @Primary
    public ConnectionFactory crmConnectionFactory(CrmRabbitProperties crm) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setAddresses(crm.getAddresses());
        connectionFactory.setUsername(crm.getUsername());
        connectionFactory.setPassword(crm.getPassword());
        connectionFactory.setVirtualHost(crm.getVirtualHost());
        connectionFactory.setRequestedHeartBeat(crm.getRequestedHeartbeat());
        return connectionFactory;
    }

    @Bean(name = "crmContainerFactory")
    @Primary
    public SimpleRabbitListenerContainerFactory crmContainerFactory(SimpleRabbitListenerContainerFactoryConfigurer configurer,
                                                                    @Qualifier("crmConnectionFactory") ConnectionFactory crmConnectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, crmConnectionFactory);
        factory.setAcknowledgeMode(AcknowledgeMode.AUTO);
        return factory;
    }

    @Bean(name = "crmRabbitTemplate")
    @Primary
    public RabbitTemplate crmRabbitTemplate(@Qualifier("crmConnectionFactory") ConnectionFactory connectionFactory){
        return new RabbitTemplate(connectionFactory);
    }

    @Bean(name = "rabbitAdmin")
    @Primary
    public RabbitAdmin rabbitAdmin(@Qualifier("crmConnectionFactory") ConnectionFactory crmConnectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(crmConnectionFactory);
        // 只有设置为 true，spring 才会加载 RabbitAdmin 这个类
        rabbitAdmin.setAutoStartup(true);

        // ***************************************************************************** DIRECT EXCHANGE ***************************************************************************************
        // 定义exchange
        Exchange directChange = ExchangeBuilder.directExchange(RabbitMqConstant.DIRECT_EXCHANGE).durable(true).build();
        rabbitAdmin.declareExchange(directChange);

        /**
         * CRM 积分拦截审核
         */
        Queue pointsInterceptReviewQueue = new Queue(RabbitMqConstant.REJECT_POINTS_INTERCEPT_RECORD, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(pointsInterceptReviewQueue);
        Binding pointsInterceptReviewQueueBinding = BindingBuilder.bind(pointsInterceptReviewQueue).to(directChange).with(RabbitMqConstant.REJECT_POINTS_INTERCEPT_RECORD).noargs();
        rabbitAdmin.declareBinding(pointsInterceptReviewQueueBinding);

        /**
         * 微信商圈支付记录推送
         */
        Queue wechatPaymentRecordQueue = new Queue(RabbitMqConstant.WECHAT_PAYMENT_SYNC_ROUTE_KEY, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(wechatPaymentRecordQueue);
        Binding wechatPaymentRecordQueueBinding = BindingBuilder.bind(wechatPaymentRecordQueue).to(directChange).with(RabbitMqConstant.WECHAT_PAYMENT_SYNC_ROUTE_KEY).noargs();
        rabbitAdmin.declareBinding(wechatPaymentRecordQueueBinding);

        /**
         * 微信商圈退款记录推送
         */
        Queue wechatRefundRecordQueue = new Queue(RabbitMqConstant.WECHAT_REFUND_SYNC_ROUTE_KEY, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(wechatRefundRecordQueue);
        Binding wechatRefundRecordQueueBinding = BindingBuilder.bind(wechatRefundRecordQueue).to(directChange).with(RabbitMqConstant.WECHAT_REFUND_SYNC_ROUTE_KEY).noargs();
        rabbitAdmin.declareBinding(wechatRefundRecordQueueBinding);

        /**
         * 会员销售金额计算推送
         */
        Queue memberSaleNumQueue = new Queue(RabbitMqConstant.XCRM_MEMBER_SALE_SUM, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(memberSaleNumQueue);
        Binding memberSaleNumQueueBinding = BindingBuilder.bind(memberSaleNumQueue).to(directChange).with(RabbitMqConstant.XCRM_MEMBER_SALE_SUM).noargs();
        rabbitAdmin.declareBinding(memberSaleNumQueueBinding);

        /**
         * group卡等级变更触发会员升级判断
         */
        Queue gradeChangeUpgradeQueue = new Queue(RabbitMqConstant.GROUP_MEMBER_GRADE_CHANGE_TRIGGER_UPGRADE, true, false, false,getQueueArgs());
        rabbitAdmin.declareQueue(gradeChangeUpgradeQueue);
        Binding gradeChangeUpgradeQueueBinding = BindingBuilder.bind(gradeChangeUpgradeQueue).to(directChange).with(RabbitMqConstant.GROUP_MEMBER_GRADE_CHANGE_TRIGGER_UPGRADE).noargs();
        rabbitAdmin.declareBinding(gradeChangeUpgradeQueueBinding);

        /**
         * 会员注册奖励发放及会员等级判断
         */
        Queue memberRegisterQueue = new Queue(RabbitMqConstant.XCRM_MEMBER_REGISTER_ACTIVITY_REWARD, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(memberRegisterQueue);
        Binding memberRegisterQueueBinding = BindingBuilder.bind(memberRegisterQueue).to(directChange).with(RabbitMqConstant.XCRM_MEMBER_REGISTER_ACTIVITY_REWARD).noargs();
        rabbitAdmin.declareBinding(memberRegisterQueueBinding);

        /**
         * 会员完善信息必填项是否已完善校验
         */
        Queue memberRefineCheckQueue = new Queue(RabbitMqConstant.XCRM_MEMBER_REFINE_CHECK, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(memberRefineCheckQueue);
        Binding memberRefineChcekQueueBinding = BindingBuilder.bind(memberRefineCheckQueue).to(directChange).with(RabbitMqConstant.XCRM_MEMBER_REFINE_CHECK).noargs();
        rabbitAdmin.declareBinding(memberRefineChcekQueueBinding);

        /**
         * KIP同步会员信息至CRM
         */
        Queue kipSyncMemberCrmQueue = new Queue(RabbitMqConstant.KIP_SYNC_MEMBER_CRM, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(kipSyncMemberCrmQueue);
        Binding kipSyncMemberCrmQueueBinding = BindingBuilder.bind(kipSyncMemberCrmQueue).to(directChange).with(RabbitMqConstant.KIP_SYNC_MEMBER_CRM).noargs();
        rabbitAdmin.declareBinding(kipSyncMemberCrmQueueBinding);

        /**
         * KIP账号注销
         */
        Queue kipUserInvalidQueue = new Queue(RabbitMqConstant.KIP_USER_INVALID_CRM, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(kipUserInvalidQueue);
        Binding kipUserInvalidQueueBinding = BindingBuilder.bind(kipUserInvalidQueue).to(directChange).with(RabbitMqConstant.KIP_USER_INVALID_CRM).noargs();
        rabbitAdmin.declareBinding(kipUserInvalidQueueBinding);

        /**
         * KIP会员身份信息同步
         */
        Queue kipSyncMemberIdentityQueue = new Queue(RabbitMqConstant.KIP_SYNC_MEMBER_IDENTITY, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(kipSyncMemberIdentityQueue);
        Binding kipSyncMemberIdentityQueueBinding = BindingBuilder.bind(kipSyncMemberIdentityQueue).to(directChange).with(RabbitMqConstant.KIP_SYNC_MEMBER_IDENTITY).noargs();
        rabbitAdmin.declareBinding(kipSyncMemberIdentityQueueBinding);

        /**
         * 品牌导览收藏/取消MQ
         */
        Queue brandGuideCollectQueue = new Queue(RabbitMqConstant.CRM_BRAND_GUIDE_COLLECT, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(brandGuideCollectQueue);
        Binding brandGuideCollectQueueBinding = BindingBuilder.bind(brandGuideCollectQueue).to(directChange).with(RabbitMqConstant.CRM_BRAND_GUIDE_COLLECT).noargs();
        rabbitAdmin.declareBinding(brandGuideCollectQueueBinding);

        // ***************************************************************************** TOPIC EXCHANGE ***************************************************************************************
        // 定义topic-exchange
        Exchange topicExchange = ExchangeBuilder.topicExchange(RabbitMqConstant.TOPIC_EXCHANGE).durable(true).build();
        rabbitAdmin.declareExchange(topicExchange);

        /**
         * 场景触发奖励-会员注册
         */
        Queue memberSceneTriggerQueue = new Queue(RabbitMqConstant.MEMBER_SCENE_TRIGGER_QUEUE, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(memberSceneTriggerQueue);
        Binding memberSceneTriggerQueueBinding = BindingBuilder.bind(memberSceneTriggerQueue).to(topicExchange).with(RabbitMqConstant.MEMBER_SCENE_TRIGGER_QUEUE).noargs();
        rabbitAdmin.declareBinding(memberSceneTriggerQueueBinding);

        // ***************************************************************************** DIRECT DELAY EXCHANGE ***************************************************************************************
        /**
         * 报名活动近期消息队列
         */
        Exchange directDelayExchange = ExchangeBuilder.directExchange(RabbitMqConstant.DIRECT_DELAY_EXCHANGE).durable(true).delayed().build();
        rabbitAdmin.declareExchange(directDelayExchange);

        /**
         * CRM 会员积分变更及会员等级变更队列
         */
        Queue memberIntegralOrGradeChangeQueue = new Queue(RabbitMqConstant.XCRM_MEMBER_INTEGRAL_GRADE_CHANGE, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(memberIntegralOrGradeChangeQueue);
        Binding memberIntegralOrGradeChangeQueueBinding = BindingBuilder.bind(memberIntegralOrGradeChangeQueue).to(directDelayExchange).with(RabbitMqConstant.XCRM_MEMBER_INTEGRAL_GRADE_CHANGE).noargs();
        rabbitAdmin.declareBinding(memberIntegralOrGradeChangeQueueBinding);

        /**
         * 微信商圈积分同步推送
         */
        Queue wechatPointsSyncQueue = new Queue(RabbitMqConstant.WECHAT_POINTS_SYNC_ROUTE_KEY, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(wechatPointsSyncQueue);
        Binding wechatPointsSyncQueueBinding = BindingBuilder.bind(wechatPointsSyncQueue).to(directDelayExchange).with(RabbitMqConstant.WECHAT_POINTS_SYNC_ROUTE_KEY).noargs();
        rabbitAdmin.declareBinding(wechatPointsSyncQueueBinding);

        /**
         * pos积分抵现补偿
         */
        Queue posPointsCashOutQueue = new Queue(RabbitMqConstant.POS_CASH_OUT_ROUTE_KEY, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(posPointsCashOutQueue);
        Binding posPointsCashOutQueueBinding = BindingBuilder.bind(posPointsCashOutQueue).to(directDelayExchange).with(RabbitMqConstant.POS_CASH_OUT_ROUTE_KEY).noargs();
        rabbitAdmin.declareBinding(posPointsCashOutQueueBinding);

        /**
         * 微信商圈积分同步推送
         */
        Queue alipayPointsSyncQueue = new Queue(RabbitMqConstant.ALIPAY_POINTS_SYNC_ROUTE_KEY, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(alipayPointsSyncQueue);
        Binding alipayPointsSyncQueueBinding = BindingBuilder.bind(alipayPointsSyncQueue).to(directDelayExchange).with(RabbitMqConstant.ALIPAY_POINTS_SYNC_ROUTE_KEY).noargs();
        rabbitAdmin.declareBinding(alipayPointsSyncQueueBinding);

        /**
         * 会员注册来源消息队列
         */
        Queue memberRegisterSourceQueue = new Queue(RabbitMqConstant.XCRM_REGISTER_SOURCE, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(memberRegisterSourceQueue);
        Binding memberRegisterSourceQueueBinding = BindingBuilder.bind(memberRegisterSourceQueue).to(directDelayExchange).with(RabbitMqConstant.XCRM_REGISTER_SOURCE).noargs();
        rabbitAdmin.declareBinding(memberRegisterSourceQueueBinding);

        /**
         * 会员支付宝无感积分开通授权记录
         */
        Queue aliMemberOpenCardQueue = new Queue(RabbitMqConstant.ALI_MEMBER_OPEN_CARD_KEY, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(aliMemberOpenCardQueue);
        Binding aliMemberOpenCardBinding = BindingBuilder.bind(aliMemberOpenCardQueue).to(directDelayExchange).with(RabbitMqConstant.ALI_MEMBER_OPEN_CARD_KEY).noargs();
        rabbitAdmin.declareBinding(aliMemberOpenCardBinding);

        /**
         * 会员注册次日通知-延迟消息
         */
        Queue nextDayNotificationQueue = new Queue(RabbitMqConstant.XCRM_SIGN_NEXT_DAY_NOTIFICATION, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(nextDayNotificationQueue);
        Binding nextDayNotificationBinding = BindingBuilder.bind(nextDayNotificationQueue).to(directDelayExchange).with(RabbitMqConstant.XCRM_SIGN_NEXT_DAY_NOTIFICATION).noargs();
        rabbitAdmin.declareBinding(nextDayNotificationBinding);

        /**
         * KIP会员注册发短信提醒
         */
        Queue newMemberQueue = new Queue(RabbitMqConstant.XCRM_MEMBER_REGISTER_SMS_NOTIFY, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(newMemberQueue);
        Binding newMemberQueueBinding = BindingBuilder.bind(newMemberQueue).to(directDelayExchange).with(RabbitMqConstant.XCRM_MEMBER_REGISTER_SMS_NOTIFY).noargs();
        rabbitAdmin.declareBinding(newMemberQueueBinding);

        /**
         * 会员销售金额-发生了退款
         */
        Queue memberSaleRefundQueue = new Queue(RabbitMqConstant.XCRM_MEMBER_SALE_REFUND, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(memberSaleRefundQueue);
        Binding memberSaleRefundQueueBinding = BindingBuilder.bind(memberSaleRefundQueue).to(directDelayExchange).with(RabbitMqConstant.XCRM_MEMBER_SALE_REFUND).noargs();
        rabbitAdmin.declareBinding(memberSaleRefundQueueBinding);

        /**
         * 拍照积分审核-驳回发送消息
         */
        Queue photoPointsRejectQueue = new Queue(RabbitMqConstant.PHOTO_POINTS_REJECT_QUEUE, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(photoPointsRejectQueue);
        Binding photoPointsRejectQueueBinding = BindingBuilder.bind(photoPointsRejectQueue).to(directDelayExchange).with(RabbitMqConstant.PHOTO_POINTS_REJECT_QUEUE).noargs();
        rabbitAdmin.declareBinding(photoPointsRejectQueueBinding);

        /**
         * 会员销售退货后，重新计算会员等级延迟队列
         */
        Queue memberSaleRefundCheckGradeQueue = new Queue(RabbitMqConstant.MEMBER_SALE_REFUND_CHECK_GRADE, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(memberSaleRefundCheckGradeQueue);
        Binding memberSaleRefundCheckGradeQueueBinding = BindingBuilder.bind(memberSaleRefundCheckGradeQueue).to(directDelayExchange).with(RabbitMqConstant.MEMBER_SALE_REFUND_CHECK_GRADE).noargs();
        rabbitAdmin.declareBinding(memberSaleRefundCheckGradeQueueBinding);

        /**
         * OCR拍照积分识别成功后，人工取消自动积分
         */
        Queue smallTicketOcrManualCancelQueue = new Queue(RabbitMqConstant.SMALL_TICKET_OCR_MANUAL_CANCEL, true, false, false, getDelayQueueArgs());
        rabbitAdmin.declareQueue(smallTicketOcrManualCancelQueue);
        Binding smallTicketOcrManualCancelQueueBinding = BindingBuilder.bind(smallTicketOcrManualCancelQueue).to(directDelayExchange).with(RabbitMqConstant.SMALL_TICKET_OCR_MANUAL_CANCEL).noargs();
        rabbitAdmin.declareBinding(smallTicketOcrManualCancelQueueBinding);

        // ***************************************************************************** OCR Fanout EXCHANGE ***************************************************************************************
        // 定义广播模式的exchange-ocr
        Exchange fanoutExchange = ExchangeBuilder.fanoutExchange(RabbitMqConstant.OCR_FANOUT_EXCHANGE).durable(true).build();
        rabbitAdmin.declareExchange(fanoutExchange);

        /**
         * OCR合合回调广播队列
         */
        Queue ocrQueue = new Queue(RabbitMqConstant.SMALL_TICKET_OCR_RECOGNITION, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(ocrQueue);
        Binding ocrQueueQueueBinding = BindingBuilder.bind(ocrQueue).to(fanoutExchange).with(RabbitMqConstant.SMALL_TICKET_OCR_RECOGNITION).noargs();
        rabbitAdmin.declareBinding(ocrQueueQueueBinding);

        // ************************************************************ 徽章项目改动 会员销售变动后，广播消息 EXCHANGE *********************************************************************
        // 定义广播模式的exchange-销售变动广播
        Exchange salesFanoutExchange = ExchangeBuilder.fanoutExchange(RabbitMqConstant.SALES_EXCHANGE_FANOUT).durable(true).build();
        rabbitAdmin.declareExchange(salesFanoutExchange);

        /**
         *会员消费后是否升级MQ消息队列
         */
        Queue upgradeJudgeQueue1 = new Queue(RabbitMqConstant.MEMBER_SALES_TRIGGER_UPGRADE_CHECK, true, false, false, getQueueArgs());
        rabbitAdmin.declareQueue(upgradeJudgeQueue1);
        Binding upgradeJudgeQueueBinding1 = BindingBuilder.bind(upgradeJudgeQueue1).to(salesFanoutExchange).with(RabbitMqConstant.MEMBER_SALES_TRIGGER_UPGRADE_CHECK).noargs();
        rabbitAdmin.declareBinding(upgradeJudgeQueueBinding1);

        return rabbitAdmin;
    }

    /**
     * @return
     */
    public Map<String,Object> getQueueArgs(){
        Map<String, Object> argsMap = new HashMap<>(4);
        //设置queue type 为 quorum
        argsMap.put("x-queue-type","quorum");
        return argsMap;
    }

    /**
     * 设置 Delay Queue args 参数
     * @return
     */
    public Map<String,Object> getDelayQueueArgs(){
        Map<String, Object> argsMap = new ConcurrentHashMap<>(4);
        //设置queue type 为 quorum
        argsMap.put("x-queue-type","quorum");
        argsMap.put("x-delayed-type", "direct");
        return argsMap;
    }

    /*
    定义连接，rabbitMQ
     */
    @Bean(name = "paymentConnectionFactory")
    public ConnectionFactory paymentConnectionFactory(PaymentRabbitProperties payment) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setAddresses(payment.getAddresses());
        connectionFactory.setUsername(payment.getUsername());
        connectionFactory.setPassword(payment.getPassword());
        connectionFactory.setVirtualHost(payment.getVirtualHost());
        connectionFactory.setRequestedHeartBeat(payment.getRequestedHeartbeat());
        return connectionFactory;
    }

    @Bean(name = "paymentContainerFactory")
    public SimpleRabbitListenerContainerFactory paymentContainerFactory(SimpleRabbitListenerContainerFactoryConfigurer configurer,
                                                                        @Qualifier("paymentConnectionFactory") ConnectionFactory paymentConnectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, paymentConnectionFactory);
        factory.setAcknowledgeMode(AcknowledgeMode.AUTO);
        return factory;
    }

    @Bean(name = "paymentRabbitAdmin")
    public RabbitAdmin kipBRabbitAdmin(@Qualifier("paymentConnectionFactory") ConnectionFactory paymentConnectionFactory) {
        RabbitAdmin paymentRabbitAdmin = new RabbitAdmin(paymentConnectionFactory);
        paymentRabbitAdmin.setAutoStartup(true);
        // payment topic exchange
        Exchange paymentTopicExchange = ExchangeBuilder.topicExchange(RabbitMqConstant.PAYMENT_TOPIC_EXCHANGE).durable(true).build();
        paymentRabbitAdmin.declareExchange(paymentTopicExchange);

        /**
         * 线上商城支付记录监听MQ
         */
        Queue paymentSyncPayRecordQueue = new Queue(RabbitMqConstant.PAYMENT_SYNC_PAY_RECORD_QUEUE, true, false, false, getQueueArgs());
        paymentRabbitAdmin.declareQueue(paymentSyncPayRecordQueue);
        Binding paymentSyncPayRecordQueueBinding = BindingBuilder.bind(paymentSyncPayRecordQueue).to(paymentTopicExchange).with(RabbitMqConstant.PAYMENT_SYNC_PAY_RECORD_ROUTE_KEY).noargs();
        paymentRabbitAdmin.declareBinding(paymentSyncPayRecordQueueBinding);

        /**
         * 线上商城退款记录监听MQ
         */
        Queue refundSyncMallRecordQueue = new Queue(RabbitMqConstant.REFUND_SYNC_MALL_RECORD_QUEUE, true, false, false, getQueueArgs());
        paymentRabbitAdmin.declareQueue(refundSyncMallRecordQueue);
        Binding refundSyncMallRecordQueueBinding = BindingBuilder.bind(refundSyncMallRecordQueue).to(paymentTopicExchange).with(RabbitMqConstant.REFUND_SYNC_MALL_RECORD_ROUTE_KEY).noargs();
        paymentRabbitAdmin.declareBinding(refundSyncMallRecordQueueBinding);

        return paymentRabbitAdmin;
    }

}