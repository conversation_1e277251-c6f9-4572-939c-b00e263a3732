package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/18/2023 09:20
 **********************************************************************************************************************/


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_ko_integral_clear_record")
public class TbKoIntegralClearRecord implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("group_id")
    private String groupId;

    @TableField("vipcode")
    private String vipcode;

    @TableField("get_points")
    private Integer getPoints;

    @TableField("cost_points")
    private Integer costPoints;

    @TableField("clear_points")
    private Integer clearPoints;

    @TableField("points_num")
    private Integer pointsNum;

    @TableField("create_date")
    private Date createDate;

    @TableField("update_date")
    private Date updateDate;

}
