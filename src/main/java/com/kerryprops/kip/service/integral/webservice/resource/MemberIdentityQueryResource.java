package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/19/2023 15:02
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "查询会员身份信息请求类")
public class MemberIdentityQueryResource implements Serializable {

    @NotBlank(message = "groupId不能为空")
    @Schema( description = "groupId", required = true)
    private String groupId;

    @NotBlank(message = "vipcode不能为空")
    @Schema( description = "vipcode", required = true)
    private String vipcode;

    @NotBlank(message = "lbs id不能为空")
    @Schema( description = "查询会员在此lbsId下的楼盘对应的身份信息", required = true)
    private String lbsId;

}
