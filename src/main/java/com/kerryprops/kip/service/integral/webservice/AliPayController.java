package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.CodeMessageEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.AliPayConfig;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayAuthDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayBizContentDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayItem;
import com.kerryprops.kip.service.integral.service.AliPayService;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import com.kerryprops.kip.service.integral.webservice.resource.CrmMemberOpenCardResource;
import com.kerryprops.kip.service.integral.webservice.response.CrmMemberOpenCardResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 微信支付相关api
 * @createDate 2022/9/7
 * @updateDate 2022/9/7
 */
@Slf4j
@RequestMapping("/alipay/notify")
@RestController
@RequiredArgsConstructor
@Tag(name = "支付宝无感积分回调接口")
public class AliPayController {

    private final MallConfig mallConfig;
    private final AliPayConfig aliPayConfig;
    private final AliPayService aliPayService;
    private final RabbitMqService rabbitMqService;

    @GetMapping("/config")
    @Operation(summary="支付宝所在商场匹配查询", method = "GET")
    public AliPayItem config(String name) {
        return aliPayConfig.getByName(name);
    }

    @RequestMapping(value = "/auth_callback")
    @Operation(summary="支付宝授权结果回调（普通）")
    public void alipayAuthCallback(HttpServletRequest request, HttpServletResponse response, String auth_code,
                                   String template_id, String request_id, String groupId, String app_auth_code, String out_string, String mallId) throws IOException {
        Map<String, String> paramsMap = new HashMap<>(8);
        paramsMap.put("auth_code", auth_code);
        paramsMap.put("template_id", template_id);
        paramsMap.put("request_id", request_id);
        paramsMap.put("groupId", groupId);
        paramsMap.put("app_auth_code", app_auth_code);
        paramsMap.put("out_string", StringUtils.isNotBlank(out_string) ? out_string : mallId);
        paramsMap.put("mallId", mallId);
        AliPayAuthDto aliPayAuthDto = JsonUtils.mapToObj(paramsMap, AliPayAuthDto.class);
        log.info("alipayAuthCallback: {}", aliPayAuthDto);
        if (Objects.isNull(aliPayAuthDto) || StringUtils.isBlank(aliPayAuthDto.getMallId())) {
            return;
        }
        // 填充所属商场id
        if (StringUtils.isBlank(aliPayAuthDto.getMallId())) {
            Optional.ofNullable(MapUtils.getString(paramsMap, "mallId")).ifPresent(aliPayAuthDto::setMallId);
        }
        MallItem mallItem = mallConfig.getByMallId(aliPayAuthDto.getMallId());
        if (Objects.isNull(mallItem)) {
            log.error("AliPay - fail to get mall by ali mall id: {}", aliPayAuthDto.getMallId());
            return;
        }
        aliPayAuthDto.setMallId(mallItem.getMallId());
        aliPayAuthDto.setGroupId(mallItem.getGroupId());
        aliPayAuthDto.setMallItem(mallItem);
        // 获取重定向的url
        String url = aliPayService.authCallback(aliPayAuthDto);
        if (StringUtils.isNotBlank(url)) {
            response.sendRedirect(url);
        }
    }

    /**
     * 支付宝支付即积分开通会员卡
     * @param resource
     * @return
     */
    @Hidden
    @PostMapping("/open_ali_member_card")
    public CrmMemberOpenCardResponse crmMemberOpenCard(@RequestBody CrmMemberOpenCardResource resource) {
        MallItem mallItem = mallConfig.getByMallId(resource.getMallId());
        if (Objects.isNull(mallItem)) {
            log.error("AliPay - fail to get mall by ali mall id: {}", resource.getMallId());
            return CrmMemberOpenCardResponse.builder().url("http://aqhcrm.aegeangroup.com.cn/KouBei/modules/h5/error.html").build();
        }
        String url = aliPayService.memberAuth(AliPayAuthDto.builder().templateId(resource.getTemplateId())
                .mallId(mallItem.getMallId()).groupId(mallItem.getGroupId()).mallItem(mallItem)
                .authCode(resource.getAuthCode()).requestId(resource.getRequestId()).build());
        return CrmMemberOpenCardResponse.builder().url(url).build();
    }

    /**
     * 支付宝支付即积分开通会员卡
     * @param resource
     * @return
     */
    @Hidden
    @PostMapping("/get_smart_points_callback_url")
    public CrmMemberOpenCardResponse getSmartPointsCallback(@RequestBody CrmMemberOpenCardResource resource) {
        MallItem mallItem = mallConfig.getByMallId(resource.getMallId());
        if (Objects.isNull(mallItem)) {
            log.error("AliPay - fail to get mall by ali mall id: {}", resource.getMallId());
            return CrmMemberOpenCardResponse.builder().url("http://aqhcrm.aegeangroup.com.cn/KouBei/modules/h5/error.html").build();
        }
        String url = aliPayService.smartPointsCallback(AliPayAuthDto.builder().templateId(resource.getTemplateId())
                .mallId(mallItem.getMallId()).groupId(mallItem.getGroupId()).mallItem(mallItem)
                .authCode(resource.getAuthCode()).requestId(resource.getRequestId()).build());
        return CrmMemberOpenCardResponse.builder().url(url).build();
    }

    @RequestMapping(value = "/callback")
    @Operation(summary="支付宝无感积分支付回调接口")
    public void alipayCallback(@RequestParam Map<String, String> map, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("alipayCallback: {}", map);
        // 获取请求头参数
        Map<String, String> requestParameterMap = this.getParameters(request);
        AliPayBizContentDto callbackDto = JsonUtils.stringToObj(MapUtils.getString(map, "biz_content"), AliPayBizContentDto.class);
        if (Objects.isNull(callbackDto)) {
            response.getWriter().print(CodeMessageEnum.FAIL.getValue());
            return;
        }
        callbackDto.setMethod(MapUtils.getString(map, "msg_method"));
        callbackDto.setAppId(MapUtils.getString(map, "app_id"));
        callbackDto.setTransactionInfo(JsonUtils.objToString(callbackDto));
        // 日志输出回传信息
        log.info("alipayCallback: {}", callbackDto.getTransactionInfo());
        // 通过支付宝的商场关系表获取嘉里的mall id
        MallItem mallItem = mallConfig.getByAliMallId(callbackDto.getMallIdAli());
        if (Objects.isNull(mallItem)) {
            log.error("AliPay - fail to get mall by ali mall id: {}", callbackDto.getMallIdAli());
            response.getWriter().print(CodeMessageEnum.FAIL.getValue());
            return;
        }
        callbackDto.setMallId(mallItem.getMallId());
        callbackDto.setGroupId(mallItem.getGroupId());
        callbackDto.setMallItem(mallItem);
//        if (Boolean.FALSE.equals(aliPayClientService.validateSignature(requestParameterMap, mallItem.getAbbreviation()))) {
//            log.error("AliPay callback unable to validate signature {}", requestParameterMap);
//        }
        log.info("AliPay - params: {}", JsonUtils.objToString(callbackDto));
        String status = aliPayService.callback(callbackDto);
        // 回写处理状态
        response.getWriter().print(status);
    }

    private Map<String, String> getParameters(HttpServletRequest request) {
        // 请求参数
        Map<String, String[]> queryMap = request.getParameterMap();
        Map<String, String> params = new HashMap<>(16);
        if (queryMap != null) {
            for (Map.Entry<String, String[]> entry : queryMap.entrySet()) {
                params.put(entry.getKey(), entry.getValue()[0]);
            }
        }
        return params;
    }

    @GetMapping(value = "/sendAliPaySms")
    @Operation(summary="支付宝无感积分短信发送接口", method = "GET")
    public void sendAliPaySms(@RequestParam String memberId,
                              @RequestParam String mallId,
                              @RequestParam String messageType) {
        // 商圈快速积分开通提醒-只有微信无感积分开通才有短信提醒，支付宝开通也有短信提醒
        // messageType 25 支付宝无感积分   19 微信无感积分
        SendMessageDto dto = SendMessageDto.builder().memberId(memberId).mallId(mallId).templateType(1)
                .messageType(messageType)
                .updateDate(DateUtil.formatDateTime(new Date())).build();
        rabbitMqService.sendLazyMessage(RabbitMqConstant.XCRM_MEMBER_INTEGRAL_GRADE_CHANGE, JsonUtils.objToString(dto), 3);

    }

}
