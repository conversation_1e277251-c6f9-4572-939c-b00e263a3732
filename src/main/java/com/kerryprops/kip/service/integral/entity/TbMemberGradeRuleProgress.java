package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName TbMemberGradeRuleProgressRecord
 * @Description 会员等级变更流程表
 * @date 2022/9/26 14:54
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "tb_member_grade_rule_process")
public class TbMemberGradeRuleProgress implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 商场id
     */
    @TableField("grade")
    private String grade;

    @TableField("year_grade_rule_id")
    private String yearGradeRuleId;

    @TableField("month_grade_rule_id")
    private String monthGradeRuleId;

    @TableField("day_grade_rule_id")
    private String dayGradeRuleId;

    @TableField("up_progress_text")
    private String upProgressText;

    @TableField("up_progress_text_not_show")
    private String upProgressTextNotShow;

    @TableField("sustain_progress_text")
    private String sustainProgressText;

    @TableField("sustain_progress_text_not_show")
    private String sustainProgressTextNotShow;

    @TableField("sustain_progress_text_max")
    private String sustainProgressTextMax;

    @TableField("create_date")
    private Date createDate;

    @TableField("create_user")
    private String createUser;

    @TableField("update_date")
    private Date updateDate;

    @TableField("update_user")
    private String updateUser;

}
