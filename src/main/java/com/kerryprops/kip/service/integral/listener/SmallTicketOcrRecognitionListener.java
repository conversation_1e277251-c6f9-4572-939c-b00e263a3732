package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.service.SmallTicketRecognitionService;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketCallbackResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/24/2024 12:16
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class SmallTicketOcrRecognitionListener {

    private final SmallTicketRecognitionService smallTicketRecognitionService;

    @RabbitHandler
    @RabbitListener( bindings = {
            @QueueBinding(value = @Queue, exchange = @Exchange(value = RabbitMqConstant.OCR_FANOUT_EXCHANGE, type = "fanout"))
    })
    public void sendMessage(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("OCR-小票识别消息体为空.");
            return;
        }
        log.info("SmallTicketOcrRecognitionListener: {}" , msg);
        SmallTicketCallbackResource resource = JsonUtils.stringToObj(msg, SmallTicketCallbackResource.class);
        if (Objects.isNull(resource)) {
            return;
        }
        // OCR回调处理逻辑
        smallTicketRecognitionService.ocrCallbackHandler(resource, msg);
    }

    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.SMALL_TICKET_OCR_RECOGNITION, containerFactory = "crmContainerFactory")
    public void sendMessage1(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("OCR-小票识别消息体为空.");
            return;
        }
    }

}
