package com.kerryprops.kip.service.integral.common.utils;

import cn.hutool.core.date.DateUtil;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 01/17/2023 17:40
 **********************************************************************************************************************/

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class KipDateUtils {

    /**
     * 比较两个日期是否超过N年
     * @param startDate
     * @param endDate
     * @param numYear
     * @return
     * true: 是，false:否
     */
    public static Boolean startLessThanEndOverCycleYears(Date startDate, Date endDate, int numYear) {
        startDate = DateUtil.offsetMonth(startDate, numYear * 12);
        return startDate.getTime() < endDate.getTime();
    }

    /**
     * 判断两个日期自然年是否相差N年
     * @param startDate
     * @param endDate
     * @param numYear
     * @return
     * true: 是，false:否
     */
    public static Boolean startLessThanEndOverNatureYears(Date startDate, Date endDate, int numYear) {
        return DateUtil.year(endDate) - DateUtil.year(startDate) > numYear;
    }

    /**
     * 检查等级变更记录信息
     * @param detail
     * @return
     */
    public static boolean checkGradeChangeDetail(TbMemberGradeChangeDetail detail) {
        return Objects.nonNull(detail) && Objects.nonNull(detail.getCreateDate());
    }

    public static void main(String[] args) {
        Date changeDate = DateUtil.parseDateTime("2024-03-07 12:00:00");
        Date now = new Date();
        System.out.println(startLessThanEndOverCycleYears(changeDate, now, 1));
        System.out.println(startLessThanEndOverNatureYears(changeDate, now, 1));
    }

}
