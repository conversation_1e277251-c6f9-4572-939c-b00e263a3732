package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 02/15/2023 09:14
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "查询会员profile基本信息")
public class CustomerUserResponse implements Serializable {

    @Schema( description = "kipUserId")
    private String id;

    @Schema( description = "手机号")
    private String phoneNumber;

    @Schema( description = "邮箱")
    private String email;

    @Schema( description = "昵称")
    private String nickName;

    @Schema( description = "真是姓名")
    private String realName;

    @Schema( description = "头像")
    private String avatar;

    /**
     * 性别，0：未知，1：男，2：女
     */
    @Schema( description = "性别，0：未知，1：男，2：女")
    private Integer gender;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema( description = "会员出生年月日")
    private Date birthDate;

    @Schema( description = "证件类型（1：身份证、2：驾驶证、3：护照、4：其他证件）")
    private Integer certificateType;

    @Schema( description = "证件号码")
    private String certificateNum;

    @Schema( description = "婚姻状态：0：未知，1：已婚，2：未婚")
    private Integer maritalStatus;

    @Schema( description = "是否有宝宝：0：未知，1：有，2：无")
    private Integer babyStatus;

    @Schema( description = "省")
    private String provinceAddress;

    @Schema( description = "市")
    private String cityAddress;

    @Schema( description = "区")
    private String districtAddress;

    @Schema( description = "详细地址")
    private String address;

    @Schema( description = "公司名称")
    private String companyName;

    @Schema( description = "职位")
    private String job;

    @Schema( description = "名族")
    private String nation;

    @Schema( description = "家庭电话")
    private String homePhone;

    @Schema( description = "学历")
    private Integer education;

    @Schema( description = "职业")
    private Integer profession;

}
