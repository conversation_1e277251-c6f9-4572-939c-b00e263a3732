package com.kerryprops.kip.service.integral.config;

import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.redis.spring.RedisLockProvider;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 22:01
 **********************************************************************************************************************/

@Configuration
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT60M")
public class ShedlockConfig {

    private static final String DEFAULT_LOCK_PREFIX = "task:points";

    @Bean
    public LockProvider lockProvider(RedisTemplate redisTemplate) {
        return new RedisLockProvider(redisTemplate.getConnectionFactory(), DEFAULT_LOCK_PREFIX);
    }

}
