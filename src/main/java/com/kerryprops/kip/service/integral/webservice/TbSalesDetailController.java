package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource;
import com.kerryprops.kip.service.integral.webservice.response.BadgeAccumulatedAmountResponse;
import com.kerryprops.kip.service.integral.webservice.response.BadgeConsumeFrequencyResponse;
import com.kerryprops.kip.service.integral.webservice.response.BadgeShopGroupResultResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@RequestMapping("/sales")
@RestController
@RequiredArgsConstructor
public class TbSalesDetailController {

    private final TbSalesDetailService tbSalesDetailService;

    @Hidden
    @PostMapping("/accumulatedSalesAmount")
    @Operation(summary="徽章查询累计金额API", method = "POST")
    public BadgeAccumulatedAmountResponse accumulatedSalesAmount(@RequestBody @Validated BadgeSalesResource resource) {
        this.checkShopNosAndMallIdsSameNullError(resource);
        BigDecimal amount = tbSalesDetailService.accumulatedSalesAmount(resource);
        return BadgeAccumulatedAmountResponse.builder().amount(amount).build();
    }

    @Hidden
    @PostMapping("/consumptionShopCount")
    @Operation(summary="徽章查询消费店铺数量API", method = "POST")
    public List<String> consumeShopCount(@RequestBody @Validated BadgeSalesResource resource) {
        this.checkShopNosAndMallIdsSameNullError(resource);
        return tbSalesDetailService.consumeShopCount(resource);
    }

    @Hidden
    @PostMapping("/consumptionFrequency")
    @Operation(summary="徽章查询消费频次API", method = "POST")
    public BadgeConsumeFrequencyResponse consumeFrequency(@RequestBody @Validated BadgeSalesResource resource) {
        this.checkShopNosAndMallIdsSameNullError(resource);
        int frequency = tbSalesDetailService.consumeFrequency(resource);
        return BadgeConsumeFrequencyResponse.builder().count(frequency).build();
    }

    @Hidden
    @PostMapping("/consumptionDays")
    @Operation(summary="徽章查询消费天数API", method = "POST")
    public List<String> consumeDays(@RequestBody @Validated BadgeSalesResource resource) {
        this.checkShopNosAndMallIdsSameNullError(resource);
        return tbSalesDetailService.consumeDays(resource);
    }

    @PostMapping("/badge_rel_shop_group_sales_details")
    @Operation(summary="徽章查询消费天数API", method = "POST")
    public List<BadgeShopGroupResultResponse> badgeShopGroupSalesDetails(@RequestBody @Validated List<BadgeSalesResource> list) {
        list.forEach(this::checkShopNosAndMallIdsSameNullError);
        return tbSalesDetailService.badgeShopGroupSalesDetails(list);
    }

    @GetMapping("/{saleId}/detail")
    @Operation(summary="查询销售明细", method = "GET", hidden = true)
    public TbSalesDetail queryById(@PathVariable("saleId") Long saleId) {
        return tbSalesDetailService.getById(saleId);
    }

    /**
     * 徽章查询销售信息，店铺号或商场ids不能同时为空判断
     * @param resource
     */
    private void checkShopNosAndMallIdsSameNullError(BadgeSalesResource resource) {
        if (CollectionUtils.isEmpty(resource.getShopNos()) && CollectionUtils.isEmpty(resource.getMallIds())) {
            throw BizException.error(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR);
        }
    }

}
