package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.model.dto.MemberPointsChangeDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.webservice.resource.MemberPointsChangeResource;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * DESC：会员积分变更处理类
 * Author - zhangxiliang
 * Created Date - 03/12/2024 08:52
 **********************************************************************************************************************/
public interface TbMemberPointsChangeService {

    /**
     * 销售记录修改用户积分
     * @param dto
     */
    int changeMemberPoints(SalesAutoPointsDto dto);

    /**
     * 会员积分变更
     * @param changeDto
     */
    String updateMemberPoints(MemberPointsChangeDto changeDto);

    /*void clearKoIntegral(MemberPointsChangeDto changeDto);*/

    /**
     * 批量调整积分
     * @param list list
     */
    void batchAdjustPoints(List<MemberPointsChangeResource> list);
}
