package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbCashOutConfig;
import com.kerryprops.kip.service.integral.webservice.resource.TbCashOutConfigResource;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 16:08
 **********************************************************************************************************************/

public interface TbCashOutConfigService extends IService<TbCashOutConfig> {

    /**
     * 分页查询
     * @param resource resource
     * @return config config
     */
    TbCashOutConfig getConfig(TbCashOutConfigResource resource);
}
