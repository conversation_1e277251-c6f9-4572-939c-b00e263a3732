package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbBrandGuideCollection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbBrandGuideCollectionMapper extends BaseMapper<TbBrandGuideCollection> {


    TbBrandGuideCollection findByGroupIdAndVipcodeAndBrandGuideId(@Param("groupId") String groupId,
                                                                  @Param("mallId") String mallId,
                                                                  @Param("vipcode") String vipcode,
                                                                  @Param("brandGuideId") String brandGuideId);

    List<String> findByGroupIdAndMallIdAndVipcode(@Param("groupId") String groupId,
                                                  @Param("mallId") String mallId,
                                                  @Param("vipcode") String vipcode);

}