//package com.kerryprops.kip.service.integral.service;
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.kerryprops.kip.service.integral.entity.SysUser;
//
//import java.util.List;
//
///***********************************************************************************************************************
// * Project - member-points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> nancy
// * Created Date - 10/30/2023 09:29
// **********************************************************************************************************************/
//public interface SysUserService extends IService<SysUser> {
//
//    /**
//     * getUserListByIds
//     * @param userIds userIds
//     * @return List list
//     */
//    List<SysUser> getUserListByIds(List<String> userIds);
//
//    /**
//     * getUserEmailList
//     * @param userIds userIds
//     * @return List list
//     */
//    List<String> getUserEmailList(List<String> userIds);
//}
