package com.kerryprops.kip.service.integral.webservice.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/23/2023 13:39
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbIntegralActivityRateResponse implements Serializable {

    private Long id;

    private String groupId;

    private String mallId;

    private String rewardNode;

    private String gradeId;

    private String gradeName;

    private BigDecimal money;

    private BigDecimal pointNum;

    private int status;

    private Date createDate;

    private String creator;

    private Date updateDate;

    private String updater;

    private String order;

    private List<GradeIntegralResponse> list;

}