package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName BillInfoRpcQueryDto
 * @Description BillInfoRpcQueryDto
 * @date 2022/10/26 9:17
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillInfoRpcQueryDto implements Serializable {

    private String method;

    private String appKey;

    private String v;

    private String signMethod;

    private String messageFormat;

    private String qrCode;

    private String timestamp;

    private String data;

    private String sign;

}
