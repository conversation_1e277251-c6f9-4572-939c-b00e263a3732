package com.kerryprops.kip.service.integral.common.enums;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 无感积分来源枚举类
 * @createDate 2022/9/9
 * @updateDate 2022/9/9
 */
public enum InsensateOriginEnum {

    /**
     * A端
     */
    WECHAT("微信", 0),

    /**
     * S端
     */
    ALIPAY("支付宝", 1),

    /**
     * 海鼎pos
     */
    HIGH_TRADE_POS("POS机", 2),
    ;


    private final String name;
    private final Integer value;

    InsensateOriginEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

}
