package com.kerryprops.kip.service.integral.service.promotion.impl;

import cn.hutool.core.util.StrUtil;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 * @DESC 会员等级满足规则
 * <AUTHOR> Bert
 * Created Date - 02/16/2023 12:18
 **********************************************************************************************************************/

@Slf4j
@Component
public class MemberGradePromotionRule implements PromotionRule {
    @Override
    public String getRuleType() {
        return "0";
    }

    @Override
    public boolean checkRule(TbActivityPromotionCondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
        TbMemberAsset member = dto.getMember();
        if (StringUtils.isNotBlank(condition.getPromotionConditionContent()) && StrUtil.split(condition.getPromotionConditionContent(), CommonSeparators.COMMA_SEPARATOR).contains(member.getGrade())) {
            return true;
        }
        return false;
    }
}
