package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.client.ProfileServiceClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGrade;
import com.kerryprops.kip.service.integral.event.FillMemberKipUserIdEvent;
import com.kerryprops.kip.service.integral.exception.BizNotFoundException;
import com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.model.dto.alipay.ActiveFormDto;
import com.kerryprops.kip.service.integral.model.dto.alipay.AliPayAuthDto;
import com.kerryprops.kip.service.integral.model.vo.KipMemberVO;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - integral-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/22/2022 15:13
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbMemberAssetServiceImpl extends BaseMemberStatus implements TbMemberAssetService {

    private final TbMemberAssetMapper tbMemberAssetMapper;
    private final RedisService redisService;
    private final HiveVasService hiveVasService;
    private final RabbitMqService rabbitMqService;
    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private final TbCardMemberRelationService tbCardMemberRelationService;
    private final ProfileServiceClient profileServiceClient;
    private final KerryStaffService kerryStaffService;
    private final TbMemberGradeService tbMemberGradeService;
    private final TbSequenceVipcodeService tbSequenceVipcodeService;
    private final MemberRegisterService memberRegisterService;
    private final TbMemberSourceService tbMemberSourceService;
    private final MallConfig mallConfig;
    private final ApplicationEventPublisher applicationEventPublisher;

    @Override
    public TbMemberAsset getAllByGroupIdAndKipUserId(String groupId, String kipUserId) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(groupId, kipUserId);
        // 填充会员基本信息
        this.fillMemberProfileInfo(tbMemberAsset);
        return tbMemberAsset;
    }

    /**
     * 填充用户profile信息
     * @param tbMemberAsset 用户对象
     */
    private void fillMemberProfileInfo(TbMemberAsset tbMemberAsset) {
        if (Objects.isNull(tbMemberAsset)) {
            return;
        }
        if (StringUtils.isNotBlank(tbMemberAsset.getKipUserId())) {
            Optional.ofNullable(kerryStaffService.findByKipUserId(tbMemberAsset.getKipUserId())).ifPresent(tbMemberAsset::fillOtherInfo);
        } else {
            Optional.ofNullable(kerryStaffService.findByMobile(tbMemberAsset.getMobile())).ifPresent(tbMemberAsset::fillOtherInfo);
        }
    }

    /**
     * 通过用户与微信openId的绑定关系去查找用户
     * @param openId openId
     * @param groupId groupId
     * @return TbMemberAsset
     */
    @Override
    public TbMemberAsset findByOpenIdAndGroupId(String openId, String groupId) {
        CustomerUserDto userDto = kerryStaffService.findByOpenId(openId);
        return this.getCrmMember(userDto, groupId);
    }

    @Override
    public TbMemberAsset getMemberWithProfileInfo(String kipUserId, String groupId) {
        return this.getMemberByKipUserIdAndGroupId(kipUserId, groupId);
    }

    private TbMemberAsset getMemberByKipUserIdAndGroupId(String kipUserId, String groupId) {
        CustomerUserDto userDto = kerryStaffService.findByKipUserId(kipUserId);
        return this.getCrmMember(userDto, groupId);
    }

    private TbMemberAsset getMemberByMobileAndGroupId(String mobile, String groupId) {
        CustomerUserDto userDto = kerryStaffService.findByMobile(mobile);
        return this.getCrmMember(userDto, groupId);
    }

    private TbMemberAsset getCrmMember(CustomerUserDto userDto, String groupId) {
        if (Objects.isNull(userDto)) {
            return null;
        }
        TbMemberAsset member = this.findByKipUserIdAndGroupId(userDto.getId(), groupId);
        if (Objects.isNull(member)) {
            member = this.findByMobileAndGroupId(userDto.getPhoneNumber(), groupId);
        }
        // 判断会员存在或是否冻结
        if (Objects.isNull(member) || StringUtils.equals(member.getStatus(), IntegralConstant.MEMBER_FREEZE_STATUS)) {
            return null;
        }
        // 补齐会员信息缺失kipUserId
        if (StringUtils.isBlank(member.getKipUserId())) {
            applicationEventPublisher.publishEvent(FillMemberKipUserIdEvent.builder().memberId(member.getId()).kipUserId(userDto.getId()).build());
        }
        member.fillOtherInfo(userDto);
        return member;
    }

    /**
     * 通过自助积分授权绑定关系去查找用户，如果找不到，则继续使用用户与微信openid的绑定关系去查找
     * @param openId openId
     * @param groupId groupId
     * @param mallId mallId
     * @return TbMemberAsset
     */
    @Override
    public TbMemberAsset getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(String openId, String groupId, String mallId) {
        // 通过无感积分授权记录openid去查询用户信息
        TbInsensatePointsAuthRecord record = tbInsensatePointsAuthRecordService.getByOpenIdAndMallId(openId, mallId);
        TbMemberAsset tbMemberAsset = null;
        if (Objects.nonNull(record) && StringUtils.isNotBlank(record.getKipUserId())) {
            tbMemberAsset = this.getMemberByKipUserIdAndGroupId(record.getKipUserId(), groupId);
        }
        if (Objects.isNull(tbMemberAsset)) {
            // 通过用户与微信openid的绑定关系去查询
            tbMemberAsset = this.findByOpenIdAndGroupId(openId, groupId);
        }
        if (Objects.isNull(tbMemberAsset)) {
            TbCardMemberRelation relation = tbCardMemberRelationService.getByMallIdAndAliUserId(mallId, openId);
            if (Objects.nonNull(relation) && StringUtils.isNotBlank(relation.getMobile())) {
                tbMemberAsset = this.getMemberByMobileAndGroupId(relation.getMobile(), groupId);
            }
        }
        // 通过openid去查找
        if (Objects.isNull(tbMemberAsset)) {
            TbCardMemberRelation relation = tbCardMemberRelationService.getByAliUserId(openId);
            if (Objects.nonNull(relation) && StringUtils.isNotBlank(relation.getMobile())) {
                tbMemberAsset = this.getMemberByMobileAndGroupId(relation.getMobile(), groupId);
            }
        }
        return tbMemberAsset;
    }

    @Override
    public TbMemberAsset findByVipcodeAndGroupId(String vipcode, String groupId) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndVipcode(groupId, vipcode);
    }

    @Override
    public TbMemberAsset findMemberAllInfoByVipcodeAndGroupId(String vipcode, String groupId) {
        TbMemberAsset tbMemberAsset = tbMemberAssetMapper.queryMemberByGroupIdAndVipcode(groupId, vipcode);
        // 填充会员基本信息
        this.fillMemberProfileInfo(tbMemberAsset);
        return tbMemberAsset;
    }

    /**
     * 获取KO Big Group会员信息
     * @param kipUserId
     * @param mobile
     * @param minGradeCode
     * @param mallItem
     * @return
     */
    private TbMemberAsset getKoMember(String kipUserId, String mobile, String minGradeCode, MallItem mallItem) {
        TbMemberAsset tbMemberAsset = null;
        List<TbMemberAsset> memberList = tbMemberAssetMapper.findByGroupIdsAndKipUserId(mallConfig.getKoBigGroupIds(), kipUserId);
        if (CollectionUtils.isEmpty(memberList)) {
            memberList = tbMemberAssetMapper.findByGroupIdsAndMobile(mallConfig.getKoBigGroupIds(), mobile);
        }
        if (CollectionUtils.isNotEmpty(memberList)) {
            tbMemberAsset = memberList.stream().filter(it -> StringUtils.equals(it.getGroupId(), mallItem.getGroupId())).findFirst().orElse(null);
            if (Objects.isNull(tbMemberAsset)) {
                // HomeSite要保持一致，但授权商场为用户实际切换的商场
                tbMemberAsset = this.fillKoOtherGroupInfo(memberList.stream().filter(it -> !StringUtils.equals(it.getGroupId(), mallItem.getGroupId()))
                        .findFirst().orElse(null), mallItem, minGradeCode, kipUserId);
            }
        }
        return tbMemberAsset;
    }

    @Override
    public TbMemberResponse getLoginMemberInfo(LoginUser loginUser) {
        // 通过lbs查询属于哪个商场，如果是办公楼，则通过楼盘ID去判断
        MallItem mallItem = hiveVasService.getMallByLbsId(loginUser.getLbsId());
        if (Objects.isNull(mallItem)) {
            log.info("MallId: [{}]，在CRM系统中找不到对应的商场.", loginUser.getLbsId());
            return null;
        }
        // 查询商场对应的会员等级信息
        List<TbMemberGrade> gradeList = tbMemberGradeService.queryGradeSortAscByGroupId(mallItem.getGroupId());
        // groupId下未设置会员等级，则说明该groupId下没有CRM小程序
        if (CollectionUtils.isEmpty(gradeList)) {
            return null;
        }
        TbMemberAsset member = null;
        // KO BIG Group判断
        if (mallConfig.isKoBigGroup(mallItem.getGroupId())) {
            // 查询会员在KO大group下的会员信息
            member = this.getKoMember(loginUser.getCId(), loginUser.getPhoneNumber(), gradeList.get(0).getCode(), mallItem);
        } else {
            member = memberRegisterService.findByGroupIdAndKipUserId(mallItem.getGroupId(), loginUser.getCId());
            if (Objects.isNull(member)) {
                member = tbMemberAssetMapper.queryMemberByGroupIdAndMobile(mallItem.getGroupId(), loginUser.getPhoneNumber());
                // 异步填充kipUserId
                Optional.ofNullable(member).ifPresent(it -> memberRegisterService.fillMemberKipUserId(it, loginUser.getCId()));
            }
        }
        if (Objects.isNull(member)) {
            // 找不到用户信息，有可能是MQ延迟导致，则新增用户
            member = this.getDefaultMember(mallItem, gradeList.get(0).getCode(), loginUser.getPhoneNumber(), loginUser.getCId(), loginUser.getLbsId());
        }
        // 新增注册会员账号信息
        if (Objects.nonNull(member) && Objects.isNull(member.getId())) {
            if (memberRegisterService.saveMember(member) <= 0) {
                log.info("tb_member_asset表出现唯一键冲突: {}", loginUser.getPhoneNumber());
                // 重新使用手机号+groupId做查询
                member = tbMemberAssetMapper.queryMemberByGroupIdAndMobile(mallItem.getGroupId(), loginUser.getPhoneNumber());
            } else {
                log.info("saveMember End.");
                // 发布会员新注册事件
                rabbitMqService.sendMemberNewMsg(member, false);
            }
        }
        if (Objects.isNull(member)) {
            log.info("会员信息不存在: {}-{}", mallItem.getGroupId(), loginUser.getPhoneNumber());
            return null;
        }
        // 会员不存在或冻结状态判断
        this.checkMemberStatus(member);
        final TbMemberResponse response = TbMemberResponse.getResponse(member);
        // 填充会员对应的profile user id
        if (StringUtils.isBlank(response.getKipUserId())) {
            response.setKipUserId(loginUser.getCId());
        }
        // 填充查询会员所需参数
        gradeList.stream().filter(it -> StringUtils.equals(it.getCode(), response.getGrade())).findFirst().ifPresent(it -> {
            response.setCardCoverUrl(it.getCardCoverUrl());
            response.setCardCoverHomeUrl(it.getCardCoverHomeUrl());
            response.setGradeName(it.getName());
            response.setSort(it.getSort());
            response.setUpGradationStatus(it.getUpGradationStatus());
        });
        // 有等级编码拼接
        // 尊享卡
        if (Objects.nonNull(response.getUpGradationStatus()) && response.getUpGradationStatus() == 0) {
            response.setGrades(gradeList.stream().map(TbMemberGrade::getCode).collect(Collectors.toList()));
        } else {
            response.setGrades(gradeList.stream().filter(it -> 1 == it.getUpGradationStatus()).map(TbMemberGrade::getCode).collect(Collectors.toList()));
        }
        // 负积分数判断
        if (!mallConfig.isAllowNegativePointsGroup(member.getGroupId()) && member.getCurrentPoints() < 0) {
            member.setCurrentPoints(0);
        }
        return response;
    }

    @Override
    public TbMemberAsset findByKipUserIdAndGroupId(String kipUserId, String groupId) {
        return memberRegisterService.findByGroupIdAndKipUserId(groupId, kipUserId);
    }

    @Override
    public TbMemberAsset findByKipUserIdAndGroupIdWithException(String kipUserId, String groupId) {
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(groupId, kipUserId);
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        return tbMemberAsset;
    }

    @Override
    public TbMemberAsset findByMobileAndGroupId(String mobile, String groupId) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndMobile(groupId, mobile);
    }

    @Override
    public List<TbMemberAsset> findByVipcodesAndGroupId(List<String> vipcodes, String groupId) {
        if (CollectionUtils.isEmpty(vipcodes)) {
            return Collections.emptyList();
        }
        List<TbMemberAsset> members = tbMemberAssetMapper.queryMemberByGroupIdAndVipcodes(groupId, vipcodes);
        if (CollectionUtils.isEmpty(members)) {
            return Collections.emptyList();
        }
        return members.stream().filter(it -> (StringUtils.equals(it.getStatus(), "1"))).collect(Collectors.toList());
    }

    @Override
    public TbMemberAsset findByDto(SingleMemberQueryDto queryDto) {
        return tbMemberAssetMapper.findByDto(queryDto);
    }

    @Override
    public TbMemberAsset findAllByDto(SingleMemberQueryDto queryDto) {
        TbMemberAsset tbMemberAsset = tbMemberAssetMapper.findByDto(queryDto);
        // 填充会员基本信息
        this.fillMemberProfileInfo(tbMemberAsset);
        return tbMemberAsset;
    }

    @Async
    @Override
    public void profileMobileModify(String kipUserId, String mobile) {
        List<TbMemberAsset> members = tbMemberAssetMapper.findByKipUserId(kipUserId);
        if (CollectionUtils.isEmpty(members)) {
            return;
        }
        if (members.stream().allMatch(item -> StringUtils.equals(item.getMobile(), mobile))) {
            return;
        }
        tbMemberAssetMapper.updateMobileByKipUserId(mobile, kipUserId);
    }

    @Override
    public TbMemberAsset getByMobileAndGroupIdWithException(String mobile, String groupId) {
        TbMemberAsset tbMemberAsset = tbMemberAssetMapper.queryMemberByGroupIdAndMobile(groupId, mobile);
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        return tbMemberAsset;
    }

    @Override
    @DS("points")
    public TbMemberAsset getOrCreateMember(AliPayAuthDto dto) {
        log.info("AliPay - Entering getOrCreateMember: {}", dto);
        ActiveFormDto activeForm = dto.getActiveForm();
        String mobile = activeForm.getMobile();
        String areaCode = "+86";
        // 支付宝海外手机号授权特殊处理
        String[] mobileString = activeForm.getMobile().split(CommonSeparators.SUB_SEPARATOR);
        if (mobileString.length > 1) {
            areaCode = mobileString[0].contains(CommonSeparators.ADD_SEPARATOR) ? mobileString[0] : CommonSeparators.ADD_SEPARATOR + mobileString[0];
            mobile = mobileString[1];
        }
        MallItem mallItem = hiveVasService.getMallByLbsId(dto.getMallId());
        Assert.notNull(mallItem, "在CRM系统中找不到对应的商场.");
        // 设置会员等级最小等级值
        String minGradeCode = tbMemberGradeService.getGroupMiniGradeCode(dto.getGroupId());
        if (StringUtils.isBlank(minGradeCode)) {
            throw BizNotFoundException.error(PointsEnum.BRAND_GRADE_NOT_EXISTS);
        }
        TbMemberAsset member = this.getMemberByMobile(mallItem, mobile, minGradeCode, "");
        if (Objects.nonNull(member) && Objects.nonNull(member.getId())) {
            return member;
        }
        Date date = new Date();
        if (Objects.isNull(member)) {
            member = TbMemberAsset.builder().groupId(dto.getGroupId()).mallId(dto.getMallId()).crmId(IdUtil.simpleUUID())
                    .mobile(mobile).vipcode(tbSequenceVipcodeService.nextId()).wxOpenMarket(dto.getMallId())
                    .createDate(date).updateDate(date).createUser("alipay").joinTime(date).currentPoints(0).status("1").isCompleted("0")
                    .registerSource("alipay").registerSourceLabel("alipay").grade(minGradeCode).authorizedMallId(mallItem.getMallId()).authorizedDate(date).build();
        }
        // 单独获取性别
        String gender = activeForm.getGender();
        KipMemberVO memberVo = KipMemberVO.builder().phoneNumber(mobile).areaCode(areaCode).brandId(dto.getGroupId())
                .lbsId(dto.getMallId()).nickName(dto.getActiveForm().getName()).realName(dto.getActiveForm().getName())
                .gender(StringUtils.isBlank(gender) ? 0 : (StringUtils.equals("男", gender) || StringUtils.equalsIgnoreCase("M", gender)) ? 1 : 2).userId("-1").build();
        IdDto idDto = profileServiceClient.alipayCreateUser(memberVo);
        if (Objects.nonNull(idDto)) {
            member.setKipUserId(idDto.getId());
        } else {
            CustomerUserDto customerUserDto = kerryStaffService.findByMobile(mobile);
            if (Objects.nonNull(customerUserDto)) {
                member.setKipUserId(customerUserDto.getId());
            }
        }
        int affectedRows = memberRegisterService.saveMember(member);
        if (affectedRows <= 0) {
            log.info("getOrCreateMember出现唯一键冲突: {}", member.getMobile());
            // 重新使用手机号+groupId做查询
            member = tbMemberAssetMapper.queryMemberByGroupIdAndMobile(dto.getGroupId(), mobile);
        } else {
            log.info("getOrCreateMemberSaveMember End.");
            // 发送用户注册时间
            rabbitMqService.sendMemberNewMsg(member, false);
            log.info("AliPay - findByMobileAndGroupId succeed");
        }
        return member;
    }

    /**
     * 查询会员信息，区分KO Big Group
     * @param mallItem
     * @param mobile
     * @param minGradeCode
     * @param kipUserId
     * @return
     */
    private TbMemberAsset getMemberByMobile(MallItem mallItem, String mobile, String minGradeCode, String kipUserId) {
        TbMemberAsset tbMemberAsset = null;
        if (mallConfig.isKoBigGroup(mallItem.getGroupId())) {
            List<TbMemberAsset> memberList = tbMemberAssetMapper.findByGroupIdsAndMobile(mallConfig.getKoBigGroupIds(), mobile);
            if (CollectionUtils.isNotEmpty(memberList)) {
                tbMemberAsset = memberList.stream().filter(it -> StringUtils.equals(it.getGroupId(), mallItem.getGroupId())).findFirst().orElse(null);
                if (Objects.isNull(tbMemberAsset)) {
                    tbMemberAsset = this.fillKoOtherGroupInfo(memberList.stream().filter(it -> !StringUtils.equals(it.getGroupId(), mallItem.getGroupId()))
                            .findFirst().orElse(null), mallItem, minGradeCode, kipUserId);
                }
            }
        } else {
            tbMemberAsset = tbMemberAssetMapper.queryMemberByGroupIdAndMobile(mallItem.getGroupId(), mobile);
        }
        return tbMemberAsset;
    }

    @Override
    public TbMemberAsset memberSignUp(MemberSignUpResource resource) {
        MallItem mallItem = hiveVasService.getMallByLbsId(resource.getMallId());
        if (Objects.isNull(mallItem)) {
            throw BizNotFoundException.error(PointsEnum.MALL_NOT_EXISTS);
        }
        // 设置会员等级最小等级值
        String minGradeCode = tbMemberGradeService.getGroupMiniGradeCode(resource.getGroupId());
        if (StringUtils.isBlank(minGradeCode)) {
            throw BizNotFoundException.error(PointsEnum.BRAND_GRADE_NOT_EXISTS);
        }
        TbMemberAsset member = this.getMemberByMobile(mallItem, resource.getMobile(), minGradeCode, "");
        if (Objects.nonNull(member) && Objects.nonNull(member.getId())) {
            return member;
        }
        if (Objects.isNull(member)) {
            Date date = new Date();
            member = TbMemberAsset.builder().groupId(resource.getGroupId()).mallId(resource.getMallId()).crmId(IdUtil.simpleUUID())
                    .mobile(resource.getMobile()).vipcode(tbSequenceVipcodeService.nextId()).wxOpenMarket(resource.getMallId())
                    .createDate(date).updateDate(date).createUser("HKC_LOCKER").joinTime(date).currentPoints(0).status("1").isCompleted("0")
                    .registerSource("HKC_LOCKER").registerSourceLabel("HKC储物柜").grade(minGradeCode).authorizedMallId(mallItem.getMallId()).authorizedDate(date).build();
        }
        KipMemberVO memberVo = KipMemberVO.builder().phoneNumber(resource.getMobile()).areaCode(resource.getAreaCode()).brandId(resource.getGroupId())
                .lbsId(resource.getMallId()).nickName(StringUtils.isBlank(resource.getNickName()) ? "会员用户" : resource.getNickName())
                .gender(0).userId("-1").build();
        IdDto idDto = profileServiceClient.alipayCreateUser(memberVo);
        if (Objects.nonNull(idDto)) {
            member.setKipUserId(idDto.getId());
        } else {
            CustomerUserDto customerUserDto = kerryStaffService.findByMobile(resource.getMobile());
            if (Objects.nonNull(customerUserDto)) {
                member.setKipUserId(customerUserDto.getId());
            }
        }
        int affectedRows = memberRegisterService.saveMember(member);
        if (affectedRows <= 0) {
            log.info("memberSignUp出现唯一键冲突: {}", member.getMobile());
            // 重新使用手机号+groupId做查询
            member = tbMemberAssetMapper.queryMemberByGroupIdAndMobile(resource.getGroupId(), resource.getMobile());
        }
        return member;
    }

    @Override
    public void updateMemberStatus(String groupId, List<String> vipcodes) {
        List<TbMemberAsset> members = tbMemberAssetMapper.queryMemberByGroupIdAndVipcodes(groupId, vipcodes);
        if (CollectionUtils.isEmpty(members)) {
            return;
        }
        log.info("updateMemberStatus size: {}", members.size());
        for (TbMemberAsset member: members) {
            tbMemberAssetMapper.updateMemberStatus(member.getId(), "1");
        }
    }

    @Override
    @CacheEvict(value = RedisCacheKey.INDEX_CRM_MEMBER_BY_KIP_USERID, key = "#tbMemberAsset.groupId +':' + #tbMemberAsset.kipUserId")
    public void updateMemberCompletedStatus(TbMemberAsset tbMemberAsset) {
        tbMemberAssetMapper.updateMemberCompletedStatus(tbMemberAsset);
    }

    @Override
    public TbMemberAsset getMemberById(String id) {
        return tbMemberAssetMapper.selectById(id);
    }

    @Override
    public TbMemberAsset getMemberAllInfoById(String id) {
        TbMemberAsset tbMemberAsset = tbMemberAssetMapper.selectById(id);
        // 填充会员基本信息
        this.fillMemberProfileInfo(tbMemberAsset);
        return tbMemberAsset;
    }

    @Override
    @CacheEvict(value = RedisCacheKey.INDEX_CRM_MEMBER_BY_KIP_USERID, key = "#dto.groupId +':' + #dto.kipUserId")
    public void updateMemberRegisterSource(MemberRegisterResourceDto dto) {
        TbMemberAsset tbMemberAsset = tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(dto.getGroupId(), dto.getKipUserId());
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        String currentDate = DateUtil.formatDate(new Date());
        String joinDate = DateUtil.formatDate(tbMemberAsset.getJoinTime());
        if (!StringUtils.equals(currentDate, joinDate)) {
            log.info("会员加入日与当前时间不匹配: {}-{}-{}", tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode(), joinDate);
            return;
        }
        if (StringUtils.isNotBlank(dto.getRegisterSource())) {
            tbMemberAsset.setRegisterSource(dto.getRegisterSource());
        }
        if (StringUtils.isNotBlank(dto.getRegisterSourceLabel())) {
            tbMemberAsset.setRegisterSourceLabel(dto.getRegisterSourceLabel());
        }
        if (StringUtils.isNotBlank(dto.getRegisterSourceRemark())) {
            tbMemberAsset.setRegisterSourceRemark(dto.getRegisterSourceRemark());
        }
        tbMemberAssetMapper.updateRegisterSource(tbMemberAsset);
        // 保存来源信息
        tbMemberSourceService.checkAndSave(dto, tbMemberAsset);
    }

    @Override
    @CacheEvict(value = RedisCacheKey.INDEX_CRM_MEMBER_BY_KIP_USERID, key = "#tbMemberAsset.groupId +':' + #tbMemberAsset.kipUserId")
    public void updateMemberGrade(TbMemberAsset tbMemberAsset) {
        if (Objects.isNull(tbMemberAsset.getId())) {
            return;
        }
        tbMemberAssetMapper.updateMemberGrade(tbMemberAsset);
    }

    /**
     * KO会员信息切换至其他group时，复制部分会员信息至另外一个group
     * @param tbMemberAsset
     * @param mallItem
     * @param gradeCode
     * @param kipUserId
     * @return
     */
    private TbMemberAsset fillKoOtherGroupInfo(TbMemberAsset tbMemberAsset, MallItem mallItem, String gradeCode, String kipUserId) {
        if (Objects.isNull(tbMemberAsset)) {
            return null;
        }
        tbMemberAsset.setId(null);
        tbMemberAsset.setCrmId(IdUtil.simpleUUID());
        tbMemberAsset.setGroupId(mallItem.getGroupId());
        tbMemberAsset.setVipcode(tbSequenceVipcodeService.nextId());
        tbMemberAsset.setGrade(gradeCode);
        tbMemberAsset.setCurrentPoints(0);
        tbMemberAsset.setAuthorizedMallId(mallItem.getMallId());
        tbMemberAsset.setAuthorizedDate(new Date());
        if (StringUtils.isBlank(tbMemberAsset.getKipUserId())) {
            tbMemberAsset.setKipUserId(kipUserId);
        }
        return tbMemberAsset;
    }

    /**
     * 会员注册，获取初始化的会员信息
     * @param mallItem
     * @param minGradeCode
     * @param mobile
     * @param kipUserId
     * @return
     */
    private TbMemberAsset getDefaultMember(MallItem mallItem, String minGradeCode, String mobile, String kipUserId, String lbsId) {
        Date now = new Date();
        return TbMemberAsset.builder().groupId(mallItem.getGroupId()).mobile(mobile).crmId(IdUtil.simpleUUID())
                .mallId(mallItem.getMallId()).status("1").grade(minGradeCode).vipcode(tbSequenceVipcodeService.nextId())
                .registerSource("miniProgram").registerSourceLabel("小程序注册").currentPoints(0).wxOpenMarket(lbsId)
                .authorizedMallId(mallItem.getMallId()).authorizedDate(now).kipUserId(kipUserId).joinTime(now).isCompleted("0")
                .createUser("sys").createDate(now).updateDate(now).updateUser("sys").build();
    }

    @Override
    public void kipSyncMemberCrmProcess(KipSyncMemberResource resource) {
        // 通过lbs查询属于哪个商场，如果是办公楼，则通过楼盘ID去判断
        MallItem mallItem = hiveVasService.getMallByLbsId(resource.getLbsId());
        if (Objects.isNull(mallItem)) {
            log.info("kipSyncMember: [{}]，在CRM系统中找不到对应的商场.", resource.getLbsId());
            this.checkAndUpdateMobileInfo(resource);
            return;
        }
        // 查询groupId下的会员等级信息
        String minGradeCode = tbMemberGradeService.getGroupMiniGradeCode(mallItem.getGroupId());
        if (StringUtils.isBlank(minGradeCode)) {
            log.info("GroupId: [{}]，会员等级信息未配置.", mallItem.getGroupId());
            return;
        }
        TbMemberAsset tbMemberAsset = null;
        if (mallConfig.isKoBigGroup(mallItem.getGroupId())) {
            // 查询会员在KO大group下的会员信息
            tbMemberAsset = this.getKoMember(resource.getUserId(), resource.getPhoneNumber(), minGradeCode, mallItem);
            if (Objects.isNull(tbMemberAsset)) {
                tbMemberAsset = this.getDefaultMember(mallItem, minGradeCode, resource.getPhoneNumber(), resource.getUserId(), resource.getLbsId());
            }
        } else {
            tbMemberAsset = tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(mallItem.getGroupId(), resource.getUserId());
            if (Objects.nonNull(tbMemberAsset)) {
                // 判断手机号是否一致，不一致，则更新手机号
                if (StringUtils.isNotBlank(resource.getPhoneNumber()) && !StringUtils.equals(tbMemberAsset.getMobile(), resource.getPhoneNumber())) {
                    tbMemberAssetMapper.updateMobileByKipUserId(resource.getPhoneNumber(), resource.getUserId());
                }
            } else {
                if (StringUtils.isNotBlank(resource.getPhoneNumber())) {
                    tbMemberAsset = tbMemberAssetMapper.queryMemberByGroupIdAndMobile(mallItem.getGroupId(), resource.getPhoneNumber());
                }
                // 通过kipUserId查找不到用户，通过手机号可以，则说明数据中缺失kipUserId，补齐
                if (Objects.nonNull(tbMemberAsset)) {
                    tbMemberAsset.setKipUserId(resource.getUserId());
                    tbMemberAssetMapper.fillMemberKipUserId(tbMemberAsset);
                }
            }
            if (Objects.isNull(tbMemberAsset)) {
                tbMemberAsset = this.getDefaultMember(mallItem, minGradeCode, resource.getPhoneNumber(), resource.getUserId(), resource.getLbsId());
            }
        }
        // 会员不为空，则说明已存在账号
        if (Objects.nonNull(tbMemberAsset) && Objects.nonNull(tbMemberAsset.getId())) {
            // 会员身份认证确定会员等级
            rabbitMqService.sendMessage(RabbitMqConstant.KIP_SYNC_MEMBER_IDENTITY,
                    JsonUtils.objToString(MemberIdentityResource.builder().userId(resource.getUserId()).lbsId(mallItem.getMallId()).projectId(mallItem.getProjectId()).build()));
        } else {
            if (memberRegisterService.saveMember(tbMemberAsset) <= 0) {
                log.info("kipSyncMember出现唯一键冲突: {}", resource.getPhoneNumber());
            } else {
                log.info("kipSyncMemberSaveMember End.");
                // 迁移会员数据，则不发送会员注册场景奖励
                if (StringUtils.equals("SZKP-MIGRATION", resource.getSource())) {
                    log.info("SZKP迁移数据: {}, {}", resource.getBrandId(), resource.getPhoneNumber());
                    return;
                }
                // 发送用户注册及次日短信提醒，会员注册场景触发奖励，会员等级根据身份认证判断
                rabbitMqService.sendMemberNewMsg(tbMemberAsset, resource.getIsInvitation());
            }
        }
    }

    /**
     * 检查并修正手机号信息
     * @param resource resource
     */
    private void checkAndUpdateMobileInfo(KipSyncMemberResource resource) {
        if (StringUtils.isAnyBlank(resource.getUserId(), resource.getPhoneNumber())) {
            return;
        }
        List<TbMemberAsset> members = tbMemberAssetMapper.findByKipUserId(resource.getUserId());
        if (CollectionUtils.isEmpty(members)) {
            return;
        }
        if (members.stream().allMatch(item -> StringUtils.equals(item.getMobile(), resource.getPhoneNumber()))) {
            return;
        }
        // 否则更新手机号信息
        tbMemberAssetMapper.updateMobileByKipUserId(resource.getPhoneNumber(), resource.getUserId());
    }

    /**
     * KIP删除账号，则CRM也需要移除账号
     * @param kipUserId kipUserId
     */
    @Override
    public void removeMemberByKipUserId(String kipUserId) {
        if (StringUtils.isBlank(kipUserId)) {
            return;
        }
        List<TbMemberAsset> list = tbMemberAssetMapper.findByKipUserId(kipUserId);
        // 备份账号信息
        tbMemberAssetMapper.copyToMemberInvalidByKipUserId(kipUserId, null);
        // 删除账号信息
        tbMemberAssetMapper.removeMemberByKipUserId(kipUserId, null);
        // 删除缓存
        this.removeCache(list);
    }

    @Override
    public void removeMemberByMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return;
        }
        List<TbMemberAsset> list = tbMemberAssetMapper.findByMobile(mobile);
        // 备份账号信息
        tbMemberAssetMapper.copyToMemberInvalidByMobile(mobile, null);
        // 删除账号信息
        tbMemberAssetMapper.removeMemberByMobile(mobile, null);
        // 删除缓存
        this.removeCache(list);
    }

    /**
     * 移除redis缓存的信息
     * @param list list
     */
    private void removeCache(List<TbMemberAsset> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> delRedisKey = new ArrayList<>(10);
        for (TbMemberAsset asset: list) {
            // 账号注销
            delRedisKey.add(String.format(IntegralConstant.DEF_REDIS_KEY, RedisCacheKey.INDEX_CRM_MEMBER_BY_KIP_USERID, asset.getGroupId(), asset.getKipUserId()));
            // 品牌导览缓存key
            delRedisKey.add(String.format("crm:brand:guide:%s:%s", asset.getGroupId(), asset.getVipcode()));
        }
        redisService.delKeys(delRedisKey);
    }

    @Async
    @Override
    public void removeGroupMemberAccount(BrandMemberDeleteResource resource) {
        if (StringUtils.isBlank(resource.getGroupId())) {
            return;
        }
        List<TbMemberAsset> list;
        if (StringUtils.isNotBlank(resource.getMobile())) {
            list = tbMemberAssetMapper.findByGroupIdsAndMobile(mallConfig.isKoBigGroup(resource.getGroupId()) ?
                    mallConfig.getKoBigGroupIds() : Collections.singletonList(resource.getGroupId()), resource.getMobile());
            // 备份账号信息
            tbMemberAssetMapper.copyToMemberInvalidByMobile(resource.getMobile(), mallConfig.isKoBigGroup(resource.getGroupId()) ?
                    mallConfig.getKoBigGroupIds() : Collections.singletonList(resource.getGroupId()));
            // 删除账号信息
            tbMemberAssetMapper.removeMemberByMobile(resource.getMobile(), mallConfig.isKoBigGroup(resource.getGroupId()) ?
                    mallConfig.getKoBigGroupIds() : Collections.singletonList(resource.getGroupId()));
        } else {
            if (StringUtils.isBlank(resource.getKipUserId())) {
                return;
            }
            list = tbMemberAssetMapper.findByGroupIdsAndKipUserId(mallConfig.isKoBigGroup(resource.getGroupId()) ?
                    mallConfig.getKoBigGroupIds() : Collections.singletonList(resource.getGroupId()), resource.getKipUserId());
            // 备份账号信息
            tbMemberAssetMapper.copyToMemberInvalidByKipUserId(resource.getKipUserId(), mallConfig.isKoBigGroup(resource.getGroupId()) ?
                    mallConfig.getKoBigGroupIds() : Collections.singletonList(resource.getGroupId()));
            // 删除账号信息
            tbMemberAssetMapper.removeMemberByKipUserId(resource.getKipUserId(), mallConfig.isKoBigGroup(resource.getGroupId()) ?
                    mallConfig.getKoBigGroupIds() : Collections.singletonList(resource.getGroupId()));
        }
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 清楚了crm账号缓存及用户品牌导览缓存信息
        this.removeCache(list);
        // 清除消费金额总和
        ClearMemberSalesSumDto salesSumDto = ClearMemberSalesSumDto.builder().groupId(list.get(0).getGroupId()).vipcode(list.get(0).getVipcode())
                .kipUserId(list.get(0).getKipUserId()).build();
        rabbitMqService.sendMessage(RabbitMqConstant.XCRM_MEMBER_SALE_SUM, JsonUtils.objToString(salesSumDto));
    }

    @Override
    public void revertMemberById(String id) {
        tbMemberAssetMapper.deleteById(id);
    }

    @Override
    public TbMemberAsset queryMemberByGroupIdAndMobile(String groupId, String mobile) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndMobile(groupId, mobile);
    }

    @Override
    public TbMemberAsset queryMemberByGroupIdAndKipUserId(String groupId, String kipUserId) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndKipUserId(groupId, kipUserId);
    }

    @Override
    public List<TbMemberAsset> queryMemberByGroupIdAndKipUserIds(String groupId, List<String> kipUserIds) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndKipUserIds(groupId, kipUserIds);
    }

    @Override
    public TbMemberAsset queryMemberByGroupIdAndVipCode(String groupId, String vipCode) {
        return tbMemberAssetMapper.queryMemberByGroupIdAndVipcode(groupId, vipCode);
    }

    @Override
    @CacheEvict(value = RedisCacheKey.INDEX_CRM_MEMBER_BY_KIP_USERID, key = "#tbMemberAsset.groupId +':' + #tbMemberAsset.kipUserId")
    public int updatePoints(TbMemberAsset tbMemberAsset) {
        return tbMemberAssetMapper.updateMemberIntegral(tbMemberAsset);
    }

    @Override
    public int getMemberCountBetweenDate(String groupId, String mallId, String startTime, String endTime, String miniProgram) {
        return tbMemberAssetMapper.getMemberCountBetweenDate(groupId, mallId, startTime, endTime, miniProgram);
    }

    @Override
    public int updateById(TbMemberAsset tbMemberAsset) {
        return tbMemberAssetMapper.updateById(tbMemberAsset);
    }
}
