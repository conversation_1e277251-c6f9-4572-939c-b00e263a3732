//package com.kerryprops.kip.service.integral.service;
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
//import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
//import com.kerryprops.kip.service.integral.entity.TbMemberSale;
//import com.kerryprops.kip.service.integral.model.dto.MemberSaleMonthNumberQueryDto;
//import com.kerryprops.kip.service.integral.model.dto.MemberSaleRepeatQueryDto;
//import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.CompletableFuture;
//
///***********************************************************************************************************************
// * Project - integral-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 08/16/2022 13:48
// **********************************************************************************************************************/
//public interface TbMemberSaleService extends IService<TbMemberSale> {
//
//    /**
//     * 保存会员信息
//     *
//     * @param memberSale
//     */
//    void saveMemberSale(TbMemberSale memberSale);
//
//    /**
//     * 检查销售记录是否重重
//     *
//     * @param queryDto 查询参数
//     * @return sale 销售对象
//     */
//    TbMemberSale checkSaleRecordRepeatedOrNot(MemberSaleRepeatQueryDto queryDto);
//
//    /**
//     * 根据销售单号查询销售记录
//     * @param sellNo 销售单号
//     * @return sale 销售对象
//     */
//    TbMemberSale querySaleBySellNo(String sellNo);
//
//    /**
//     * 根据销售单号查询销售记录
//     * @param sellNo 销售单号
//     * @param saleType 销售类型
//     * @return sale 销售对象
//     */
//    TbMemberSale queryBySellNoAndSaleType(String sellNo, String saleType);
//
//    /**
//     * 查询用户月消费比数
//     * @param query 查询参数
//     * @return int 数量
//     */
//    int queryMemberMonthSalesNumber(MemberSaleMonthNumberQueryDto query);
//
//    /**
//     * 查询销售对象
//     * @param sellNo 销售单
//     * @param mallId 商城id
//     * @return sale 对象
//     */
//    TbMemberSale selectBySellNoAndMallId(String sellNo, String mallId);
//
//    /**
//     * 保存销售记录
//     * @param dto 销售单
//     */
//    void saveSaleData(SalesAutoPointsDto dto);
//
//    /**
//     * 查询用户时间段内的销售金额-先查缓存再查数据库
//     * @param member
//     * @param rule
//     * @param beginDate
//     * @param endDate
//     * @param shopIds
//     * @return double
//     */
//    Double getMemberSaleAmountBetweenTime(TbMemberAsset member, TbMemberGradeRule rule, Date beginDate, Date endDate, List<String> shopIds);
//
//    /**
//     * 查询用户时间段内的销售金额-实时查数据库
//     * @param member
//     * @param ruleId
//     * @param beginDate
//     * @param endDate
//     * @param shopIds
//     * @return double
//     */
//    Double getMemberSaleAmountBetweenDate(TbMemberAsset member, List<String> shopIds, Date beginDate, Date endDate, Long ruleId);
//
//    /**
//     * 校验用户在保级规则下是否保级成功
//     * @param member
//     * @param rule
//     * @param beginDate
//     * @param endDate
//     * @return
//     */
//    boolean checkMemberSaleAmountWhetherRelegationSucceeded(TbMemberAsset member, TbMemberGradeRule rule, Date beginDate, Date endDate, List<String> shopIds);
//
//    /**
//     * 处理11/2，11/3号这两天由于bug导致用户未升级的等级
//     */
//    void initMemberGradeData(String vipcode);
//
//    /**
//     * 修改商场错误的店铺id
//     * @param shopIds 店铺id
//     * @param mallId 商城id
//     * @param newIds newIds
//     */
//    void modifyMemberSaleShopId(String shopIds, String mallId, String newIds);
//
//    /**
//     * 修改商场错误的店铺id
//     * @param groupId 店铺id
//     * @param mallId 商城id
//     * @param vipcode 会员号
//     * @param sellNos 销售单
//     */
//    CompletableFuture<Map<String, TbMemberSale>> findSalesInfoMap(String groupId, String mallId, String vipcode, List<String> sellNos);
//
//    Map<String, TbMemberSale> getSalesInfoList(String groupId, String mallId, String vipcode, List<String> sellNos);
//
//    /**
//     * 发生了退款，处理满赠活动满赠金额MQ推送
//     * @param saleId
//     */
//    void memberSaleRefundProcess(String saleId);
//
//    /**
//     * 通过id查询销售数据
//     * @param id
//     * @return
//     */
//    TbMemberSale getMemberSaleById(String id);
//
//    /**
//     * 查询时间范围内的销售记录
//     * @param groupId
//     * @param vipcode
//     * @param beginDate
//     * @param endDate
//     * @param shopIds
//     * @return
//     */
//    List<TbMemberSale> getSaleListCreateAsc(String groupId, String vipcode, Date beginDate, Date endDate, List<String> shopIds);
//
//    void checkSalesUpgradeGrade(String groupId, String startDate, String endDate);
//
//    /**
//     * 获取会员等级规则缓存的金额信息
//     * @param rule
//     * @param tbMemberAsset
//     * @return
//     */
//    Double getGradeRuleCacheAmount(TbMemberGradeRule rule, TbMemberAsset tbMemberAsset);
//
//}
