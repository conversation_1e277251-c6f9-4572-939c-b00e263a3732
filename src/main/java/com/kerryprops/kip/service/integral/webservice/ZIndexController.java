package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.CustomizePageTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.ProviderEnum;
import com.kerryprops.kip.service.integral.common.enums.ZIndexModuleSignEnum;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.response.BadgeListResponse;
import com.kerryprops.kip.service.integral.webservice.response.SelfDefiningContextResource;
import com.kerryprops.kip.service.integral.webservice.response.SelfDefiningModuleResponse;
import com.kerryprops.kip.service.integral.webservice.response.ZIndexListResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */

@Slf4j
@RequestMapping("/customize-ui")
@RestController
@RequiredArgsConstructor
@Tag(name = "自定义页面列表相关api")
public class ZIndexController {

    private final MemberRegisterService memberRegisterService;
    private final ZIndexResourcesService zIndexResourcesService;
    private final CaffeineCacheService caffeineCacheService;
    private final ZIndexService zIndexService;

    /**
     * 支付宝支付即积分URL
     */
    private static final String ALI_AUTO_POINTS = "automatic/alipayAutoPoints";

    @GetMapping("/homePage")
    @Operation(summary="自定义UI页面-首页查询", method = "GET")
    public ZIndexListResponse getZIndexForHomePage(@RequestHeader("brandId") String brandId,
                                                   @RequestHeader("lbsId") String lbsId,
                                                   @CurrentUser LoginUser loginUser,
                                                   HttpServletRequest request) {
        String provider = this.getProvider(request);
        TbMemberAsset memberAsset = this.getMemberGrade(loginUser);
        // 查询首页配置
        CompletableFuture<ZIndexListResponse> listResponseFuture = caffeineCacheService.asyncGetHomePage(brandId, lbsId, CustomizePageTypeEnum.HOME_PAGE.getValue());
        // 查询品牌导览
        CompletableFuture<List<SelfDefiningContextResource>> brandLogoFuture = zIndexResourcesService.getBrandLogoByMallId(lbsId);
        CompletableFuture.allOf(listResponseFuture, brandLogoFuture).join();
        ZIndexListResponse listResponse = null;
        try {
            listResponse = listResponseFuture.get();
            List<SelfDefiningContextResource> brandLogoList = brandLogoFuture.get();
            if (CollectionUtils.isEmpty(listResponse.getModuleList())) {
                return null;
            }
            this.fixedAliMiniShow(listResponse, provider);
            log.info("LoginUser: {}", Objects.nonNull(memberAsset) ? memberAsset.getGrade() : null);
            // 处理悬浮球，品牌导览是否展示逻辑
            this.fixedFloatingBallLogic(listResponse, Objects.nonNull(memberAsset) ? memberAsset.getGrade() : null, brandLogoList);
        } catch (Exception e) {
            log.error("HomePage ListResponse: {}", e.getMessage());
        }
        return listResponse;
    }

    private String getProvider(HttpServletRequest request) {
        String provider = request.getHeader(IntegralConstant.PROVIDER);
        return ProviderEnum.getProvider(provider);
    }

    @GetMapping("/myPage")
    @Operation(summary="自定义UI页面-我的查询", method = "GET")
    public ZIndexListResponse getZIndexForMyPage(@CurrentUser LoginUser loginUser,
                                                 @RequestHeader("brandId") String brandId,
                                                 @RequestHeader("lbsId") String lbsId,
                                                 HttpServletRequest request) {
        String provider = this.getProvider(request);
        TbMemberAsset memberAsset = this.getMemberGrade(loginUser);
        // 查询我的页面的配置
        CompletableFuture<ZIndexListResponse> listResponseFuture = caffeineCacheService.asyncGetMyPage(brandId, lbsId, CustomizePageTypeEnum.MY_PAGE.getValue());
        // 我的徽章
        CompletableFuture<BadgeListResponse> badgeFuture = caffeineCacheService.asyncGetMyBadge(brandId, lbsId, Objects.nonNull(memberAsset) ? memberAsset.getVipcode() : "");
        CompletableFuture.allOf(listResponseFuture, badgeFuture).join();
        ZIndexListResponse myResponse = null;
        try {
            myResponse = listResponseFuture.get();
            if (Objects.isNull(myResponse)) {
                return null;
            }
            if (Objects.nonNull(myResponse.getShowBadge()) && myResponse.getShowBadge() == 1) {
                myResponse.setMyBadge(badgeFuture.get());
            }
            // 支付宝小程序是否显示处理
            this.fixedAliMiniShow(myResponse, provider);
            // 处理悬浮球是否展示逻辑
            this.fixedFloatingBallLogic(myResponse, Objects.nonNull(memberAsset) ? memberAsset.getGrade() : null, Collections.emptyList());
            // 将最外层数据放到myPage中，因为3.11的数据格式修改
            this.setFloatingPage(myResponse);
        } catch (Exception e) {
            log.error("MyPage ListResponse: {}", e.getMessage());
        }
        return myResponse;
    }

    private void setFloatingPage(ZIndexListResponse myResponse) {
        if (CollectionUtils.isEmpty(myResponse.getModuleList())) {
            return;
        }
        for (SelfDefiningModuleResponse myModule : myResponse.getModuleList()) {
            myModule.setFloatingBtnShow(myResponse.getFloatingBtnShow());
            myModule.setFloatingBtnUrl(myResponse.getFloatingBtnUrl());
            myModule.setMyAdShow(myResponse.getMyAdShow());
            myModule.setMyAdUrl(myResponse.getMyAdUrl());
            myModule.setShowLineNum(myResponse.getShowLineNum());
        }
    }

    /**
     * 获取登录用户的会员等级
     * @param loginUser loginUser
     * @return string string
     */
    private TbMemberAsset getMemberGrade(LoginUser loginUser) {
        if (Objects.isNull(loginUser)) {
            return null;
        }
        return memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
    }

    /**
     * 过滤掉不展示在支付宝小程序的功能
     * @param listResponse listResponse
     */
    private void fixedAliMiniShow(ZIndexListResponse listResponse, String provider) {
        List<SelfDefiningModuleResponse> modules = new ArrayList<>();
        log.info("PROVIDER: {}", provider);
        // 系统当前时间
        Date now = new Date();
        if (ProviderEnum.ALI.getVal().equalsIgnoreCase(provider)) {
            for (SelfDefiningModuleResponse module: listResponse.getModuleList()) {
                // 做展示时间的过滤
                module.setImgList(this.filterImgListShowTime(module.getModuleSign(), module.getImgList(), now));
                // 配置了展示时间的模块，如果imgList为空，则不返回给前端
                if (ZIndexModuleSignEnum.hasShowTimeModule(module.getModuleSign()) && CollectionUtils.isEmpty(module.getImgList())) {
                    continue;
                }
                if (ZIndexModuleSignEnum.isBallOrWaterFall(module.getModuleSign())) {
                    module.setImgList(module.getImgList().stream().filter(item -> IntegralConstant.SHOW_ALI_MINI.equals(item.getShowAliMini())).toList());
                    if (CollectionUtils.isEmpty(module.getImgList())) {
                        continue;
                    }
                    modules.add(module);
                } else if (ZIndexModuleSignEnum.MY.getVal().equals(module.getModuleSign())) {
                    if (CollectionUtils.isNotEmpty(module.getImgList())) {
                        module.setImgList(module.getImgList().stream().filter(it -> IntegralConstant.SHOW_ALI_MINI.equals(it.getShowAliMini())).toList());
                    }
                    if (CollectionUtils.isNotEmpty(module.getMultipleLineList())) {
                        module.setMultipleLineList(module.getMultipleLineList().stream().filter(it -> IntegralConstant.SHOW_ALI_MINI.equals(it.getShowAliMini())).toList());
                    }
                    if (0 == listResponse.getFloatingBtnShow() || module.getShowAliMini() == 0) {
                        module.setSuspensionList(Collections.emptyList());
                    } else {
                        if (CollectionUtils.isNotEmpty(module.getSuspensionList())) {
                            module.setSuspensionList(module.getSuspensionList().stream().filter(it -> IntegralConstant.SHOW_ALI_MINI.equals(it.getShowAliMini())).toList());
                        }
                    }
                    modules.add(module);
                } else if (IntegralConstant.SHOW_ALI_MINI.equals(module.getShowAliMini())) {
                    modules.add(module);
                }
            }
        } else {
            for (SelfDefiningModuleResponse module: listResponse.getModuleList()) {
                // 做展示时间的过滤
                module.setImgList(this.filterImgListShowTime(module.getModuleSign(), module.getImgList(), now));
                // 配置了展示时间的模块，如果imgList为空，则不返回给前端
                if (ZIndexModuleSignEnum.hasShowTimeModule(module.getModuleSign()) && CollectionUtils.isEmpty(module.getImgList())) {
                    continue;
                }
                if (ZIndexModuleSignEnum.MY.getVal().equals(module.getModuleSign())) {
                    if (0 == listResponse.getFloatingBtnShow()) {
                        module.setSuspensionList(Collections.emptyList());
                    }
                }
                // 微信小程序过滤支付宝支付即积分配置（SCRM-8093：Tick 4.2提的优化）
                if (CollectionUtils.isNotEmpty(module.getImgList())) {
                    module.setImgList(module.getImgList().stream().filter(item -> (StringUtils.isBlank(item.getInsideUrl()) || !item.getInsideUrl().contains(ALI_AUTO_POINTS))).toList());
                }
                // 微信小程序过滤支付宝支付即积分配置（SCRM-8093：Tick 4.2提的优化）
                if (CollectionUtils.isNotEmpty(module.getMultipleLineList())) {
                    module.setMultipleLineList(module.getMultipleLineList().stream().filter(item -> (StringUtils.isBlank(item.getInsideUrl()) || !item.getInsideUrl().contains(ALI_AUTO_POINTS))).toList());
                }
                // 微信小程序过滤支付宝支付即积分配置（SCRM-8093：Tick 4.2提的优化）
                if (CollectionUtils.isNotEmpty(module.getSuspensionList())) {
                    module.setSuspensionList(module.getSuspensionList().stream().filter(item -> (StringUtils.isBlank(item.getInsideUrl()) || !item.getInsideUrl().contains(ALI_AUTO_POINTS))).toList());
                }
                modules.add(module);
            }
        }
        listResponse.setModuleList(modules);
    }

    /**
     * 过滤：title loop screen advertisement四个模块配置的展示时间
     * @param moduleSign moduleSign
     * @param imgList imgList
     * @param now: 系统当前时间
     * @return List
     */
    private List<SelfDefiningContextResource> filterImgListShowTime(String moduleSign, List<SelfDefiningContextResource> imgList, Date now) {
        if (CollectionUtils.isEmpty(imgList)) {
            return Collections.emptyList();
        }
        if (!ZIndexModuleSignEnum.hasShowTimeModule(moduleSign)) {
            return imgList;
        }
        List<SelfDefiningContextResource> newImgList = new ArrayList<>(imgList.size());
        List<String> tmpList = null;
        for (SelfDefiningContextResource resource: imgList) {
            List<String> timeList = StringUtils.isBlank(resource.getShowTime()) ? Collections.emptyList() : CharSequenceUtil.split(resource.getShowTime(), CommonSeparators.WELL_SEPARATOR);
            if (CollectionUtils.isEmpty(timeList)) {
                newImgList.add(resource);
                continue;
            }
            for (String time: timeList) {
                tmpList = CharSequenceUtil.split(time, CommonSeparators.COMMA_SEPARATOR);
                if (CollectionUtils.isEmpty(tmpList) || tmpList.size() != 2) {
                    continue;
                }
                // 比较展示时间与系统当前时间的大小，在区间内的，则展示
                if (DateUtil.parseDateTime(tmpList.get(0)).before(now) && DateUtil.parseDateTime(tmpList.get(1)).after(now)) {
                    newImgList.add(resource);
                    break;
                }
            }
        }
        return newImgList;
    }

    /**
     * 处理悬浮球展示逻辑
     * @param listResponse listResponse
     * @param grade grade
     */
    private void fixedFloatingBallLogic(ZIndexListResponse listResponse, String grade, List<SelfDefiningContextResource> brandLogoList) {
        if (StringUtils.isBlank(grade) && CollectionUtils.isEmpty(listResponse.getModuleList())) {
            return;
        }
        List<SelfDefiningModuleResponse> allModules = new ArrayList<>();
        if (StringUtils.isBlank(grade)) {
            for (SelfDefiningModuleResponse tmpModule: listResponse.getModuleList()) {
                if (ZIndexModuleSignEnum.SUSPEN.getVal().equals(tmpModule.getModuleSign())) {
                    continue;
                }
                if (ZIndexModuleSignEnum.MY.getVal().equals(tmpModule.getModuleSign())) {
                    tmpModule.setSuspensionList(Collections.emptyList());
                } else {
                    // 设置品牌导览
                    this.setBrandLogoList(tmpModule, brandLogoList);
                }
                allModules.add(tmpModule);
            }
            listResponse.setModuleList(allModules);
            return;
        }
        log.info("fixedFloatingBallLogic: {}", listResponse.getMallid());
        // 是我的页面，判断悬浮球是否展示
        if (CustomizePageTypeEnum.MY_PAGE.getValue().equals(listResponse.getType())) {
            for (SelfDefiningModuleResponse tmpModule: listResponse.getModuleList()) {
                if (ZIndexModuleSignEnum.MY.getVal().equals(tmpModule.getModuleSign())) {
                    if (StringUtils.isBlank(listResponse.getMyShowGrade()) || !listResponse.getMyShowGrade().contains(grade)) {
                        tmpModule.setSuspensionList(Collections.emptyList());
                    }
                } else {
                    // 设置品牌导览
                    this.setBrandLogoList(tmpModule, brandLogoList);
                }
                allModules.add(tmpModule);
            }
        } else {
            for (SelfDefiningModuleResponse tmpModule: listResponse.getModuleList()) {
                // 查询等级对应的虚浮icon资源列表
                if (ZIndexModuleSignEnum.SUSPEN.getVal().equals(tmpModule.getModuleSign())) {
                    List<SelfDefiningContextResource> susList = this.getSusListByGrade(grade, tmpModule.getSuspensionList());
                    //悬浮球
                    tmpModule.setImgList(susList);
                    tmpModule.setSuspensionList(Collections.emptyList());
                } else {
                    // 设置品牌导览
                    this.setBrandLogoList(tmpModule, brandLogoList);
                }
                allModules.add(tmpModule);
            }
        }
        listResponse.setModuleList(allModules);
    }

    private List<SelfDefiningContextResource> getSusListByGrade(String grade, List<SelfDefiningContextResource> suspensionList) {
        if (CollectionUtils.isEmpty(suspensionList)) {
            return Collections.emptyList();
        }
        return suspensionList.stream().filter(it -> CollectionUtils.isNotEmpty(it.getMemberGrade())).filter(it ->it.getMemberGrade().contains(grade)).toList();
    }

    /**
     * 设置品牌导览列表
     * @param tmpModule tmpModule
     * @param brandLogoList brandLogoList
     */
    private void setBrandLogoList(SelfDefiningModuleResponse tmpModule, List<SelfDefiningContextResource> brandLogoList) {
        if (!ZIndexModuleSignEnum.BRANDLOGO.getVal().equals(tmpModule.getModuleSign())) {
            return;
        }
        if (CollectionUtils.isNotEmpty(brandLogoList)) {
            brandLogoList.forEach(logo -> logo.setModuleId(tmpModule.getId()));
        }
        tmpModule.setImgList(brandLogoList);
    }

    @GetMapping("/getCustomerPage")
    @Operation(summary="自定义UI页面-查询自定义页面", method = "GET")
    public ZIndexListResponse getCustomerPage(@RequestHeader("brandId") String brandId,
                                              @RequestHeader("lbsId") String lbsId,
                                              @RequestParam(value = "id") String id,
                                              @CurrentUser LoginUser loginUser,
                                              HttpServletRequest request) {
        String provider = this.getProvider(request);
        TbMemberAsset memberAsset = this.getMemberGrade(loginUser);
        CompletableFuture<List<SelfDefiningContextResource>> brandLogoFuture = zIndexResourcesService.getBrandLogoByMallId(lbsId);
        ZIndexListResponse resp = zIndexService.getPageById(brandId, lbsId, id);
        CompletableFuture.allOf(brandLogoFuture).join();
        try {
            List<SelfDefiningContextResource> brandLogoList = brandLogoFuture.get();
            if (Objects.isNull(resp) || CollectionUtils.isEmpty(resp.getModuleList())) {
                return null;
            }
            this.fixedAliMiniShow(resp, provider);
            this.fixedFloatingBallLogic(resp, Objects.nonNull(memberAsset) ? memberAsset.getGrade() : null, brandLogoList);
            this.setFloatingPage(resp);
            if (resp.getType().equals("2") && resp.getOpenFlag().equals("1")) {
                resp.setMyPageId(resp.getId());
            }
        } catch (Exception e) {
            log.info("getCustomerPage {}", e.getMessage());
        }
        return resp;
    }

}
