package com.kerryprops.kip.service.integral.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> zhangxiliang
 * Created Date - 06/25/2024 16:49
 **********************************************************************************************************************/

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "abnormal.points.review")
public class NotifyEmailProperties {

    private String crmAdminUrl;

    private String crmPhotoPointsUrl;

}
