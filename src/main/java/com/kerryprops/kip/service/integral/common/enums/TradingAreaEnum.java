package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝商圈枚举类
 * @createDate 2022/9/20
 * @updateDate 2022/9/20
 */
@Getter
public enum TradingAreaEnum {


    /**
     * 北京商圈或口碑
     */
    BEIJING("BKC", "B"),

    /**
     * 静安
     */
    JINGAN("JAKC", "A"),

    /**
     * 杭州嘉里中心
     */
    HANGZHOU ("HKC", "C"),

    /**
     * 浦东嘉里中心
     */
    PUDONG("KP", "G"),

    /**
     * 天津嘉里中心
     */
    TIANJIN("TKC", "E"),

    /**
     * 前海嘉里中心
     */
    QIANHAI("QHKC", "F"),

    /**
     * 深圳建设广场
     */
    SZKP("SZKP", "K"),

    /**
     * 沈阳嘉里中心
     */
    SHENYANG("SYKC", "H"),

    /**
     * 合集&企业坊
     */
    HJQYF("HJQYF", "J"),

    /**
     * 沙箱
     */
    SANDBOX("SB", "S"),
    ;

    private final String abbreviation;
    private final String code;

    TradingAreaEnum(String abbreviation, String code) {
        this.abbreviation = abbreviation;
        this.code = code;
    }

    public static TradingAreaEnum getByAbbr(String abbreviation) {
        return Arrays.stream(TradingAreaEnum.values()).filter(p -> p.getAbbreviation().equals(abbreviation)).findFirst().orElse(null);
    }

    public static boolean isJakcOrKpc(String abbreviation) {
        return Stream.of(TradingAreaEnum.JINGAN, TradingAreaEnum.PUDONG).anyMatch(it -> StringUtils.equals(it.getAbbreviation(), abbreviation));
    }

}
