package com.kerryprops.kip.service.integral.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/11/2023 15:56
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntegralAdjustEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    // 集团编号
    private String groupId;

    // 调整类型 A->普通调整， S->销售调整
    private String type;

    //商场
    private String mallId;

    // 会员手机号
    private String mobile;

    // 会员编号
    private String vipCode;

    // 会员当前积分
    private BigDecimal currentIntegral;

    // 红星积分调整数量
    private BigDecimal hxIntegral;

    // 消费金额(销售录入)  积分调整数量(会员信息查询)
    private BigDecimal number;

    // 积分调整原因
    private String remark;

    // 积分调整原因
    private String remarkName;

    // 销售单号(销售调整)
    private String sellNo;

    // 收银机号(销售调整)
    private String cashNo;

    // 店铺号(销售调整)
    private String shopId;

    // 店铺名称(销售调整)
    private String shopName;

    // 销售日期(销售调整)
    private Date sellDate;

    // 额外积分
    private BigDecimal  extraIntegral;

    // 积分规则
    private String integralRule;

    // 优惠金额.
    private BigDecimal discounts;

    // 销售金额对应的积分
    private BigDecimal numberToIntegral;

    // 上传图片积分
    private String imageUrl;

    // 0pc端积分 1服务台积分
    private String channelSource;

    // 调整前积分
    private BigDecimal beforeIntegral;

    //会员调整前等级
    private String beforeGrade;

    // 以下字段数据库不需要，但是查询和展示需要

    // 会员编号(爱情海专用，可以不传）
    private String memberId;

    // 多个会员编号
    private List<String> memberIds;

    // 会员当前等级
    private String grade;

    // 查询是否可否可退货 1：第一次查询 2：第二次查询
    private String queryType;

    // 销售日期  ipad端做转化使用
    private String sellTime;

    // 销售类型
    private String saleType;

    // 销售单子中的消费金额
    private BigDecimal useMoney;

    // 销售流水号
    private String expDate;

    // 审核通过 0通过 1不通过
    private String approved;

    private String status;

    @Schema(name = "创建时间")
    @JsonFormat(timezone="GMT+8", pattern="yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date createDate;

    @Schema(name = "创建人")
    private String creator;

    @Schema(name = "修改时间")
    @JsonFormat(timezone="GMT+8", pattern="yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date updateDate;

    @Schema(name = "更新人")
    private String updater;
}
