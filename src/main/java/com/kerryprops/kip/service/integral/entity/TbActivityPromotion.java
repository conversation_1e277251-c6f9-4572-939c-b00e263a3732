package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * tb_activity_promotion
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_activity_promotion")
public class TbActivityPromotion implements Serializable {

    private String id;

    /**
     * 规则名字
     */
    private String name;

    /**
     *  积分模式(1:多倍积分   2：固定积分) 
     */
    private String module;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 营销积分过期时间
     */
    private Date promotionBonusExpirationDate;

    /**
     * 商场编号
     */
    private String mallId;

    /**
     * 店铺id
     */
    private String shopId;

    /**
     * 关系类型（叠加或者取最高 必填项二选一）叠加  0    最高   1
     */
    private String type;

    /**
     * 倍数
     */
    private BigDecimal times;

    /**
     * 积分
     */
    private Integer bonus;

    /**
     * 集团id
     */
    private String groupId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 人群包id，用,分隔
     */
    private String crowdId;

    /**
     * 满足的条件
     */
    private transient List<List<TbActivityPromotionCondition>> conditions;

    /**
     * 首次入会
     */
    private transient List<TbActivityPromotionJoinvip> vips;

}