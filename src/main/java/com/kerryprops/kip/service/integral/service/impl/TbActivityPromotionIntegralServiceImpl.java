package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotion;
import com.kerryprops.kip.service.integral.entity.TbActivityPromotionIntegral;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.mapper.TbActivityPromotionIntegralMapper;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.service.TbActivityPromotionIntegralService;
import com.kerryprops.kip.service.integral.service.TbSaleMatchedPromotionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/23/2022 11:42
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbActivityPromotionIntegralServiceImpl extends ServiceImpl<TbActivityPromotionIntegralMapper, TbActivityPromotionIntegral> implements TbActivityPromotionIntegralService {

    private final TbActivityPromotionIntegralMapper tbActivityPromotionIntegralMapper;
    private final TbSaleMatchedPromotionService tbSaleMatchedPromotionService;

    /**
     * @param matchRules
     * @param dto
     * @param type 1: 多倍积分 + 最高, 2: 固定积分 + 最高
     */
    @Async
    @Override
    public void savePromotionIntegral(List<TbActivityPromotion> matchRules, SalesAutoPointsDto dto, int type, int basePoints) {
        if (CollectionUtils.isEmpty(matchRules)) {
            return;
        }
        TbActivityPromotion promotion;
        if (1 == type) {
            promotion = matchRules.stream().filter(it -> StringUtils.equals(it.getModule(), "1")).filter(it -> StringUtils.equals(it.getType(), "1"))
                    .filter(it -> Objects.nonNull(it.getTimes())).max(Comparator.comparing(TbActivityPromotion::getTimes)).orElse(null);
        } else {
            promotion = matchRules.stream().filter(it -> StringUtils.equals(it.getModule(), "2")).filter(it -> StringUtils.equals(it.getType(), "1"))
                    .filter(it -> Objects.nonNull(it.getBonus())).max(Comparator.comparing(TbActivityPromotion::getBonus)).orElse(null);
        }

        // 多倍/固定 + 叠加
        List<TbActivityPromotion> djPromotions = matchRules.stream().filter(it -> StringUtils.equals(it.getType(), "0")).collect(Collectors.toList());
        if (Objects.nonNull(promotion)) {
            djPromotions.add(promotion);
        }
        if (CollectionUtils.isNotEmpty(djPromotions)) {
            List<TbActivityPromotionIntegral> records = new ArrayList<>(djPromotions.size());
            Date date = new Date();
            TbActivityPromotionIntegral integral = null;
            for (TbActivityPromotion promotionDto: djPromotions) {
                integral = new TbActivityPromotionIntegral();
                integral.setPointsId(dto.getIntegralAdjustId());
                integral.setPromotionId(promotionDto.getId());
                integral.setPromotionName(promotionDto.getName());
                integral.setVipcode(dto.getMember().getVipcode());
                integral.setMobile(dto.getMember().getMobile());
                integral.setSellNo(dto.getSaleNo());
                integral.setSaleMoney(NumberUtil.toBigDecimal(dto.getAmount()));
                integral.setRemark(dto.getRemarkName());
                integral.setMallId(dto.getMallId());
                integral.setExpireTime(promotionDto.getPromotionBonusExpirationDate());
                integral.setCreateDate(date);
                integral.setUpdateDate(date);
                Integer points = StringUtils.equals("2", promotionDto.getModule()) ? promotionDto.getBonus() : (int) ((promotionDto.getTimes().doubleValue() - 1) * basePoints);
                integral.setIntegral(points);
                integral.setEnableIntegral(points);
                // 填充默认值
                integral.fillDefaultVal();
                records.add(integral);
            }
            tbActivityPromotionIntegralMapper.saveBatch(records);
        }
        // 该销售满足的营销积分活动
        tbSaleMatchedPromotionService.saveRecord(djPromotions, dto);
    }

    @Async
    @Override
    public void updatePromotionIntegral(TbSalesDetail detail) {
        List<TbActivityPromotionIntegral> enableEntity = tbActivityPromotionIntegralMapper.getSaleMatchedActivityPromotions(detail.getVipcode(), detail.getOrderNo());
        if(CollectionUtils.isEmpty(enableEntity)) {
            return;
        }
        for (TbActivityPromotionIntegral entity : enableEntity) {
            if (entity.getEnableIntegral() <= 0) {
                continue;
            }
            entity.setEnableIntegral(0);
            entity.setUpdateDate(new Date());
            // 填充默认值
            entity.fillDefaultVal();
            tbActivityPromotionIntegralMapper.updateById(entity);
        }
    }

}
