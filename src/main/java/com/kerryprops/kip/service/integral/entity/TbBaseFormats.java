package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_base_formats
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_base_formats")
public class TbBaseFormats implements Serializable {
    private Long id;

    /**
     * 父id
     */
    private Long pid;

    /**
     * 集团id-对应hive-brandId
     */
    private String groupId;

    /**
     * 业态编码
     */
    private String formatCode;

    /**
     * 业态名称
     */
    private String formatName;

    /**
     * 业态层级1:业态;2:业态细分;3:业种
     */
    private String formatLevel;

    /**
     * 状态 0有效1失效
     */
    private String delflag;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 该字段无用-备注
     */
    private String remaker;

}