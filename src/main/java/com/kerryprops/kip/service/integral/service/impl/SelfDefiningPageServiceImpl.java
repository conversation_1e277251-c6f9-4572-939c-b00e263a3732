package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.SelfDefiningPage;
import com.kerryprops.kip.service.integral.mapper.SelfDefiningPageMapper;
import com.kerryprops.kip.service.integral.service.SelfDefiningPageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description 自定义页面列表
 * <AUTHOR>
 * @date 2022-12-15
 */

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class SelfDefiningPageServiceImpl extends ServiceImpl<SelfDefiningPageMapper, SelfDefiningPage> implements SelfDefiningPageService {

    private final SelfDefiningPageMapper selfDefiningPageMapper;

    @Override
    public SelfDefiningPage getZIndex(String groupId, String mallId, String type) {
        return selfDefiningPageMapper.findHomePage(groupId, mallId, type);
    }

    @Override
    public SelfDefiningPage getPageById(String groupId, String mallId, String id) {
        return selfDefiningPageMapper.getPageById(groupId, mallId, id);
    }
}