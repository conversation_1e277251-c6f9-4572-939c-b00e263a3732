package com.kerryprops.kip.service.integral.webservice.resource;

import com.baomidou.mybatisplus.annotation.TableField;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/23/2023 13:39
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbCashOutConfigResource implements Serializable {

    private Long id;

    @NotBlank(message = "groupId不能为空")
    private String groupId;

    @NotBlank(message = "mallId不能为空")
    private String mallId;

    @NotBlank(message = "name不能为空")
    private String name;

    @TableField("business_type")
    private String businessType;

    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    @NotBlank(message = "结束不能为空")
    private String endTime;

    @NotBlank(message = "店铺号不能为空")
    private String shopNo;

    @NotNull(message = "isLimit不能为空")
    private Integer isLimit;

    private BigDecimal money;

    private String creator;

}