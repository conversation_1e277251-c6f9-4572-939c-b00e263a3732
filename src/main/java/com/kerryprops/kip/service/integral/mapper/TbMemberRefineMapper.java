package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberRefine;
import org.apache.ibatis.annotations.Param;
/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 09/14/2023 11:16
 **********************************************************************************************************************/
public interface TbMemberRefineMapper extends BaseMapper<TbMemberRefine> {

    /**
     * findByMallId
     * @param mallId mallId
     * @return dto dto
     */
    TbMemberRefine findByMallId(@Param("mallId") String mallId);

}