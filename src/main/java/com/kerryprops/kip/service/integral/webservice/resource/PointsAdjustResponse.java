package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/15/2023 16:48
 **********************************************************************************************************************/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema( description = "积分调整返回调整记录id类")
public class PointsAdjustResponse implements Serializable {

    @Schema( description = "积分调整记录ID")
    private String pointsAdjustId;

    @Schema( description = "变更积分数")
    private Integer changePointsNum;
    
}
