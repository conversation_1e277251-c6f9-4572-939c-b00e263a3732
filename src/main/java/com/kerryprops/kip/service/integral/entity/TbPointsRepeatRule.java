package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_integral_repeat_rule
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_points_repeat_rule")
public class TbPointsRepeatRule implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 集团ID
     */
    private String groupId;

    /**
     * 商场编号
     */
    private String mallId;

    /**
     * 状态(1:有效，0或其他：无效)
     */
    private Integer status;

    /**
     * 销售时间差，单位分钟
     */
    private Integer salesTimeDifference;

    /**
     * 去重条件，多个值以英文逗号分隔，1:店铺，2:金额，3:销售时间，4:会员号
     */
    private String repeatRule;

    /**
     * 是否配置白名单，0:否，1:是
     */
    @TableField("white_list")
    private Integer whiteList;

    /**
     * 白名单店铺列表，用英文逗号分隔
     */
    @TableField("shop_no")
    private String shopNo;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private String updateUser;
}