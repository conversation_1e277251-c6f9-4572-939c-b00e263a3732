package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_points_review
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_points_review")
public class TbPhotoReview implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 会员号
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 商场编号
     */
    @TableField("mall_id")
    private String mallId;

    /**
     * 流水号
     */
    @TableField("serial_num")
    private String serialNum;

    /**
     * 金额
     */
    @TableField("money")
    private Double money;

    /**
     * 获得积分
     */
    @TableField("bonus")
    private Integer bonus;

    /**
     * 积分方式（1.小票；2.二维码; 3.CRM后台录入）
     *   修改后  4 小程序扫码  3 小票审核  1CRM后台录入
     */
    @TableField("type")
    private String type;

    /**
     * 积分审核图片地址
     */
    @TableField("image_url")
    private String imageUrl;

    @TableField("qrcode")
    private String qrcode;

    /**
     * 是OCR拍照积分的话，该字段对应合合返回的错误码
     */
    @TableField("description")
    private String description;

    /**
     * 未通过原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 上传时间
     */
    @TableField("upload_date")
    private Date uploadDate;

    /**
     * 对应枚举值：PhotoReviewStatusEnum
     */
    @TableField("status")
    private String status;

    /**
     * 时间戳
     */
    @TableField("timestamp")
    private String timestamp;

    /**
     * 店铺id
     */
    @TableField("shop_no")
    private String shopNo;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 交易日期
     */
    @TableField("trading_date")
    private Date tradingDate;

    /**
     * 审核时间
     */
    @TableField("check_time")
    private Date checkTime;

    /**
     * 审核人名字
     */
    @TableField("check_name")
    private String checkName;

    /**
     * 集团id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 创建人id
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 修改人id
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 业态组合
     */
    @TableField("format_id")
    private String formatId;

    /**
     * 业态名称
     */
    @TableField("format_name")
    private String formatName;

    /**
     * 积分审核记录id
     */
    @TableField("integral_adjust_id")
    private String integralAdjustId;

    /**
     * 变更前积分
     */
    @TableField("org_points")
    private Integer orgPoints;

    /**
     * 变更前等级
     */
    @TableField("org_grade")
    private String orgGrade;

    @TableField(exist = false)
    private String taskId;

}