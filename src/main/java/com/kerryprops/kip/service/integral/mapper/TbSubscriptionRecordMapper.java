package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.SubscriptionRecord;
import com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TbSubscriptionRecordMapper extends BaseMapper<SubscriptionRecord> {

    /**
     * querySubscriptionDto
     * @param query query
     * @return SubscriptionRecord dto
     */
    SubscriptionRecord querySubscriptionDto(SubscriptionRecordQueryDto query);

    /**
     * querySubscriptionList
     * @param query query
     * @return list list
     */
    List<SubscriptionRecord> querySubscriptionList(SubscriptionRecordQueryDto query);

    /**
     * querySubscriptionUserIdList
     * @param query query
     * @return list list
     */
    List<String> querySubscriptionUserIdList(SubscriptionRecordQueryDto query);
}