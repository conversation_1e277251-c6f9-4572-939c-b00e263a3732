package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.SysDict;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/14/2022 09:27
 **********************************************************************************************************************/
public interface SysDictService extends IService<SysDict> {

    CompletableFuture<Map<String, String>> findDictNames(List<String> dictTypes);

    SysDict findByDictType(String dictType);

    List<SysDict> getByTypes(List<String> types);

}
