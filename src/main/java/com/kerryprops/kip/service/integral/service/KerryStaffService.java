package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.model.dto.CountryCodeDto;
import com.kerryprops.kip.service.integral.model.dto.CrmSysUserDto;
import com.kerryprops.kip.service.integral.model.dto.CustomerThirdPartyDto;
import com.kerryprops.kip.service.integral.model.dto.CustomerUserDto;
import com.kerryprops.kip.service.integral.model.vo.KipMemberVO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 10/21/2022 16:37
 **********************************************************************************************************************/
public interface KerryStaffService {

    CustomerUserDto findByKipUserId(String kipUserId);

    CustomerUserDto findByMobile(String mobile);

    CustomerUserDto findByOpenId(String openId);

    String modifyProfileInfo(KipMemberVO memberVO);

    List<CustomerUserDto> findByKipUserIds(List<String> kipUserIds);

    Map<String, CustomerUserDto> getMapByKipUserIds(List<String> kipUserIds);

    /**
     * 从profile-service查询用户信息
     * @param kipUserIds kipUserIds
     * @return map map
     */
    Map<String, CustomerUserDto> getMapByMobiles(List<String> kipUserIds);

    /**
     * 从profile-service查询用户信息
     * @param kipUserIds kipUserIds
     * @return map map
     */
    CompletableFuture<Map<String, CustomerUserDto>> getMapFutureByMobile(List<String> kipUserIds);

    /**
     * 通过手机号 + AppId查询用户openId信息
     * @param mobile
     * @param appId
     * @return
     */
    CustomerThirdPartyDto getWxInfoByMobileAndAppId(String mobile, String appId);

    /**
     * 查询区号对应的信息
     * @param areaCode
     * @return
     */
    CountryCodeDto getByAreaCodeDetail(String areaCode);


    /**
     * 查询区号对应的信息
     * @param userIds userIds
     * @return list list
     */
    List<CrmSysUserDto> getUserList(List<Long> userIds);

}
