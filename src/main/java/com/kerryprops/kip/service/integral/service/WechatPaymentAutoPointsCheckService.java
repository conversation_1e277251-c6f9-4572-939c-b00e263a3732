package com.kerryprops.kip.service.integral.service;

import com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 09/08/2023 10:55
 **********************************************************************************************************************/

@Slf4j
@Service
@RequiredArgsConstructor
public class WechatPaymentAutoPointsCheckService {

    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;
    private static final int BATCH_SIZE = 200;

    @Async
    public void checkMallWechatAutoPointsStatus(MallItem mallItem) {
        log.info("CheckMallWechatAutoPointsStatus Begin: {}", mallItem.getMallId());
        Long id = 0L;
        while (true) {
            List<TbInsensatePointsAuthRecord> records = tbInsensatePointsAuthRecordService.getMallBatchData(mallItem.getMallId(), id, BATCH_SIZE);
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            for (TbInsensatePointsAuthRecord authRecord: records) {
                // 检查微信无感积分授权状态
                tbInsensatePointsAuthRecordService.checkAuthStatus(authRecord, mallItem);
            }
            id = records.get(records.size() - 1).getId();
        }
        log.info("CheckMallWechatAutoPointsStatus End: {}", mallItem.getMallId());
    }

}
