package com.kerryprops.kip.service.integral.webservice.resource;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/09/2022 12:07
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( description = "积分清零文案提示响应类")
public class IntegralClearShowResponse implements Serializable {

    @Schema( description = "是否展示(0: 是, 1: 否)")
    private Integer isShow;

    @Schema( description = "展示文案内容")
    private String content;

}
