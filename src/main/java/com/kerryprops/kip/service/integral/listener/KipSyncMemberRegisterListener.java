package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsAuthRecordService;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import com.kerryprops.kip.service.integral.service.TbOpenCardFailRecordService;
import com.kerryprops.kip.service.integral.webservice.resource.KipSyncMemberResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.*;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/22/2023 11:49
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class KipSyncMemberRegisterListener {

    private final TbMemberAssetService tbMemberAssetService;
    private final TbInsensatePointsAuthRecordService tbInsensatePointsAuthRecordService;

    /**
     * 自定义线程池去消费KIP同步过来的会员注册信息
     */
    private static final ExecutorService executorService =  new ThreadPoolExecutor(5, 10,5,
            TimeUnit.MINUTES, new ArrayBlockingQueue<>(5, true), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * KIP同步会员信息至CRM
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.KIP_SYNC_MEMBER_CRM, containerFactory = "crmContainerFactory")
    public void kipSyncMemberCrmProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("KIP同步会员信息至CRM数据为空.");
            return;
        }
        log.info("KipSyncMemberRegisterListener: {}" , msg);
        KipSyncMemberResource resource = JsonUtils.stringToObj(msg, KipSyncMemberResource.class);
        if (Objects.isNull(resource)) {
            log.error("MQ信息转对象失败-{}", msg);
            return;
        }
        // 是否来源于会员邀约
        if (Objects.isNull(resource.getIsInvitation())) {
            resource.setIsInvitation(Boolean.FALSE);
        }
        try {
            executorService.execute(() -> {
                tbMemberAssetService.kipSyncMemberCrmProcess(resource);
                // 检查微信支付即积分授权状态是否有效
                tbInsensatePointsAuthRecordService.recheckWxAuthRecordByKipUserId(resource.getUserId());
            });
        } catch (Exception e) {
            log.error("KIP同步会员信息至CRM异常", e);
        }
    }

}
