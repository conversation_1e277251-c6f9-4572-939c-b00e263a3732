//package com.kerryprops.kip.service.integral.service.promotion.impl;
//
//import com.kerryprops.kip.service.integral.common.IntegralConstant;
//import com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip;
//import com.kerryprops.kip.service.integral.entity.TbActivityPromotioncondition;
//import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
//import com.kerryprops.kip.service.integral.service.promotion.PromotionRule;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 02/17/2023 12:27
// **********************************************************************************************************************/
//
//@Slf4j
//@Component
//public class AlipayAutoPointsPromotionRule implements PromotionRule {
//
//    @Override
//    public String getRuleType() {
//        return "10";
//    }
//
//    @Override
//    public boolean checkRule(TbActivityPromotioncondition condition, SalesAutoPointsDto dto, Date saleDate, List<TbActivityPromotionJoinvip> vips) {
//        return Objects.equals(dto.getRemark(), IntegralConstant.ALIPAY_POINTS);
//    }
//}
