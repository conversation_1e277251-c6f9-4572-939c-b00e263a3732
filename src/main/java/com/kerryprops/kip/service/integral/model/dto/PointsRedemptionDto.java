package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/21/2024 11:43
 **********************************************************************************************************************/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointsRedemptionDto implements Serializable {

    /**
     * 最大可用金额
     */
    private int amount;

    /**
     * 店铺抵现比例-金额设置
     */
    private BigDecimal money;

    /**
     * 店铺抵现比例-积分设置
     */
    private BigDecimal pointNum;

}
