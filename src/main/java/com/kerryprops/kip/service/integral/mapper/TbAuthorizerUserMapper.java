package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbAuthorizerUser;
import org.apache.ibatis.annotations.Param;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 11/14/2024 14:04
 **********************************************************************************************************************/
public interface TbAuthorizerUserMapper extends BaseMapper<TbAuthorizerUser> {

    TbAuthorizerUser getAuthorizerUserByUser(@Param("user") String user);

}
