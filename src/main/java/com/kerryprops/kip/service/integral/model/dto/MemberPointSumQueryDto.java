package com.kerryprops.kip.service.integral.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/09/2022 11:28
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberPointSumQueryDto implements Serializable {

    private String groupId;

    private String mallId;

    private String vipcode;

    private String startDate;

    private String endDate;

    /**
     * 积分类型: 0/null: 所有积分， 1: 收入积分，2: 支出积分
     */
    private Integer type;

}
