package com.kerryprops.kip.service.integral.common.enums;
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 终端类型枚举类
 * @createDate 2022/8/29
 */
public enum EndpointTypeEnum {
    /**
     * A端
     */
    A("A"),

    /**
     * S端
     */
    S("S");


    private final String type;
    EndpointTypeEnum(String str) {
        this.type = str;
    }

    @Override
    public String toString() {
        return type;
    }
}
