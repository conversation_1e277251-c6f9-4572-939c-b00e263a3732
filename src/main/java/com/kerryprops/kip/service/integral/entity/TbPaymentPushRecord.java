package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 微信、支付宝推送记录表
 * <AUTHOR>
 * @date 2022-09-14
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbPaymentPushRecord implements Serializable {

    /**
    * 符合规范的唯一主键id
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 订单号
    */
    private String orderNo;

    /**
    * 交易类型
    */
    private String tradeType;

    /**
    * 支付方式
    */
    private String payMethod;

    /**
     * 退款单号
     */
    private String refundNo;

    /**
    * 支付具体信息
    */
    private String content;

    /**
    * createDate
    */
    private Date createDate;

    /**
    * update_date
    */
    private Date updateDate;

}