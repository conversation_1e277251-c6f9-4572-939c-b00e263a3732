package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.entity.TbPointClearedDetail;
import com.kerryprops.kip.service.integral.mapper.TbPointClearedDetailMapper;
import com.kerryprops.kip.service.integral.service.TbPointClearedDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 09/14/2023 16:44
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbPointClearedDetailServiceImpl implements TbPointClearedDetailService {

    private final TbPointClearedDetailMapper tbPointClearedDetailMapper;

    @Override
    public TbPointClearedDetail getLatestMemberClearIntegral(String groupId, String vipcode) {
        return tbPointClearedDetailMapper.getLatestMemberClearIntegral(groupId, vipcode);
    }
}
