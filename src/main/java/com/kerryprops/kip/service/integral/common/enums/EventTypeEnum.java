package com.kerryprops.kip.service.integral.common.enums;

import lombok.Getter;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/14/2022 18:07
 **********************************************************************************************************************/

@Getter
public enum EventTypeEnum {

    /**
     * 支付成功
     */
    TRANS_SUCC("MALL_TRANSACTION.SUCCESS", 1),

    /**
     * 退款成功
     */
    REFUND_SUCC("MALL_REFUND.SUCCESS", 2),
    /**
     * 用户授权
     */
    MALL_AUTH("MALL_AUTH.ACTIVATE_CARD", 3),
    /**
     * 海鼎积分抵现
     */
    CASH_OUT("POINT_CASH_OUT", 4),
    ;

    private final String name;
    private final int value;

    EventTypeEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }

}
