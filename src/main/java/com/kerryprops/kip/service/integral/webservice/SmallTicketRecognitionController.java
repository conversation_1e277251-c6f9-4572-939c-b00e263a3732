package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.kerryprops.kip.service.integral.client.AuthServiceClient;
import com.kerryprops.kip.service.integral.client.ToolkitServiceClient;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.ResultVO;
import com.kerryprops.kip.service.integral.common.aop.SignValid;
import com.kerryprops.kip.service.integral.common.current.CurrentUser;
import com.kerryprops.kip.service.integral.common.current.LoginUser;
import com.kerryprops.kip.service.integral.common.enums.OcrErrorCodeMappingEnum;
import com.kerryprops.kip.service.integral.common.enums.PhotoReviewStatusEnum;
import com.kerryprops.kip.service.integral.common.enums.SaleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.DeferredResultCacheUtils;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.common.utils.RedisCacheKeyUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.config.SmallTicketProperties;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbOcrCallbackRecord;
import com.kerryprops.kip.service.integral.entity.TbPhotoReview;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.BonusSelfUploadResource;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketCallbackResource;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketPointsResource;
import com.kerryprops.kip.service.integral.webservice.resource.SmallTicketUploadResource;
import com.kerryprops.kip.service.integral.webservice.response.OcrPhotoResponse;
import com.kerryprops.kip.service.integral.webservice.response.PhotoPointsResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 04/24/2024 09:05
 **********************************************************************************************************************/


@Slf4j
@RestController
@RequestMapping("/small_ticket/recognition")
@RequiredArgsConstructor
@Tag(name="小票识别API")
public class SmallTicketRecognitionController {

    private final MallConfig mallConfig;
    private final RedisService redisService;
    private final HiveVasService hiveVasService;
    private final RabbitMqService rabbitMqService;
    private final TbBaseShopService tbBaseShopService;
    private final AuthServiceClient authServiceClient;
    private final RedisTemplate<String, Object> redisTemplate;
    private final DefaultRedisScript<Long> accessLimitScript;
    private final TbMemberAssetService tbMemberAssetService;
    private final MemberRegisterService memberRegisterService;
    private final TbPhotoReviewService tbPhotoReviewService;
    private final SmallTicketProperties smallTicketProperties;
    private final ToolkitServiceClient toolkitServiceClient;
    private final TbOcrCallbackRecordService tbOcrCallbackRecordService;
    private final SmallTicketRecognitionService smallTicketRecognitionService;
    private final MemberSalePointsProcessService memberSalePointsProcessService;

    @GetMapping("/config")
    @Operation(summary="拍照积分页面进入配置", method = "GET")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    })
    public PhotoPointsResponse getPhotoConfig(@CurrentUser LoginUser loginUser,
                                              @RequestHeader(value = "lbsId") String lbsId) {
        MallItem mallItem = hiveVasService.getMallByLbsId(lbsId);
        if (Objects.isNull(mallItem)) {
            return PhotoPointsResponse.builder().ocr(Boolean.FALSE).build();
        }
        PhotoPointsResponse response = PhotoPointsResponse.builder().ocr(mallConfig.isKO(mallItem.getGroupId())).build();
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByKipUserIdAndGroupId(loginUser.getCId(), loginUser.getBrandId());
        if (Objects.isNull(tbMemberAsset)) {
            return response;
        }
        // redis缓存key
        String memberOcrFailNumberKey = RedisCacheKeyUtils.getOcrFailNumberKey(tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode());
        String failCount = redisService.getValue(memberOcrFailNumberKey);
        if (StringUtils.isNotBlank(failCount) && Integer.parseInt(failCount) >= 5) {
            response.setTime(this.getRetryTime(memberOcrFailNumberKey));
        }
        return response;
    }

    @PostMapping("/upload")
    @Operation(summary="小票识别上传", method = "POST")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    })
    public DeferredResult<OcrPhotoResponse> smallTicketUpload(@CurrentUser LoginUser loginUser,
                                                              @Valid @RequestBody SmallTicketUploadResource resource,
                                                              @RequestHeader("appId") String appId,
                                                              HttpServletRequest request) {
        // 设置超时时间12秒
        DeferredResult<OcrPhotoResponse> result = new DeferredResult<>(smallTicketProperties.getTimeout());
        // 查询所属商场
        MallItem mallItem = hiveVasService.getMallByLbsId(loginUser.getLbsId());
        if (Objects.isNull(mallItem)) {
            result.setErrorResult(OcrPhotoResponse.builder().code(OcrErrorCodeMappingEnum.ERR_80.getOcrErrorCode()).msg(OcrErrorCodeMappingEnum.ERR_80.getErrorMsg()).build());
        } else {
            CheckJsCodeDto jsCodeDto = CheckJsCodeDto.builder().appId(appId).jsCode(resource.getJsCode()).build();
            // 调用auth服务校验小程序jsCode
            KerryResultDto<String> resultDto = authServiceClient.checkMiniProgramJsCode(jsCodeDto);
            // jsCode出现异常，则直接报错
            if (!StringUtils.equals(IntegralConstant.DEF_SUCC, resultDto.getCode())) {
                result.setErrorResult(OcrPhotoResponse.builder().code(OcrErrorCodeMappingEnum.ERR_50.getOcrErrorCode()).msg(OcrErrorCodeMappingEnum.ERR_50.getErrorMsg()).build());
                return result;
            }
            // 获取请求的ip地址
            String ipAddress = this.getIpAddr(request);
            // 检查登录用户手机号是否是虚拟手机号，如是虚拟号，则直接报错不允许拍照图片
            List<VirtualPhoneResponseDto> dtoList = toolkitServiceClient.riskCheck(VirtualPhoneQueryDto.builder().phoneNumbers(Collections.singletonList(loginUser.getPhoneNumber())).build());
            if (CollectionUtils.isNotEmpty(dtoList) && dtoList.stream().anyMatch(item -> StringUtils.equalsIgnoreCase(item.getRiskLevel(), "high"))) {
                log.info("OCR拍照积分判定为虚拟号，报错提示: [{}-{}-{}-{}]", loginUser.getBrandId(), loginUser.getPhoneNumber(), ipAddress, resource.getImgUrl());
                result.setErrorResult(OcrPhotoResponse.builder().code(OcrErrorCodeMappingEnum.ERR_110.getOcrErrorCode()).msg(OcrErrorCodeMappingEnum.ERR_110.getErrorMsg()).build());
                return result;
            }
            // 添加判断重复逻辑，上传的图片URL当天不能出现重复(防止通过接口重复调用)
            String ocrImgRedisKey = String.format(RedisCacheKey.KO_OCR_UPLOAD_IMAGE, resource.getImgUrl());
            Boolean absent = redisService.setIfAbsentWithExpire(ocrImgRedisKey, Instant.now().getEpochSecond(), 24L, TimeUnit.HOURS);
            if (Objects.nonNull(absent) && Boolean.FALSE.equals(absent)) {
                log.warn("会员通过smallTicketUpload上传异常图片: [{}-{}-{}]", loginUser.getBrandId(), loginUser.getPhoneNumber(), ipAddress);
                // 相应错误信息
                result.setErrorResult(OcrPhotoResponse.builder().code(OcrErrorCodeMappingEnum.ERR_10.getOcrErrorCode()).msg(OcrErrorCodeMappingEnum.ERR_10.getErrorMsg()).build());
                return result;
            }
            // 查询会员信息
            TbMemberAsset tbMemberAsset = tbMemberAssetService.findByKipUserIdAndGroupIdWithException(loginUser.getCId(), mallItem.getGroupId());
            // redis缓存key
            String memberOcrFailNumberKey = RedisCacheKeyUtils.getOcrFailNumberKey(tbMemberAsset.getGroupId(), tbMemberAsset.getVipcode());
            String failCount = redisService.getValue(memberOcrFailNumberKey);
            // 超出用户5分钟内因为五元素不齐或重复积分的错误次数，则直接报错
            if (StringUtils.isNotBlank(failCount) && Integer.parseInt(failCount) >= 5) {
                result.setErrorResult(OcrPhotoResponse.builder().code(OcrErrorCodeMappingEnum.ERR_90.getOcrErrorCode()).msg(this.getRetryTime(memberOcrFailNumberKey)).build());
            } else {
                // API访问限制，使用lua脚本执行
                Long count = redisTemplate.execute(accessLimitScript, Collections.singletonList(RedisCacheKey.CRM_OCR_ACCESS_LIMIT_KEY), smallTicketProperties.getAccessLimit(), 1);
                if (Objects.nonNull(count) && count > smallTicketProperties.getAccessLimit()) {
                    result.setErrorResult(OcrPhotoResponse.builder().code(OcrErrorCodeMappingEnum.ERR_100.getOcrErrorCode()).msg(OcrErrorCodeMappingEnum.ERR_100.getErrorMsg()).build());
                } else {
                    // 上传合合科技
                    smallTicketRecognitionService.asyncUploadImgToHeHe(result, tbMemberAsset, mallItem, resource.getImgUrl());
                }
            }
        }
        return result;
    }

    /**
     * 获取用户下次能再次使用OCR拍照积分的时间
     * @param memberOcrFailNumberKey
     * @return
     */
    private String getRetryTime(String memberOcrFailNumberKey) {
        Date date = new Date();
        Long expireTime = redisService.getKeyExpireTime(memberOcrFailNumberKey);
        if (Objects.nonNull(expireTime)) {
            return DateUtil.formatTime(DateUtil.offsetSecond(date, expireTime.intValue()));
        }
        return null;
    }

    /**
     * 获取客户端IP地址
     * 由于客户端的IP地址可能通过多个代理层转发，因此需要检查多个HTTP头字段以获取真实IP。
     * 此方法首先检查“x-forwarded-for”头，这是最常用的代理头，然后尝试其他不那么常见的头字段。
     * 如果所有尝试都失败，则回退到使用请求的远程地址。
     * @param request HttpServletRequest对象，用于获取客户端IP地址。
     * @return 客户端的IP地址字符串。如果无法确定客户端IP，则返回请求的远程地址。
     */
    protected String getIpAddr(HttpServletRequest request) {
        // 尝试获取“x-forwarded-for”头，这是最常用的代理头字段。
        String ip = request.getHeader("x-forwarded-for");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            ip = ip.contains(",") ? ip.split(",")[0] : ip;
        }
        // 如果“x-forwarded-for”头无效，尝试其他不那么常见的代理头字段。
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        log.info("GetRequestIpAddress: [{}]", ip);
        return ip;
    }

    @Hidden
    @GetMapping("/test_fanout")
    public void testSendFanoutMessage(@RequestParam("taskId") String taskId) {
        TbOcrCallbackRecord callbackRecord = tbOcrCallbackRecordService.findByTaskId(taskId);
        if (Objects.isNull(callbackRecord)) {
            return;
        }
        rabbitMqService.sendFanoutMessage(RabbitMqConstant.OCR_FANOUT_EXCHANGE,callbackRecord.getContent(), RabbitMqConstant.SMALL_TICKET_OCR_RECOGNITION);
    }

    @Hidden
    @GetMapping("/task_detail")
    public SmallTicketCallbackResource getTaskDetail(@RequestParam("taskId") String taskId) {
        SmallTicketCallbackResource resource = smallTicketRecognitionService.getUploadResult(taskId);
        if (Objects.isNull(resource) || StringUtils.isBlank(resource.getTaskId())) {
            return resource;
        }
        // 合合未完成
        if (Objects.nonNull(resource.getCode()) && (101 == resource.getCode() || 404 == resource.getCode())) {
            return resource;
        }
        TbPhotoReview photoReview = tbPhotoReviewService.findByOcrTaskId(resource.getTaskId());
        if (Objects.isNull(photoReview)) {
            return resource;
        }
        TbOcrCallbackRecord callbackRecord = tbOcrCallbackRecordService.findByTaskId(resource.getTaskId());
        if (Objects.isNull(callbackRecord)) {
            callbackRecord = TbOcrCallbackRecord.builder().photoId(photoReview.getId()).taskId(resource.getTaskId()).content(JsonUtils.objToString(resource)).createDate(new Date()).build();
            tbOcrCallbackRecordService.save(callbackRecord);
        }
        // 超时自动提交积分
        try {
            // 状态为非审核状态，或者已自动积分的单子，则忽略
            if (!PhotoReviewStatusEnum.autoPointsStatus(photoReview.getStatus()) || StringUtils.isNotBlank(photoReview.getIntegralAdjustId())) {
                log.info("该OCR拍照小票已处理完成: [{}]", taskId);
                return resource;
            }
            // 填充查询到的拍照记录
            smallTicketRecognitionService.fillPhotoInfo(photoReview, resource);
            // 自动提交积分
            smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(resource, photoReview);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resource;
    }

    @Hidden
    @GetMapping("/task_detail/outer_id")
    public SmallTicketCallbackResource getTaskDetailByOuterId(@RequestParam("outerId") String outerId) {
        SmallTicketCallbackResource resource = smallTicketRecognitionService.getUploadResultByOuterId(outerId);
        if (Objects.isNull(resource) || StringUtils.isBlank(resource.getTaskId())) {
            return resource;
        }
        // 合合未完成
        if (Objects.nonNull(resource.getCode()) && (101 == resource.getCode() || 404 == resource.getCode())) {
            return resource;
        }
        TbPhotoReview photoReview = tbPhotoReviewService.findByQrCode(resource.getOuterId());
        if (Objects.isNull(photoReview)) {
            return resource;
        }
        TbOcrCallbackRecord callbackRecord = tbOcrCallbackRecordService.findByTaskId(resource.getTaskId());
        if (Objects.isNull(callbackRecord)) {
            callbackRecord = TbOcrCallbackRecord.builder().photoId(photoReview.getId()).taskId(resource.getTaskId()).content(JsonUtils.objToString(resource)).createDate(new Date()).build();
            tbOcrCallbackRecordService.save(callbackRecord);
        }
        // 超时自动提交积分
        try {
            // 状态为非审核状态，或者已自动积分的单子，则忽略
            if (!PhotoReviewStatusEnum.autoPointsStatus(photoReview.getStatus()) || StringUtils.isNotBlank(photoReview.getIntegralAdjustId())) {
                log.info("该OCR拍照小票已处理完成: [{}]", outerId);
                return resource;
            }
            // 填充查询到的拍照记录
            smallTicketRecognitionService.fillPhotoInfo(photoReview, resource);
            // 自动提交积分
            smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(resource, photoReview);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resource;
    }

    @SignValid(value = "ocr")
    @PostMapping("/callback")
    @Operation(summary="合合回调-小票识别结果回传", method = "POST")
    public ResultVO smallTicketCallback(@RequestBody @Validated SmallTicketCallbackResource resource) {
        String content = JsonUtils.objToString(resource);
        log.info("smallTicketCallback: {}", content);
        // 合合未完成
        if (Objects.nonNull(resource.getCode()) && (101 == resource.getCode() || 404 == resource.getCode())) {
            return ResultVO.fail(400,"任务未处理完成");
        }
        // 系统当前时间
        Date currentDate = new Date();
        // 更新拍照积分记录信息
        TbOcrCallbackRecord callbackRecord = tbOcrCallbackRecordService.findByTaskId(resource.getTaskId());
        // 更新ocr回调记录的内容，并判断是不是重复推送
        if (Objects.nonNull(callbackRecord) && StringUtils.isBlank(callbackRecord.getContent())) {
            // 如果当前服务Map内包含前端的请求，则不发送MQ消息
            if (DeferredResultCacheUtils.containsKey(resource.getTaskId())) {
                smallTicketRecognitionService.ocrCallbackHandler(resource, content);
            } else {
                TbPhotoReview photoReview = tbPhotoReviewService.getById(callbackRecord.getPhotoId());
                // 判断是否回传结果超时，如超时，自动提交积分
                if (Objects.nonNull(photoReview) && DateUtil.between(currentDate, photoReview.getUploadDate(), DateUnit.MS) > smallTicketProperties.getTimeout()) {
                    // 填充查询到的拍照记录
                    smallTicketRecognitionService.fillPhotoInfo(photoReview, resource);
                    // 自动提交积分
                    smallTicketRecognitionService.ocrRecognitionTimeoutAutomaticPoints(resource, photoReview);
                } else {
                    // 优先广播消息，响应前端页面
                    rabbitMqService.sendFanoutMessage(RabbitMqConstant.OCR_FANOUT_EXCHANGE, content, RabbitMqConstant.SMALL_TICKET_OCR_RECOGNITION);
                }
            }
            // 回填响应内容
            callbackRecord.setContent(content);
            tbOcrCallbackRecordService.updateById(callbackRecord);
            // 异步过30分钟，重新判断用户提交的OCR是否自动积分
            SmallTicketUploadDto dto = SmallTicketUploadDto.builder().code(0).taskId(resource.getTaskId()).build();
            rabbitMqService.sendLazyMessage(RabbitMqConstant.SMALL_TICKET_OCR_MANUAL_CANCEL,
                    JsonUtils.objToString(dto), 1800);
        } else {
            log.info("OCRUploadRepeatedCallback: [{}]", resource.getTaskId());
        }
        return ResultVO.success(200,null);
    }

    @PostMapping("/points")
    @Operation(summary="小票识别结果手动确认积分", method = "POST")
    @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    public ResultVO<Integer> smallTicketPoints(@CurrentUser LoginUser loginUser, @RequestBody @Validated SmallTicketPointsResource resource) {
        // 通过OCR上传任务taskId查询用户上传的拍照记录
        TbPhotoReview photoReview = tbPhotoReviewService.findByOcrTaskId(resource.getTaskId());
        if (Objects.isNull(photoReview)) {
            return ResultVO.fail(400, "OCR拍照积分记录不存在.");
        }
        MallItem mallItem = hiveVasService.getMallByLbsId(photoReview.getMallId());
        if (Objects.isNull(mallItem)) {
            return ResultVO.fail(400, "商场不存在");
        }
        // 查询会员信息
        TbMemberAsset tbMemberAsset = tbMemberAssetService.getAllByGroupIdAndKipUserId(mallItem.getGroupId(), loginUser.getCId());
        if (Objects.isNull(tbMemberAsset) || !StringUtils.equals(photoReview.getVipcode(), tbMemberAsset.getVipcode())) {
            return ResultVO.fail(400, "会员账号不存在.");
        }
        // 只有自动识别/人工审核通过的拍照记录才能自动积分-(待审核)
        if (!PhotoReviewStatusEnum.autoPointsStatus(photoReview.getStatus())) {
            return ResultVO.fail(400, "该小票OCR识别失败，无法积分.");
        }
        // 该拍照积分是否已积分
        if (StringUtils.isNotBlank(photoReview.getIntegralAdjustId()) && Objects.nonNull(photoReview.getBonus())) {
            return ResultVO.fail(OcrErrorCodeMappingEnum.ERR_11.getOcrErrorCode(), OcrErrorCodeMappingEnum.ERR_11.getErrorMsg());
        }
        // 查询租户信息
        TbBaseShop baseShop = tbBaseShopService.getByContractNoAndMallId(photoReview.getShopNo(), mallItem.getMallId());
        if (Objects.isNull(baseShop)) {
            return ResultVO.fail(400, OcrErrorCodeMappingEnum.ERR_70.getErrorMsg());
        }
        // 添加数据重复验证
        String saleNoKey = String.format(RedisCacheKey.SALES_NO_KEY, resource.getTaskId());
        SalesAutoPointsDto pointsDto = null;
        Integer points = 0;
        // 重复提交申请判断
        if (Boolean.FALSE.equals(redisService.setSaleNoIfAbsent(saleNoKey, IdUtil.simpleUUID()))) {
            return ResultVO.fail(400, "请勿重复提交申请积分.");
        }
        try {
            // 通用销售记录去重及计算积分逻辑
            pointsDto = SalesAutoPointsDto.builder().id(IdUtil.simpleUUID()).groupId(mallItem.getGroupId())
                    .mallId(mallItem.getMallId()).member(tbMemberAsset).amount(photoReview.getMoney() + "").vipcode(tbMemberAsset.getVipcode())
                    .remark(IntegralConstant.TAKE_PHOTO).remarkName("拍照积分").shopId(photoReview.getShopNo()).baseShop(baseShop)
                    .saleNo(photoReview.getSerialNum()).saleDate(DateUtil.formatDateTime(photoReview.getTradingDate())).createUser(loginUser.getNickName())
                    .saleType(SaleTypeEnum.TICKET.getValue()).refund(false).imageUrl(photoReview.getImageUrl()).build();
            points = memberSalePointsProcessService.salePointsProcess(pointsDto);
            // 补齐拍照积分记录的积分信息
            tbPhotoReviewService.fillPhotoViewInfo(tbMemberAsset, photoReview, baseShop, points, pointsDto.getIntegralAdjustId());
        } catch (Exception e) {
            e.printStackTrace();
            // 重复销售
            if (Objects.nonNull(pointsDto) && Objects.nonNull(pointsDto.getMemberSale())) {
                // 更新拍照积分记录审核状态及异常原因
                tbPhotoReviewService.updatePhotoViewRejectReason(photoReview, pointsDto.getMemberSale(), baseShop);
                // 响应前端错误信息
                return ResultVO.fail(OcrErrorCodeMappingEnum.ERR_40.getOcrErrorCode(), OcrErrorCodeMappingEnum.ERR_40.getErrorMsg());
            }
            // 进入积分异常拦截
            if (Objects.nonNull(pointsDto)) {
                if (4 == pointsDto.getValidateStep()) {
                    // 更新拍照积分的状态为异常积分审核中
                    tbPhotoReviewService.updatePhotoViewStatus(photoReview, PhotoReviewStatusEnum.TYPE_4);
                } else {
                    // 该笔已进入异常积分审核，属于重复上传
                    tbPhotoReviewService.updatePhotoViewStatus(photoReview, PhotoReviewStatusEnum.TYPE_2);
                }
                // 响应前端错误信息
                return ResultVO.fail(OcrErrorCodeMappingEnum.ERR_30.getOcrErrorCode(), OcrErrorCodeMappingEnum.ERR_30.getErrorMsg());
            }
        } finally {
            redisService.delKeys(Collections.singletonList(saleNoKey));
        }
        return ResultVO.success(0, points);
    }

    @PutMapping("/cancel_points")
    @Operation(summary="小票识别结果-C端用户选择重新拍照，手动取消自动积分", method = "PUT")
    @Parameters({
            @Parameter(name = "loginUser", description = "登录用户信息", hidden = true)
    })
    public ResultVO<Boolean> smallTicketCancelPoints(@CurrentUser LoginUser loginUser, @RequestBody @Validated SmallTicketPointsResource resource) {
        // 查询拍照积分记录
        TbPhotoReview photoReview = tbPhotoReviewService.findByOcrTaskId(resource.getTaskId());
        if (Objects.isNull(photoReview)) {
            return ResultVO.success(Boolean.TRUE);
        }
        // 查询会员信息
        TbMemberAsset tbMemberAsset = memberRegisterService.findByGroupIdAndKipUserId(loginUser.getBrandId(), loginUser.getCId());
        if (Objects.isNull(tbMemberAsset) || !StringUtils.equals(photoReview.getVipcode(), tbMemberAsset.getVipcode())) {
            return ResultVO.success(Boolean.TRUE);
        }
        // 如果该OCR记录是已审核，且未自动积分，才能手动取消自动积分
        if (PhotoReviewStatusEnum.autoPointsStatus(photoReview.getStatus()) && StringUtils.isAllBlank(photoReview.getIntegralAdjustId(), photoReview.getReason())) {
            photoReview.setReason("会员选择重新拍照，手动取消该单自动积分");
            tbPhotoReviewService.updatePhotoViewStatus(photoReview, PhotoReviewStatusEnum.TYPE_2);
        }
        return ResultVO.success(Boolean.TRUE);
    }

    public static void main(String[] args) {
        DateTime time = DateUtil.parseDateTime("2023-11-23 13:14:15");
        System.out.println(time.getTime());
    }

}
