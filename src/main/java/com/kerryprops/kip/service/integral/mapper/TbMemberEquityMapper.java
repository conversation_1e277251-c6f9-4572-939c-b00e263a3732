package com.kerryprops.kip.service.integral.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kerryprops.kip.service.integral.entity.TbMemberEquity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/01/2022 10:25
 **********************************************************************************************************************/
public interface TbMemberEquityMapper extends BaseMapper<TbMemberEquity> {

    List<TbMemberEquity> findByMallIdAndType(@Param("groupId") String groupId, @Param("mallId") String mallId, @Param("types") List<Integer> types);

}
