package com.kerryprops.kip.service.integral.common.utils;

import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - zhangxiliang
 * Created Date - 08/01/2024 14:30
 **********************************************************************************************************************/

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RedisCacheKeyUtils {

    /**
     * 获取ocr失败次数的redis 缓存key
     * @param groupId
     * @param vipcode
     * @return
     */
    public static String getOcrFailNumberKey(String groupId, String vipcode) {
        return String.format(RedisCacheKey.OCR_PHOTO_MEMBER_FAIL_NUMBER_KEY, groupId, vipcode);
    }

}
