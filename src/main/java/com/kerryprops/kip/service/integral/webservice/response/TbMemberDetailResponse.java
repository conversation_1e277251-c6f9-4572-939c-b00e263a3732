package com.kerryprops.kip.service.integral.webservice.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbMemberDetailResponse implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 集团ID
     */
    private String groupId;

    /**
     * 商场编号
     */
    private String mallId;

    @Schema( description = "授权商场id")
    private String authorizedMallId;

    @Schema( description = "授权时间")
    private Date authorizedDate;

    /**
     * 手机区号
     */
    private  String areaCode;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 会员编号（规则生成）
     */
    private String vipcode;

    /**
     * 当前积分
     */
    private Integer currentIntegral;

    /**
     * 当前会员卡等[编码]
     */
    private String grade;

    /**
     * 会员状态（0:冻结；1:非冻结)
     */
    private String status;

    /**
     * ,微信激活商场
     */
    private String wxopenmarket;

    /**
     * 加入日期
     */
    private Date joinTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否完善信息（0:未完善；1:完善)
     */
    private String isCompleted;

    /**
     * kip对应的user_id
     */
    private String kipUserId;

    /**
     * 是否黑名单(0:是 1:不是),供活动使用
     */
    private Integer whetherBlacklist;

    /**
     * 注册来源
     */
    private String registerSource;

    /**
     * 注册来源-附注信息(例如：活动，电子券)
     */
    private String registerSourceLabel;

    /**
     * 来源名称
     */
    private String registerSourceRemark;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 创建人
     */
    private String createUser;

    private  String nickName;

    private  String realName;

    private  String avatar;

    private  String email;

    private  Integer gender;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private  Date birthDate;

    private  Integer certificateType;

    private  String certificateNum;

    private  Integer maritalStatus;

    private  Integer babyStatus;

    private  String provinceAddress;

    private  String cityAddress;

    private  String districtAddress;

    private  String address;

    private  String companyName;

    private  String job;

    private  String nation;

    private  String homePhone;

    private  Integer education;

    private  Integer profession;

    private  Integer amountOfPoints;

    /**
     * 积分发生变动的积分值，更新时用户乐观锁判断
     */
    private  Double adjustIntegralNum;

}