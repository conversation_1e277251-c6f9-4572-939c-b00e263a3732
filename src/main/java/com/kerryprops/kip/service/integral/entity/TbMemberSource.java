package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_member_source
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_register_source")
public class TbMemberSource implements Serializable {

    @TableId(value = "id", type= IdType.INPUT)
    private Long id;

    /**
     * 会员id
     */
    @TableField("vipcode")
    private String vipcode;

    /**
     * 会员id
     */
    @TableField("kip_user_id")
    private String kipUserId;

    /**
     * 商场id
     */
    @TableField("utm_lbs")
    private String utmLbs;

    /**
     * 当前渠道
     */
    @TableField("channel")
    private String channel;

    /**
     * 来源渠道
     */
    @TableField("utm_channel")
    private String utmChannel;

    /**
     * 方式
     */
    @TableField("utm_method")
    private String utmMethod;

    /**
     * 来源
     */
    @TableField("utm_source")
    private String utmSource;

    /**
     * 功能
     */
    @TableField("utm_function")
    private String utmFunction;

    /**
     * 分享人
     */
    @TableField("utm_user")
    private String utmUser;

    /**
     * 页面连接
     */
    @TableField("page_path")
    private String pagePath;

    /**
     * 原始参数
     */
    @TableField("original_params")
    private String originalParams;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    @TableField("last_update_date")
    private Date lastUpdateDate;

}