package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.model.dto.SendMessageDto;
import com.kerryprops.kip.service.integral.service.TemplateMessageSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 04/23/2023 10:55
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class PhotoPointsRejectSendMessageListener {

    private final TemplateMessageSendService templateMessageSendService;

    @RabbitHandler
    @RabbitListener(queues = {RabbitMqConstant.PHOTO_POINTS_REJECT_QUEUE}, containerFactory = "crmContainerFactory")
    public void sendMessage(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("拍照积分审核-驳回短信提醒消息体为空.");
            return;
        }
        log.info("PhotoPointsRejectSendMessageListener: {}" , msg);
        SendMessageDto messageDto = JsonUtils.stringToObj(msg, SendMessageDto.class);
        if (Objects.isNull(messageDto)) {
            return;
        }
        // 拍照积分审核-驳回短信提醒
        templateMessageSendService.sendMessage(messageDto);
    }

}
