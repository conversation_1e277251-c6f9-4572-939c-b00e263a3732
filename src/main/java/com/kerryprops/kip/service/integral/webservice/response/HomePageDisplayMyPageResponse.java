package com.kerryprops.kip.service.integral.webservice.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/28/2023 17:35
 **********************************************************************************************************************/


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema( name = "首页展示在我的页面的信息")
public class HomePageDisplayMyPageResponse implements Serializable {

    @Schema(name = "背景图全图开关（0关1开）")
    private Integer completeFlag;

    @Schema(name = "首页展示在我的页面的模块")
    private List<SelfDefiningModuleResponse> moduleList;

}
