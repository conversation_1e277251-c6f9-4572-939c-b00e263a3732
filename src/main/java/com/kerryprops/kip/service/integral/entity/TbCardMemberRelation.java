package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * @description 支付宝会员商场关系表
 * <AUTHOR>
 * @date 2022-09-16
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_member_mall_relation")
public class TbCardMemberRelation implements Serializable {

    @TableId(type = IdType.AUTO)
    /**
    * id
    */
    private Long id;

    /**
    * 手机号
    */
    private String mobile;

    /**
    * 姓名
    */
    private String name;

    /**
    * 性别
    */
    private String gender;

    /**
    * 商场编号
    */
    @TableField("mall_id")
    private String mallId;

    /**
    * 集团id
    */
    @TableField("group_id")
    private String groupId;

    /**
    * 支付宝业务卡号
    */
    @TableField("biz_card_no")
    private String bizCardNo;

    /**
    * 外部商户会员卡卡号
    */
    @TableField("external_card_no")
    private String externalCardNo;

    /**
    * 开卡时间
    */
    @TableField("open_date")
    private Date openDate;

    /**
    * 有效期
    */
    @TableField("valid_date")
    private String validDate;

    /**
     * 支付宝用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 会员所属商圈，0集团，1上海静安，2北京
     */
    @TableField("mall_type")
    private Integer mallType;

    /**
    * 创建时间
    */
    @TableField("create_time")
    private Date createTime;

    /**
    * 更新时间
    */
    @TableField("update_time")
    private Date updateTime;

    public void fillDefaultVal() {
        if (StringUtils.isBlank(this.groupId)) {
            this.groupId = "";
        }
        if (StringUtils.isBlank(this.mallId)) {
            this.mallId = "";
        }
        if (StringUtils.isBlank(this.mobile)) {
            this.mobile = "";
        }
        if (StringUtils.isBlank(this.name)) {
            this.name = "";
        }
        if (StringUtils.isBlank(this.gender)) {
            this.gender = "";
        }
        if (StringUtils.isBlank(this.bizCardNo)) {
            this.bizCardNo = "";
        }
        if (StringUtils.isBlank(this.externalCardNo)) {
            this.externalCardNo = "";
        }
        if (Objects.isNull(this.openDate)) {
            this.openDate = new Date();
        }
        if (StringUtils.isBlank(this.validDate)) {
            this.validDate = "";
        }
        if (StringUtils.isBlank(this.userId)) {
            this.userId = "";
        }
        if (Objects.isNull(this.mallType)) {
            this.mallType = 0;
        }
    }


}