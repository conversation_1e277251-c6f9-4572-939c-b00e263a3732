package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 12/17/2022 09:12
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_integral_category_rate")
public class TbIntegralCategoryRate implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("group_id")
    private String groupId;

    @TableField("mall_id")
    private String mallId;

    @TableField("category_id")
    private String categoryId;

    @TableField("category_name")
    private String categoryName;

    @TableField("grade_id")
    private String gradeId;

    @TableField("grade_name")
    private String gradeName;

    @TableField("money")
    private BigDecimal money;

    @TableField("point_num")
    private BigDecimal pointNum;

    @TableField("status")
    private int status;

    @TableField("create_date")
    private Date createDate;

    @TableField("creator")
    private String creator;

    @TableField("update_date")
    private Date updateDate;

    @TableField("updater")
    private String updater;

    @TableField("is_consistent")
    private int isConsistent;

    private List<String> categoryList;

    /**
     * 满足的业态级别
     */
    private transient Integer formatIndex;

}
