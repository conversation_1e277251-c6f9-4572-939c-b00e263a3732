package com.kerryprops.kip.service.integral.exception;

import com.kerryprops.kip.service.integral.common.enums.PointsEnum;
import com.kerryprops.kip.service.integral.webservice.resource.ExceptionResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/27/2022 16:37
 **********************************************************************************************************************/

@Slf4j
@Component
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(value = Exception.class)
    public ExceptionResource handle(Exception e) {
        log.error("未知异常: {}", e);
        return ExceptionResource.builder().errorCode(PointsEnum.INTERNAL_SERVER_ERROR.getCode()).errorMessage(PointsEnum.INTERNAL_SERVER_ERROR.getMsg()).build();
    }

    @ResponseStatus(HttpStatus.CONFLICT)
    @ExceptionHandler(value = BizException.class)
    public ExceptionResource handleBizException(BizException e) {
        log.error("业务异常: {}", e);
        return ExceptionResource.builder().errorCode(e.getCode()).errorMessage(e.getMsg()).build();
    }

    @ResponseStatus(HttpStatus.CONFLICT)
    @ExceptionHandler(value = PointsException.class)
    public ExceptionResource handlePointsException(PointsException e) {
        log.error("业务异常: {}", e);
        return ExceptionResource.builder().errorCode(e.getCode()).errorMessage(e.getMsg()).dataMsg(e.getData()).build();
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionResource mapArgumentNotValidException(MethodArgumentNotValidException ex) {
        log.error("argument not valid exception: {}", ex.getMessage(), ex);
        return ExceptionResource.builder().errorCode(PointsEnum.VALIDATION_FAILED.getCode()).errorMessage(PointsEnum.VALIDATION_FAILED.getMsg()).build();
    }

    @ExceptionHandler(PointBusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionResource pointBusinessException(PointBusinessException ex) {
        log.error("points service business exception: {}", ex.getMessage(), ex);
        return ExceptionResource.builder().errorCode(ex.getCode()).errorMessage(ex.getMsg()).build();
    }

    @ExceptionHandler(BizNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ExceptionResource mapNotFoundException(BizNotFoundException ex) {
        log.error("Data Not Found: {}", ex.getMessage(), ex);
        return ExceptionResource.builder().errorCode(ex.getCode()).errorMessage(ex.getMsg()).build();
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ExceptionResource notSupportedException(HttpRequestMethodNotSupportedException ex) {
        log.error("Method Not Supported: {}", ex.getMessage(), ex);
        return ExceptionResource.builder().errorCode(PointsEnum.METHOD_NOT_ALLOW.getCode()).errorMessage(ex.getMessage()).build();
    }

}
