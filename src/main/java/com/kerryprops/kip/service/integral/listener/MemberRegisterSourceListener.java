package com.kerryprops.kip.service.integral.listener;

import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.model.dto.MemberRegisterResourceDto;
import com.kerryprops.kip.service.integral.service.TbMemberAssetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 03/23/2023 11:38
 **********************************************************************************************************************/

@Slf4j
@Component
@RequiredArgsConstructor
public class MemberRegisterSourceListener {

    private final TbMemberAssetService tbMemberAssetService;

    /**
     * 会员注册来源:
     *  更新会员注册来源
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.XCRM_REGISTER_SOURCE, containerFactory = "crmContainerFactory")
    public void memberRegisterSourceProcess(Message message) {
        String msg = new String(message.getBody());
        if (StringUtils.isBlank(msg)) {
            log.error("获取用户注册来源信息为空.");
            return;
        }
        log.info("MemberRegisterSourceListener: {}" , msg);
        MemberRegisterResourceDto dto = JsonUtils.stringToObj(msg, MemberRegisterResourceDto.class);
        if (Objects.isNull(dto)) {
            return;
        }
        try {
            tbMemberAssetService.updateMemberRegisterSource(dto);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
