package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.enums.MemberGradeChangeTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.RuleTimeEnum;
import com.kerryprops.kip.service.integral.common.enums.RuleTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.KipDateUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail;
import com.kerryprops.kip.service.integral.entity.TbMemberGradeRule;
import com.kerryprops.kip.service.integral.model.dto.MemberYearAmountCalDto;
import com.kerryprops.kip.service.integral.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberGradeStrategyServiceImpl implements MemberGradeStrategyService {

    private final TbBaseShopService tbBaseShopService;
    private final TbSalesDetailService tbSalesDetailService;
    private final TbMemberGradeService tbMemberGradeService;
    private final TbMemberGradeRuleService tbMemberGradeRuleService;
    private final TbMemberGradeChangeDetailService memberGradeChangeDetailService;
    private final MallConfig mallConfig;

    @Override
    public String modifyUpgradeLeftMoney(TbMemberGradeRule memberGradeRule, TbMemberAsset member) {
        MemberYearAmountCalDto calDto = this.getCalculateYearAmountBeginDate(member.getGroupId(), member.getVipcode());
        return this.getRuleLeftAmount(memberGradeRule, member, calDto);
    }

    private String getRuleLeftAmount(TbMemberGradeRule memberGradeRule, TbMemberAsset member, MemberYearAmountCalDto calDto) {
        Date endDate = new Date();
        List<String> shopIds = tbBaseShopService.getContractNoList(member.getGroupId(), memberGradeRule.getFormats());
        // 获取升级金额统计开始时间
        Date beginDate = this.getUpgradeRuleBeginDate(RuleTypeEnum.getRuleType(memberGradeRule.getRuleType()), calDto.getBeginDate());
        // 查询消费金额
        Double amount = tbSalesDetailService.getMemberSaleAmountBetweenTime(member, memberGradeRule, beginDate, endDate, shopIds);
        // (rule money) - (used money)
        return String.valueOf(NumberUtil.sub(memberGradeRule.getMoney(), new BigDecimal((Objects.nonNull(amount) ? amount : 0) + "")));
    }

    @Override
    public Date getNaturalYearStartDate(TbMemberGradeChangeDetail changeDetail, Date now) {
        DateTime beforeTime = DateUtil.offsetMonth(DateUtil.beginOfYear(now), -12);
        if (KipDateUtils.checkGradeChangeDetail(changeDetail) && changeDetail.getCreateDate().getTime() > beforeTime.getTime()) {
            return changeDetail.getCreateDate();
        }
        return beforeTime;
    }

    @Override
    public Date getCycleYearStartDate(TbMemberGradeChangeDetail changeDetail, Date now) {
        DateTime beforeTime = DateUtil.offsetMonth(now, -12);
        if (KipDateUtils.checkGradeChangeDetail(changeDetail) && changeDetail.getCreateDate().getTime() > beforeTime.getTime()) {
            return changeDetail.getCreateDate();
        }
        return beforeTime;
    }

    @Override
    public Date getRelegationRuleBeginDate(TbMemberGradeChangeDetail changeDetail, boolean isCycleYear, String calculateType, Date beginDate) {
        Date now = new Date();
        Date startDate = null;
        if (RuleTimeEnum.DAY.getValue().equals(calculateType)) {
            // 确定会员日统计金额的开始时间
            if (KipDateUtils.checkGradeChangeDetail(changeDetail)) {
                startDate = StringUtils.equals(DateUtil.formatDate(now), DateUtil.formatDate(changeDetail.getCreateDate())) ? changeDetail.getCreateDate() : DateUtil.beginOfDay(now);
            } else {
                startDate = DateUtil.beginOfDay(now);
            }
        } else if (RuleTimeEnum.MONTH.getValue().equals(calculateType)) {
            // 确定会员月统计金额的开始时间
            startDate = DateUtil.offsetMonth(now, -1);
            // 月销售额统计，开始日期与会员等级变更日期比较大小
            if (KipDateUtils.checkGradeChangeDetail(changeDetail) && changeDetail.getCreateDate().getTime() > beginDate.getTime()) {
                startDate = changeDetail.getCreateDate();
            }
        } else {
            // 查询会员最终卡等变动记录(1:自然年; 2:周期年)
            startDate = isCycleYear ? this.getCycleYearStartDate(changeDetail, now) : this.getNaturalYearStartDate(changeDetail, now);
        }
        if (beginDate.after(startDate)) {
            startDate = beginDate;
        }
        return startDate;
    }

    @Override
    public Date getUpgradeRuleBeginDate(String calculateType, Date beginDate) {
        Date now = new Date();
        Date startDate = null;
        if (RuleTimeEnum.DAY.getValue().equals(calculateType)) {
            // 确定会员日统计金额的开始时间
            startDate = DateUtil.beginOfDay(now);
        } else if (RuleTimeEnum.MONTH.getValue().equals(calculateType)) {
            // 确定会员月统计金额的开始时间
            startDate = DateUtil.beginOfMonth(now);
        } else {
            // 查询会员最终卡等变动记录(1:自然年; 2:周期年)
            startDate = beginDate;
        }
        if (beginDate.after(startDate)) {
            startDate = beginDate;
        }
        return startDate;
    }

    @Override
    @Cacheable(value = RedisCacheKey.MEMBER_AMOUNT_CAL_START_DATE, key = "#groupId +':' + #vipcode")
    public MemberYearAmountCalDto getCalculateYearAmountBeginDate(String groupId, String vipcode) {
        return this.getCalDto(groupId, vipcode);
    }

    @Override
    public MemberYearAmountCalDto getNoCacheCalculateYearAmountBeginDate(String groupId, String vipcode) {
        return this.getCalDto(groupId, vipcode);
    }

    /**
     * 获取会员年消费金额的开始时间，初始卡等
     * @param groupId
     * @param vipcode
     * @return
     */
    private MemberYearAmountCalDto getCalDto(String groupId, String vipcode) {
        MemberYearAmountCalDto calDto = MemberYearAmountCalDto.builder().minGrade(Boolean.FALSE).build();
        Date currentDate = new Date();
        // 是否是周期年
        calDto.setCycleYear(tbMemberGradeRuleService.isCycleYear(groupId, 1));
        // 是周期年
        Date startDate = Boolean.TRUE.equals(calDto.getCycleYear()) ?
                DateUtil.offsetMonth(DateUtil.beginOfDay(currentDate), -12) :
                DateUtil.offsetMonth(DateUtil.beginOfYear(currentDate), -12);
        // 设置默认初始值
        calDto.setBeginDate(startDate);
        // groupId下最低卡等code
        String miniGradeCode = tbMemberGradeService.getGroupMiniGradeCode(groupId);
        // 保级记录（区分自然年/周期年，其中周期年，要添加手动调整会员卡等的变更记录，作为统计金额的开始时间）
        List<TbMemberGradeChangeDetail> changeList = memberGradeChangeDetailService.getChangeDetailGtCreateDate(groupId, vipcode, DateUtil.formatDateTime(startDate),
                Boolean.TRUE.equals(calDto.getCycleYear()) ? MemberGradeChangeTypeEnum.getRelegationType() : MemberGradeChangeTypeEnum.getNaturalRelegationType());
        TbMemberGradeChangeDetail detail = null;
        // 等级变更记录不为空，则判断是否有人工调整的等级记录，并判断是否是受影响的group
        if (CollectionUtils.isNotEmpty(changeList) && !mallConfig.noEffectGroups(groupId)) {
            detail = changeList.stream().filter(item -> MemberGradeChangeTypeEnum.isManualDowngrade(item.getChangeType())).findFirst().orElse(null);
        }
        if (Objects.isNull(detail) && CollectionUtils.isNotEmpty(changeList)) {
            detail = changeList.get(changeList.size() - 1);
        }
        // 如果存在保级记录
        if (Objects.nonNull(detail) && Objects.nonNull(detail.getCreateDate())) {
            calDto.setMinGrade(StringUtils.equals(miniGradeCode, detail.getNewGrade()));
            // 是人工调整会员等级或非最低卡等(非最低卡等的意思是前一年的消费金额已用于保级某个等级)，开始时间则为会员等级变更时间
            if (MemberGradeChangeTypeEnum.isManualDowngrade(detail.getChangeType()) || Boolean.FALSE.equals(calDto.getMinGrade())) {
                calDto.setBeginDate(detail.getCreateDate());
            } else {
                // 是最低卡等，则开始时间为上次升级的时间往前推1年
                Date changeDate = DateUtil.offsetMonth(detail.getCreateDate(), -12);
                if (changeDate.getTime() >= startDate.getTime()) {
                    calDto.setBeginDate(changeDate);
                }
            }
            // 会员年初始等级
            calDto.setOrgGrade(detail.getNewGrade());
        } else {
            calDto.setOrgGrade(miniGradeCode);
            calDto.setMinGrade(Boolean.TRUE);
        }
        return calDto;
    }
}
