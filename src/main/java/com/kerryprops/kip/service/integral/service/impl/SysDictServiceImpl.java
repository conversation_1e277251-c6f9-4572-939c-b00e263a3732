package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.SysDict;
import com.kerryprops.kip.service.integral.mapper.SysDictMapper;
import com.kerryprops.kip.service.integral.service.SysDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/14/2022 09:27
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class SysDictServiceImpl extends ServiceImpl<SysDictMapper, SysDict> implements SysDictService {

    private final SysDictMapper sysDictMapper;

    @Async
    @Override
    public CompletableFuture<Map<String, String>> findDictNames(List<String> dictTypes) {
        if (CollectionUtils.isEmpty(dictTypes)) {
            return CompletableFuture.completedFuture(MapUtil.empty());
        }
        List<SysDict> types = sysDictMapper.findByDictTypes(dictTypes);
        return CompletableFuture.completedFuture(types.stream().collect(Collectors.toMap(SysDict::getDictType, SysDict::getDictName, (k1, k2) -> k1)));
    }

    @Override
    public SysDict findByDictType(String dictType) {
        return sysDictMapper.findByDictType(dictType);
    }

    @Override
    public List<SysDict> getByTypes(List<String> types) {
        return sysDictMapper.getListByTypes(types);
    }
}
