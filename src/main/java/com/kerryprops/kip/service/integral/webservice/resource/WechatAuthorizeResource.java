package com.kerryprops.kip.service.integral.webservice.resource;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/15/2022 15:11
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatAuthorizeResource implements Serializable {

    @JsonProperty(value = "openid")
    private String openId;

    private String code;

    private String mchid;

    @JsonProperty(value = "auth_type")
    private String authType;

}
