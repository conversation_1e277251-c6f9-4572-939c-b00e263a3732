//package com.kerryprops.kip.service.integral.service.impl;
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.kerryprops.kip.service.integral.entity.SysUser;
//import com.kerryprops.kip.service.integral.mapper.SysUserMapper;
//import com.kerryprops.kip.service.integral.service.SysUserService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///***********************************************************************************************************************
// * Project - member-points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> nancy
// * Created Date - 10/30/2023 09:40
// **********************************************************************************************************************/
//
//@Slf4j
//@Service
//@DS("hbs")
//@RequiredArgsConstructor
//public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
//
//    private final SysUserMapper sysUserMapper;
//
//    @Override
//    public List<SysUser> getUserListByIds(List<String> userIds) {
//        return sysUserMapper.getUserListByIds(userIds);
//    }
//
//    @Override
//    public List<String> getUserEmailList(List<String> userIds) {
//        return sysUserMapper.getUserEmailList(userIds);
//    }
//}
