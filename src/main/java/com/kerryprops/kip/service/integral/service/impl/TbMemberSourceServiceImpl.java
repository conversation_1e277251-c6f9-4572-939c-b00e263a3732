package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbMemberSource;
import com.kerryprops.kip.service.integral.mapper.TbMemberSourceMapper;
import com.kerryprops.kip.service.integral.model.dto.MemberRegisterResourceDto;
import com.kerryprops.kip.service.integral.service.TbMemberSourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/23/2022 15:19
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbMemberSourceServiceImpl extends ServiceImpl<TbMemberSourceMapper, TbMemberSource> implements TbMemberSourceService {

    private final TbMemberSourceMapper tbMemberSourceMapper;

    @Override
    public void checkAndSave(MemberRegisterResourceDto dto, TbMemberAsset tbMemberAsset) {
        if (StringUtils.isBlank(dto.getRegMemberSourceOriginalParams())) {
            return;
        }
        TbMemberSource tbMemberSource = tbMemberSourceMapper.findByVipcode(tbMemberAsset.getVipcode());
        if (Objects.nonNull(tbMemberSource)) {
            return;
        }
        Map<String, Object> jsonMap = JsonUtils.stringToMap(dto.getRegMemberSourceOriginalParams());
        TbMemberSource source = new TbMemberSource();
        source.setUtmLbs(MapUtils.getString(jsonMap, "utm_lbs", ""));
        source.setChannel(MapUtils.getString(jsonMap, "channel", ""));
        source.setUtmChannel(MapUtils.getString(jsonMap, "utm_channel", ""));
        source.setUtmMethod(MapUtils.getString(jsonMap, "utm_method", ""));
        source.setUtmSource(MapUtils.getString(jsonMap, "utm_source", ""));
        source.setUtmFunction(MapUtils.getString(jsonMap, "utm_function", ""));
        source.setUtmUser(MapUtils.getString(jsonMap, "utm_user", ""));
        source.setPagePath(MapUtils.getString(jsonMap, "page_path", ""));
        source.setOriginalParams(StringUtils.isNotBlank(dto.getRegMemberSourceOriginalParams()) ? dto.getRegMemberSourceOriginalParams() : "");
        source.setVipcode(StringUtils.isNotBlank(tbMemberAsset.getVipcode()) ? tbMemberAsset.getVipcode() : "");
        source.setKipUserId(StringUtils.isNotBlank(dto.getKipUserId()) ? dto.getKipUserId() : (StringUtils.isNotBlank(tbMemberAsset.getKipUserId()) ? tbMemberAsset.getKipUserId() : ""));
        source.setUtmLbs(StringUtils.isBlank(dto.getMallId()) ? "" : dto.getMallId());
        source.setCreateUser(StringUtils.isNotBlank(tbMemberAsset.getVipcode()) ? tbMemberAsset.getVipcode() : "");
        source.setId(IdWorker.getId());
        tbMemberSourceMapper.saveSource(source);
    }

}
