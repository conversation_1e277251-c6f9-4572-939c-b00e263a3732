package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.entity.SubscriptionRecord;
import com.kerryprops.kip.service.integral.mapper.TbSubscriptionRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto;
import com.kerryprops.kip.service.integral.service.TbSubscriptionRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 12/06/2022 16:37
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbSubscriptionRecordServiceImpl extends ServiceImpl<TbSubscriptionRecordMapper, SubscriptionRecord> implements TbSubscriptionRecordService {

    private final TbSubscriptionRecordMapper tbSubscriptionRecordMapper;


    @Override
    public SubscriptionRecord querySubscriptionDto(SubscriptionRecordQueryDto query) {
        return tbSubscriptionRecordMapper.querySubscriptionDto(query);
    }

    @Override
    public List<SubscriptionRecord> querySubscriptionList(SubscriptionRecordQueryDto query) {
        return tbSubscriptionRecordMapper.querySubscriptionList(query);
    }

    @Override
    public List<String> querySubscriptionUserIdList(SubscriptionRecordQueryDto query) {
        return tbSubscriptionRecordMapper.querySubscriptionUserIdList(query);
    }
}
