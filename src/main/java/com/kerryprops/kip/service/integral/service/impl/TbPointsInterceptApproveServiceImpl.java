package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.enums.CommonSeparators;
import com.kerryprops.kip.service.integral.common.enums.PointsInterceptEnum;
import com.kerryprops.kip.service.integral.common.enums.SubscriptionTypeEnum;
import com.kerryprops.kip.service.integral.entity.TbPointsIntercept;
import com.kerryprops.kip.service.integral.entity.TbPointsInterceptConfig;
import com.kerryprops.kip.service.integral.event.EmailApproveSendEvent;
import com.kerryprops.kip.service.integral.mapper.TbPointsInterceptApproveMapper;
import com.kerryprops.kip.service.integral.mapper.TbPointsInterceptConfigMapper;
import com.kerryprops.kip.service.integral.model.dto.SaleDetailQueryDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.TbPointsInterceptQueryDto;
import com.kerryprops.kip.service.integral.service.TbPointsInterceptApproveService;
import com.kerryprops.kip.service.integral.service.TbSalesDetailService;
import com.kerryprops.kip.service.integral.webservice.response.TbInterceptResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/20/2022 14:03
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@AllArgsConstructor
public class TbPointsInterceptApproveServiceImpl extends ServiceImpl<TbPointsInterceptApproveMapper, TbPointsIntercept> implements TbPointsInterceptApproveService {

    private final TbPointsInterceptConfigMapper tbPointsInterceptConfigMapper;
    private final TbPointsInterceptApproveMapper tbPointsInterceptApproveMapper;
    private final TbSalesDetailService tbSalesDetailService;
    private final ApplicationEventPublisher applicationEventPublisher;

    @Override
    public TbInterceptResponse checkSaleIsIntercept(SalesAutoPointsDto points) {
        List<TbPointsInterceptConfig> list = tbPointsInterceptConfigMapper.findByMallId(points.getMallId());
        if (CollectionUtils.isEmpty(list)) {
            return TbInterceptResponse.builder().isIntercept(Boolean.FALSE).build();
        }
        // 添加白名单逻辑判断
        List<TbPointsInterceptConfig> whiteList = list.stream().filter(item -> (1 == item.getWhiteList() && StringUtils.isNotBlank(item.getShopNo())
                && Stream.of(item.getShopNo().split(CommonSeparators.COMMA_SEPARATOR)).anyMatch(shop -> StringUtils.equals(shop, points.getShopId()))))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(whiteList) && list.size() == whiteList.size()) {
            return TbInterceptResponse.builder().isIntercept(Boolean.FALSE).build();
        }
        // 过滤掉该笔销售满足的白名单拦截规则
        if (CollectionUtils.isNotEmpty(whiteList)) {
            list = list.stream().filter(item -> whiteList.stream().noneMatch(it -> Objects.equals(item.getId(), it.getId()))).collect(Collectors.toList());
        }
        // 再次判断是否存在非白名单设置的规则
        if (CollectionUtils.isEmpty(list)) {
            return TbInterceptResponse.builder().isIntercept(Boolean.FALSE).build();
        }
        if (Objects.isNull(points.getMember())) {
            return TbInterceptResponse.builder().isIntercept(Boolean.FALSE).build();
        }
        SaleDetailQueryDto queryDto = SaleDetailQueryDto.builder().shopNo(points.getShopId())
                .vipcode(points.getMember().getVipcode()).mallId(points.getMallId()).build();
        // 当日单店铺 日，自然周，自然月，周期周(连续7天)
        Long interceptId = null;
        String interceptReason = "";
        String interceptSaleIds = "";
        boolean flag = Boolean.FALSE;
        for (TbPointsInterceptConfig config : list) {
            // 填充开始时间和结束时间
            this.fillQueryDate(config, queryDto, points.getSaleDate());
            // 不在当前设置的异常积分拦截时间内
            if (Boolean.FALSE.equals(queryDto.getSaleTimeIsBetweenQueryTime())) {
                continue;
            }
            List<String> saleIds = tbSalesDetailService.checkSaleIsIntercept(queryDto);
            int count = CollectionUtils.isEmpty(saleIds) ? 0 : saleIds.size();
            // 销售时间不在开始时间或者结束时间的周期内，一样给通过，比如单日一次，但是录入的是昨天的，那么这笔记录无论如何都应该给通过
            if (count < config.getCount() - 1) {
                continue;
            }
            log.info("saleIsIntercept {} , {}", points.getVipcode(), points.getSaleNo());
            interceptId = config.getId();
            interceptSaleIds = CollectionUtils.isEmpty(saleIds) ? "" : StringUtils.join(saleIds, CommonSeparators.COMMA_SEPARATOR);
            interceptReason = String.format("%s超过或等于%s次", PointsInterceptEnum.getDescByType(config.getTimeType()), config.getCount());
            flag = Boolean.TRUE;
            break;
        }
        return TbInterceptResponse.builder()
                .isIntercept(flag)
                .interceptId(interceptId+"")
                .interceptReason(interceptReason)
                .interceptSaleIds(interceptSaleIds)
                .build();
    }

    @Override
    public void savePointsIntercept(TbPointsIntercept intercept) {
        tbPointsInterceptApproveMapper.saveIntercept(intercept);
        // 发送异常拦截邮件提醒事件
        applicationEventPublisher.publishEvent(EmailApproveSendEvent.builder().groupId(intercept.getGroupId()).mallId(intercept.getMallId()).subscriptionType(SubscriptionTypeEnum.POINTS_INTERCEPT.getType()).build());
    }

    private void fillQueryDate(TbPointsInterceptConfig config, SaleDetailQueryDto queryDto, String saleDate) {
        // 时间类型 1:日 2:自然周 3：周期周(连续7天) 4:自然月
        // 销售时间在开始时间之后且在结束时间之前
        Date saleTime = DateUtil.parse(saleDate);
        Date startTime = null, endTime = null;
        if (Objects.equals(config.getTimeType(), PointsInterceptEnum.DAY.getType())) {
            startTime = DateUtil.beginOfDay(saleTime);
            endTime = DateUtil.endOfDay(saleTime);
        } else if (Objects.equals(config.getTimeType(), PointsInterceptEnum.NATURAL_WEEK.getType())) {
            startTime = DateUtil.beginOfWeek(saleTime);
            endTime = DateUtil.endOfWeek(saleTime);
        } else if (Objects.equals(config.getTimeType(), PointsInterceptEnum.CYCLE_WEEK.getType())) {
            startTime = DateUtil.beginOfDay(DateUtil.offsetDay(saleTime, -6));
            endTime = DateUtil.endOfDay(saleTime);
        } else {
            startTime = DateUtil.beginOfMonth(saleTime);
            endTime = DateUtil.endOfMonth(saleTime);
        }
        queryDto.setStartTime(DateUtil.formatDateTime(startTime));
        queryDto.setEndTime(DateUtil.formatDateTime(endTime));
        queryDto.setSaleTimeIsBetweenQueryTime(saleTime.compareTo(startTime) >= 0 && saleTime.compareTo(endTime) <= 0);
    }

    @Override
    public int getTotal(TbPointsInterceptQueryDto queryDto) {
        return tbPointsInterceptApproveMapper.getTotal(queryDto);
    }

    @Override
    public List<TbPointsIntercept> getPageData(TbPointsInterceptQueryDto queryDto) {
        if (Objects.nonNull(queryDto.getNum()) && Objects.nonNull(queryDto.getSize())) {
            queryDto.setOffset(queryDto.getNum() * queryDto.getSize());
        }
        return tbPointsInterceptApproveMapper.getPageData(queryDto);
    }

    @Override
    public TbPointsIntercept getInterceptDto(TbPointsInterceptQueryDto queryDto) {
        return tbPointsInterceptApproveMapper.getInterceptDto(queryDto);
    }
}
