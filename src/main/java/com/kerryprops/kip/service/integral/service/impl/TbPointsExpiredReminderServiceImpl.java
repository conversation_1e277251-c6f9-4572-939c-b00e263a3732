package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.entity.TbBatchIntegralPresentDetail;
import com.kerryprops.kip.service.integral.entity.TbPointClearedDetail;
import com.kerryprops.kip.service.integral.entity.TbPointsExpiredReminder;
import com.kerryprops.kip.service.integral.mapper.TbBatchIntegralPresentDetailMapper;
import com.kerryprops.kip.service.integral.mapper.TbPointsExpiredReminderMapper;
import com.kerryprops.kip.service.integral.model.dto.IntegralClearShowDto;
import com.kerryprops.kip.service.integral.model.dto.MemberPointSumQueryDto;
import com.kerryprops.kip.service.integral.service.TbPointClearedDetailService;
import com.kerryprops.kip.service.integral.service.TbPointsDetailService;
import com.kerryprops.kip.service.integral.service.TbPointsExpiredReminderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 12/08/2022 16:36
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbPointsExpiredReminderServiceImpl extends ServiceImpl<TbPointsExpiredReminderMapper, TbPointsExpiredReminder> implements TbPointsExpiredReminderService {

    private final TbPointsExpiredReminderMapper tbPointsExpiredReminderMapper;
    private final TbPointClearedDetailService tbPointClearedDetailService;
    private final TbBatchIntegralPresentDetailMapper tbBatchIntegralPresentDetailMapper;
    private final TbPointsDetailService tbPointsDetailService;

    @Override
    public IntegralClearShowDto getPointsClearMessage(String groupId, String mallId, String vipcode) {
        IntegralClearShowDto showDto = IntegralClearShowDto.builder().isShow(1).clearNum(0).build();
        TbPointsExpiredReminder prompt = tbPointsExpiredReminderMapper.findByMallIdAndGroupId(mallId, groupId);
        if (Objects.isNull(prompt) || prompt.getIsShowC() == 0) {
            return showDto;
        }
        Date currentDate = DateUtil.date();
        // 展示文案内容
        String content = (prompt.getIsShowC() == 1) ? prompt.getYearEnd() : prompt.getNotYearEnd();
        // 展现年底积分清零提示 -- 通过积分清零记录来
        if (prompt.getIsShowC() == 1) {
            TbPointClearedDetail detail = tbPointClearedDetailService.getLatestMemberClearIntegral(groupId, vipcode);
            if (Objects.isNull(detail) || Objects.isNull(detail.getClearDate())) {
                return showDto;
            }
            // 展示时间
            Date showStartTime = (prompt.getShowType() == 1) ? DateUtil.offsetMonth(detail.getClearDate(), -prompt.getAheadMonthNum()) :
                    DateUtil.offsetDay(detail.getClearDate(), -prompt.getAheadDayNum());
            if (currentDate.getTime() >= showStartTime.getTime()) {
                showDto.setIsShow(0);
                // 清零积分数
                showDto.setClearNum(Objects.isNull(detail.getClearIntegral()) ? 0 : detail.getClearIntegral());
                content = content.replace(IntegralConstant.REFINE_CONFIG_PARAMS, showDto.getClearNum() + "")
                        .replace(IntegralConstant.INTEGRAL_CLEAR_DATE, DateUtil.formatDate(detail.getClearDate()));
            }
        } else {
            // 展示时间
            Date showTime = (prompt.getShowType() == 1) ? DateUtil.offsetMonth(currentDate, prompt.getAheadMonthNum()) :
                    DateUtil.offsetDay(currentDate, prompt.getAheadDayNum());
            // 通过赠送积分
            TbBatchIntegralPresentDetail detail = tbBatchIntegralPresentDetailMapper
                    .queryDetailByVipcodeAndOverdueTime(vipcode, groupId, DateUtil.formatDateTime(showTime));
            if (Objects.isNull(detail)) {
                return showDto;
            }
            MemberPointSumQueryDto queryDto = MemberPointSumQueryDto.builder().type(2).groupId(groupId).mallId(mallId).vipcode(vipcode).build();
            queryDto.setStartDate(DateUtil.format(detail.getValidTime(), "yyyy-MM-dd HH") + ":00:00");
            queryDto.setEndDate(DateUtil.format(detail.getOverdueTime(), "yyyy-MM-dd HH") + ":59:59");
            int consumePoints = tbPointsDetailService.getMemberConsumePointsBetweenDate(queryDto);
            if (detail.getImportIntegral() + consumePoints > 0) {
                showDto.setIsShow(0);
                // 清零积分数
                showDto.setClearNum(detail.getImportIntegral() + consumePoints);
                content = content.replace(IntegralConstant.REFINE_CONFIG_PARAMS, showDto.getClearNum() + "")
                        .replace(IntegralConstant.INTEGRAL_CLEAR_DATE, DateUtil.formatDate(detail.getOverdueTime()));
            }
        }
        showDto.setContent(content);
        return showDto;
    }

}
