package com.kerryprops.kip.service.integral.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 支付宝会员卡商场表
 * @createDate 2022/10/24
 * @updateDate 2022/10/24
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName("tb_card_mall_relation")
public class CardMallRelation implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 集团id
     */
    private String groupId;
    /**
     * 商场名称
     */
    private String mallName;
    /**
     * 商场授权token
     */
    private String token;
    /**
     * 刷新授权token
     */
    private String refreshToken;
    /**
     * token过期时间（支付宝已经作废，token长期有效）
     */
    private Date tokenExpireDate;
    /**
     * 刷新token有效时间
     */
    private Date refreshExpireDate;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateDate;
}