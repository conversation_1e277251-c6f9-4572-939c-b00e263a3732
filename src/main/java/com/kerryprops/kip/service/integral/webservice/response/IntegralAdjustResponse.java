package com.kerryprops.kip.service.integral.webservice.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Nancy
 * Created Date - 04/26/2023 13:43
 **********************************************************************************************************************/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntegralAdjustResponse implements Serializable {

    /**
     * 会员id(销售录入传来的id是会员id，tb_member表id)
     */
    private String id;

    /**
     * 调整类型 A->普通调整， S->销售调整
     */
    private String type;

    private String groupId;

    /**
     * 商场编号
     */
    private String mallId;

    /**
     * 积分调整原因
     */
    private String remark;

    /**
     * 销售单号(销售调整)
     */
    private String sellNo;

    /**
     * 店铺编号(给积分记录使用)
     */
    private String shopNo;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 消费金额
     */
    private BigDecimal useMoney;

    /**
     * 优惠金额
     */
    private BigDecimal discounts;

    /**
     * 销售日期(销售调整)
     */
    private String sellDate;

    /**
     * 备注
     */
    private String content;

    /**
     * 收银机号
     */
    private String cashNo;

    /**
     * 小票地址
     */
    private String imageUrl;

    /**
     * 创建者
     */
    private String creator;

}
