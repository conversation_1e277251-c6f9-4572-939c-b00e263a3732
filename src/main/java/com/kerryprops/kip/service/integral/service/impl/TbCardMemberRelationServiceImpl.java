package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.kerryprops.kip.service.integral.entity.TbCardMemberRelation;
import com.kerryprops.kip.service.integral.mapper.TbCardMemberRelationMapper;
import com.kerryprops.kip.service.integral.service.TbCardMemberRelationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 07/25/2023 14:02
 **********************************************************************************************************************/

@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbCardMemberRelationServiceImpl implements TbCardMemberRelationService {

    private final TbCardMemberRelationMapper tbCardMemberRelationMapper;

    @Override
    public TbCardMemberRelation getByMallIdAndAliUserId(String mallId, String aliUserId) {
        return tbCardMemberRelationMapper.getByMallIdAndAliUserId(mallId, aliUserId);
    }

    @Override
    public TbCardMemberRelation getByAliUserId(String aliUserId) {
        return tbCardMemberRelationMapper.getByAliUserId(aliUserId);
    }

    @Override
    public TbCardMemberRelation getByMallIdAndExternalCardNo(String mallId, String externalCardNo) {
        return tbCardMemberRelationMapper.getByMallIdAndExternalCardNo(mallId, externalCardNo);
    }

    @Override
    public TbCardMemberRelation getByMallIdAndMobile(String mallId, String mobile) {
        return tbCardMemberRelationMapper.getByMallIdAndMobile(mallId, mobile);
    }

    @Override
    public TbCardMemberRelation findByExternalCardNo(String vipcode) {
        return tbCardMemberRelationMapper.findByExternalCardNo(vipcode);
    }

    @Override
    public TbCardMemberRelation getByGroupIdAndMobileAndMallId(String groupId, String mobile, String mallId) {
        return tbCardMemberRelationMapper.getByGroupIdAndMobileAndMallId(groupId, mobile, mallId);
    }
}
