package com.kerryprops.kip.service.integral.webservice;

import cn.hutool.core.util.IdUtil;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.RedisCacheKey;
import com.kerryprops.kip.service.integral.common.aop.SignValid;
import com.kerryprops.kip.service.integral.common.enums.*;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbBaseShop;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbPointsDetail;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.exception.BizException;
import com.kerryprops.kip.service.integral.exception.PointBusinessException;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;
import com.kerryprops.kip.service.integral.model.dto.PointsRedemptionDto;
import com.kerryprops.kip.service.integral.model.dto.SalesAutoPointsDto;
import com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto;
import com.kerryprops.kip.service.integral.service.*;
import com.kerryprops.kip.service.integral.webservice.resource.HaiDingPosParamResource;
import com.kerryprops.kip.service.integral.webservice.resource.HaiDingPosSaleResource;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberForPosResponse;
import com.kerryprops.kip.service.integral.webservice.response.HaiDingPosResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/highTrade/pos")
@RestController
@AllArgsConstructor
@Tag(name = "海鼎pos回调接口")
public class HadiDingPosController extends BaseMemberStatus {

    private final TbSetshoprateService tbSetshoprateService;
    private final TbBaseShopService tbBaseShopService;
    private final Mapper mapper;
    private final TbMemberAssetService tbMemberAssetService;
    private final TbMemberPointsChangeService tbMemberPointsChangeService;
    private final KerryStaffService kerryStaffService;
    private final MemberSalePointsProcessService memberSalePointsProcessService;
    private final TbSalesDetailService tbSalesDetailService;
    private final TbInsensatePointsPushRecordService tbInsensatePointsPushRecordService;
    private final RabbitMqService rabbitMqService;
    private final TbPaymentPushRecordService tbPaymentPushRecordService;
    private final TbPointsDetailService tbPointsDetailService;
    private final RedisService redisService;

    @GetMapping("/getMemberForPos")
    @Operation(summary="海鼎查询会员信息", method = "GET")
    @SignValid(value = "pos")
    public TbMemberForPosResponse getMemberForPos(@RequestParam String groupId,
                                                  @RequestParam(required = false) String mallId,
                                                  @RequestParam(required = false) String vipcode,
                                                  @RequestParam(required = false) String mobile,
                                                  @RequestParam(required = false) String shopNo) {
        if (StringUtils.isAllBlank(vipcode, mobile)) {
            throw PointBusinessException.error(PointsEnum.REQUIRED_PARAMETER_NULL_ERROR);
        }
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(groupId).vipcode(vipcode).mobile(mobile).build());
        // 检查会员是否存在及是否冻结
        this.checkMemberStatus(tbMemberAsset);
        int amountOfPoints = 0;
        if (StringUtils.isNotBlank(shopNo)) {
            // 判断店铺号是否正常
            TbBaseShop tbBaseShop = tbBaseShopService.getByContractNoAndMallId(shopNo, mallId);
            if (Objects.isNull(tbBaseShop)) {
                throw PointBusinessException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
            }
            // 查询店铺的最大抵现金额
            PointsRedemptionDto dto = tbSetshoprateService.getMaxAmountOfStoreCashOut(SalesAutoPointsDto.builder().groupId(tbMemberAsset.getGroupId()).mallId(tbBaseShop.getMallId())
                    .shopId(shopNo).member(tbMemberAsset).saleType(SaleTypeEnum.POS.getValue()).remark(PointsRedemptionEnum.POS_CASH_OUT.getCode()).build());
            // 取最大金额
            amountOfPoints = Objects.isNull(dto) ? 0 : dto.getAmount();
        }
        tbMemberAsset.setAmountOfPoints(amountOfPoints);
        return this.getResponse(tbMemberAsset);
    }

    private TbMemberForPosResponse getResponse(TbMemberAsset tbMemberAsset) {
        if (StringUtils.isNotBlank(tbMemberAsset.getKipUserId())) {
            Optional.ofNullable(kerryStaffService.findByKipUserId(tbMemberAsset.getKipUserId())).ifPresent(tbMemberAsset::fillOtherInfo);
        } else {
            Optional.ofNullable(kerryStaffService.findByMobile(tbMemberAsset.getMobile())).ifPresent(tbMemberAsset::fillOtherInfo);
        }
        TbMemberForPosResponse resp = mapper.map(tbMemberAsset, TbMemberForPosResponse.class);
        resp.setCurrentIntegral(tbMemberAsset.getCurrentPoints());
        return resp;
    }

    @PostMapping("/redeemPoints")
    @Operation(summary="海鼎积分抵现", method = "POST")
    @SignValid(value = "pos")
    public HaiDingPosResponse redeemPoints(@RequestBody @Validated HaiDingPosParamResource resource) {
        TbPointsDetail adjust = tbPointsDetailService.queryAdjustList(resource.getGroupId(), resource.getMallId(), resource.getDocNo(), resource.getShopNo(), PointsRedemptionEnum.POS_CASH_OUT.getCode());
        if (Objects.nonNull(adjust)) {
            log.info("duplicateCashOut :{}",JsonUtils.objToString(resource));
            throw PointBusinessException.error(PointsEnum.FORBID_DUPLICATE_CASH_OUT);
        }
        // 判断会员信息
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findByDto(SingleMemberQueryDto.builder().groupId(resource.getGroupId()).vipcode(resource.getVipcode()).build());
        if (Objects.isNull(tbMemberAsset)) {
            throw PointBusinessException.error(PointsEnum.MEMBER_NOT_EXISTS);
        }
        // 判断店铺
        TbBaseShop tbBaseShop = tbBaseShopService.getByContractNoAndMallId(resource.getShopNo(), resource.getMallId());
        if (Objects.isNull(tbBaseShop)) {
            throw PointBusinessException.error(PointsEnum.MALL_SHOP_NOT_FOUND);
        }
        if (resource.getAmountOfPoints() <= 0) {
            return HaiDingPosResponse.builder().success(true).points(0).amountOfPoints(resource.getAmountOfPoints()).build();
        }
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder().groupId(resource.getGroupId()).mallId(resource.getMallId()).shopId(resource.getShopNo())
                .member(tbMemberAsset).saleType(SaleTypeEnum.POS.getValue()).remark(PointsRedemptionEnum.POS_CASH_OUT.getCode()).build();
        // 获取店铺最大积分抵现金额
        PointsRedemptionDto dto = tbSetshoprateService.getMaxAmountOfStoreCashOut(pointsDto);
        // 取最大金额
        int maxAmountOfStoreCashOut = Objects.isNull(dto) ? 0 : dto.getAmount();
        // 抵现金额小于传入金额，报错
        if (maxAmountOfStoreCashOut < resource.getAmountOfPoints()) {
            throw PointBusinessException.error(PointsEnum.AMOUNT_OF_POINTS_CHECK_ERROR);
        }
        InsensatePointsPushRecordDto pushRecord = InsensatePointsPushRecordDto.builder().openId(resource.getDocNo()).eventType(EventTypeEnum.CASH_OUT.getValue())
                .transactionId(resource.getDocNo()).transactionInfo(JsonUtils.objToString(resource)).origin(InsensateOriginEnum.HIGH_TRADE_POS.getValue()).build();
        tbInsensatePointsPushRecordService.pushTransaction(pushRecord);
        // 记录销售单号
        pointsDto.setSaleNo(resource.getDocNo());
        // 将金额转换成积分
        pointsDto.setRemark(PointsRedemptionEnum.POS_CASH_OUT.getCode());
        pointsDto.setRemarkName(PointsRedemptionEnum.POS_CASH_OUT.getDesc());
        pointsDto.setAmount(resource.getAmountOfPoints() + "");
        String lockKey = String.format(RedisCacheKey.INTEGRAL_ADJUST_LOCK, "REDEEM", resource.getMallId(), resource.getDocNo());
        if (Boolean.FALSE.equals(redisService.setSaleNoIfAbsent(lockKey, IntegralConstant.KIP_POINTS))) {
            log.info("请勿重复积分抵现");
            throw PointBusinessException.error(PointsEnum.REPEAT_SALES_DATA_ERROR);
        }
        try {
            int points = tbSetshoprateService.salesToIntegral(pointsDto);
            if (points > 0) {
                pointsDto.setCreateUser("pos");
                pointsDto.setExchangePoints(-points);
                pointsDto.setSalesRemark(String.format("%s - 积分抵现金额: %s,扣除积分: %s" ,pointsDto.getRemarkName(), pointsDto.getAmount(), pointsDto.getExchangePoints()));
                tbMemberPointsChangeService.changeMemberPoints(pointsDto);
                // 抵扣了积分发MQ消息，检查销售记录是否推送CRM系统
                rabbitMqService.sendLazyMessage(RabbitMqConstant.POS_CASH_OUT_ROUTE_KEY, JsonUtils.objToString(resource), 1800);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            redisService.delKeys(Collections.singletonList(lockKey));
        }
        return HaiDingPosResponse.builder().success(true).points(pointsDto.getExchangePoints()).amountOfPoints(resource.getAmountOfPoints()).build();
    }

    @PostMapping("/posSaleRecord")
    @Operation(summary = "保存海鼎交易记录数据", method = "POST")
    @SignValid(value = "pos")
    public HaiDingPosResponse posSaleRecord(@RequestBody @Validated HaiDingPosSaleResource resource) {
        log.info("posSaleRecord resource:{}",JsonUtils.objToString(resource));
        // 校验会员信息
        TbMemberAsset tbMemberAsset = tbMemberAssetService.findMemberAllInfoByVipcodeAndGroupId(resource.getVipcode(), resource.getGroupId());
        this.checkMemberStatus(tbMemberAsset);
        SalesAutoPointsDto pointsDto = SalesAutoPointsDto.builder()
                .id(IdUtil.simpleUUID())
                .groupId(resource.getGroupId())
                .mallId(resource.getMallId())
                .member(tbMemberAsset)
                .vipcode(tbMemberAsset.getVipcode())
                .discountAmount(resource.getDiscounts())
                // 实付金额，计算积分用
                .amount(resource.getPayAmount())
                .totalAmount(resource.getTotalAmount())
                .shopId(resource.getShopNo())
                .saleNo(resource.getDocNo())
                .saleDate(resource.getSaleDate())
                .createUser(StringUtils.isBlank(resource.getCreateUser()) ? "pos" : resource.getCreateUser())
                .saleType(SaleTypeEnum.POS.getValue())
                .build();
        int points = 0;
        String lockKey = String.format(RedisCacheKey.INTEGRAL_ADJUST_LOCK, resource.getOptType(), resource.getMallId(), resource.getDocNo());
        if (StringUtils.equals(resource.getOptType(), IntegralConstant.REFUND)) {
            if (Boolean.FALSE.equals(redisService.setSaleNoIfAbsent(lockKey, IntegralConstant.KIP_POINTS))) {
                throw BizException.error(PointsEnum.SALE_REMARK_NOT_FOUND);
            }
            pointsDto.setRefund(Boolean.TRUE);
            pointsDto.setRemark(IntegralConstant.ORDER_REFUND);
            pointsDto.setRemarkName("有单退货");
            try {
                points = this.handlePosRefund(pointsDto, resource.getAmountOfPoints());
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                redisService.delKeys(Collections.singletonList(lockKey));
            }
        } else {
            if (Boolean.FALSE.equals(redisService.setSaleNoIfAbsent(lockKey, IntegralConstant.KIP_POINTS))) {
                throw BizException.error(PointsEnum.SALE_REMARK_NOT_FOUND);
            }
            try {
                pointsDto.setRefund(Boolean.FALSE);
                pointsDto.setRemark(IntegralConstant.SALE_INTEGRAL);
                if (StringUtils.isBlank(resource.getAmountOfPoints()) || Integer.parseInt(resource.getAmountOfPoints()) <= 0) {
                    pointsDto.setRemarkName("POS支付积分");
                } else {
                    pointsDto.setRemarkName("POS支付积分，抵现金额: " + resource.getAmountOfPoints());
                }
                points = memberSalePointsProcessService.salePointsProcess(pointsDto);
            } catch (Exception e) {
                if (Integer.parseInt(IntegralConstant.INTEGRAL_REPEAT_3) == pointsDto.getValidateStep()) {
                    return HaiDingPosResponse.builder().success(true).points(0).build();
                } else {
                    throw e;
                }
            } finally {
                redisService.delKeys(Collections.singletonList(lockKey));
            }
        }
        return HaiDingPosResponse.builder().success(true).points(points).build();
    }

    private int handlePosRefund(SalesAutoPointsDto pointsDto, String amountOfPoints) {
        int points ;
        // 需要校验tb_member_sale表是否有数据
        TbSalesDetail sale = tbSalesDetailService.selectBySellNoAndMallId(pointsDto.getSaleNo(), pointsDto.getMallId());
        // 销售记录为空
        if (Objects.isNull(sale)) {
            throw PointBusinessException.error(PointsEnum.SALE_NOT_EXISTS_ERROR);
        }
        // 已退款状态
        if (RefundStatusEnum.REFUND.getValue() == sale.getStatus()) {
            throw PointBusinessException.error(PointsEnum.SALE_IS_REFUND);
        }
        // 退货金额.取消费金额字段(算积分时是按消费金额算的积分,而不是销售金额)
        pointsDto.setRefundAmount(sale.getPayAmount().toString());
        pointsDto.setMemberSale(sale);
        // 只支持全部退款
        pointsDto.setRefundAmount(sale.getPayAmount().toString());
        points = memberSalePointsProcessService.refundSalePointsProcess(pointsDto);
        // 抵扣金额大于0，退款要返回积分抵扣金额对应的积分0
        if (StringUtils.isBlank(amountOfPoints) || Integer.parseInt(amountOfPoints) <= 0) {
            return points;
        }
        tbPaymentPushRecordService.refundPosPoints(pointsDto, Integer.parseInt(amountOfPoints));
        return points;
    }

}
