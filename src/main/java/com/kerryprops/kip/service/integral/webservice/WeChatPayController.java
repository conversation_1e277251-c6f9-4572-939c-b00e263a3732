package com.kerryprops.kip.service.integral.webservice;

import com.kerryprops.kip.pmw.client.resource.BizCircleParkingNotifyInputResource;
import com.kerryprops.kip.pmw.client.resource.PointsActivationConfirmResource;
import com.kerryprops.kip.pmw.client.resource.PointsPaymentConfirmResource;
import com.kerryprops.kip.pmw.client.resource.PointsRefundConfirmResource;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.CodeMessageEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.config.MallConfig;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsPushRecord;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.model.dto.MallItem;
import com.kerryprops.kip.service.integral.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description 微信支付相关api
 * @createDate 2022/9/7
 * @updateDate 2022/9/7
 */
@Slf4j
@RequestMapping("/wechat_pay")
@RestController
@AllArgsConstructor
@Tag(name = "微信无感积分API")
public class WeChatPayController {

    private final WechatAccelerateIntegralService wechatAccelerateIntegralService;
    private final TbInsensatePointsPushRecordService tbInsensatePointsPushRecordService;
    private final TbOpenCardFailRecordService tbOpenCardFailRecordService;
    private final RabbitMqService rabbitMqService;
    private final TbMemberAssetService tbMemberAssetService;
    private final MallConfig mallConfig;

    @PostMapping("/callback")
    @Operation(summary="微信支付结果回调", method = "POST")
    public String payCallback(@RequestBody PointsPaymentConfirmResource resource) {
        if (Objects.isNull(resource.getBody())) {
            return CodeMessageEnum.FAIL.getValue();
        }
        PointsPaymentConfirmResource.PointsPaymentDetailResource body = resource.getBody().getResource();
        if (Objects.isNull(body)) {
            return CodeMessageEnum.FAIL.getValue();
        }
        // 保存推送记录
        tbInsensatePointsPushRecordService.saveWeChatPushPaymentRecord(resource);
        log.info("接受到Payment推送支付记录: {}-{}", body.getOpenId(), body.getTransactionId());
        MallItem mallItem = mallConfig.getByMchId(body.getMchId());
        if (Objects.isNull(mallItem)) {
            return CodeMessageEnum.FAIL.getValue();
        }
        TbMemberAsset member = tbMemberAssetService.getMemberForAutoPointsByOpenIdAndGroupIdAndMallId(body.getOpenId(), mallItem.getGroupId(), mallItem.getMallId());
        if (Objects.isNull(member)) {
            log.error("OpenId: {}未查询到用户信息.", body.getOpenId());
            return CodeMessageEnum.FAIL.getValue();
        }
        rabbitMqService.sendMessage(RabbitMqConstant.WECHAT_PAYMENT_SYNC_ROUTE_KEY, JsonUtils.objToString(resource));
        return CodeMessageEnum.ACCEPTED.getValue();
    }

    @PostMapping("/refund_callback")
    @Operation(summary="微信退款结果回调", method = "POST")
    public String refundCallback(@RequestBody PointsRefundConfirmResource resource) {
        // 保存推送的退款记录
        tbInsensatePointsPushRecordService.saveWeChatPushRefundRecord(resource);
        // 发送mq消息
        rabbitMqService.sendMessage(RabbitMqConstant.WECHAT_REFUND_SYNC_ROUTE_KEY, JsonUtils.objToString(resource));
        return CodeMessageEnum.ACCEPTED.getValue();
    }

    @PostMapping("/member_points_auth")
    @Operation(summary="微信用户授权结果通知", method = "POST")
    public String memberPointsAuth(@RequestBody PointsActivationConfirmResource resource) {
        // 保存授权开通记录记录
        tbOpenCardFailRecordService.saveOrUpdateRecord(resource);
        log.info("memberPointsAuth: {}", JsonUtils.objToString(resource));
        // 记录用户授权成功标识
        wechatAccelerateIntegralService.memberPointsAuth(resource);
        return CodeMessageEnum.ACCEPTED.getValue();
    }

    @PostMapping("/member_card_open")
    @Operation(summary="微信用户开通会员卡事件", method = "POST")
    public String memberCardOpenEvent(@RequestBody PointsActivationConfirmResource resource) {
        log.info("memberCardOpenEvent: {}", JsonUtils.objToString(resource));
        wechatAccelerateIntegralService.memberCardOpenEvent(resource);
        return CodeMessageEnum.ACCEPTED.getValue();
    }

    @PostMapping("/sync_parking")
    @Operation(summary="商圈会员停车状态同步", method = "POST")
    public String syncParking(@RequestBody BizCircleParkingNotifyInputResource resource) {
        return CodeMessageEnum.ACCEPTED.getValue();
    }

    @PostMapping("/sync_ali_pay")
    @Operation(summary="单次同步全量支付宝授权记录", method = "POST")
    public String syncAliPay() {
        wechatAccelerateIntegralService.syncAliPay();
        return CodeMessageEnum.ACCEPTED.getValue();
    }

    @GetMapping("/record")
    @Operation(summary="通过推送单号查询推送记录", method = "GET")
    public TbInsensatePointsPushRecord queryByOrderId(@RequestParam("orderId") String orderId) {
        return tbInsensatePointsPushRecordService.findByTransactionId(orderId);
    }

}
