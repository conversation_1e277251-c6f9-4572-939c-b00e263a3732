package com.kerryprops.kip.service.integral.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.kerryprops.kip.service.integral.common.IntegralConstant;
import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
import com.kerryprops.kip.service.integral.common.enums.MessageTypeEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
import com.kerryprops.kip.service.integral.entity.TbSalesDetail;
import com.kerryprops.kip.service.integral.model.dto.*;
import com.kerryprops.kip.service.integral.service.RabbitMqService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 09/21/2022 12:09
 **********************************************************************************************************************/

@Slf4j
@Component
public class RabbitMqServiceImpl implements RabbitMqService {

    @Resource(name = "crmRabbitTemplate")
    private RabbitTemplate crmRabbitTemplate;

    @Override
    public void sendMessage(String routeKey, String msg) {
        Message message = MessageBuilder.withBody(msg.getBytes())
                .setContentType(MessageProperties.CONTENT_TYPE_JSON).setContentEncoding(IntegralConstant.DEFAULT_ENC)
                .setMessageId(IdUtil.simpleUUID()).build();
        log.info("RabbitMqService sendMessage: {}-{}", routeKey, msg);
        crmRabbitTemplate.convertAndSend(RabbitMqConstant.DIRECT_EXCHANGE, routeKey, message);
    }

    @Override
    public void sendTopicMessage(String routeKey, String msg) {
        Message message = MessageBuilder.withBody(msg.getBytes())
                .setContentType(MessageProperties.CONTENT_TYPE_JSON).setContentEncoding(IntegralConstant.DEFAULT_ENC)
                .setMessageId(IdUtil.simpleUUID()).build();
        log.info("RabbitMqService sendTopicMessage: {}-{}", routeKey, msg);
        crmRabbitTemplate.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, routeKey, message);
    }

    @Override
    public void sendLazyMessage(String routeKey, String msg, int time) {
        Message message = MessageBuilder.withBody(msg.getBytes())
                .setContentType(MessageProperties.CONTENT_TYPE_JSON).setContentEncoding(IntegralConstant.DEFAULT_ENC)
                .setMessageId(IdUtil.simpleUUID()).build();
        message.getMessageProperties().setDelayLong(time * 1000L);
        log.info("RabbitMqService sendLazyMessage: {}-{}", routeKey, msg);
        crmRabbitTemplate.convertAndSend(RabbitMqConstant.DIRECT_DELAY_EXCHANGE, routeKey, message);
    }

    /**
     * 1、新注册用户场景触发奖励
     * 2、身份认证决定会员等级
     * 2、次日短信提醒
     */
    @Async
    @Override
    public void sendMemberNewMsg(TbMemberAsset member, boolean isInvitation) {
        log.info("sendMemberNewMsg");
        MemberRegisterRewardDto rewardDto = MemberRegisterRewardDto.builder().memberId(String.valueOf(member.getId())).mallId(member.getMallId()).invitation(isInvitation).build();
        log.info("会员注册事件发送------>会员号【{}", member.getVipcode());
        this.sendMessage(RabbitMqConstant.XCRM_MEMBER_REGISTER_ACTIVITY_REWARD, JsonUtils.objToString(rewardDto));
        MemberIdDto dto = MemberIdDto.builder().id(String.valueOf(member.getId())).messageType(MessageTypeEnum.NEW_MEMBER.getType()).build();
        // 会员注册及次日短信提醒
        this.sendLazyMessage(RabbitMqConstant.XCRM_MEMBER_REGISTER_SMS_NOTIFY, JsonUtils.objToString(dto), 1);
    }

    /**
     * 发送会员等级计算、会员成长进度条计算消息
     * @param detail
     * @param dto
     */
    @Async
    @Override
    public void sendMqMessage(TbSalesDetail detail, SalesAutoPointsDto dto) {
        // 清除会员消费金额的缓存
        ClearMemberSalesSumDto sumDto = ClearMemberSalesSumDto.builder().groupId(detail.getGroupId())
                .vipcode(detail.getVipcode()).kipUserId(detail.getKipUserId()).build();
        this.sendMessage(RabbitMqConstant.XCRM_MEMBER_SALE_SUM, JsonUtils.objToString(sumDto));
        // 退款满赠或会员邀约奖励发放(消费的已从: XCRM_MEMBER_GRADE 内发送了)
        if (dto.isRefund()) {
            this.sendFullActivityReward(detail, dto, Boolean.TRUE);
            // 退款会员等级判断
            this.sendRefundCheckGradeMsg(detail, dto.getFromType());
        } else {
            // 触发会员升级或场景触发
            log.info("sending XCRM_MEMBER_GRADE mq - saleNo: {}, money: {}, points: {}", detail.getOrderNo(), detail.getPayAmount(), detail.getPointsNum());
            this.sendFullActivityReward(detail, dto, Boolean.FALSE);
        }
    }

    /**
     * 发生退款后，根据商场是否开启退款降级判断开关去做会员等级判断
     * @param detail detail
     * @param detail fromType
     */
    private void sendRefundCheckGradeMsg(TbSalesDetail detail, String fromType) {
        Map<String, String> params = new HashMap<>(4);
        params.put(IntegralConstant.SALE_ID, detail.getId()+"");
        if (StringUtils.isNotBlank(fromType)) {
            params.put(IntegralConstant.FROM_TYPE, fromType);
        }
        this.sendLazyMessage(RabbitMqConstant.MEMBER_SALE_REFUND_CHECK_GRADE, JsonUtils.objToString(params), 2);
    }

    @Async
    @Override
    public void sendFullActivityReward(TbSalesDetail detail, SalesAutoPointsDto dto, Boolean fromRefund) {
        if (Objects.isNull(detail) || Objects.isNull(dto) || Objects.isNull(dto.getMember())) {
            return;
        }
        // 升级的时候用详情的支付金额，退款的时候要用到每次具体的退款金额，所以用dto的退款金额
        TbMemberAsset member = dto.getMember();
        // 防止有地方没改到
        dto.setRefundAmount(StringUtils.isBlank(dto.getRefundAmount()) ? detail.getRefundAmount().toString() : dto.getRefundAmount());
        MemberSaleActivityDto activityDto = MemberSaleActivityDto.builder().memberId(String.valueOf(member.getId())).mallId(detail.getMallId()).shopId(detail.getShopNo())
                .sellNo(detail.getOrderNo()).createDate(DateUtil.formatDateTime(detail.getSaleDate())).createUser(detail.getCreateUser()).saleType(detail.getSaleType())
                .money(detail.getPayAmount()).saleId(String.valueOf(detail.getId())).isRefund(Boolean.FALSE)
                .fromType(dto.getFromType()).orgGrade(member.getGrade()).build();
        // 来自退款操作
        if (Boolean.TRUE.equals(fromRefund)) {
            activityDto.setRefund(true);
            activityDto.setMoney(NumberUtil.toBigDecimal(dto.getRefundAmount()));
        } else {
            // 判断退款金额是否大于0
            if (Objects.nonNull(detail.getRefundAmount()) && detail.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
                activityDto.setMoney(NumberUtil.sub(detail.getPayAmount(), detail.getRefundAmount()));
            }
        }
        log.info("sending SALE_ACTIVITY_REWARD mq - status: {}, isRefund: {}, saleNo: {}, money: {}",detail.getStatus(), activityDto.isRefund(), detail.getOrderNo(), activityDto.isRefund() ? detail.getRefundAmount() : detail.getPayAmount());
        // 发送广播消息（满赠及会员邀约活动及停车券活动，徽章活动及会员升降级监听）
        this.sendFanoutMessage(RabbitMqConstant.SALES_EXCHANGE_FANOUT, JsonUtils.objToString(activityDto), RabbitMqConstant.MEMBER_SALES_TRIGGER_UPGRADE_CHECK);
    }

    @Override
    public void sendFanoutMessage(String exchange, String msg, String routeKey) {
        Message message = MessageBuilder.withBody(msg.getBytes())
                .setContentType(MessageProperties.CONTENT_TYPE_JSON).setContentEncoding(IntegralConstant.DEFAULT_ENC)
                .setMessageId(IdUtil.simpleUUID()).build();
        log.info("RabbitMqService sendFanoutMessage: {}", msg);
        crmRabbitTemplate.convertAndSend(exchange, "", message);
    }

}