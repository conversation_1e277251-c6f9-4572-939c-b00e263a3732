package com.kerryprops.kip.service.integral.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kerryprops.kip.service.integral.entity.TbMemberRefine;
import com.kerryprops.kip.service.integral.entity.TbMemberRefineField;
import com.kerryprops.kip.service.integral.webservice.resource.TbMemberRefineResource;

import java.util.List;

/***********************************************************************************************************************
 * Project - points-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 11/17/2022 13:48
 **********************************************************************************************************************/
public interface TbMemberRefineService extends IService<TbMemberRefine> {

    /**
     * 通过商场ID查询商场
     * @param mallId mallId
     * @return dto dto
     */
    TbMemberRefineResource getByMallId(String mallId);

    /**
     * 通过商场ID查询商场
     * @param mallId mallId
     * @return list list
     */
    List<TbMemberRefineField> memberRefineRequiredFields(String mallId);

}
