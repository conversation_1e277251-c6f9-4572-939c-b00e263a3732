package com.kerryprops.kip.service.integral.model.vo;


import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description zindex 自定义配置展示类
 * @createDate 2022/12/15
 * @updateDate 2022/12/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ZIndexVo {

	private Long id;

	private String groupId;

	private String mallId;

	/**
	 * 页面类型 0首页 1自定义2我的
	 */
	private String type;

	private String headNavigationShow;

	private String completeFlag;

	private List<String> moduleList;

	private Long myPageId;
}
