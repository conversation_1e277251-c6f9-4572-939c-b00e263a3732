package com.kerryprops.kip.service.integral.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.pmw.client.resource.PointsPaymentConfirmResource;
import com.kerryprops.kip.pmw.client.resource.PointsRefundConfirmResource;
import com.kerryprops.kip.service.integral.common.enums.EventTypeEnum;
import com.kerryprops.kip.service.integral.common.enums.InsensateOriginEnum;
import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
import com.kerryprops.kip.service.integral.entity.TbInsensatePointsPushRecord;
import com.kerryprops.kip.service.integral.mapper.TbInsensatePointsPushRecordMapper;
import com.kerryprops.kip.service.integral.model.dto.InsensatePointsPushRecordDto;
import com.kerryprops.kip.service.integral.service.TbInsensatePointsPushRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @description 微信、支付宝推送记录表
 * <AUTHOR>
 * @date 2022-09-14
 */
@Slf4j
@Service
@DS("points")
@RequiredArgsConstructor
public class TbInsensatePointsPushRecordServiceImpl extends ServiceImpl<TbInsensatePointsPushRecordMapper, TbInsensatePointsPushRecord> implements TbInsensatePointsPushRecordService {

    private final TbInsensatePointsPushRecordMapper insensatePointsPushRecordMapper;
    private final Mapper mapper;

    @Async
    @Override
    public void pushTransaction(InsensatePointsPushRecordDto dto) {
        TbInsensatePointsPushRecord record = mapper.map(dto, TbInsensatePointsPushRecord.class);
        // 保存无感积分推送记录
        insensatePointsPushRecordMapper.insert(record);
    }

    @Async
    @Override
    public void saveWeChatPushPaymentRecord(PointsPaymentConfirmResource dto) {
        if (!StringUtils.equals(dto.getBody().getEventType(), EventTypeEnum.TRANS_SUCC.getName())) {
            return;
        }
        PointsPaymentConfirmResource.PointsPaymentDetailResource resource = dto.getBody().getResource();
        // 保存推送记录
        TbInsensatePointsPushRecord record = TbInsensatePointsPushRecord.builder().openId(resource.getOpenId()).eventType(EventTypeEnum.TRANS_SUCC.getValue())
                .transactionId(resource.getTransactionId()).transactionInfo(JsonUtils.objToString(resource)).origin(InsensateOriginEnum.WECHAT.getValue()).build();
        insensatePointsPushRecordMapper.insert(record);
    }

    @Async
    @Override
    public void saveWeChatPushRefundRecord(PointsRefundConfirmResource dto) {
        PointsRefundConfirmResource.PointsRefundDetailResource resource = dto.getBody().getResource();
        TbInsensatePointsPushRecord recordDto = TbInsensatePointsPushRecord.builder().openId(resource.getOpenId()).eventType(EventTypeEnum.REFUND_SUCC.getValue()).transactionId(resource.getTransactionId())
                .transactionInfo(JsonUtils.objToString(resource)).origin(InsensateOriginEnum.WECHAT.getValue()).build();
        insensatePointsPushRecordMapper.insert(recordDto);
    }

    @Override
    public InsensatePointsPushRecordDto findByTransactionIdAndEventTypeAndOrigin(String transactionId, Integer eventType, int origin) {
        TbInsensatePointsPushRecord record = insensatePointsPushRecordMapper.findByTransactionIdAndEventTypeAndOrigin(transactionId, eventType, origin);
        if (Objects.isNull(record)) {
            return null;
        }
        return mapper.map(record, InsensatePointsPushRecordDto.class);
    }

    @Override
    public List<InsensatePointsPushRecordDto> selectByOpenId(String openId) {
        return insensatePointsPushRecordMapper.selectByOpenId(openId);
    }

    @Override
    public TbInsensatePointsPushRecord findByTransactionId(String transactionId) {
        return insensatePointsPushRecordMapper.findByTransactionId(transactionId);
    }

}