//package com.kerryprops.kip.service.integral.service.impl;
//
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.util.IdUtil;
//import cn.hutool.core.util.NumberUtil;
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.google.common.collect.Maps;
//import com.kerryprops.kip.service.integral.common.IntegralConstant;
//import com.kerryprops.kip.service.integral.common.RabbitMqConstant;
//import com.kerryprops.kip.service.integral.common.utils.JsonUtils;
//import com.kerryprops.kip.service.integral.entity.TbMemberAsset;
//import com.kerryprops.kip.service.integral.entity.TbMemberIntegraladjust;
//import com.kerryprops.kip.service.integral.mapper.TbMemberIntegraladjustMapper;
//import com.kerryprops.kip.service.integral.model.dto.*;
//import com.kerryprops.kip.service.integral.service.RabbitMqService;
//import com.kerryprops.kip.service.integral.service.SysDictService;
//import com.kerryprops.kip.service.integral.service.TbMemberIntegraladjustService;
//import com.kerryprops.kip.service.integral.webservice.resource.MemberMonthlyIntegralResponse;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.collections4.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//import java.util.concurrent.CompletableFuture;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///***********************************************************************************************************************
// * Project - points-service
// *
// * (c) Kerry Properties Limited. All rights reserved.
// *
// * <AUTHOR> Bert
// * Created Date - 09/22/2022 16:50
// **********************************************************************************************************************/
//
//@Slf4j
//@Service
//@DS("xcrm")
//@RequiredArgsConstructor
//public class TbMemberIntegraladjustServiceImpl extends ServiceImpl<TbMemberIntegraladjustMapper, TbMemberIntegraladjust> implements TbMemberIntegraladjustService {
//
//    private final TbMemberIntegraladjustMapper tbMemberIntegraladjustMapper;
//    private final SysDictService sysDictService;
//    private final RabbitMqService rabbitMqService;
//
//    @Override
//    public String saveIntegralAdjustRecord(SalesAutoPointsDto dto) {
//        TbMemberIntegraladjust integralAdjust = new TbMemberIntegraladjust();
//        integralAdjust.setId(StringUtils.isNotBlank(dto.getIntegralAdjustId()) ? dto.getIntegralAdjustId() : IdUtil.simpleUUID());
//        integralAdjust.setGroupId(dto.getGroupId());
//        integralAdjust.setMallId(dto.getMallId());
//        TbMemberAsset member = dto.getMember();
//        integralAdjust.setVipcode(member.getVipcode());
//        integralAdjust.setMobile(member.getMobile());
//        // 销售调整
//        integralAdjust.setType(dto.isRefund() ? "A" : "S");
//        integralAdjust.setRemark(dto.getRemark());
//        integralAdjust.setRemarkName(dto.getRemarkName());
//        integralAdjust.setBeforeGrade(member.getGrade());
//        integralAdjust.setBeforeIntegral(member.getCurrentPoints());
//        integralAdjust.setSellDate(DateUtil.parse(dto.getSaleDate()));
//        integralAdjust.setSellNo(dto.getSaleNo());
//        integralAdjust.setShopId(dto.getShopId());
//        integralAdjust.setNumber((double) dto.getExchangePoints());
//        // 剩余可用积分数
//        integralAdjust.setLeftPoints(Math.max(dto.getExchangePoints(), 0));
//        integralAdjust.setDiscounts(0D);
//        integralAdjust.setCreateUser(dto.getCreateUser());
//        Date date = new Date();
//        integralAdjust.setCreateDate(date);
//        integralAdjust.setUpdateDate(date);
//        integralAdjust.setUpdateUser(dto.getCreateUser());
//        integralAdjust.setChannerlSource("0");
//        integralAdjust.setOperationId(dto.getId());
//        integralAdjust.setExtraIntegral("0");
//        // 记录当前积分
//        integralAdjust.setCurrentIntegral(NumberUtil.add(member.getCurrentPoints(), (double) dto.getExchangePoints()));
//        // 记录销售金额
//        if (StringUtils.isNotBlank(dto.getAmount())) {
//            integralAdjust.setNumberToIntegral(Double.valueOf(dto.getAmount()));
//            integralAdjust.setContent(dto.getSalesRemark());
//        }
//        // 是退款的话，记录实际的退款金额
//        if (dto.isRefund() && StringUtils.isNotBlank(dto.getRefundAmount())) {
//            integralAdjust.setNumberToIntegral(Double.valueOf(dto.getRefundAmount()));
//        }
//        // 补齐收银机号
//        if (StringUtils.isNotBlank(dto.getPosNo())) {
//            integralAdjust.setCashNo(dto.getPosNo());
//        }
//        tbMemberIntegraladjustMapper.insert(integralAdjust);
//        if (dto.getExchangePoints() == 0) {
//            log.info("获取积分为零提前返回.");
//            return integralAdjust.getId();
//        }
//        // 非积分清零类型-发送模版消息及短信通知
//        if (!IntegralConstant.INTEGRAL_CLEAR_REMARK.equals(dto.getRemark())) {
//            this.sendMessage(dto.getRemark(), dto.getExchangePoints(), dto.getMallId(), member);
//        }
//        // 会员消耗积分MQ
//        this.sendPointsConsumeMq(member, integralAdjust.getId());
//        return integralAdjust.getId();
//    }
//
//    @Override
//    public String savePointsAdjustRecord(MemberPointsChangeDto dto, TbMemberAsset tbMemberAsset) {
//        String adjustId = this.saveRecord(dto, tbMemberAsset);
//        // 非积分清零类型-发送积分变更模版消息
//        if (!IntegralConstant.INTEGRAL_CLEAR_REMARK.equals(dto.getDictValue())) {
//            this.sendMessage(dto.getDictValue(), dto.getChangePointsNum(), dto.getMallId(), tbMemberAsset);
//        }
//        // 会员消耗积分MQ
//        this.sendPointsConsumeMq(tbMemberAsset, adjustId);
//        return adjustId;
//    }
//
//    /**
//     * 发送积分变更消息
//     * @param dictValue
//     * @param changePoints
//     * @param mallId
//     * @param tbMemberAsset
//     */
//    private void sendMessage(String dictValue, Integer changePoints, String mallId, TbMemberAsset tbMemberAsset) {
//        SendMessageDto messageDto = SendMessageDto.builder().memberId(String.valueOf(tbMemberAsset.getId())).dictType(dictValue)
//                .number(changePoints + "").mallId(mallId).messageType("5").templateType(1).templateActivityid(IdUtil.simpleUUID())
//                .updateDate(DateUtil.formatDateTime(new Date())).build();
//        rabbitMqService.sendLazyMessage(RabbitMqConstant.XCRM_MEMBER_INTEGRAL_GRADE_CHANGE, JsonUtils.objToString(messageDto), 2);
//    }
//
//    /**
//     * 会员消耗积分发送MQ通知
//     * @param tbMemberAsset
//     * @param detailId
//     */
//    private void sendPointsConsumeMq(TbMemberAsset tbMemberAsset, String detailId) {
//        // 积分>0，则不是消耗积分，直接返回
//        if (tbMemberAsset.getAdjustIntegralNum() >= 0) {
//            log.info("会员获得积分，无需发送积分消耗MQ.");
//            return;
//        }
//        MemberConsumePointsDto consumeDto = MemberConsumePointsDto.builder().detailId(detailId).memberId(String.valueOf(tbMemberAsset.getId()))
//                .groupId(tbMemberAsset.getGroupId()).build();
//        rabbitMqService.sendLazyMessage(RabbitMqConstant.CRM_MEMBER_CONSUME_POINTS_QUEUE, JsonUtils.objToString(consumeDto), 1);
//    }
//
//    @Override
//    public void savePointsAdjustRecordNoMessage(MemberPointsChangeDto dto, TbMemberAsset member) {
//        this.saveRecord(dto, member);
//    }
//
//    private String saveRecord(MemberPointsChangeDto dto, TbMemberAsset member) {
//        TbMemberIntegraladjust integralAdjust = new TbMemberIntegraladjust();
//        integralAdjust.setId(StringUtils.isNotBlank(dto.getAdjustPointsId()) ? dto.getAdjustPointsId() : IdUtil.simpleUUID());
//        integralAdjust.setGroupId(dto.getGroupId());
//        integralAdjust.setMallId(StringUtils.isBlank(dto.getMallId()) ? member.getMallId() : dto.getMallId());
//        integralAdjust.setVipcode(member.getVipcode());
//        integralAdjust.setMobile(member.getMobile());
//        // 销售调整
//        integralAdjust.setType(StringUtils.isBlank(dto.getType()) ? "S" : dto.getType());
//        integralAdjust.setRemark(dto.getDictValue());
//        if (StringUtils.isNotBlank(dto.getDictName())) {
//            integralAdjust.setRemarkName(dto.getDictName());
//        } else {
//            Optional.ofNullable(sysDictService.findByDictType(dto.getDictValue())).ifPresent(item -> integralAdjust.setRemarkName(item.getDictName()));
//        }
//        integralAdjust.setBeforeGrade(member.getGrade());
//        integralAdjust.setBeforeIntegral(member.getCurrentPoints());
//        if (StringUtils.isNotBlank(dto.getShopNo())) {
//            integralAdjust.setShopId(dto.getShopNo());
//        }
//        integralAdjust.setSellNo(StringUtils.isNotBlank(dto.getSaleNo()) ? dto.getSaleNo() : null);
//        integralAdjust.setNumber((double) dto.getChangePointsNum());
//        // 剩余可用积分数
//        integralAdjust.setLeftPoints(Math.max(dto.getChangePointsNum(), 0));
//        integralAdjust.setDiscounts(0D);
//        Date date = new Date();
//        integralAdjust.setCreateUser(StringUtils.isBlank(dto.getCreator()) ? "user_operation" : dto.getCreator());
//        integralAdjust.setSellDate(date);
//        integralAdjust.setCreateDate(date);
//        integralAdjust.setUpdateDate(date);
//        integralAdjust.setUpdateUser(StringUtils.isBlank(dto.getCreator()) ? "user_operation" : dto.getCreator());
//        integralAdjust.setChannerlSource("0");
//        if (StringUtils.isNotBlank(dto.getAssociatedBusinessId())) {
//            integralAdjust.setOperationId(dto.getAssociatedBusinessId());
//        } else {
//            integralAdjust.setOperationId(IdUtil.simpleUUID());
//        }
//        integralAdjust.setExtraIntegral("0");
//        // 记录当前积分
//        integralAdjust.setCurrentIntegral(NumberUtil.add(member.getCurrentPoints(), (double) dto.getChangePointsNum()));
//        // 记录销售金额
//        if (Objects.nonNull(dto.getSaleAmount())) {
//            integralAdjust.setNumberToIntegral(dto.getSaleAmount().doubleValue());
//        } else {
//            integralAdjust.setNumberToIntegral(0d);
//        }
//        if (StringUtils.isNotBlank(dto.getContent())) {
//            integralAdjust.setContent(dto.getContent());
//        } else {
//            if (StringUtils.isNotBlank(dto.getRemark())) {
//                integralAdjust.setContent(dto.getRemark());
//            } else {
//                integralAdjust.setContent(dto.getDictName());
//            }
//        }
//        tbMemberIntegraladjustMapper.insert(integralAdjust);
//        return integralAdjust.getId();
//    }
//
//    @Override
//    public List<TbMemberIntegraladjust> recordList(MemberIntegralAdjustRecordPageQueryDto pageQuery) {
//        pageQuery.setOffset(pageQuery.getPage() * pageQuery.getSize());
//        return tbMemberIntegraladjustMapper.getList(pageQuery);
//    }
//
//    @Override
//    public int getCount(MemberIntegralAdjustRecordPageQueryDto pageQuery) {
//        return tbMemberIntegraladjustMapper.getCount(pageQuery);
//    }
//
//    @Async
//    @Override
//    public CompletableFuture<List<MemberMonthlyIntegralResponse>> getMonthlyIntegral(String groupId, String mallId, String vipcode, List<String> moons) {
//        List<TbMemberIntegraladjust> list = tbMemberIntegraladjustMapper.getMonthlyIntegral(groupId, mallId, vipcode, moons);
//        // 按月分组
//        Map<String, List<TbMemberIntegraladjust>> listMap = list.stream().collect(Collectors.groupingBy(TbMemberIntegraladjust::getMoon));
//        List<TbMemberIntegraladjust> tmpList;
//        List<MemberMonthlyIntegralResponse> months = new ArrayList<>(10);
//        for (String moon: moons) {
//            tmpList = MapUtils.getObject(listMap, moon);
//            if (CollectionUtils.isEmpty(tmpList)) {
//                continue;
//            }
//            int sum1 = (int) tmpList.stream().filter(it -> it.getNumber() >= 0).mapToDouble(TbMemberIntegraladjust::getNumber).sum();
//            int sum2 = (int) tmpList.stream().filter(it -> it.getNumber() < 0).mapToDouble(TbMemberIntegraladjust::getNumber).sum();
//            months.add(MemberMonthlyIntegralResponse.builder().month(moon).incomePoints(sum1).bonusPoints(sum2).build());
//        }
//        return CompletableFuture.completedFuture(months);
//    }
//
//    @Override
//    public double getMemberConsumePointsBetweenDate(MemberPointSumQueryDto dto) {
//        return tbMemberIntegraladjustMapper.getMemberConsumePointsBetweenDate(dto);
//    }
//
//    @Async
//    @Override
//    public CompletableFuture<Map<String, TbMemberIntegraladjust>> findByIds(List<String> adjustIds) {
//        if (CollectionUtils.isEmpty(adjustIds)) {
//            return CompletableFuture.completedFuture(Maps.newHashMap());
//        }
//        List<TbMemberIntegraladjust> list = tbMemberIntegraladjustMapper.findByIds(adjustIds);
//        if (CollectionUtils.isEmpty(list)) {
//            return CompletableFuture.completedFuture(Maps.newHashMap());
//        }
//        return CompletableFuture.completedFuture(list.stream().collect(Collectors.toMap(TbMemberIntegraladjust::getId, Function.identity(), (k1, k2) -> k2)));
//    }
//
//    @Override
//    public int integralRecordTotal(String vipCode, String mallId) {
//        return tbMemberIntegraladjustMapper.integralRecordTotal(vipCode, mallId);
//    }
//
//    @Override
//    public List<TbMemberIntegraladjust> integralRecordList(String vipCode, String mallId, int page, int size) {
//        return tbMemberIntegraladjustMapper.integralRecordList(vipCode, mallId, (page - 1) * size, size);
//    }
//
//    @Override
//    public TbMemberIntegraladjust queryAdjustList(String groupId, String mallId, String saleNo, String shopId, String remark) {
//        return tbMemberIntegraladjustMapper.queryAdjustList(groupId, mallId, saleNo, shopId, remark);
//    }
//}
