<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.kerryprops.kip.service.integral.mapper.SysUserMapper">

	<resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.SysUser" >
		<result column="id" property="id" />
		<result column="mallid" property="mallId" />
		<result column="username" property="userName" />
		<result column="password" property="password" />
		<result column="real_name" property="realName" />
		<result column="head_url" property="headUrl" />
		<result column="gender" property="gender" />
		<result column="email" property="email" />
		<result column="mobile" property="mobile" />
		<result column="dept_id" property="deptId" />
		<result column="super_admin" property="superAdmin" />
		<result column="status" property="status" />
		<result column="postid" property="postId" />
		<result column="ipad_user" property="ipadUser" />
		<result column="is_temporary" property="isTemporary" />
		<result column="effective_start_date" property="effectiveStartDate" />
		<result column="effective_end_date" property="effectiveEndDate" />
		<result column="creator" property="creator" />
		<result column="create_date" property="createDate" />
		<result column="updator" property="updater" />
		<result column="update_date" property="updateDate" />
	</resultMap>

	<sql id="Base_Columns">
		id, mallid, username, real_name, head_url, gender, email, mobile, super_admin, status
	</sql>

	<select id="getUserListByIds" resultMap="BaseResultMap">
		select <include refid="Base_Columns" /> from sys_user
		<where>
			<if test="userIds != null and userIds.size > 0">
				AND id IN
				<foreach collection="userIds" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</select>

	<select id="getUserEmailList" resultType="string">
		select email from sys_user
		<where>
			<if test="userIds != null and userIds.size > 0">
				AND id IN
				<foreach collection="userIds" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</select>


</mapper>