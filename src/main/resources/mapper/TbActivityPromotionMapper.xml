<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbActivityPromotionMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbActivityPromotion">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="mallid" jdbcType="VARCHAR" property="mallId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="module" jdbcType="VARCHAR" property="module" />
    <result column="beginTime" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="endTime" jdbcType="TIMESTAMP" property="endTime" />
    <result column="promotionBonusExpirationDate" jdbcType="TIMESTAMP" property="promotionBonusExpirationDate" />
    <result column="shopId" jdbcType="VARCHAR" property="shopId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="times" jdbcType="DECIMAL" property="times" />
    <result column="bonus" jdbcType="INTEGER" property="bonus" />
    <result column="crowd_id" jdbcType="VARCHAR" property="crowdId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <sql id="Base_Columns">
    id, group_id, mallid, `name`, `module`, beginTime, endTime, promotionBonusExpirationDate, shopId, `type`, times,
    bonus, crowd_id, create_date, create_user, update_date, update_user
  </sql>

  <select id="queryByMallIdAndDate" resultMap="BaseResultMap">
    SELECT <include refid="Base_Columns" /> FROM tb_activity_promotion WHERE mallid = #{mallId}
    <if test="saleDate != null and saleDate != ''">
      AND beginTime &lt;= #{saleDate} AND endTime &gt;= #{saleDate}
    </if>
    ORDER BY create_date ASC
  </select>

</mapper>