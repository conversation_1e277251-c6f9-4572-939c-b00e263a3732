<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbBatchIntegralPresentDetailMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbBatchIntegralPresentDetail">
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="group_id" jdbcType="VARCHAR" property="groupId" />
      <result column="record_id" jdbcType="VARCHAR" property="recordId" />
      <result column="status" jdbcType="TINYINT" property="status" />
      <result column="mobile" jdbcType="VARCHAR" property="mobile" />
      <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
      <result column="current_integral" jdbcType="INTEGER" property="currentIntegral" />
      <result column="exec_result" jdbcType="TINYINT" property="execResult" />
      <result column="exec_remark" jdbcType="VARCHAR" property="execRemark" />
      <result column="import_integral" jdbcType="INTEGER" property="importIntegral" />
      <result column="valid_time" jdbcType="TIMESTAMP" property="validTime" />
      <result column="integral_type" jdbcType="VARCHAR" property="integralType" />
      <result column="remark" jdbcType="VARCHAR" property="remark" />
      <result column="overdue_time" jdbcType="TIMESTAMP" property="overdueTime" />
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
      <result column="creator" jdbcType="BIGINT" property="creator" />
      <result column="create_user" jdbcType="VARCHAR" property="createUser" />
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
      <result column="updater" jdbcType="BIGINT" property="updater" />
      <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="queryDetailByVipcodeAndOverdueTime" resultMap="BaseResultMap">
    SELECT * FROM tb_batch_point_present_detail
    where
        `status` = 2
        AND exec_result = 1
        AND overdue_time &gt;= now()
        AND vipcode = #{vipcode}
        AND group_id = #{groupId}
        AND overdue_time &lt;= #{overdueTime}
        ORDER BY overdue_time LIMIT 1
  </select>

</mapper>