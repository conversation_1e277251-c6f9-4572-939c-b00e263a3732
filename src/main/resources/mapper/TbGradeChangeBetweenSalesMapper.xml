<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbGradeChangeBetweenSalesMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbGradeChangeBetweenSales" >
        <result column="id" property="id" />
        <result column="change_id" property="changeId" />
        <result column="sales_start_time" property="salesStartTime" />
        <result column="sales_end_time" property="salesEndTime" />
        <result column="create_date" property="createDate" />
    </resultMap>

    <sql id="Base_Columns">
        id, change_id, sales_start_time, sales_end_time, create_date
    </sql>

    <select id="findByChangeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM tb_grade_change_between_sales WHERE change_id = #{changeId};
    </select>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.kerryprops.kip.service.integral.entity.TbGradeChangeBetweenSales" >
        INSERT INTO tb_grade_change_between_sales (change_id, sales_start_time, sales_end_time, create_date)
        VALUES (#{changeId}, #{salesStartTime}, #{salesEndTime}, NOW());
    </insert>

</mapper>