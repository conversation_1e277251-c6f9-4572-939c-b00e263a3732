<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbPointsRepeatRuleMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbPointsRepeatRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="sales_time_difference" jdbcType="INTEGER" property="salesTimeDifference" />
    <result column="repeat_rule" jdbcType="VARCHAR" property="repeatRule" />
    <result column="white_list" jdbcType="INTEGER" property="whiteList" />
    <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="queryRulesByMallIdAndGroupId" resultMap="BaseResultMap">
    SELECT * FROM tb_points_repeat_rule WHERE `status` = 1
    <if test="mallId != null and mallId != ''">
      AND mall_id = #{mallId}
    </if>
    <if test="groupId != null and groupId != ''">
      AND group_id = #{groupId}
    </if>
    LIMIT 1;
  </select>


</mapper>