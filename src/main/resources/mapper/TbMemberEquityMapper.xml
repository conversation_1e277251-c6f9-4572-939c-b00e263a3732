<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberEquityMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberEquity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="protocol_name" jdbcType="VARCHAR" property="protocolName" />
    <result column="code" jdbcType="INTEGER" property="code" />
  </resultMap>

  <sql id="Base_Column_List">
    id, mall_id, content, `create_user`, update_user, group_id, create_date, update_date, `type`, title, protocol_name, code
  </sql>

  <select id="findByMallIdAndType" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_member_equity
    where mall_id = #{mallId} and group_id = #{groupId}
    <if test="types != null and types.size > 0">
      and `type` in
      <foreach collection="types" open="(" close=")" separator="," item="type">
        #{type}
      </foreach>
    </if>
    ORDER BY create_date DESC;
  </select>

</mapper>