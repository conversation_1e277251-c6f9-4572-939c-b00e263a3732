<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.kerryprops.kip.service.integral.mapper.TicketMemberGradeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.kerryprops.kip.service.integral.model.dto.TicketMemberGradeDto" id="BaseResultMap">
        <result property="integral" column="integral"/>
        <result property="money" column="money"/>
        <result property="ticketId" column="ticketId"/>
    </resultMap>

	<select id="findList" resultMap="BaseResultMap">
	  select integral, money, ticketId from tb_ticket_membergrade
	  <where>
		  <if test="ticketIds != null and ticketIds.size > 0">
			  and ticketId in
	  		  <foreach collection="ticketIds" item="ticketId" open="(" close=")" separator=",">
				  #{ticketId}
			  </foreach>
		  </if>
	  	  <if test="grade != null and grade != ''">
			  and memberGrade = #{grade}
		  </if>
	  </where>
	 </select>

</mapper>