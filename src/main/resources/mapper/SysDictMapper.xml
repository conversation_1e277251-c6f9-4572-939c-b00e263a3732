<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.SysDictMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.SysDict" >
        <result column="id" property="id" />
        <result column="pid" property="pid" />
        <result column="dict_type" property="dictType" />
        <result column="dict_name" property="dictName" />
        <result column="dict_value" property="dictValue" />
        <result column="remark" property="remark" />
        <result column="sort" property="sort" />
        <result column="create_user" property="createUser" />
        <result column="create_date" property="createDate" />
        <result column="update_user" property="updateUser" />
        <result column="update_date" property="updateDate" />
    </resultMap>

    <sql id="Base_Columns">
        id, pid, dict_type, dict_name, dict_value, remark, sort, create_user, create_date, update_user, update_date
    </sql>

    <select id="findByDictTypes" resultMap="BaseResultMap">
        select dict_type, dict_name from tb_sys_dict where dict_type in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findByDictType" resultMap="BaseResultMap">
        select dict_type, dict_name from tb_sys_dict where dict_type = #{dictType} limit 1;
    </select>

    <select id="getListByTypes" resultMap="BaseResultMap">
        SELECT id, dict_type, dict_name, dict_value, remark FROM tb_sys_dict where pid in (
            SELECT id FROM tb_sys_dict
            <where>
                <if test="types != null and types.size > 0">
                    and dict_type in
                    <foreach collection="types" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            </where>
        );
    </select>

</mapper>