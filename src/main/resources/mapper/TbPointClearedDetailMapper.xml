<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbPointClearedDetailMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbPointClearedDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="integral_clear_id" jdbcType="VARCHAR" property="integralClearId" />
    <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="current_integral" jdbcType="INTEGER" property="currentIntegral" />
    <result column="clear_integral" jdbcType="INTEGER" property="clearIntegral" />
    <result column="clear_date" jdbcType="TIMESTAMP" property="clearDate" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="grade" jdbcType="VARCHAR" property="grade" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>

  <sql id="Base_Column_List">
    id, integral_clear_id, vipcode, mobile, nick_name, current_integral, clear_integral, clear_date,
    mall_id, group_id, grade, create_user, create_date, update_user, update_date
  </sql>

  <select id="getDetailByIntegralClearId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_point_cleared_detail where integral_clear_id = #{integralClearId} limit 1;
  </select>

  <select id="getLatestMemberClearIntegral" resultMap="BaseResultMap">
    SELECT d.* FROM (
        SELECT id, group_id FROM tb_point_cleared WHERE group_id = #{groupId} AND `status` = 1 ORDER BY create_date DESC LIMIT 1
    ) AS t join tb_point_cleared_detail d ON t.group_id = d.group_id AND t.id = d.integral_clear_id
    WHERE d.vipcode = #{vipcode};
  </select>

</mapper>