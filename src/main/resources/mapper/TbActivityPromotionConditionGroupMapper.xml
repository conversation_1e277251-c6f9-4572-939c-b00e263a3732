<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbActivityPromotionconditiongroupMapper">
  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbActivityPromotionConditionGroup">
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="promotion_id" jdbcType="VARCHAR" property="promotionId" />
  </resultMap>

  <sql id="Base_columns">
    id, promotion_id
  </sql>

</mapper>