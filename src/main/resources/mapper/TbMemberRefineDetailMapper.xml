<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberRefineDetailMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberRefineField">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_ename" jdbcType="VARCHAR" property="fieldEname" />
    <result column="is_refine" jdbcType="INTEGER" property="isRefine" />
    <result column="is_required" jdbcType="INTEGER" property="isRequired" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="field_type" jdbcType="INTEGER" property="fieldType" />
    <result column="components_type" jdbcType="VARCHAR" property="componentsType" />
  </resultMap>

  <sql id="Base_Column_List">
    id, config_id, field_name, field_ename, is_refine, is_required, sort, field_type, components_type
  </sql>

  <select id="findByConfigId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_member_refine_detail where config_id = #{configId}
    <if test="isRefine != null">
        and is_refine = #{isRefine}
    </if>
    order by sort asc;
  </select>

  <select id="findMallRefineFields" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_member_refine_detail where config_id = (
        select id from tb_member_refine where mall_id = #{mallId} order by id desc limit 1
    ) and is_refine = 1 order by sort asc;
  </select>

</mapper>