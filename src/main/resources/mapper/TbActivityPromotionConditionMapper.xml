<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbActivityPromotionconditionMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbActivityPromotionCondition">
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="promotion_id" jdbcType="VARCHAR" property="promotionId" />
    <result column="promotion_condition_group_id" jdbcType="VARCHAR" property="promotionConditionGroupId" />
    <result column="promotion_condition_type" jdbcType="VARCHAR" property="promotionConditionType" />
    <result column="promotion_condition_content" jdbcType="VARCHAR" property="promotionConditionContent" />
  </resultMap>

  <sql id="Base_Columns">
    id, promotion_id, promotion_condition_group_id, promotion_condition_type, promotion_condition_content
  </sql>
  
  <select id="findByPromotionId" resultMap="BaseResultMap">
    SELECT <include refid="Base_Columns" /> FROM tb_activity_promotion_condition WHERE promotion_id = #{promotionId};
  </select>

  <select id="findByPromotionIds" resultMap="BaseResultMap">
    SELECT <include refid="Base_Columns" /> FROM tb_activity_promotion_condition
    <where>
      <if test="promotionIds != null and promotionIds.size > 0">
        AND promotion_id IN
        <foreach collection="promotionIds" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

</mapper>