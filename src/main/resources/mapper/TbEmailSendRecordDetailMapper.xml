<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbEmailSendRecordDetailMapper">

<!--  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbEmailSendRecordDetail">-->
<!--    <id column="id" jdbcType="BIGINT" property="id" />-->
<!--    <result column="record_id" jdbcType="BIGINT" property="recordId" />-->
<!--    <result column="send_email" jdbcType="VARCHAR" property="sendEmail" />-->
<!--    <result column="remark" jdbcType="VARCHAR" property="remark" />-->
<!--  </resultMap>-->

<!--  <sql id="Base_Column_List">-->
<!--    id, record_id, send_email, remark-->
<!--  </sql>-->

<!--  <insert id="saveBatch" parameterType="com.kerryprops.kip.service.integral.entity.TbEmailSendRecordDetail">-->
<!--    insert into tb_email_send_record_detail (id, record_id, send_email, remark)-->
<!--    values-->
<!--    <foreach collection="list" item="it" separator=",">-->
<!--      (#{it.id}, #{it.recordId}, #{it.sendEmail}, #{it.remark})-->
<!--    </foreach>-->
<!--  </insert>-->

</mapper>