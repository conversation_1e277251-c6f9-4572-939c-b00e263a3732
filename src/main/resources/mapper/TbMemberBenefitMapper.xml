<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberBenefitMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberBenefit">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="member_grade" jdbcType="VARCHAR" property="memberGrade" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>

  <sql id="Base_Column_List">
    id, mall_id, member_grade, `status`, group_id, create_user, create_date, update_user, update_date, is_del
  </sql>

  <select id="findByMallIdAndMemberGrade" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_member_benefit
    where mall_id = #{mallId} and member_grade = #{grade};
  </select>

</mapper>