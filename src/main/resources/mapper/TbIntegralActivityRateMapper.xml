<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbIntegralActivityRateMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbIntegralActivityRate" >
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="grade_id" jdbcType="VARCHAR" property="gradeId" />
        <result column="grade_name" jdbcType="VARCHAR" property="gradeName" />
        <result column="reward_node" jdbcType="VARCHAR" property="rewardNode" />
        <result column="money" jdbcType="DECIMAL" property="money" />
        <result column="point_num" jdbcType="DECIMAL" property="pointNum" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
    </resultMap>

    <select id="activityRatePage" resultMap="BaseResultMap">
        select id, group_id, mall_id, grade_id, grade_name, reward_node,
               money, point_num, status, create_date, creator, update_date, updater
        from tb_integral_activity_rate
        <where>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="rewardNode != null and rewardNode != '' ">
                and reward_node = #{rewardNode}
            </if>
        </where>
        ORDER BY #{order} DESC LIMIT #{offset}, #{size}
    </select>

    <select id="getTotal" resultType="int">
        SELECT count(*) FROM tb_integral_activity_rate
        <where>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="rewardNode != null and rewardNode != '' ">
                and reward_node = #{rewardNode}
            </if>
        </where>
    </select>

    <select id="getActivityRateList" parameterType="com.kerryprops.kip.service.integral.entity.TbIntegralActivityRate" resultMap="BaseResultMap">
        SELECT id, group_id, mall_id, grade_id, grade_name, reward_node,
        money, point_num, status, create_date, creator, update_date, updater
        from tb_integral_activity_rate
        <where>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="rewardNode != null and rewardNode != '' ">
                and reward_node = #{rewardNode}
            </if>
        </where>
    </select>

    <insert id="saveActivityRateList" parameterType="com.kerryprops.kip.service.integral.entity.TbIntegralActivityRate">
        insert into tb_integral_activity_rate(group_id, mall_id, grade_id, grade_name, reward_node,
        money, point_num, status, create_date, creator, update_date, updater) values
        <foreach collection="rateList" separator="," item="it">
            (#{it.groupId}, #{it.mallId}, #{it.gradeId}, #{it.gradeName}, #{it.rewardNode},
             #{it.money}, #{it.pointNum}, #{it.status}, NOW(), #{it.creator}, NOW(), #{it.updater})
        </foreach>
    </insert>

    <delete id="deleteByParams" >
         delete from tb_integral_activity_rate
        <where>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="rewardNode != null and rewardNode != '' ">
                and reward_node = #{rewardNode}
            </if>
        </where>
    </delete>

</mapper>