<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbPointsInterceptConfigMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbPointsInterceptConfig">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
        <result column="time_type" jdbcType="INTEGER" property="timeType" />
        <result column="count_type" jdbcType="INTEGER" property="countType" />
        <result column="count" jdbcType="INTEGER" property="count" />
        <result column="white_list" jdbcType="INTEGER" property="whiteList" />
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    </resultMap>

    <sql id="Base_Column_List">
        id, group_id, mall_id, rule_type, time_type, count_type, count, white_list, shop_no, create_date, create_user, update_date, update_user
    </sql>

    <select id="findByMallId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_points_intercept_config
        where mall_id = #{mallId} order by id asc;
    </select>

</mapper>