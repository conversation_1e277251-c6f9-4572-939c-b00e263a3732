<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbFieldSortMapper">
  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbFieldSort">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_sort" jdbcType="INTEGER" property="fieldSort" />
    <result column="page_type" jdbcType="VARCHAR" property="pageType" />
    <result column="is_initial" jdbcType="INTEGER" property="isInitial" />
    <result column="field_cn_name" jdbcType="VARCHAR" property="fieldCnName" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <sql id="Base_Column_List">
    id, field_name, field_sort, page_type, is_initial, field_cn_name, user_id, group_id, create_date,
    create_user, update_date, update_user
  </sql>

  <select id="selectByUserIdsAndPageType" resultMap="BaseResultMap">
    select field_name, field_sort, field_cn_name, user_id, group_id from tb_field_sort where field_sort &lt;&gt; 0
    <if test="userIds != null and userIds.size > 0">
      and user_id in
      <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
        #{userId}
      </foreach>
    </if>
    <if test="pageType != null and pageType != ''">
      and page_type = #{pageType}
    </if>
    ORDER BY field_sort ASC
  </select>

</mapper>