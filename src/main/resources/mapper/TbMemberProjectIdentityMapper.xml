<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberProjectIdentityMapper">

<!--    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberProjectIdentity">-->
<!--        <id column="id" jdbcType="BIGINT" property="id" />-->
<!--        <result column="kip_user_id" jdbcType="VARCHAR" property="kipUserId" />-->
<!--        <result column="project_id" jdbcType="VARCHAR" property="projectId" />-->
<!--        <result column="office" jdbcType="TINYINT" property="office" />-->
<!--        <result column="apartment" jdbcType="TINYINT" property="apartment" />-->
<!--        <result column="residence" jdbcType="TINYINT" property="residence" />-->
<!--        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />-->
<!--        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        id, kip_user_id, project_id, office, apartment, residence, create_date, update_date-->
<!--    </sql>-->

<!--    <sql id="Base_Column">-->
<!--        id, kip_user_id, project_id, office, apartment, residence-->
<!--    </sql>-->

<!--    <insert id="saveOrUpdateBatch" parameterType="com.kerryprops.kip.service.integral.entity.TbMemberProjectIdentity" useGeneratedKeys="true" keyProperty="id">-->
<!--        insert into tb_member_project_identity(kip_user_id, project_id, office, apartment, residence, create_date, update_date) values-->
<!--        <foreach collection="list" separator="," item="it">-->
<!--            (#{it.kipUserId}, #{it.projectId}, #{it.office}, #{it.apartment}, #{it.residence}, now(), now())-->
<!--        </foreach>-->
<!--        ON DUPLICATE KEY UPDATE office = VALUES(office), apartment = VALUES(apartment), residence = VALUES(residence), update_date = now()-->
<!--    </insert>-->

<!--    <select id="findByKipUserIdAndProjectIds" resultMap="BaseResultMap">-->
<!--        select <include refid="Base_Column" /> from tb_member_project_identity where kip_user_id = #{kipUserId}-->
<!--        <if test="projectIds != null and projectIds.size > 0">-->
<!--          and project_id in-->
<!--          <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">-->
<!--              #{projectId}-->
<!--          </foreach>-->
<!--        </if>-->
<!--    </select>-->

<!--    <delete id="deleteByKipUserId" parameterType="String">-->
<!--        delete from tb_member_project_identity where kip_user_id = #{kipUserId};-->
<!--    </delete>-->

<!--    <delete id="deleteByKipUserIdAndProjectIds">-->
<!--        delete from tb_member_project_identity where kip_user_id = #{kipUserId}-->
<!--        <if test="projectIds != null and projectIds.size > 0">-->
<!--            and project_id in-->
<!--            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">-->
<!--                #{projectId}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </delete>-->

<!--    <delete id="removeByUserIdAndNotInProjectIds">-->
<!--        delete from tb_member_project_identity-->
<!--        where kip_user_id = #{kipUserId}-->
<!--        <if test="projectIds != null and projectIds.size > 0">-->
<!--            and project_id not in-->
<!--            <foreach collection="projectIds" item="item" open="(" close=")" separator=",">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </delete>-->

</mapper>