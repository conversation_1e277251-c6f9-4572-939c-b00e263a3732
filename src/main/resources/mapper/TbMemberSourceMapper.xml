<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberSourceMapper">
  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberSource">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
    <result column="kip_user_id" jdbcType="VARCHAR" property="kipUserId" />
    <result column="utm_lbs" jdbcType="VARCHAR" property="utmLbs" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="utm_channel" jdbcType="VARCHAR" property="utmChannel" />
    <result column="utm_method" jdbcType="VARCHAR" property="utmMethod" />
    <result column="utm_source" jdbcType="VARCHAR" property="utmSource" />
    <result column="utm_function" jdbcType="VARCHAR" property="utmFunction" />
    <result column="utm_user" jdbcType="VARCHAR" property="utmUser" />
    <result column="page_path" jdbcType="VARCHAR" property="pagePath" />
    <result column="original_params" jdbcType="VARCHAR" property="originalParams" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, vipcode, kip_user_id, utm_lbs, channel, utm_channel, utm_method, utm_source, 
    utm_function, utm_user, page_path, original_params, create_user, create_date, last_update_date
  </sql>

  <insert id="saveSource" parameterType="com.kerryprops.kip.service.integral.entity.TbMemberSource">
    insert into tb_member_register_source (id, vipcode, kip_user_id, utm_lbs, channel, utm_channel, utm_method,
                                  utm_source, utm_function, utm_user, page_path, original_params, create_user,
                                  create_date, last_update_date)
    values (#{id}, #{vipcode}, #{kipUserId}, #{utmLbs}, #{channel}, #{utmChannel}, #{utmMethod},
            #{utmSource}, #{utmFunction}, #{utmUser}, #{pagePath}, #{originalParams}, #{createUser},
            NOW(), NOW())
  </insert>

  <select id="findByVipcode" parameterType="string" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_member_register_source
    where vipcode = #{vipcode}
  </select>

</mapper>