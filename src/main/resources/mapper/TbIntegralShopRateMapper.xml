<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbIntegralShopRateMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbIntegralShopRate" >
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="grade_id" jdbcType="VARCHAR" property="gradeId" />
        <result column="grade_name" jdbcType="VARCHAR" property="gradeName" />
        <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
        <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
        <result column="money" jdbcType="DECIMAL" property="money" />
        <result column="point_num" jdbcType="DECIMAL" property="pointNum" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="is_consistent" jdbcType="INTEGER" property="isConsistent" />
    </resultMap>

    <select id="shopRatePage" resultMap="BaseResultMap">
        select id, group_id, mall_id, grade_id, grade_name, shop_id, shop_name,
               IFNULL(money, 0) AS money, IFNULL(point_num, 0) as point_num, status, create_date, creator, update_date, updater
        from tb_integral_shop_rate
        <where>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
            <if test="mallIds != null and mallIds.size > 0 ">
                and mall_id in
                <foreach collection="mallIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="shopId != null and shopId != '' ">
                and shop_id = #{shopId}
            </if>
        </where>
        ORDER BY create_date DESC LIMIT #{offset}, #{size}
    </select>

    <select id="getTotal" resultType="int">
        SELECT count(*) FROM tb_integral_shop_rate
        <where>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
            <if test="mallIds != null and mallIds.size > 0 ">
                and mall_id in
                <foreach collection="mallIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="shopId != null and shopId != '' ">
                and shop_id = #{shopId}
            </if>
        </where>
    </select>

    <select id="getShopRateList" parameterType="com.kerryprops.kip.service.integral.entity.TbIntegralShopRate" resultMap="BaseResultMap">
        SELECT id, group_id, mall_id, grade_id, grade_name, shop_id, shop_name,
        IFNULL(money, 0) as money, IFNULL(point_num, 0) as point_num, status,
        create_date, creator, update_date, updater, is_consistent
        from tb_integral_shop_rate
        <where>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="shopId != null and shopId != '' ">
                and shop_id = #{shopId}
            </if>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
        </where>
    </select>

    <select id="getShopRateDto" parameterType="com.kerryprops.kip.service.integral.entity.TbIntegralShopRate" resultMap="BaseResultMap">
        SELECT id, group_id, mall_id, grade_id, grade_name, shop_id, shop_name,
        IFNULL(money, 0) as money, IFNULL(point_num, 0) as point_num, status, create_date, creator, update_date, updater
        from tb_integral_shop_rate
        <where>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="shopId != null and shopId != '' ">
                and shop_id = #{shopId}
            </if>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
            <if test="gradeId != null and gradeId != '' ">
                and grade_id = #{gradeId}
            </if>
        </where>
        limit 1
    </select>

    <insert id="saveShopRateList" parameterType="com.kerryprops.kip.service.integral.entity.TbIntegralShopRate">
        insert into tb_integral_shop_rate(group_id, mall_id, grade_id, grade_name, shop_id, shop_name,
        money, point_num, status, create_date, creator, update_date, updater, is_consistent) values
        <foreach collection="rateList" separator="," item="it">
            (#{it.groupId}, #{it.mallId}, #{it.gradeId}, #{it.gradeName}, #{it.shopId}, #{it.shopName},
             #{it.money}, #{it.pointNum}, #{it.status}, NOW(), #{it.creator}, NOW(), #{it.updater}, #{it.isConsistent})
        </foreach>
    </insert>

    <delete id="deleteByParams" >
         delete from tb_integral_shop_rate
        <where>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="shopIdList != null and shopIdList.size > 0 ">
                AND shop_id IN
                <foreach collection="shopIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>

    </delete>

</mapper>