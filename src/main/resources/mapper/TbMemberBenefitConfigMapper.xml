<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberBenefitConfigMapper">
  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberBenefitConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="benefits_id" jdbcType="VARCHAR" property="benefitsId" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="member_grade" jdbcType="VARCHAR" property="memberGrade" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="subtitle" jdbcType="VARCHAR" property="subTitle" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="link_type" jdbcType="VARCHAR" property="linkType" />
    <result column="inside_type" jdbcType="VARCHAR" property="insideType" />
    <result column="inside_url" jdbcType="VARCHAR" property="insideUrl" />
    <result column="inside_id" jdbcType="VARCHAR" property="insideId" />
    <result column="two_level_linkage" jdbcType="VARCHAR" property="twoLevelLinkage" />
    <result column="outside_type" jdbcType="VARCHAR" property="outsideType" />
    <result column="outside_app_id" jdbcType="VARCHAR" property="outsideAppid" />
    <result column="outside_url_name" jdbcType="VARCHAR" property="outsideUrlName" />
    <result column="outside_url" jdbcType="VARCHAR" property="outsideUrl" />
    <result column="show_type" jdbcType="VARCHAR" property="showType" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>

  <sql id="Base_Column_List">
    id, benefits_id, mall_id, member_grade, `name`, subtitle, logo, link_type, inside_type,
    inside_url, inside_id, two_level_linkage, outside_type, outside_app_id, outside_url_name,
    outside_url, show_type, content, `status`, group_id, create_user,
    create_date, update_user, update_date, is_del
  </sql>

  <select id="findByMallIdAndMemberGrade" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_member_benefit_config
    where mall_id = #{mallId} and member_grade = #{memberGrade} and is_del = 0;
  </select>

</mapper>