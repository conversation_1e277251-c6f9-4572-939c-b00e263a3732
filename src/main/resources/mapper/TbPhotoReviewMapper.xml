<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbPhotoReviewMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbPhotoReview">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="serial_num" jdbcType="VARCHAR" property="serialNum" />
    <result column="money" jdbcType="DOUBLE" property="money" />
    <result column="bonus" jdbcType="INTEGER" property="bonus" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="qrcode" jdbcType="VARCHAR" property="qrcode" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="upload_date" jdbcType="TIMESTAMP" property="uploadDate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="timestamp" jdbcType="VARCHAR" property="timestamp" />
    <result column="trading_date" jdbcType="TIMESTAMP" property="tradingDate" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="check_name" jdbcType="VARCHAR" property="checkName" />
    <result column="format_id" jdbcType="VARCHAR" property="formatId" />
    <result column="format_name" jdbcType="VARCHAR" property="formatName" />
    <result column="integral_adjust_id" jdbcType="VARCHAR" property="integralAdjustId" />
    <result column="org_points" jdbcType="INTEGER" property="orgPoints" />
    <result column="org_grade" jdbcType="VARCHAR" property="orgGrade" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
  </resultMap>

  <sql id="Base_Column_List">
    id,group_id,mall_id,vipcode,mobile,shop_no,shop_name,serial_num,money,bonus,`type`,image_url,qrcode,description,
    reason,upload_date,status,`timestamp`,trading_date,check_time,check_name,format_id,format_name,integral_adjust_id,
    org_points,org_grade,create_user,create_date,update_user,update_date
  </sql>

  <select id="getReviewPage" resultMap="BaseResultMap" parameterType="com.kerryprops.kip.service.integral.model.dto.BonusSelfQueryDto">
    SELECT b.id,b.group_id,b.mall_id,b.vipcode,b.mobile,b.shop_no,b.shop_name,b.serial_num,b.money,b.bonus,b.`type`,b.image_url,b.qrcode,b.description,b.
    reason,b.upload_date,b.status,b.`timestamp`,b.trading_date,b.check_time,b.check_name,b.format_id,b.format_name,b.integral_adjust_id,b.
    org_points,b.org_grade,b.create_user,b.create_date,b.update_user,b.update_date, c.task_id FROM (
    SELECT id FROM tb_points_review
    <where>
      <if test="id != null and id != 0">
        AND id &lt; #{id}
      </if>
      <if test="groupId != null and groupId != ''">
        AND group_id = #{groupId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        AND vipcode = #{vipcode}
      </if>
      <if test="mobile != null and mobile != ''">
        AND mobile = #{mobile}
      </if>
      <if test="type != null and type != ''">
        AND type = #{type}
      </if>
      <if test="mallId != null and mallId != ''">
        AND mall_id = #{mallId}
      </if>
      <if test="shopNo != null and shopNo != ''">
        AND shop_no = #{shopNo}
      </if>
      <if test="qrcode != null and qrcode != ''">
        AND qrcode = #{qrcode}
      </if>
      <if test="status != null and status.size > 0">
        AND `status` IN
        <foreach collection="status" item="it" open="(" close=")" separator=",">
          #{it}
        </foreach>
      </if>
      <if test="serialNum != null and serialNum != ''">
        AND serial_num LIKE CONCAT(#{serialNum}, '%')
      </if>
      <if test="examineStartTime != null and examineStartTime != ''">
        and check_time between #{examineStartTime} and #{examineEndTime}
      </if>
      <if test="uploadStartTime != null and uploadStartTime != ''">
        and upload_date between #{uploadStartTime} and #{uploadEndTime}
      </if>
      <if test="moneyStart != null">
        AND money &gt;= ${moneyStart}
      </if>
      <if test="moneyEnd != null">
        AND money &lt;= ${moneyEnd}
      </if>
    </where>
    <if test="orderBy != null and orderBy != ''">
      ORDER BY ${orderBy}
    </if>
    <if test="orderBy == null or orderBy == ''">
      ORDER BY upload_date DESC
    </if>
    <if test="offset != null and size != null">
      LIMIT #{offset}, #{size}
    </if>
    ) as t LEFT JOIN tb_points_review b on t.id = b.id
    LEFT JOIN tb_ocr_callback_record c on t.id = c.photo_id
  </select>

  <select id="getBonusList" resultMap="BaseResultMap" parameterType="com.kerryprops.kip.service.integral.model.dto.BonusSelfQueryDto">
    SELECT b.id, b.shop_no, b.`status`, b.integral_adjust_id, b.upload_date, b.shop_name, b.money, b.reason, b.mall_id, b.group_id,
           b.image_url, b.qrcode FROM (
    SELECT id FROM tb_points_review
    <where>
      <if test="groupId != null and groupId != ''">
        AND group_id = #{groupId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        AND vipcode = #{vipcode}
      </if>
      <if test="type != null and type != ''">
        AND type = #{type}
      </if>
      <if test="mallId != null and mallId != ''">
        AND mall_id = #{mallId}
      </if>
      <if test="status != null and status.size > 0">
        AND `status` IN
        <foreach collection="status" item="it" open="(" close=")" separator=",">
          #{it}
        </foreach>
      </if>
    </where>
    ORDER BY upload_date DESC
    <if test="offset != null and size != null">
      LIMIT #{offset}, #{size}
    </if>
    ) as t LEFT JOIN tb_points_review b on t.id = b.id;
  </select>

  <select id="getBonusTotal" resultType="int" parameterType="com.kerryprops.kip.service.integral.model.dto.BonusSelfQueryDto">
    SELECT COUNT(*) FROM tb_points_review
    <where>
      <if test="groupId != null and groupId != ''">
        AND group_id = #{groupId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        AND vipcode = #{vipcode}
      </if>
      <if test="mobile != null and mobile != ''">
        AND mobile = #{mobile}
      </if>
      <if test="type != null and type != ''">
        AND type = #{type}
      </if>
      <if test="mallId != null and mallId != ''">
        AND mall_id = #{mallId}
      </if>
      <if test="shopNo != null and shopNo != ''">
        AND shop_no = #{shopNo}
      </if>
      <if test="qrcode != null and qrcode != ''">
        AND qrcode = #{qrcode}
      </if>
      <if test="status != null and status.size > 0">
        AND `status` IN
        <foreach collection="status" item="it" open="(" close=")" separator=",">
          #{it}
        </foreach>
      </if>
      <if test="serialNum != null and serialNum != ''">
        AND serial_num LIKE CONCAT(#{serialNum}, '%')
      </if>
      <if test="examineStartTime != null and examineStartTime != ''">
        and check_time between #{examineStartTime} and #{examineEndTime}
      </if>
      <if test="uploadStartTime != null and uploadStartTime != ''">
        and upload_date between #{uploadStartTime} and #{uploadEndTime}
      </if>
      <if test="moneyStart != null">
        AND money &gt;= ${moneyStart}
      </if>
      <if test="moneyEnd != null">
        AND money &lt;= ${moneyEnd}
      </if>
    </where>
  </select>

  <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.kerryprops.kip.service.integral.entity.TbPhotoReview">
    insert into tb_points_review (group_id,mall_id,vipcode,mobile,shop_no,shop_name,serial_num,money,bonus,`type`,image_url,qrcode,description,
                               reason,upload_date,status,`timestamp`,trading_date,check_time,check_name,format_id,format_name,integral_adjust_id,
                               org_points,org_grade,create_user,create_date,update_user,update_date)
    values (#{groupId}, #{mallId},#{vipcode},#{mobile},#{shopNo}, #{shopName}, #{serialNum},#{money}, #{bonus}, #{type},
      #{imageUrl}, #{qrcode}, #{description}, #{reason}, #{uploadDate}, #{status},#{timestamp}, #{tradingDate},
      #{checkTime}, #{checkName},#{formatId}, #{formatName},#{integralAdjustId},#{orgPoints}, #{orgGrade},
      #{createUser}, #{createDate}, #{updateUser}, #{updateDate})
  </insert>

  <select id="getForPrevPage" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tb_points_review
    <where>
      and type = 3
      and status = '1'
      <if test="mallId != null and mallId !=''">
        and mall_id = #{mallId}
      </if>
      <if test="startTime != null and endTime !=''">
        and timestamp  BETWEEN #{startTime} AND #{endTime}
      </if>
      <if test="timestamp != null and timestamp !=''">
        and timestamp &lt; #{timestamp}
      </if>
    </where>
    order by timestamp desc limit 1
  </select>

  <select id="getForNextPage" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tb_points_review
    <where>
      and type = 3
      and status = '1'
      <if test="mallId != null and mallId !=''">
        and mall_id = #{mallId}
      </if>
      <if test="startTime != null and endTime !=''">
        and timestamp  BETWEEN #{startTime} AND #{endTime}
      </if>
      <if test="timestamp != null and timestamp !=''">
        and timestamp &gt; #{timestamp}
      </if>
    </where>
    order by timestamp limit 1
  </select>

  <select id="getNextImage" resultType="string">
    SELECT image_url FROM tb_points_review
    <where>
      and type = 3
      and status = '1'
      <if test="mallId != null and mallId !=''">
        and mall_id = #{mallId}
      </if>
      <if test="startTime != null and endTime !=''">
        and timestamp  BETWEEN #{startTime} AND #{endTime}
      </if>
      <if test="timestamp != null and timestamp !=''">
        and timestamp &gt; #{timestamp}
      </if>
    </where>
    order by timestamp limit 1
  </select>

  <select id="findByOcrTaskId" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tb_points_review WHERE id = (
        SELECT photo_id FROM tb_ocr_callback_record WHERE task_id = #{taskId} LIMIT 1
    )
  </select>

  <select id="findByQrCode" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tb_points_review WHERE qrcode = #{qrCode} order by create_date desc limit 1;
  </select>

  <select id="checkPhotoReviewRecord" resultMap="BaseResultMap" parameterType="com.kerryprops.kip.service.integral.model.dto.PhotoReviewCheckDto">
    SELECT <include refid="Base_Column_List"/> FROM tb_points_review
    <where>
      <if test="groupId != null and groupId != ''">
        AND group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        AND vipcode = #{vipcode}
      </if>
      <if test="ticketNo != null and ticketNo != ''">
        AND serial_num = #{ticketNo}
      </if>
      <if test="shopNo != null and shopNo != ''">
        AND shop_no = #{shopNo}
      </if>
      <if test="amount != null">
        AND money = #{amount}
      </if>
      <if test="tradingDate != null and tradingDate !=''">
        and trading_date = #{tradingDate}
      </if>
      <if test="timestamp != null and timestamp !=''">
        and timestamp = #{timestamp}
      </if>
      <if test="status != null and status != ''">
        AND `status` = #{status}
      </if>
      <if test="status == null or status == ''">
        AND `status` IN (1, 2, 5)
      </if>
      <if test="idNotIn != null and idNotIn.size > 0">
        and id not in
        <foreach collection="idNotIn" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by create_date desc limit 1;
  </select>

  <select id="checkQrCodeExists" parameterType="string" resultType="int">
    SELECT COUNT(*) FROM `tb_points_review` WHERE qrcode = #{qrCode};
  </select>

</mapper>