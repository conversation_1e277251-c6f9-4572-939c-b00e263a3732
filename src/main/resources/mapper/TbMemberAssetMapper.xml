<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberAssetMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberAsset">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="crm_id" jdbcType="VARCHAR" property="crmId" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="authorized_mall_id" jdbcType="VARCHAR" property="authorizedMallId" />
        <result column="authorized_date" jdbcType="TIMESTAMP" property="authorizedDate" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="current_points" jdbcType="INTEGER" property="currentPoints" />
        <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
        <result column="grade" jdbcType="VARCHAR" property="grade" />
        <result column="kip_user_id" jdbcType="VARCHAR" property="kipUserId" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="wx_open_market" jdbcType="VARCHAR" property="wxOpenMarket" />
        <result column="is_completed" jdbcType="VARCHAR" property="isCompleted" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="join_time" jdbcType="TIMESTAMP" property="joinTime" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="whether_blacklist" jdbcType="INTEGER" property="whetherBlacklist" />
        <result column="register_source" jdbcType="VARCHAR" property="registerSource" />
        <result column="register_source_label" jdbcType="VARCHAR" property="registerSourceLabel" />
        <result column="register_source_remark" jdbcType="VARCHAR" property="registerSourceRemark" />
    </resultMap>

    <sql id="Base_Column_List">
        id, crm_id, group_id, mall_id, mobile, current_points, vipcode, `status`, wx_open_market, join_time, remark, grade, create_date, update_date, is_completed,
        update_user, create_user, kip_user_id, whether_blacklist, register_source, register_source_label, register_source_remark, authorized_mall_id, authorized_date
    </sql>

    <sql id="Base_Columns">
        id, group_id, mall_id, mobile, current_points, vipcode, `status`, wx_open_market, join_time, remark, grade, create_date, update_date, is_completed,
        kip_user_id, whether_blacklist, register_source, register_source_label, register_source_remark, authorized_mall_id, authorized_date
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.kerryprops.kip.service.integral.entity.TbMemberAsset">
        INSERT IGNORE INTO tb_member_asset(crm_id, group_id, mall_id, mobile, current_points, vipcode, `status`, wx_open_market, join_time, remark, grade, create_date, update_date,
          update_user, create_user, is_completed, kip_user_id, whether_blacklist, register_source, register_source_label, register_source_remark, authorized_mall_id, authorized_date)
        VALUES (#{crmId}, #{groupId}, #{mallId}, #{mobile}, #{currentPoints}, #{vipcode}, "1", #{wxOpenMarket}, #{joinTime}, #{remark}, #{grade}, now(), now(),
         #{updateUser}, #{createUser}, "0", #{kipUserId}, 1, #{registerSource}, #{registerSourceLabel}, #{registerSourceRemark}, #{authorizedMallId}, now())
    </insert>

    <select id="queryMemberByGroupIdAndKipUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns"/> FROM tb_member_asset WHERE group_id = #{groupId}
        <if test="kipUserId !=null and kipUserId != ''">
            and kip_user_id=#{kipUserId}
        </if>
        limit 1
    </select>

    <select id="queryMemberByGroupIdAndKipUserIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns"/> FROM tb_member_asset WHERE group_id = #{groupId} and `status` = 1
        <if test="kipUserIds != null and kipUserIds.size > 0">
            and kip_user_id in
            <foreach collection="kipUserIds" open="(" close=")" separator="," item="item">
               #{item}
            </foreach>
        </if>
    </select>

    <select id="queryMemberByGroupIdAndMobile" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns"/> FROM tb_member_asset WHERE group_id = #{groupId}
        <if test="mobile !=null and mobile != ''">
            and mobile = #{mobile}
        </if>
        limit 1
    </select>

    <select id="queryMemberByGroupIdAndVipcode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns"/> FROM tb_member_asset WHERE group_id = #{groupId}
        <if test="vipcode !=null and vipcode != ''">
            and vipcode = #{vipcode}
        </if>
        limit 1
    </select>

    <select id="findKipUserIdByGroupIdAndVipcode" resultType="string">
        SELECT kip_user_id FROM tb_member_asset WHERE group_id = #{groupId}
        <if test="vipcode !=null and vipcode != ''">
            and vipcode = #{vipcode}
        </if>
        limit 1
    </select>

    <select id="queryMemberByGroupIdAndVipcodes" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns"/> FROM tb_member_asset WHERE group_id = #{groupId}
        <if test="vipcodes !=null and vipcodes.size > 0">
            and vipcode in
            <foreach collection="vipcodes" item="it" open="(" close=")" separator=",">
                #{it}
            </foreach>
        </if>
    </select>

    <update id="updateMemberStatus">
        UPDATE tb_member_asset SET `status` = #{status} WHERE id = #{id};
    </update>

    <update id="updateMemberIntegral" parameterType="com.kerryprops.kip.service.integral.entity.TbMemberAsset">
        UPDATE tb_member_asset SET current_points = current_points + #{adjustIntegralNum}, update_date = NOW() WHERE id = #{id} AND current_points = #{currentPoints};
    </update>

    <update id="fillMemberKipUserId" parameterType="com.kerryprops.kip.service.integral.entity.TbMemberAsset">
        UPDATE tb_member_asset SET kip_user_id=#{kipUserId}, update_date = NOW() WHERE mobile = #{mobile};
    </update>

    <select id="getMemberCountBetweenDate" resultType="int">
        SELECT count(*) FROM tb_member_asset
        <where>
            <if test="groupId !=null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="mallId !=null and mallId != ''">
                AND mall_id = #{mallId}
            </if>
            <if test="startTime != null and startTime != ''">
                AND join_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND join_time &lt;= #{endTime}
            </if>
            <if test="registerSource != null and registerSource != ''">
                AND register_source = #{registerSource}
            </if>
        </where>
    </select>

    <update id="updateMemberCompletedStatus" parameterType="com.kerryprops.kip.service.integral.entity.TbMemberAsset">
        UPDATE tb_member_asset SET is_completed=#{isCompleted}, update_date = NOW() WHERE id = #{id};
    </update>

    <update id="updateRegisterSource" parameterType="com.kerryprops.kip.service.integral.entity.TbMemberAsset">
        UPDATE tb_member_asset
        <set>
            <if test="registerSource != null and registerSource != ''">
                `register_source` = #{registerSource},
            </if>
            <if test="registerSourceLabel != null and registerSourceLabel != ''">
                `register_source_label` = #{registerSourceLabel},
            </if>
            <if test="registerSourceRemark != null and registerSourceRemark != ''">
                `register_source_remark` = #{registerSourceRemark},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateMemberGrade" parameterType="com.kerryprops.kip.service.integral.entity.TbMemberAsset">
        UPDATE tb_member_asset
        <set>
            <if test="grade != null and grade != ''">
                grade = #{grade},
            </if>
            update_date = now()
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateMobileByKipUserId">
        UPDATE tb_member_asset
        <set>
            <if test="mobile != null and mobile != ''">
                mobile = #{mobile},
            </if>
            update_date = now()
        </set>
        WHERE kip_user_id = #{kipUserId}
    </update>

    <select id="findByKipUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM tb_member_asset WHERE kip_user_id = #{kipUserId};
    </select>

    <select id="findByMobile" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM tb_member_asset WHERE mobile = #{mobile};
    </select>

    <select id="findByDto" parameterType="com.kerryprops.kip.service.integral.model.dto.SingleMemberQueryDto" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns"/> FROM tb_member_asset WHERE group_id = #{groupId}
        <if test="vipcode !=null and vipcode != ''">
            and vipcode = #{vipcode}
        </if>
        <if test="mobile != null and mobile != ''">
            and mobile = #{mobile}
        </if>
        <if test="kipUserId != null and kipUserId != ''">
            and kip_user_id = #{kipUserId}
        </if>
        limit 1
    </select>

    <!--将即将被删除的会员数据备份到tb_member_invalid表中-->
    <insert id="copyToMemberInvalidByKipUserId" parameterType="java.lang.String">
        INSERT INTO tb_member_asset_invalid (id, group_id, mall_id, mobile, current_points, vipcode, `status`, wx_open_market, join_time, remark, grade, create_date, update_date,
        update_user, `create_user`, is_completed, kip_user_id, whether_blacklist, register_source, register_source_label, register_source_remark)
        SELECT id, group_id, mall_id, mobile, current_points, vipcode, `status`, wx_open_market, join_time, remark, grade, create_date, now() as update_date,
        update_user, create_user, is_completed, kip_user_id, whether_blacklist, register_source, register_source_label, register_source_remark FROM tb_member_asset
        <where>
            <if test="kipUserId != null and kipUserId != ''">
                AND kip_user_id = #{kipUserId}
            </if>
            <if test="groupIds !=null and groupIds.size > 0">
                AND group_id IN
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
    </insert>

    <delete id="removeMemberByKipUserId">
        DELETE FROM tb_member_asset
        <where>
            <if test="kipUserId != null and kipUserId != ''">
                AND kip_user_id = #{kipUserId}
            </if>
            <if test="groupIds !=null and groupIds.size > 0">
                AND group_id IN
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
    </delete>

    <!--将即将被删除的会员数据备份到tb_member_asset_invalid表中-->
    <insert id="copyToMemberInvalidByMobile">
        INSERT INTO tb_member_asset_invalid (id, group_id, mall_id, mobile, current_points, vipcode, `status`, wx_open_market, join_time, remark, grade, create_date, update_date,
        update_user, `create_user`, is_completed, kip_user_id, whether_blacklist, register_source, register_source_label, register_source_remark)
        SELECT id, group_id, mall_id, mobile, current_points, vipcode, `status`, wx_open_market, join_time, remark, grade, create_date, now() as update_date,
        update_user, create_user, is_completed, kip_user_id, whether_blacklist, register_source, register_source_label, register_source_remark FROM tb_member_asset
        <where>
            <if test="mobile != null and mobile != ''">
                AND mobile = #{mobile}
            </if>
            <if test="groupIds !=null and groupIds.size > 0">
                AND group_id IN
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
    </insert>

    <delete id="removeMemberByMobile">
        DELETE FROM tb_member_asset
        <where>
            <if test="mobile != null and mobile != ''">
                AND mobile = #{mobile}
            </if>
            <if test="groupIds !=null and groupIds.size > 0">
                AND group_id IN
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
        </where>
    </delete>

    <select id="getLatestMemberAsset" resultMap="BaseResultMap">
        select <include refid="Base_Columns" /> FROM tb_member_asset order by create_date desc limit 1;
    </select>

    <select id="findByGroupIdsAndKipUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns"/> FROM tb_member_asset
        <where>
            <if test="groupIds != null and groupIds.size > 0">
                AND group_id IN
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="kipUserId != null and kipUserId != ''">
                AND kip_user_id = #{kipUserId}
            </if>
        </where>
    </select>

    <select id="findByGroupIdsAndMobile" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns"/> FROM tb_member_asset
        <where>
            <if test="groupIds != null and groupIds.size > 0">
                AND group_id IN
                <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach>
            </if>
            <if test="mobile != null and mobile != ''">
                AND mobile = #{mobile}
            </if>
        </where>
    </select>

</mapper>