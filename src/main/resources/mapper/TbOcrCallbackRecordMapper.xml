<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbOcrCallbackRecordMapper">
  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbOcrCallbackRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="photo_id" jdbcType="BIGINT" property="photoId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>

  <sql id="Base_Column_List">
    id, task_id, photo_id, content, create_date
  </sql>

  <select id="findByPhotoIds" resultMap="BaseResultMap">
    SELECT task_id, photo_id FROM tb_ocr_callback_record
    <where>
      <if test="photoIds != null and photoIds.size > 0">
        AND photo_id IN
        <foreach collection="photoIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
</mapper>