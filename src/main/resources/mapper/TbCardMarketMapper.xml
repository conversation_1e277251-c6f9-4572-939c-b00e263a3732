<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbCardMarketMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbCardMarket">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="zfb_name" jdbcType="VARCHAR" property="zfbName" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="mall_ali" jdbcType="VARCHAR" property="mallAli" />
    <result column="is_to_mini_program_ali" jdbcType="INTEGER" property="isToMiniProgramAli" />
    <result column="pages_ali" jdbcType="VARCHAR" property="pagesAli" />
    <result column="appid_ali" jdbcType="VARCHAR" property="appidAli" />
  </resultMap>

  <sql id="Base_Column_List">
    id, group_id, mall_id, `name`, zfb_name, latitude, longitude, url, template_id, `status`, 
    create_time, update_date, mall_ali, is_to_mini_program_ali, pages_ali, appid_ali
  </sql>

  <select id="selectByMallAli" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_card_market where mall_ali = #{mallAli} limit 1
  </select>

</mapper>