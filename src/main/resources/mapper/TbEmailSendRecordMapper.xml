<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbEmailSendRecordMapper">
<!--  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbEmailSendRecord">-->
<!--    <id column="id" jdbcType="BIGINT" property="id" />-->
<!--    <result column="mallid" jdbcType="VARCHAR" property="mallId" />-->
<!--    <result column="group_id" jdbcType="VARCHAR" property="groupId" />-->
<!--    <result column="template_id" jdbcType="BIGINT" property="templateId" />-->
<!--    <result column="email_subject" jdbcType="VARCHAR" property="emailSubject" />-->
<!--    <result column="email_content" jdbcType="VARCHAR" property="emailContent" />-->
<!--    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />-->
<!--    <result column="status" jdbcType="INTEGER" property="status" />-->
<!--    <result column="create_user" jdbcType="VARCHAR" property="createUser" />-->
<!--    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />-->
<!--    <result column="creator" jdbcType="BIGINT" property="creator" />-->
<!--    <result column="fail_cause" jdbcType="VARCHAR" property="failCause" />-->
<!--  </resultMap>-->
<!--  <sql id="Base_Column_List">-->
<!--    id, mallid, group_id, template_id, email_subject, email_content, send_time, `status`, -->
<!--    create_user, create_date, creator, fail_cause-->
<!--  </sql>-->

<!--  <insert id="saveRecord" parameterType="com.kerryprops.kip.service.integral.entity.TbEmailSendRecord">-->
<!--    insert into tb_email_send_record (id, mallid, group_id, template_id,-->
<!--      email_subject, email_content, send_time, -->
<!--      `status`, create_user, create_date, creator, fail_cause)-->
<!--    values (#{id}, #{mallId}, #{groupId}, #{templateId},-->
<!--      #{emailSubject}, #{emailContent}, #{sendTime},-->
<!--      #{status}, #{createUser}, NOW(), #{creator}, #{failCause})-->
<!--  </insert>-->

</mapper>