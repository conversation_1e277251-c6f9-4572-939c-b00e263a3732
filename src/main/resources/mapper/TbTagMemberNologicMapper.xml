<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbTagMemberNologicMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbTagMemberNologic">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="groupId" column="group_id" jdbcType="VARCHAR"/>
        <result property="mallId" column="mall_id" jdbcType="VARCHAR"/>
        <result property="vipcode" column="vipcode" jdbcType="VARCHAR"/>
        <result property="tagIds" column="tag_ids" jdbcType="VARCHAR"/>
        <result property="lables" column="lables" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, group_id, mall_id, vipcode, tag_ids, lables, create_date, update_date
    </sql>

    <select id="getByGroupIdAndVipcode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_tag_member_no_logic
        <where>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="vipcode != null and vipcode != ''">
                AND vipcode = #{vipcode}
            </if>
        </where>
        LIMIT 1;
    </select>

</mapper>
