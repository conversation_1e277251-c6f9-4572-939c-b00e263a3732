<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbInsensatePointsAuthRecordMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord">
        <result column="id" property="id"/>
        <result column="open_id" property="openId"/>
        <result column="kip_user_id" property="kipUserId"/>
        <result column="group_id" property="groupId"/>
        <result column="mall_id" property="mallId"/>
        <result column="card_no" property="cardNo"/>
        <result column="status" property="status"/>
        <result column="origin" property="origin"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="mchid" property="mchid" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        open_id,
        kip_user_id,
        group_id,
        mall_id,
        status,
        origin,
        create_date,
        update_date,
        mchid,
        card_no
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.kerryprops.kip.service.integral.entity.TbInsensatePointsAuthRecord">
        INSERT IGNORE INTO tb_insensate_points_auth_record
            (open_id, kip_user_id, group_id, mall_id, status, origin, create_date, update_date, mchid, card_no)
        values
            (#{openId}, #{kipUserId}, #{groupId}, #{mallId}, 0, #{origin}, NOW(), NOW(), #{mchid}, #{cardNo})
    </insert>

    <insert id="insertBatchFromCardMemberRelation">
        INSERT INTO tb_insensate_points_auth_record (open_id, kip_user_id, group_id, mall_id, origin, card_no, update_date)
            SELECT cmr.user_id, m.kip_user_id, cmr.group_id, cmr.mall_id, '1', biz_card_no, '1997-03-26'  from tb_card_member_relation cmr
                LEFT JOIN tb_member_asset m
                ON cmr.group_id=m.group_id and cmr.mobile=m.mobile
            WHERE m.kip_user_id is not null  order by m.kip_user_id ASC
        ON DUPLICATE KEY UPDATE tb_insensate_points_auth_record.update_date = '1997-03-26';
    </insert>

    <select id="selectByOpenId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_insensate_points_auth_record
        <where>
            open_id = #{openId}
            and mall_id = #{mallId}
            and origin = #{origin}
            <if test="groupId != null and groupId != ''">
              and  group_id = #{groupId}
            </if>
            <if test="status != null ">
              and  status = #{status}
            </if>
        </where>
        limit 1
    </select>

    <select id="getByOpenIdAndMallId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_insensate_points_auth_record
        <where>
            <if test="openId != null and openId != ''">
                and open_id = #{openId}
            </if>
            <if test="mallId != null and mallId != ''">
                and mall_id = #{mallId}
            </if>
        </where>
        limit 1;
    </select>

    <select id="selectByKipUserIdAndMallId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_insensate_points_auth_record
        where kip_user_id = #{kipUserId}
        <if test="mallId != null and mallId != ''">
            and mall_id = #{mallId}
        </if>
        and `status` = 0
    </select>

    <select id="getByKipUserIdAndMallIdAndOrigin" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_insensate_points_auth_record
        where kip_user_id = #{kipUserId}
        <if test="mallId != null and mallId != ''">
            and mall_id = #{mallId}
        </if>
        and origin=#{origin}
        and `status` = 0
        ORDER BY create_date DESC
        limit 1;
    </select>

    <select id="checkExists" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_insensate_points_auth_record
        where open_id = #{openId}
        and mall_id = #{mallId}
        and origin=#{origin}
        <if test="groupId != null and groupId != ''">
            and group_id = #{groupId}
        </if>
        limit 1
    </select>

    <select id="getBatchData" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_insensate_points_auth_record
        <where>
            <if test="id != null">
                and id &gt; #{id}
            </if>
            and origin = 0
        </where>
        ORDER BY id ASC LIMIT #{batchSize};
    </select>

    <select id="getMallBatchData" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_insensate_points_auth_record
        WHERE origin = 0 and `status` = 0
        <if test="mallId != null and mallId != ''">
            AND mall_id = #{mallId}
        </if>
        <if test="id != null">
            AND id &gt; #{id}
        </if>
        ORDER BY id ASC LIMIT #{batchSize};
    </select>

    <select id="getByCardNoAndOrigin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_insensate_points_auth_record
        where card_no = #{cardNo}
        <if test="origin != null">
            and origin = #{origin}
        </if>
        ORDER BY id DESC LIMIT 1;
    </select>

    <select id="findWxAuthRecordByKipUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_insensate_points_auth_record
        WHERE kip_user_id = #{kipUserId} AND origin = 0 and `status` = 0;
    </select>
</mapper>