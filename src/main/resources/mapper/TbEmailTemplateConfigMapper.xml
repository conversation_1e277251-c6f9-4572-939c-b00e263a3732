<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbEmailTemplateConfigMapper">
  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbEmailTemplateConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mallid" jdbcType="VARCHAR" property="mallId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="email_send_node" jdbcType="INTEGER" property="emailSendNode" />
    <result column="memberGrade" jdbcType="VARCHAR" property="memberGrade" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="email_subject" jdbcType="VARCHAR" property="emailSubject" />
    <result column="email_content" jdbcType="VARCHAR" property="emailContent" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
    <result column="start_date_status" jdbcType="INTEGER" property="startDateStatus" />
  </resultMap>

  <sql id="Base_Column_List">
    id, `name`, mallid, group_id, email_send_node, memberGrade, start_date, end_date, 
    email_subject, email_content, `status`, create_date, update_date, create_user, update_user, 
    creator, updater, start_date_status
  </sql>

  <select id="findByEntity" resultMap="BaseResultMap">
    SELECT e.* FROM (
        SELECT id FROM tb_email_template_config WHERE `status` = 0 AND start_date_status = 0
        <if test="emailSendNode != null">
          AND email_send_node = #{emailSendNode}
        </if>
        <if test="memberGrade!=null and memberGrade !=''">
          AND memberGrade = #{memberGrade}
        </if>
        <if test="mallId != null and mallId != ''">
          AND mallid = #{mallId}
        </if>
        <if test="groupId != null and groupId != ''">
          AND group_id = #{groupId}
        </if>
        UNION ALL
        SELECT id FROM tb_email_template_config WHERE `status` = 0 AND start_date_status = 1 AND NOW() &gt;= start_date AND NOW() &lt;= end_date
        <if test="emailSendNode != null">
          AND email_send_node = #{emailSendNode}
        </if>
        <if test="memberGrade!=null and memberGrade !=''">
          AND memberGrade = #{memberGrade}
        </if>
        <if test="mallId != null and mallId != ''">
          AND mallid = #{mallId}
        </if>
        <if test="groupId != null and groupId != ''">
          AND group_id = #{groupId}
        </if>
    ) as t left join tb_email_template_config e on t.id = e.id order by e.create_date desc
  </select>

</mapper>