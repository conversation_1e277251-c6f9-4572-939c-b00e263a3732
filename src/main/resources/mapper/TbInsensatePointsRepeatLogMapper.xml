<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbInsensatePointsRepeatLogMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbInsensatePointsRepeatLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="transaction_id" jdbcType="VARCHAR" property="transactionId" />
    <result column="origin" property="origin" />
    <result column="repeat_sale_id" jdbcType="VARCHAR" property="repeatSaleId" />
    <result column="create_date" property="createDate" />
  </resultMap>

  <select id="getByTransactionIdAndOrigin" resultMap="BaseResultMap">
    select * from tb_insensate_points_repeat_log
    where transaction_id = #{transactionId} and origin = #{origin} limit 1;
  </select>

</mapper>