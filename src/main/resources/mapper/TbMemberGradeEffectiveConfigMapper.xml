<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberGradeEffectiveConfigMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberGradeEffectiveConfig" >
        <result column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="enable_downgrade_job" property="enableDowngradeJob" />
        <result column="enable_refund_downgrade" property="enableRefundDowngrade" />
        <result column="create_date" property="createDate" />
        <result column="create_user" property="createUser" />
        <result column="update_date" property="updateDate" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <sql id="Base_Column_List">
        id, group_id, enable_downgrade_job, enable_refund_downgrade, create_user, create_date, update_user, update_date
    </sql>

    <select id="findByGroupId" parameterType="string" resultMap="BaseResultMap">
        select id, group_id, enable_downgrade_job, enable_refund_downgrade from tb_member_grade_effective_config where group_id = #{groupId};
    </select>


</mapper>