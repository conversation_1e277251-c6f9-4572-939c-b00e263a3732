<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbBkcSaleMemberMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbBkcSaleMember">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
        <result column="sale_case" jdbcType="INTEGER" property="saleCase" />
        <result column="confirm_status" jdbcType="INTEGER" property="confirmStatus" />
    </resultMap>

    <sql id="Base_Column_List">
        id, group_id, vipcode, sale_case, confirm_status
    </sql>

    <select id="findByVipcode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_bkc_sale_member
        <where>
            <if test="vipcode != null and vipcode != ''">
                and vipcode = #{vipcode}
            </if>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
        </where>
        limit 1;
    </select>

    <insert id="saveOrUpdate" parameterType="com.kerryprops.kip.service.integral.entity.TbBkcSaleMember" keyProperty="id" keyColumn="id" useGeneratedKeys="true">
        INSERT INTO tb_bkc_sale_member(group_id, vipcode, sale_case, confirm_status)
        VALUES (#{groupId}, #{vipcode}, #{saleCase}, #{confirmStatus}) ON DUPLICATE KEY UPDATE sale_case = #{saleCase}, confirm_status = #{confirmStatus};
    </insert>

</mapper>