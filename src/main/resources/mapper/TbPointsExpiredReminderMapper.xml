<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbPointsExpiredReminderMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbPointsExpiredReminder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="is_show_c" jdbcType="INTEGER" property="isShowC" />
    <result column="show_type" jdbcType="INTEGER" property="showType" />
    <result column="ahead_month_num" jdbcType="INTEGER" property="aheadMonthNum" />
    <result column="ahead_day_num" jdbcType="INTEGER" property="aheadDayNum" />
    <result column="year_end" jdbcType="VARCHAR" property="yearEnd" />
    <result column="not_year_end" jdbcType="VARCHAR" property="notYearEnd" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <sql id="Base_Column_List">
    id, mall_id, is_show_c, show_type, ahead_month_num, ahead_day_num, year_end, not_year_end,
    group_id, create_date, create_user, update_date, update_user
  </sql>

  <select id="findByMallIdAndGroupId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_points_expired_reminder
    where mall_id = #{mallId} and group_id = #{groupId} limit 1;
  </select>

</mapper>