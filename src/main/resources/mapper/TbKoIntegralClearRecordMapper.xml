<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbKoIntegralClearRecordMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbKoIntegralClearRecord">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
        <result column="get_points" jdbcType="INTEGER" property="getPoints" />
        <result column="cost_points" jdbcType="INTEGER" property="costPoints" />
        <result column="clear_points" jdbcType="INTEGER" property="clearPoints" />
        <result column="points_num" jdbcType="INTEGER" property="pointsNum" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    </resultMap>

    <sql id="Base_Column_List">
        id, group_id, vipcode, get_points, cost_points, clear_points, points_num
    </sql>

    <insert id="saveBatch" parameterType="com.kerryprops.kip.service.integral.entity.TbKoIntegralClearRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tb_ko_integral_clear_record(group_id, vipcode, get_points, cost_points, clear_points, points_num, create_date, update_date) values
        <foreach collection="list" separator="," item="it">
            (#{it.groupId}, #{it.vipcode}, #{it.getPoints}, #{it.costPoints}, #{it.clearPoints}, #{it.pointsNum}, now(), now())
        </foreach>
    </insert>

    <select id="total" resultType="int">
        select count(*) from tb_ko_integral_clear_record
    </select>

    <select id="findList" resultMap="BaseResultMap">
        select r.id, r.group_id, r.vipcode, r.get_points, r.cost_points, r.clear_points, r.points_num from (
            select id from tb_ko_integral_clear_record order by id asc limit #{offset}, #{size}
        ) as t join tb_ko_integral_clear_record r on t.id = r.id
    </select>

</mapper>