<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbQrCodeRecordMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbQrCodeRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="qr_code" jdbcType="VARCHAR" property="qrCode" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>

  <sql id="Base_Column_List">
    id, qr_code, content, create_date
  </sql>

  <select id="getByQrCode" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_qr_code_record where qr_code = #{qrCode} order by id desc limit 1;
  </select>

</mapper>