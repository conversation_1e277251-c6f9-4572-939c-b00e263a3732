<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbSaleMatchedPromotionMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbSaleMatchedPromotion">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="sell_no" jdbcType="VARCHAR" property="sellNo" />
        <result column="integral_adjust_id" jdbcType="VARCHAR" property="integralAdjustId" />
        <result column="promotion_id" jdbcType="VARCHAR" property="promotionId" />
        <result column="promotion_name" jdbcType="VARCHAR" property="promotionName" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    </resultMap>

    <sql id="Base_Column_List">
        id, sell_no, integral_adjust_id, promotion_id, promotion_name
    </sql>

    <select id="findBySellNos" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_sale_matched_promotion
        <where>
            <if test="sellNos != null and sellNos.size > 0">
                AND sell_no in
                <foreach collection="sellNos" item="it" open="(" close=")" separator=",">
                    #{it}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findByAdjustIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_sale_matched_promotion
        <where>
            <if test="adjustIds != null and adjustIds.size > 0">
                AND integral_adjust_id in
                <foreach collection="adjustIds" item="it" open="(" close=")" separator=",">
                    #{it}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="saveBatch" parameterType="com.kerryprops.kip.service.integral.entity.TbSaleMatchedPromotion" useGeneratedKeys="true" keyProperty="id">
        insert into tb_sale_matched_promotion(sell_no, integral_adjust_id, promotion_id, promotion_name, create_date, update_date) values
        <foreach collection="list" separator="," item="it">
            (#{it.sellNo}, #{it.integralAdjustId}, #{it.promotionId}, #{it.promotionName}, now(), now())
        </foreach>
    </insert>

</mapper>