<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbCardMemberRelationMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbCardMemberRelation" >
        <result column="id" property="id" />
        <result column="mobile" property="mobile" />
        <result column="name" property="name" />
        <result column="gender" property="gender" />
        <result column="mall_id" property="mallId" />
        <result column="group_id" property="groupId" />
        <result column="biz_card_no" property="bizCardNo" />
        <result column="external_card_no" property="externalCardNo" />
        <result column="open_date" property="openDate" />
        <result column="valid_date" property="validDate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="user_id" property="userId" />
        <result column="mall_type" property="mallType" />
    </resultMap>

    <sql id="Base_Column_List">
        id, mobile, `name`, gender, mall_id, group_id, biz_card_no, external_card_no, open_date,
        valid_date, user_id, mall_type, create_time, update_time
    </sql>

    <select id="getByGroupIdAndAliUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_member_mall_relation
        where group_id = #{groupId} and user_id = #{userId} limit 1;
    </select>

    <select id="getByMallIdAndAliUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_member_mall_relation
        where mall_id = #{mallId} and user_id = #{userId} limit 1;
    </select>

    <select id="getByMallIdAndMobile" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_member_mall_relation
        where mall_id = #{mallId} and mobile = #{mobile} limit 1;
    </select>

    <select id="getByMallIdAndExternalCardNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_member_mall_relation
        where mall_id = #{mallId} and external_card_no = #{externalCardNo} limit 1;
    </select>

    <select id="getByAliUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select <include refid="Base_Column_List"/> from tb_member_mall_relation
        where user_id = #{userId} limit 1;
    </select>

    <select id="getByGroupIdAndMobileAndMallId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_member_mall_relation
        where group_id = #{groupId} and mobile = #{mobile} and mall_id = #{mallId} limit 1;
    </select>

    <select id="findByExternalCardNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_member_mall_relation
        where external_card_no = #{externalCardNo} order by create_time desc limit 1;
    </select>

</mapper>