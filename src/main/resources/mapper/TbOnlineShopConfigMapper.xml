<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbOnlineShopConfigMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="grade" jdbcType="VARCHAR" property="grade" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="money" jdbcType="DECIMAL" property="money" />
        <result column="point_num" jdbcType="DECIMAL" property="pointNum" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="is_consistent" jdbcType="INTEGER" property="isConsistent" />
    </resultMap>

    <sql id="BaseColumns">
        id, group_id, mall_id, grade, business_type, money, point_num, creator, is_consistent
    </sql>

    <select id="onlineShopPage" resultMap="BaseResultMap">
        select * from tb_online_shop_rate
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="businessTypes != null and businessTypes.size > 0 ">
                and business_type in
                <foreach collection="businessTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="mallIdList != null and mallIdList.size > 0">
                and mall_id in
                <foreach collection="mallIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by id desc limit #{offset}, #{size};
    </select>

    <select id="onlineShopTotal" resultType="int">
        select count(*) from tb_online_shop_rate
        <where>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
            <if test="businessTypes != null and businessTypes.size > 0 ">
                and business_type in
                <foreach collection="businessTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="mallIdList != null and mallIdList.size > 0">
                and mall_id in
                <foreach collection="mallIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getOnlineShop" parameterType="com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig" resultMap="BaseResultMap">
        select <include refid="BaseColumns" /> from tb_online_shop_rate
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="mallId != null and mallId != ''">
                and mall_id = #{mallId}
            </if>
            <if test="grade != null and grade != ''">
                and grade = #{grade}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
        </where>
    </select>

    <delete id="deleteByParam" parameterType="com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig">
           delete from tb_online_shop_rate where mall_id = #{mallId} and business_type = #{businessType}
    </delete>

    <insert id="insertOnlineShopBatch" parameterType="com.kerryprops.kip.service.integral.entity.TbOnlineShopConfig">
        insert into tb_online_shop_rate(group_id, mall_id, grade, business_type, money, point_num,
        status, create_date, creator, update_date, updater, is_consistent) values
        <foreach collection="list" separator="," item="it">
            (#{it.groupId}, #{it.mallId}, #{it.grade}, #{it.businessType}, #{it.money}, #{it.pointNum},
             #{it.status}, now(), #{it.creator}, now(), #{it.updater}, #{it.isConsistent})
        </foreach>
    </insert>

    <select id="findByMallIdAndGradeAndBusinessType" resultMap="BaseResultMap">
        select <include refid="BaseColumns" /> from tb_online_shop_rate
        <where>
            <if test="mallId != null and mallId != ''">
                and mall_id = #{mallId}
            </if>
            <if test="grade != null and grade != ''">
                and grade = #{grade}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
        </where>
        limit 1;
    </select>

</mapper>