<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbPointsDetailMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbPointsDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="crm_id" jdbcType="VARCHAR" property="crmId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="current_points" jdbcType="INTEGER" property="currentPoints" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="left_points" jdbcType="INTEGER" property="leftPoints" />
    <result column="reason_type" jdbcType="VARCHAR" property="reasonType" />
    <result column="reason_desc" jdbcType="VARCHAR" property="reasonDesc" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="pos_no" jdbcType="VARCHAR" property="posNo" />
    <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="expire_date" jdbcType="VARCHAR" property="expireDate" />
    <result column="extend1" jdbcType="VARCHAR" property="extend1" />
    <result column="extend2" jdbcType="VARCHAR" property="extend2" />
    <result column="extend3" jdbcType="VARCHAR" property="extend3" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="org_points" jdbcType="INTEGER" property="orgPoints" />
    <result column="org_grade" jdbcType="VARCHAR" property="orgGrade" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <sql id="Base_Column_List">
    id, crm_id, group_id, mall_id, mobile, vipcode, `type`, current_points, amount, left_points,
    reason_type, reason_desc, order_no, pos_no, shop_no, remark, expire_date, extend1,
    extend2, extend3, image_url, channel, org_points, org_grade, create_date, create_user, 
    update_date, update_user
  </sql>

  <select id="getTotalPointsOfDay" resultType="int">
    select IFNULL(SUM(`amount`), 0) from `tb_points_detail`
    where group_id = #{groupId} and vipcode = #{vipcode}
    <if test="remarks != null and remarks.size > 0">
      and reason_type in
      <foreach collection="remarks" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    AND DATE_FORMAT(create_date, '%Y-%m-%d') = #{date};
  </select>

  <select id="getCount" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberIntegralAdjustRecordPageQueryDto" resultType="int">
    select count(*) from `tb_points_detail`
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
      <if test="excludeList != null and excludeList.size > 0">
        and reason_type not in
        <foreach collection="excludeList" open="(" close=")" item="item" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="startTime != null and startTime != ''">
        and create_date &gt;= #{startTime}
      </if>
      <if test="type != null">
        <if test="type == 0">
          and `amount` &lt;&gt; 0
        </if>
        <if test="type == 1">
          and amount &gt; 0
        </if>
        <if test="type == 2">
          and amount &lt; 0
        </if>
      </if>
      <if test="type == null">
        and amount &lt;&gt; 0
      </if>
    </where>
  </select>

  <select id="getList" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberIntegralAdjustRecordPageQueryDto" resultMap="BaseResultMap">
    select a.id, a.group_id, a.mall_id, a.mobile, a.vipcode, a.`type`, a.current_points, a.amount, a.reason_type, a.reason_desc, a.order_no,
      a.pos_no, a.shop_no, a.remark, a.expire_date, a.image_url, a.create_date from (
    select id from tb_points_detail
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
      <if test="excludeList != null and excludeList.size > 0">
        and reason_type not in
        <foreach collection="excludeList" open="(" close=")" item="item" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="startTime != null and startTime != ''">
        and create_date &gt;= #{startTime}
      </if>
      <if test="type != null">
        <if test="type == 0">
          and `amount` &lt;&gt; 0
        </if>
        <if test="type == 1">
          and amount &gt; 0
        </if>
        <if test="type == 2">
          and amount &lt; 0
        </if>
      </if>
      <if test="type == null">
        and amount &lt;&gt; 0
      </if>
    </where>
    ORDER BY create_date DESC LIMIT #{offset}, #{size}
    ) as t join tb_points_detail a on t.id = a.id ORDER BY a.create_date DESC;
  </select>

  <select id="getPointsList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> FROM tb_points_detail WHERE group_id = #{groupId} and vipcode = #{vipcode};
  </select>

  <select id="getMonthlyIntegral" resultMap="BaseResultMap">
    select DATE_FORMAT(create_date, '%Y-%m') as moon, amount from `tb_points_detail`
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
      <if test="list != null and list.size > 0">
        and DATE_FORMAT(create_date,'%Y-%m') in
        <foreach collection="list" item="it" open="(" close=")" separator=",">
          #{it}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getMemberConsumePointsBetweenDate" resultType="int">
    select IFNULL(SUM(amount), 0) from `tb_points_detail`
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
      <if test="startDate != null and startDate != ''">
        AND create_date &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND create_date &lt;= #{endDate}
      </if>
      <if test="type == 1">
        AND amount &gt;= 0
      </if>
      <if test="type == 2">
        AND amount &lt; 0
      </if>
    </where>
  </select>

  <select id="findByCrmIds" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" /> FROM `tb_points_detail`
    WHERE crm_id IN
    <foreach collection="list" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
  </select>

  <select id="integralRecordTotal" resultType="int">
    select count(*) from `tb_points_detail`
    <where>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
    </where>
  </select>

  <select id="integralRecordList" parameterType="com.kerryprops.kip.service.integral.entity.TbPointsDetail" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from `tb_points_detail`
    <where>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
    </where>
    ORDER BY create_date DESC LIMIT #{offset}, #{size}
  </select>

  <select id="queryAdjustList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from `tb_points_detail`
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="shopNo != null and shopNo != ''">
        and shop_no = #{shopNo}
      </if>
      <if test="orderNo != null and orderNo != ''">
        and order_no = #{orderNo}
      </if>
      <if test="remark != null and remark != ''">
        and reason_type = #{remark}
      </if>
    </where>
    limit 1
  </select>

  <select id="queryKerryPayPointsRedemptionAmountRecord" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from `tb_points_detail`
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
      <if test="orderNo != null and orderNo != ''">
        and order_no = #{orderNo}
      </if>
      <if test="shopNo != null and shopNo != ''">
        and shop_no = #{shopNo}
      </if>
      <if test="reasonType != null and reasonType != ''">
        and reason_type = #{reasonType}
      </if>
    </where>
    limit 1
  </select>

  <select id="queryPointsRecordByConditions" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from `tb_points_detail`
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
      <if test="orderNo != null and orderNo != ''">
        and order_no = #{orderNo}
      </if>
      <if test="shopNo != null and shopNo != ''">
        and shop_no = #{shopNo}
      </if>
      <if test="reasonTypes != null and reasonTypes.size > 0">
        and reason_type in
        <foreach collection="reasonTypes" item="reasonType" open="(" close=")" separator=",">
          #{reasonType}
        </foreach>
      </if>
    </where>
  </select>

  <select id="checkPointsAdjustExistRecord" resultMap="BaseResultMap">
    select id, crm_id from `tb_points_detail`
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
      <if test="orderNo != null and orderNo != ''">
        and order_no = #{orderNo}
      </if>
      <if test="changePointsNum != null">
        and amount = #{changePointsNum}
      </if>
      <if test="reasonType != null and reasonType != ''">
        and reason_type = #{reasonType}
      </if>
      <if test="startTime != null and startTime != ''">
        AND create_date &gt;= #{startTime}
      </if>
      <if test="endTime != null and endTime != ''">
        AND create_date &lt;= #{endTime}
      </if>
    </where>
    order by create_date desc limit 1;
  </select>

</mapper>