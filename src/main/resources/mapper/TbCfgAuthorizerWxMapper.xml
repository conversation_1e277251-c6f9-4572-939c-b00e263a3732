<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbCfgAuthorizerWxMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbCfgAuthorizerWx">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mallid" jdbcType="VARCHAR" property="mallId" />
    <result column="nickName" jdbcType="VARCHAR" property="nickName" />
    <result column="headImg" jdbcType="VARCHAR" property="headImg" />
    <result column="serviceTypeInfo" jdbcType="VARCHAR" property="serviceTypeInfo" />
    <result column="verifyTypeInfo" jdbcType="VARCHAR" property="verifyTypeInfo" />
    <result column="userName" jdbcType="VARCHAR" property="userName" />
    <result column="principalName" jdbcType="VARCHAR" property="principalName" />
    <result column="businessInfo" jdbcType="VARCHAR" property="businessInfo" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="qrcodeUrl" jdbcType="VARCHAR" property="qrcodeUrl" />
    <result column="authorizerAppid" jdbcType="VARCHAR" property="authorizerAppId" />
    <result column="funcInfo" jdbcType="VARCHAR" property="funcinfo" />
    <result column="signature" jdbcType="VARCHAR" property="signature" />
    <result column="miniProgramInfo" jdbcType="VARCHAR" property="miniProgramInfo" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
    <result column="create_Date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_Date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
  </resultMap>

  <sql id="Base_Column_List">
    id, mallid, nickName, headImg, serviceTypeInfo, verifyTypeInfo, userName, principalName, 
    businessInfo, `alias`, qrcodeUrl, authorizerAppid, funcInfo, signature, miniProgramInfo, 
    `type`, creator, updater, create_Date, update_Date, group_id
  </sql>

  <select id="findByEntity" parameterType="com.kerryprops.kip.service.integral.entity.TbCfgAuthorizerWx" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_cfg_authorizer_wx
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mallid = #{mallId}
      </if>
      <if test="type != null and type != ''">
        and `type` = #{type}
      </if>
    </where>
  </select>
</mapper>