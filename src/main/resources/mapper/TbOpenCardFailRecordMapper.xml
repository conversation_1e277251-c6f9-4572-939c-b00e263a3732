<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbOpenCardFailRecordMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbOpenCardFailRecord">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="open_id" jdbcType="VARCHAR" property="openId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    </resultMap>

    <sql id="BaseColumns">
        id, open_id, mall_id, content, create_date
    </sql>

    <insert id="saveOrUpdate" parameterType="com.kerryprops.kip.service.integral.entity.TbOpenCardFailRecord">
        INSERT INTO tb_open_card_fail_record (open_id, mall_id, content, create_date)
        VALUES (#{openId}, #{mallId}, #{content}, NOW()) ON DUPLICATE KEY UPDATE content = VALUES(content)
    </insert>

    <select id="queryByOpenIdAndMallId" resultMap="BaseResultMap">
        select <include refid="BaseColumns" /> from tb_open_card_fail_record
        <where>
            <if test="openId != null and openId != ''">
                and open_id = #{openId}
            </if>
            <if test="mallId != null and mallId != ''">
                and mall_id = #{mallId}
            </if>
        </where>
    </select>

    <select id="queryByOpenId" resultMap="BaseResultMap">
        select <include refid="BaseColumns" /> from `tb_open_card_fail_record` where open_id = #{openId}
    </select>

</mapper>