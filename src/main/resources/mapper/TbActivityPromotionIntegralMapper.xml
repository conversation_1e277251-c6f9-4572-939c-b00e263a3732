<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbActivityPromotionIntegralMapper">
  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbActivityPromotionIntegral">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="promotionid" jdbcType="VARCHAR" property="promotionId" />
    <result column="points_id" jdbcType="VARCHAR" property="pointsId" />
    <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
    <result column="integral" jdbcType="INTEGER" property="integral" />
    <result column="enable_integral" jdbcType="INTEGER" property="enableIntegral" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="prime_integral" jdbcType="DOUBLE" property="primeIntegral" />
    <result column="promotion_name" jdbcType="VARCHAR" property="promotionName" />
    <result column="sale_money" jdbcType="DECIMAL" property="saleMoney" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="sell_no" jdbcType="VARCHAR" property="sellNo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>

  <sql id="Base_Column_List">
    id, promotion_id, points_id, vipcode, integral, enable_integral, expire_time, create_date, update_date,
    mobile, prime_integral, promotion_name, sale_money, mall_id, sell_no, remark
  </sql>

  <insert id="saveBatch" parameterType="com.kerryprops.kip.service.integral.entity.TbActivityPromotionIntegral" useGeneratedKeys="true" keyProperty="id">
    insert into tb_activity_promotion_points(promotion_id, points_id, vipcode, integral, enable_integral, expire_time, create_date, update_date,
                                              mobile, prime_integral, promotion_name, sale_money, mall_id, sell_no, remark) values
    <foreach collection="list" separator="," item="it">
      (#{it.promotionId}, #{it.pointsId}, #{it.vipcode}, #{it.integral}, #{it.enableIntegral}, #{it.expireTime}, NOW(), NOW(),
       #{it.mobile}, #{it.primeIntegral}, #{it.promotionName}, #{it.saleMoney}, #{it.mallId}, #{it.sellNo}, #{it.remark})
    </foreach>
  </insert>

  <select id="getMatchedPromotions" resultMap="BaseResultMap">
    select points_id, promotion_name from tb_activity_promotion_points
    where points_id in
    <foreach collection="adjustIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="getListByVipCode" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_activity_promotion_points
    where vipcode = #{vipCode}
  </select>

  <select id="getSaleMatchedActivityPromotions" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" /> from tb_activity_promotion_points
    <where>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
      <if test="sellNo != null and sellNo != ''">
        and sell_no = #{sellNo}
      </if>
    </where>
  </select>

</mapper>