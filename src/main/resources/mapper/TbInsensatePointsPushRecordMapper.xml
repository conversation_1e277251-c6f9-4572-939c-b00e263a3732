<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbInsensatePointsPushRecordMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbInsensatePointsPushRecord" >
        <result column="id" property="id" />
        <result column="open_id" property="openId" />
        <result column="transaction_id" property="transactionId" />
        <result column="event_type" property="eventType" />
        <result column="transaction_info" property="transactionInfo" />
        <result column="origin" property="origin" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        open_id,
        transaction_id,
        event_type,
        transaction_info,
        origin,
        create_date,
        update_date
    </sql>

    <insert id="insert" parameterType="com.kerryprops.kip.service.integral.entity.TbInsensatePointsPushRecord" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_insensate_points_push_record (open_id, transaction_id, event_type, transaction_info, origin, create_date, update_date)
        values (#{openId}, #{transactionId}, #{eventType}, #{transactionInfo}, #{origin}, now(), now())
    </insert>

    <select id="findByTransactionIdAndEventTypeAndOrigin" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_insensate_points_push_record
        where transaction_id = #{transactionId}
        <if test="eventType != null">
            and event_type = #{eventType}
        </if>
        and origin = #{origin} order by id desc limit 1;
    </select>

    <select id="findByOpenIdAndTransactionIdAndEventType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_insensate_points_push_record
        <where>
            <if test="openId != null and openId != ''">
                and open_id = #{openId}
            </if>
            <if test="transactionId != null and transactionId != ''">
                and transaction_id = #{transactionId}
            </if>
            <if test="eventType != null">
                and event_type = #{eventType}
            </if>
        </where>
        LIMIT 1;
    </select>

    <select id="selectByOpenId"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_insensate_points_push_record
        <where>
            <if test="openId != null and openId != ''">
                and open_id = #{openId}
            </if>
        </where>
    </select>

    <select id="findByTransactionId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_insensate_points_push_record where transaction_id = #{transactionId} order by create_date desc limit 1;
    </select>

</mapper>