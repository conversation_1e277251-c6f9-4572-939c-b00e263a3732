<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbBrandGuideCollectionMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbBrandGuideCollection">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
    <result column="brand_guide_id" jdbcType="VARCHAR" property="brandGuideId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>

  <sql id="Base_Column_List">
    id, group_id, mall_id, vipcode, brand_guide_id, `status`, create_date
  </sql>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.kerryprops.kip.service.integral.entity.TbBrandGuideCollection" useGeneratedKeys="true">
    insert into tb_brand_guide_collection (group_id, mall_id, vipcode, brand_guide_id, `status`, create_date)
    values (#{groupId}, #{mallId}, #{vipcode}, #{brandGuideId}, #{status}, NOW())
  </insert>

  <select id="findByGroupIdAndVipcodeAndBrandGuideId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_brand_guide_collection
    <where>
      <if test="groupId != null and groupId != ''">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != ''">
        and mall_id = #{mallId}
      </if>
      <if test="vipcode != null and vipcode != ''">
        and vipcode = #{vipcode}
      </if>
      <if test="brandGuideId != null and brandGuideId != ''">
        and brand_guide_id = #{brandGuideId}
      </if>
    </where>
    LIMIT 1;
  </select>

  <select id="findByGroupIdAndMallIdAndVipcode" resultType="string">
    select brand_guide_id from tb_brand_guide_collection where `status` = 1
    <if test="groupId != null and groupId != ''">
      and group_id = #{groupId}
    </if>
    <if test="mallId != null and mallId != ''">
      and mall_id = #{mallId}
    </if>
    <if test="vipcode != null and vipcode != ''">
      and vipcode = #{vipcode}
    </if>
    order by create_date desc;
  </select>

</mapper>