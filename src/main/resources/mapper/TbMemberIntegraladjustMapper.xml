<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberIntegraladjustMapper">

<!--  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberIntegraladjust">-->
<!--    <id column="id" jdbcType="VARCHAR" property="id" />-->
<!--    <result column="group_id" jdbcType="VARCHAR" property="groupId" />-->
<!--    <result column="type" jdbcType="VARCHAR" property="type" />-->
<!--    <result column="mallid" jdbcType="VARCHAR" property="mallId" />-->
<!--    <result column="mobile" jdbcType="VARCHAR" property="mobile" />-->
<!--    <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />-->
<!--    <result column="currentIntegral" jdbcType="DOUBLE" property="currentIntegral" />-->
<!--    <result column="number" jdbcType="DOUBLE" property="number" />-->
<!--    <result column="left_points" jdbcType="INTEGER" property="leftPoints" />-->
<!--    <result column="hxintegral" jdbcType="DOUBLE" property="hxIntegral" />-->
<!--    <result column="remark" jdbcType="VARCHAR" property="remark" />-->
<!--    <result column="remarkName" jdbcType="VARCHAR" property="remarkName" />-->
<!--    <result column="integralDead" jdbcType="VARCHAR" property="integralDead" />-->
<!--    <result column="sellNo" jdbcType="VARCHAR" property="sellNo" />-->
<!--    <result column="cashNo" jdbcType="VARCHAR" property="cashNo" />-->
<!--    <result column="shopId" jdbcType="VARCHAR" property="shopId" />-->
<!--    <result column="sellDate" jdbcType="TIMESTAMP" property="sellDate" />-->
<!--    <result column="extraIntegral" jdbcType="VARCHAR" property="extraIntegral" />-->
<!--    <result column="integralRule" jdbcType="VARCHAR" property="integralRule" />-->
<!--    <result column="discounts" jdbcType="DOUBLE" property="discounts" />-->
<!--    <result column="numberToIntegra" jdbcType="DOUBLE" property="numberToIntegral" />-->
<!--    <result column="content" jdbcType="VARCHAR" property="content" />-->
<!--    <result column="expdate" jdbcType="VARCHAR" property="expdate" />-->
<!--    <result column="extend1" jdbcType="VARCHAR" property="extend1" />-->
<!--    <result column="extend2" jdbcType="VARCHAR" property="extend2" />-->
<!--    <result column="extend3" jdbcType="VARCHAR" property="extend3" />-->
<!--    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />-->
<!--    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />-->
<!--    <result column="createUser" jdbcType="VARCHAR" property="createUser" />-->
<!--    <result column="centerDisposeStatus" jdbcType="INTEGER" property="centerdisposestatus" />-->
<!--    <result column="updateUser" jdbcType="VARCHAR" property="updateUser" />-->
<!--    <result column="imageUrl" jdbcType="VARCHAR" property="imageUrl" />-->
<!--    <result column="channerl_source" jdbcType="VARCHAR" property="channerlSource" />-->
<!--    <result column="xf_amount" jdbcType="DOUBLE" property="xfAmount" />-->
<!--    <result column="beforeIntegral" jdbcType="INTEGER" property="beforeIntegral" />-->
<!--    <result column="beforeGrade" jdbcType="VARCHAR" property="beforeGrade" />-->
<!--    <result column="operationsId" jdbcType="VARCHAR" property="operationId" />-->
<!--  </resultMap>-->

<!--  <sql id="Base_Column_List">-->
<!--    id, group_id, `type`, mallid, mobile, vipcode, currentIntegral, `number`, left_points, hxintegral,-->
<!--    remark, remarkName, integralDead, sellNo, cashNo, shopId, sellDate, extraIntegral,-->
<!--    integralRule, discounts, numberToIntegra, content, expdate, extend1, extend2, extend3,-->
<!--    create_date, update_date, `createUser`, centerDisposeStatus,-->
<!--    updateUser, imageUrl, channerl_source, xf_amount, beforeIntegral, beforeGrade, operationsId-->
<!--  </sql>-->

<!--  <select id="getTotalPointsOfDay" resultType="double">-->
<!--    select IFNULL(SUM(`number`), 0) from tb_member_integraladjust-->
<!--    where group_id = #{groupId} and vipcode = #{vipcode}-->
<!--    <if test="remarks != null and remarks.size > 0">-->
<!--     and remark in-->
<!--     <foreach collection="remarks" item="item" open="(" close=")" separator=",">-->
<!--       #{item}-->
<!--     </foreach>-->
<!--    </if>-->
<!--    AND DATE_FORMAT(create_date, '%Y-%m-%d') = #{date};-->
<!--  </select>-->

<!--  <select id="getCount" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberIntegralAdjustRecordPageQueryDto" resultType="int">-->
<!--    select count(*) from tb_member_integraladjust-->
<!--    <where>-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="vipcode != null and vipcode != ''">-->
<!--        and vipcode = #{vipcode}-->
<!--      </if>-->
<!--      <if test="type != null">-->
<!--        <if test="type == 0">-->
<!--          and `number` &lt;&gt; 0-->
<!--        </if>-->
<!--        <if test="type == 1">-->
<!--          and `number` &gt; 0-->
<!--        </if>-->
<!--        <if test="type == 2">-->
<!--          and `number` &lt; 0-->
<!--        </if>-->
<!--      </if>-->
<!--      <if test="type == null">-->
<!--        and `number` &lt;&gt; 0-->
<!--      </if>-->
<!--    </where>-->
<!--  </select>-->

<!--  <select id="getList" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberIntegralAdjustRecordPageQueryDto" resultMap="BaseResultMap">-->
<!--    select a.id, a.group_id, a.`type`, a.mallid, a.mobile, a.vipcode, a.currentIntegral, a.`number`,-->
<!--    a.remark, a.remarkName, a.integralDead, a.sellNo, a.shopId, a.sellDate, a.extraIntegral, a.discounts, a.numberToIntegra, a.content, a.expdate,-->
<!--    a.create_date, a.imageUrl, DATE_FORMAT(a.create_date,'%Y-%m') as moon from (-->
<!--      select id from tb_member_integraladjust-->
<!--      <where>-->
<!--        <if test="groupId != null and groupId != ''">-->
<!--          and group_id = #{groupId}-->
<!--        </if>-->
<!--        <if test="mallId != null and mallId != ''">-->
<!--          and mallid = #{mallId}-->
<!--        </if>-->
<!--        <if test="vipcode != null and vipcode != ''">-->
<!--          and vipcode = #{vipcode}-->
<!--        </if>-->
<!--        <if test="type != null">-->
<!--          <if test="type == 0">-->
<!--            and `number` &lt;&gt; 0-->
<!--          </if>-->
<!--          <if test="type == 1">-->
<!--            and `number` &gt; 0-->
<!--          </if>-->
<!--          <if test="type == 2">-->
<!--            and `number` &lt; 0-->
<!--          </if>-->
<!--        </if>-->
<!--        <if test="type == null">-->
<!--          and `number` &lt;&gt; 0-->
<!--        </if>-->
<!--      </where>-->
<!--      ORDER BY create_date DESC LIMIT #{offset}, #{size}-->
<!--    ) as t join tb_member_integraladjust a on t.id = a.id-->
<!--    ORDER BY a.create_date DESC;-->
<!--  </select>-->

<!--  <select id="getMonthlyIntegral" resultMap="BaseResultMap">-->
<!--    select DATE_FORMAT(create_date,'%Y-%m') as moon, `number` from tb_member_integraladjust-->
<!--    <where>-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="vipcode != null and vipcode != ''">-->
<!--        and vipcode = #{vipcode}-->
<!--      </if>-->
<!--      <if test="list != null and list.size > 0">-->
<!--        and DATE_FORMAT(create_date,'%Y-%m') in-->
<!--        <foreach collection="list" item="it" open="(" close=")" separator=",">-->
<!--          #{it}-->
<!--        </foreach>-->
<!--      </if>-->
<!--    </where>-->
<!--  </select>-->

<!--  <select id="getMemberConsumePointsBetweenDate" resultType="double">-->
<!--    select IFNULL(SUM(`number`), 0) from tb_member_integraladjust-->
<!--    <where>-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="vipcode != null and vipcode != ''">-->
<!--        and vipcode = #{vipcode}-->
<!--      </if>-->
<!--      <if test="startDate != null and startDate != ''">-->
<!--        AND create_date &gt;= #{startDate}-->
<!--      </if>-->
<!--      <if test="endDate != null and endDate != ''">-->
<!--        AND create_date &lt;= #{endDate}-->
<!--      </if>-->
<!--      <if test="type == 1">-->
<!--        AND `number` &gt;= 0-->
<!--      </if>-->
<!--      <if test="type == 2">-->
<!--        AND `number` &lt; 0-->
<!--      </if>-->
<!--    </where>-->
<!--  </select>-->

<!--  <select id="getDailyIntegralInfo" resultType="com.kerryprops.kip.service.integral.model.dto.DailyMemberStatisticsInfo">-->
<!--    SELECT t.* FROM (-->
<!--      SELECT ifnull(SUM(`number`), 0) as integralNum, count(distinct vipcode) as memNum, 1 as `type`-->
<!--        FROM tb_member_integraladjust where `number` &gt;= 0-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="startTime != null and startTime != ''">-->
<!--        AND create_date &gt;= #{startTime}-->
<!--      </if>-->
<!--      <if test="endTime != null and endTime != ''">-->
<!--        AND create_date &lt;= #{endTime}-->
<!--      </if>-->
<!--      union all-->
<!--      SELECT ifnull(SUM(`number`), 0) as integralNum, count(distinct vipcode) as memNum, 0 as `type`-->
<!--        FROM tb_member_integraladjust where `number` &lt; 0-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="startTime != null and startTime != ''">-->
<!--        AND create_date &gt;= #{startTime}-->
<!--      </if>-->
<!--      <if test="endTime != null and endTime != ''">-->
<!--        AND create_date &lt;= #{endTime}-->
<!--      </if>-->
<!--      union all-->
<!--      SELECT ifnull(SUM(`number`), 0) as integralNum, count(distinct vipcode) as memNum, 2 as `type`-->
<!--      FROM tb_member_integraladjust where `type` = 'S'-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="startTime != null and startTime != ''">-->
<!--        AND create_date &gt;= #{startTime}-->
<!--      </if>-->
<!--      <if test="endTime != null and endTime != ''">-->
<!--        AND create_date &lt;= #{endTime}-->
<!--      </if>-->
<!--    ) as t;-->
<!--  </select>-->

<!--  <select id="findByIds" resultMap="BaseResultMap">-->
<!--    SELECT <include refid="Base_Column_List" /> FROM tb_member_integraladjust-->
<!--    WHERE id IN-->
<!--    <foreach collection="list" item="id" separator="," open="(" close=")">-->
<!--      #{id}-->
<!--    </foreach>-->
<!--  </select>-->

<!--  <select id="integralRecordTotal" resultType="int">-->
<!--    select count(*) from tb_member_integraladjust-->
<!--    <where>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mall_id = #{mallId}-->
<!--      </if>-->
<!--      <if test="vipCode != null and vipCode != ''">-->
<!--        and vip_code = #{vipCode}-->
<!--      </if>-->
<!--    </where>-->
<!--  </select>-->

<!--  <select id="integralRecordList" parameterType="com.kerryprops.kip.service.integral.entity.IntegralAdjustEntity" resultMap="BaseResultMap">-->
<!--    select <include refid="Base_Column_List" /> from tb_member_integraladjust-->
<!--    <where>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mall_id = #{mallId}-->
<!--      </if>-->
<!--      <if test="vipCode != null and vipCode != ''">-->
<!--        and vip_code = #{vipCode}-->
<!--      </if>-->
<!--    </where>-->
<!--    ORDER BY create_date DESC LIMIT #{offset}, #{size}-->
<!--  </select>-->

<!--  <select id="queryAdjustList" resultMap="BaseResultMap">-->
<!--    select <include refid="Base_Column_List" /> from tb_member_integraladjust-->
<!--    <where>-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="shopId != null and shopId != ''">-->
<!--        and shopId = #{shopId}-->
<!--      </if>-->
<!--      <if test="saleNo != null and saleNo != ''">-->
<!--        and sellNo = #{saleNo}-->
<!--      </if>-->
<!--      <if test="remark != null and remark != ''">-->
<!--        and remark = #{remark}-->
<!--      </if>-->
<!--    </where>-->
<!--    limit 1-->
<!--  </select>-->

</mapper>