<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbPaymentPushRecordMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbPaymentPushRecord" >
        <result column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="trade_type" property="tradeType" />
        <result column="pay_method" property="payMethod" />
        <result column="refund_no" property="refundNo"/>
        <result column="content" property="content" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
    </resultMap>

    <sql id="BaseColumns">
        id, order_no, trade_type, pay_method, refund_no, content, create_date, update_date
    </sql>

    <insert id="insert" parameterType="com.kerryprops.kip.service.integral.entity.TbPaymentPushRecord" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_payment_push_record(order_no, trade_type, pay_method, refund_no, content, create_date, update_date)
        values (#{orderNo}, #{tradeType}, #{payMethod}, #{refundNo}, #{content}, now(), now())
    </insert>

</mapper>