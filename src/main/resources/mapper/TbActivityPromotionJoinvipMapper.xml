<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbActivityPromotionJoinvipMapper">
  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbActivityPromotionJoinvip">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="promotion_id" jdbcType="VARCHAR" property="promotionId" />
    <result column="promotion_condition_group_id" jdbcType="VARCHAR" property="promotionConditionGroupId" />
    <result column="promotion_condition_id" jdbcType="VARCHAR" property="promotionConditionId" />
    <result column="member_grade" jdbcType="VARCHAR" property="memberGrade" />
    <result column="join_time_type" jdbcType="INTEGER" property="joinTimeType" />
    <result column="join_time" jdbcType="INTEGER" property="joinTime" />
    <result column="sale_rule_type" jdbcType="INTEGER" property="saleRuleType" />
    <result column="sale_num" jdbcType="INTEGER" property="saleNum" />
    <result column="former_num" jdbcType="INTEGER" property="formerNum" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>

  <sql id="Base_columns">
    id, promotion_id, promotion_condition_group_id, promotion_condition_id, member_grade, join_time_type, join_time, sale_rule_type,
    sale_num, former_num, creator, create_date, updater, update_date
  </sql>

  <select id="findByPromotionId" resultMap="BaseResultMap">
    SELECT * FROM tb_activity_promotion_join_vip WHERE promotion_id = #{promotionId}
  </select>

  <select id="findByPromotionIds" resultMap="BaseResultMap">
    SELECT * FROM tb_activity_promotion_join_vip
    <where>
      <if test="promotionIds != null and promotionIds.size > 0">
        AND promotion_id IN
        <foreach collection="promotionIds" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

</mapper>