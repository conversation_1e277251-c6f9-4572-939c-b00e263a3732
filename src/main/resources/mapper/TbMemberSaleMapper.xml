<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberSaleMapper">

<!--  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberSale">-->
<!--    <id column="id" jdbcType="VARCHAR" property="id" />-->
<!--    <result column="sellNo" jdbcType="VARCHAR" property="sellno" />-->
<!--    <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />-->
<!--    <result column="integraladjustId" jdbcType="VARCHAR" property="integraladjustid" />-->
<!--    <result column="backIntegraladjustid" jdbcType="VARCHAR" property="backIntegraladjustid" />-->
<!--    <result column="saleMoney" jdbcType="DOUBLE" property="salemoney" />-->
<!--    <result column="preferentialMoney" jdbcType="DOUBLE" property="preferentialmoney" />-->
<!--    <result column="useMoney" jdbcType="DOUBLE" property="usemoney" />-->
<!--    <result column="shopName" jdbcType="VARCHAR" property="shopName" />-->
<!--    <result column="integral" jdbcType="DOUBLE" property="integral" />-->
<!--    <result column="shopId" jdbcType="VARCHAR" property="shopid" />-->
<!--    <result column="mallid" jdbcType="VARCHAR" property="mallid" />-->
<!--    <result column="status" jdbcType="VARCHAR" property="status" />-->
<!--    <result column="saleType" jdbcType="VARCHAR" property="saleType" />-->
<!--    <result column="backMoney" jdbcType="DOUBLE" property="backmoney" />-->
<!--    <result column="backIntegral" jdbcType="DOUBLE" property="backintegral" />-->
<!--    <result column="parentOrderNo" jdbcType="VARCHAR" property="parentOrderNo" />-->
<!--    <result column="extend1" jdbcType="VARCHAR" property="extend1" />-->
<!--    <result column="extend2" jdbcType="VARCHAR" property="extend2" />-->
<!--    <result column="extend3" jdbcType="VARCHAR" property="extend3" />-->
<!--    <result column="remark" jdbcType="VARCHAR" property="remark" />-->
<!--    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />-->
<!--    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />-->
<!--    <result column="updateUser" jdbcType="VARCHAR" property="updateUser" />-->
<!--    <result column="createUser" jdbcType="VARCHAR" property="createUser" />-->
<!--    <result column="group_id" jdbcType="VARCHAR" property="groupId" />-->
<!--    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />-->
<!--    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />-->
<!--    <result column="beforeIntegral" jdbcType="INTEGER" property="beforeIntegral" />-->
<!--    <result column="beforeGrade" jdbcType="VARCHAR" property="beforeGrade" />-->
<!--  </resultMap>-->

<!--  <select id="checkMemberSaleExists" resultType="integer">-->
<!--    select 1 from tb_member_sale-->
<!--    <where>-->
<!--      <if test="saleNo !=null and saleNo !=''">-->
<!--        and sellNo = #{saleNo}-->
<!--      </if>-->
<!--      <if test="mallId !=null and mallId !=''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="shopId != null and shopId != ''">-->
<!--        and shopid = #{shopId}-->
<!--      </if>-->
<!--      <if test="saleDate !=null and saleDate !=''">-->
<!--        and DATE_FORMAT(create_date,'%Y-%m-%d') = #{saleDate}-->
<!--      </if>-->
<!--    </where>-->
<!--    LIMIT 1;-->
<!--  </select>-->

<!--  <select id="querySaleBySellNo" parameterType="string" resultMap="BaseResultMap">-->
<!--    select * from tb_member_sale where sellNo = #{sellNo} LIMIT 1;-->
<!--  </select>-->

<!--  <select id="queryBySellNoAndSaleType" resultMap="BaseResultMap">-->
<!--    select * from tb_member_sale where sellNo = #{sellNo} and saleType = #{saleType} LIMIT 1;-->
<!--  </select>-->


<!--  <select id="checkSaleRecordRepeatedOrNot" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberSaleRepeatQueryDto" resultMap="BaseResultMap">-->
<!--    select * from tb_member_sale-->
<!--    <where>-->
<!--      <if test="vipcode != null and vipcode != ''">-->
<!--        and vipcode = #{vipcode}-->
<!--      </if>-->
<!--      <if test="mallId !=null and mallId !=''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="saleNo !=null and saleNo !=''">-->
<!--        and sellNo = #{saleNo}-->
<!--      </if>-->
<!--      <if test="shopId != null and shopId != ''">-->
<!--        and shopid = #{shopId}-->
<!--      </if>-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="saleDateBefore != null and saleDateAfter != ''">-->
<!--        and create_date between #{saleDateBefore} and #{saleDateAfter}-->
<!--      </if>-->
<!--      <if test="useMoney !=null and useMoney != ''">-->
<!--        and useMoney = #{useMoney}-->
<!--      </if>-->
<!--      <if test="notTypes != null and notTypes.size > 0">-->
<!--        and saleType not in-->
<!--        <foreach collection="notTypes" item="item" open="(" close=")" separator=",">-->
<!--          #{item}-->
<!--        </foreach>-->
<!--      </if>-->
<!--    </where>-->
<!--    limit 1-->
<!--  </select>-->

<!--  <select id="queryMemberMonthSalesNumber"-->
<!--          parameterType="com.kerryprops.kip.service.integral.model.dto.MemberSaleMonthNumberQueryDto" resultType="int">-->
<!--    select count(*) from tb_member_sale where `status` = '0'-->
<!--    <if test="groupId != null and groupId != ''">-->
<!--      and group_id = #{groupId}-->
<!--    </if>-->
<!--    <if test="vipcode != null and vipcode != ''">-->
<!--      and vipcode = #{vipcode}-->
<!--    </if>-->
<!--    <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">-->
<!--      and create_date between #{startDate} and #{endDate}-->
<!--    </if>-->
<!--    <if test="shopNos != null and shopNos.size > 0">-->
<!--      AND shopId IN-->
<!--      <foreach collection="shopNos" open="(" close=")" separator="," item="shopNo">-->
<!--        #{shopNo}-->
<!--      </foreach>-->
<!--    </if>-->
<!--  </select>-->

<!--  <select id="selectBySellNoAndMallId" resultType="com.kerryprops.kip.service.integral.entity.TbMemberSale">-->
<!--    select *-->
<!--    from tb_member_sale-->
<!--    where sellNo = #{sellNo}-->
<!--    <if test="mallId != null and mallId != ''">-->
<!--      and mallid = #{mallId}-->
<!--    </if>-->
<!--    limit 1;-->
<!--  </select>-->

<!--  <select id="getSaleListCreateAsc" resultType="com.kerryprops.kip.service.integral.entity.TbMemberSale">-->
<!--    SELECT * FROM tb_member_sale-->
<!--    WHERE group_id = #{groupId} AND vipcode = #{vipcode} AND create_date between #{beginDate} and #{endDate}-->
<!--    <if test="shopIds != null and shopIds.size > 0">-->
<!--      AND shopId IN-->
<!--      <foreach collection="shopIds" open="(" close=")" separator="," item="shopId">-->
<!--        #{shopId}-->
<!--      </foreach>-->
<!--    </if>-->
<!--    ORDER BY create_date ASC-->
<!--  </select>-->

<!--  <select id="getMemberSaleAmountBetweenTime" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberSaleAmountQueryDto" resultMap="BaseResultMap">-->
<!--    SELECT SUM(IFNULL(useMoney, 0)) as useMoney, SUM(IFNULL(backMoney, 0)) as backMoney-->
<!--    FROM tb_member_sale-->
<!--    WHERE group_id = #{groupId} AND vipcode = #{vipcode}-->
<!--    <if test="startDate != null and startDate != ''">-->
<!--      AND create_date &gt; #{startDate}-->
<!--    </if>-->
<!--    <if test="endDate != null and endDate != ''">-->
<!--      AND create_date &lt;= #{endDate}-->
<!--    </if>-->
<!--    <if test="shopIds != null and shopIds.size > 0">-->
<!--      AND shopId IN-->
<!--      <foreach collection="shopIds" open="(" close=")" separator="," item="shopId">-->
<!--        #{shopId}-->
<!--      </foreach>-->
<!--    </if>-->
<!--  </select>-->

<!--  <select id="checkMemberSaleAmountWhetherRelegationSucceeded" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberGradeWhetherRelegationQueryDto" resultType="int">-->
<!--    SELECT COUNT(*) FROM (-->
<!--      SELECT SUM(t.useMoney - t.backMoney) as amount FROM (-->
<!--        SELECT IFNULL(useMoney, 0) as useMoney, IFNULL(backMoney, 0) as backMoney, DATE_FORMAT(create_date, '%Y-%m-%d') as each_day, DATE_FORMAT(create_date, '%Y-%m') as each_month FROM tb_member_sale-->
<!--        WHERE vipcode = #{vipcode} AND group_id = #{groupId}-->
<!--        <if test="shopIds != null and shopIds.size > 0">-->
<!--          AND shopId IN-->
<!--          <foreach collection="shopIds" open="(" close=")" separator="," item="shopId">-->
<!--            #{shopId}-->
<!--          </foreach>-->
<!--        </if>-->
<!--        <if test="startDate != null and startDate != ''">-->
<!--          AND create_date &gt; #{startDate}-->
<!--        </if>-->
<!--        <if test="endDate != null and endDate != ''">-->
<!--          AND create_date &lt;= #{endDate}-->
<!--        </if>-->
<!--      ) as t-->
<!--      <if test="groupBy != null">-->
<!--        <if test="groupBy == 1 or groupBy == 5">-->
<!--          GROUP BY t.each_day-->
<!--        </if>-->
<!--        <if test="groupBy == 8 or groupBy == 9">-->
<!--          GROUP BY t.each_month-->
<!--        </if>-->
<!--      </if>-->
<!--    ) AS tmp-->
<!--    <where>-->
<!--      <if test="amount != null">-->
<!--        and tmp.amount &gt;= #{amount}-->
<!--      </if>-->
<!--    </where>-->
<!--  </select>-->

<!--  <select id="initMemberGradeData" resultMap="BaseResultMap">-->
<!--    select * from tb_member_sale where mallid = #{mallId}-->
<!--    <if test="vipcode != null and vipcode != ''">-->
<!--      and vipcode = #{vipcode}-->
<!--    </if>-->
<!--    and create_date between #{beginDate} and #{endDate}-->
<!--    group by group_id, mallid, vipcode;-->
<!--  </select>-->

<!--  <select id="getShopMemberSalesIds" resultType="string">-->
<!--    select id from tb_member_sale-->
<!--    <where>-->
<!--      <if test="mallId !=null and mallId !=''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="shopId != null and shopId != ''">-->
<!--        and shopid = #{shopId}-->
<!--      </if>-->
<!--    </where>-->
<!--  </select>-->

<!--  <update id="updateMemberSalesShopId">-->
<!--    update tb_member_sale set shopid = #{shopId} where id in-->
<!--    <foreach collection="salesIds" item="it" open="(" close=")" separator=",">-->
<!--      #{it}-->
<!--    </foreach>-->
<!--  </update>-->

<!--  <select id="findSalesInfo" resultMap="BaseResultMap">-->
<!--    select id, sellNo, shopId, shopName, useMoney from tb_member_sale-->
<!--    <where>-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="mallId != null and mallId != ''">-->
<!--        and mallid = #{mallId}-->
<!--      </if>-->
<!--      <if test="vipcode != null and vipcode != ''">-->
<!--        and vipcode = #{vipcode}-->
<!--      </if>-->
<!--      <if test="sellNos != null and sellNos.size > 0">-->
<!--        and sellNo in-->
<!--        <foreach collection="sellNos" item="it" open="(" close=")" separator=",">-->
<!--          #{it}-->
<!--        </foreach>-->
<!--      </if>-->
<!--    </where>-->
<!--  </select>-->

<!--  <select id="getDailyMemberStatisticsInfo" resultType="com.kerryprops.kip.service.integral.model.dto.DailyMemberStatisticsInfo">-->
<!--    SELECT (t.useMoney - t.backMoney) as integralNum, t.mem_num as memNum, t.sale_num as `type`, (t.useMoney - t.backMoney)/t.sale_num as eachMoney FROM (-->
<!--     SELECT IFNULL(SUM(useMoney), 0) as useMoney, IFNULL(SUM(backMoney), 0) as backMoney, count(distinct vipcode) as mem_num, count(id) as sale_num FROM tb_member_sale-->
<!--      <where>-->
<!--        <if test="groupId != null and groupId != ''">-->
<!--          and group_id = #{groupId}-->
<!--        </if>-->
<!--        <if test="mallId !=null and mallId !=''">-->
<!--          and mallid = #{mallId}-->
<!--        </if>-->
<!--        <if test="startTime != null and startTime != ''">-->
<!--          and create_date &gt;= #{startTime}-->
<!--        </if>-->
<!--        <if test="endTime != null and endTime != ''">-->
<!--          and create_date &lt;= #{endTime}-->
<!--        </if>-->
<!--      </where>-->
<!--    ) as t;-->
<!--  </select>-->

<!--  <select id="getMemberSaleById" parameterType="string" resultType="com.kerryprops.kip.service.integral.entity.TbMemberSale">-->
<!--    SELECT * FROM tb_member_sale WHERE id = #{id};-->
<!--  </select>-->

<!--  <select id="checkSalesUpgradeGrade" resultType="string">-->
<!--    SELECT vipcode FROM tb_member_sale-->
<!--    <where>-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="startTime != null and startTime != ''">-->
<!--        and create_date &gt;= #{startTime}-->
<!--      </if>-->
<!--      <if test="endTime != null and endTime != ''">-->
<!--        and create_date &lt;= #{endTime}-->
<!--      </if>-->
<!--    </where>-->
<!--    group by vipcode;-->
<!--  </select>-->

<!--  <select id="getOneSalesBetweenTime" resultType="string">-->
<!--    SELECT id FROM tb_member_sale-->
<!--    <where>-->
<!--      <if test="groupId != null and groupId != ''">-->
<!--        and group_id = #{groupId}-->
<!--      </if>-->
<!--      <if test="vipcode != null and vipcode != ''">-->
<!--        and vipcode = #{vipcode}-->
<!--      </if>-->
<!--      <if test="startTime != null and startTime != ''">-->
<!--        and create_date &gt;= #{startTime}-->
<!--      </if>-->
<!--      <if test="endTime != null and endTime != ''">-->
<!--        and create_date &lt;= #{endTime}-->
<!--      </if>-->
<!--    </where>-->
<!--    ORDER BY create_date DESC LIMIT 1-->
<!--  </select>-->

</mapper>