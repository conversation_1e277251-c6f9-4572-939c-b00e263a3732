<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbAuthorizerUserMapper" >

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbAuthorizerUser" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user" property="user" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="ips" property="ips" jdbcType="VARCHAR" />
    <result column="is_use" property="isUse" jdbcType="INTEGER" />
    <result column="is_check" property="isCheck" jdbcType="INTEGER" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="authorizer_wx_type" property="authorizerWxType" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, `user`, `password`, ips, is_use, is_check, create_user, create_time, update_user,
    update_time, authorizer_wx_type
  </sql>

  <select id="getAuthorizerUserByUser" resultMap="BaseResultMap" parameterType="java.lang.String" >
    SELECT <include refid="Base_Column_List" /> FROM tb_authorizer_user
    WHERE `user` = #{user}
  </select>
</mapper>