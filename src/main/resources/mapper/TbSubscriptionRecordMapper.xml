<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbSubscriptionRecordMapper">
  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.SubscriptionRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="subscription_type" jdbcType="TINYINT" property="subscriptionType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>

  <sql id="Base_Column_List">
    id, group_id, mall_id, user_id, subscription_type, status, create_user
  </sql>

  <select id="querySubscriptionDto" parameterType="com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>  from tb_subscription_record
    <where>
      <if test="groupId != null and groupId != '' ">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != '' ">
        and mall_id = #{mallId}
      </if>
      <if test="status != null">
        and status =#{status}
      </if>
      <if test="userId != null and userId != '' ">
        and user_id =#{userId}
      </if>
      <if test="subscriptionType != null ">
        and subscription_type =#{subscriptionType}
      </if>
    </where>
    limit 1
  </select>

  <select id="querySubscriptionList" parameterType="com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>  from tb_subscription_record
    <where>
      <if test="groupId != null and groupId != '' ">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != '' ">
        and mall_id = #{mallId}
      </if>
      <if test="status != null">
        and status =#{status}
      </if>
      <if test="userId != null and userId != '' ">
        and user_id =#{userId}
      </if>
      <if test="subscriptionType != null ">
        and subscription_type =#{subscriptionType}
      </if>
    </where>
  </select>

  <select id="querySubscriptionUserIdList" parameterType="com.kerryprops.kip.service.integral.model.dto.SubscriptionRecordQueryDto" resultType="string">
    SELECT user_id from tb_subscription_record
    <where>
      <if test="groupId != null and groupId != '' ">
        and group_id = #{groupId}
      </if>
      <if test="mallId != null and mallId != '' ">
        and mall_id = #{mallId}
      </if>
      <if test="status != null">
        and status =#{status}
      </if>
      <if test="userId != null and userId != '' ">
        and user_id =#{userId}
      </if>
      <if test="subscriptionType != null ">
        and subscription_type =#{subscriptionType}
      </if>
    </where>
  </select>
</mapper>