<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbCashOutConfigMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbCashOutConfig">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="money" jdbcType="DECIMAL" property="money" />
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
        <result column="is_limit" jdbcType="INTEGER" property="isLimit" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    </resultMap>

    <sql id="BaseColumns">
        id, group_id, mall_id, `name`, business_type, money, shop_no, is_limit, start_time, end_time, create_date, create_user, update_date, update_user
    </sql>

    <select id="getConfig" resultMap="BaseResultMap" parameterType="com.kerryprops.kip.service.integral.webservice.resource.TbCashOutConfigResource">
        select <include refid="BaseColumns" /> from tb_cash_out_config
        <where>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="businessType != null and businessType != '' ">
                and business_type = #{businessType}
            </if>
            <if test="startTime != null and startTime != '' ">
                and start_time &lt;= #{startTime} and end_time &gt;= #{startTime}
            </if>
        </where>
        limit 1
    </select>
</mapper>