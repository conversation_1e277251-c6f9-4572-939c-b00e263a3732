<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbAutoSaleMemberTagMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbAutoSaleMemberTag">
        <result property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="tagName" column="tag_name"/>
        <result property="status" column="status"/>
        <result property="firSortId" column="firsort_id"/>
        <result property="secSortId" column="secsort_id"/>
        <result property="targetNumber" column="target_number"/>
        <result property="conditionParam" column="condition_param"/>
        <result property="queryConditions" column="query_conditions"/>
        <result property="remark" column="remark"/>
        <result property="describes" column="describes"/>
        <result property="type" column="type"/>
        <result property="createUser" column="create_user"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, group_id, tag_name, `status`, firsort_id, secsort_id, target_number, condition_param, query_conditions, remark, describes, `type`, create_user, create_date,
        update_user, update_date
    </sql>

    <select id="getBySecSortIdAndType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_auto_sale_member_tag
        <where>
            <if test="secSortId != null">
                and secsort_id = #{secSortId}
            </if>
            <if test="type != null ">
                and `type` = #{type}
            </if>
        </where>
    </select>

</mapper>