<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberCardOpenRecordMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberCardOpenRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="kip_user_id" jdbcType="VARCHAR" property="kipUserId" />
    <result column="card_id" jdbcType="VARCHAR" property="cardId" />
    <result column="card_no" jdbcType="VARCHAR" property="cardNo" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>

  <sql id="Base_Column_List">
    id, group_id, mall_id, open_id, kip_user_id, card_id, card_no, event_type, create_date
  </sql>

  <select id="selectByOpenIdAndMallId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_member_open_card_record where open_id = #{openId} and mall_id = #{mallId} limit 1;
  </select>

</mapper>