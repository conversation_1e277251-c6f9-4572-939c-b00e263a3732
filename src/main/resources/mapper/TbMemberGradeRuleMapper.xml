<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberGradeRuleMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberGradeRule">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="gradeCode" column="grade_code"/>
        <result property="ruleType" column="rule_type"/>
        <result property="money" column="money"/>
        <result property="formats" column="formats"/>
        <result property="formatsDesc" column="formats_desc"/>
        <result property="sumType" column="sum_type"/>
        <result property="type" column="type"/>
        <result property="isCount" column="is_count"/>
        <result property="groupId" column="group_id"/>
        <result property="mallId" column="mall_id"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="createUser" column="create_User"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUser" column="update_User"/>
        <result property="updateDate" column="update_date"/>
        <result property="certification" column="certification"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, pid, grade_code, rule_type, money, formats, formats_desc, sum_type, `type`, is_count, group_id, mall_id, certification,
        effective_date, create_user, create_date, update_user
    </sql>

    <select id="queryGradeRulesByGroupIdAndGradeAndRuleTypes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_member_grade_rule
        WHERE group_id = #{groupId} AND grade_code = #{grade}
        <if test="ruleTypes != null and ruleTypes.size > 0">
            AND rule_type IN
            <foreach collection="ruleTypes" item="item" index="index" open="(" separator="," close=")">
              #{item}
            </foreach>
        </if>
    </select>

    <select id="isCycleYear" resultType="boolean">
        SELECT EXISTS(
            SELECT id FROM tb_member_grade_rule
            WHERE group_id = #{groupId}
              <if test="type != null">
                  AND `type` = #{type}
              </if>
              AND sum_type = 2 limit 1
        ) AS tmp;
    </select>

    <select id="queryGradeRulesByGroupIdAndGradeAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_member_grade_rule
        WHERE group_id = #{groupId}
        AND grade_code = #{grade}
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>

    <select id="findGradeRulesByGroupIdAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_member_grade_rule
        WHERE group_id = #{groupId} AND type = #{type}
        ORDER BY id ASC
    </select>

    <select id="findGradeRulesByGroupIdAndRuleType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_member_grade_rule
        WHERE group_id = #{groupId} AND rule_type = #{ruleType}
        ORDER BY id ASC
    </select>

</mapper>