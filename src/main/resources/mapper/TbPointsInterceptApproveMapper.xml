<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbPointsInterceptApproveMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbPointsIntercept">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="kip_user_id" jdbcType="VARCHAR" property="kipUserId" />
        <result column="sale_id" jdbcType="VARCHAR" property="saleId" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="sale_type" jdbcType="VARCHAR" property="saleType" />
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
        <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
        <result column="refund_points_num" jdbcType="INTEGER" property="refundPointsNum" />
        <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
        <result column="remark_name" jdbcType="VARCHAR" property="remarkName" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="wx_or_ali_id" jdbcType="VARCHAR" property="wxOrAliId" />
        <result column="sale_date" jdbcType="TIMESTAMP" property="saleDate" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="intercept_reason" jdbcType="VARCHAR" property="interceptReason" />
        <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
        <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="exist_sale_ids" jdbcType="VARCHAR" property="existSaleIds" />
        <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
    </resultMap>

    <sql id="Base_Column_List">
        id,group_id,mall_id,mobile,kip_user_id,sale_id,order_no,sale_type,shop_no,
    pay_amount,discount_amount,total_amount,
    refund_points_num,refund_amount,remark_name,app_id,wx_or_ali_id,sale_date,
    status,intercept_reason,reject_reason,img_url,create_date,update_date,create_user,update_user,exist_sale_ids,vipcode
    </sql>

    <insert id="saveIntercept" useGeneratedKeys="true" keyColumn="id" keyProperty="id"  parameterType="com.kerryprops.kip.service.integral.entity.TbPointsIntercept">
        INSERT INTO tb_points_intercept_approve (group_id,mall_id,mobile,
        kip_user_id,sale_id,order_no,shop_no,total_amount,discount_amount,pay_amount,
        sale_date,status,sale_type,remark_name,refund_amount,refund_points_num,app_id,wx_or_ali_id,intercept_reason,
        reject_reason,img_url,create_date,create_user,update_date,update_user,exist_sale_ids,vipcode) VALUES
        (#{groupId},
        #{mallId},
        #{mobile},
        #{kipUserId},
        #{saleId},
        #{orderNo},
        #{shopNo},
        #{totalAmount},
        #{discountAmount},
        #{payAmount},
        #{saleDate},
        #{status},
        #{saleType},
        #{remarkName},
        #{refundAmount},
        #{refundPointsNum},
        #{appId},
        #{wxOrAliId},
        #{interceptReason},
        #{rejectReason},
        #{imgUrl},
        #{createDate},
        #{createUser},
        #{updateDate},
        #{updateUser},
        #{existSaleIds},
        #{vipcode})

    </insert>

    <select id="getTotal" parameterType="com.kerryprops.kip.service.integral.model.dto.TbPointsInterceptQueryDto" resultType="int">
        SELECT count(*) FROM tb_points_intercept_approve
        <where>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="mallId != null and mallId != ''">
                AND mall_id = #{mallId}
            </if>
            <if test="shopNo != null and shopNo != ''">
                AND shop_no = #{shopNo}
            </if>
            <if test="mobile != null and mobile != ''">
                AND mobile = #{mobile}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="statusList != null and statusList.size > 0">
                AND status in
                <foreach collection="statusList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getPageData" parameterType="com.kerryprops.kip.service.integral.model.dto.TbPointsInterceptQueryDto" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" /> FROM tb_points_intercept_approve
        <where>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="mallId != null and mallId != ''">
                AND mall_id = #{mallId}
            </if>
            <if test="shopNo != null and shopNo != ''">
                AND shop_no = #{shopNo}
            </if>
            <if test="mobile != null and mobile != ''">
                AND mobile = #{mobile}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="statusList != null and statusList.size > 0">
                AND status in
                <foreach collection="statusList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by create_date desc
        <if test="offset != null and size != null">
            limit #{offset}, #{size}
        </if>
    </select>

    <select id="getInterceptDto" parameterType="com.kerryprops.kip.service.integral.model.dto.TbPointsInterceptQueryDto" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" /> FROM tb_points_intercept_approve
        <where>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="mallId != null and mallId != ''">
                AND mall_id = #{mallId}
            </if>
            <if test="shopNo != null and shopNo != ''">
                AND shop_no = #{shopNo}
            </if>
            <if test="mobile != null and mobile != ''">
                AND mobile = #{mobile}
            </if>
            <if test="vipcode != null and vipcode != ''">
                AND vipcode = #{vipcode}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
            <if test="saleDate != null and saleDate != ''">
                AND sale_date = #{saleDate}
            </if>
        </where>
        limit 1
    </select>

</mapper>