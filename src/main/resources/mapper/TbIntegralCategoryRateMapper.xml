<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbIntegralCategoryRateMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate" >
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="category_id" jdbcType="VARCHAR" property="categoryId" />
        <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
        <result column="grade_id" jdbcType="VARCHAR" property="gradeId" />
        <result column="grade_name" jdbcType="VARCHAR" property="gradeName" />
        <result column="money" jdbcType="DECIMAL" property="money" />
        <result column="point_num" jdbcType="DECIMAL" property="pointNum" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="is_consistent" jdbcType="INTEGER" property="isConsistent" />
    </resultMap>

    <sql id="Base_Column_List">
        id, group_id, category_id, category_name, grade_id, grade_name,
               IFNULL(money, 0) AS money, IFNULL(point_num, 0) as point_num, status, create_date, creator, update_date,
          updater, mall_id, is_consistent
    </sql>

    <select id="categoryRatePage" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from tb_integral_category_rate
        <where>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
            <if test="mallIds != null and mallIds.size > 0 ">
                and mall_id in
                <foreach collection="mallIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="categoryId != null and categoryId != '' ">
                and category_id = #{categoryId}
            </if>
        </where>
        ORDER BY create_date DESC LIMIT #{offset}, #{size}
    </select>

    <select id="getTotal" resultType="int">
        SELECT count(*) FROM tb_integral_category_rate
        <where>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
            <if test="mallIds != null and mallIds.size > 0 ">
                and mall_id in
                <foreach collection="mallIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="categoryId != null and categoryId != '' ">
                and category_id = #{categoryId}
            </if>
        </where>
    </select>

    <select id="getCategoryRateList" parameterType="com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from tb_integral_category_rate
        <where>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="gradeId != null and gradeId != '' ">
                and grade_id = #{gradeId}
            </if>
            <if test="categoryId != null and categoryId != '' ">
                and category_id = #{categoryId}
            </if>
            <if test="categoryList != null and categoryList.size > 0 ">
                and category_id in
                <foreach collection="categoryList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="saveCategoryRateList" parameterType="com.kerryprops.kip.service.integral.entity.TbIntegralCategoryRate">
        insert into tb_integral_category_rate(group_id, category_id, category_name, grade_id, grade_name,
        money, point_num, status, create_date, creator, update_date, updater, mall_id, is_consistent) values
        <foreach collection="rateList" separator="," item="it">
            (#{it.groupId}, #{it.categoryId}, #{it.categoryName}, #{it.gradeId}, #{it.gradeName},
             #{it.money}, #{it.pointNum}, #{it.status}, NOW(), #{it.creator}, NOW(), #{it.updater},
              #{it.mallId}, #{it.isConsistent})
        </foreach>
    </insert>

    <delete id="deleteByParams" >
         delete from tb_integral_category_rate
        <where>
            <if test="groupId != null and groupId != '' ">
                and group_id = #{groupId}
            </if>
            <if test="mallId != null and mallId != '' ">
                and mall_id = #{mallId}
            </if>
            <if test="categoryId != null and categoryId != '' ">
                and category_id = #{categoryId}
            </if>
        </where>
    </delete>

</mapper>