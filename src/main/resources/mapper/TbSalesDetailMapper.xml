<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbSalesDetailMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbSalesDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="crm_id" jdbcType="VARCHAR" property="crmId" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
        <result column="kip_user_id" jdbcType="VARCHAR" property="kipUserId" />
        <result column="points_id" jdbcType="VARCHAR" property="pointsId" />
        <result column="points_num" jdbcType="INTEGER" property="pointsNum" />
        <result column="parent_order_no" jdbcType="VARCHAR" property="parentOrderNo" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
        <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
        <result column="points_offset_amount" jdbcType="DECIMAL" property="pointsOffsetAmount" />
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
        <result column="sale_date" jdbcType="TIMESTAMP" property="saleDate" />
        <result column="sale_type" jdbcType="VARCHAR" property="saleType" />
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
        <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="refund_points_id" jdbcType="VARCHAR" property="refundPointsId" />
        <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
        <result column="refund_points_num" jdbcType="INTEGER" property="refundPointsNum" />
        <result column="extend1" jdbcType="VARCHAR" property="extend1" />
        <result column="extend2" jdbcType="VARCHAR" property="extend2" />
        <result column="extend3" jdbcType="VARCHAR" property="extend3" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
        <result column="org_points" jdbcType="INTEGER" property="orgPoints" />
        <result column="org_grade" jdbcType="VARCHAR" property="orgGrade" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    </resultMap>

    <sql id="Base_Columns">
        id, crm_id, group_id, mall_id, vipcode, kip_user_id, points_id, points_num, parent_order_no, order_no, total_amount, discount_amount, points_offset_amount, pay_amount,
        sale_date, sale_type, shop_no, shop_name, `status`, refund_points_id, refund_amount, refund_points_num, extend1, extend2, extend3, remark, image_url, org_points,
        org_grade, create_date, create_user, update_date, update_user
    </sql>

    <select id="checkMemberSaleExists" resultType="integer">
        SELECT 1 FROM `tb_sales_detail`
        <where>
            <if test="orderNo !=null and orderNo !=''">
                and order_no = #{orderNo}
            </if>
            <if test="mallId !=null and mallId !=''">
                and mall_id = #{mallId}
            </if>
            <if test="shopNo != null and shopNo != ''">
                and shop_no = #{shopNo}
            </if>
            <if test="saleDate !=null and saleDate !=''">
                and DATE_FORMAT(create_date,'%Y-%m-%d') = #{saleDate}
            </if>
        </where>
        LIMIT 1;
    </select>

    <select id="queryByOrderNo" parameterType="string" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail` WHERE order_no = #{orderNo} LIMIT 1;
    </select>

    <select id="queryByOrderNoAndSaleType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail` WHERE order_no = #{orderNo}
        <if test="saleType != null and saleType != ''">
            and sale_type = #{saleType}
        </if>
        LIMIT 1;
    </select>

    <select id="checkSaleRecordRepeatedOrNot" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberSaleRepeatQueryDto" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail`
        <where>
            <if test="vipcode != null and vipcode != ''">
                and vipcode = #{vipcode}
            </if>
            <if test="mallId !=null and mallId !=''">
                and mall_id = #{mallId}
            </if>
            <if test="saleNo !=null and saleNo !=''">
                and order_no = #{saleNo}
            </if>
            <if test="shopId != null and shopId != ''">
                and shop_no = #{shopId}
            </if>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="saleDateBefore != null and saleDateBefore != '' and saleDateAfter != null and saleDateAfter != ''">
                and sale_date between #{saleDateBefore} and #{saleDateAfter}
            </if>
            <if test="useMoney !=null and useMoney != ''">
                and pay_amount = #{useMoney}
            </if>
            <if test="notFullRefundFlag != null and notFullRefundFlag == 1">
                and points_num &lt;&gt; refund_points_num
            </if>
            <if test="saleId != null">
                and id = #{saleId}
            </if>
            <if test="saleTypes != null and saleTypes.size > 0">
                and sale_type in
                <foreach collection="saleTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        limit 1
    </select>

    <select id="checkSaleRepeatedByConditions" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberSaleRepeatQueryDto" resultMap="BaseResultMap">
        <foreach collection="list" item="item" separator=" UNION ALL ">
            SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail`
            <where>
                <if test="item.vipcode != null and item.vipcode != ''">
                    and vipcode = #{item.vipcode}
                </if>
                <if test="item.mallId !=null and item.mallId !=''">
                    and mall_id = #{item.mallId}
                </if>
                <if test="item.saleNo !=null and item.saleNo !=''">
                    and order_no = #{item.saleNo}
                </if>
                <if test="item.shopId != null and item.shopId != ''">
                    and shop_no = #{item.shopId}
                </if>
                <if test="item.groupId != null and item.groupId != ''">
                    and group_id = #{item.groupId}
                </if>
                <if test="item.saleDateBefore != null and item.saleDateBefore != '' and item.saleDateAfter != null and item.saleDateAfter != ''">
                    and sale_date between #{item.saleDateBefore} and #{item.saleDateAfter}
                </if>
                <if test="item.useMoney !=null and item.useMoney != ''">
                    and pay_amount = #{item.useMoney}
                </if>
                <if test="item.saleId != null">
                    and id = #{item.saleId}
                </if>
                <if test="item.notFullRefundFlag != null and item.notFullRefundFlag == 1">
                    and points_num &lt;&gt; refund_points_num
                </if>
                <if test="item.saleTypes != null and item.saleTypes.size > 0">
                    and sale_type in
                    <foreach collection="item.saleTypes" item="saleType" open="(" close=")" separator=",">
                        #{saleType}
                    </foreach>
                </if>
            </where>
        </foreach>
    </select>

    <select id="queryMemberMonthSalesNumber"
            parameterType="com.kerryprops.kip.service.integral.model.dto.MemberSaleMonthNumberQueryDto" resultType="int">
        SELECT count(*) FROM `tb_sales_detail` WHERE `status` = 0
        <if test="groupId != null and groupId != ''">
            and group_id = #{groupId}
        </if>
        <if test="vipcode != null and vipcode != ''">
            and vipcode = #{vipcode}
        </if>
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            and sale_date between #{startDate} and #{endDate}
        </if>
        <if test="shopNos != null and shopNos.size > 0">
            AND shop_no IN
            <foreach collection="shopNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryMemberSaleCountBetweenTime"
            parameterType="com.kerryprops.kip.service.integral.model.dto.MemberSaleMonthNumberQueryDto" resultType="int">
        SELECT count(*) FROM `tb_sales_detail`
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="vipcode != null and vipcode != ''">
                and vipcode = #{vipcode}
            </if>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                and sale_date between #{startDate} and #{endDate}
            </if>
            <if test="shopNos != null and shopNos.size > 0">
                AND shop_no IN
                <foreach collection="shopNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryByOrderNoAndMallId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail` WHERE order_no = #{orderNo}
        <if test="mallId != null and mallId != ''">
            and mall_id = #{mallId}
        </if>
        limit 1;
    </select>

    <select id="getSaleListCreateAsc" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail`
        WHERE group_id = #{groupId} AND vipcode = #{vipcode} AND create_date between #{beginDate} and #{endDate}
        <if test="shopNos != null and shopNos.size > 0">
            AND shop_no IN
            <foreach collection="shopNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        ORDER BY create_date ASC
    </select>

    <select id="getSaleAmountBetweenTime" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberSaleAmountQueryDto" resultMap="BaseResultMap">
        SELECT IFNULL(SUM(`pay_amount`), 0) as pay_amount, IFNULL(SUM(`refund_amount`), 0) as refund_amount FROM `tb_sales_detail`
        WHERE group_id = #{groupId} AND vipcode = #{vipcode}
        <if test="startDate != null and startDate != ''">
            AND sale_date &gt; #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND sale_date &lt;= #{endDate}
        </if>
        <if test="shopIds != null and shopIds.size > 0">
            AND shop_no IN
            <foreach collection="shopIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="checkMemberSaleAmountWhetherRelegationSucceeded" parameterType="com.kerryprops.kip.service.integral.model.dto.MemberGradeWhetherRelegationQueryDto" resultType="int">
        SELECT COUNT(*) FROM (
            SELECT SUM(t.pay_amount - t.refund_amount) as amount FROM (
                SELECT IFNULL(`pay_amount`, 0) as pay_amount, IFNULL(`refund_amount`, 0) as refund_amount, DATE_FORMAT(`sale_date`, '%Y-%m-%d') as each_day, DATE_FORMAT(`sale_date`, '%Y-%m') as each_month FROM `tb_sales_detail`
                WHERE vipcode = #{vipcode} AND group_id = #{groupId}
                <if test="shopIds != null and shopIds.size > 0">
                    AND shop_no IN
                    <foreach collection="shopIds" open="(" close=")" separator="," item="shopId">
                        #{shopId}
                    </foreach>
                </if>
                <if test="startDate != null and startDate != ''">
                    AND sale_date &gt; #{startDate}
                </if>
                <if test="endDate != null and endDate != ''">
                    AND sale_date &lt;= #{endDate}
                </if>
            ) as t
            <if test="groupBy != null">
                <if test="groupBy == 1 or groupBy == 5">
                    GROUP BY t.each_day
                </if>
                <if test="groupBy == 8 or groupBy == 9">
                    GROUP BY t.each_month
                </if>
            </if>
        ) AS tmp
        <where>
            <if test="amount != null">
                and tmp.amount &gt;= #{amount}
            </if>
        </where>
    </select>

    <select id="initMemberGradeData" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail` WHERE mall_id = #{mallId}
        <if test="vipcode != null and vipcode != ''">
            and vipcode = #{vipcode}
        </if>
        and create_date between #{beginDate} and #{endDate}
        group by group_id, mall_id, vipcode;
    </select>

    <select id="getShopMemberSalesIds" resultType="long">
        SELECT id FROM `tb_sales_detail`
        <where>
            <if test="mallId !=null and mallId !=''">
                and mall_id = #{mallId}
            </if>
            <if test="shopNo != null and shopNo != ''">
                and shop_no = #{shopNo}
            </if>
        </where>
    </select>

    <update id="updateMemberSalesShopId">
        update `tb_sales_detail` set shop_no = #{shopNo} where id in
        <foreach collection="salesIds" item="it" open="(" close=")" separator=",">
            #{it}
        </foreach>
    </update>

    <select id="findSalesList" resultMap="BaseResultMap">
        SELECT `id`, `crm_id`, `order_no`, `shop_no`, `shop_name`, `pay_amount` FROM `tb_sales_detail`
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="mallId != null and mallId != ''">
                and mall_id = #{mallId}
            </if>
            <if test="vipcode != null and vipcode != ''">
                and vipcode = #{vipcode}
            </if>
            <if test="orderNos != null and orderNos.size > 0">
                and order_no in
                <foreach collection="orderNos" item="it" open="(" close=")" separator=",">
                    #{it}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getMemberSaleById" parameterType="long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail` WHERE id = #{id};
    </select>

    <select id="checkSalesUpgradeGrade" resultType="string">
        SELECT `vipcode` FROM `tb_sales_detail`
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="startTime != null and startTime != ''">
                and sale_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and sale_date &lt;= #{endTime}
            </if>
        </where>
        group by vipcode;
    </select>

    <select id="getOneSalesBetweenTime" resultType="string">
        SELECT `id` FROM `tb_sales_detail`
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="vipcode != null and vipcode != ''">
                and vipcode = #{vipcode}
            </if>
            <if test="startTime != null and startTime != ''">
                and sale_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and sale_date &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_date DESC LIMIT 1
    </select>

    <select id="querySaleIsIntercept"
            parameterType="com.kerryprops.kip.service.integral.model.dto.SaleDetailQueryDto" resultType="string">
        SELECT id FROM `tb_sales_detail` WHERE `status` in (0, 2)
        <if test="groupId != null and groupId != ''">
            and group_id = #{groupId}
        </if>
        <if test="mallId != null and mallId != ''">
            and mall_id = #{mallId}
        </if>
        <if test="vipcode != null and vipcode != ''">
            and vipcode = #{vipcode}
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            and sale_date between #{startTime} and #{endTime}
        </if>
        <if test="shopNo != null and shopNo != '' ">
            AND shop_no = #{shopNo}
        </if>
    </select>

    <select id="getMemberSaleList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail` WHERE group_id = #{groupId} AND vipcode = #{vipcode};
    </select>

    <update id="executeSql">
        ${sql}
    </update>

    <select id="querySqlResult" resultType="java.util.Map">
        ${sql}
    </select>

    <select id="accumulatedSalesAmount" parameterType="com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource" resultType="decimal">
        SELECT IFNULL(SUM(`pay_amount` - `refund_amount`), 0) as pay_amount FROM `tb_sales_detail`
        WHERE group_id = #{groupId} AND vipcode = #{vipcode}
        <if test="startDate != null and startDate != ''">
            AND sale_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND sale_date &lt;= #{endDate}
        </if>
        <if test="shopNos != null and shopNos.size > 0">
            AND shop_no in
            <foreach collection="shopNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="mallIds != null and mallIds.size > 0">
            AND mall_id in
            <foreach collection="mallIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="consumeShopCount" parameterType="com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource" resultType="string">
        SELECT shop_no FROM `tb_sales_detail` WHERE group_id = #{groupId} AND vipcode = #{vipcode} AND `pay_amount` &lt;&gt; `refund_amount`
        <if test="startDate != null and startDate != ''">
            AND sale_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND sale_date &lt;= #{endDate}
        </if>
        <if test="shopNos != null and shopNos.size > 0">
            AND shop_no in
            <foreach collection="shopNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="mallIds != null and mallIds.size > 0">
            AND mall_id in
            <foreach collection="mallIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        group by shop_no;
    </select>

    <select id="consumeFrequency" parameterType="com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource" resultType="int">
        SELECT COUNT(*) FROM `tb_sales_detail`
        WHERE group_id = #{groupId} AND vipcode = #{vipcode} AND `pay_amount` &lt;&gt; `refund_amount`
        <if test="startDate != null and startDate != ''">
            AND sale_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND sale_date &lt;= #{endDate}
        </if>
        <if test="shopNos != null and shopNos.size > 0">
            AND shop_no in
            <foreach collection="shopNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="mallIds != null and mallIds.size > 0">
            AND mall_id in
            <foreach collection="mallIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="consumeDays" parameterType="com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource" resultType="String">
        select tmp.sale_date from (
            SELECT DATE_FORMAT(`sale_date`, '%Y-%m-%d') AS sale_date FROM `tb_sales_detail`
            WHERE group_id = #{groupId} AND vipcode = #{vipcode} AND `pay_amount` &lt;&gt; `refund_amount`
            <if test="startDate != null and startDate != ''">
                AND sale_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND sale_date &lt;= #{endDate}
            </if>
            <if test="shopNos != null and shopNos.size > 0">
                AND shop_no in
                <foreach collection="shopNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="mallIds != null and mallIds.size > 0">
                AND mall_id in
                <foreach collection="mallIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        ) as tmp group by tmp.sale_date;
    </select>

    <select id="queryBadgeShopGroupDetails" parameterType="com.kerryprops.kip.service.integral.webservice.resource.BadgeSalesResource"
            resultType="com.kerryprops.kip.service.integral.webservice.response.BadgeShopGroupResultResponse">
        <foreach collection="list" item="item" separator=" UNION ALL ">
            SELECT IFNULL(SUM(`pay_amount` - `refund_amount`), 0) as amount, COUNT(distinct shop_no) as shopCount,
                   COUNT(id) AS frequency, COUNT(distinct DATE_FORMAT(`sale_date`, '%Y-%m-%d')) AS days,
                   #{item.shopGroupId} as shopGroupId FROM tb_sales_detail
            WHERE group_id = #{item.groupId} AND vipcode = #{item.vipcode}
            AND `pay_amount` &lt;&gt; `refund_amount` AND org_points &gt;= 0
            AND sale_date BETWEEN #{item.startDate} and #{item.endDate}
            <if test="item.shopNos != null and item.shopNos.size > 0">
                AND shop_no in
                <foreach collection="item.shopNos" open="(" close=")" separator="," item="shopNo">
                    #{shopNo}
                </foreach>
            </if>
            <if test="item.activityStartDate != null and item.activityEndDate != null and item.activityStartDate != '' and item.activityEndDate != ''">
                AND create_date BETWEEN #{item.activityStartDate} AND #{item.activityEndDate}
            </if>
            <if test="item.grades != null and item.grades.size > 0">
                AND org_grade in
                <foreach collection="item.grades" open="(" close=")" separator="," item="grade">
                    #{grade}
                </foreach>
            </if>
        </foreach>
    </select>

    <select id="gradeChangeTriggerMemberUpgrade" resultType="string">
        SELECT distinct vipcode FROM tb_sales_detail WHERE group_id = #{groupId} AND create_date &gt;= #{date};
    </select>

    <select id="getLatestSalesRecord" resultMap="BaseResultMap">
        SELECT <include refid="Base_Columns" /> FROM `tb_sales_detail` WHERE group_id = #{groupId} AND vipcode = #{vipcode}
        ORDER BY create_date DESC LIMIT 1;
    </select>

</mapper>