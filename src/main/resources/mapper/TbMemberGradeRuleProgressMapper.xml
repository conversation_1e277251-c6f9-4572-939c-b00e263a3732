<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberGradeRuleProgressMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberGradeRuleProgress">
        <result property="id" column="id"/>
        <result property="grade" column="grade"/>
        <result property="yearGradeRuleId" column="year_grade_rule_id"/>
        <result property="monthGradeRuleId" column="month_grade_rule_id"/>
        <result property="dayGradeRuleId" column="day_grade_rule_id"/>
        <result property="upProgressText" column="up_progress_text"/>
        <result property="upProgressTextNotShow" column="up_progress_text_not_show"/>
        <result property="sustainProgressText" column="sustain_progress_text"/>
        <result property="sustainProgressTextNotShow" column="sustain_progress_text_not_show"/>
        <result property="sustainProgressTextMax" column="sustain_progress_text_max"/>
        <result property="groupId" column="group_id"/>
        <result property="createDate" column="create_date"/>
        <result property="createUser" column="create_user"/>
        <result property="updateDate" column="update_date"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, grade, year_grade_rule_id, month_grade_rule_id, day_grade_rule_id, up_progress_text, up_progress_text_not_show, sustain_progress_text, sustain_progress_text_not_show, sustain_progress_text_max, group_id,
        create_date, create_user, update_date, update_user
    </sql>

    <select id="queryByGroupIdAndGrade" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_member_grade_rule_process
        WHERE group_id = #{groupId} AND grade = #{grade} LIMIT 1;
    </select>

</mapper>