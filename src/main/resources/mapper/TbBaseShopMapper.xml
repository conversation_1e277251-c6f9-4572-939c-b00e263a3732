<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbBaseShopMapper">

    <resultMap type="com.kerryprops.kip.service.integral.entity.TbBaseShop" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="contractNo" column="contract_no"/>
        <result property="groupId" column="group_id"/>
        <result property="mallid" column="mallid"/>
        <result property="marketName" column="market_name"/>
        <result property="mallCode" column="mall_code"/>
        <result property="buildingId" column="building_id"/>
        <result property="floorNo" column="floor_no"/>
        <result property="floorName" column="floor_name"/>
        <result property="firstFormatCode" column="first_format_code"/>
        <result property="firstFormatName" column="first_format_name"/>
        <result property="secondFormatCode" column="second_format_code"/>
        <result property="secondFormatName" column="second_format_name"/>
        <result property="thirdFormatCode" column="third_format_code"/>
        <result property="thirdFormatName" column="third_format_name"/>
        <result property="shopStatus" column="shop_status"/>
        <result property="shopName" column="shop_name"/>
        <result property="brand" column="brand"/>
        <result property="brandName" column="brand_name"/>
        <result property="delFlag" column="del_flag"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
        <result property="isEnable" column="is_enable"/>
        <result property="isEnterShop" column="is_enter_shop"/>
        <result property="isEnterPos" column="is_enter_pos"/>
        <result property="shopCode" column="shop_code"/>
        <result property="shopType" column="shop_type"/>
        <result property="password" column="password"/>
        <result property="aliShopCode" column="ali_shop_code"/>
        <result property="creator" column="creator"/>
        <result property="createDate" column="create_date"/>
        <result property="updater" column="updater"/>
        <result property="updateDate" column="update_date"/>
    </resultMap>

    <select id="selectByContractNo" resultMap="BaseResultMap">
        select * from tb_base_shop where contract_no = #{contractNo} and del_flag = '0' limit 1;
    </select>

    <select id="getByContractNoAndMallId" resultMap="BaseResultMap">
        select * from tb_base_shop where contract_no = #{contractNo} and mallid = #{mallId} and del_flag = '0' limit 1;
    </select>

    <select id="selectByGroupIdAndFormats" resultType="string">
        SELECT contract_no FROM tb_base_shop WHERE group_id = #{groupId} AND brand IN (
            SELECT id FROM tb_base_brand WHERE group_id = #{groupId} AND SUBSTRING_INDEX(category_ids, '#', 1) IN (
                SELECT id FROM tb_base_formats WHERE group_id = #{groupId}
                <if test="formats != null and formats.size > 0">
                    AND format_code IN
                    <foreach collection="formats" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            ));
    </select>
    
</mapper>