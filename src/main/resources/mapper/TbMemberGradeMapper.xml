<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberGradeMapper">
    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberGrade" >
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="desc_remark" property="descRemark" />
        <result column="sort" property="sort" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
        <result column="group_id" property="groupId" />
        <result column="card_cover_url" property="cardCoverUrl" />
        <result column="card_cover_home_url" property="cardCoverHomeUrl" />
        <result column="grade_desc" property="gradeDesc" />
        <result column="up_gradation_status" property="upGradationStatus" />
    </resultMap>
    <sql id="Base_Columns">
        id,
        code,
        name,
        desc_remark,
        `sort`,
        create_date,
        update_date,
        group_id,
        card_cover_url,
        card_cover_home_url,
        grade_desc,
        up_gradation_status
    </sql>

    <select id="getGradeSortAscByGroupId" resultMap="BaseResultMap">
        select <include refid="Base_Columns" /> from tb_member_grade where group_id = #{groupId} ORDER BY sort ASC;
    </select>

    <select id="getGradeSortDescByGroupId" resultMap="BaseResultMap">
        select <include refid="Base_Columns" /> from tb_member_grade where group_id = #{groupId} ORDER BY sort DESC;
    </select>

    <select id="getMinGradeByGroupId" resultMap="BaseResultMap">
        select <include refid="Base_Columns" /> from tb_member_grade where group_id = #{groupId} ORDER BY sort ASC limit 1;
    </select>

    <select id="getByGradeAndGroupId" resultMap="BaseResultMap">
        select <include refid="Base_Columns" /> from tb_member_grade where group_id = #{groupId}
        and code = #{grade}
        limit 1;
    </select>

</mapper>