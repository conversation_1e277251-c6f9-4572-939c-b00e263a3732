<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.SelfDefiningPageMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.SelfDefiningPage" >
        <result property="id" column="id"/>
        <result property="pageName" column="page_name"/>
        <result property="groupId" column="group_id"/>
        <result property="mallId" column="mall_id"/>
        <result property="openFlag" column="open_flag"/>
        <result property="type" column="type"/>
        <result property="versions" column="versions"/>
        <result property="headNavigationShow" column="head_navigation_show"/>
        <result property="headNavigationColor" column="head_navigation_color"/>
        <result property="capsuleColor" column="capsule_color"/>
        <result property="backgroundImageUrl" column="background_image_url"/>
        <result property="backgroundImageFlag" column="background_image_flag"/>
        <result property="myAdShow" column="my_ad_show"/>
        <result property="myAdUrl" column="my_ad_url"/>
        <result property="myWaterfallShow" column="my_waterfall_show"/>
        <result property="floatingBtnShow" column="floating_btn_show"/>
        <result property="floatingBtnUrl" column="floating_btn_url"/>
        <result property="completeFlag" column="complete_flag"/>
        <result property="underline" column="underline"/>
        <result property="myShowGrade" column="my_show_grade"/>
        <result property="moduleContext" column="module_context"/>
        <result property="showLineNum" column="show_line_num"/>
        <result property="showBadge" column="show_badge"/>
        <result property="createDate" column="create_date"/>
        <result property="createUser" column="create_user"/>
        <result property="updateDate" column="update_date"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        page_name,
        group_id,
        mall_id,
        open_flag,
        type,
        versions,
        head_navigation_show,
        head_navigation_color,
        capsule_color,
        background_image_url,
        background_image_flag,
        my_ad_show,
        my_ad_url,
        my_waterfall_show,
        floating_btn_show,
        floating_btn_url,
        complete_flag,
        underline,
        module_context,
        show_line_num,
        show_badge,
        my_show_grade,
        create_date,
        create_user,
        update_date,
        update_user
    </sql>

    <select id="findHomePage" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_self_defining_page where open_flag = '1'
        <if test="groupId != null and groupId != ''">
            and group_id = #{groupId}
        </if>
        <if test="mallId != null and mallId != ''">
            and mall_id = #{mallId}
        </if>
        <if test="type != null and type != ''">
            and `type` = #{type}
        </if>
        order by create_date desc limit 1;
    </select>

    <select id="findPageId" resultType="long">
        select id from tb_self_defining_page where open_flag = '1'
        <if test="groupId != null and groupId != ''">
            and group_id = #{groupId}
        </if>
        <if test="mallId != null and mallId != ''">
            and mall_id = #{mallId}
        </if>
        <if test="type != null and type != ''">
            and `type` = #{type}
        </if>
        order by create_date desc limit 1;
    </select>

    <select id="getPageById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_self_defining_page
           <where>
               <if test="groupId != null and groupId != ''">
                   and group_id = #{groupId}
               </if>
               <if test="mallId != null and mallId != ''">
                   and mall_id = #{mallId}
               </if>
               <if test="id != null">
                   and `id` = #{id}
               </if>
           </where>
    </select>

</mapper>