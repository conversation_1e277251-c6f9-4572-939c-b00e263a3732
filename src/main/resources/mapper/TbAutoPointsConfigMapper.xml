<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbAutoPointsConfigMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbAutoPointsConfig">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="format" jdbcType="VARCHAR" property="format" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="lbs_id" jdbcType="VARCHAR" property="lbsId" />
        <result column="project_id" jdbcType="VARCHAR" property="projectId" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
    </resultMap>

    <sql id="BaseColumns">
        id, format, group_id, lbs_id, project_id, code
    </sql>

    <select id="findByLbsId" parameterType="string" resultMap="BaseResultMap">
        select <include refid="BaseColumns" /> from tb_auto_points_config where lbs_id = #{lbsId} order by id desc limit 1;
    </select>

    <select id="listData" resultMap="BaseResultMap">
        select * from tb_auto_points_config
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="projectIds != null and projectIds.size > 0">
                and project_id in
                <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by id desc limit #{offset}, #{size};
    </select>

    <select id="total" resultType="int">
        select count(*) from tb_auto_points_config
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="projectIds != null and projectIds.size > 0">
                and project_id in
                <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>