<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberGradeChangeDetailMapper">

    <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
        <result column="authorized_mall_id" jdbcType="VARCHAR" property="authorizedMallId" />
        <result column="vipcode" jdbcType="VARCHAR" property="vipcode" />
        <result column="old_grade" jdbcType="VARCHAR" property="oldGrade" />
        <result column="new_grade" jdbcType="VARCHAR" property="newGrade" />
        <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="change_type" jdbcType="INTEGER" property="changeType" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    </resultMap>

    <sql id="Base_Column_List">
        id, group_id, mall_id, authorized_mall_id, vipcode, nick_name, mobile, old_grade,
        new_grade, remark, create_date, change_type, create_user
    </sql>

    <select id="queryChangeMaxByGroupIdAndVipcode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_member_grade_change_detail
        WHERE group_id = #{groupId} AND vipcode = #{vipcode} ORDER BY create_date DESC LIMIT 1
    </select>

    <select id="getMaxChangeDetailByGroupIdAndVipcodeAndOverDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_member_grade_change_detail
        WHERE group_id = #{groupId} AND vipcode = #{vipcode}
        <if test="overDate != null and overDate != ''">
            AND create_date &gt;= #{overDate}
        </if>
        ORDER BY create_date DESC LIMIT 1
    </select>

    <select id="getChangeDetailList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_member_grade_change_detail
        WHERE group_id = #{groupId} AND vipcode = #{vipcode};
    </select>

    <select id="queryMemberJoinTimeMaxGradeChangeItem" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tb_member_grade_change_detail
        WHERE group_id = #{groupId} AND vipcode = #{vipcode}
        <if test="joinTime != null and joinTime != ''">
            AND DATE_FORMAT(create_date, '%Y-%m-%d') = #{joinTime}
        </if>
        ORDER BY create_date DESC LIMIT 1;
    </select>

    <select id="queryDetailByGroupIdAndDate" resultMap="BaseResultMap">
        SELECT id, vipcode, create_date FROM tb_member_grade_change_detail WHERE
            group_id = #{groupId} AND create_date &gt; #{createDate}
    </select>

    <update id="updateChangeDate" parameterType="com.kerryprops.kip.service.integral.entity.TbMemberGradeChangeDetail">
        UPDATE tb_member_grade_change_detail set create_date = #{createDate} WHERE id = #{id};
    </update>

    <select id="queryChangeGradeListBySaleDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_member_grade_change_detail
        <where>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="vipcode != null and vipcode != ''">
                AND vipcode = #{vipcode}
            </if>
            <if test="saleDate != null and saleDate != ''">
                AND create_date &gt;= #{saleDate}
            </if>
        </where>
        ORDER BY create_date ASC
    </select>

    <select id="getMaxRelegationRecordByDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" /> from tb_member_grade_change_detail
        <where>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="vipcode != null and vipcode != ''">
                AND vipcode = #{vipcode}
            </if>
            <if test="createDate != null and createDate != ''">
                AND create_date &gt;= #{createDate}
            </if>
            <if test="changeTypes != null and changeTypes.size > 0">
                AND change_type IN
                <foreach collection="changeTypes" item="changeType" open="(" close=")" separator=",">
                    #{changeType}
                </foreach>
            </if>
        </where>
        ORDER BY create_date DESC LIMIT 1;
    </select>

    <select id="getChangeDetailGtCreateDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" /> from tb_member_grade_change_detail
        <where>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="vipcode != null and vipcode != ''">
                AND vipcode = #{vipcode}
            </if>
            <if test="createDate != null and createDate != ''">
                AND create_date &gt;= #{createDate}
            </if>
            <if test="changeTypes != null and changeTypes.size > 0">
                AND change_type IN
                <foreach collection="changeTypes" item="changeType" open="(" close=")" separator=",">
                    #{changeType}
                </foreach>
            </if>
        </where>
        ORDER BY create_date DESC;
    </select>

    <select id="getChangeDetailBetweenTime" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" /> from tb_member_grade_change_detail
        <where>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="vipcode != null and vipcode != ''">
                AND vipcode = #{vipcode}
            </if>
            <if test="startTime != null and startTime != ''">
                AND create_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND create_date &lt; #{endTime}
            </if>
            <if test="changeTypes != null and changeTypes.size > 0">
                AND change_type IN
                <foreach collection="changeTypes" item="changeType" open="(" close=")" separator=",">
                    #{changeType}
                </foreach>
            </if>
        </where>
        ORDER BY create_date DESC;
    </select>

    <select id="queryNearChangeGradeBySaleDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_member_grade_change_detail
        <where>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="vipcode != null and vipcode != ''">
                AND vipcode = #{vipcode}
            </if>
            <if test="saleDate != null and saleDate != ''">
                AND create_date &lt; #{saleDate}
            </if>
        </where>
        ORDER BY create_date DESC LIMIT 1;
    </select>

    <select id="getJoinDayMaxGrade" resultType="string">
        SELECT new_grade FROM tb_member_grade_change_detail
        where vipcode = #{vipcode} and group_id = #{groupId} and DATE_FORMAT(create_date, '%Y-%m-%d') = #{joinDay}
        order by create_date desc limit 1
    </select>

</mapper>