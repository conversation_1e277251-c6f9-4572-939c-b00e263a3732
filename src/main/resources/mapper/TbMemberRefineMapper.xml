<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kerryprops.kip.service.integral.mapper.TbMemberRefineMapper">

  <resultMap id="BaseResultMap" type="com.kerryprops.kip.service.integral.entity.TbMemberRefine">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mall_id" jdbcType="VARCHAR" property="mallId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="top_img" jdbcType="VARCHAR" property="topImg" />
    <result column="synopsis" jdbcType="VARCHAR" property="synopsis" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>

  <sql id="Base_Column_List">
    id, mall_id, group_id, top_img, synopsis, create_user, create_date, update_user, update_date
  </sql>

  <select id="findByMallId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tb_member_refine where mall_id = #{mallId} order by id desc limit 1;
  </select>

</mapper>