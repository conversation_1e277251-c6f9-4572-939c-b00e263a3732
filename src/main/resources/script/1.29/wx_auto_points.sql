
DROP TABLE IF EXISTS `tb_insensate_points_auth_record`;
CREATE TABLE `tb_insensate_points_auth_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
  `open_id` varchar(128) NOT NULL COMMENT '微信open id',
  `kip_user_id` varchar(128) NOT NULL COMMENT 'kip系统中用户唯一id',
  `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团id',
  `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场编号',
  `card_no` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '会员卡号（微信/支付宝）',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '授权状态，0已授权，1未授权，默认0',
  `origin` tinyint(3) unsigned NOT NULL COMMENT '来源，0微信，1支付宝',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `mchid` varchar(128) NOT NULL DEFAULT '' COMMENT '商圈id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid_mallid_origin` (`open_id`,`mall_id`,`origin`),
  KEY `idx_kip_mall_origin` (`kip_user_id`,`mall_id`,`origin`),
  KEY `idx_mchid_origin_status` (`mchid`,`origin`,`status`),
  KEY `idx_card_no_origin` (`card_no`,`origin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='无感积分_微信、支付宝授权记录表';

--add index to tb_card_member_relation for avoiding full table scan
ALTER TABLE `hdi_xcrm`.`tb_card_member_relation` ADD INDEX `idx_group_mobile` (`group_id` ASC, `mobile` ASC);

CREATE TABLE `tb_insensate_points_push_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
  `open_id` varchar(128) NOT NULL COMMENT '微信openid或支付宝user_id。根据origin判断',
  `transaction_id` varchar(32) NOT NULL COMMENT '支付订单号',
  `event_type` tinyint(1) unsigned NOT NULL COMMENT '事件类型。1支付，2退款。对应IntegralConstant.WX_PAY_SUCCESS等枚举',
  `transaction_info` varchar(8126) NOT NULL DEFAULT '' COMMENT '支付具体信息',
  `origin` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '来源，0微信，1支付宝',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid_transid_event` (`open_id`,`transaction_id`,`event_type`),
  KEY `idx_transid_event` (`transaction_id`,`event_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信、支付宝推送记录表';

-- 修改并且添加自助积分入口的sql
-- 1、 先将自助积分页面链接名称修改为   扫码积分
update tb_zindex_pageurl SET `name` = '扫码积分' WHERE `name` = '自助积分';
-- 2、 查询自助积分的上级会员工具的id
SELECT id FROM tb_zindex_pageurl WHERE name = '会员工具';
-- 3、 将会员工具的id用作新增数据的pid，将这个id替换 sql所写的  会员工具查询的id
INSERT INTO tb_zindex_pageurl (id, type, pid, name, url, function_type) VALUES (55, 0, 会员工具查询的id, '自助积分', '/pages/accumulatePoints/selfServicePoints', 'zindex#live');

CREATE TABLE `tb_insensate_points_repeat_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `transaction_id` varchar(64) NOT NULL COMMENT '无感积分单号',
  `origin` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源，0微信，1支付宝',
  `repeat_sale_id` VARCHAR(32) NOT NULL COMMENT '重复销售记录id',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_transaction_id_origin` (`transaction_id`, `origin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信、支付宝无感积分重复积分日志';

-- 业态表添加普通索引，QA未执行
ALTER TABLE tb_base_formats ADD INDEX idx_format_code_group_id_delFlag(`format_code`, `group_id`, `delFlag`) COMMENT '业态+品牌+是否删除索引';
ALTER TABLE tb_base_brand ADD INDEX idx_group_id_category_ids(`group_id`, `category_ids`);

DROP TABLE IF EXISTS `tb_qr_code_record`;
CREATE TABLE `tb_qr_code_record` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `qr_code` varchar(128) NOT NULL COMMENT '扫码积分二维码编号',
    `content` VARCHAR(2048) NULL COMMENT '销售记录json字符串',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_qr_code` (`qr_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫码积分gag查询记录';

-- 查询页面路径为pages/accumulatePoints/guide的数据条数
SELECT COUNT(*) FROM tb_zindex_pageurl WHERE url = 'pages/accumulatePoints/guide'
-- 如果执行结果查询为0则不需要修改
UPDATE tb_zindex_pageurl SET url = '/pages/accumulatePoints/guide' WHERE url = 'pages/accumulatePoints/guide';

CREATE TABLE `tb_member_card_open_record` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `open_id` VARCHAR(128) NOT NULL COMMENT 'open id',
    `kip_user_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'kip系统中用户唯一id',
    `card_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '会员卡号ID',
    `card_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员卡号',
    `event_type` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '事件类型',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_mall_id_group_id` (`mall_id`, `group_id`),
    UNIQUE KEY `uniq_open_id_mall_id` (`open_id`, `mall_id`),
    KEY `idx_card_no` (`card_no`),
    KEY `idx_kip_user_id_mall_id` (`kip_user_id`, `mall_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信/支付宝会员开卡记录';
