DROP TABLE IF EXISTS `tb_points_review`;
CREATE TABLE `tb_points_review` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL COMMENT '集团id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `vipcode` varchar(120) NOT NULL COMMENT '会员号',
    `mobile` varchar(32) NOT NULL COMMENT '手机号',
    `shop_no` varchar(120) NOT NULL COMMENT '店铺合同号',
    `shop_name` varchar(32) NOT NULL COMMENT '店铺名称',
    `serial_num` varchar(64) NOT NULL COMMENT '流水号',
    `money` decimal(16,2) NOT NULL COMMENT '金额',
    `bonus` INT NOT NULL DEFAULT 0 COMMENT '获得积分数量',
    `type` TINYINT NOT NULL DEFAULT 1 COMMENT '积分方式（1.小票；2.二维码）',
    `image_url` varchar(255) NOT NULL COMMENT '积分审核图片地址',
    `qrcode` varchar(255) NOT NULL COMMENT '扫码积分二维码编号',
    `description` varchar(255) NOT NULL COMMENT '描述',
    `reason` varchar(2550) NOT NULL COMMENT '未通过原因',
    `upload_date` datetime NOT NULL COMMENT '上传时间',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '审核状态 1待审核 2审核通过 3审核未通过',
    `timestamp` varchar(50) NOT NULL COMMENT '时间戳',
    `trading_date` varchar(32) NOT NULL COMMENT '交易日期',
    `check_time` datetime NOT NULL COMMENT '审核时间',
    `check_name` varchar(64) NOT NULL COMMENT '审核人名字',
    `format_id` varchar(255) NOT NULL COMMENT '业态组合',
    `format_name` varchar(255) NOT NULL COMMENT '业态名称',
    `integral_adjust_id` varchar(32) NOT NULL COMMENT '积分审核记录id',
    `org_points` INT NOT NULL DEFAULT 0 COMMENT '变更前积分数',
    `org_grade` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '变更前会员等级',
    `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` varchar(128) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_mall_id_upload_date` (`mall_id`,`upload_date`,`status`),
    KEY `idx_vipcode_status` (`vipcode`,`status`),
    KEY `idx_mobile_status` (`mobile`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分审核表';

INSERT INTO tb_points_review
(group_id,mall_id,vipcode,mobile,shop_no,shop_name,serial_num,money,bonus,`type`,image_url,qrcode,description,reason,upload_date,status,
 `timestamp`,trading_date,check_time,check_name,format_id,format_name,integral_adjust_id,org_points,org_grade,create_user,create_date,
 update_user,update_date)
SELECT
    IFNULL(group_id,''),IFNULL(mallid,''),IFNULL(vipcode,''),IFNULL(mobile,''),IFNULL(contractNo,''),IFNULL(storename,''),
    IFNULL(serialnum,''),IFNULL(money,0),IFNULL(bonus,0),IFNULL(type,0),IFNULL(uploadImgId,''),IFNULL(qrcode,''),IFNULL(describ,''),
    IFNULL(reason,''),IFNULL(upload_date,NOW()),IFNULL(state,1),IFNULL(timestamp,''),IFNULL(trading_date,NOW()),IFNULL(checktime,NOW()),
    IFNULL(checkname,''),IFNULL(formatids,''),IFNULL(formatnames,''),IFNULL(integraladjustid,''),IFNULL(beforeIntegral,0),
    IFNULL(beforeGrade,''),'',IFNULL(create_date,''),'', IFNULL(update_date,'') FROM tb_bonus_self where 1=1;