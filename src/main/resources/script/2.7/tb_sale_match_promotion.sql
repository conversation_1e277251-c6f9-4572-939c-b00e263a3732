DROP TABLE IF EXISTS `tb_sale_matched_promotion`;
CREATE TABLE `tb_sale_matched_promotion` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `sell_no` varchar(64) DEFAULT NULL COMMENT '销售单号',
    `integral_adjust_id` varchar(64) DEFAULT NULL COMMENT '积分调整记录id',
    `promotion_id` varchar(64) DEFAULT NULL COMMENT '营销活动id',
    `promotion_name` varchar(255) DEFAULT NULL COMMENT '营销活动名称',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_integral_adjust_id` (`integral_adjust_id`),
    KEY `idx_sell_no` (`sell_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售匹配的营销积分活动表';

DROP TABLE IF EXISTS `tb_ko_integral_clear_record`;
CREATE TABLE `tb_ko_integral_clear_record` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团id',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员号',
    `get_points` int NOT NULL DEFAULT 0 COMMENT '获得积分数',
    `cost_points` int NOT NULL DEFAULT 0 COMMENT '消耗积分数',
    `clear_points` int NOT NULL DEFAULT 0 COMMENT '清零积分数',
    `points_num` int NOT NULL DEFAULT 0 COMMENT '实际操作积分数',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_vipcode` (`vipcode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='KO积分清零记录表';