ALTER TABLE tb_member_asset ADD COLUMN authorized_mall_id VARCHAR(128) NOT NULL DEFAULT '' COMMENT '授权商场id';
ALTER TABLE tb_member_asset ADD COLUMN authorized_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间';
-- mall_id字段描述变更： KO: HomeSite    非KO: 开卡商场

UPDATE tb_member_asset SET authorized_mall_id = mall_id WHERE 1=1;

ALTER TABLE tb_member_grade_change_detail ADD COLUMN authorized_mall_id VARCHAR(128) NOT NULL DEFAULT '' COMMENT '授权商场id';
UPDATE tb_member_grade_change_detail SET authorized_mall_id = mall_id WHERE 1=1;