DROP TABLE IF EXISTS `tb_brand_guide_collection`;
CREATE TABLE `tb_brand_guide_collection` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL COMMENT '集团id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `vipcode` varchar(128) NOT NULL COMMENT '会员号',
    `brand_guide_id` varchar(128) NOT NULL COMMENT '品牌导览id',
    `status` tinyint unsigned NOT NULL DEFAULT 1 COMMENT '是否有效，1: 有效，0: 无效',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_vipcode_mall_id_brand_guide_id_status` (`vipcode`, `mall_id`, `brand_guide_id`, `status`),
    KEY `idx_brand_guide_id_status` (`brand_guide_id`, `status`),
    KEY `idx_mall_id_status` (`mall_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='品牌导览收藏表';