CREATE TABLE `tb_sys_oper_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `title` varchar(50) NOT NULL DEFAULT '' COMMENT '模块标题',
    `desc` varchar(255) NOT NULL DEFAULT '0' COMMENT '操作描述',
    `method` varchar(255) NOT NULL DEFAULT '' COMMENT '方法名称',
    `request_method` varchar(10) NOT NULL DEFAULT '' COMMENT '请求方式',
    `oper_type` varchar(128) NOT NULL DEFAULT '' COMMENT '操作来源',
    `oper_name` varchar(128) NOT NULL DEFAULT '' COMMENT '操作人名称',
    `oper_url` varchar(255) NOT NULL DEFAULT '' COMMENT '请求URL',
    `oper_param` varchar(2000) NOT NULL DEFAULT '' COMMENT '请求参数',
    `json_result` varchar(2000) NOT NULL DEFAULT '' COMMENT '返回参数',
    `status` int(1) NOT NULL DEFAULT '0' COMMENT '操作状态（1正常 0异常）',
    `error_msg` varchar(2000) NOT NULL DEFAULT '' COMMENT '错误消息',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    PRIMARY KEY (`id`),
    INDEX `idx_oper_url` (`oper_url`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT='系统操作日志表';

CREATE TABLE `tb_member_asset_freeze_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `member_id` bigint NOT NULL DEFAULT 0 COMMENT '会员id',
    `oper_desc` varchar(256) NOT NULL DEFAULT '' COMMENT '操作描述',
    `oper_name` varchar(256) NOT NULL DEFAULT '' COMMENT '操作人',
    `oper_param` varchar(2000) NOT NULL  DEFAULT '' COMMENT '请求参数',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    PRIMARY KEY (`id`),
    INDEX `idx_member_id` (`member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员冻结/解冻操作日志表';

CREATE TABLE `tb_member_grade_relegation_estimate_detail`  (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商场id',
    `nick_name` varchar(255) NOT NULL DEFAULT '' COMMENT '会员昵称',
    `mobile` varchar(64) NOT NULL DEFAULT '' COMMENT '会员手机号',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员号',
    `join_time` datetime NOT NULL COMMENT '会员加入时间',
    `latest_change_time` datetime NOT NULL COMMENT '最后一次卡等变更时间',
    `current_grade` varchar(255) NOT NULL DEFAULT '' COMMENT '当前卡等',
    `grade_sort` int(11) NOT NULL DEFAULT 0 COMMENT '卡等排序',
    `grade_valid_time` datetime NOT NULL COMMENT '卡等有效期',
    `total_amount` decimal(12, 2) NOT NULL COMMENT '保级期内年累计消费',
    `rule_year_accumulate_amount` decimal(12, 2) NOT NULL COMMENT '保级年累计金额',
    `differ_amount` decimal(12, 2) NOT NULL COMMENT '保级累计消费相差金额',
    `max_amount_per_day` decimal(12, 2) NOT NULL COMMENT '保级期内单日最高金额',
    `relegation_tag` tinyint(4) NOT NULL COMMENT '是否满足保级，1:满足，0:不满足',
    `relegate_type` tinyint(4) NOT NULL COMMENT '保级类型 4:不满足保级，8:满足每日消费，11:满足月消费，7:满足年消费，9:满足身份认证，99:特殊卡等保级',
    `year` int(11) NOT NULL COMMENT '保级记录所属年',
    `estimate_grade` varchar(255) NOT NULL DEFAULT '' COMMENT '保级执行后会员所属卡等',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_vipcode_group_id`(`vipcode` ASC, `group_id` ASC) USING BTREE,
    INDEX `idx_join_time_group_id`(`join_time` ASC, `group_id` ASC) USING BTREE,
    INDEX `idx_group_id_year`(`year` ASC, `group_id` ASC) USING BTREE,
    INDEX `idx_latest_change_time_group_id`(`latest_change_time`, `group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员保级/降级预估表';