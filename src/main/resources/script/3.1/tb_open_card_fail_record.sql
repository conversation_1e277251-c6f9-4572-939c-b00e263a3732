DROP TABLE IF EXISTS `tb_open_card_fail_record`;
CREATE TABLE `tb_open_card_fail_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `open_id` varchar(128) NOT NULL COMMENT '微信openId',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `content` text COMMENT '授权内容',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_open_id_mall_id` (`open_id`,`mall_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付即积分授权开通失败记录';