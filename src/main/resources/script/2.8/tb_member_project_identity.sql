DROP TABLE IF EXISTS `tb_member_project_identity`;
CREATE TABLE `tb_member_project_identity` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `kip_user_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'kip用户id',
    `project_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '楼盘ID',
    `office` TINYINT NOT NULL DEFAULT 0 COMMENT '是否是办公楼员工(0: 否；1:是)',
    `apartment` TINYINT NOT NULL DEFAULT 0 COMMENT '是否是公寓租户(0: 否；1:是)',
    `residence` TINYINT NOT NULL DEFAULT 0 COMMENT '是否是住宅住户(0: 否；1:是)',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_kip_user_id_project_id` (`kip_user_id`, `project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员楼盘身份信息表';