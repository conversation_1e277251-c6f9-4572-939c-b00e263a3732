-- 积分抵现config
DROP TABLE IF EXISTS `tb_cash_out_config`;
CREATE TABLE `tb_cash_out_config` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `name` varchar(128) NOT NULL COMMENT '活动名称',
    `start_time` varchar(128) NOT NULL COMMENT '活动开始时间',
    `end_time` varchar(128) NOT NULL COMMENT '活动结束时间',
    `shop_no` varchar(1000) NOT NULL COMMENT '活动店铺，用,分隔',
    `is_limit` int(10) NOT NULL COMMENT '是否开启最大金额现在 0 关闭 1 开启',
    `money` decimal(10,0) DEFAULT NULL COMMENT '抵现最大金额',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_mall_id` (`mall_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分抵现配置表';




