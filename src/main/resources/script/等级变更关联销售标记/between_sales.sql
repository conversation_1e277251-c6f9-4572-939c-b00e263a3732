DROP TABLE IF EXISTS `tb_grade_change_between_sales`;
CREATE TABLE `tb_grade_change_between_sales`  (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `change_id` varchar(64) NOT NULL COMMENT '关联等级变更记录id',
    `sales_start_time` datetime NOT NULL COMMENT '销售开始时间',
    `sales_end_time` datetime NOT NULL COMMENT '销售结束时间',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_change_id` (`change_id`) USING BTREE COMMENT '等级变更记录id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级变更关联销售时间记录表';