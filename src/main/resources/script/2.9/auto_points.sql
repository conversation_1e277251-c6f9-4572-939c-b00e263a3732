DROP TABLE IF EXISTS `tb_auto_points_config`;
CREATE TABLE `tb_auto_points_config` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `format` varchar(128) NOT NULL COMMENT '业态',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `lbs_id` varchar(128) NOT NULL COMMENT 'lbs id',
    `project_id` varchar(128) NOT NULL COMMENT 'project id',
    `code` varchar(128) NOT NULL DEFAULT '' COMMENT '自助积分功能位，多个之间英文逗号隔开',
    `status` tinyint unsigned NOT NULL DEFAULT 1 COMMENT '是否启用，1: 启用，0: 禁用',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    PRIMARY KEY (`id`),
    KEY `idx_lbs_id_status` (`lbs_id`,`status`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自助积分入口配置表';

-- 线上商场积分兑换比例
DROP TABLE IF EXISTS `tb_online_shop_rate`;
CREATE TABLE `tb_online_shop_rate` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT 'lbs id',
    `grade` varchar(128) NOT NULL COMMENT '会员等级code',
    `business_type` varchar(128) NOT NULL DEFAULT '' COMMENT '业务类型',
    `money` decimal(10, 1) NOT NULL COMMENT '金额',
    `point_num` decimal(10, 1) NOT NULL COMMENT '积分数',
    `status` tinyint unsigned NOT NULL DEFAULT 1 COMMENT '是否启用，1: 启用，0: 禁用',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    PRIMARY KEY (`id`),
    KEY `idx_mall_id` (`mall_id`),
    KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线上商城积分比例设置表';

--积分商城购买支付推送记录
DROP TABLE IF EXISTS `tb_payment_push_record`;
CREATE TABLE `tb_payment_push_record` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `order_no` varchar(128) NOT NULL COMMENT '订单号',
    `trade_type` varchar(32) NOT NULL COMMENT '交易类型',
    `pay_method` varchar(32) NOT NULL COMMENT '支付方式',
    `refund_no` varchar(128) NOT NULL DEFAULT '0' COMMENT '退款单号',
    `content` varchar(8126) NOT NULL DEFAULT '' COMMENT '支付具体信息',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no_trade_type_pay_method_refund_no` (`order_no`,`trade_type`,`pay_method`, `refund_no`),
    KEY `idx_refund_no` (`refund_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线上商城推送支付记录表';