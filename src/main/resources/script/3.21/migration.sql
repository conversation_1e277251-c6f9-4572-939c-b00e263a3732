DROP TABLE IF EXISTS `tb_activity_promotion_points`;
CREATE TABLE `tb_activity_promotion_points` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `promotion_id` varchar(64) NOT NULL DEFAULT '' COMMENT '营销规则id',
    `points_id` varchar(64) NOT NULL DEFAULT '' COMMENT '积分调整记录id',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员编号',
    `integral` INT NOT NULL DEFAULT 0 COMMENT '营销积分',
    `enable_integral` INT NOT NULL DEFAULT 0 COMMENT '可用营销积分',
    `mobile` varchar(100) NOT NULL DEFAULT '' COMMENT '手机号',
    `prime_integral` INT NOT NULL DEFAULT 0 COMMENT '初始积分',
    `promotion_name` varchar(255) NOT NULL DEFAULT '' COMMENT '营销活动名称',
    `sale_money` decimal(10, 2) DEFAULT 0 COMMENT '消费金额',
    `mall_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商场号',
    `sell_no` varchar(64) NOT NULL DEFAULT '' COMMENT '销售单号',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `expire_time` datetime NOT NULL DEFAULT '9999-12-31 23:59:59' COMMENT '营销积分过期时间',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_queryMemberPromotionIntegral`(`points_id`, `vipcode`, `expire_time`),
    KEY `idx_querEnableIntegralByVipNo`(`vipcode`, `expire_time`),
    KEY `idx_sell_no`(`sell_no`),
    KEY `idx_vipcode`(`vipcode`),
    KEY `idx_promotion_id_mall_id` (`promotion_id`, `mall_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '会员营销积分表';

-- 数据迁移sql
INSERT INTO tb_activity_promotion_points(promotion_id, points_id, vipcode, integral, enable_integral, expire_time, create_date, update_date,
                                         mobile, prime_integral, promotion_name, sale_money, mall_id, sell_no, remark)
SELECT
    IFNULL(promotionid, '') as promotion_id,
    IFNULL(integraladjustid, '') as points_id,
    IFNULL(vipno, '') AS vipcode,
    IFNULL(integral, 0) AS integral,
    IFNULL(enableIntegral, 0) AS enable_integral,
    IFNULL(integralExpirationDate, NOW()) AS expire_time,
    IFNULL(createdate, NOW()) AS create_date,
    IFNULL(updatedate, NOW()) AS update_date,
    IFNULL(mobile, '') AS mobile,
    IFNULL(prime_integral, 0) AS prime_integral,
    IFNULL(promotion_name, '') AS promotion_name,
    IFNULL(sale_money, 0) AS sale_money,
    IFNULL(mallid, '') AS mall_id,
    IFNULL(sell_no, '') AS sell_no,
    IFNULL(remark, '') AS remark
FROM tb_activity_promotionintegral WHERE 1=1

DROP TABLE IF EXISTS `tb_member_mall_relation`;
CREATE TABLE `tb_member_mall_relation` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `mobile` varchar(64) NOT NULL DEFAULT '' COMMENT '手机号',
    `name` varchar(64) NOT NULL DEFAULT '' COMMENT '姓名',
    `gender` varchar(64) NOT NULL DEFAULT '' COMMENT '性别',
    `mall_id` varchar(120) NOT NULL DEFAULT '' COMMENT '商场编号',
    `group_id` varchar(120) DEFAULT NULL COMMENT '集团id',
    `biz_card_no` varchar(64) NOT NULL DEFAULT '' COMMENT '支付宝业务卡号',
    `external_card_no` varchar(64) NOT NULL DEFAULT '' COMMENT '外部商户会员卡卡号',
    `open_date` date NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开卡时间',
    `valid_date` varchar(128) NOT NULL DEFAULT '' COMMENT '有效期',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '支付宝用户id',
    `mall_type` int NOT NULL DEFAULT '0' COMMENT '会员所属商圈，0集团，1上海静安，2北京',
    PRIMARY KEY (`id`),
    KEY `idx_mobile`(`mobile`),
    KEY `idx_biz_card_no`(`biz_card_no`),
    KEY `idx_external_card_no`(`external_card_no`),
    KEY `idx_user_id`(`user_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '会员商场关系表';

-- 数据迁移sql
INSERT INTO tb_member_mall_relation(mobile, `name`, gender, mall_id, group_id, biz_card_no, external_card_no, open_date,
                                    valid_date, user_id, mall_type, create_time, update_time)
SELECT
    IFNULL(mobile, '') as mobile,
    IFNULL(`name`, '') as `name`,
    IFNULL(gender, '') as gender,
    IFNULL(mall_id, '') as mall_id,
    IFNULL(group_id, '') as group_id,
    IFNULL(biz_card_no, '') as biz_card_no,
    IFNULL(external_card_no, '') as external_card_no,
    IFNULL(open_date, NOW()) as open_date,
    IFNULL(valid_date, '') as valid_date,
    IFNULL(user_id, '') as user_id,
    IFNULL(mall_type, '') as mall_type,
    IFNULL(create_time, NOW()) as create_time,
    IFNULL(update_date, NOW()) as update_time
FROM tb_card_member_relation WHERE 1=1;

DROP TABLE IF EXISTS `tb_member_open_card_record`;
CREATE TABLE `tb_member_open_card_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `open_id` varchar(128) NOT NULL COMMENT 'open id',
    `kip_user_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'kip系统中用户唯一id',
    `card_id` varchar(128) NOT NULL DEFAULT '' COMMENT '会员卡号ID',
    `card_no` varchar(64) NOT NULL DEFAULT '' COMMENT '会员卡号',
    `event_type` varchar(64) NOT NULL DEFAULT '' COMMENT '事件类型',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_open_id_mall_id` (`open_id`, `mall_id`),
    KEY `idx_mall_id_group_id` (`mall_id`, `group_id`),
    KEY `idx_card_no` (`card_no`),
    KEY `idx_kip_user_id_mall_id` (`kip_user_id`, `mall_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '微信/支付宝会员开卡记录';

-- 数据迁移sql
INSERT INTO tb_member_open_card_record(group_id, mall_id, open_id, kip_user_id, card_id, card_no, event_type, create_date)
SELECT
    IFNULL(group_id, '') AS group_id,
    IFNULL(mall_id, '') AS mall_id,
    IFNULL(open_id, '') AS open_id,
    IFNULL(kip_user_id, '') AS kip_user_id,
    IFNULL(card_id, '') AS card_id,
    IFNULL(card_no, '') AS card_no,
    IFNULL(event_type, '') AS event_type,
    IFNULL(create_date, NOW()) AS create_date
FROM tb_member_card_open_record WHERE 1=1;

DROP TABLE IF EXISTS `tb_auto_sale_member_tag`;
CREATE TABLE `tb_auto_sale_member_tag` (
    `id` bigint NOT NULL COMMENT '主键id',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团Id',
    `tag_name` varchar(255) NOT NULL DEFAULT '' COMMENT '标签名称',
    `describes` varchar(2048) NOT NULL DEFAULT '' COMMENT '标签描述',
    `status` int NOT NULL DEFAULT 0 COMMENT '1:统计中  2：统计成功 3：统计失败',
    `firsort_id` bigint NOT NULL DEFAULT 0 COMMENT '一级分类id',
    `secsort_id` bigint NOT NULL DEFAULT 0 COMMENT '二级分类id',
    `target_number` bigint NOT NULL DEFAULT 0 COMMENT '目标人数',
    `condition_param` varchar(2048) NOT NULL DEFAULT '' COMMENT '规则条件',
    `query_conditions` varchar(2048) NOT NULL DEFAULT '' COMMENT '查询sql字符串',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `type` int NOT NULL DEFAULT 0 COMMENT '标签类型（1：逻辑标签；2：无逻辑标签）',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(30) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(30) NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_tag_name` (`tag_name`, `group_id`),
    KEY `idx_secsort_id_type` (`secsort_id`, `type`),
    KEY `idx_group_id_type` (`group_id`, `type`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '标签库';

-- 数据迁移sql
insert into tb_auto_sale_member_tag(id, group_id, tag_name, `status`, firsort_id, secsort_id, target_number, condition_param, query_conditions, remark, describes, `type`, create_user, create_date,
    update_user, update_date)
select
    id,
    IFNULL(group_id, '') as group_id,
    IFNULL(tagname, '') as tag_name,
    IFNULL(`status`, 0) as `status`,
    IFNULL(`firsort_id`, 0) as `firsort_id`,
    IFNULL(`secsort_id`, 0) as `secsort_id`,
    IFNULL(`targetnumber`, 0) as `target_number`,
    IFNULL(`conditionparam`, '') as `condition_param`,
    IFNULL(`queryconditions`, '') as `query_conditions`,
    IFNULL(`remark`, '') as `remark`,
    IFNULL(`describes`, '') as `describes`,
    IFNULL(`type`, 0) as `type`,
    IFNULL(`create_user`, '') as `create_user`,
    IFNULL(`create_date`, NOW()) as `create_date`,
    IFNULL(`update_user`, '') as `update_user`,
    IFNULL(`update_date`, NOW()) as `update_date`
    from tb_autosale_membertag where 1=1;

DROP TABLE IF EXISTS `tb_tag_member_no_logic`;
CREATE TABLE `tb_tag_member_no_logic` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团编号',
    `mall_id` varchar(32) NOT NULL DEFAULT '' COMMENT '商场号',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员号',
    `tag_ids` varchar(1024) NOT NULL DEFAULT '' COMMENT '会员无逻辑标签组',
    `lables` varchar(1024) NOT NULL DEFAULT '' COMMENT '会员逻辑标签组',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_vipcode` (`vipcode`, `group_id`),
    FULLTEXT KEY `idx_lables` (`lables`),
    FULLTEXT KEY `idx_tag_ids` (`tag_ids`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '会员无逻辑标签数据表';

-- 数据迁移sql
insert into tb_tag_member_no_logic(id, group_id, mall_id, vipcode, tag_ids, lables, create_date, update_date)
select
    id,
    IFNULL(group_id, '') as group_id,
    IFNULL(mallid, '') as mall_id,
    IFNULL(vipcode, '') as vipcode,
    IFNULL(tagids, '') as tag_ids,
    IFNULL(lables, '') as lables,
    IFNULL(`createDate`, NOW()) as `create_date`,
    IFNULL(`updateDate`, NOW()) as `update_date`
from tb_tag_member_nologic where 1=1;

DROP TABLE IF EXISTS `tb_auto_sale_member_sort`;
CREATE TABLE `tb_auto_sale_member_sort` (
    `id` bigint NOT NULL COMMENT '主键',
    `group_id` varchar(255) NOT NULL DEFAULT '' COMMENT '集团id',
    `sort_name` varchar(255) NOT NULL DEFAULT '' COMMENT '分类名称',
    `pid` bigint NOT NULL DEFAULT 0 COMMENT 'pid',
    `create_user` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_pid` (`pid`),
    KEY `idx_group_id` (`group_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '标签库分类详情表';

-- 数据迁移sql
insert into tb_auto_sale_member_sort(id, group_id, sort_name, pid, create_user, create_date, update_user, update_date)
select
    id,
    IFNULL(group_id, '') as group_id,
    IFNULL(sortname, '') as sort_name,
    IFNULL(pid, 0) as pid,
    IFNULL(create_user, '') as create_user,
    IFNULL(`create_date`, NOW()) as `create_date`,
    IFNULL(update_user, '') as update_user,
    IFNULL(`update_date`, NOW()) as `update_date`
from tb_autosale_membersort where 1=1;