DROP TABLE IF EXISTS `tb_member_register_source`;
CREATE TABLE `tb_member_register_source` (
    `id` bigint NOT NULL COMMENT '主键id',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员id',
    `kip_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '会员id',
    `utm_lbs` varchar(64) NOT NULL DEFAULT '' COMMENT '商场id',
    `channel` varchar(64) NOT NULL DEFAULT '' COMMENT '当前渠道',
    `utm_channel` varchar(64) NOT NULL DEFAULT '' COMMENT '来源渠道',
    `utm_method` varchar(64) NOT NULL DEFAULT '' COMMENT '方式',
    `utm_source` varchar(64) NOT NULL DEFAULT '' COMMENT '来源',
    `utm_function` varchar(64) NOT NULL DEFAULT '' COMMENT '功能',
    `utm_user` varchar(64) NOT NULL DEFAULT '' COMMENT '分享人',
    `page_path` varchar(255) NOT NULL DEFAULT '' COMMENT '页面连接',
    `original_params` text COMMENT '原始参数',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_vipcode` (`vipcode`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '会员注册来源表';

DROP TABLE IF EXISTS `tb_authorizer_user`;
CREATE TABLE `tb_authorizer_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user` varchar(255) NOT NULL DEFAULT '' COMMENT '请求参数用户名(系统用户sys_开头，第三方thirdparty_开头)',
    `password` varchar(255) NOT NULL DEFAULT '' COMMENT '请求参数用户密码',
    `ips` varchar(512) NOT NULL DEFAULT '' COMMENT '用户关联请求ip',
    `is_use` int NOT NULL DEFAULT '1' COMMENT '(0 : 不可用; 1 : 可用)',
    `is_check` int NOT NULL DEFAULT '1' COMMENT '(0 : 不验证; 1 : 验证)',
    `create_user` varchar(255) NOT NULL DEFAULT '' COMMENT '创建用户',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` varchar(255) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `authorizer_wx_type` varchar(255) NOT NULL DEFAULT '' COMMENT '授权类型',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_user` (`user`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '权限验证用户表';