
CREATE TABLE `tb_qr_code_record` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `qr_code` varchar(128) NOT NULL COMMENT '扫码积分二维码编号',
    `content` varchar(2048) DEFAULT NULL COMMENT '销售记录json字符串',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_qr_code` (`qr_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫码积分gag查询记录';