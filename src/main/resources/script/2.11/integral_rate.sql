-- 店铺积分兑换比例设置表
DROP TABLE IF EXISTS `tb_integral_shop_rate`;
CREATE TABLE `tb_integral_shop_rate` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `mall_id` varchar(128) NOT NULL COMMENT '商场id',
    `grade_id` varchar(128) NOT NULL COMMENT '会员等级code',
    `grade_name` varchar(128) NOT NULL COMMENT '会员等级名称',
    `shop_id` varchar(128) NOT NULL COMMENT '商铺id',
    `shop_name` varchar(128) NOT NULL COMMENT '商铺名称',
    `money` decimal(10, 1) DEFAULT NULL COMMENT '金额',
    `point_num` decimal(10, 1) DEFAULT NULL COMMENT '积分数',
    `status` tinyint unsigned NOT NULL DEFAULT 1 COMMENT '是否启用，1: 启用，0: 禁用',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    PRIMARY KEY (`id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_mall_id` (`mall_id`),
    KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺积分兑换比例设置表';

-- 业态积分兑换比例设置表
DROP TABLE IF EXISTS `tb_integral_category_rate`;
CREATE TABLE `tb_integral_category_rate` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `group_id` varchar(128) NOT NULL COMMENT 'group id',
    `category_id` varchar(128) NOT NULL COMMENT '业态id',
    `category_name` varchar(128) NOT NULL COMMENT '业态名称',
    `grade_id` varchar(128) NOT NULL COMMENT '会员等级code',
    `grade_name` varchar(128) DEFAULT NULL COMMENT '会员等级名称',
    `money` decimal(10, 1) DEFAULT NULL COMMENT '金额',
    `point_num` decimal(10, 1) DEFAULT NULL COMMENT '积分数',
    `status` tinyint unsigned NOT NULL DEFAULT 1 COMMENT '是否启用，1: 启用，0: 禁用',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updater` varchar(128) NOT NULL DEFAULT 'sys' COMMENT '修改人',
    PRIMARY KEY (`id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业态积分兑换比例设置表';


