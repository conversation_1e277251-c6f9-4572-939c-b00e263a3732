DROP TABLE IF EXISTS `tb_points_detail`;
CREATE TABLE `tb_points_detail` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增唯一主键id',
    `crm_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'CRM迁移数据主键ID,对应原crm积分变更明细表的主键id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员编号',
    `type` varchar(1) NOT NULL DEFAULT '1' COMMENT '调整类型 1->普通调整， 2->销售调整',
    `current_points` int(11) NOT NULL DEFAULT '0' COMMENT '积分变更后会员积分数',
    `amount` int(11) NOT NULL DEFAULT '0' COMMENT '调整积分数量',
    `left_points` int(11) NOT NULL DEFAULT '0' COMMENT '剩余可用积分数',
    `reason_type` varchar(255) NOT NULL DEFAULT '' COMMENT '积分调整原因',
    `reason_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '冗余调整原因',
    `order_no` varchar(64) NOT NULL DEFAULT '' COMMENT '销售单号(销售调整)',
    `pos_no` varchar(64) NOT NULL DEFAULT '' COMMENT '收银机号(销售调整)',
    `shop_no` varchar(120) NOT NULL DEFAULT '' COMMENT '商场编号',
    `remark` varchar(1024) NOT NULL DEFAULT '' COMMENT '备注',
    `expire_date` varchar(64) NOT NULL DEFAULT '' COMMENT '过期时间',
    `extend1` varchar(64) NOT NULL DEFAULT '' COMMENT '扩展字段1',
    `extend2` varchar(255) NOT NULL DEFAULT '' COMMENT '扩展字段2',
    `extend3` varchar(64) NOT NULL DEFAULT '' COMMENT '扩展字段3',
    `image_url` varchar(255) NOT NULL DEFAULT '' COMMENT '拍照积分上传图片地址',
    `channel` varchar(1) NOT NULL DEFAULT '2' COMMENT '调整渠道（0:crm admin端积分 1:服务台积分, 2:用户自动积分 默认2）',
    `org_points` int(11) NOT NULL DEFAULT '0' COMMENT '变更前积分数',
    `org_grade` varchar(32) NOT NULL DEFAULT '' COMMENT '变更前会员等级',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(128) NOT NULL DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`),
    KEY `idx_crm_id` (`crm_id`),
    KEY `idx_order_no` (`order_no`,`group_id`),
    KEY `idx_vipcode_create_date` (`vipcode`,`create_date`),
    KEY `idx_mobile_create_date` (`mobile`,`create_date`),
    KEY `idx_shop_no_create_date` (`shop_no`,`create_date`),
    KEY `idx_create_date_group_id_mall_id` (`create_date`,`group_id`,`mall_id`) COMMENT '商场+创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员积分变更明细表';

INSERT INTO `tb_points_detail` (`crm_id`, `group_id`, `mall_id`, `mobile`, `vipcode`, `type`, `current_points`, `amount`, `left_points`, `reason_type`, `reason_desc`, `order_no`, `pos_no`, `shop_no`, `remark`, `expire_date`,
                                `extend1`, `extend2`, `extend3`, `image_url`, `channel`, `org_points`, `org_grade`, `create_date`, `create_user`, `update_date`, `update_user`)
SELECT `id` as crm_id, IFNULL(`group_id`, '') as group_id, IFNULL(`mallid`, '') as mall_id, IFNULL(`mobile`, '') AS `mobile`, IFNULL(`vipcode`, '') AS `vipcode`, IFNULL(`type`, 'A') AS `type`, IFNULL(`currentIntegral`, 0) AS `current_points`, IFNULL(`number`, 0) AS `amount`, IFNULL(`left_points`, 0) AS `left_points`, IFNULL(`remark`, '') AS `reason_type`,
       IFNULL(`remarkName`, '') AS `reason_desc`, IFNULL(`sellNo`, '') AS `order_no`, IFNULL(`cashNo`, '') `pos_no`, IFNULL(`shopId`, '') AS `shop_no`, IFNULL(`content`, '') AS `remark`, IFNULL(`expdate`, '') `expire_date`, IFNULL(`extend1`,'') AS `extend1`, IFNULL(`extend2`,'') AS `extend2`,
       IFNULL(`extend3`,'') AS `extend3`, IFNULL(`imageUrl`,'') AS `image_url`, IFNULL(`channerl_source`,'') AS `channel`, IFNULL(`beforeIntegral`, 0) `org_points`, IFNULL(`beforeGrade`,'') `org_grade`, `create_date`, IFNULL(`createUser`,'') AS `create_user`, `update_date`, IFNULL(`updateUser`,'') AS `update_user`
FROM `tb_member_integraladjust` WHERE 1=1;

DROP TABLE IF EXISTS `tb_sales_detail`;
CREATE TABLE `tb_sales_detail` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增唯一主键id',
    `crm_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'CRM迁移数据主键ID,对应原crm销售明细表的主键id',
    `group_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '商场ID',
    `vipcode` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员编号',
    `kip_user_id` VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'kip用户id',
    `points_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '积分变更明细表的crm_id',
    `points_num` int NOT NULL DEFAULT 0 COMMENT '销售单获得积分数',
    `parent_order_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '父级订单号',
    `order_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '销售单号(销售调整)流水号',
    `total_amount` decimal(30,2) NOT NULL DEFAULT '0.00' COMMENT '销售单号总金额',
    `discount_amount` decimal(30,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
    `points_offset_amount` decimal(30,2) NOT NULL DEFAULT '0.00' COMMENT '积分抵现金额',
    `pay_amount` decimal(30,2) NOT NULL DEFAULT '0.00' COMMENT '实际支付金额',
    `sale_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '销售日期',
    `shop_no` VARCHAR(120) NOT NULL DEFAULT '' COMMENT '店铺编号',
    `shop_name` VARCHAR(120) NOT NULL DEFAULT '' COMMENT '店铺名称',
    `status` int NOT NULL DEFAULT 0 COMMENT '退货状态(0->未退货, 1->退货, 2->部分退款)',
    `sale_type` VARCHAR(32) NOT NULL DEFAULT 1 COMMENT '销售类型1.CRM销售,2.POS销售,3.微信照片,4.微信扫码,5.口碑销售',
    `refund_points_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '有单退货扣积分 积分变更明细表的crm_id',
    `refund_amount` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '退货金额',
    `refund_points_num` int NOT NULL DEFAULT 0 COMMENT '退货积分数',
    `extend1` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员身份信息-是否办公楼会员',
    `extend2` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员身份信息-是否公寓会员',
    `extend3` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会员身份信息-是否住户会员',
    `remark` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT '备注',
    `image_url` VARCHAR(512) NOT NULL DEFAULT '' COMMENT '销售小票地址',
    `org_points` int NOT NULL DEFAULT 0 COMMENT '变更前积分',
    `org_grade` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '变更前等级',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(128) NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_crm_id` (`crm_id`),
    KEY `idx_order_no_mall_id` (`order_no`, `mall_id`),
    KEY `idx_shop_no_create_date` (`shop_no`, `create_date`),
    KEY `idx_points_id` (`points_id`),
    KEY `idx_vipcode_create_date` (`vipcode`, `create_date`),
    KEY `idx_kip_user_id_create_date` (`kip_user_id`, `create_date`),
    KEY `idx_create_date_group_id` (`create_date`, `group_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员销售明细表';

INSERT INTO tb_sales_detail (crm_id, group_id, mall_id, vipcode, kip_user_id, points_id, points_num, parent_order_no, order_no, total_amount, discount_amount, points_offset_amount, pay_amount,
                             sale_date, sale_type, shop_no, shop_name, `status`, refund_points_id, refund_amount, refund_points_num, extend1, extend2, extend3, remark, image_url, org_points,
                             org_grade, create_date, create_user, update_date, update_user)
SELECT id as crm_id, IFNULL(group_id,''), IFNULL(mallid,'') AS mall_id, IFNULL(vipcode,'') AS vipcode, '' AS kip_user_id, IFNULL(integraladjustId,'') AS points_id, IFNULL(`integral`, '0') AS points_num, IFNULL(parentOrderNo,'') AS parent_order_no, IFNULL(sellNo,'') AS order_no, IFNULL(saleMoney, 0.00) AS total_amount,
       IFNULL(preferentialMoney, 0.00) AS discount_amount, 0.00 AS points_offset_amount, IFNULL(useMoney, 0.00) AS pay_amount, IFNULL(create_date, NOW()) AS sale_date, IFNULL(saleType,'1') AS sale_type, IFNULL(shopId,'') AS shop_no, IFNULL(shopName,'') AS shop_name,
       CASE WHEN `status` is NULL THEN 0 WHEN `status`= '' THEN 0 ELSE `status` END AS `status`,
       IFNULL(backIntegraladjustid,'') AS refund_points_id, IFNULL(backMoney, 0.00) AS refund_amount, IFNULL(`backIntegral`, '0') AS refund_points_num, IFNULL(extend1,'0') AS extend1, IFNULL(extend2,'0') AS extend2, IFNULL(extend3,'0') AS extend3,
    IFNULL(remark,'') AS remark, IFNULL(image_url,'') AS image_url, IFNULL(`beforeIntegral`, '0') AS org_points, IFNULL(beforeGrade,'') AS org_grade, IFNULL(create_date, NOW()) AS create_date, IFNULL(createUser,'') AS create_user, IFNULL(update_date, NOW()) AS update_date,
    IFNULL(updateUser,'') AS update_user FROM tb_member_sale WHERE 1=1;

CREATE TABLE `tb_member_asset` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增唯一主键id',
    `crm_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'CRM迁移数据主键ID,对应原crm销售明细表的主键id',
    `group_id` varchar(64) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商场编号',
    `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号码',
    `current_points` int NOT NULL DEFAULT 0 COMMENT '当前积分数',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员编号（规则生成）',
    `grade` varchar(64) NOT NULL DEFAULT '' COMMENT '会员等级[编码]',
    `kip_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'kip对应的user_id',
    `status` int NOT NULL DEFAULT 1 COMMENT '会员状态（0:冻结；1:非冻结)',
    `wx_open_market` varchar(64) NOT NULL DEFAULT '' COMMENT ',微信激活商场',
    `is_completed` varchar(20) NOT NULL DEFAULT '' COMMENT '是否完善信息（0:未完善；1:完善)',
    `remark` varchar(4000) NOT NULL DEFAULT '' COMMENT '备注',
    `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间即加入日期',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间即加入日期',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人',
    `whether_blacklist` int NOT NULL DEFAULT 1 COMMENT '是否黑名单(0:是 1:不是),供活动使用',
    `register_source` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源',
    `register_source_label` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源-附注信息(例如：活动，电子券)',
    `register_source_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源-备注信息',
    PRIMARY KEY (`id`),
    KEY `idx_crm_id` (`crm_id`),
    UNIQUE KEY `uniq_mobile_group_id` (`mobile`,`group_id`),
    KEY `idx_kip_user_id_group_id` (`kip_user_id`, `group_id`),
    KEY `idx_vipcode_group_id` (`vipcode`, `group_id`),
    KEY `idx_group_id_create_date` (`group_id`,`create_date`) COMMENT '集团id+创建时间',
    KEY `idx_mall_id_create_date` (`mall_id`,`create_date`),
    KEY `idx_create_date` (`create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员资产表';

INSERT INTO tb_member_asset (crm_id, group_id,mall_id,mobile,current_points,
                             vipcode,grade,kip_user_id,status,wx_open_market,
                             is_completed,remark,join_time,create_date,create_user,
                             update_date,update_user,whether_blacklist,register_source,register_source_label,
                             register_source_remark)
select id as crm_id, IFNULL(group_id, ''),IFNULL(mallid, ''),IFNULL(mobile, ''),IFNULL(currnentIntegral, 0),IFNULL(vipcode, ''),
       IFNULL(grade, ''),IFNULL(kip_user_id, ''),
       CASE WHEN `status` is NULL THEN 0 WHEN `status`= '' THEN 0 ELSE `status` END AS `status`,
       IFNULL(wxOpenMarket, ''),IFNULL(isCompleted, '0'),
       IFNULL(remark, ''),IFNULL(joinTime,now()),IFNULL(create_date, now()),IFNULL(createUser, ''),IFNULL(update_date, now()),IFNULL(updateUser, ''),
       IFNULL(whether_blacklist, 1),IFNULL(register_source, ''),IFNULL(register_source_label, ''),IFNULL(register_source_remark, '')
from tb_member where 1=1 and group_id is not null and group_id != '';


CREATE TABLE `tb_member_asset_invalid` (
    `id` varchar(64) NOT NULL COMMENT '主键',
    `group_id` varchar(255) NOT NULL DEFAULT '' COMMENT '集团ID',
    `mall_id` varchar(120) NOT NULL DEFAULT '' COMMENT '商场编号',
    `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号码',
    `current_points` int NOT NULL DEFAULT 0 COMMENT '当前积分数',
    `vipcode` varchar(64) NOT NULL DEFAULT '' COMMENT '会员编号（规则生成）',
    `status` varchar(8) NOT NULL DEFAULT '' COMMENT '会员状态（0:冻结；1:非冻结)',
    `wx_open_market` varchar(64) NOT NULL DEFAULT '' COMMENT ',微信激活商场',
    `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间即加入日期',
    `kip_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'kip对应的user_id',
    `is_completed` varchar(20) NOT NULL DEFAULT '' COMMENT '是否完善信息（0:未完善；1:完善)',
    `remark` varchar(4000) NOT NULL DEFAULT '' COMMENT '备注',
    `grade` varchar(32) NOT NULL DEFAULT '' COMMENT '等级[编码]',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间即加入日期',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人',
    `whether_blacklist` int NOT NULL DEFAULT 1 COMMENT '是否黑名单(0:是 1:不是),供活动使用',
    `register_source` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源',
    `register_source_label` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源-附注信息(例如：活动，电子券)',
    `register_source_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '注册来源-备注信息',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员资产禁用表';

INSERT ignore INTO  tb_member_asset_invalid (id,group_id,mall_id,mobile,current_points,
                                     vipcode,grade,kip_user_id,status,wx_open_market,
                                     is_completed,remark,join_time,create_date,create_user,
                                     update_date,update_user,whether_blacklist,register_source,register_source_label,
                                     register_source_remark)
select id, IFNULL(group_id, ''),IFNULL(mallid, ''),IFNULL(mobile, ''),IFNULL(currnentIntegral, 0),IFNULL(vipcode, ''),
       IFNULL(grade, ''),IFNULL(kip_user_id, ''),IFNULL(status, 0),IFNULL(wxOpenMarket, ''),IFNULL(isCompleted, 0),
       IFNULL(remark, ''),IFNULL(joinTime,now()),IFNULL(create_date, now()),IFNULL(createUser, ''),IFNULL(update_date, now()),IFNULL(updateUser, ''),
       IFNULL(whether_blacklist, 1),IFNULL(register_source, ''),IFNULL(register_source_label, ''),IFNULL(register_source_remark, '')
from tb_member_invalid  where id not in (SELECT id
                                         FROM tb_member_invalid
                                         GROUP BY id
                                         HAVING COUNT(*) > 1);


CREATE TABLE `tb_insensate_points_auth_record` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '符合规范的唯一主键id',
    `open_id` varchar(128) NOT NULL COMMENT '微信open id',
    `kip_user_id` varchar(128) NOT NULL COMMENT 'kip系统中用户唯一id',
    `group_id` varchar(128) NOT NULL DEFAULT '' COMMENT '集团id',
    `mall_id` varchar(128) NOT NULL DEFAULT '' COMMENT '商场编号',
    `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '会员卡号（支付宝）',
    `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '授权状态，0已授权，1未授权，默认0',
    `origin` tinyint(3) unsigned NOT NULL COMMENT '来源，0微信，1支付宝',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `mchid` varchar(128) NOT NULL DEFAULT '' COMMENT '商圈id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_openid_origin` (`open_id`,`mall_id`,`origin`),
    KEY `idx_kip_mall_origin` (`kip_user_id`,`mall_id`,`origin`),
    KEY `idx_mchid_origin_status` (`mchid`,`origin`,`status`),
    KEY `idx_card_no_origin` (`card_no`,`origin`) COMMENT '会员卡号+来源',
    KEY `idx_mall_id_create_date` (`mall_id`,`create_date`) COMMENT '商场id+创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='无感积分_微信、支付宝授权记录表';

INSERT INTO tb_insensate_points_auth_record (open_id,kip_user_id,group_id,mall_id,card_no,status,origin,create_date,update_date,mchid)
select IFNULL(open_id, ''),IFNULL(kip_user_id, ''),IFNULL(group_id, ''),IFNULL(mall_id, ''),IFNULL(card_no, ''),
       IFNULL(status, 0),IFNULL(origin, 0),IFNULL(create_date, now()),IFNULL(update_date, now()),IFNULL(mchid, '')
from tb_insensate_points_auth_record where 1=1;


alter table tb_points_detail drop PRIMARY KEY ;
-- 变更主键
alter table tb_points_detail change column id crm_id varchar(64) NOT NULL COMMENT 'crm主键', add column `id` bigint(20) PRIMARY KEY NOT NULL AUTO_INCREMENT COMMENT '主键';
-- 添加索引
ALTER TABLE tb_points_detail ADD INDEX idx_crm_id (`crm_id`)



alter table tb_sales_detail drop PRIMARY KEY ;
-- 变更主键
alter table tb_sales_detail change column id crm_id varchar(64) NOT NULL COMMENT 'crm主键', add column `id` bigint(20) PRIMARY KEY NOT NULL AUTO_INCREMENT COMMENT '主键';
-- 添加索引
ALTER TABLE tb_sales_detail ADD INDEX idx_crm_id (`crm_id`)









