DROP TABLE IF EXISTS `tb_ocr_callback_record`;
CREATE TABLE `tb_ocr_callback_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `task_id` varchar(128) NOT NULL COMMENT '微信openId',
    `photo_id` bigint NOT NULL COMMENT '拍照积分审核记录id',
    `content` varchar(140000) NOT NULL default '' COMMENT '合合回调内容',
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_task_id` (`task_id`),
    KEY `idx_photo_id` (`photo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ocr拍照积分回调记录';