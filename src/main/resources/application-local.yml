spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    dynamic:
      primary: points
      datasource:
        points:
          url: ************************************************************************************************************************************************************************************
          username: dev_kip_member_points
          password: 'I5vXfpf6JXdWb7T3rYdXwjvyTZ9GZx1!'
          driver-class-name: com.mysql.cj.jdbc.Driver
      # Hikari 连接池配置
      hikari:
        # 最小空闲连接数量
        minimum-idle: 10
        # 空闲连接存活最大时间，默认600000（10分钟）
        idle-timeout: 600000
        # 连接池最大连接数，默认是10
        maximum-pool-size: 20
        # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
        auto-commit: true
        # 连接池名称
        pool-name: Integral-HikariCP
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
        max-lifetime: 600000
        # 数据库连接超时时间,默认30秒，即30000
        connection-timeout: 10000
        connection-test-query: SELECT 1
        # 连接将被测试活动的最大时间量
        validation-timeout: 3000
  data:
    redis:
      host: 'cnsh-kerry-crm-dev.redis.rds.aliyuncs.com'
      port: 6379
      database: 1
      password: 'HBWj&OgZl%loBU&v79w$oWXwDIJ^akoq'

kerry-crm:
  rabbitmq:
    password: 'NTI5NkYyODlBMjJEMEYyQkMyMDdCOEFEQzU3RjEzNENGOTBGNzZFRDoxNzMwMzU1OTI4OTE2'
    username: 'MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLXZjMjN6Yzc0azAyOkxUQUk1dEdMajVKRFBHOGFFN05Dc2ViSA=='
    addresses: rabbitmq-serverless-cn-vc23zc74k02.cn-shanghai.amqp-0.vpc.mq.amqp.aliyuncs.com:5672
    virtual-host: crm-kpl
    requested-heartbeat: 10
    listener:
      direct:
        retry:
          enabled: true
          max-attempts: 5
          max-interval: 10000
        acknowledge-mode: auto

payment:
  rabbitmq:
    password: 'MjQ0NTc0RDU2NDY2Q0M5NzU0OTM4QTA0OTI2RDlGNzFEMEU4QjdCRToxNzMwNDM5ODQyMzIz'
    username: 'MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLXZjMjN6Yzc0azAyOkxUQUk1dEtBRTFQTWFmczkzeXNUQThnOQ=='
    addresses: rabbitmq-serverless-cn-vc23zc74k02.cn-shanghai.amqp-0.vpc.mq.amqp.aliyuncs.com:5672
    virtual-host: kip-payment-middleware-service
    requested-heartbeat: 10

server:
  port: 8081

kerry:
  services:
    message-distribution-service: https://dev-kip-service-internal.kerryonvip.com/message-distribution-service
    kerry-staff: https://dev-kip-service-internal.kerryonvip.com/kerry-staff-service
    payment: https://dev-payment.kerryonvip.com/services
    hive-service: https://dev-kip-service-internal.kerryonvip.com/hive-service
    hive-vas: https://dev-kip-service-internal.kerryonvip.com/hive-view-assembler-service
    event-trigger: https://dev-kip-service-internal.kerryonvip.com/event-trigger-service
    unified-messaging: https://dev-kip-service-internal.kerryonvip.com/unified-messaging-service
    cip-service: https://dev-kip-service-internal.kerryonvip.com/cip-middleware-service
    profile: https://dev-kip-service-internal.kerryonvip.com/profile-service
    auth-service: https://dev-kip-service-internal.kerryonvip.com/auth-service
    toolkit-service: https://dev-kip-service-internal.kerryonvip.com/toolkit-service
    ocr-service: https://api.textin.com/robot
    temporary-parking: https://dev-kip-service-internal.kerryonvip.com/temporary-parking-service
    badge-service: https://dev-kip-service-internal.kerryonvip.com/badge-service
    flexible-space: https://dev-kip-service-internal.kerryonvip.com/flexible-space-service


mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mall:
  #允许积分为负数的商场
  negativeMallGroup: '8aaa81947c6e1ca0017c73c13cc30006,4028e3817c2b3f79017c2b48c54c0000,8a8480f9805e8c7201805eaa0d520000'
  koBigGroup: '4028e3817c2b3f79017c2b48c54c0000,8a8884e77cc9e70a017cca14d07e0002'
  list:
    # 杭州嘉里
    - mallId: 8aaa80b47c784020017c78b00d060022
      abbreviation: HKC
      groupId: 4028e3817c2b3f79017c2b48c54c0000
      code: qr,wxpay,alipay
      appId: wxd830fe4d1e04988e
      wxMchId: 1616241765
      wxBrandId: 5484
      wxCardTemplateId: 'p3qJ3s82q7MoYS705PTnOo2VY7Y0'
      aliMallId: '10012629'
      aliCardTemplateId: '20220321000000003944406000300728'
      projectId: 186
    # 天津嘉里
    - mallId: 8a8883557cca9463017ccb0ce0a00002
      abbreviation: TKC
      groupId: 8a8884e77cc9e70a017cca188fae0003
      code: photo,wxpay,alipay
      appId: wxd830fe4d1e04988e
      wxMchId: 1615796420
      wxBrandId: 4872
      wxCardTemplateId: 'p-DAvuLPMcec1iXuTA8jKUNmR-OI'
      aliMallId: '10082256'
      aliCardTemplateId: '20220506000000004000626000300720'
      projectId: 180
    # 沈阳嘉里
    - mallId: 8a888aed7d0295e5017d029ff1f40000
      abbreviation: SYKC
      groupId: 8a8884e77cc9e70a017cca1c77e80004
      code: photo,wxpay,alipay
      appId: wxd830fe4d1e04988e
      wxMchId: 1615610395
      wxBrandId: 4875
      wxCardTemplateId: 'pdJnI053lUTZNR4D6RfvsTcZwiUE'
      aliMallId: '10081831'
      aliCardTemplateId: '20220120000000003842589000300720'
      projectId: 187,SYYSDY,SYYSG,SYYSJ
    # 前海嘉里
    - mallId: 8a88a9fd7f73ffcd017f968739870006
      abbreviation: QHKC
      groupId: 8a888a087cc59dc0017cc622f9ad0000
      code: photo,wxpay,alipay
      appId: wxd830fe4d1e04988e
      wxMchId: 1623514491
      wxBrandId: 4851
      wxCardTemplateId: 'pxq15xDmp28OXAvDakTVzS44P2KE'
      aliMallId: '10081842'
      aliCardTemplateId: '20220330000000003948490000300729'
      projectId: 192
    # 静安嘉里
    - mallId: 8a88835c7cd96d31017cda2fd9910000
      abbreviation: JAKC
      groupId: 8a8884e77cc9e70a017cca14d07e0002
      code: qr,wxpay,alipay
      appId: wx2a1741f02f7bb650
      wxMchId: 1616264750
      wxBrandId: 4934
      wxCardTemplateId: 'p1hKav1NxSo3tTN61sqUQMa7fY9c'
      aliMallId: '10002803'
      aliCardTemplateId: '20200813000000002412173000300303'
      projectId: 189
    # 浦东嘉里
    - mallId: 8a88835c7cd96d31017cda3f77e80003
      abbreviation: KP
      groupId: 8a8884e77cc9e70a017cca14d07e0002
      code: qr,wxpay,alipay
      appId: wx2a1741f02f7bb650
      wxMchId: 1615791369
      wxBrandId: 4941
      wxCardTemplateId: 'pjQkexFnPVu_G5NX77_tdsR3H7KQ'
      aliMallId: '10093868'
      aliCardTemplateId: '20220106000000003801208000300722'
      projectId: 184
    # 北京嘉里
    - mallId: 8a8884e77cc9e70a017cca4003590008
      abbreviation: BKC
      groupId: 8a8884e77cc9e70a017cca09d72a0000
      code: qr,wxpay,alipay
      appId: wxd830fe4d1e04988e
      wxMchId: 1616264644
      wxBrandId: 4882
      wxCardTemplateId: 'pmi1EwX-ge6tyRF-JNiYqnz4KFiI'
      aliMallId: '10016532,10016532K'
      aliCardTemplateId: '20210312000000002669102000300258'
      projectId: 185
    # 福州-榕城·江上图
    - mallId: 8a888bfd816101980181a969f513019c
      abbreviation: FZKC
      groupId: 8a888bfd816101980181a9593f99019b
      code: photo,wxpay,alipay
      appId: wxd830fe4d1e04988e
      wxMchId: 1632570829
      wxBrandId: 5939
      wxCardTemplateId: 'psmXr53YkRfePSFfysA4h4YgLfWA'
      aliMallId: '10154370'
      aliCardTemplateId: '20240812000000005599146000300667'
      projectId: FZJLZX
    # 上海合集&企业坊
    - mallId: 8a8881dc8754766f0187796cee6b0000
      abbreviation: HJQYF
      groupId: 8a88835c7cd96d31017cda3662720001
      code: photo
      appId: wxd830fe4d1e04988e
      wxMchId: 1642890565
      wxBrandId: 7316
      wxCardTemplateId: 'ppyXQ6jiJolGAHmog48R5cJA2w2E'
      aliMallId: '10138496'
      aliCardTemplateId: '20230704000000004763452000300721'
      projectId: 181
    # 深圳建设广场
    - mallId: 8a888abd8ceb6c70018d117f469c0010
      abbreviation: SZKP
      groupId: 8a888a087cc59dc0017cc622f9ad0000
      code: photo
      appId: wxd830fe4d1e04988e
      wxMchId: 1656883827
      wxBrandId: 9223
      wxCardTemplateId: 'pjPmyjsDoTSTXME70mAgvXXdmh48'
      aliMallId: '10150356'
      aliCardTemplateId: '20240416000000005275147000300720'
      projectId: 188
    #测试商场
    - mallId: prod-test-f4b2f01902f655bfb0001
      abbreviation: QATS
      groupId: prod-test-ae252018fdc2dfb780000
      code: photo
      appId: wxd830fe4d1e04988e
      projectId: prod-test-100

alipay:
  list:
    - name: HKC,TKC,QHKC,KP,SYKC,HJQYF,SZKP
      aliPayPrivateKey: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCgT3gh9h+WSStILZuwrgHhlNk3Do5HA8n30mYQAiOd8S+0Kenby+FsVAdrxWqsbFuRO9eIzp1kSdNHZF7tjmURGKeQoB69aZE390Yg61kwiLeWZAhQ4P4pnOtygEfuQq13MWAiRFXW8GGLTA3vfq3J0mroSLwZA7GxNDIW3rolT8VeqDnandbJmQXLAAyWLaKykXfU3E5QWLC2JquUCTrJIdMmLorJmQsnf1abHojHtnkXWKDscNhwX0kiE+f2MgIMkbe8573F7cHe4HcQVgIjy/PpFhVmdspKPYpjEbv0PcKqxYxnNDNaRgJCha4s0esgoK4JDWT1T8Njyk2dc7GVAgMBAAECggEAQyhnnFqeavgmIKuyoHaVJSqVQOJhKlDlCGPlC+nJf/4tTN2OMd0ch6RAI57ljGwshBA8v/xGpV/kGfFrbzuLDXl1oTnjBti11GtLvri5eYKc3mzzquZGs6ogXKxu9dqLDvbpXhGgoFW9l3x6Dg3UwkrbLIpj+lq40xKZtUkDVGOFJZKoYvESP8C+LT7uqMEKSGda3ZfLCLjuC3MEEpP7rfAoRbcE5GDqEPOuZxZ1xNy1qohPK3vS9IbrTBGOF2wYvySXOjBUukNYwT2o+eauhRRUWA/CsoU1MZesPTQa4E8EqomhXFSL9k3g+LRZTmlNzPTb5Nwy8QVl0a1NNizkgQKBgQDzTIByNWJg5J1qGOJXC6LmvwX3/DNcMzQ+lrCPFy7esnMrz39wiJXnCr3tBeHCjuFrasukNujgvphnL0YyfL+O31BDeVrpB9khvbRUT+qO1V3mpWHccOK7hnKoexTGDJ/VHggKsyzwEtmAikk0G84/dBWrVVqqGlV6I9J2KAB+YQKBgQCoreR6IsfFhEe0cVRXZWpI/MBdrrG5M9j1QcsF9FzVOzUNiN/OqsVVyzA/Ztmcdu2NiVxh0KCzcj3wAnqqLGq37EfM8yZ/QFdNwxrAza6g4G6UUI+xI2FkTum9AGfHkXX98jR+4Nnz/HLAX7U3vDtOmI4GEhZyQ2Xc1cDbY6O3tQKBgAQXTzmcaDEU5wi3rmakTXLv11vaI8rzGXfL9OATFbis637bGFt+EHc4GQV5SdiJqoSx4Qa73aKZ0do6U0XaDyaJ3sGjssGkXiRRGk0ootsP/2o3cbPROh7JHfhzSWAjKjTdMmlrpd/LkVi1+pEjvo4Wl56v6rxdgP5yx8Ew3wFBAoGAPQJoWSSrTO2DYeh7sQClaLDCgx/keDAgkNSzMfwod5dqoPKYwI9TwKw1TzlMDCOX2FGOcHKdLUpszJqY82QNVWjIr4BitDP5X87ppkK+nsGOPWbGEjPRzg4czJeQ8cXS/37TLfD15CGy/5GeYgJbERpshBWWuM1X0oAduTxYwuECgYEAn7xinH5UWnCN2f/Ps2nBEG28O41xCBDTG3PjQFOZIpiflVMMJNxh5PEt6lkQUtFxMuEo8YXdNPQ/NpuhikQ+6wLuMGw+4sbIEO1dUnC5aRjf2hLFiy9UIqzkVC9yZKIcsOmYf958xP076a2o+j2hEaLDOSc5JrGsXFfXuXgQjxU='
      alipayPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvJoOI50RcAXHRI9nMX0KxFMBPkAxqaeWf4fFuWaBzXkaPETOo9KY9hC0TzxBcAcavIWImvPWs7aaDTPcKcynhAOucpNiKOqnvM0m2yTQVvlLaDQye2tlkdtcptLm2SvrWHAFiUKXcdC1CMyKpMiqJpDaNqPyZGlATGdcPthuz9qqUfPwgxoOvFllxt2zbN6sUCixtenqp1tpTKHuSzH/ieKdj9J2jlSUogwFiOeKbkuvsWlxOkYxWcGgxxd7vWTOk6TsE7QI1Jj4OexmQBd7gE+ZNdQxv7sc+N9A5P+gIrE8kuV9YpxKfWb3N98mn9j+rc9eyl9eeZlCyFV0o1szfwIDAQAB'
      aliPayAppId: '2021002194684364'
      gatewayUrl: https://openapi.alipay.com/gateway.do
      callBackUrl: 'https://crm.kerryplus.com/xcrm-api/zhsq/alipay/alipayMarketCardTemplateCallBack'
    - name: JAKC
      aliPayPrivateKey: 'MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC6DChMMH4Bck9g95vO1Fj/qBVQetw0RNWgBgKWbg1BL3hgwWRGhG+fZQX/5gmx1fjIaL3R6VBjXp5NodQXA9l2zht7h6+Z1iWk097i5x4Vr71eL7/fdO4E7OBef/f14BAWq7NkuVVJQIbL14iWXdXH+XcUjBMOu6ShnVfVONAcVgxqiiWzDOSEoKp1feuQtIOo0W5lhHG3llxGNOKvTvXWiv4BPf4lNP6MCS9pocG6ddAOK/57jXTOVxgJIUmDrM0Sn5n65yAC7ltvOasA9YUS7tVqinQgji3opRrfJPB/AjU/aDbxB0gBDGJDsTyyqzmjhzO+IcFYsgsn5Yl04RkHAgMBAAECggEBALPo88MRlZgygT5k0z/EKrg3Qz1LQcwt0GLpzPDJRXoGvKLcAOsMQKDj4VRfkHpvTyAF4aGuvDULl4u89Uq4Rz/S+dKQGKhr5IUq+Tt0gaw5AKenByXAfjc3FZVsrE0ROp2htl2ttyKaCn/uUkAOR6AUximRdxBlNkOu0vP295Cdo0miV5kNIB2wX03vhKaH/vSqLwl2OOkjBjlG2ZjFtaUF8iubecGXa5Ft+3vWphqGjvkpGFGcMsWkTpfjqePOjMS6ffDTK+iAZDDYtfladt8VJQ9ygyIo9o0MYLrlJ2ALmApRZifai2cDNLwUok8nvCoNl5cYezvjIBJBHM+s32kCgYEA3d4sv9NSm5PBx2QxJaeBjZDZxc5G/vO05awftsh9ooWYJdYpaeTOX8mnCBV3WHEuBRvCMZTgrRZwSPNYb+sHj8Uz2To0Ncn+7HeYv5SeuhWt98UjnqgqmCgFUuI/9s/eiin3rjMkzeH2wpu6m3iWZaQhqoPXUcecsF9+TvBavaMCgYEA1qtEopwHFARNbjecWQwwsG04KmuJRGk6Sg7EEWZ8usqx909omBfCXxw/LSiOJT21cxsx/UyD8JYK6Z2KkheUTLtjyNF3V5yp/Acj9cSZZPmPrtSe2siRTwN6L9x1SJPp4CVzPyqNEvGPvLgBsC+RBhA76TXUt4Yik241rzr0pU0CgYAE3/zbuij2giif2PO7gBK4qZYfYojs0S4IXd/UhFM+XvEY47vFN+HQ+P1SkCYvK013j0SVElBdfUC6WZ531xIzdoHNJBpBlE6lo3sXuoxCSneoGgzOT6Of9EEpsQ72vVqGbqWijj0gyPZDEGjpQZ0MXq7esEbq5RfsSCzeTymvQQKBgQCRNGRGwdef2JEvnnopWa+NhzNYrki5d5wv//Dr/DEegIEE0jfjuR6HCypIt4f+9NDJvXmA5sLQeA2FnEThMQXlKVQDwHxDTPtGOOZAFe3UeWKRVczmHIj9s1JxEWRb5YFrVqmv7m5ig/dAJg8uR96HBXAg/YKXaSn2I7Q4hV56eQKBgQDXYzY7ZVLwDqsEkf7Q1jNP3CjGkLfEy8v8j/DdsVDTCivBvxNJdjrhRpLkVm1DUvsF9Moke0nC68CzdDB52MhV5jyalvMCr2yGfLrBY1JmrJ2tDIEuAT/yVu/0Utaa/NQ0Mm9zsPX77Ox0ALhTqWHghoppI23jKN/KoMUVW4uA5g=='
      alipayPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqbTfJiaCGJJPOQKb2mSJvS+cGbxHb4Rij/CLbtEtxDr8dmcE9Ou44roN9ygmo9Lk8Tc5mNVLelaR9wIFk1aXzyrAThTWUw4phbnmm+dx7Moelmf2Juxn0YbNqLeJgpPQrg4+VeeGzQS/mTdunT/Rccnh5SrSEep1orh99xtGlk+78YsKBvZIGLsgBXGeEJsiX42j3N48ffyx2kUFlaqg92MA537SU+lS8x+z3d/DG8vpYaJ3640DB059QuTt7ngd010CjhUKIekmKIXLwP+CAit/75XUKyW6u+LTCzKb2isDicJyc1qw5i1lPHOr1FoYKpcUCBxvOzkxyhkTxihKrwIDAQAB'
      aliPayAppId: '2021001183606939'
      gatewayUrl: https://openapi.alipay.com/gateway.do
      callBackUrl: 'https://crm.kerryplus.com/xcrm-api/zhsq/alipay/jingan/alipayMarketCardTemplateCallBack'
    - name: BKC
      aliPayPrivateKey: 'MIIEwAIBADANBgkqhkiG9w0BAQEFAASCBKowggSmAgEAAoIBAQCyvha5PNQgXsYMR+MRG7pFnZwJwqD0D/KcJeVLXCV8NlULKFIVjMZQQnCIrEhv0QVi7Sbu+28zX1FouU2RDXCIlTnPeDcTrfXVTGjwED2MS99+uobWeIhGad7/eBabX2C37JYSkUBIrGdzSXcZefYSQD7N5U5gvZwLyeSxZxyv1Md1SLldBaVPy1t/vTvGvM9IEkwfpUF4ogiakbnlAbOsSVsaODJHezuNJpDYzem9kMg3rA+keRpoksWnQdlClImGRYPiBXP5mBgGrx8nXsTdOUQAx9zkGzkD9pH5GsiEdcQR5sFnvSu08Vmt/CmnpzJ7JtnxS6Goowhdt9XqwpxfAgMBAAECggEBAK5PgomTMKuAwVMop9//UmChm5JJI/3fr5Fnw30Cl+vScI04/RPW/W8329btbJbfGImpDNbyjTdkNVgTnflfSedo37/cVJn0hFFG+u9iZyNyuroz3Vs6oGtKxBYPXryW/xNevdvNmwtEVn6vnl5R+g9CZ/0TKqRdRNiaQ1ReYjN1g72HMCez6s9jNue7YvfU0cPpZuC4DEqbQuEy8jeGQdchSOjUDzRKyBSUokpg+ln1QuqqHvr9REQqxxMabBDhtv4KENsRlHuWRoAfL7v5XxQw6AmO/+y0exHcx1FX1rlw5aB3zRwrT0Ilh5DPde0XRtCJBU+22+k9AS2pRlHzA4ECgYEA5ugYGNKRNxKs1j63OxWq0qUvugxv5IUygdmllFlL65H7DtHqtUtq089KGxP9r7P539EMY3f+1cuqgJK7Y5MJBHCmcXER7vm/Nfnc/dK0tjfUAEGQNXkgxvew4VQ05VgffhrccvBtXYVyy7Cx5QWxFfZZv6p7cpChS7vUqVd6w2cCgYEAxirFWlY06MMSQSYIfcdfr/Ov/nCGahxC9pHLWhaOGwsSiSAp4xuOMVVYuz6JIlAm7STI+IOnCVuSw2gGq9vnImPFySLJOSFi4280kfPzbQaNn4sV59f5fC41HlamWAAuitqxCpzl0mzviP9H3q6penkCdvHxn5mfEEG2ne8tfEkCgYEAnHLdANSKt3nbyT2tAEdp+lcZw0Dx/y+QTRKecakfG3lJVX09PaDfvKtSVo5w8D9NKiyF7E89RrY7y+mfNZRGI8//thUaRYTJ3VRyOzgsYNnqtlG118U+RBFGsyVOwaW3kMW6X9YvA5KLeR87KSV2Gp9Tp91mzvTXlGyrZiFzeDkCgYEAp36FOnq807P1nO0ppi47GXwuWDAB3HQwwTmvmGCSEheEvjRaUyP6RoZtri4IOOFoVW6PE0YrjARDgzhM0zvWG8Ia7YuiatBmRfe7hlWcfZ1ZXO1xQXnR3H0t1ovEo4Lb0+9OTrsYoYgD2lZzB6u3gdYAGEsdQBnXUSVJ1inP10kCgYEAmEj0E2eY/vgit1HvZXKgJKCljNpN4JHE9z91SUKzvo7qPUEldnYdBEDlAbMKepXSh5OOXWdKLUk51djhlJvKMzj8FEmxiosg8LGnvb9ypItq21L0PcFBAirgHjL3ey62ORfHwG1FBZTnkQWLkBFUx2jqKHg93bcLwzepIHwkq4Q='
      alipayPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnQpbfJfKfmV+xTklFyBGtkwGiEnxD/C29xJTd9Au/roKOuUtWT/opX1FAulU0jcCaCpViWOm7u3teqEq5suoNEy+b5poYECj/rfy4pJ1KozM4i0/0lx/FC8cUfx14AD1hN/vvnWgSYouRSrmTYXj4xtgM3Ie7o0rNByS+8In5AkrAXnZoRXZTNyIAAwz9+0/e71N6uP8y89u6Zcj/8/wcvVVDknGsBAWp4Rcmm52+MsTad30jMEloQQBCrdgEsnbpd2b2rQ+Z13Z4TiWS754rvWegz5ZJm/7QvGT2gSXei62YlRkEaBHNpFctc/Wke0xCTg1Vy6gYsoHrSeAwmisawIDAQAB'
      aliPayAppId: '2021002131630072'
      gatewayUrl: https://openapi.alipay.com/gateway.do
      callBackUrl: 'https://crm.kerryplus.com/xcrm-api/zhsq/alipay/beijing/alipayMarketCardTemplateCallBack'
    - name: BKC_KB
      aliPayPrivateKey: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCJnVGC+rfqFLEWtEfrY3NmVxpjYEUrDu1gbuKeewfSBIbVsF0odDmgCK+EiBVjEwJkCjay+I9GfEMpkiL3hioPWh+iXeCdXauBE8HlkgbtsCHS6dC3LgRDPlHvqrZ3PQt9z+h4tT/dDb2AeqitHzVZNOeJlhmyoVCOfV839iyofwhrl6fVlBzqSR9cpxrREMiup+lJrYnBekrsjYZF5XdYRi2bxQi7fUFx0Jm7rqHqfqKJGWdG7MAcovmWrVZvL02CDMpVG+xjzpmNEBSW4UEz8/2lNXbK+mksTw1FjwOKU1KHZqtj4Y3+TbG/z0vcv9OwaKwYOhJ/e0d2f1eNvPS5AgMBAAECggEAaadKyZDMWv9/5rqK7QHxu4TsUZe8Aw722K7JdntmyauICc/Z9hElnOSOPOZt5R7XGw9sZ4PGHIuS1PvJtNhgumCrIPGKXcdowTT2DNdMUniDv3acM8c0ppvWNHeQl4Naq0FQbjxZx9GgyF9j2Wa4gsDHcy62lBqdyoge7swmBUY7Iq+pGO0d//ZhKOHp+KoWEy3zurvLGdFChJWBytDaRTT/I59DQFTutHh7k0P2tXFI6cmIcroJ8jEQeT3h3faO+B14bdb8G1JjoPLfWdzpEns6JAP0n1j7qf5EiE6vG8XlFH/+h0oHhi3uvmGMD0ay3IJoBqJUPKexuyMQFLFnsQKBgQDq9Ii8JjnqvnvZVsBqqystE4y4IdXf5iWobnFThW4PawEPWAB/OQnuXV+RS7IFtQmY9eB/xoYBrktVtSe0a2z74RpLO6sxqhAEf8zxxPp6UBFZnZ1Yjvx8Sz9emqOzjS5iyX0Ndau0f2jND6alIAyq4++hI4xBj44fLYM/ULgegwKBgQCV8Mk34KcHJI4JPLuCHtWt2skZoD5eGcmC0QWDxN6FWC304yCNLMUuxA7GuWBNMpZxP+6XDR5Uiqf5TebGSBjJgUxyYh48QBdxEu10hufCWtui/t+HIP9vs/kev/yia3bJD+Hon6vp+q8j/+oUKYubE2QUAcC/qf/ZH8odnMO7EwKBgQCxzUnocFkl7u6UY5ULY+sxmnjEIz7RyD25P5LdLL1ER6qurN77Z/O/AcQ/lPPsY7sWmQNCTr68aOSzwHqCi0UXO5sFUetUkyi57ujuES2oAA2RLriS+unRObSD6Cx3xSBUO7w5wlGlL0Hk5LIshZtwezutStRqDLP1niCTeodK9wKBgDTiX2Y481B+oSY66HvFemI+d411SmoQOFXOeV53cnCfoF3uOg3xC38qIXgP7BpuTEeXYASg2ODCouo4grvOtAdmdlslpjpj9KCqJ1xhNJ8FQtBjit1zUwTH+obQ+j8jHhteYbSVlKILC/jri1kfKiFBHpw73gr738RqBPSa1SJHAoGATA82IquC4lRrHUGy/R9bK8zVLqFNWzcYNJhDqdDb93xFfZ+2wdzpM+IcnK2zWNxnfDzkEkdz45AalfVE72iNIXAnSp8hx2HImNA4SSzFosaAftdmz6OnQCciI4vpFuCB2YQcIIm5o05arSmtGB7tjNcCWpY7xjawkdGYBhF7Eu4='
      alipayPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnQpbfJfKfmV+xTklFyBGtkwGiEnxD/C29xJTd9Au/roKOuUtWT/opX1FAulU0jcCaCpViWOm7u3teqEq5suoNEy+b5poYECj/rfy4pJ1KozM4i0/0lx/FC8cUfx14AD1hN/vvnWgSYouRSrmTYXj4xtgM3Ie7o0rNByS+8In5AkrAXnZoRXZTNyIAAwz9+0/e71N6uP8y89u6Zcj/8/wcvVVDknGsBAWp4Rcmm52+MsTad30jMEloQQBCrdgEsnbpd2b2rQ+Z13Z4TiWS754rvWegz5ZJm/7QvGT2gSXei62YlRkEaBHNpFctc/Wke0xCTg1Vy6gYsoHrSeAwmisawIDAQAB'
      aliPayAppId: '2021001179620698'
      gatewayUrl: https://openapi.alipay.com/gateway.do
    - name: FZKC
      aliPayPrivateKey: 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC29noc29zgOKEd9lxcZOW/4ixx5cNqyCdU0RvxEIbo7kPOVKr/JNsbUJdf0vH1lld2cZYB53x155NjiSRU39fgSt5rWKhyJYXsNYK5VFOyffltxfhC28y0KUphaNTxKr74oyuIVm/JkCv0UPWo2uMuUL2hib4JVl2XK7bEa0rE+nclGuw31wliqmE+zaGWblheNveVhr/59Re94r1JTWRm4vrw5rY/ILJtHT4BfkFdXj8gZ6zUXnAjxVZqSL7mFP7JgmX+dZ1W5Q+6Yb6qjZTxK1L3exu/ycDhQQ6OQIQ2DRtSdJULqPhT9OCwpvTqaIjpfO18oFocl5WiunRwYdZ5AgMBAAECggEAJoSLPqoWvOmPGcS0uwrOx2+xFaVeV4udpCCFtmFZVY5bngN7IE29BuoXhCffomktLpVJFEuMizHeeJT1nhyl0OVSH91BUQ6PPCQjyipzmF8m9LI5yrGFTeRncUxlkAQEwklvr7squ49rgXrNzOeP7j83PNvHsyWIfHvKdV11FzSb+UJvRhp7/cXQOCkfhD1FIN0FDLgYHgnusvinTYcsoYTqyCyxZytdlesUhiSGC2gRDNkFnZN0JGKQv+4oV82PdWsdEf8/YVROHBx9PcFDg1ubhuK0WrQ63bKda0F1eVsSHu6wVmILXEKAcsiP4QkFQAPK1VlrfNhc6Ccjqk6E0QKBgQDuAOOM7YFHeEu+fnW2vC7nf/s4ZQtffYnzdOyFFDGHL7w5r+5xr7vJZzi79abeyCMNFnCRK+7jnQMRwpPAoPCbh0/go6gnUoidXib8hxlNmJS0o3QoLaBX+WtY2LkiUqB3nUoaUa57u786gRMqHpYw1i+FxUGJ69o1NXBJ0y/q1QKBgQDEzCXYvWXEuybEe//5TMpRAdGT/64lT/qcq4lkDIoO0WfNYwvLle4EEhIbfW7FF2jLagWb+LiMb8ZihOGmQ/5YeombnkyEWWKGR8VkmXP+0pCqHrJM2U70PBCB9lfGoCc8YEzM+ne8b5PDqgBcwWCPm/BWWK5EitgsoYn/ikjHFQKBgG1XAzMPOcE3xrRxLGsS8QlxhlxWL29jISkzoiVRLZ/JklN+O7EG5uKUBUB0joDyuQeeM4C+CjWPQIW8OlzYkzEgbNXq2amXBSQmyQpiloFLg6WpIggDThKch3B9wC/Afe/11sFYnSxDCIs8GS3Ud3NgMtzlzkIqlEi1kCyaVP0xAoGBAKbkYewuolF2TWgvqGKpBqIwIqWaHPszmlqm9jrMj4AFgjwhlO0v1svI72Y0t22Zbpui6+LbyNYALIN8IUi5PMeYE37PfiB3FSC/WNz5be9abqh2o8qcN2LUVUOBv+qIAv4mxLrIZiZ9kRhL4UbID7V6DxWK2y7xeGZREBCQGywJAoGBANhVJafA8DcWWfQuB7PJpUAW2QF+/xuRdPVxkeksJpptuBjAO+TlkDOf9BGmsrLPvcsN7K/temre41I6Niqrs2rm8rI34EnjrO112puyjMX2cZ3TpBG6+o0DGxhLqS31Qd5xAjl7v8vi78DbWZbZrOgPwQlMOPzQnDIXgYEucj4n'
      alipayPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAg6BEnpyT2rTTlYtF/ftGkYKVoS1Hp2V6omAdoIWBk2dp/RIgp/w/GwiGRkh1CdrK95MTqIzVSkMCjqo2Q0C+ar8foqubfjhrIaS4FNbBtS7AlRgbQf8YDIUaaP3Q/rQWm31D292z9qSpMEU1F1ZiDjm4PPjJOEwv1ueo7E9qnJ57CsHWTm15MYuAnThLxpLAEqlQvUGDojgYTFqXtxGIeq9Hc4I0ZuI4215gTRt1dcKpfkNOgXCNKnziXeTUPJ54tI66AEVjD3dm3N9p7r0prlbB4GOES9/8w051la+wKPss6YxHzBZVu8+0qIT+kr7OBBwRRV5+5VtGi28mgEfygQIDAQAB'
      aliPayAppId: '2021003157640328'
      gatewayUrl: https://openapi.alipay.com/gateway.do

distributed:
  jobs:
    enabled: true

#GAG销采对接
smart-pos:
  url: http://api.test.goago.cn/oapi/rest
  appId: a5cd175df9ec41f4bead4ea4afd4f732
  appKey: 2c968585823429d9018497f69099000e
  appSecret: 08246B4091BB017B4EE42E0FF8CD8C5A
  messageFormat: 'json'
  method: 'gogo.bill.amount.query'
  version: '1.0'
  signMethod: 'MD5'

crm:
  image:
    domain: https://cnsh-kerry-crm-le.oss-cn-shanghai.aliyuncs.com/

kip:
  signature:
    pos:
      system-id: kip4f3842c637732da5
      system-secret: kip8dfe013a0ad769bafef6bc0e66d001aa
    ocr:
      system-id: kipe5a199fab333482b
      system-secret: kip6ab6e9384534422cafc665dc42f79f22

abnormal:
 points:
   review:
     crmAdminUrl: 'https://dev-crm-admin-internal.kerryplus.com/admin/#/crm-integral-abnormalIntegralIntercept'
     crmPhotoPointsUrl: 'https://dev-crm-admin-internal.kerryplus.com/admin/#/equity/bonusself/bonusself'

textin:
  appId: '5966ece64246e2f51468af5dd05d8e88'
  secret: '3da77e913b35b6979c0826bed20846a2'
  service: 'mall_receipt'
  env: 'uat'
  timeout: 60000
  accessLimit: 40
  ocrFileExternalDomain: 'kip-public-dev.oss-cn-shanghai.aliyuncs.com'
  ocrFileInternalDomain: 'kip-public-dev.oss-cn-shanghai-internal.aliyuncs.com'
  mock: true