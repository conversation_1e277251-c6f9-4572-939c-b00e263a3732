spring:
  application:
    name: points-service
  profiles:
    active: local
  data:
    redis:
      timeout: 20000ms
      jedis:
        pool:
          max-active: 300 # 连接池最大连接数
          max-idle: 20  # 连接池中的最大空闲数
          max-wait: 3000ms # 连接池最大阻塞等待时间
          min-idle: 5   # 连接池中的最小空闲数
          time-between-eviction-runs: 30000  # 设置驱逐空闲连接的时间间隔
  jackson:
    time-zone: GMT+8
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
  cloud:
    openfeign:
      httpclient:
        enabled: false
        connection-timeout: 10000
        max-connections: 200
        max-connections-per-route: 50
        time-to-live: 60
        time-to-live-unit: seconds
      okhttp:
        enabled: true
      compression:
        request:
          enabled: true
          min-request-size: 2048
          mime-types: text/xml,application/xml,application/json
        response:
          enabled: true

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000

server:
  port: 8080
  undertow:
    buffer-size: 1024
    threads:
      io: 16
      worker: 256
    direct-buffers: true

crm:
  mq:
    list:
      - name: '会员积分变更'
        queue: 'xcrm-member-integral-grade-alter'
        delay: true
      - name: '微信支付记录推送'
        queue: 'crm-wechat-auto-points-route'
        delay: false
      - name: '微信支付发生退款'
        queue: 'wechat-auto-points-refund-route'
        delay: false
      - name: '微信支付积分同步微信'
        queue: 'wechat-points-sync-route'
        delay: true
      - name: '支付包无感积分，同步会员积分'
        queue: 'alipay-points-sync-route'
        delay: true
      - name: '会员等级变更'
        queue: 'xcrm-memberGrade'
        delay: false
      - name: '会员年消费金额清除'
        queue: 'xcrm-member-sale-sum'
        delay: false
      - name: '会员消费金额-发生了退款'
        queue: 'xcrm-member-sale-refund-delay'
        delay: true
      - name: '支付宝/微信无感积分绑定奖励'
        queue: 'member-senseless-reward'
        delay: false
      - name: '销售记录满赠活动奖励'
        queue: 'sale-activity-reward'
        delay: false
      - name: '新注册用户场景触发奖励'
        queue: 'xcrm-member-register'
        delay: false
      - name: '会员注册短信通知'
        queue: 'xcrm-new-member-register'
        delay: true
      - name: '会员注册次日通知'
        queue: 'xcrm-sign-next-day-notification'
        delay: true
      - name: '完善信息必填项是否完善'
        queue: 'xcrm-member-refine-check'
        delay: false
      - name: '完善信息场景触发奖励'
        queue: 'perfect-profile-senseless-reward'
        delay: false
      - name: '会员注册来源'
        queue: 'xcrm-register-source-new'
        delay: true
      - name: '支付宝会员开通会员卡授权推送'
        queue: 'ali-member-open-card-queue'
        delay: true
      - name: '会员场景触发奖励-会员升级'
        queue: 'member-upgrade'
        delay: false
      - name: 'kip同步会员信息至crm'
        queue: 'kip-sync-member-to-crm'
        delay: false
      - name: 'kip用户注销'
        queue: 'kip-user-invalid-crm'
        delay: false
      - name: 'kip同步会员身份信息'
        queue: 'kip-sync-member-identity'
        delay: false